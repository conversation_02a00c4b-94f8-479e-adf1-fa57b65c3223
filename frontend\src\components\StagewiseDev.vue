<template>
  <!-- Stagewise toolbar only renders in development mode -->
  <StagewiseToolbar 
    v-if="shouldShowToolbar"
    :config="config"
    :enabled="toolbarEnabled"
    @error="handleStagewiseError"
  />
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { StagewiseToolbar } from '@stagewise/toolbar-vue';
import { stagewiseConfig, shouldEnableStagewise } from '@/utils/stagewise';

/**
 * Stagewise development toolbar wrapper component
 * This component ensures the toolbar only appears in development mode
 * and handles connection errors gracefully
 */

const config = stagewiseConfig;
const toolbarEnabled = ref(true);
const errorCount = ref(0);

const shouldShowToolbar = computed(() => {
  return shouldEnableStagewise() && toolbarEnabled.value;
});

/**
 * Handle Stagewise errors to prevent spam and disable if necessary
 */
const handleStagewiseError = (error: any) => {
  console.warn('[Stagewise] Connection error:', error);
  errorCount.value++;
  
  // Disable toolbar after too many errors to prevent spam
  if (errorCount.value > 5) {
    console.warn('[Stagewise] Too many connection errors, disabling toolbar');
    toolbarEnabled.value = false;
  }
};

onMounted(() => {
  // Add a small delay to ensure Vue is fully initialized
  setTimeout(() => {
    if (shouldEnableStagewise()) {
      console.log('[Stagewise] Toolbar initialized in development mode');
    }
  }, 1000);
});
</script>

<style scoped>
/* Any custom styles for the stagewise toolbar can be added here */
</style>
