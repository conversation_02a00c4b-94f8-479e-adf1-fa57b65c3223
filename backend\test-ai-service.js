// Test AI service with predefined tags and MUNygo context
const fetch = require('node-fetch');

async function testAiService() {
    console.log('🤖 Testing AI service with MUNygo context and predefined tags...\n');
      const testPayload = {
        transcription: "The transaction flow is broken when I try to confirm payment. The timer keeps running even after I click confirm, and the other user doesn't see my confirmation. This is causing disputes in the P2P currency exchange.",
        language: "en",
        userContext: {
            currentPage: "/transaction/confirm",
            userAgent: "Test-Script/1.0"
        },
        predefinedTags: ["transaction-flow", "payment-confirmation", "real-time-updates", "timer-issues", "dispute-resolution", "socket-connection"]
    };
    
    try {
        const response = await fetch('http://localhost:3000/api/ai/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testPayload)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
          const result = await response.json();
        
        console.log('Raw AI Response:', JSON.stringify(result, null, 2));
        
        console.log('✅ AI Analysis Results:');
        console.log(`   Report Type: ${result.reportType || result.type}`);
        console.log(`   Severity: ${result.severity}`);
        console.log(`   Summary: ${result.summary}`);
        console.log(`   Category: ${result.category}`);
        console.log('');
        
        if (!result.tags || !Array.isArray(result.tags)) {
            console.log('⚠️  No tags returned or tags not in expected format');
            return;
        }
        
        console.log('🏷️  Tags with Origins:');
        
        let predefinedCount = 0;
        let aiSuggestedCount = 0;
        let userDefinedCount = 0;
        
        result.tags.forEach(tag => {
            const origin = tag.origin;
            let symbol = '';
            
            switch(origin) {
                case 'PREDEFINED':
                    symbol = '⭐';
                    predefinedCount++;
                    break;
                case 'AI_SUGGESTED':
                    symbol = '🤖';
                    aiSuggestedCount++;
                    break;
                case 'USER_DEFINED':
                    symbol = '👤';
                    userDefinedCount++;
                    break;
                default:
                    symbol = '❓';
            }
            
            console.log(`   ${symbol} "${tag.tag}" (${origin})`);
        });
        
        console.log('');
        console.log('📊 Tag Origin Summary:');
        console.log(`   Predefined: ${predefinedCount}`);
        console.log(`   AI-Suggested: ${aiSuggestedCount}`);
        console.log(`   User-Defined: ${userDefinedCount}`);
        console.log(`   Total: ${result.tags.length}`);
        
        // Verify that predefined tags were used appropriately
        if (predefinedCount > 0) {
            console.log('\n✅ AI successfully utilized predefined tags');
        } else {
            console.log('\n⚠️  AI did not use any predefined tags (might be context-dependent)');
        }
        
        // Check if MUNygo context was considered
        const summary = result.summary.toLowerCase();
        const hasP2PContext = summary.includes('p2p') || summary.includes('peer') || summary.includes('exchange') || summary.includes('currency');
        const hasTransactionContext = summary.includes('transaction') || summary.includes('payment') || summary.includes('confirm');
        
        if (hasP2PContext || hasTransactionContext) {
            console.log('✅ AI analysis includes MUNygo-specific context');
        } else {
            console.log('⚠️  AI analysis may not fully incorporate MUNygo context');
        }
        
        console.log('\n🎉 AI Service Test PASSED! ✅');
        
    } catch (error) {
        console.error('❌ AI Service Test FAILED:', error.message);
        process.exit(1);
    }
}

testAiService();
