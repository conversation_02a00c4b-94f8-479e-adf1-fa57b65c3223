const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const prisma = new PrismaClient();

async function getLocalSchema() {
    try {
        const tables = await prisma.$queryRaw`
            SELECT table_name, column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'debug_reports'
            ORDER BY table_name, ordinal_position;
        `;
        
        console.log('=== LOCAL DEVELOPMENT DATABASE SCHEMA ===');
        console.table(tables);
        
        // Also check migration status
        console.log('\n=== LOCAL MIGRATION STATUS ===');
        const migrationFiles = fs.readdirSync('./prisma/migrations').filter(f => f !== 'migration_lock.toml');
        console.log('Migration files:', migrationFiles);
        
        await prisma.$disconnect();
    } catch (error) {
        console.error('Error:', error.message);
    }
}

getLocalSchema();
