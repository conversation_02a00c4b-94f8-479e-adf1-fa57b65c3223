# Accessibility (A11y) Requirements for Transaction-Chat UI

## 🎯 Critical Accessibility Considerations

### **WCAG 2.1 AA Compliance Requirements**

#### **1. Screen Reader Support**
**Fixed Transaction Summary Bar:**
```html
<div class="transaction-summary-bar" role="banner" aria-label="Transaction details">
  <div aria-live="polite" aria-label="Exchange: <PERSON> pays $1,000 USD to <PERSON> for €925 EUR at rate 0.925">
    <!-- Transaction details -->
  </div>
</div>
```

**Step Progress Indicator:**
```html
<div class="step-progress-bar" role="progressbar" aria-valuenow="3" aria-valuemin="1" aria-valuemax="6" 
     aria-label="Transaction progress: Step 3 of 6 - First Payment">
  <!-- Steps with proper ARIA labels -->
</div>
```

**Smart Action Bar with Timers:**
```html
<div class="smart-action-bar" role="region" aria-label="Required actions">
  <div class="timer-display" aria-live="assertive" aria-atomic="true" 
       aria-label="Payment window expires in 2 hours 45 minutes 30 seconds">
    <!-- Timer content -->
  </div>
</div>
```

#### **2. Keyboard Navigation**
- **Tab order**: Transaction summary → Progress indicator → Chat messages → Action bar → Message input
- **Focus management**: Clear focus indicators, logical flow
- **Keyboard shortcuts**: 
  - `Ctrl/Cmd + Enter`: Send message
  - `Escape`: Close modals/menus
  - `Tab/Shift+Tab`: Navigate elements

#### **3. Color Accessibility**
**Contrast Ratios (WCAG AA):**
- Normal text: 4.5:1 minimum
- Large text: 3:1 minimum
- UI components: 3:1 minimum

**Color Coding Alternatives:**
- Timer states: Icons + colors (⚠️ Warning, 🔴 Critical, ℹ️ Info)
- Step states: Shapes + colors (● Active, ✓ Complete, ○ Pending)
- Status indicators: Text + icons + colors

#### **4. Voice-Over Support (iOS/Android)**
- **Custom rotor items** for transaction elements
- **Gesture shortcuts** for quick navigation
- **Announcement prioritization** for critical updates

### **Implementation Requirements**

#### **ARIA Labels for Financial Context**
```typescript
interface A11yLabels {
  transactionSummary: string; // "Exchange between Sarah and Alex"
  paymentDirection: string;   // "Sarah sends $1000 to Alex"
  timerWarning: string;       // "Payment due in 45 minutes"
  stepProgress: string;       // "Step 3 of 6: Awaiting payment"
  criticalAction: string;     // "Urgent: Confirm payment receipt"
}
```

#### **Live Regions for Dynamic Updates**
- **aria-live="polite"**: Step progress updates
- **aria-live="assertive"**: Timer warnings, critical actions
- **aria-atomic="true"**: Complete timer announcements

### **Testing Requirements**
1. **Screen reader testing**: NVDA, JAWS, VoiceOver
2. **Keyboard-only navigation testing**
3. **High contrast mode testing**
4. **Voice control testing** (Dragon, Voice Control)

### **Mobile A11y Considerations**
- **Minimum touch targets**: 44px iOS, 48px Android
- **VoiceOver gestures**: Custom rotor, direct touch
- **TalkBack support**: Proper content descriptions
- **Switch Control**: Alternative input method support
