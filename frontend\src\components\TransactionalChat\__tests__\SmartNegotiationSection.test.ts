import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import SmartNegotiationSection from '../SmartNegotiationSection.vue'
import { NegotiationStatus } from '@/types/payerNegotiation'

// Mock dependencies
vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn((key: string, params?: any) => {
      // Mock translation function
      const translations: Record<string, string> = {
        'common.you': 'You',
        'common.cancel': 'Cancel',
        'common.optional': 'Optional',
        'transactionalChat.actionCards.negotiation.whoShouldPayFirst': 'Who should pay first?',
        'transactionalChat.actionCards.negotiation.systemRecommendation': 'System Recommendation',
        'transactionalChat.actionCards.negotiation.youShouldPayFirst': 'You should pay first',
        'transactionalChat.actionCards.negotiation.otherShouldPayFirst': `${params?.name || 'Other user'} should pay first`,
        'transactionalChat.actionCards.negotiation.iAgree': 'I Agree',
        'transactionalChat.actionCards.negotiation.proposeTheyPayFirst': 'Propose they pay first',
        'transactionalChat.actionCards.negotiation.sendProposal': 'Send Proposal',
        'transactionalChat.actionCards.negotiation.agreeToProposal': 'Agree to Proposal',
        'transactionalChat.actionCards.negotiation.decline': 'Decline',
        'transactionalChat.actionCards.negotiation.cancel': 'Cancel',
        'transactionalChat.actionCards.negotiation.reasons.trustScore': 'Trust score consideration'
      }
      return translations[key] || key
    })
  })
}))

vi.mock('naive-ui', () => ({
  useMessage: () => ({
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  })
}))

vi.mock('@/i18n', () => ({
  i18n: {
    global: {
      t: vi.fn((key: string) => key)
    }
  }
}))

// Mock stores
const mockPayerNegotiationStore = {
  currentNegotiation: null as any,
  fetchNegotiation: vi.fn(),
  acceptCurrentProposal: vi.fn(),
  proposeFirstPayer: vi.fn(),
  cancelNegotiation: vi.fn(),
  initializeSocketListeners: vi.fn()
}

const mockAuthStore = {
  user: { id: 'user123' }
}

const mockTransactionalChatStore = {
  transactionDetails: null as any,
  otherUser: null as any
}

vi.mock('@/stores/payerNegotiation', () => ({
  usePayerNegotiationStore: () => mockPayerNegotiationStore
}))

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => mockAuthStore
}))

vi.mock('@/stores/transactionalChat/transactionalChatStore', () => ({
  useTransactionalChatStore: () => mockTransactionalChatStore
}))

describe('SmartNegotiationSection', () => {
  let wrapper: VueWrapper<any>
  let pinia: any

  const defaultProps = {
    transactionId: 'transaction123'
  }

  const mockTransactionData = {
    amountToSend: 28500000,
    currencyFrom: 'IRR',
    amountToReceive: 500,
    currencyTo: 'CAD'
  }

  const mockNegotiation = {
    partyA_Id: 'partyA123',
    partyB_Id: 'user123',
    systemRecommendedPayerId: 'partyA123',
    negotiationStatus: NegotiationStatus.READY_TO_NEGOTIATE,
    partyA_agreedToCurrentProposal: false,
    partyB_agreedToCurrentProposal: false,
    currentProposal_PayerId: null,
    currentProposal_ById: null,
    currentProposal_Message: null,
    systemRecommendationReason: 'Trust score consideration',
    isFinalOffer: false
  }

  const mockOtherUser = {
    name: 'TestUser',
    reputation: 7
  }

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    // Reset mocks
    vi.clearAllMocks()
    
    // Set default mock data
    mockPayerNegotiationStore.currentNegotiation = mockNegotiation
    mockTransactionalChatStore.transactionDetails = mockTransactionData
    mockTransactionalChatStore.otherUser = mockOtherUser
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  const createWrapper = (props = {}) => {
    wrapper = mount(SmartNegotiationSection, {
      props: { ...defaultProps, ...props },
      global: {
        plugins: [pinia]
      }
    })
    return wrapper
  }

  describe('Component Mounting and Basic Rendering', () => {
    it('should mount successfully', () => {
      const wrapper = createWrapper()
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('[data-testid="smart-negotiation-section"]').exists()).toBe(true)
    })

    it('should display the correct title', () => {
      const wrapper = createWrapper()
      const title = wrapper.find('[data-testid="negotiation-title"]')
      expect(title.text()).toBe('Who should pay first?')
    })

    it('should call fetchNegotiation on mount', () => {
      createWrapper()
      expect(mockPayerNegotiationStore.fetchNegotiation).toHaveBeenCalledWith('transaction123')
    })

    it('should initialize socket listeners', () => {
      createWrapper()
      expect(mockPayerNegotiationStore.initializeSocketListeners).toHaveBeenCalled()
    })
  })

  describe('Loading State', () => {
    it('should show loading state when negotiation data is missing', () => {
      mockPayerNegotiationStore.currentNegotiation = null
      const wrapper = createWrapper()
      
      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)
      expect(wrapper.find('.status-text.loading').exists()).toBe(true)
    })

    it('should show loading state when transaction data is missing', () => {
      mockTransactionalChatStore.transactionDetails = null
      const wrapper = createWrapper()
      
      // When transaction data is missing but negotiation exists, 
      // it should still show the negotiation UI, not loading state
      // The loading is specifically for when negotiation data is missing
      const negotiationState = (wrapper.vm as any).negotiationState
      expect(negotiationState).toBe('system-recommendation') // Based on our mock negotiation
    })
  })

  describe('Payment Amount Calculations', () => {
    describe('When current user is first payer', () => {
      beforeEach(() => {
        mockAuthStore.user.id = 'user123';
        mockPayerNegotiationStore.currentNegotiation = {
          ...mockNegotiation,
          systemRecommendedPayerId: 'user123', // Current user recommended to pay first
          partyA_Id: 'partyA123',
          partyB_Id: 'user123'
        };
        mockTransactionalChatStore.transactionDetails = {
          amountToSend: 500, // User B (current) sends 500 CAD
          currencyFrom: 'CAD',
          amountToReceive: 28500000, // User B (current) receives 28.5M IRR
          currencyTo: 'IRR'
        };
      });

      it('should calculate correct payment amounts when current user pays first', async () => {
        const wrapper = createWrapper();
        await nextTick();
        
        const firstPaymentAmount = (wrapper.vm as any).firstPaymentAmount;
        const secondPaymentAmount = (wrapper.vm as any).secondPaymentAmount;
        
        // Current user (Party B) sends CAD, so this is the first payment.
        expect(firstPaymentAmount).toBe('CA$500');
        // The other user (Party A) sends IRR, so this is the second payment.
        expect(secondPaymentAmount).toContain('IRR');
        expect(secondPaymentAmount).toContain('28,500,000');
      });
    });

    describe('When other user is first payer', () => {
      beforeEach(() => {
        mockAuthStore.user.id = 'user123'; // Current user is Party B
        mockPayerNegotiationStore.currentNegotiation = {
          ...mockNegotiation,
          systemRecommendedPayerId: 'partyA123', // Other user (Party A) recommended to pay first
          partyA_Id: 'partyA123',
          partyB_Id: 'user123'
        };
        mockTransactionalChatStore.transactionDetails = {
          amountToSend: 500, // User B (current) sends 500 CAD
          currencyFrom: 'CAD',
          amountToReceive: 28500000, // User B (current) receives 28.5M IRR
          currencyTo: 'IRR'
        };
      });

      it('should calculate correct payment amounts when other user pays first', async () => {
        const wrapper = createWrapper();
        await nextTick();
        
        const firstPaymentAmount = (wrapper.vm as any).firstPaymentAmount;
        const secondPaymentAmount = (wrapper.vm as any).secondPaymentAmount;
        
        // The other user (Party A) is the first payer, and they send IRR.
        expect(firstPaymentAmount).toContain('IRR');
        expect(firstPaymentAmount).toContain('28,500,000');
        // The current user (Party B) is the second payer, and they send CAD.
        expect(secondPaymentAmount).toBe('CA$500');
      });
    });
  });

  describe('System Recommendation State', () => {
    beforeEach(() => {
      mockPayerNegotiationStore.currentNegotiation = {
        ...mockNegotiation,
        negotiationStatus: NegotiationStatus.READY_TO_NEGOTIATE,
        systemRecommendedPayerId: 'partyA123'
      }
    })

    it('should show system recommendation when available', async () => {
      const wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('[data-testid="recommendation-statement"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="payment-flow-smart"]').exists()).toBe(true)
    })

    it('should show correct recommendation text when other user is recommended', async () => {
      const wrapper = createWrapper()
      await nextTick()
      
      const recommendationText = wrapper.find('.recommendation-main').text()
      expect(recommendationText).toBe('TestUser should pay first')
    })

    it('should show correct recommendation text when current user is recommended', async () => {
      mockPayerNegotiationStore.currentNegotiation.systemRecommendedPayerId = 'user123'
      const wrapper = createWrapper()
      await nextTick()
      
      const recommendationText = wrapper.find('.recommendation-main').text()
      expect(recommendationText).toBe('You should pay first')
    })

    it('should show appropriate action buttons for recommended user', async () => {
      mockPayerNegotiationStore.currentNegotiation.systemRecommendedPayerId = 'user123'
      const wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('[data-testid="accept-recommendation-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="request-counter-btn"]').exists()).toBe(true)
    })

    it('should show single accept button when other user is recommended', async () => {
      const wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('[data-testid="agree-to-recommendation-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="accept-recommendation-btn"]').exists()).toBe(false)
    })
  })

  describe('User Proposal State', () => {
    beforeEach(() => {
      mockPayerNegotiationStore.currentNegotiation = {
        ...mockNegotiation,
        negotiationStatus: NegotiationStatus.PENDING_RESPONSE,
        currentProposal_PayerId: 'partyA123',
        currentProposal_ById: 'partyA123',
        currentProposal_Message: 'I think I should pay second'
      }
    })

    it('should show user proposal state', async () => {
      const wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('[data-testid="proposal-statement"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="proposal-message"]').exists()).toBe(true)
    })

    it('should display proposal message', async () => {
      const wrapper = createWrapper()
      await nextTick()
      
      const messageContent = wrapper.find('.message-content').text()
      expect(messageContent).toBe('"I think I should pay second"')
    })

    it('should show accept and counter proposal buttons', async () => {
      const wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('[data-testid="accept-proposal-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="counter-proposal-btn"]').exists()).toBe(true)
    })

    it('should show decline button', async () => {
      const wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('[data-testid="decline-proposal-btn-full"]').exists()).toBe(true)
    })
  })

  describe('Final Proposal Handling', () => {
    beforeEach(() => {
      mockPayerNegotiationStore.currentNegotiation = {
        ...mockNegotiation,
        negotiationStatus: NegotiationStatus.PENDING_RESPONSE,
        currentProposal_PayerId: 'partyA123',
        currentProposal_ById: 'partyA123',
        isFinalOffer: true
      }
    })

    it('should show decline button in grid for final proposals', async () => {
      const wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('[data-testid="decline-proposal-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="decline-proposal-btn-full"]').exists()).toBe(false)
    })

    it('should show final proposal warning', async () => {
      // Set up conditions for final proposal warning
      mockPayerNegotiationStore.currentNegotiation.isFinalOffer = false
      const wrapper = createWrapper()
      await nextTick()
      
      // Access the computed property that determines if it would be a final proposal
      const wouldBeFinalProposal = (wrapper.vm as any).wouldBeFinalProposal
      if (wouldBeFinalProposal) {
        expect(wrapper.find('[data-testid="final-proposal-warning"]').exists()).toBe(true)
      }
    })
  })

  describe('Action Handlers', () => {
    it('should handle accept system recommendation', async () => {
      mockPayerNegotiationStore.acceptCurrentProposal.mockResolvedValue({})
      mockPayerNegotiationStore.currentNegotiation.systemRecommendedPayerId = 'user123'
      
      const wrapper = createWrapper()
      await nextTick()
      
      const acceptBtn = wrapper.find('[data-testid="accept-recommendation-btn"]')
      await acceptBtn.trigger('click')
      
      expect(mockPayerNegotiationStore.acceptCurrentProposal).toHaveBeenCalledWith('transaction123')
    })

    it('should handle accept user proposal', async () => {
      mockPayerNegotiationStore.acceptCurrentProposal.mockResolvedValue({})
      mockPayerNegotiationStore.currentNegotiation = {
        ...mockNegotiation,
        negotiationStatus: NegotiationStatus.PENDING_RESPONSE,
        currentProposal_PayerId: 'partyA123',
        currentProposal_ById: 'partyA123'
      }
      
      const wrapper = createWrapper()
      await nextTick()
      
      const acceptBtn = wrapper.find('[data-testid="accept-proposal-btn"]')
      await acceptBtn.trigger('click')
      
      expect(mockPayerNegotiationStore.acceptCurrentProposal).toHaveBeenCalledWith('transaction123')
    })

    it('should handle decline proposal', async () => {
      mockPayerNegotiationStore.cancelNegotiation.mockResolvedValue({})
      mockPayerNegotiationStore.currentNegotiation = {
        ...mockNegotiation,
        negotiationStatus: NegotiationStatus.PENDING_RESPONSE,
        currentProposal_PayerId: 'partyA123',
        currentProposal_ById: 'partyA123'
      }
      
      const wrapper = createWrapper()
      await nextTick()
      
      const declineBtn = wrapper.find('[data-testid="decline-proposal-btn-full"]')
      await declineBtn.trigger('click')
      
      expect(mockPayerNegotiationStore.cancelNegotiation).toHaveBeenCalledWith(
        'transaction123',
        'Proposal declined by user.'
      )
    })

    it('should handle counter proposal request', async () => {
      mockPayerNegotiationStore.currentNegotiation.systemRecommendedPayerId = 'user123'
      const wrapper = createWrapper()
      await nextTick()
      
      const counterBtn = wrapper.find('[data-testid="request-counter-btn"]')
      await counterBtn.trigger('click')
      
      // Should show the first counter warning modal
      await nextTick()
      expect(wrapper.find('[data-testid="first-counter-warning-modal"]').exists()).toBe(true)
    })
  })

  describe('Modal Interactions', () => {
    it('should show and handle first counter warning modal', async () => {
      mockPayerNegotiationStore.currentNegotiation.systemRecommendedPayerId = 'user123'
      const wrapper = createWrapper()
      await nextTick()
      
      // Click counter proposal button
      const counterBtn = wrapper.find('[data-testid="request-counter-btn"]')
      await counterBtn.trigger('click')
      await nextTick()
      
      // Modal should be visible
      expect(wrapper.find('[data-testid="first-counter-warning-modal"]').exists()).toBe(true)
      
      // Click proceed
      const proceedBtn = wrapper.find('.modal-btn.primary')
      await proceedBtn.trigger('click')
      await nextTick()
      
      // Should show message input
      expect(wrapper.find('[data-testid="message-input-section"]').exists()).toBe(true)
    })

    it('should handle message input and send proposal', async () => {
      mockPayerNegotiationStore.proposeFirstPayer.mockResolvedValue({})
      
      const wrapper = createWrapper()
      
      // First trigger the message input to show by clicking the counter button
      mockPayerNegotiationStore.currentNegotiation.systemRecommendedPayerId = 'user123'
      await nextTick()
      
      const counterBtn = wrapper.find('[data-testid="request-counter-btn"]')
      await counterBtn.trigger('click')
      await nextTick()
      
      // Proceed through the modal to show message input
      const proceedBtn = wrapper.find('.modal-btn.primary')
      await proceedBtn.trigger('click')
      await nextTick()
      
      // Enter custom message
      const textarea = wrapper.find('[data-testid="custom-message-input"]')
      await textarea.setValue('My counter proposal message')
      
      // Click send proposal
      const sendBtn = wrapper.find('[data-testid="send-proposal-btn"]')
      await sendBtn.trigger('click')
      
      expect(mockPayerNegotiationStore.proposeFirstPayer).toHaveBeenCalledWith(
        'transaction123',
        'partyA123', // other user ID
        'My counter proposal message'
      )
    })
  })

  describe('Error Handling', () => {
    it('should handle accept proposal error', async () => {
      mockPayerNegotiationStore.acceptCurrentProposal.mockRejectedValue(new Error('Network error'))
      mockPayerNegotiationStore.currentNegotiation.systemRecommendedPayerId = 'user123'
      
      const wrapper = createWrapper()
      await nextTick()
      
      const acceptBtn = wrapper.find('[data-testid="accept-recommendation-btn"]')
      await acceptBtn.trigger('click')
      
      // Should not throw and should handle error gracefully
      expect(mockPayerNegotiationStore.acceptCurrentProposal).toHaveBeenCalled()
    })

    it('should handle proposal counter error', async () => {
      mockPayerNegotiationStore.proposeFirstPayer.mockRejectedValue(new Error('Network error'))
      
      const wrapper = createWrapper()
      
      // Setup to show message input by triggering counter proposal flow
      mockPayerNegotiationStore.currentNegotiation.systemRecommendedPayerId = 'user123'
      await nextTick()
      
      const counterBtn = wrapper.find('[data-testid="request-counter-btn"]')
      await counterBtn.trigger('click')
      await nextTick()
      
      // Proceed through modal
      const proceedBtn = wrapper.find('.modal-btn.primary')
      await proceedBtn.trigger('click')
      await nextTick()
      
      const sendBtn = wrapper.find('[data-testid="send-proposal-btn"]')
      await sendBtn.trigger('click')
      
      expect(mockPayerNegotiationStore.proposeFirstPayer).toHaveBeenCalled()
    })
  })

  describe('Finalized and Cancelled States', () => {
    it('should show finalized state', async () => {
      mockPayerNegotiationStore.currentNegotiation = {
        ...mockNegotiation,
        negotiationStatus: NegotiationStatus.FINALIZED,
        finalizedPayerId: 'user123'
      }
      
      const wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('[data-testid="success-content"]').exists()).toBe(true)
      expect(wrapper.find('.success-checkmark').exists()).toBe(true)
    })

    it('should show cancelled state', async () => {
      mockPayerNegotiationStore.currentNegotiation = {
        ...mockNegotiation,
        negotiationStatus: NegotiationStatus.CANCELLED
      }
      
      const wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('[data-testid="cancelled-content"]').exists()).toBe(true)
      expect(wrapper.find('.cancelled-icon').exists()).toBe(true)
    })
  })

  describe('Waiting State', () => {
    it('should show waiting state when user has made a proposal', async () => {
      mockPayerNegotiationStore.currentNegotiation = {
        ...mockNegotiation,
        negotiationStatus: NegotiationStatus.PENDING_RESPONSE,
        currentProposal_ById: 'user123', // Current user made the proposal
        currentProposal_PayerId: 'partyA123',
        currentProposal_Message: 'My proposal message'
      }
      
      const wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('[data-testid="waiting-content"]').exists()).toBe(true)
      expect(wrapper.find('.waiting-dots').exists()).toBe(true)
      
      // Check for the payment flow visualization
      expect(wrapper.find('[data-testid="payment-flow-smart-waiting"]').exists()).toBe(true)
    })
  })

  describe('Responsive Design and Accessibility', () => {
    it('should have proper data-testid attributes', () => {
      const wrapper = createWrapper()
      
      expect(wrapper.find('[data-testid="smart-negotiation-section"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="negotiation-title"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="negotiation-status"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="card-actions"]').exists()).toBe(true)
    })

    it('should handle empty states gracefully', () => {
      mockTransactionalChatStore.otherUser = null
      const wrapper = createWrapper()
      
      // Should use fallback user info
      const otherUserInfo = (wrapper.vm as any).otherUserInfo
      expect(otherUserInfo.name).toContain('User')
      expect(otherUserInfo.reputation).toBe(5)
    })
  })

  describe('Props and Emits', () => {
    it('should emit negotiationUpdate on successful actions', async () => {
      mockPayerNegotiationStore.acceptCurrentProposal.mockResolvedValue({})
      mockPayerNegotiationStore.currentNegotiation.systemRecommendedPayerId = 'user123'
      
      const wrapper = createWrapper()
      await nextTick()
      
      const acceptBtn = wrapper.find('[data-testid="accept-recommendation-btn"]')
      await acceptBtn.trigger('click')
      
      // Wait for async operation
      await new Promise(resolve => setTimeout(resolve, 0))
      
      expect(wrapper.emitted('negotiationUpdate')).toBeDefined()
    })

    it('should watch transactionId prop changes', async () => {
      const wrapper = createWrapper()
      
      // Change the transactionId prop
      await wrapper.setProps({ transactionId: 'newTransaction456' })
      
      expect(mockPayerNegotiationStore.fetchNegotiation).toHaveBeenCalledWith('newTransaction456')
    })
  })
})