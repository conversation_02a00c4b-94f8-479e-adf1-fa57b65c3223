const { PrismaClient } = require('@prisma/client');

async function testCreatePaymentMethod() {
  const prisma = new PrismaClient();
  
  try {
    console.log('=== Testing Payment Method Creation ===');
    
    const userId = 'cmbqrmwcv0001vl48wlfffva4';
    
    // Test creating a new CAD payment method
    const newMethod = await prisma.paymentReceivingInfo.create({
      data: {
        userId: userId,
        currency: 'CAD',
        paymentMethodType: 'BANK_TRANSFER',
        bankName: 'Test Bank',
        accountNumber: '*********',
        accountHolderName: 'Test User',
        isDefaultForUser: true,
        isActive: true
      }
    });
    
    console.log('✅ Successfully created new payment method:');
    console.log(`- ID: ${newMethod.id.slice(-8)}`);
    console.log(`- Currency: ${newMethod.currency}`);
    console.log(`- Bank: ${newMethod.bankName}`);
    console.log(`- Default: ${newMethod.isDefaultForUser}`);
    console.log(`- Active: ${newMethod.isActive}`);
    
    // Show all methods for this user
    const allMethods = await prisma.paymentReceivingInfo.findMany({
      where: { userId: userId },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log('\n=== All payment methods for user ===');
    allMethods.forEach(method => {
      console.log(`- ${method.currency}: ID ${method.id.slice(-8)}, Active: ${method.isActive}, Default: ${method.isDefaultForUser}, Bank: ${method.bankName}`);
    });
    
  } catch (error) {
    console.error('❌ Error creating payment method:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testCreatePaymentMethod();
