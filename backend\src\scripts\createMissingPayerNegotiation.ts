import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createMissingPayerNegotiation() {
  const transactionId = 'cmckn520l000jvlv4cvit2ch0';
  
  console.log('Creating missing PayerNegotiation record...');
  
  try {
    // Get the transaction
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId }
    });
    
    if (!transaction) {
      console.log('❌ Transaction not found');
      return;
    }
    
    // Check if PayerNegotiation already exists
    const existingNegotiation = await prisma.payerNegotiation.findUnique({
      where: { transactionId }
    });
    
    if (existingNegotiation) {
      console.log('✅ PayerNegotiation already exists');
      return;
    }
    
    // Create PayerNegotiation record
    const payerNegotiation = await prisma.payerNegotiation.create({
      data: {
        transactionId: transactionId,
        partyA_Id: transaction.currencyAProviderId,
        partyB_Id: transaction.currencyBProviderId,
        negotiationStatus: 'PENDING_RECEIVING_INFO',
        partyA_receivingInfoStatus: 'PENDING_INPUT',
        partyB_receivingInfoStatus: 'PENDING_INPUT'
      }
    });
    
    console.log('✅ PayerNegotiation created successfully:');
    console.log('  - Transaction ID:', payerNegotiation.transactionId);
    console.log('  - Negotiation Status:', payerNegotiation.negotiationStatus);
    console.log('  - Party A Status:', payerNegotiation.partyA_receivingInfoStatus);
    console.log('  - Party B Status:', payerNegotiation.partyB_receivingInfoStatus);
    
  } catch (error) {
    console.error('Error creating PayerNegotiation:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createMissingPayerNegotiation();
