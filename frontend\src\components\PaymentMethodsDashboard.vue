<template>
  <div class="payment-methods-dashboard">
    <!-- Header -->
    <div class="dashboard-header mb-6">
      <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-200">
        Payment Methods
      </h2>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        Manage your payment information for different currencies
      </p>
    </div>

    <!-- Currency Tabs -->
    <NTabs
      v-model:value="selectedCurrency"
      type="line"
      animated
      @update-value="handleCurrencyChange"
      class="mb-6"
    >
      <NTabPane
        v-for="currency in supportedCurrencies"
        :key="currency.code"
        :name="currency.code"
        :tab="currency.label"
      >
        <!-- Payment Methods for Selected Currency -->
        <div class="currency-content">
          <!-- Add New Payment Method Button -->
          <div class="add-method-section mb-6">
            <NButton
              type="primary"
              @click="openCreateForm"
              :loading="isLoading"
              class="w-full sm:w-auto"
            >
              <template #icon>
                <Icon name="mdi:plus" />
              </template>
              Add {{ selectedCurrency }} Payment Method
            </NButton>
          </div>

          <!-- Loading State -->
          <div v-if="isLoading && paymentMethods.length === 0" class="loading-state">
            <NSkeleton height="120px" class="mb-4" />
            <NSkeleton height="120px" class="mb-4" />
            <NSkeleton height="120px" />
          </div>

          <!-- Empty State -->
          <div
            v-else-if="!isLoading && paymentMethods.length === 0"
            class="empty-state text-center py-12"
          >
            <Icon name="mdi:credit-card-off-outline" size="64" class="text-gray-400 mb-4" />
            <h3 class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
              No payment methods found
            </h3>
            <p class="text-gray-500 dark:text-gray-500 mb-6">
              Add your first {{ selectedCurrency }} payment method to start receiving payments
            </p>
            <NButton type="primary" @click="openCreateForm">
              Add Payment Method
            </NButton>
          </div>

          <!-- Payment Methods List -->
          <div v-else class="payment-methods-grid">
            <div
              v-for="method in paymentMethods"
              :key="method.id"
              class="payment-method-card"
            >
              <NCard
                :bordered="true"
                class="payment-method-item"
                :class="{ 'default-method': method.isDefaultForUser }"
              >
                <!-- Method Header -->
                <div class="method-header flex justify-between items-start mb-4">
                  <div class="method-info">
                    <h4 class="font-semibold text-gray-800 dark:text-gray-200">
                      {{ method.bankName }}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      {{ formatAccountNumber(method.accountNumber) }}
                    </p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      {{ method.accountHolderName }}
                    </p>
                  </div>
                  
                  <!-- Method Type Badge -->
                  <NBadge
                    :type="getMethodTypeBadgeType(method.paymentMethodType)"
                    :value="formatMethodType(method.paymentMethodType)"
                  />
                </div>

                <!-- Default Badge -->
                <div v-if="method.isDefaultForUser" class="default-badge mb-4">
                  <NBadge type="success" value="Default" />
                </div>

                <!-- Validation Status -->
                <div class="validation-status mb-4">
                  <div class="flex items-center">
                    <Icon
                      :name="method.validationStatus === 'complete' ? 'mdi:check-circle' : 'mdi:alert-circle'"
                      :class="method.validationStatus === 'complete' ? 'text-green-500' : 'text-orange-500'"
                      size="16"
                      class="mr-2"
                    />
                    <span
                      :class="method.validationStatus === 'complete' ? 'text-green-600' : 'text-orange-600'"
                      class="text-sm font-medium"
                    >
                      {{ method.validationStatus === 'complete' ? 'Complete' : 'Incomplete' }}
                    </span>
                  </div>
                  
                  <!-- Missing Fields Warning -->
                  <div v-if="method.missingFields.length > 0" class="mt-2">
                    <p class="text-xs text-orange-600">
                      Missing: {{ method.missingFields.join(', ') }}
                    </p>
                  </div>
                </div>

                <!-- Additional Details -->
                <div v-if="method.swiftCode || method.iban || method.notes" class="additional-details mb-4">
                  <div v-if="method.swiftCode" class="detail-item">
                    <span class="text-xs text-gray-500">SWIFT:</span>
                    <span class="text-sm">{{ method.swiftCode }}</span>
                  </div>
                  <div v-if="method.iban" class="detail-item">
                    <span class="text-xs text-gray-500">IBAN:</span>
                    <span class="text-sm">{{ method.iban }}</span>
                  </div>
                  <div v-if="method.notes" class="detail-item">
                    <span class="text-xs text-gray-500">Notes:</span>
                    <span class="text-sm">{{ method.notes }}</span>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="method-actions">
                  <NSpace>
                    <NButton
                      size="small"
                      @click="editMethod(method)"
                    >
                      Edit
                    </NButton>
                    
                    <NButton
                      v-if="!method.isDefaultForUser"
                      size="small"
                      type="primary"
                      @click="setAsDefault(method.id)"
                      :loading="settingDefault === method.id"
                    >
                      Set Default
                    </NButton>
                    
                    <NButton
                      size="small"
                      type="error"
                      @click="confirmDeactivate(method)"
                      :loading="deactivating === method.id"
                    >
                      Remove
                    </NButton>
                  </NSpace>
                </div>
              </NCard>
            </div>
          </div>
        </div>
      </NTabPane>
    </NTabs>

    <!-- Create/Edit Modal -->
    <NModal
      v-model:show="showModal"
      preset="card"
      :title="editingMethod ? 'Edit Payment Method' : 'Add Payment Method'"
      style="width: 600px"
      :mask-closable="false"
    >
      <PaymentMethodForm
        :method="editingMethod"
        :currency="selectedCurrency"
        @submit="handleFormSubmit"
        @cancel="closeModal"
        :loading="isSubmitting"
      />
    </NModal>

    <!-- Deactivate Confirmation Modal -->
    <NModal
      v-model:show="showDeactivateModal"
      preset="dialog"
      title="Remove Payment Method"
      positive-text="Remove"
      negative-text="Cancel"
      @positive-click="handleDeactivate"
      @negative-click="showDeactivateModal = false"
    >
      <p>Are you sure you want to remove this payment method?</p>
      <p class="text-sm text-gray-600 mt-2">
        <strong>{{ methodToDeactivate?.bankName }}</strong> - {{ formatAccountNumber(methodToDeactivate?.accountNumber || '') }}
      </p>
      <p class="text-sm text-orange-600 mt-2">This action cannot be undone.</p>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import {
  NTabs,
  NTabPane,
  NButton,
  NCard,
  NBadge,
  NSpace,
  NModal,
  NSkeleton,
  useMessage,
  useNotification
} from 'naive-ui';
import { Icon } from '@iconify/vue';
import PaymentMethodForm from './PaymentMethodForm.vue';
import { usePaymentMethodsStore } from '@/stores/paymentMethodsStore';
import type { PaymentMethodWithValidation, PaymentMethodType } from '@/types/paymentMethods';

// Debounce utility function
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Store and composables
const paymentMethodsStore = usePaymentMethodsStore();
const message = useMessage();
const notification = useNotification();

// Reactive state
const selectedCurrency = ref('IRR');
const showModal = ref(false);
const showDeactivateModal = ref(false);
const editingMethod = ref<PaymentMethodWithValidation | null>(null);
const methodToDeactivate = ref<PaymentMethodWithValidation | null>(null);
const settingDefault = ref<string | null>(null);
const deactivating = ref<string | null>(null);

// Computed properties
const isLoading = computed(() => paymentMethodsStore.isLoading);
const isSubmitting = computed(() => paymentMethodsStore.isSubmitting);
const paymentMethods = computed(() => paymentMethodsStore.getMethodsByCurrency(selectedCurrency.value));

// Supported currencies configuration
const supportedCurrencies = [
  { code: 'IRR', label: 'Iranian Rial (IRR)' },
  { code: 'CAD', label: 'Canadian Dollar (CAD)' },
  { code: 'USD', label: 'US Dollar (USD)' },
  { code: 'EUR', label: 'Euro (EUR)' },
  { code: 'GBP', label: 'British Pound (GBP)' }
];

// Methods
const handleCurrencyChange = async (currency: string) => {
  selectedCurrency.value = currency;
  await loadPaymentMethods();
};

const loadPaymentMethods = async () => {
  try {
    await paymentMethodsStore.fetchMethodsByCurrency(selectedCurrency.value);
  } catch (error) {
    notification.error({
      title: 'Error',
      content: 'Failed to load payment methods. Please try again.',
      duration: 3000
    });
  }
};

// Create debounced version for the watcher
const debouncedLoadPaymentMethods = debounce(async () => {
  await loadPaymentMethods();
}, 300); // 300ms delay

const openCreateForm = () => {
  editingMethod.value = null;
  showModal.value = true;
};

const editMethod = (method: PaymentMethodWithValidation) => {
  editingMethod.value = method;
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
  editingMethod.value = null;
};

const handleFormSubmit = async (formData: any) => {
  try {
    if (editingMethod.value) {
      await paymentMethodsStore.updateMethod(editingMethod.value.id, formData);
      message.success('Payment method updated successfully');
    } else {
      await paymentMethodsStore.createMethod(formData);
      message.success('Payment method created successfully');
    }
    closeModal();
    await loadPaymentMethods();
  } catch (error) {
    notification.error({
      title: 'Error',
      content: editingMethod.value ? 'Failed to update payment method' : 'Failed to create payment method',
      duration: 3000
    });
  }
};

const setAsDefault = async (methodId: string) => {
  settingDefault.value = methodId;
  try {
    await paymentMethodsStore.setAsDefault(methodId);
    message.success('Default payment method updated');
    await loadPaymentMethods();
  } catch (error) {
    notification.error({
      title: 'Error',
      content: 'Failed to set as default payment method',
      duration: 3000
    });
  } finally {
    settingDefault.value = null;
  }
};

const confirmDeactivate = (method: PaymentMethodWithValidation) => {
  methodToDeactivate.value = method;
  showDeactivateModal.value = true;
};

const handleDeactivate = async () => {
  if (!methodToDeactivate.value) return;
  
  deactivating.value = methodToDeactivate.value.id;
  try {
    await paymentMethodsStore.deactivateMethod(methodToDeactivate.value.id);
    message.success('Payment method removed successfully');
    await loadPaymentMethods();
  } catch (error) {
    notification.error({
      title: 'Error',
      content: 'Failed to remove payment method',
      duration: 3000
    });
  } finally {
    deactivating.value = null;
    showDeactivateModal.value = false;
    methodToDeactivate.value = null;
  }
};

// Utility functions
const formatAccountNumber = (accountNumber: string): string => {
  if (!accountNumber || accountNumber.length <= 4) return accountNumber;
  const lastFour = accountNumber.slice(-4);
  const masked = '*'.repeat(Math.min(accountNumber.length - 4, 8));
  return `${masked}${lastFour}`;
};

const formatMethodType = (type: PaymentMethodType): string => {
  const typeMap: Record<PaymentMethodType, string> = {
    BANK_TRANSFER: 'Bank Transfer',
    DIGITAL_WALLET: 'Digital Wallet',
    CRYPTO_WALLET: 'Crypto Wallet',
    MOBILE_MONEY: 'Mobile Money',
    CASH_PICKUP: 'Cash Pickup'
  };
  return typeMap[type] || type;
};

const getMethodTypeBadgeType = (type: PaymentMethodType): 'default' | 'info' | 'success' | 'warning' | 'error' => {
  const typeMap: Record<PaymentMethodType, 'default' | 'info' | 'success' | 'warning' | 'error'> = {
    BANK_TRANSFER: 'info',
    DIGITAL_WALLET: 'success',
    CRYPTO_WALLET: 'warning',
    MOBILE_MONEY: 'info',
    CASH_PICKUP: 'default'
  };
  return typeMap[type] || 'default';
};

// Lifecycle
onMounted(async () => {
  await loadPaymentMethods();
});

// Watch for currency changes with debouncing
watch(selectedCurrency, async (newCurrency) => {
  debouncedLoadPaymentMethods();
});
</script>

<style scoped>
.payment-methods-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.payment-methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.payment-method-card {
  height: 100%;
}

.payment-method-item {
  transition: all 0.3s ease;
}

.payment-method-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.default-method {
  border-color: #18a058;
  box-shadow: 0 0 0 1px #18a058;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.method-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.loading-state,
.empty-state {
  padding: 24px;
}

@media (max-width: 768px) {
  .payment-methods-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-header h2 {
    font-size: 1.5rem;
  }
}
</style>
