import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import { ref } from 'vue'
import LanguageSelector from '../LanguageSelector.vue'

// Create mock functions that we can spy on
const mockSetLanguage = vi.fn()
const mockT = vi.fn((key: string) => `translated:${key}`)

// Create reactive refs that we can change in tests
const mockCurrentLanguage = ref('en')

// Mock the useTranslation composable
vi.mock('@/composables/useTranslation', () => ({
  useTranslation: () => ({
    t: mockT,
    currentLanguage: mockCurrentLanguage,
    setLanguage: mockSetLanguage
  })
}))

describe('LanguageSelector', () => {
  let wrapper: any

  beforeEach(() => {
    // Clear all mocks
    vi.clearAllMocks()
    // Reset language to default
    mockCurrentLanguage.value = 'en'
  })

  const createComponent = () => {
    return mount(LanguageSelector, {
      global: {
        plugins: [createTestingPinia()]
      }
    })
  }
  describe('Component Rendering', () => {    it('should render without errors', () => {
      wrapper = createComponent()
      expect(wrapper.exists()).toBe(true)
    })

    it('should render NButton component', () => {
      wrapper = createComponent()
      
      const button = wrapper.find('[data-testid="nbutton"]')
      expect(button.exists()).toBe(true)
    })

    it('should have correct CSS classes applied', () => {
      wrapper = createComponent()
      
      const component = wrapper.find('.language-selector')
      expect(component.exists()).toBe(true)
    })
  })

  describe('Language Display', () => {
    it('should show "فا" when current language is English', async () => {
      mockCurrentLanguage.value = 'en'
      wrapper = createComponent()
      await wrapper.vm.$nextTick()
      
      // When current language is English, should show Persian option
      expect(wrapper.text()).toContain('فا')
    })

    it('should show "EN" when current language is Persian', async () => {
      mockCurrentLanguage.value = 'fa'
      wrapper = createComponent()
      await wrapper.vm.$nextTick()
      
      // When current language is Persian, should show English option
      expect(wrapper.text()).toContain('EN')
    })
  })

  describe('User Interactions', () => {
    it('should call setLanguage with "fa" when current language is English and button is clicked', async () => {
      mockCurrentLanguage.value = 'en'
      wrapper = createComponent()
      
      const button = wrapper.find('[data-testid="nbutton"]')
      await button.trigger('click')
      
      expect(mockSetLanguage).toHaveBeenCalledWith('fa')
    })

    it('should call setLanguage with "en" when current language is Persian and button is clicked', async () => {
      mockCurrentLanguage.value = 'fa'
      wrapper = createComponent()
      
      const button = wrapper.find('[data-testid="nbutton"]')
      await button.trigger('click')
      
      expect(mockSetLanguage).toHaveBeenCalledWith('en')
    })

    it('should call setLanguage only once per click', async () => {
      mockCurrentLanguage.value = 'en'
      wrapper = createComponent()
      
      const button = wrapper.find('[data-testid="nbutton"]')
      await button.trigger('click')
      
      expect(mockSetLanguage).toHaveBeenCalledTimes(1)
    })
  })

  describe('Accessibility', () => {
    it('should have accessible button structure', () => {
      wrapper = createComponent()
      
      const button = wrapper.find('[data-testid="nbutton"]')
      expect(button.exists()).toBe(true)
      expect(button.element.tagName).toBe('BUTTON')
    })

    it('should be focusable and clickable', () => {
      wrapper = createComponent()
      
      const button = wrapper.find('[data-testid="nbutton"]')
      expect(button.element.tagName).toBe('BUTTON')
    })
  })

  describe('Translation Integration', () => {
    it('should use the translation composable', () => {
      wrapper = createComponent()
      
      // The component should have access to translation functions
      expect(wrapper.vm).toBeDefined()
    })

    it('should work with both language directions', async () => {
      // Test English to Persian
      mockCurrentLanguage.value = 'en'
      wrapper = createComponent()
      await wrapper.vm.$nextTick()
      expect(wrapper.text()).toContain('فا')
      
      // Test Persian to English
      mockCurrentLanguage.value = 'fa'
      wrapper = createComponent()
      await wrapper.vm.$nextTick()
      expect(wrapper.text()).toContain('EN')
    })
  })

  describe('Error Handling', () => {
    it('should handle missing composable gracefully', () => {
      // Should not throw error during mounting even with mock issues
      expect(() => {
        wrapper = createComponent()
      }).not.toThrow()
    })

    it('should handle undefined language gracefully', () => {
      // Mock with undefined language
      mockCurrentLanguage.value = undefined as any
      
      expect(() => {
        wrapper = createComponent()
      }).not.toThrow()
    })
  })
})
