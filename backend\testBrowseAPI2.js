// Test the actual browse API endpoint 
const fetch = require('node-fetch');

async function testBrowseAPI() {
  try {
    console.log('Testing actual browse API endpoint...\n');

    // First get a user to authenticate as
    const response = await fetch('http://localhost:3000/api/browse', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // You'll need to get an actual auth token - for now let's test without auth
      }
    });

    if (!response.ok) {
      console.log(`API Response Status: ${response.status}`);
      const errorText = await response.text();
      console.log('Error:', errorText);
      return;
    }

    const data = await response.json();
    console.log('API Response:', JSON.stringify(data, null, 2));

    // Look for the offer we know has a completed transaction
    const targetOffer = data.find(offer => offer.id === 'cmb40jjag002dvly8m8r2gwku');
    if (targetOffer) {
      console.log('\n=== TARGET OFFER WITH COMPLETED TRANSACTION ===');
      console.log('Offer ID:', targetOffer.id);
      console.log('Transaction Status:', targetOffer.transactionStatus);
      console.log('Negotiation Status:', targetOffer.negotiationStatus);
      console.log('Current User Interest Status:', targetOffer.currentUserInterestStatus);
      console.log('Current User Has Shown Interest:', targetOffer.currentUserHasShownInterest);
      console.log('Chat Session ID:', targetOffer.chatSessionId);
    } else {
      console.log('\nTarget offer not found in API response');
    }

  } catch (error) {
    console.error('Error testing browse API:', error);
  }
}

testBrowseAPI();
