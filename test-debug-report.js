/**
 * Test script for debug report submission debugging
 * This script helps identify validation issues with the debug report endpoint
 */
const axios = require('axios');

// Construct a minimal test report
const testReport = {
  logs: [{
    timestamp: new Date().toISOString(),
    level: 'ERROR',
    message: 'Test error message',
    context: { source: 'test-script' }
  }],
  reportDetails: {
    type: 'bug',
    severity: 'medium',
    title: 'Test Bug Report',
    description: 'This is a test report to debug validation issues'
  },
  timestamp: new Date().toISOString(),
  sessionId: 'test_session_' + Date.now(),
  // We're deliberately not including userIdentification to see if that's causing the issue
};

// Version with userIdentification
const testReportWithUser = {
  ...testReport,
  userIdentification: {
    userId: 'test-user-123',
    email: '<EMAIL>'
  }
};

// Function to run tests
async function runTests() {
  console.log('Sending test report WITHOUT userIdentification...');
  try {
    const response = await axios.post('http://localhost:3000/api/debug/report-issue', testReport, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('SUCCESS! Response:', response.data);
  } catch (error) {
    console.error('Error status:', error.response?.status);
    console.error('Error data:', error.response?.data);
  }

  console.log('\nSending test report WITH userIdentification...');
  try {
    const response = await axios.post('http://localhost:3000/api/debug/report-issue', testReportWithUser, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('SUCCESS! Response:', response.data);
  } catch (error) {
    console.error('Error status:', error.response?.status);
    console.error('Error data:', error.response?.data);
  }
}

runTests();
