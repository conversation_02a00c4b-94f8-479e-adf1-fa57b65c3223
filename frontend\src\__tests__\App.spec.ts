import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { createTestingP<PERSON> } from '@pinia/testing'
import { h } from 'vue'

import App from '../App.vue'
import { useAuthStore } from '@/stores/auth' // Adjust path if needed
import { NConfigProvider, NLayout, NLayoutHeader, NLayoutContent, NMenu } from 'naive-ui' // Import Naive UI components used

// Mock Components for simplicity in testing App structure
const MockLanding = { template: '<div>Landing Page</div>', name: 'Landing' }
const MockLogin = { template: '<div>Login Page</div>', name: 'Login' }
const MockRegister = { template: '<div>Register Page</div>', name: 'Register' }
const MockHome = { template: '<div>Home Page</div>', name: 'Home' }
const MockProfile = { template: '<div>Profile Page</div>', name: 'Profile' }

const routes: RouteRecordRaw[] = [
  { path: '/', name: 'home', component: MockHome },
  { path: '/landing', name: 'landing', component: MockLanding }, // Ensure name is 'landing'
  { path: '/login', name: 'login', component: MockLogin }, // Ensure name is 'login'
  { path: '/register', name: 'register', component: MockRegister }, // Ensure name is 'register'
  { path: '/profile', name: 'profile', component: MockProfile },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// Helper function to mount the component
const mountApp = async (initialRoute = '/') => {
  // First clear any existing navigation
  router.push('/')
  await router.isReady()
  
  // Then navigate to the desired route
  router.push(initialRoute)
  await router.isReady() 

  const wrapper = mount(App, {
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            auth: { user: null, token: null },
          },
          stubActions: false,
        }),
      ],
      components: {
        NConfigProvider,
        NLayout,
        NLayoutHeader,
        NLayoutContent,
        NMenu,
        'router-link': {
          props: ['to'],
          template: '<a :href="to"><slot /></a>'
        },
      },
    },
  })

  const authStore = useAuthStore()
  
  // Add multiple ticks to ensure the router-view updates fully
  await wrapper.vm.$nextTick()
  await wrapper.vm.$nextTick()
  
  return { wrapper, authStore, router }
}

describe('App.vue', () => {
  beforeEach(() => {
    // Reset mocks and store state before each test
    vi.clearAllMocks()
    // Reset Pinia store state if necessary (createTestingPinia handles some reset)
  })

  it('calls initializeAuth on mount', async () => {
    const { authStore } = await mountApp()
    expect(authStore.initializeAuth).toHaveBeenCalledTimes(1)
  })

  // --- Unauthenticated Tests ---

  it('renders simple layout for landing route', async () => {
    // Create a wrapper with a mocked router-view that provides the correct route name
    const wrapper = mount(App, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            initialState: { auth: { user: null, token: null } },
          }),
        ],
        stubs: {
          RouterView: {
            template: `
              <div>
                <slot :Component="component" :route="route"></slot>
              </div>
            `,
            setup() {
              return {
                component: MockLanding,
                route: { name: 'landing', path: '/landing' }
              }
            }
          }
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    expect(wrapper.html()).toContain('Landing Page')
    expect(wrapper.findComponent(NLayout).exists()).toBe(false)
    expect(wrapper.findComponent(NMenu).exists()).toBe(false)
  })

  it('renders simple layout for login route', async () => {
    // Use the same direct stubbing approach that worked for landing route
    const wrapper = mount(App, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            initialState: { auth: { user: null, token: null } },
          }),
        ],
        stubs: {
          RouterView: {
            template: `
              <div>
                <slot :Component="component" :route="route"></slot>
              </div>
            `,
            setup() {
              return {
                component: MockLogin,
                route: { name: 'login', path: '/login' }
              }
            }
          }
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    expect(wrapper.html()).toContain('Login Page')
    expect(wrapper.findComponent(NLayout).exists()).toBe(false)
    expect(wrapper.findComponent(NMenu).exists()).toBe(false)
  })

  it('renders simple layout for register route', async () => {
    // Use the same direct stubbing approach
    const wrapper = mount(App, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            initialState: { auth: { user: null, token: null } },
          }),
        ],
        stubs: {
          RouterView: {
            template: `
            <div>
              <slot :Component="component" :route="route"></slot>
            </div>
          `,
            setup() {
              return {
                component: MockRegister,
                route: { name: 'register', path: '/register' }
              }
            }
          }
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    expect(wrapper.html()).toContain('Register Page')
    expect(wrapper.findComponent(NLayout).exists()).toBe(false)
    expect(wrapper.findComponent(NMenu).exists()).toBe(false)
  })

  it('renders main layout with header and menu for other routes when unauthenticated', async () => {
    const wrapper = mount(App, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            initialState: { auth: { user: null, token: null } },
          }),
        ],
        stubs: {
          RouterView: {
            template: `
            <div>
              <slot :Component="component" :route="route"></slot>
            </div>
          `,
            setup() {
              return {
                component: MockHome,
                route: { name: 'home', path: '/' } // Not landing, login, or register
              }
            }
          }
        },
        components: {
          NConfigProvider, 
          NLayout, 
          NLayoutHeader,
          NLayoutContent, 
          NMenu
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    expect(wrapper.findComponent(NLayout).exists()).toBe(true)
    expect(wrapper.findComponent(NLayoutHeader).exists()).toBe(true)
    expect(wrapper.findComponent(NMenu).exists()).toBe(true)
    expect(wrapper.html()).toContain('Home Page')
  })

  it('displays correct menu items when unauthenticated', async () => {
    const wrapper = mount(App, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            initialState: { auth: { user: null, token: null } },
          }),
        ],
        stubs: {
          RouterView: {
            template: `
            <div>
              <slot :Component="component" :route="route"></slot>
            </div>
          `,
            setup() {
              return {
                component: MockHome,
                route: { name: 'home', path: '/' }
              }
            }
          }
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    // Check for menu links in rendered HTML
    const html = wrapper.html()
    expect(html).toContain('Home')
    expect(html).toContain('Login')
    expect(html).toContain('Register')
  })

  it('navigates to login when login menu item is selected', async () => {
    const { wrapper, router } = await mountApp('/')
    const pushSpy = vi.spyOn(router, 'push')
    const menu = wrapper.findComponent(NMenu)

    // Simulate the @select event from NMenu
    await menu.vm.$emit('select', 'login')

    expect(pushSpy).toHaveBeenCalledWith('/login')
  })

    it('navigates to register when register menu item is selected', async () => {
    const { wrapper, router } = await mountApp('/')
    const pushSpy = vi.spyOn(router, 'push')
    const menu = wrapper.findComponent(NMenu)

    await menu.vm.$emit('select', 'register')

    expect(pushSpy).toHaveBeenCalledWith('/register')
  })

  // --- Authenticated Tests ---

  it('renders main layout with header and menu for routes when authenticated', async () => {
    const { wrapper, authStore } = await mountApp('/')
    // Set authenticated state matching UserInfo interface
    authStore.user = { id: '1', email: '<EMAIL>' }
    authStore.token = 'fake-token'
    await wrapper.vm.$nextTick() // Wait for computed properties to update

    expect(wrapper.findComponent(NLayout).exists()).toBe(true)
    expect(wrapper.findComponent(NLayoutHeader).exists()).toBe(true)
    expect(wrapper.findComponent(NMenu).exists()).toBe(true)
    expect(wrapper.findComponent(MockHome).exists()).toBe(true)
  })

  it('displays correct menu items when authenticated', async () => {
    const { wrapper, authStore } = await mountApp('/')
    // Set authenticated state matching UserInfo interface
    authStore.user = { id: '1', email: '<EMAIL>' }
    authStore.token = 'fake-token'
    await wrapper.vm.$nextTick()

    const menu = wrapper.findComponent(NMenu)
    const options = menu.props('options') as any[]

    expect(options).toHaveLength(2)
    expect(options[0].key).toBe('home')
    expect(options[1].key).toBe('user')
    // Check rendered label for user email
    expect(options[1].label()).toBe('<EMAIL>')
    expect(options[1].children).toHaveLength(2)
    expect(options[1].children[0].key).toBe('profile')
    expect(options[1].children[0].label).toBe('Profile')
    expect(options[1].children[1].key).toBe('logout')
    expect(options[1].children[1].label).toBe('Logout')
  })

  it('navigates to profile when profile menu item is selected', async () => {
    const { wrapper, authStore, router } = await mountApp('/')
    // Set authenticated state matching UserInfo interface
    authStore.user = { id: '1', email: '<EMAIL>' }
    authStore.token = 'fake-token'
    await wrapper.vm.$nextTick()

    const pushSpy = vi.spyOn(router, 'push')
    const menu = wrapper.findComponent(NMenu)

    // Simulate selecting the 'profile' key (nested under 'user')
    await menu.vm.$emit('select', 'profile')

    expect(pushSpy).toHaveBeenCalledWith('/profile')
  })

  it('calls logout action and navigates to login when logout menu item is selected', async () => {
    const { wrapper, authStore, router } = await mountApp('/')
    // Set authenticated state matching UserInfo interface
    authStore.user = { id: '1', email: '<EMAIL>' }
    authStore.token = 'fake-token'
    await wrapper.vm.$nextTick()

    const pushSpy = vi.spyOn(router, 'push')
    // Spy on the actual logout action if stubActions: false
    const logoutSpy = vi.spyOn(authStore, 'logout')

    const menu = wrapper.findComponent(NMenu)
    await menu.vm.$emit('select', 'logout')

    expect(logoutSpy).toHaveBeenCalledTimes(1)
    expect(pushSpy).toHaveBeenCalledWith('/login')
  })
})
