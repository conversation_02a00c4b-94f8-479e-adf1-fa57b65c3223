import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

export function useRtl() {
  const { locale } = useI18n();
  
  const isRtl = computed(() => {
    const rtlLocales = ['fa', 'ar', 'he']; // RTL languages
    return rtlLocales.includes(locale.value);
  });
  
  const direction = computed(() => {
    return isRtl.value ? 'rtl' : 'ltr';
  });
  
  return {
    isRtl,
    direction
  };
}