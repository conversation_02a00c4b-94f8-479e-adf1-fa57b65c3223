const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testBrowseEndpointLogic() {
  try {
    console.log('=== Testing Browse Endpoint Logic ===');
    
    // Find an offer that has completed transaction (the one we saw: cmb40jjag002dvly8m8r2gwku)
    const offerId = 'cmb40jjag002dvly8m8r2gwku';
    
    const offer = await prisma.offer.findUnique({
      where: { id: offerId },
      include: {
        user: { select: { username: true, reputationLevel: true } },        interests: {
          include: {
            interestedUser: { select: { username: true, reputationLevel: true } },
            chatSession: {
              include: {
                transaction: {
                  include: {
                    payerNegotiation: true
                  }
                }
              }
            }
          }
        },
        chatSessions: {
          include: {
            transaction: {
              include: {
                payerNegotiation: true
              }
            }
          }
        }
      }
    });
    
    if (!offer) {
      console.log('Offer not found');
      return;
    }
    
    console.log('\nOffer found:');
    console.log('ID:', offer.id);
    console.log('Type:', offer.type);
    console.log('Amount:', offer.amount);
    console.log('Creator:', offer.user?.username);
    
    console.log('\n--- INTERESTS ---');
    offer.interests.forEach((interest, i) => {
      console.log('Interest', i + 1, ':', interest.id);
      console.log('  Status:', interest.status);
      console.log('  User:', interest.interestedUser?.username);
      if (interest.chatSession) {
        console.log('  Chat Session:', interest.chatSession.id);
        if (interest.chatSession.transaction) {
          console.log('  Transaction Status:', interest.chatSession.transaction.status);
          if (interest.chatSession.transaction.payerNegotiation) {
            console.log('  Negotiation Status:', interest.chatSession.transaction.payerNegotiation.negotiationStatus);
          }
        }
      }
    });
    
    console.log('\n--- CHAT SESSIONS ---');
    offer.chatSessions.forEach((chatSession, i) => {
      console.log('Chat Session', i + 1, ':', chatSession.id);
      if (chatSession.transaction) {
        console.log('  Transaction Status:', chatSession.transaction.status);
        if (chatSession.transaction.payerNegotiation) {
          console.log('  Negotiation Status:', chatSession.transaction.payerNegotiation.negotiationStatus);
        }
      }
    });
      // Simulate the browse endpoint logic for a viewing user
    const viewingUserId = 'cmav458yl0000vl50932ofx2h'; // some user
    const currentUserInterest = offer.interests.find(i => i.interestedUserId === viewingUserId);
    
    console.log('\n--- BROWSE ENDPOINT LOGIC SIMULATION ---');
    console.log('Viewing User ID:', viewingUserId);
    console.log('Current User Interest:', currentUserInterest ? currentUserInterest.id : 'None');
    
    // Check for transaction status from current user interest OR any chat session
    let transactionStatus = currentUserInterest?.chatSession?.transaction?.status || null;
    let negotiationStatus = currentUserInterest?.chatSession?.transaction?.payerNegotiation?.negotiationStatus || null;
    
    console.log('Transaction Status from user interest:', transactionStatus);
    console.log('Negotiation Status from user interest:', negotiationStatus);
    
    // If no transaction from current user interest, check all chat sessions
    if (!transactionStatus && offer.chatSessions) {
      for (const chatSession of offer.chatSessions) {
        if (chatSession.transaction) {
          transactionStatus = chatSession.transaction.status;
          negotiationStatus = chatSession.transaction.payerNegotiation?.negotiationStatus || null;
          console.log('Transaction Status from chat session:', transactionStatus);
          console.log('Negotiation Status from chat session:', negotiationStatus);
          break;
        }
      }
    }
    
    console.log('\n--- FINAL RESULT ---');
    console.log('transactionStatus:', transactionStatus);
    console.log('negotiationStatus:', negotiationStatus);
    
    // Expected mapping
    let expectedDisplay = null;
    if (transactionStatus) {
      switch (transactionStatus) {
        case 'AWAITING_FIRST_PAYER_DESIGNATION':
          expectedDisplay = negotiationStatus === 'FINALIZED' ? 'In Progress' : 'Negotiating';
          break;
        case 'COMPLETED':
          expectedDisplay = 'Completed';
          break;
        default:
          expectedDisplay = 'In Progress';
      }
    }
    
    console.log('Expected display:', expectedDisplay);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testBrowseEndpointLogic();
