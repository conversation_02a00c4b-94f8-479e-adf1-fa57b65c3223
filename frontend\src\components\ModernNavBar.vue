<template>
  <n-layout-header bordered class="modern-navbar" :class="{ 'rtl-layout': languageStore.isRTL }">
    <div class="navbar-container">
      <!-- Logo Section -->
      <div class="navbar-brand" @click="$router.push('/')">
        <div class="logo-container">
          <img 
            :src="themeStore.isDark ? '/logo-dark.webp' : '/logo-light.webp'" 
            alt="MUNygo" 
            class="logo-image" 
          />
          <span class="brand-text">{{ t('app.title') }}</span>
        </div>
      </div>

      <!-- Desktop Navigation Menu -->
      <div class="navbar-menu desktop-only">
        <n-menu
          v-model:value="activeMenuKey"
          mode="horizontal"
          :options="menuOptions"
          :render-label="renderMenuLabel"
          :render-icon="renderMenuIcon"
          @update:value="handleMenuSelect"
          class="main-navigation"
        />
      </div>

      <!-- Desktop Action Buttons -->
      <div class="navbar-actions desktop-only">
        <!-- Notification Bell -->
        <div class="action-item">
          <NotificationBell />
        </div>        <!-- Connection Status -->
        <n-tooltip :placement="languageStore.isRTL ? 'bottom-end' : 'bottom-start'">
          <template #trigger>
            <n-button
              circle
              quaternary
              class="action-button"
              @click="handleConnectionClick"
              :loading="isReconnecting"
            >
              <template #icon>
                <n-icon 
                  size="18" 
                  :color="getConnectionColor()"
                  :class="{ 'pulse-animation': connectionStore.connectionQuality === 'poor' || connectionStore.connectionQuality === 'disconnected' }"
                >
                  <WifiOutline v-if="connectionStore.connectionQuality === 'excellent' || connectionStore.connectionQuality === 'good'" />
                  <AlertCircleOutline v-else />
                </n-icon>
              </template>
            </n-button>
          </template>
          {{ getConnectionTooltip() }}
        </n-tooltip>

        <!-- Language Selector -->
        <div class="action-item">
          <LanguageSelector />
        </div>

        <!-- Theme Toggle -->
        <n-tooltip :placement="languageStore.isRTL ? 'bottom-end' : 'bottom-start'">
          <template #trigger>
            <n-button
              circle
              quaternary
              class="action-button theme-toggle"
              @click="toggleTheme"
            >
              <template #icon>
                <n-icon size="18" class="theme-icon">
                  <SunnyOutline v-if="themeStore.isDark" />
                  <MoonOutline v-else />
                </n-icon>
              </template>
            </n-button>
          </template>
          {{ getThemeTooltip() }}
        </n-tooltip>

        <!-- User Menu Dropdown -->
        <n-dropdown
          :options="userMenuOptions"
          :render-label="renderUserMenuLabel"
          :render-icon="renderUserMenuIcon"
          @select="handleUserMenuSelect"
          placement="bottom-end"
          trigger="click"
        >          <n-avatar
            round
            size="medium"
            class="user-avatar"
            :src="authStore.user?.profile?.avatarUrl || undefined"
            :fallback-src="defaultAvatarUrl"
          >
            <template #placeholder>
              <n-icon size="20">
                <PersonOutline />
              </n-icon>
            </template>
          </n-avatar>
        </n-dropdown>
      </div>

      <!-- Mobile Actions -->
      <div class="navbar-mobile mobile-only">
        <!-- Mobile Notification -->
        <div class="mobile-action">
          <NotificationBell />
        </div>

        <!-- Mobile Menu Toggle -->
        <n-button
          circle
          quaternary
          class="mobile-menu-toggle"
          @click="toggleMobileMenu"
        >
          <template #icon>
            <n-icon size="20">
              <MenuOutline v-if="!showMobileMenu" />
              <CloseOutline v-else />
            </n-icon>
          </template>
        </n-button>
      </div>
    </div>

    <!-- Mobile Menu Drawer -->
    <n-drawer
      v-model:show="showMobileMenu"
      :width="280"
      :placement="languageStore.isRTL ? 'right' : 'left'"
      class="mobile-menu-drawer"
    >
      <n-drawer-content 
        :title="t('navigation.menu')"
        closable
        class="mobile-menu-content"
      >
        <!-- Mobile User Profile Section -->        <div class="mobile-user-section">
          <n-avatar
            round
            size="large"
            :src="authStore.user?.profile?.avatarUrl || undefined"
            :fallback-src="defaultAvatarUrl"
          >
            <template #placeholder>
              <n-icon size="24">
                <PersonOutline />
              </n-icon>
            </template>
          </n-avatar>
          <div class="user-info">
            <h3 class="user-name">{{ getUserDisplayName() }}</h3>
            <p class="user-email">{{ authStore.user?.email }}</p>
          </div>
        </div>

        <n-divider />

        <!-- Mobile Navigation Menu -->
        <div class="mobile-navigation">
          <n-menu
            v-model:value="activeMenuKey"
            :options="mobileMenuOptions"
            :render-label="renderMobileMenuLabel"
            :render-icon="renderMobileMenuIcon"
            @update:value="handleMobileMenuSelect"
            class="mobile-menu-list"
          />
        </div>

        <n-divider />

        <!-- Mobile Settings -->
        <div class="mobile-settings">
          <div class="setting-item" @click="toggleTheme">
            <n-icon size="18" class="setting-icon">
              <SunnyOutline v-if="themeStore.isDark" />
              <MoonOutline v-else />
            </n-icon>
            <span class="setting-label">{{ getThemeLabel() }}</span>
            <n-switch 
              :value="themeStore.isDark" 
              @update:value="toggleTheme"
              size="small"
            />
          </div>

          <div class="setting-item">
            <n-icon size="18" class="setting-icon">
              <LanguageOutline />
            </n-icon>
            <span class="setting-label">{{ t('navigation.language') }}</span>
            <LanguageSelector size="small" />
          </div>          <div class="setting-item connection-status">
            <n-icon size="18" class="setting-icon" :color="getConnectionColor()">
              <WifiOutline v-if="connectionStore.connectionQuality === 'excellent' || connectionStore.connectionQuality === 'good'" />
              <AlertCircleOutline v-else />
            </n-icon>
            <span class="setting-label">{{ getConnectionStatus() }}</span>
            <n-tag 
              :type="connectionStore.isConnected ? 'success' : 'error'"
              size="small"
              round
            >
              {{ connectionStore.isConnected ? t('connection.online') : t('connection.offline') }}
            </n-tag>
          </div>
        </div>

        <!-- Mobile Logout -->
        <div class="mobile-logout">
          <n-button
            block
            type="error"
            ghost
            @click="handleLogout"
            class="logout-button"
          >
            <template #icon>
              <n-icon>
                <LogOutOutline />
              </n-icon>
            </template>
            {{ t('auth.logout') }}
          </n-button>
        </div>
      </n-drawer-content>
    </n-drawer>
  </n-layout-header>
</template>

<script setup lang="ts">
import { ref, computed, h, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import {
  NLayoutHeader,
  NMenu,
  NButton,
  NIcon,
  NDrawer,
  NDrawerContent,
  NTooltip,
  NDropdown,
  NAvatar,
  NDivider,
  NSwitch,
  NTag,
  useMessage,
  type MenuOption,
  type DropdownOption
} from 'naive-ui'
import {  HomeOutline,
  SearchOutline,
  AddOutline,
  PersonOutline,
  DocumentTextOutline,
  LogOutOutline,
  MenuOutline,
  CloseOutline,
  WifiOutline,
  AlertCircleOutline,
  SunnyOutline,
  MoonOutline,
  LanguageOutline
} from '@vicons/ionicons5'

// Stores and composables
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'
import { useConnectionStore } from '@/stores/connection'
import { useLanguageStore } from '@/stores/language'
import centralizedSocketManager from '@/services/centralizedSocketManager'
import NotificationBell from './NotificationBell.vue'
import LanguageSelector from './LanguageSelector.vue'

// Setup
const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const message = useMessage()

// Stores
const authStore = useAuthStore()
const themeStore = useThemeStore()
const connectionStore = useConnectionStore()
const languageStore = useLanguageStore()

// Reactive state
const showMobileMenu = ref(false)
const isReconnecting = ref(false)
const activeMenuKey = ref<string>('')

// Watch route changes to update active menu key
watch(() => route.name, (newRouteName) => {
  activeMenuKey.value = newRouteName as string || 'home'
}, { immediate: true })

// Computed values
const defaultAvatarUrl = computed(() => 
  themeStore.isDark ? '/default-avatar-dark.svg' : '/default-avatar-light.svg'
)

// Menu options
const menuOptions = computed<MenuOption[]>(() => [
  {
    label: t('navigation.home'),
    key: 'home',
    icon: () => h(NIcon, null, { default: () => h(HomeOutline) })
  },
  {
    label: t('navigation.browse'),
    key: 'BrowseOffers',
    icon: () => h(NIcon, null, { default: () => h(SearchOutline) })
  },
  {
    label: t('navigation.myOffers'),
    key: 'MyOffers',
    icon: () => h(NIcon, null, { default: () => h(DocumentTextOutline) })
  },
  {
    label: t('navigation.createOffer'),
    key: 'CreateOffer',
    icon: () => h(NIcon, null, { default: () => h(AddOutline) })
  }
])

const mobileMenuOptions = computed<MenuOption[]>(() => [
  ...menuOptions.value,
  {
    label: t('navigation.profile'),
    key: 'profile',
    icon: () => h(NIcon, null, { default: () => h(PersonOutline) })
  }
])

const userMenuOptions = computed<DropdownOption[]>(() => [
  {
    label: t('navigation.profile'),
    key: 'profile',
    icon: () => h(NIcon, null, { default: () => h(PersonOutline) })
  },
  {
    type: 'divider',
    key: 'divider2'
  },
  {
    label: t('auth.logout'),
    key: 'logout',
    icon: () => h(NIcon, null, { default: () => h(LogOutOutline) })
  }
])

// Render functions
const renderMenuLabel = (option: MenuOption) => {
  return option.label as string
}

const renderMenuIcon = (option: MenuOption) => {
  return option.icon?.()
}

const renderMobileMenuLabel = (option: MenuOption) => {
  return h('span', { class: 'mobile-menu-label' }, option.label as string)
}

const renderMobileMenuIcon = (option: MenuOption) => {
  return option.icon?.()
}

const renderUserMenuLabel = (option: DropdownOption) => {
  return option.label as string
}

const renderUserMenuIcon = (option: DropdownOption) => {
  return option.icon?.()
}

// Helper functions
const getUserDisplayName = () => {
  const user = authStore.user
  if (user?.username) {
    return user.username
  }
  return user?.email || t('auth.guest')
}

const getConnectionColor = () => {
  const quality = connectionStore.connectionQuality
  if (quality === 'excellent' || quality === 'good') {
    return themeStore.isDark ? '#18a058' : '#36ad6a'
  }
  return themeStore.isDark ? '#d03050' : '#e88080'
}

const getConnectionTooltip = () => {
  const quality = connectionStore.connectionQuality
  switch (quality) {
    case 'excellent':
      return t('connection.online') + ' - ' + t('connection.excellent')
    case 'good':
      return t('connection.online') + ' - ' + t('connection.good')
    case 'poor':
      return t('connection.reconnecting')
    case 'disconnected':
      return t('connection.offline')
    default:
      return t('connection.unknown')
  }
}

const getConnectionStatus = () => {
  return connectionStore.isConnected 
    ? t('connection.connected') 
    : t('connection.disconnected')
}

const getThemeTooltip = () => {
  return themeStore.isDark 
    ? t('theme.switchToLight') 
    : t('theme.switchToDark')
}

const getThemeLabel = () => {
  return themeStore.isDark 
    ? t('theme.darkMode') 
    : t('theme.lightMode')
}

// Event handlers
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const toggleTheme = () => {
  themeStore.toggleTheme()
}

const handleMenuSelect = (key: string) => {
  if (key && key !== route.name) {
    router.push({ name: key })
  }
}

const handleMobileMenuSelect = (key: string) => {
  showMobileMenu.value = false
  if (key && key !== route.name) {
    router.push({ name: key })
  }
}

const handleUserMenuSelect = (key: string) => {
  switch (key) {
    case 'profile':
      router.push({ name: 'profile' })
      break
    case 'chat':
      router.push({ name: 'chat' })
      break
    case 'settings':
      router.push({ name: 'settings' })
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleConnectionClick = async () => {
  if (connectionStore.connectionQuality === 'disconnected' || connectionStore.connectionQuality === 'poor') {
    isReconnecting.value = true
    try {
      // Attempt to reconnect via centralized socket manager
      await centralizedSocketManager.forceReconnect()
      message.success(t('connection.reconnected'))
    } catch (error) {
      message.error(t('connection.reconnectFailed'))
    } finally {
      isReconnecting.value = false
    }
  }
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    message.success(t('auth.loggedOut'))
    router.push({ name: 'landing' })
  } catch (error) {
    message.error(t('auth.logoutError'))
  }
}

// Watch for route changes to close mobile menu
watch(route, () => {
  showMobileMenu.value = false
})
</script>

<style scoped>
.modern-navbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--n-color);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  height: 64px;
}

.rtl-layout .navbar-container {
  direction: rtl;
}

/* Logo and Brand */
.navbar-brand {
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.navbar-brand:hover {
  opacity: 0.8;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-image {
  height: 40px;
  width: auto;
  object-fit: contain;
}

.brand-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--n-text-color);
  background: linear-gradient(135deg, var(--n-primary-color), var(--n-primary-color-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Desktop Navigation */
.navbar-menu {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 2rem;
}

.main-navigation {
  background: transparent !important;
}

.main-navigation :deep(.n-menu-item) {
  padding: 0 1rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.main-navigation :deep(.n-menu-item:hover) {
  background: var(--n-action-color-hover);
}

.main-navigation :deep(.n-menu-item.n-menu-item--selected) {
  background: var(--n-primary-color-suppl);
  color: var(--n-primary-color);
  font-weight: 600;
}

/* Desktop Actions */
.navbar-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-item,
.action-button {
  transition: all 0.2s ease;
}

.action-button:hover {
  background: var(--n-action-color-hover);
  transform: translateY(-1px);
}

.theme-toggle {
  overflow: hidden;
}

.theme-icon {
  transition: transform 0.3s ease;
}

.theme-toggle:hover .theme-icon {
  transform: rotate(180deg);
}

.user-avatar {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.user-avatar:hover {
  border-color: var(--n-primary-color);
  transform: scale(1.05);
}

/* Mobile Actions */
.navbar-mobile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mobile-menu-toggle {
  transition: all 0.2s ease;
}

.mobile-menu-toggle:hover {
  background: var(--n-action-color-hover);
}

/* Mobile Menu Drawer */
.mobile-menu-drawer :deep(.n-drawer-content) {
  padding: 0;
}

.mobile-menu-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mobile-user-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--n-card-color);
  border-radius: 0 0 12px 12px;
  margin-bottom: 1rem;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--n-text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  margin: 0.25rem 0 0 0;
  font-size: 0.875rem;
  color: var(--n-text-color-2);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mobile-navigation {
  flex: 1;
  padding: 0 1rem;
}

.mobile-menu-list {
  background: transparent !important;
}

.mobile-menu-list :deep(.n-menu-item) {
  padding: 0.75rem 1rem;
  margin: 0.25rem 0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.mobile-menu-list :deep(.n-menu-item:hover) {
  background: var(--n-action-color-hover);
}

.mobile-menu-list :deep(.n-menu-item.n-menu-item--selected) {
  background: var(--n-primary-color-suppl);
  color: var(--n-primary-color);
  font-weight: 600;
}

.mobile-menu-label {
  font-size: 1rem;
  font-weight: 500;
}

/* Mobile Settings */
.mobile-settings {
  padding: 1rem;
  background: var(--n-card-color);
  border-radius: 12px;
  margin: 1rem;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.setting-item:not(:last-child) {
  border-bottom: 1px solid var(--n-divider-color);
}

.setting-item:hover {
  color: var(--n-primary-color);
}

.setting-icon {
  flex-shrink: 0;
}

.setting-label {
  flex: 1;
  font-size: 0.95rem;
  font-weight: 500;
}

.connection-status {
  cursor: default;
}

.connection-status:hover {
  color: inherit;
}

/* Mobile Logout */
.mobile-logout {
  padding: 1rem;
  margin-top: auto;
}

.logout-button {
  font-weight: 600;
}

/* Responsive Design */
.desktop-only {
  display: flex;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .mobile-only {
    display: flex;
  }

  .navbar-container {
    padding: 0 0.75rem;
  }

  .logo-container {
    gap: 0.5rem;
  }

  .logo-image {
    height: 32px;
  }

  .brand-text {
    font-size: 1.25rem;
  }
}

/* Animations */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* RTL Support */
.rtl-layout .navbar-actions {
  flex-direction: row-reverse;
}

.rtl-layout .mobile-user-section {
  text-align: right;
}

.rtl-layout .setting-item {
  flex-direction: row-reverse;
  text-align: right;
}

.rtl-layout .setting-label {
  text-align: right;
}
</style>
