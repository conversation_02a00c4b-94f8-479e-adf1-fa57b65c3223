-- Migration to enhance payment information system
-- This will be converted to a proper Prisma migration

-- Add currency support to existing PaymentReceivingInfo
ALTER TABLE "PaymentReceivingInfo" ADD COLUMN "currency" TEXT NOT NULL DEFAULT 'IRR';
ALTER TABLE "PaymentReceivingInfo" ADD COLUMN "paymentMethodType" TEXT NOT NULL DEFAULT 'BANK_TRANSFER';
ALTER TABLE "PaymentReceivingInfo" ADD COLUMN "swiftCode" TEXT;
ALTER TABLE "PaymentReceivingInfo" ADD COLUMN "iban" TEXT;
ALTER TABLE "PaymentReceivingInfo" ADD COLUMN "routingNumber" TEXT;
ALTER TABLE "PaymentReceivingInfo" ADD COLUMN "sortCode" TEXT;
ALTER TABLE "PaymentReceivingInfo" ADD COLUMN "bsb" TEXT; -- For Australian banks
ALTER TABLE "PaymentReceivingInfo" ADD COLUMN "isActive" BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "PaymentReceivingInfo" ADD COLUMN "notes" TEXT;

-- Create unique constraint for one default per user per currency
CREATE UNIQUE INDEX "PaymentReceivingInfo_userId_currency_default_idx" 
ON "PaymentReceivingInfo" ("userId", "currency") 
WHERE "isDefaultForUser" = true;

-- Create index for active payment methods
CREATE INDEX "PaymentReceivingInfo_userId_currency_active_idx" 
ON "PaymentReceivingInfo" ("userId", "currency", "isActive");
