import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Calculate reputation score based on user data and account age
function calculateReputationScore(user: any): number {
  let score = 0;
  
  // Add points for email verification (base points for having an account)
  if (user.emailVerified) {
    score += 10;
  }
  
  // Add points for phone verification
  if (user.phoneVerified) {
    score += 15;
  }
  
  // Account age bonus (1 point per day, up to 30)
  const accountAgeInDays = Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24));
  score += Math.min(accountAgeInDays, 30);
  
  return score;
}

// Map score to level (1-5)
function calculateReputationLevel(score: number): number {
  if (score < 10) return 1; // Newcomer
  if (score < 25) return 2; // Verified
  if (score < 40) return 3; // Reliable
  if (score < 60) return 4; // Trusted
  return 5; // Elite
}

async function main() {
  try {
    console.log('Updating user profiles with username and reputation data...');
    
    // Fetch all users
    const allUsers = await prisma.user.findMany();
    console.log(`Total users found: ${allUsers.length}`);
    
    for (const user of allUsers) {
      // Extract username from email
      const username = user.email.split('@')[0];
      
      // Calculate reputation metrics
      const reputationScore = calculateReputationScore(user);
      const reputationLevel = calculateReputationLevel(reputationScore);
      
      console.log(`Updating user ${user.id} (${user.email}):`);
      console.log(`  Username: ${username}`);
      console.log(`  Reputation Score: ${reputationScore}`);
      console.log(`  Reputation Level: ${reputationLevel}`);
      
      // Update the user record
      await prisma.user.update({
        where: { id: user.id },
        data: {
          username,
          reputationScore,
          reputationLevel
        }
      });
    }
    
    console.log('\nAll users updated successfully.');
    
  } catch (error) {
    console.error('Error updating users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('Profile update completed.'))
  .catch(e => {
    console.error('Script failed:', e);
    process.exit(1);
  });
