<template>
  <div class="interest-carousel-container">
    <!-- Carousel Wrapper -->
    <div class="carousel-wrapper">
      <!-- Slides Container -->
      <div 
        class="carousel-slides" 
        :style="{ transform: carouselTransform }"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @mousedown="handleMouseStart"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseEnd"
        @mouseleave="handleMouseEnd"
      >
        <div 
          v-for="(interest, index) in interests" 
          :key="interest.id"
          class="carousel-slide"
          :class="{ 'is-active': index === currentIndex }"
        >
          <!-- Interest Mini Card -->
          <div class="interest-mini-card">
            <!-- Top Section with User Header and Navigation -->
            <div class="card-top-section">
              <!-- User Header -->
              <div class="user-header">
                <div class="user-info">
                  <h6 class="username">{{ interest.username || `User #${interest.id ? interest.id.slice(-4) : 'N/A'}` }}</h6>
                  <n-tag 
                    size="tiny" 
                    :type="(interest.reputationLevel || 1) >= 4 ? 'success' : 'default'" 
                    class="reputation-tag"
                  >
                    L{{ interest.reputationLevel || '?' }}
                  </n-tag>
                </div>
                
                <div class="status-badge">
                  <n-tag 
                    v-if="getStatusInfo(interest)"
                    :type="getStatusInfo(interest)?.type || 'default'"
                    size="small"
                    :class="{ 
                      'animated-status': getStatusInfo(interest)?.text === 'Negotiating' || getStatusInfo(interest)?.text === 'In Progress' 
                    }"
                  >
                    {{ getStatusInfo(interest)?.text }}
                  </n-tag>
                  <n-tag v-else :type="getBasicStatusType(interest.status)" size="small">
                    {{ interest.status || 'UNKNOWN' }}
                  </n-tag>
                </div>
              </div>

              <!-- Navigation Controls (only show if multiple interests) -->
              <div v-if="interests.length > 1" class="carousel-controls-inline">
                <!-- Previous Button -->
                <n-button 
                  v-if="currentIndex > 0"
                  size="tiny"
                  quaternary
                  circle
                  @click="previousInterest"
                  class="nav-button-inline prev-button"
                >
                  <template #icon>
                    <n-icon><ArrowLeftOutlined /></n-icon>
                  </template>
                </n-button>
                
                <!-- Navigation Dots -->
                <div class="nav-dots-inline">
                  <button
                    v-for="(_, dotIndex) in interests"
                    :key="dotIndex"
                    class="nav-dot-inline"
                    :class="{ 'is-active': dotIndex === currentIndex }"
                    @click="goToIndex(dotIndex)"
                  />
                </div>
                
                <!-- Next Button -->
                <n-button 
                  v-if="currentIndex < interests.length - 1"
                  size="tiny"
                  quaternary
                  circle
                  @click="nextInterest"
                  class="nav-button-inline next-button"
                >
                  <template #icon>
                    <n-icon><ArrowRightOutlined /></n-icon>
                  </template>
                </n-button>
              </div>
            </div>

            <!-- Interest Content -->
            <div class="interest-body">
              <!-- Declined Reason -->
              <div v-if="interest.status === 'DECLINED' && interest.reasonCode" class="decline-info">
                <div class="info-label">{{ t('offers.declineReason') }}:</div>
                <div class="decline-reason">{{ interest.reasonCode }}</div>
              </div>
              
              <!-- Created Date -->
              <div class="created-info">
                <div class="info-label">{{ t('offers.interestedAt') }}:</div>
                <div class="created-date">{{ formatDate(interest.createdAt) }}</div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div v-if="interest.status === 'PENDING'" class="action-buttons">
              <n-button 
                type="success"
                size="small"
                :loading="processingInterests.has(interest.id)"
                :disabled="processingInterests.has(interest.id)"
                @click="emit('acceptInterest', interest.id)"
                class="accept-button"
              >
                <template #icon>
                  <n-icon><CheckOutlined /></n-icon>
                </template>
                {{ t('offers.accept') }}
              </n-button>
              
              <n-button 
                type="error"
                size="small"
                :loading="processingInterests.has(interest.id)"
                :disabled="processingInterests.has(interest.id)"
                @click="emit('declineInterest', interest.id)"
                class="decline-button"
              >
                <template #icon>
                  <n-icon><CloseOutlined /></n-icon>
                </template>
                {{ t('offers.decline') }}
              </n-button>
            </div>

            <!-- Chat Button for Accepted -->
            <div v-else-if="interest.status === 'ACCEPTED' && interest.chatSessionId" class="chat-section">
              <n-button 
                type="primary"
                size="small"
                @click="emit('goToChat', interest.chatSessionId)"
                class="chat-button"
              >
                <template #icon>
                  <n-icon><MessageOutlined /></n-icon>
                </template>
                {{ t('common.openChat') }}
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useTranslation } from '@/composables/useTranslation';
import { useLanguageStore } from '@/stores/language';
import type { InterestRequestFrontend } from '@/types/offer';
import { NButton, NTag, NIcon } from 'naive-ui';
import { 
  ArrowLeftOutlined, 
  ArrowRightOutlined,
  MessageOutlined,
  CheckOutlined,
  CloseOutlined
} from '@vicons/antd';

// Props
interface Props {
  interests: InterestRequestFrontend[];
  processingInterests: Set<string>;
}

const props = defineProps<Props>();

// Emits
interface Emits {
  acceptInterest: [interestId: string];
  declineInterest: [interestId: string];
  goToChat: [chatSessionId: string];
}

const emit = defineEmits<Emits>();

// Composables
const { t } = useTranslation();
const languageStore = useLanguageStore();

// State
const currentIndex = ref(0);

// Touch/Mouse handling
const startX = ref(0);
const currentX = ref(0);
const isDragging = ref(false);
const threshold = 50; // Minimum swipe distance in pixels

// RTL Detection
const isRTL = computed(() => languageStore.currentLanguage === 'fa');

// Carousel Transform with RTL support
const carouselTransform = computed(() => {
  if (isRTL.value) {
    // In RTL, positive translateX moves left, negative moves right
    return `translateX(${currentIndex.value * 100}%)`;
  } else {
    // In LTR, negative translateX moves left
    return `translateX(-${currentIndex.value * 100}%)`;
  }
});

// Methods
function goToIndex(index: number) {
  if (index >= 0 && index < props.interests.length) {
    currentIndex.value = index;
  }
}

function nextInterest() {
  if (currentIndex.value < props.interests.length - 1) {
    currentIndex.value++;
  }
}

function previousInterest() {
  if (currentIndex.value > 0) {
    currentIndex.value--;
  }
}

// Touch handling with RTL support
function handleTouchStart(e: TouchEvent) {
  if (props.interests.length <= 1) return;
  
  startX.value = e.touches[0].clientX;
  currentX.value = startX.value;
  isDragging.value = true;
}

function handleTouchMove(e: TouchEvent) {
  if (!isDragging.value || props.interests.length <= 1) return;
  
  currentX.value = e.touches[0].clientX;
  e.preventDefault();
}

function processSwipe() {
  if (!isDragging.value || props.interests.length <= 1) return;
  
  const diffX = currentX.value - startX.value;
  const absDiff = Math.abs(diffX);
  
  if (absDiff > threshold) {
    if (isRTL.value) {
      // In RTL, reverse the swipe logic
      if (diffX > 0) {
        // Swipe right = previous in RTL
        previousInterest();
      } else {
        // Swipe left = next in RTL
        nextInterest();
      }
    } else {
      // In LTR, normal swipe logic
      if (diffX > 0) {
        // Swipe right = previous
        previousInterest();
      } else {
        // Swipe left = next
        nextInterest();
      }
    }
  }
  
  isDragging.value = false;
}

function handleTouchEnd() {
  processSwipe();
}

// Mouse handling with RTL support
function handleMouseStart(e: MouseEvent) {
  if (props.interests.length <= 1) return;
  
  startX.value = e.clientX;
  currentX.value = startX.value;
  isDragging.value = true;
  e.preventDefault();
}

function handleMouseMove(e: MouseEvent) {
  if (!isDragging.value || props.interests.length <= 1) return;
  
  currentX.value = e.clientX;
  e.preventDefault();
}

function handleMouseEnd() {
  processSwipe();
}

// Utility functions
function getStatusInfo(interest: InterestRequestFrontend) {
  try {
    // Simple fallback instead of complex helper
    switch (interest.status) {
      case 'PENDING':
        return { text: 'Pending', type: 'warning' as const }
      case 'ACCEPTED':
        if (interest.transactionStatus) {
          switch (interest.transactionStatus) {
            case 'PENDING_AGREEMENT':
            case 'AWAITING_FIRST_PAYER_DESIGNATION':
              return { text: 'Setting Up', type: 'info' as const }
            case 'AWAITING_FIRST_PAYER_PAYMENT':
            case 'AWAITING_SECOND_PAYER_PAYMENT':
              return { text: 'In Progress', type: 'warning' as const }
            case 'COMPLETED':
              return { text: 'Complete', type: 'success' as const }
            default:
              return { text: 'Active', type: 'success' as const }
          }
        }
        return { text: 'Active', type: 'success' as const }
      case 'DECLINED':
        return { text: 'Declined', type: 'error' as const }
      default:
        return { text: interest.status || 'Unknown', type: 'default' as const }
    }
  } catch (error) {
    console.error('Error in getStatusInfo:', error, 'Interest:', interest)
    return { text: 'Error', type: 'default' as const }
  }
}

function getBasicStatusType(status: string): 'success' | 'warning' | 'error' | 'info' | 'default' {
  switch (status) {
    case 'ACCEPTED': return 'success';
    case 'PENDING': return 'warning';
    case 'DECLINED': return 'error';
    default: return 'default';
  }
}

function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  } catch {
    return 'N/A';
  }
}

// Reset index when interests change
watch(() => props.interests.length, () => {
  if (currentIndex.value >= props.interests.length) {
    currentIndex.value = 0;
  }
});
</script>

<style scoped>
/* Mobile-First Interest Carousel */
.interest-carousel-container {
  width: 100%;
  height: 100%;
}

.carousel-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.carousel-slides {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.carousel-slide {
  min-width: 100%;
  width: 100%;
  flex-shrink: 0;
  box-sizing: border-box;
  padding: 0;
}

/* Interest Mini Card */
.interest-mini-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 0.75rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

[data-theme="dark"] .interest-mini-card {
  background: rgba(26, 27, 46, 0.9);
  border-color: rgba(255, 255, 255, 0.15);
}

/* Top Section with User Header and Navigation */
.card-top-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

/* User Header */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.username {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.reputation-tag {
  flex-shrink: 0;
}

.status-badge {
  flex-shrink: 0;
}

/* Interest Body */
.interest-body {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  flex: 1;
}

.decline-info,
.created-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-label {
  font-size: 0.75rem;
  color: var(--text-color-3);
  font-weight: 500;
}

.decline-reason,
.created-date {
  font-size: 0.8rem;
  color: var(--text-color-2);
}

.decline-reason {
  background: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
}

.accept-button,
.decline-button {
  flex: 1;
  min-height: 36px;
}

.chat-section {
  margin-top: auto;
}

.chat-button {
  width: 100%;
  min-height: 36px;
}

/* Inline Navigation Controls */
.carousel-controls-inline {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex-shrink: 0;
}

.nav-button-inline {
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-dots-inline {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  margin: 0 0.125rem;
}

.nav-dot-inline {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  border: none;
  background: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.nav-dot-inline.is-active {
  background: var(--primary-color);
  transform: scale(1.5);
}

[data-theme="dark"] .nav-dot-inline {
  background: rgba(255, 255, 255, 0.3);
}

/* Animated Status */
.animated-status {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Responsive Enhancements */
@media (min-width: 768px) {
  .interest-mini-card {
    padding: 1.25rem;
  }
  
  .username {
    font-size: 1rem;
    max-width: 160px;
  }
  
  .action-buttons {
    flex-direction: row;
    gap: 0.75rem;
  }
  
  .carousel-controls {
    bottom: 1.5rem;
    padding: 0.75rem 1.25rem;
  }
}

@media (min-width: 1024px) {
  .interest-mini-card {
    padding: 1.5rem;
  }
  
  .username {
    max-width: 200px;
  }
  
  .nav-button {
    width: 36px;
    height: 36px;
  }
  
  .nav-dot {
    width: 10px;
    height: 10px;
  }
}
</style>
