const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugMatchDetails() {
  console.log('=== DEBUGGING MATCH DETAILS ===\n');
  
  try {
    // Get the specific match
    const match = await prisma.offerMatch.findFirst({
      where: { matchId: 'MATCH_20250609_ZPGPNB' },
      include: {
        offerA: { include: { user: { select: { username: true, id: true } } } },
        offerB: { include: { user: { select: { username: true, id: true } } } },
        userA: { select: { username: true, id: true } },
        userB: { select: { username: true, id: true } }
      }
    });
    
    if (match) {
      console.log('Match Details:');
      console.log(`- Match ID: ${match.matchId}`);
      console.log(`- Status: ${match.status}`);
      console.log(`- Created: ${match.createdAt}`);
      console.log(`- Expires: ${match.expiresAt}`);
      console.log(`- User A: ${match.userA.username} (${match.userA.id})`);
      console.log(`- User B: ${match.userB.username} (${match.userB.id})`);
      console.log(`- User A Response: ${match.userAResponse} at ${match.userARespondedAt}`);
      console.log(`- User B Response: ${match.userBResponse} at ${match.userBRespondedAt}`);
      console.log(`- Offer A: ${match.offerA.type} ${match.offerA.amount}@${match.offerA.baseRate} by ${match.offerA.user.username}`);
      console.log(`- Offer B: ${match.offerB.type} ${match.offerB.amount}@${match.offerB.baseRate} by ${match.offerB.user.username}`);
      
      // Check if it's expired
      const now = new Date();
      const isExpired = match.expiresAt < now;
      console.log(`- Is Expired: ${isExpired}`);
      
      if (isExpired) {
        console.log('⚠️ This match has expired and should be cleaned up!');
      }
      
    } else {
      console.log('❌ Match not found!');
    }
    
    // Also check all pending matches
    console.log('\n=== ALL PENDING MATCHES ===');
    const pendingMatches = await prisma.offerMatch.findMany({
      where: { status: 'PENDING' },
      include: {
        userA: { select: { username: true, id: true } },
        userB: { select: { username: true, id: true } }
      }
    });
    
    pendingMatches.forEach(match => {
      const now = new Date();
      const isExpired = match.expiresAt < now;
      console.log(`- ${match.matchId}: ${match.userA.username} <-> ${match.userB.username} [${isExpired ? 'EXPIRED' : 'ACTIVE'}]`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugMatchDetails();
