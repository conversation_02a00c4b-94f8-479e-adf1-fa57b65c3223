// src/services/eventBus.ts
type Callback = (...args: any[]) => void;

class EventBusService {
  private events: { [key: string]: Callback[] } = {};

  // Subscribe to an event
  on(event: string, callback: Callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);    // Return unsubscribe function
    return () => {
      if (this.events[event]) { // Check if event array still exists
        this.events[event] = this.events[event].filter(cb => cb !== callback);
        if (this.events[event].length === 0) {
          delete this.events[event];
        }
      }
    };
  }

  // Emit an event
  emit(event: string, ...args: any[]) {
    const callbacks = this.events[event];
    if (callbacks) {
      callbacks.forEach(callback => callback(...args));
    }
  }

  // Clear all events of a certain type
  off(event: string) {
    delete this.events[event];
  }
}

export const EventBus = new EventBusService();

// Event names
export const EventTypes = {
  OFFER_STATUS_CHANGED: 'offer:statusChanged',
  OFFER_UPDATED: 'offer:updated',
  OFFER_CREATED: 'offer:created',
  OFFER_INTEREST_SHOWN: 'offer:interestShown',
  OFFER_ERROR: 'offer:error',
  INTEREST_STATUS_CHANGED_BY_OTHERS: 'interest:statusChangedByOthers', // New event type
} as const;

export type EventPayloads = {
  [EventTypes.OFFER_ERROR]: { message: string; },
  [EventTypes.OFFER_STATUS_CHANGED]: { status: string; offerId: string; },
  [EventTypes.OFFER_UPDATED]: { offerId: string; },
  [EventTypes.OFFER_CREATED]: undefined,
  [EventTypes.OFFER_INTEREST_SHOWN]: undefined,
  [EventTypes.INTEREST_STATUS_CHANGED_BY_OTHERS]: { // Payload for the new event type
    offerId: string;
    interestId: string;
    newStatus: 'ACCEPTED' | 'DECLINED' | 'PENDING'; // More specific status
    reasonCode?: string;
    chatSessionId?: string; // ADDED: Optional chatSessionId
  },
};
