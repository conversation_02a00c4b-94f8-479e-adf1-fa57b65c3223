// filepath: c:\Code\MUNygo\frontend\src\utils\errorHandler.ts
import type { MessageApiInjection } from 'naive-ui/es/message/src/MessageProvider';
import type { ApiErrorResponse } from '@/types/api'; // Assuming you have this type

// Define a type for errors that are expected to have a 'response' property
interface ErrorWithResponse {
  response?: {
    data?: ApiErrorResponse | Record<string, unknown>; // Allow ApiErrorResponse or a generic object
    statusText?: string;
    // Add other properties from a typical error response if needed, e.g., status: number
  };
  // Include other common error properties if necessary
  message?: string;
}

// Type guard to check if the error has a response property
function isErrorWithResponse(error: unknown): error is ErrorWithResponse {
  return (
    typeof error === 'object' &&
    error !== null &&
    'response' in error &&
    typeof (error as ErrorWithResponse).response === 'object' &&
    (error as ErrorWithResponse).response !== null
  );
}

/**
 * Centralized error handler for API calls or other async operations.
 * Logs the error and displays a user-friendly message using Naive UI.
 *
 * @param error The error object caught.
 * @param message Optional Naive UI message instance.
 * @param defaultMessage A fallback message if the error doesn't provide one.
 */
export function handleError(
  error: unknown,
  message?: MessageApiInjection,
  defaultMessage: string = 'An unexpected error occurred. Please try again.'
): string { // MODIFIED: Changed void to string
  console.error('Error caught by handleError:', error);

  let displayMessage = defaultMessage;

  if (isErrorWithResponse(error) && error.response) {
    const response = error.response;
    if (response.data && typeof response.data === 'object') {
      // Check if data conforms to ApiErrorResponse structure before accessing specific fields
      const data = response.data as Partial<ApiErrorResponse>; // Use Partial to safely access optional props
      displayMessage = data.message || data.error || displayMessage;
    } else if (response.statusText) {
      displayMessage = response.statusText; // Fallback to status text
    }
  } else if (error instanceof Error) {
    displayMessage = error.message; // Use standard Error message
  }

  // Display the message using Naive UI if available
  if (message) {
    message.error(displayMessage);
  } else {
    // Fallback if message instance isn't provided (e.g., log differently)
    console.error('Error message to user (no UI instance):', displayMessage);
  }
  return displayMessage; // ADDED: Return the processed message
}