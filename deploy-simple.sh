#!/bin/bash

# SIMPLE PRODUCTION DEPLOYMENT
# =============================
# Deploy new debug report features using Docker Compose

echo "🚀 Simple Production Deployment"
echo "==============================="

# Exit on any error
set -e

echo "🔍 Checking current containers..."
docker-compose ps

echo ""
echo "🧹 Step 1: Building new images (with debug features)..."

# Force rebuild without cache to ensure latest code
docker-compose build --no-cache

echo ""
echo "🔄 Step 2: Updating containers..."

# Stop and recreate containers with new images
docker-compose up -d --force-recreate

echo ""
echo "⏳ Step 3: Waiting for containers to be ready..."
sleep 10

echo ""
echo "🏥 Step 4: Health checks..."

# Check if containers are running
echo "📊 Container status:"
docker-compose ps

# Check backend health
echo ""
echo "🔍 Backend health check..."
if docker-compose exec backend curl -f http://localhost:3000/health >/dev/null 2>&1; then
    echo "✅ Backend is healthy"
else
    echo "⚠️  Backend health check failed (might need more time)"
fi

# Check if frontend is accessible
echo ""
echo "🌐 Frontend container status:"
if docker-compose ps | grep frontend | grep -q "Up"; then
    echo "✅ Frontend container is running"
else
    echo "❌ Frontend container is not running"
fi

echo ""
echo "📝 Checking logs for any errors..."
echo "Backend logs (last 10 lines):"
docker-compose logs --tail=10 backend

echo ""
echo "Frontend logs (last 10 lines):"
docker-compose logs --tail=10 frontend

echo ""
echo "🎉 Deployment completed!"
echo ""
echo "✅ Next steps:"
echo "1. Test your application at your domain"
echo "2. Log in as an admin user (using emails from VITE_ADMIN_EMAILS)"
echo "3. Look for the 'Debug Report' button"
echo "4. Test creating a debug report"
echo ""
echo "🔧 If debug button is not visible:"
echo "• Clear browser cache and hard refresh"
echo "• Verify you're logged in with an admin email"
echo "• Check browser console for errors"
echo ""
echo "📋 Environment variables to verify:"
echo "• VITE_ENABLE_DEBUG_REPORT=true"
echo "• VITE_ADMIN_EMAILS contains your admin emails"
echo "• GEMINI_API_KEY is set for AI analysis"
