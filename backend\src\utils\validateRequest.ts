import { Context } from 'hono';
import { z } from 'zod';

export async function validateRequest<T>(
  c: Context,
  schema: z.ZodSchema<T>
): Promise<{ success: true; data: T } | { success: false; response: Response }> {
  try {
    const body = await c.req.json();
    const data = schema.parse(body);
    return { success: true, data };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        response: c.json(
          {
            error: 'Validation failed',
            details: error.errors,
          },
          400
        ),
      };
    }
    return {
      success: false,
      response: c.json({ error: 'Invalid request body' }, 400),
    };
  }
}
