import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function runScript(scriptName: string, description: string) {
  console.log(`\n🚀 ${description}...`);
  console.log(`Running: npm run ${scriptName}`);
  
  try {
    const { stdout, stderr } = await execAsync(`npm run ${scriptName}`);
    console.log(stdout);
    if (stderr) {
      console.warn('Warnings:', stderr);
    }
    console.log(`✅ ${description} completed successfully`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error);
    throw error;
  }
}

async function main() {
  console.log('🌱 Starting complete database seeding process...');
  console.log('This will create test users, payment methods, and offers.');

  try {
    // Step 1: Seed test users
    await runScript('seed:test-users', 'Seeding test users');

    // Step 2: Seed payment methods for test users
    await runScript('seed:payment-methods', 'Seeding payment methods');

    // Step 3: Seed test offers
    await runScript('seed:test-offers', 'Seeding test offers');

    console.log('\n🎉 Complete seeding process finished successfully!');
    console.log('\nTest users created:');
    console.log('  📧 <EMAIL> (password: 11111111) - Elite reputation');
    console.log('  📧 <EMAIL> (password: 11111111) - New user');
    console.log('  📧 <EMAIL> (password: 11111111) - Reliable user');
    console.log('\nEach user has payment methods for USD, EUR, TRY, and IRR');
    console.log('Multiple test offers have been created for currency exchanges');
    
  } catch (error) {
    console.error('\n💥 Seeding process failed:', error);
    console.log('\nYou may need to check:');
    console.log('  - Database connection');
    console.log('  - Prisma schema is up to date');
    console.log('  - Run migrations: npm run prisma:migrate');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { main };
