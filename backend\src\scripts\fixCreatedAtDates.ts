import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking all users for missing date values...');
    
    // Fetch all users
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        createdAt: true,
      }
    });
    
    console.log(`Total users found: ${allUsers.length}`);
    
    // Filter users with missing or invalid createdAt
    const usersToUpdate = [];
    for (const user of allUsers) {
      if (!user.createdAt) {
        usersToUpdate.push(user);
      }
    }
    
    console.log(`Found ${usersToUpdate.length} users with missing or invalid createdAt`);
    
    // Set a default creation date (May 1, 2025 - a week before current date)
    const defaultDate = new Date('2025-05-01T00:00:00Z');
    
    // Update all users with missing createdAt
    if (usersToUpdate.length > 0) {
      for (const user of usersToUpdate) {
        console.log(`Updating user ${user.id} (${user.email})...`);
        
        await prisma.user.update({
          where: { id: user.id },
          data: {
            createdAt: defaultDate,
            updatedAt: defaultDate
          }
        });
      }
      
      console.log('All users updated successfully');
    } else {
      console.log('No users need updating.');
    }
    
  } catch (error) {
    console.error('Error updating users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('Migration completed successfully.'))
  .catch(e => {
    console.error('Migration failed:', e);
    process.exit(1);
  });
