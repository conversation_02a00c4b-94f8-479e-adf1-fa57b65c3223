import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import centralizedSocketManagerInstance from '../centralizedSocketManager';

// Mock socket.io-client
const mockSocket = {
  connected: false,
  disconnected: true,
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  connect: vi.fn(),
  disconnect: vi.fn(),
  removeAllListeners: vi.fn()
};

vi.mock('socket.io-client', () => ({
  io: vi.fn(() => mockSocket)
}));

// Mock the auth store
const mockAuthStore = {
  token: 'mock-jwt-token',
  isAuthenticated: true,
  logout: vi.fn()
};

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => mockAuthStore
}));

// Mock the connection store
const mockConnectionStore = {
  setConnectionStatus: vi.fn(),
  setLastReconnectAttempt: vi.fn(),
  incrementReconnectAttempts: vi.fn(),
  resetReconnectAttempts: vi.fn()
};

vi.mock('@/stores/connection', () => ({
  useConnectionStore: () => mockConnectionStore
}));

describe('CentralizedSocketManager', () => {
  let socketManager: typeof centralizedSocketManagerInstance;
  let mockIo: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    // Reset socket mock state
    mockSocket.connected = false;
    mockSocket.disconnected = true;
    
    // Get the mocked io function
    const { io } = await import('socket.io-client');
    mockIo = io;
    
    // Reset auth store state    mockAuthStore.token = 'mock-jwt-token';
    mockAuthStore.isAuthenticated = true;
    
    // Reset global navigator.onLine if it exists
    Object.defineProperty(navigator, 'onLine', {
      value: true,
      writable: true
    });
    
    socketManager = centralizedSocketManagerInstance;
  });

  afterEach(() => {
    vi.useRealTimers();
    socketManager.cleanup();
  });

  describe('Socket Initialization', () => {
    it('should initialize socket with correct configuration when authenticated', () => {
      socketManager.initSocket();

      expect(mockIo).toHaveBeenCalledWith(
        expect.any(String), // backend URL
        expect.objectContaining({
          auth: {
            token: 'mock-jwt-token'
          },
          transports: ['websocket']
        })
      );
    });

    it('should not initialize socket when not authenticated', () => {
      mockAuthStore.isAuthenticated = false;
      mockAuthStore.token = null;

      socketManager.initSocket();

      expect(mockIo).not.toHaveBeenCalled();
    });

    it('should setup event listeners on socket initialization', () => {
      socketManager.initSocket();

      expect(mockSocket.on).toHaveBeenCalledWith('connect', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('connect_error', expect.any(Function));
    });

    it('should setup browser online/offline event listeners', () => {
      const addEventListenerSpy = vi.spyOn(window, 'addEventListener');
      
      socketManager.initSocket();

      expect(addEventListenerSpy).toHaveBeenCalledWith('online', expect.any(Function));
      expect(addEventListenerSpy).toHaveBeenCalledWith('offline', expect.any(Function));
    });
  });

  describe('Connection Status Management', () => {
    beforeEach(() => {
      socketManager.initSocket();
    });

    it('should update connection status on connect', () => {
      // Get the connect handler and call it
      const connectHandler = mockSocket.on.mock.calls.find(call => call[0] === 'connect')[1];
      connectHandler();

      expect(mockConnectionStore.setConnectionStatus).toHaveBeenCalledWith('connected');
      expect(mockConnectionStore.resetReconnectAttempts).toHaveBeenCalled();
    });

    it('should update connection status on disconnect and schedule reconnection', async () => {
      // Get the disconnect handler and call it with a reason
      const disconnectHandler = mockSocket.on.mock.calls.find(call => call[0] === 'disconnect')[1];
      disconnectHandler('transport close');

      expect(mockConnectionStore.setConnectionStatus).toHaveBeenCalledWith('disconnected');
      expect(mockConnectionStore.incrementReconnectAttempts).toHaveBeenCalled();

      // Advance timers to trigger reconnection
      await vi.advanceTimersByTimeAsync(5000);
      
      expect(mockSocket.connect).toHaveBeenCalled();
    });

    it('should handle auth error during connection and logout user', () => {
      const connectErrorHandler = mockSocket.on.mock.calls.find(call => call[0] === 'connect_error')[1];
      const authError = new Error('Authentication failed');
      authError.type = 'authentication_error';
      
      connectErrorHandler(authError);

      expect(mockConnectionStore.setConnectionStatus).toHaveBeenCalledWith('disconnected');
      expect(mockAuthStore.logout).toHaveBeenCalled();
    });

    it('should handle network error and schedule reconnection', async () => {
      const connectErrorHandler = mockSocket.on.mock.calls.find(call => call[0] === 'connect_error')[1];
      const networkError = new Error('Network error');
      
      connectErrorHandler(networkError);

      expect(mockConnectionStore.setConnectionStatus).toHaveBeenCalledWith('disconnected');
      expect(mockConnectionStore.incrementReconnectAttempts).toHaveBeenCalled();

      // Advance timers to trigger reconnection
      await vi.advanceTimersByTimeAsync(2000);
      
      expect(mockSocket.connect).toHaveBeenCalled();
    });
  });

  describe('Browser Online/Offline Handling', () => {
    beforeEach(() => {
      socketManager.initSocket();
    });

    it('should attempt reconnection when browser comes online', async () => {
      // Simulate browser going offline
      Object.defineProperty(navigator, 'onLine', { value: false, writable: true });
      
      // Get the online handler and call it
      const onlineHandler = socketManager['handleBrowserOnline'].bind(socketManager);
      onlineHandler();

      expect(mockSocket.connect).toHaveBeenCalled();
    });

    it('should not reconnect when coming online if not authenticated', () => {
      mockAuthStore.isAuthenticated = false;
      
      // Get the online handler and call it
      const onlineHandler = socketManager['handleBrowserOnline'].bind(socketManager);
      onlineHandler();

      expect(mockSocket.connect).not.toHaveBeenCalled();
    });

    it('should update connection status when browser goes offline', () => {
      const offlineHandler = socketManager['handleBrowserOffline'].bind(socketManager);
      offlineHandler();

      expect(mockConnectionStore.setConnectionStatus).toHaveBeenCalledWith('disconnected');
    });
  });

  describe('Force Reconnection', () => {
    beforeEach(() => {
      socketManager.initSocket();
    });

    it('should force reconnection when authenticated', () => {
      const result = socketManager.forceReconnect();

      expect(result).toBe(true);
      expect(mockSocket.disconnect).toHaveBeenCalled();
      expect(mockSocket.connect).toHaveBeenCalled();
      expect(mockConnectionStore.setConnectionStatus).toHaveBeenCalledWith('connecting');
    });

    it('should not force reconnection when not authenticated', () => {
      mockAuthStore.isAuthenticated = false;
      
      const result = socketManager.forceReconnect();

      expect(result).toBe(false);
      expect(mockSocket.disconnect).not.toHaveBeenCalled();
      expect(mockSocket.connect).not.toHaveBeenCalled();
    });
  });

  describe('Event Management', () => {
    beforeEach(() => {
      socketManager.initSocket();
    });

    it('should register event listeners and return unsubscribe function', () => {
      const handler = vi.fn();
      const unsubscribe = socketManager.on('test-event', handler);

      expect(mockSocket.on).toHaveBeenCalledWith('test-event', handler);
      expect(typeof unsubscribe).toBe('function');
    });

    it('should emit events through socket', () => {
      const testData = { message: 'test' };
      socketManager.emit('test-event', testData);

      expect(mockSocket.emit).toHaveBeenCalledWith('test-event', testData);
    });

    it('should remove specific event listeners', () => {
      const handler = vi.fn();
      socketManager.off('test-event', handler);

      expect(mockSocket.off).toHaveBeenCalledWith('test-event', handler);
    });
  });

  describe('Reconnection Timing', () => {
    beforeEach(() => {
      socketManager.initSocket();
    });

    it('should use exponential backoff for reconnection delays', async () => {
      const disconnectHandler = mockSocket.on.mock.calls.find(call => call[0] === 'disconnect')[1];
      
      // First disconnect - should schedule reconnection in 2s
      disconnectHandler('transport close');
      expect(mockConnectionStore.incrementReconnectAttempts).toHaveBeenCalledTimes(1);
      
      // Advance by 2 seconds
      await vi.advanceTimersByTimeAsync(2000);
      expect(mockSocket.connect).toHaveBeenCalledTimes(1);
      
      // Second disconnect - should schedule reconnection in 4s
      disconnectHandler('transport close');
      expect(mockConnectionStore.incrementReconnectAttempts).toHaveBeenCalledTimes(2);
      
      // Advance by 4 seconds
      await vi.advanceTimersByTimeAsync(4000);
      expect(mockSocket.connect).toHaveBeenCalledTimes(2);
    });

    it('should cap reconnection delay at maximum value', async () => {
      const disconnectHandler = mockSocket.on.mock.calls.find(call => call[0] === 'disconnect')[1];
      
      // Simulate many disconnects to reach max delay
      for (let i = 0; i < 10; i++) {
        disconnectHandler('transport close');
      }
      
      expect(mockConnectionStore.incrementReconnectAttempts).toHaveBeenCalledTimes(10);
      
      // Should cap at 30 seconds max
      await vi.advanceTimersByTimeAsync(30000);
      expect(mockSocket.connect).toHaveBeenCalled();
    });
  });

  describe('Cleanup', () => {
    it('should remove event listeners and disconnect socket on cleanup', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');
      
      socketManager.initSocket();
      socketManager.cleanup();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('online', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('offline', expect.any(Function));
      expect(mockSocket.removeAllListeners).toHaveBeenCalled();
      expect(mockSocket.disconnect).toHaveBeenCalled();
    });

    it('should clear reconnection timeout on cleanup', () => {
      socketManager.initSocket();
      
      // Trigger a disconnect to set up reconnection timeout
      const disconnectHandler = mockSocket.on.mock.calls.find(call => call[0] === 'disconnect')[1];
      disconnectHandler('transport close');
      
      // Cleanup should clear the timeout
      socketManager.cleanup();
      
      // Advance timers - socket.connect should not be called
      vi.advanceTimersByTime(5000);
      expect(mockSocket.connect).not.toHaveBeenCalled();
    });
  });

  describe('Socket State Checking', () => {
    it('should return correct connection status', () => {
      expect(socketManager.isConnected()).toBe(false);
      
      mockSocket.connected = true;
      mockSocket.disconnected = false;
      
      expect(socketManager.isConnected()).toBe(true);
    });

    it('should handle socket not being initialized', () => {
      const newManager = new CentralizedSocketManager();
      expect(newManager.isConnected()).toBe(false);
    });
  });
});
