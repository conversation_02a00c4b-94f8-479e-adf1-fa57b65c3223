import { describe, it, expect, beforeEach, vi, beforeAll, afterAll } from 'vitest';
import { PrismaClient, OfferType, OfferStatus, MatchStatus } from '@prisma/client';
import { MatchingService } from '../services/matchingService';
import { NotificationService } from '../services/notificationService';
import { EmailService } from '../services/email';
import { TwilioService } from '../services/twilio';
import { Server } from 'socket.io';

// Mock Socket.IO
const mockIo = {
  to: vi.fn().mockReturnThis(),
  emit: vi.fn()
} as unknown as Server;

// Mock dependencies
vi.mock('../services/notificationService');
vi.mock('../services/email'); 
vi.mock('../services/twilio');

describe('MatchingService', () => {
  let prisma: PrismaClient;
  let matchingService: MatchingService;
  let mockNotificationService: NotificationService;
  beforeAll(async () => {
    // Require DATABASE_URL environment variable to be explicitly set
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable must be set for tests. No fallback database connection allowed for security.');
    }
    
    // Use test database from DATABASE_URL environment variable
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });
    
    // Initialize mocked services
    mockNotificationService = {
      createAndEmitNotification: vi.fn()
    } as any;

    matchingService = new MatchingService(
      prisma,
      mockIo,
      mockNotificationService
    );
  });

  beforeEach(async () => {
    // Clean up test data before each test
    await prisma.offerMatch.deleteMany();
    await prisma.offer.deleteMany();
    await prisma.user.deleteMany();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('findPotentialMatches', () => {
    it('should find exact rate matches between compatible offers', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      // Create compatible offers
      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176, // Inverse rate (1/0.85)
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Test finding matches
      const matches = await matchingService.findPotentialMatches(offerA.id);

      expect(matches).toHaveLength(1);
      expect(matches[0]).toMatchObject({
        offerA: expect.objectContaining({ id: offerA.id }),
        offerB: expect.objectContaining({ id: offerB.id })
      });
    });

    it('should not match offers from the same user', async () => {
      // Create test user
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'user',
          reputationLevel: 3
        }
      });

      // Create two offers from same user
      const offerA = await prisma.offer.create({
        data: {
          userId: user.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: user.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Test finding matches - should not match same user's offers
      const matches = await matchingService.findPotentialMatches(offerA.id);

      expect(matches).toHaveLength(0);
    });

    it('should not match inactive offers', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      // Create offers - one inactive
      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.INACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Test finding matches for inactive offer
      const matches = await matchingService.findPotentialMatches(offerA.id);

      expect(matches).toHaveLength(0);
    });
  });

  describe('createMatch', () => {
    it('should create a new match with correct initial status', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create a match
      const match = await matchingService.createMatch(offerA, offerB);

      expect(match).toMatchObject({
        offerAId: offerA.id,
        offerBId: offerB.id,
        status: MatchStatus.PENDING,
        userAResponse: 'PENDING',
        userBResponse: 'PENDING'
      });
    });

    it('should handle duplicate match creation gracefully', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create a match
      const match1 = await matchingService.createMatch(offerA, offerB);
      
      // Try to create the same match again
      const match2 = await matchingService.createMatch(offerA, offerB);

      // Should return the existing match
      expect(match1.id).toBe(match2.id);
    });
  });

  describe('acceptMatch', () => {
    it('should handle race condition when both users accept simultaneously', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create a match
      const match = await matchingService.createMatch(offerA, offerB);

      // Simulate both users accepting simultaneously
      const [result1, result2] = await Promise.all([
        matchingService.acceptMatch(match.id, userA.id),
        matchingService.acceptMatch(match.id, userB.id)
      ]);

      // One should succeed and create transaction, the other should handle gracefully
      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
      
      // Check final match status
      const finalMatch = await prisma.offerMatch.findUnique({
        where: { id: match.id }
      });
      
      expect(finalMatch?.status).toBe(MatchStatus.MUTUAL_ACCEPTANCE);
    });

    it('should update match status correctly for partial acceptance', async () => {
      // Create test users and offers
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create a match
      const match = await matchingService.createMatch(offerA, offerB);

      // User A accepts first
      await matchingService.acceptMatch(match.id, userA.id);

      // Check partial acceptance status
      const updatedMatch = await prisma.offerMatch.findUnique({
        where: { id: match.id }
      });

      expect(updatedMatch?.status).toBe(MatchStatus.PARTIAL_ACCEPTANCE);
      expect(updatedMatch?.userAResponse).toBe('ACCEPTED');
      expect(updatedMatch?.userBResponse).toBe('PENDING');
    });

    it('should create transaction when both users accept', async () => {
      // Create test users and offers
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create a match
      const match = await matchingService.createMatch(offerA, offerB);

      // Both users accept
      await matchingService.acceptMatch(match.id, userA.id);
      const result = await matchingService.acceptMatch(match.id, userB.id);

      // Check that transaction was created
      expect(result.transaction).toBeDefined();
      expect(result.transaction.status).toBe('PENDING_NEGOTIATION');
      
      // Verify transaction exists in database
      const transaction = await prisma.transaction.findUnique({
        where: { id: result.transaction.id }
      });
      
      expect(transaction).toBeDefined();
      expect(transaction?.matchId).toBe(match.id);
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle expired matches', async () => {
      // Create test users and offers
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create an expired match (manually set past expiry)
      const expiredDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago
      const expiredMatch = await prisma.offerMatch.create({
        data: {
          offerAId: offerA.id,
          offerBId: offerB.id,
          status: MatchStatus.PENDING,
          userAResponse: 'PENDING',
          userBResponse: 'PENDING',
          createdAt: expiredDate,
          expiresAt: new Date(expiredDate.getTime() + 24 * 60 * 60 * 1000) // 24 hours from creation
        }
      });

      // Test cleanup of expired matches
      await matchingService.cleanupExpiredMatches();

      // Check that expired match was updated
      const updatedMatch = await prisma.offerMatch.findUnique({
        where: { id: expiredMatch.id }
      });

      expect(updatedMatch?.status).toBe(MatchStatus.EXPIRED);
    });

    it('should handle non-existent match', async () => {
      const fakeMatchId = 'fake-match-id';
      const fakeUserId = 'fake-user-id';

      await expect(
        matchingService.acceptMatch(fakeMatchId, fakeUserId)
      ).rejects.toThrow();
    });
  });
});
