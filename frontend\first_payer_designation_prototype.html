<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>First Payer Designation Demo</title>
    <style>
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: auto;
        }
        .perspective-bar {
            background-color: #e0e0e0;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .status-display {
            background-color: #f9f9f9;
            border: 1px solid #eee;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            min-height: 40px;
        }
        .action-area button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin-right: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
        }
        .action-area button:hover {
            background-color: #0056b3;
        }
        .action-area button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .action-area p {
            color: #555;
            font-style: italic;
        }
        .user-switcher {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }
        .user-switcher button {
            background-color: #6c757d;
            color: white;
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .user-switcher button.active {
            background-color: #495057;
            font-weight: bold;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>

<div class="container">
    <h2>First Payer Designation</h2>

    <div class="perspective-bar" id="perspectiveDisplay">Viewing as: Alice</div>

    <div class="status-display" id="statusDisplay">
        Loading status...
    </div>

    <div class="action-area" id="actionArea">
        <!-- Buttons will be rendered here by JavaScript -->
    </div>

    <div class="user-switcher">
        <strong>Switch View:</strong>
        <button id="viewAsAliceBtn" onclick="switchUser('alice')" class="active">View as Alice</button>
        <button id="viewAsBobBtn" onclick="switchUser('bob')">View as Bob</button>
    </div>
</div>

<script>
    const users = {
        alice: "Alice",
        bob: "Bob"
    };

    let currentUserKey = 'alice'; // 'alice' or 'bob'

    // --- STATE VARIABLES (Simulating backend state) ---
    let systemRecommendedPayerId = 'alice'; // Can be 'alice', 'bob', or null if no system recommendation
    let currentProposal = {
        payerId: systemRecommendedPayerId, // Who is proposed to pay
        byId: 'system'                    // Who made the proposal ('system', 'alice', or 'bob')
    };
    let finalizedPayerId = null; // 'alice' or 'bob' once agreed

    // --- UI Elements ---
    const perspectiveDisplay = document.getElementById('perspectiveDisplay');
    const statusDisplay = document.getElementById('statusDisplay');
    const actionArea = document.getElementById('actionArea');
    const viewAsAliceBtn = document.getElementById('viewAsAliceBtn');
    const viewAsBobBtn = document.getElementById('viewAsBobBtn');

    // --- Core Logic ---
    function renderUI() {
        const viewingUser = users[currentUserKey];
        const otherUserKey = currentUserKey === 'alice' ? 'bob' : 'alice';
        const otherUser = users[otherUserKey];

        perspectiveDisplay.textContent = `Viewing as: ${viewingUser}`;
        viewAsAliceBtn.classList.toggle('active', currentUserKey === 'alice');
        viewAsBobBtn.classList.toggle('active', currentUserKey === 'bob');

        let statusText = "";
        let actionsHTML = "";

        if (finalizedPayerId) {
            const finalPayerName = users[finalizedPayerId];
            statusText = `Agreement Reached: <strong>${finalizedPayerId === currentUserKey ? 'YOU' : finalPayerName}</strong> will pay first.`;
            actionsHTML = "<p>(Negotiation complete)</p>";
        } else {
            const proposalPayerId = currentProposal.payerId;
            const proposalById = currentProposal.byId;
            const proposalPayerName = users[proposalPayerId];

            if (proposalById === 'system') {
                if (proposalPayerId === currentUserKey) { // System recommended ME
                    statusText = `The system recommends <strong>YOU</strong> pay first.`;
                    actionsHTML = `
                        <button onclick="handleAgreeToCurrentProposal()">Agree & Pay First</button>
                        <button onclick="handleProposeOtherPays()">Propose ${otherUser} Pays First</button>
                    `;
                } else { // System recommended OTHER user
                    statusText = `The system recommends <strong>${proposalPayerName}</strong> pays first. Waiting for ${proposalPayerName}'s decision or your proposal.`;
                    actionsHTML = `
                        <button onclick="handleAgreeToCurrentProposal()">Agree (${proposalPayerName} Pays First)</button>
                        <button onclick="handleProposeSelfPays()">Propose I (${viewingUser}) Pay First</button>
                    `;
                }
            } else if (proposalById === currentUserKey) { // I made the current proposal
                statusText = `You proposed <strong>${proposalPayerId === currentUserKey ? 'YOU' : proposalPayerName}</strong> pay first. Waiting for ${otherUser}'s response.`;
                actionsHTML = "<p>(Waiting for response)</p>";
                // Optional: Add a button to withdraw/change proposal here
            } else { // The OTHER user made the current proposal (proposalById === otherUserKey)
                if (proposalPayerId === currentUserKey) { // Other user proposed ME
                    statusText = `<strong>${otherUser}</strong> proposed <strong>YOU</strong> pay first.`;
                    actionsHTML = `
                        <button onclick="handleAgreeToCurrentProposal()">Agree & Pay First</button>
                        <button onclick="handleProposeOtherPays()">Propose ${otherUser} Pays First (Counter)</button>
                    `;
                } else { // Other user proposed THEMSELVES (proposalPayerId === otherUserKey)
                    statusText = `<strong>${otherUser}</strong> proposed <strong>THEY</strong> pay first.`;
                    actionsHTML = `
                        <button onclick="handleAgreeToCurrentProposal()">Agree (${otherUser} Pays First)</button>
                        <button onclick="handleProposeSelfPays()">Propose I (${viewingUser}) Pay First (Counter)</button>
                    `;
                }
            }
        }

        statusDisplay.innerHTML = statusText;
        actionArea.innerHTML = actionsHTML;
    }

    // --- Action Handler Functions ---
    function handleAgreeToCurrentProposal() {
        if (finalizedPayerId) return; // Don't do anything if already finalized

        finalizedPayerId = currentProposal.payerId;
        // In a real app, send this to the server
        console.log(`Agreement: ${users[finalizedPayerId]} will pay. Proposal was by ${currentProposal.byId} for ${users[currentProposal.payerId]}`);
        renderUI();
    }

    function handleProposeOtherPays() {
        if (finalizedPayerId) return;

        const otherUserKey = currentUserKey === 'alice' ? 'bob' : 'alice';
        currentProposal = {
            payerId: otherUserKey,
            byId: currentUserKey
        };
        // In a real app, send this to the server
        console.log(`${users[currentUserKey]} proposes ${users[otherUserKey]} pays. State:`, currentProposal);
        renderUI();
    }

    function handleProposeSelfPays() {
        if (finalizedPayerId) return;

        currentProposal = {
            payerId: currentUserKey,
            byId: currentUserKey
        };
        // In a real app, send this to the server
        console.log(`${users[currentUserKey]} proposes THEY pay. State:`, currentProposal);
        renderUI();
    }

    // --- Utility Functions ---
    function switchUser(newUserKey) {
        currentUserKey = newUserKey;
        console.log(`Switched view to ${users[currentUserKey]}`);
        renderUI();
    }

    // --- Initial Setup ---
    // Simulate initial system recommendation if not already set
    if (!currentProposal.payerId && systemRecommendedPayerId) {
         currentProposal = {
            payerId: systemRecommendedPayerId,
            byId: 'system'
        };
    } else if (!currentProposal.payerId && !systemRecommendedPayerId) {
        // Fallback if no system recommendation, e.g. Alice is default proposed by system
        systemRecommendedPayerId = 'alice';
         currentProposal = {
            payerId: 'alice',
            byId: 'system'
        };
    }


    // Initial render
    renderUI();

</script>

</body>
</html>