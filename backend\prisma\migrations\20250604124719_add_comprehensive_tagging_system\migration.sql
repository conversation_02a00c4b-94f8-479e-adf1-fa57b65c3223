/*
  Warnings:

  - You are about to drop the column `tag` on the `debug_report_tags` table. All the data in the column will be lost.

*/
-- DropIndex
DROP INDEX "debug_report_tags_report_id_tag_key";

-- DropIndex
DROP INDEX "debug_report_tags_tag_idx";

-- AlterTable
ALTER TABLE "debug_report_tags" DROP COLUMN "tag",
ADD COLUMN     "tag_id" TEXT,
ADD COLUMN     "tag_name" VARCHAR(50);

-- CreateTable
CREATE TABLE "tag_categories" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "color" VARCHAR(7),
    "order" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tag_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tags" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "displayName" JSONB NOT NULL,
    "description" JSONB,
    "category_id" TEXT,
    "color" VARCHAR(7),
    "icon" VARCHAR(50),
    "weight" INTEGER NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_system" BOOLEAN NOT NULL DEFAULT false,
    "usage_count" INTEGER NOT NULL DEFAULT 0,
    "last_used_at" TIMESTAMP(3),
    "ai_relevance" DOUBLE PRECISION DEFAULT 0.0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tags_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tag_report_type_associations" (
    "id" TEXT NOT NULL,
    "tag_id" TEXT NOT NULL,
    "report_type" VARCHAR(50) NOT NULL,
    "weight" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tag_report_type_associations_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tag_categories_name_key" ON "tag_categories"("name");

-- CreateIndex
CREATE INDEX "tag_categories_name_idx" ON "tag_categories"("name");

-- CreateIndex
CREATE INDEX "tag_categories_order_idx" ON "tag_categories"("order");

-- CreateIndex
CREATE UNIQUE INDEX "tags_name_key" ON "tags"("name");

-- CreateIndex
CREATE INDEX "tags_name_idx" ON "tags"("name");

-- CreateIndex
CREATE INDEX "tags_category_id_idx" ON "tags"("category_id");

-- CreateIndex
CREATE INDEX "tags_is_active_idx" ON "tags"("is_active");

-- CreateIndex
CREATE INDEX "tags_usage_count_idx" ON "tags"("usage_count");

-- CreateIndex
CREATE INDEX "tags_ai_relevance_idx" ON "tags"("ai_relevance");

-- CreateIndex
CREATE INDEX "tag_report_type_associations_report_type_idx" ON "tag_report_type_associations"("report_type");

-- CreateIndex
CREATE INDEX "tag_report_type_associations_weight_idx" ON "tag_report_type_associations"("weight");

-- CreateIndex
CREATE UNIQUE INDEX "tag_report_type_associations_tag_id_report_type_key" ON "tag_report_type_associations"("tag_id", "report_type");

-- CreateIndex
CREATE INDEX "debug_report_tags_report_id_idx" ON "debug_report_tags"("report_id");

-- CreateIndex
CREATE INDEX "debug_report_tags_tag_id_idx" ON "debug_report_tags"("tag_id");

-- CreateIndex
CREATE INDEX "debug_report_tags_tag_name_idx" ON "debug_report_tags"("tag_name");

-- AddForeignKey
ALTER TABLE "debug_report_tags" ADD CONSTRAINT "debug_report_tags_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "tags"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tags" ADD CONSTRAINT "tags_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "tag_categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tag_report_type_associations" ADD CONSTRAINT "tag_report_type_associations_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;
