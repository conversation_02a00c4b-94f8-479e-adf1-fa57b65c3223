import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { aiApiService } from '@/services/aiApiService';
import type { ReportDetails } from '@/types/logging';

// Voice analysis failure interface
export interface VoiceAnalysisFailure {
  reason: 'POOR_QUALITY_OR_SHORT' | 'TOO_NOISY' | 'IRRELEVANT_CONTENT';
  userMessageKey: string;
  suggestionKeys: string[];
  transcription?: string;
  confidence: number;
}

/**
 * AI Analysis Store - Global state for AI-powered voice analysis
 * Provides a singleton instance that can be shared across components
 */
export const useAiAnalysisStore = defineStore('aiAnalysis', () => {
  // State
  const isProcessing = ref(false);
  const isTranscribing = ref(false);
  const isAnalyzing = ref(false);
  const transcription = ref<string>('');
  const generatedReport = ref<ReportDetails | null>(null);
  const confidence = ref<number>(0);
  const processingTime = ref<number>(0);
  const error = ref<string>('');
  const analysisFailure = ref<VoiceAnalysisFailure | null>(null);

  // AI service state
  type AiStatus = 'idle' | 'checking' | 'available' | 'unavailable' | 'error';
  const status = ref<AiStatus>('idle');
  const isAvailable = ref<boolean>(false);
  const supportedLanguages = ref<string[]>(['en', 'fa']);
  const maxAudioDuration = ref<number>(60);

  // Computed properties
  const hasTranscription = computed(() => !!transcription.value);
  const hasGeneratedReport = computed(() => !!generatedReport.value);
  const hasAnalysisFailure = computed(() => !!analysisFailure.value);
  const confidencePercentage = computed(() => Math.round(confidence.value * 100));
  const processingTimeFormatted = computed(() => {
    if (processingTime.value < 1000) {      return `${processingTime.value}ms`;
    }
    return `${(processingTime.value / 1000).toFixed(1)}s`;
  });

  /**
   * Check AI service availability
   */   const checkAvailability = async (): Promise<boolean> => {
  status.value = 'checking'

  try {
    const result = await aiApiService.getStatus()

    // ← changed code: bail out if result.status is undefined
    if (!result.status) {
      status.value = 'unavailable'
      return false
    }

    if (result.success) {
      isAvailable.value = result.status.features.voiceToReport
      supportedLanguages.value = result.status.supportedLanguages || ['en', 'fa']
      maxAudioDuration.value = result.status.maxAudioDuration || 60

      status.value = isAvailable.value ? 'available' : 'unavailable'
      return isAvailable.value
    }

    status.value = 'unavailable'
    return false
  } catch (err: any) {
    console.error('Failed to check AI service availability:', err)
    isAvailable.value = false
    status.value = 'error'
    return false
  }
}

/**
 * Process voice recording to generate bug report
 */
const processVoiceToReport = async (
  audioBase64: string,
  mimeType: string,
  duration: number,
  language: string = 'en',
  predefinedTags: Record<string, string[]> = {}
): Promise<boolean> => {
  if (!isAvailable.value) {
    error.value = 'AI service not available';
    return false;
  }    isProcessing.value = true;
    isTranscribing.value = true;
    isAnalyzing.value = false;
    error.value = '';
    transcription.value = '';
    generatedReport.value = null;
    analysisFailure.value = null;
    confidence.value = 0;

    try {
      // Get user context
      const userContext = {
        currentPage: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
      };      // Call the combined voice-to-report API
      const result = await aiApiService.processVoiceToReport({
        audioData: audioBase64,
        mimeType,
        duration,
        language,
        predefinedTags,        userContext,
      });

      isTranscribing.value = false;
      isAnalyzing.value = true;

      // Handle analysis failure
      if (!result.success && result.analysisFailure) {
        transcription.value = result.transcription || '';
        analysisFailure.value = {
          reason: result.analysisFailure.reason,
          userMessageKey: result.analysisFailure.userMessageKey,
          suggestionKeys: result.analysisFailure.suggestionKeys,
          confidence: 0,
        };
        confidence.value = 0;
        processingTime.value = result.processingTime || 0;
        return false;
      }

      // Handle successful analysis
      if (result.success && result.generatedReport) {
          transcription.value = result.transcription || '';
          generatedReport.value = {
            type: result.generatedReport.suggestedType,
            severity: result.generatedReport.suggestedSeverity,
            title: result.generatedReport.title,
            description: result.generatedReport.description,
            stepsToReproduce: result.generatedReport.stepsToReproduce || '',
            expectedBehavior: result.generatedReport.expectedBehavior || '',
            actualBehavior: result.generatedReport.actualBehavior || '',
            additionalNotes: result.generatedReport.additionalNotes || '',
            reportTags: (result.generatedReport.suggestedTags || []).map(tag => {
              console.log('[aiAnalysisStore] Original tag:', tag);
              const transformedTag = {
                ...(tag.origin === 'PREDEFINED' ? { tagId: tag.tag } : { tagName: tag.tag }),
                origin: tag.origin
              };
              console.log('[aiAnalysisStore] Transformed tag:', transformedTag);
              return transformedTag;            }),
          };
          confidence.value = result.generatedReport.confidence;
          processingTime.value = result.processingTime || 0;
          return true;
      } else {
        error.value = result.error || 'Analysis failed';
        return false;
      }
    } catch (err: any) {
      console.error('Voice-to-report processing failed:', err);
      error.value = err.message || 'Processing failed';
      return false;
    } finally {
      isProcessing.value = false;
      isTranscribing.value = false;
      isAnalyzing.value = false;
    }
  };

  /**
   * Transcribe audio only (without AI analysis)
   */
  const transcribeAudio = async (
    audioBase64: string,
    mimeType: string,
    duration: number,
    language: string = 'en'
  ): Promise<string | null> => {    if (!isAvailable.value) {
      error.value = 'AI service not available';
      return null;
    }

    isTranscribing.value = true;
    error.value = '';

    try {
      const result = await aiApiService.transcribeAudio({
        audioData: audioBase64,
        mimeType,
        duration,
        language,
      });

      if (result.success && result.transcription) {
        transcription.value = result.transcription;
        return result.transcription;
      } else {
        error.value = result.error || 'Transcription failed';
        return null;
      }
    } catch (err: any) {
      console.error('Audio transcription failed:', err);
      error.value = err.message || 'Transcription failed';
      return null;
    } finally {
      isTranscribing.value = false;
    }  };

  /**
   * Clear all state
   */
  const clearState = () => {
    isProcessing.value = false;
    isTranscribing.value = false;
    isAnalyzing.value = false;
    transcription.value = '';
    generatedReport.value = null;
    analysisFailure.value = null;
    confidence.value = 0;
    processingTime.value = 0;
    error.value = '';
  };

  // Initialize by checking availability
  checkAvailability();

  return {    // State
    status,
    isProcessing,
    isTranscribing,
    isAnalyzing,
    transcription,
    generatedReport,
    analysisFailure,
    confidence,
    confidencePercentage,
    processingTime,
    processingTimeFormatted,
    error,

    // Availability
    isAvailable,
    supportedLanguages,
    maxAudioDuration,

    // Computed flags
    hasTranscription,
    hasGeneratedReport,
    hasAnalysisFailure,    // Methods
    checkAvailability,
    processVoiceToReport,
    transcribeAudio,
    clearState,
  };
});
