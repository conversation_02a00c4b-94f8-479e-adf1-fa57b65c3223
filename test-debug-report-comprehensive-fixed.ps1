#!/usr/bin/env pwsh

# DebugReportButtonEnhanced Comprehensive Test Suite
# This script runs all tests related to the debug report functionality
# and verifies that data flows correctly from frontend to backend

param(
    [switch]$Verbose,
    [switch]$Coverage,
    [string]$TestFilter = ""
)

Write-Host "Debug Report Data Flow Test Suite" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
Write-Host ""

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to run tests with proper error handling
function Run-TestSuite {
    param(
        [string]$TestDir,
        [string]$TestName,
        [string]$TestCommand
    )
    
    Write-Host "Running $TestName tests..." -ForegroundColor Yellow
    Write-Host "   Directory: $TestDir" -ForegroundColor Gray
    Write-Host "   Command: $TestCommand" -ForegroundColor Gray
    Write-Host ""
    
    try {
        Push-Location $TestDir
        
        if ($Coverage) {
            $TestCommand += " --coverage"
        }
        
        if ($Verbose) {
            $TestCommand += " --reporter=verbose"
        }
        
        if ($TestFilter) {
            $TestCommand += " --grep `"$TestFilter`""
        }
        
        Invoke-Expression $TestCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS: $TestName tests PASSED" -ForegroundColor Green
        } else {
            Write-Host "FAILED: $TestName tests FAILED" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "ERROR running $TestName tests: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    finally {
        Pop-Location
    }
    
    Write-Host ""
    return $true
}

# Test configuration
$frontendDir = "C:\Code\MUNygo\frontend"
$backendDir = "C:\Code\MUNygo\backend"

# Array to track test results
$testResults = @()

Write-Host "Test Plan:" -ForegroundColor Magenta
Write-Host "1. Frontend Component Tests (DebugReportButtonEnhanced)" -ForegroundColor Gray
Write-Host "2. Frontend Integration Tests (Data Flow)" -ForegroundColor Gray
Write-Host "3. Backend Service Tests (ClientLogService)" -ForegroundColor Gray
Write-Host "4. Backend Integration Tests (Debug Routes)" -ForegroundColor Gray
Write-Host ""

# 1. Frontend Component Tests
Write-Host "Step 1: Frontend Component Tests" -ForegroundColor Magenta
$success = Run-TestSuite -TestDir $frontendDir -TestName "Frontend Component" -TestCommand "npm test -- src/test/components/DebugReportButtonEnhanced.test.ts"
$testResults += @{ Name = "Frontend Component Tests"; Success = $success }

if (-not $success) {
    Write-Host "WARNING: Component tests failed. Continuing with other tests..." -ForegroundColor Yellow
}

# 2. Frontend Integration Tests
Write-Host "Step 2: Frontend Integration Tests" -ForegroundColor Magenta
$success = Run-TestSuite -TestDir $frontendDir -TestName "Frontend Integration" -TestCommand "npm test -- src/test/integration/debugReportDataFlow.test.ts"
$testResults += @{ Name = "Frontend Integration Tests"; Success = $success }

if (-not $success) {
    Write-Host "WARNING: Frontend integration tests failed. Continuing with other tests..." -ForegroundColor Yellow
}

# 3. Backend Service Tests
Write-Host "Step 3: Backend Service Tests" -ForegroundColor Magenta
$success = Run-TestSuite -TestDir $backendDir -TestName "Backend Service" -TestCommand "npm test -- src/test/services/clientLogService.test.ts"
$testResults += @{ Name = "Backend Service Tests"; Success = $success }

if (-not $success) {
    Write-Host "WARNING: Backend service tests failed. Continuing with other tests..." -ForegroundColor Yellow
}

# 4. Backend Integration Tests
Write-Host "Step 4: Backend Integration Tests" -ForegroundColor Magenta
$success = Run-TestSuite -TestDir $backendDir -TestName "Backend Integration" -TestCommand "npm test -- src/test/integration/debugRoutes.integration.test.ts"
$testResults += @{ Name = "Backend Integration Tests"; Success = $success }

if (-not $success) {
    Write-Host "WARNING: Backend integration tests failed." -ForegroundColor Yellow
}

# Test Summary
Write-Host "Test Results Summary" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan
Write-Host ""

$allPassed = $true
foreach ($result in $testResults) {
    $status = if ($result.Success) { "PASSED" } else { "FAILED" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    Write-Host "$($result.Name): $status" -ForegroundColor $color
    
    if (-not $result.Success) {
        $allPassed = $false
    }
}

Write-Host ""

if ($allPassed) {
    Write-Host "ALL TESTS PASSED!" -ForegroundColor Green
    Write-Host "The DebugReportButtonEnhanced component correctly:" -ForegroundColor Green
    Write-Host "- Collects all form data from user input" -ForegroundColor Green
    Write-Host "- Sends complete payload to the backend" -ForegroundColor Green
    Write-Host "- Backend properly saves all form fields" -ForegroundColor Green
    Write-Host "- Data integrity is maintained throughout the flow" -ForegroundColor Green
} else {
    Write-Host "SOME TESTS FAILED" -ForegroundColor Red
    Write-Host "Please review the test output above to identify issues." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Test Coverage Areas:" -ForegroundColor Magenta
Write-Host "- Form data collection and validation" -ForegroundColor Gray
Write-Host "- User interaction logging" -ForegroundColor Gray
Write-Host "- API communication (frontend to backend)" -ForegroundColor Gray
Write-Host "- Data persistence and integrity" -ForegroundColor Gray
Write-Host "- Error handling and edge cases" -ForegroundColor Gray
Write-Host "- Special characters and Unicode support" -ForegroundColor Gray
Write-Host ""

if ($Coverage) {
    Write-Host "Coverage reports generated in respective coverage directories" -ForegroundColor Blue
}

Write-Host "Test suite completed successfully!" -ForegroundColor Green
