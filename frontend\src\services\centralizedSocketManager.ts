// Centralized Socket Manager
// This service handles all socket communication for the entire application
// to prevent multiple socket instances and ensure reliable event handling

import { io, type Socket } from 'socket.io-client';
import { useAuthStore } from '@/stores/auth';
import { useConnectionStore, type TransportType } from '@/stores/connection';
import { 
  INTEREST_RECEIVED, 
  INTEREST_PROCESSED, 
  INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY,
  INTEREST_REQUEST_DECLINED,
  OFFER_CREATED,
  OFFER_UPDATED,
  OFFER_STATUS_CHANGED,
  CHAT_MESSAGE_RECEIVE,
  SYSTEM_MESSAGE_RECEIVE,
  TRANSACTION_STATUS_UPDATED,
  NEGOTIATION_STATE_UPDATED,
  NEGOTIATION_FINALIZED,
  NEW_NOTIFICATION,
  MATCH_FOUND,
  MATCH_ACCEPTED,
  MATCH_DECLINED,
  MATCH_EXPIRED,
  MATCH_CONVERTED,
  type InterestReceivedPayload,
  type InterestProcessedPayload,
  type InterestRequestAcceptedAndChatReadyPayload,
  type YourInterestDeclinedPayload,
  type OfferCreatedPayload,
  type ChatMessageReceivePayload,
  type SystemMessagePayload,
  type TransactionStatusUpdatePayload,
  type PayerNegotiationStatePayload,
  type NegotiationFinalizedPayload,
  type NewNotificationPayload,
  type MatchFoundPayload,
  type MatchAcceptedPayload,
  type MatchDeclinedPayload,
  type MatchExpiredPayload,
  type MatchConvertedPayload
} from '@/types/socketEvents';

// Debug flag to control console logging in development vs production
// Only show critical socket connection information, not verbose handler registration
const DEBUG_SOCKET_VERBOSE = import.meta.env.VITE_DEBUG_SOCKET_VERBOSE === 'true';

// Event handler type definitions
type EventHandler<T = any> = (payload: T) => void;
type EventHandlers = {
  [INTEREST_RECEIVED]: EventHandler<InterestReceivedPayload>[];
  [INTEREST_PROCESSED]: EventHandler<InterestProcessedPayload>[];
  [INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY]: EventHandler<InterestRequestAcceptedAndChatReadyPayload>[];
  [INTEREST_REQUEST_DECLINED]: EventHandler<YourInterestDeclinedPayload>[];
  [OFFER_CREATED]: EventHandler<OfferCreatedPayload>[];
  [OFFER_UPDATED]: EventHandler<any>[];
  [OFFER_STATUS_CHANGED]: EventHandler<any>[];
  [CHAT_MESSAGE_RECEIVE]: EventHandler<ChatMessageReceivePayload>[];
  [SYSTEM_MESSAGE_RECEIVE]: EventHandler<SystemMessagePayload>[];
  [TRANSACTION_STATUS_UPDATED]: EventHandler<TransactionStatusUpdatePayload>[];
  [NEGOTIATION_STATE_UPDATED]: EventHandler<PayerNegotiationStatePayload>[];
  [NEGOTIATION_FINALIZED]: EventHandler<NegotiationFinalizedPayload>[];
  [NEW_NOTIFICATION]: EventHandler<NewNotificationPayload>[];
  [MATCH_FOUND]: EventHandler<MatchFoundPayload>[];
  [MATCH_ACCEPTED]: EventHandler<MatchAcceptedPayload>[];
  [MATCH_DECLINED]: EventHandler<MatchDeclinedPayload>[];
  [MATCH_EXPIRED]: EventHandler<MatchExpiredPayload>[];
  [MATCH_CONVERTED]: EventHandler<MatchConvertedPayload>[];
  connect: EventHandler<void>[];
  disconnect: EventHandler<Socket.DisconnectReason>[];
  connect_error: EventHandler<Error>[];
};

class CentralizedSocketManager {
  private socket: Socket | null = null;
  private initializationPromise: Promise<Socket> | null = null;
  private isOnlineListenerAdded = false;
  private reconnectionTimeoutId: number | null = null;
  private lastAuthToken: string | null = null;
  private authErrorCount = 0;
  private maxAuthErrors = 3;
  private isAuthenticationError = false;

  private handlers: EventHandlers = {
    [INTEREST_RECEIVED]: [],
    [INTEREST_PROCESSED]: [],
    [INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY]: [],
    [INTEREST_REQUEST_DECLINED]: [],
    [OFFER_CREATED]: [],
    [OFFER_UPDATED]: [],
    [OFFER_STATUS_CHANGED]: [],
    [CHAT_MESSAGE_RECEIVE]: [],
    [SYSTEM_MESSAGE_RECEIVE]: [],
    [TRANSACTION_STATUS_UPDATED]: [],
    [NEGOTIATION_STATE_UPDATED]: [],
    [NEGOTIATION_FINALIZED]: [],
    [NEW_NOTIFICATION]: [],
    [MATCH_FOUND]: [],
    [MATCH_ACCEPTED]: [],
    [MATCH_DECLINED]: [],
    [MATCH_EXPIRED]: [],
    [MATCH_CONVERTED]: [],
    connect: [],
    disconnect: [],
    connect_error: []
  };
  constructor() {
    console.log('🚀 [CentralizedSocketManager] Creating singleton instance');
    this.setupBrowserOfflineDetection();
  }
  // Browser online/offline event handlers as class methods
  private handleOnline = () => {
    console.log('🌐 [CentralizedSocketManager] Browser back online, checking socket connection...');
    
    const authStore = useAuthStore();
    const connectionStore = useConnectionStore();
    
    // Update connection store to indicate we're back online
    if (!this.isConnected() && authStore.isAuthenticated) {
      console.log('[CentralizedSocketManager] Attempting reconnection due to browser online event');
      connectionStore.setReconnecting();
      
      // Force a fresh connection since we were offline
      this.forceReconnect().catch(error => {
        console.error('[CentralizedSocketManager] Failed to reconnect on browser online:', error);
        // Set disconnected status with specific reason
        connectionStore.setDisconnected('reconnect_failed_after_online');
      });
    } else if (this.isConnected()) {
      console.log('[CentralizedSocketManager] Socket already connected, connection is healthy');
      connectionStore.setConnected(true);
    }
  };

  private handleOffline = () => {
    console.log('🚫 [CentralizedSocketManager] Browser went offline');
    const connectionStore = useConnectionStore();
    connectionStore.setDisconnected('browser_offline');
  };

  // Browser online/offline detection
  private setupBrowserOfflineDetection() {
    if (this.isOnlineListenerAdded) return;
    
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
    
    this.isOnlineListenerAdded = true;
    
    // Initial check
    if (!navigator.onLine) {
      console.log('🚫 [CentralizedSocketManager] Browser is currently offline');
      const connectionStore = useConnectionStore();
      connectionStore.setDisconnected('browser_offline');
    }
  }

  private cleanupBrowserOfflineDetection() {
    if (!this.isOnlineListenerAdded) return;
    
    console.log('[CentralizedSocketManager] Cleaning up browser offline detection listeners');
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
    
    this.isOnlineListenerAdded = false;
  }  // Register event handlers
  on<T extends keyof EventHandlers>(
    event: T, 
    handler: T extends keyof EventHandlers ? EventHandlers[T][0] : EventHandler
  ): () => void {    if (DEBUG_SOCKET_VERBOSE) {
      console.log(`🔌 [CentralizedSocketManager] Registering handler for event: ${event}`);
    }
    
    if (!this.handlers[event]) {
      this.handlers[event] = [];
    }
    
    this.handlers[event].push(handler);
    
    if (DEBUG_SOCKET_VERBOSE) {
      console.log(`🔌 [CentralizedSocketManager] Handler registered. New count for ${event}:`, this.handlers[event].length);
    }
    
    // Return unsubscribe function
    return () => {
      const handlers = this.handlers[event];
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);        if (DEBUG_SOCKET_VERBOSE) {
          console.log(`🔌 [CentralizedSocketManager] Unregistered handler for event: ${event}`);
        }
      }
    };
  }

  // Register one-time event handler
  once<T extends keyof EventHandlers>(
    event: T, 
    handler: T extends keyof EventHandlers ? EventHandlers[T][0] : EventHandler
  ): () => void {
    if (DEBUG_SOCKET_VERBOSE) {
      console.log(`🔌 [CentralizedSocketManager] Registering one-time handler for event: ${event}`);
    }
    
    let isHandled = false;
    
    // Create a wrapper that auto-unsubscribes after first call
    const onceHandler = (payload: any) => {
      if (isHandled) return;
      isHandled = true;
      
      // Remove from handlers array
      const handlers = this.handlers[event];
      const index = handlers.indexOf(onceHandler as any);
      if (index > -1) {
        handlers.splice(index, 1);
        if (DEBUG_SOCKET_VERBOSE) {
          console.log(`🔌 [CentralizedSocketManager] Auto-unregistered one-time handler for event: ${event}`);
        }
      }
      
      // Call the original handler
      handler(payload);
    };
    
    if (!this.handlers[event]) {
      this.handlers[event] = [];
    }
    
    this.handlers[event].push(onceHandler as any);
    
    if (DEBUG_SOCKET_VERBOSE) {
      console.log(`🔌 [CentralizedSocketManager] One-time handler registered. New count for ${event}:`, this.handlers[event].length);
    }
    
    // Return unsubscribe function (in case user wants to cancel before it fires)
    return () => {
      if (isHandled) return;
      isHandled = true;
      
      const handlers = this.handlers[event];
      const index = handlers.indexOf(onceHandler as any);
      if (index > -1) {
        handlers.splice(index, 1);
        if (DEBUG_SOCKET_VERBOSE) {
          console.log(`🔌 [CentralizedSocketManager] Manually unregistered one-time handler for event: ${event}`);
        }
      }
    };
  }

  // Get current socket instance
  getSocket(): Socket | null {
    return this.socket;
  }

  // Check if socket is connected
  isConnected(): boolean {
    return this.socket?.connected || false;
  }
  // Initialize socket connection
  async initializeSocket(): Promise<Socket> {
    console.log('🚀 [CentralizedSocketManager] initializeSocket called');
    console.log('🔍 [CentralizedSocketManager] Current state - connected:', this.isConnected(), 'online:', navigator.onLine);

    const authStore = useAuthStore();
    const currentToken = authStore.token;

    // Check if we have a valid token
    if (!currentToken) {
      console.warn('[CentralizedSocketManager] No authentication token available');
      throw new Error('No authentication token available');
    }

    // Check if token has changed (user re-logged in)
    if (this.lastAuthToken && this.lastAuthToken !== currentToken) {
      console.log('🔄 [CentralizedSocketManager] Token changed, forcing reconnection');
      this.forceDisconnect();
      this.resetAuthErrorState();

      // Clear any existing initialization promise when token changes
      this.initializationPromise = null;
    }

    // If already initializing with the same token, return existing promise
    if (this.initializationPromise && this.lastAuthToken === currentToken) {
      console.log('🚀 [CentralizedSocketManager] Already initializing with same token, returning existing promise');
      return this.initializationPromise;
    }

    // If socket exists and is connected with same token, return it
    if (this.socket?.connected && this.lastAuthToken === currentToken) {
      console.log('🚀 [CentralizedSocketManager] Socket already connected with current token, returning existing socket');
      return Promise.resolve(this.socket);
    }

    // Re-setup browser offline detection if it was previously cleaned up
    if (!this.isOnlineListenerAdded) {
      console.log('🚀 [CentralizedSocketManager] Re-setting up browser offline detection');
      this.setupBrowserOfflineDetection();
    }

    // Start initialization
    console.log('🚀 [CentralizedSocketManager] Starting fresh socket initialization');
    this.initializationPromise = this._createSocket();

    try {
      const socket = await this.initializationPromise;
      this.lastAuthToken = currentToken;
      this.initializationPromise = null;
      console.log('✅ [CentralizedSocketManager] Socket initialization successful');
      return socket;
    } catch (error) {
      console.error('❌ [CentralizedSocketManager] Socket initialization failed:', error);
      this.initializationPromise = null;
      
      // Update connection store with failure status
      const connectionStore = useConnectionStore();
      connectionStore.setDisconnected(this._getErrorMessage(error));
      
      throw error;
    }
  }
  private async _createSocket(): Promise<Socket> {
    return new Promise((resolve, reject) => {
      try {
        // Clean up existing socket
        if (this.socket) {
          console.log('🚀 [CentralizedSocketManager] Cleaning up existing socket before creating new one');
          this.socket.removeAllListeners();
          this.socket.disconnect();
          this.socket = null;
        }

        const authStore = useAuthStore();
        const token = authStore.token;
        
        if (!token) {
          throw new Error('No authentication token available');
        }

        const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3000';
        console.log('🚀 [CentralizedSocketManager] Creating socket connection to:', backendUrl);
        
        this.socket = io(backendUrl, {
          auth: { token },
          transports: ['websocket', 'polling'],
          timeout: 20000, // Increased timeout to 20 seconds
          reconnectionAttempts: 3, // Reduced attempts for faster failure detection
          reconnectionDelay: 2000, // Increased initial delay
          reconnectionDelayMax: 10000, // Increased max delay
          forceNew: true, // Force new connection to avoid cached connections
          autoConnect: true, // Ensure auto connection
        });

        // Set up core event listeners
        // Store timeout ID to clear it when connection succeeds or fails
        let connectionTimeoutId: number | null = null;        this.socket.on('connect', () => {
          console.log('✅ [CentralizedSocketManager] Connected with ID:', this.socket?.id);
          console.log('🔍 [CentralizedSocketManager] Transport used:', this.socket?.io.engine.transport.name);
          console.log('🔍 [CentralizedSocketManager] Socket state - connected:', this.socket?.connected);

          // Clear connection timeout since we connected successfully
          if (connectionTimeoutId) {
            clearTimeout(connectionTimeoutId);
            connectionTimeoutId = null;
          }

          // Reset authentication error state on successful connection
          this.resetAuthErrorState();

          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setConnected(true);

          // Detect transport type and update store
const transportName = this.socket?.io.engine.transport.name;
const validTransports: TransportType[] = ['websocket', 'polling'];
const transport = validTransports.includes(transportName as TransportType)
  ? transportName as TransportType
  : 'websocket';
connectionStore.setTransportType(transport);          this._notifyHandlers('connect', undefined);
          
          // Initialize notification system after socket connection
          this._initializeNotificationSystemAfterConnection();
          
          resolve(this.socket!);
        });this.socket.on('disconnect', (reason: Socket.DisconnectReason) => {
          console.log('🔌 [CentralizedSocketManager] Disconnected:', reason);
          
          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setDisconnected(reason);
          
          // Handle different disconnect reasons
          if (reason === 'io server disconnect') {
            // Server initiated disconnect - might be maintenance
            console.log('[CentralizedSocketManager] Server disconnect detected, scheduling reconnect...');
            this.scheduleReconnection();
          } else if (reason === 'transport close' || reason === 'transport error') {
            // Network issues - try to reconnect if browser is online
            if (navigator.onLine) {
              console.log('[CentralizedSocketManager] Network issue detected, scheduling reconnect...');
              this.scheduleReconnection();
            }
          }
          
          this._notifyHandlers('disconnect', reason);
        });        this.socket.on('connect_error', async (error: Error) => {
          const errorMessage = this._getErrorMessage(error);
          console.error('❌ [CentralizedSocketManager] Connection error:', errorMessage);

          // Clear connection timeout since we got a definitive error
          if (connectionTimeoutId) {
            clearTimeout(connectionTimeoutId);
            connectionTimeoutId = null;
          }

          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setDisconnected(errorMessage);

          // Enhanced authentication error detection
          const errorMessageLower = errorMessage.toLowerCase();
          const isAuthError = this.isAuthenticationError ||
                             errorMessageLower.includes('unauthorized') ||
                             errorMessageLower.includes('invalid') ||
                             errorMessageLower.includes('token') ||
                             errorMessageLower.includes('authentication') ||
                             errorMessageLower.includes('forbidden') ||
                             errorMessageLower.includes('expired');

          if (isAuthError) {
            this.authErrorCount++;
            this.isAuthenticationError = true;
            console.log(`[CentralizedSocketManager] Authentication error detected (${this.authErrorCount}/${this.maxAuthErrors})`);

            // Try to refresh token on first auth error
            if (this.authErrorCount === 1) {
              console.log('[CentralizedSocketManager] Attempting token refresh...');
              try {
                const authStore = useAuthStore();
                await authStore.refreshToken();
                console.log('[CentralizedSocketManager] Token refreshed successfully, retrying connection...');
                
                // Reset auth error state and retry
                this.resetAuthErrorState();
                setTimeout(() => {
                  this.initializeSocket().catch(console.error);
                }, 1000);
                return;
              } catch (refreshError) {
                console.error('[CentralizedSocketManager] Token refresh failed:', refreshError);
                // Continue with normal auth error handling
              }
            }

            if (this.authErrorCount >= this.maxAuthErrors) {
              console.log('[CentralizedSocketManager] Max auth errors reached, stopping reconnection attempts');
              this._notifyHandlers('connect_error', error);
              reject(error);
              return;
            }
          }

          // Network error or recoverable auth error - schedule retry if user is still authenticated
          const authStore = useAuthStore();
          if (authStore.isAuthenticated && navigator.onLine && !this.isAuthenticationError) {
            console.log('[CentralizedSocketManager] Network error detected, scheduling retry...');
            this.scheduleReconnection();
          }

          this._notifyHandlers('connect_error', error);
          reject(error);
        });

        this.socket.on('reconnect_attempt', () => {
          console.log('🔄 [CentralizedSocketManager] Reconnection attempt...');
          
          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setReconnecting();
        });

        this.socket.on('reconnect', (attemptNumber: number) => {
          console.log(`✅ [CentralizedSocketManager] Reconnected after ${attemptNumber} attempts`);
          
          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setConnected(true);
          connectionStore.resetReconnectAttempts();
        });

        this.socket.on('reconnect_failed', () => {
          console.error('❌ [CentralizedSocketManager] Failed to reconnect after maximum attempts');
          
          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setDisconnected('reconnect_failed');
          
          // Schedule a manual reconnection attempt after a longer delay
          this.scheduleReconnection();
        });

        // Set up application event listeners
        this._setupApplicationEventListeners();

        // Connection timeout - matches socket timeout
        const connectionTimeout = 25000; // 25 seconds (5 seconds more than socket timeout)
        connectionTimeoutId = window.setTimeout(() => {
          if (!this.socket?.connected) {
            console.error('⏰ [CentralizedSocketManager] Connection timeout after 25 seconds');

            // Check if we're online before treating this as a connection failure
            if (!navigator.onLine) {
              console.log('[CentralizedSocketManager] Device is offline, timeout expected');
              const connectionStore = useConnectionStore();
              connectionStore.setDisconnected('offline');
            }

            if (this.socket) {
              this.socket.disconnect();
              this.socket = null;
            }
            reject(new Error('Socket connection timeout'));
          }
        }, connectionTimeout);

      } catch (error) {
        console.error('💥 [CentralizedSocketManager] Error creating socket:', error);
        reject(error);
      }
    });
  }

  private _setupApplicationEventListeners(): void {
    if (!this.socket) return;

    console.log('🎯 [CentralizedSocketManager] Setting up application event listeners');

    // Interest events
    this.socket.on(INTEREST_RECEIVED, (payload: InterestReceivedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received INTEREST_RECEIVED:', payload);
      this._notifyHandlers(INTEREST_RECEIVED, payload);
    });

    this.socket.on(INTEREST_PROCESSED, (payload: InterestProcessedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received INTEREST_PROCESSED:', payload);
      this._notifyHandlers(INTEREST_PROCESSED, payload);
    });

    this.socket.on(INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY, (payload: InterestRequestAcceptedAndChatReadyPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY:', payload);
      this._notifyHandlers(INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY, payload);
    });

    this.socket.on(INTEREST_REQUEST_DECLINED, (payload: YourInterestDeclinedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received INTEREST_REQUEST_DECLINED:', payload);
      this._notifyHandlers(INTEREST_REQUEST_DECLINED, payload);
    });

    // Offer events
    this.socket.on(OFFER_CREATED, (payload: OfferCreatedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received OFFER_CREATED:', payload);
      this._notifyHandlers(OFFER_CREATED, payload);
    });

    this.socket.on(OFFER_UPDATED, (payload: any) => {
      console.log('🔔 [CentralizedSocketManager] Received OFFER_UPDATED:', payload);
      this._notifyHandlers(OFFER_UPDATED, payload);
    });

    this.socket.on(OFFER_STATUS_CHANGED, (payload: any) => {
      console.log('🔔 [CentralizedSocketManager] Received OFFER_STATUS_CHANGED:', payload);
      this._notifyHandlers(OFFER_STATUS_CHANGED, payload);
    });

    // Chat events
    this.socket.on(CHAT_MESSAGE_RECEIVE, (payload: ChatMessageReceivePayload) => {
      console.log('🔔 [CentralizedSocketManager] Received CHAT_MESSAGE_RECEIVE:', payload);
      this._notifyHandlers(CHAT_MESSAGE_RECEIVE, payload);
    });    this.socket.on(SYSTEM_MESSAGE_RECEIVE, (payload: SystemMessagePayload) => {
      console.log('🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE:', payload);
      this._notifyHandlers(SYSTEM_MESSAGE_RECEIVE, payload);
    });

    // Transaction events
    this.socket.on(TRANSACTION_STATUS_UPDATED, (payload: TransactionStatusUpdatePayload) => {
      console.log('🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED:', payload);
      this._notifyHandlers(TRANSACTION_STATUS_UPDATED, payload);
    });

    // Payer negotiation events
    this.socket.on(NEGOTIATION_STATE_UPDATED, (payload: PayerNegotiationStatePayload) => {
      console.log('🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED:', payload);
      this._notifyHandlers(NEGOTIATION_STATE_UPDATED, payload);
    });    this.socket.on(NEGOTIATION_FINALIZED, (payload: NegotiationFinalizedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received NEGOTIATION_FINALIZED:', payload);
      this._notifyHandlers(NEGOTIATION_FINALIZED, payload);
    });

    // Notification events
    this.socket.on(NEW_NOTIFICATION, (payload: NewNotificationPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION:', payload);
      this._notifyHandlers(NEW_NOTIFICATION, payload);
    });

    // Match events
    this.socket.on(MATCH_FOUND, (payload: MatchFoundPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received MATCH_FOUND:', payload);
      this._notifyHandlers(MATCH_FOUND, payload);
    });

    this.socket.on(MATCH_ACCEPTED, (payload: MatchAcceptedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received MATCH_ACCEPTED:', payload);
      this._notifyHandlers(MATCH_ACCEPTED, payload);
    });

    this.socket.on(MATCH_DECLINED, (payload: MatchDeclinedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received MATCH_DECLINED:', payload);
      this._notifyHandlers(MATCH_DECLINED, payload);
    });

    this.socket.on(MATCH_EXPIRED, (payload: MatchExpiredPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received MATCH_EXPIRED:', payload);
      this._notifyHandlers(MATCH_EXPIRED, payload);
    });

    this.socket.on(MATCH_CONVERTED, (payload: MatchConvertedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received MATCH_CONVERTED:', payload);
      this._notifyHandlers(MATCH_CONVERTED, payload);
    });
  }  private _notifyHandlers<T extends keyof EventHandlers>(event: T, payload: any): void {
    const handlers = this.handlers[event];
    
    if (handlers && handlers.length > 0) {
      if (DEBUG_SOCKET_VERBOSE) {
        console.log(`🎯 [CentralizedSocketManager] Notifying ${handlers.length} handlers for event: ${event}`);
      }
      handlers.forEach(handler => {
        try {
          handler(payload);
        } catch (error) {
          console.error(`💥 [CentralizedSocketManager] Error in handler for ${event}:`, error);
        }
      });
    } else if (DEBUG_SOCKET_VERBOSE) {
      console.log(`⚠️ [CentralizedSocketManager] No handlers registered for event: ${event}`);
    }
  }

  // Emit events to server
  emit(event: string, payload: any): void {
    if (this.socket?.connected) {
      console.log(`📤 [CentralizedSocketManager] Emitting ${event}:`, payload);
      this.socket.emit(event, payload);
    } else {
      console.warn(`⚠️ [CentralizedSocketManager] Cannot emit ${event}: socket not connected`);
    }
  }
  // Disconnect socket
  disconnect(): void {
    if (this.socket) {
      console.log('🔌 [CentralizedSocketManager] Disconnecting socket');
      this.socket.disconnect();
      this.socket = null;
    }
    
    // Clear any reconnection timeout
    if (this.reconnectionTimeoutId) {
      clearTimeout(this.reconnectionTimeoutId);
      this.reconnectionTimeoutId = null;
    }
    
    // Clean up browser offline detection on disconnect
    this.cleanupBrowserOfflineDetection();
    
    // Clear handlers
    Object.keys(this.handlers).forEach(event => {
      this.handlers[event as keyof EventHandlers] = [];
    });
  }

  // Schedule reconnection with exponential backoff
  private scheduleReconnection() {
    // Clear any existing reconnection timeout
    if (this.reconnectionTimeoutId) {
      clearTimeout(this.reconnectionTimeoutId);
    }

    const authStore = useAuthStore();
    if (!authStore.isAuthenticated) {
      console.log('[CentralizedSocketManager] User not authenticated, skipping reconnection');
      return;
    }

    // Don't retry if we're in an authentication error state
    if (this.isAuthenticationError) {
      console.log('[CentralizedSocketManager] In authentication error state, skipping reconnection');
      return;
    }

    // Don't retry if we're offline
    if (!navigator.onLine) {
      console.log('[CentralizedSocketManager] Device offline, skipping reconnection');
      return;
    }

    // Calculate delay with exponential backoff (capped at 30 seconds)
    const baseDelay = 3000; // 3 seconds
    const maxDelay = 30000; // 30 seconds
    const exponentialDelay = Math.min(baseDelay * Math.pow(2, this.authErrorCount), maxDelay);
    const reconnectDelay = exponentialDelay;

    console.log(`[CentralizedSocketManager] Scheduling reconnection in ${reconnectDelay}ms (attempt ${this.authErrorCount + 1})`);

    this.reconnectionTimeoutId = window.setTimeout(() => {
      if (!this.isConnected() && navigator.onLine && authStore.isAuthenticated && !this.isAuthenticationError) {
        console.log('[CentralizedSocketManager] Attempting scheduled reconnection...');
        this.initializeSocket().catch(error => {
          console.error('[CentralizedSocketManager] Scheduled reconnection failed:', error);
        });
      }
    }, reconnectDelay);
  }  // Force reconnection manually (can be called from UI)
  forceReconnect(): Promise<Socket> {
    console.log('🔄 [CentralizedSocketManager] Force reconnection requested');

    // Reset authentication error state for manual reconnection
    this.resetAuthErrorState();

    // Disconnect existing socket if any
    this.forceDisconnect();

    // Clear any existing initialization promise
    this.initializationPromise = null;

    // Clear any reconnection timeout
    if (this.reconnectionTimeoutId) {
      clearTimeout(this.reconnectionTimeoutId);
      this.reconnectionTimeoutId = null;
    }    // Ensure browser offline detection is active for reconnection
    if (!this.isOnlineListenerAdded) {
      console.log('🔄 [CentralizedSocketManager] Re-setting up browser offline detection during force reconnect');
      this.setupBrowserOfflineDetection();
    }

    // Attempt fresh connection
    return this.initializeSocket();
  }
  // Helper method to force disconnect and cleanup resources
  private forceDisconnect(): void {
    if (this.socket) {
      console.log('🔌 [CentralizedSocketManager] Force disconnecting and cleaning up socket');
      this.socket.removeAllListeners();
      this.socket.disconnect();
      this.socket = null;
      
      // Only update connection status if we actually disconnected a socket
      const connectionStore = useConnectionStore();
      connectionStore.setDisconnected('force_disconnect');
    }
  }
  // Reset authentication error state
  private resetAuthErrorState(): void {
    this.authErrorCount = 0;
    this.isAuthenticationError = false;
    console.log('[CentralizedSocketManager] Authentication error state reset');
  }

  // Safely extract error message from error object
  private _getErrorMessage(error: unknown): string {
    if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string') {
      return error.message;
    }
    return String(error || 'Unknown error');
  }

  // Check if we're in an authentication error state
  isInAuthErrorState(): boolean {
    return this.isAuthenticationError;
  }

  // Check if socket is ready for event handlers
  isReady(): boolean {
    return this.socket?.connected || false;
  }

  // Wait for socket to be ready
  async waitForReady(timeoutMs: number = 10000): Promise<boolean> {
    if (this.isReady()) {
      return true;
    }

    return new Promise((resolve) => {
      const startTime = Date.now();
      const checkInterval = setInterval(() => {
        if (this.isReady()) {
          clearInterval(checkInterval);
          resolve(true);
        } else if (Date.now() - startTime > timeoutMs) {
          clearInterval(checkInterval);
          resolve(false);
        }
      }, 100);
    });
  }

  // Test method to simulate offline/online cycle for debugging
  debugTestOfflineOnline() {
    console.log('🧪 [CentralizedSocketManager] Testing offline/online cycle...');
      // Simulate going offline
    this.handleOffline();
    
    // Wait 2 seconds then simulate coming back online
    setTimeout(() => {
      console.log('🧪 [CentralizedSocketManager] Simulating back online...');
      this.handleOnline();
    }, 2000);
  }

  /**
   * Initialize notification system after socket connection is established
   * This ensures notification handlers are always registered
   */
  private _initializeNotificationSystemAfterConnection(): void {
    console.log('🔔 [CentralizedSocketManager] Initializing notification system after socket connection');
    
    try {
      // Dynamically import and initialize notification store
      // We use dynamic import to avoid circular dependencies
      import('../stores/notificationStore').then((module) => {
        const { useNotificationStore } = module;
        const notificationStore = useNotificationStore();
        
        // Check if notification system is already initialized
        if (notificationStore.isInitialized) {
          console.log('🔔 [CentralizedSocketManager] Notification system already initialized, skipping');
          return;
        }
        
        console.log('🔔 [CentralizedSocketManager] Calling notification store initialization...');
        notificationStore.initializeNotificationSystem();
      }).catch((error) => {
        console.error('❌ [CentralizedSocketManager] Failed to initialize notification system:', error);
      });
    } catch (error) {
      console.error('❌ [CentralizedSocketManager] Error during notification system initialization:', error);
    }
  }
}

// Create singleton instance
const centralizedSocketManager = new CentralizedSocketManager();

// Expose test method globally for debugging in development
if (import.meta.env.DEV) {
  (window as any).testSocketReconnect = () => centralizedSocketManager.debugTestOfflineOnline();
  (window as any).forceSocketReconnect = () => centralizedSocketManager.forceReconnect();
  (window as any).socketManager = centralizedSocketManager;
}

export default centralizedSocketManager;
