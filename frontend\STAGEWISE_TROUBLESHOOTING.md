# Stagewise Toolbar Troubleshooting Guide

## Overview
The Stagewise toolbar provides AI-powered editing capabilities through a browser toolbar. This guide helps resolve common connection issues.

## Common Issues and Solutions

### 1. WebSocket Connection Errors
**Symptoms:** 
- "Max reconnection attempts reached"
- "Failed to get session info from port 5746"
- "WebSocket closed intentionally, not attempting to reconnect"

**Solutions:**
1. **Disable temporarily**: Create a `.env.local` file in the frontend directory:
   ```
   VITE_DISABLE_STAGEWISE=true
   ```

2. **Check Stagewise server**: The toolbar needs the Stagewise development server running. If you don't have it set up, the toolbar will show connection errors.

3. **Restart development server**: Sometimes restarting `npm run dev` resolves connection issues.

### 2. Vue Detection Issues
**Symptoms:**
- "[stagewise/vue] No Vue installation detected on the selected element"

**Solutions:**
1. The component now includes better Vue detection logic
2. Make sure you're running in development mode (`npm run dev`)
3. Try refreshing the browser after the dev server fully loads

### 3. Port Conflicts
**Symptoms:**
- Connection refused on port 5746
- Port already in use errors

**Solutions:**
1. Check if another process is using port 5746
2. The configuration includes reduced reconnection attempts to minimize spam

## Configuration Options

### Disable Stagewise Completely
Create `.env.local` file:
```
VITE_DISABLE_STAGEWISE=true
```

### Modify Configuration
Edit `src/utils/stagewise.ts` to adjust:
- Connection timeout
- Reconnection attempts
- Port (if needed)

## Best Practices

1. **Development Only**: The toolbar only appears in development mode
2. **Error Handling**: The component automatically disables after too many connection errors
3. **Performance**: Minimal impact on production builds (completely excluded)

## If Issues Persist

1. Check browser console for specific error messages
2. Ensure you're in development mode
3. Try disabling temporarily with environment variable
4. Contact the development team if problems continue

## Integration Status

✅ Packages installed: `@stagewise/toolbar-vue`, `@stagewise-plugins/vue`
✅ Vue plugin configured
✅ Development-only rendering
✅ Error handling implemented
✅ Environment variable control available
