import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Finding users with missing createdAt...');
      // Check all users in the database and update those with issues
    // Since we can't filter by null directly in Prisma, we'll fetch all and filter manually
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        createdAt: true,
      }
    });
      // Filter out users that have a valid createdAt value
    const usersToUpdate = allUsers.filter(user => 
      !user.createdAt || user.createdAt.toString() === 'Invalid Date'
    );
    
    console.log(`Found ${usersToUpdate.length} users with missing createdAt`);
    
    // Set a default creation date (you might want to adjust this)
    // Here we're using a recent date since these are presumably existing users
    const defaultDate = new Date();
    
    // Update all users with missing createdAt
    if (usersToUpdate.length > 0) {
      for (const user of usersToUpdate) {
        console.log(`Updating user ${user.id} (${user.email})...`);
        
        await prisma.user.update({
          where: { id: user.id },
          data: {
            createdAt: defaultDate,
            updatedAt: defaultDate
          }
        });
      }
      
      console.log('All users updated successfully');
    }
    
  } catch (error) {
    console.error('Error updating users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('Migration completed successfully.'))
  .catch(e => {
    console.error('Migration failed:', e);
    process.exit(1);
  });
