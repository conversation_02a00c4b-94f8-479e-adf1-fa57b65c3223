import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createWorkingTimerTest() {
  try {
    console.log('🧪 Creating timer test scenario...');

    // Clean up any existing test data
    console.log('Cleaning up existing test data...');
    await prisma.transaction.deleteMany({
      where: {
        OR: [
          { currencyAProvider: { email: { contains: 'timer_test' } } },
          { currencyBProvider: { email: { contains: 'timer_test' } } }
        ]
      }
    });
    
    await prisma.chatSession.deleteMany({
      where: {
        OR: [
          { userOne: { email: { contains: 'timer_test' } } },
          { userTwo: { email: { contains: 'timer_test' } } }
        ]
      }
    });
    
    await prisma.interest.deleteMany({
      where: {
        interestedUser: { email: { contains: 'timer_test' } }
      }
    });
    
    await prisma.offer.deleteMany({
      where: {
        user: { email: { contains: 'timer_test' } }
      }
    });
    
    await prisma.user.deleteMany({
      where: {
        email: { contains: 'timer_test' }
      }
    });

    // Create test users
    console.log('Creating test users...');
    const userA = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword123',
        emailVerified: true,
        phoneNumber: '+**********',
        phoneVerified: true,
        username: 'TimerTestUserA',
        reputationScore: 100,
        reputationLevel: 3
      }
    });

    const userB = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword456',
        emailVerified: true,
        phoneNumber: '+1234567891',
        phoneVerified: true,
        username: 'TimerTestUserB',
        reputationScore: 150,
        reputationLevel: 4
      }
    });

    console.log(`✅ Created users: ${userA.id} and ${userB.id}`);

    // Create an offer from user A
    console.log('Creating test offer...');
    const offer = await prisma.offer.create({
      data: {
        userId: userA.id,
        type: 'SELL',
        currencyPair: 'CAD-IRR',
        amount: 1000,
        baseRate: 35000,
        adjustmentForLowerRep: 500,
        adjustmentForHigherRep: -200,
        status: 'ACTIVE'
      }
    });

    console.log(`✅ Created offer: ${offer.id}`);

    // User B shows interest
    console.log('Creating interest...');
    const interest = await prisma.interest.create({
      data: {
        offerId: offer.id,
        interestedUserId: userB.id,
        status: 'ACCEPTED'
      }
    });

    console.log(`✅ Created interest: ${interest.id}`);

    // Create chat session
    console.log('Creating chat session...');
    const chatSession = await prisma.chatSession.create({
      data: {
        offerId: offer.id,
        userOneId: userA.id, // Offer creator
        userTwoId: userB.id, // Interested user
        interestId: interest.id
      }
    });

    console.log(`✅ Created chat session: ${chatSession.id}`);

    // Create transaction in AWAITING_FIRST_PAYER_PAYMENT status
    // This will trigger the countdown timer
    console.log('Creating transaction with timer...');
    
    const now = new Date();
    const paymentDueDate = new Date(now.getTime() + (2 * 60 * 60 * 1000)); // 2 hours from now
    
    const transaction = await prisma.transaction.create({
      data: {
        chatSessionId: chatSession.id,
        offerId: offer.id,
        currencyA: 'CAD',
        amountA: 1000,
        currencyAProviderId: userA.id,
        currencyB: 'IRR',
        amountB: 35000000, // 1000 * 35000
        currencyBProviderId: userB.id,
        status: 'AWAITING_FIRST_PAYER_PAYMENT',
        agreedFirstPayerId: userB.id, // User B pays first
        firstPayerDesignationTimestamp: now,
        paymentExpectedByPayer1: paymentDueDate
      }
    });

    console.log(`✅ Created transaction: ${transaction.id}`);
    console.log(`⏰ Payment due date: ${paymentDueDate.toISOString()}`);
    console.log(`👤 First payer: ${userB.username} (${userB.id})`);

    // Add a system message to the chat
    await prisma.chatMessage.create({
      data: {
        chatSessionId: chatSession.id,
        content: `Timer test transaction created. ${userB.username} has 2 hours to make the first payment.`,
        isSystemMessage: true,
        transactionId: transaction.id
      }
    });

    console.log('\n🎉 Timer test scenario created successfully!');
    console.log('\n📋 Test Details:');
    console.log(`- User A (Offer Creator): ${userA.username} (${userA.email})`);
    console.log(`- User B (First Payer): ${userB.username} (${userB.email})`);
    console.log(`- Chat Session ID: ${chatSession.id}`);
    console.log(`- Transaction ID: ${transaction.id}`);
    console.log(`- Payment Due: ${paymentDueDate.toISOString()}`);
    console.log('\n🔗 Test URLs:');
    console.log(`- Login as User A: http://localhost:5174/login`);
    console.log(`- Login as User B: http://localhost:5174/login`);
    console.log(`- Chat Session: After login, navigate to chat or offers page`);
    console.log('\n⚠️  Note: Use the email and password "password123" to login');

    return {
      userA,
      userB,
      offer,
      interest,
      chatSession,
      transaction
    };

  } catch (error) {
    console.error('❌ Error creating timer test scenario:', error);
    throw error;
  }
}

async function main() {
  try {
    await createWorkingTimerTest();
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export { createWorkingTimerTest };
