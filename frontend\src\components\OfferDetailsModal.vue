<template>
  <n-modal
    v-model:show="isModalVisible"
    preset="card"
    :style="{ width: '600px' }"
    :title="modalTitle"
    :bordered="false"
    size="huge"
    transform-origin="center"
    @close="handleClose"
  >
    <div v-if="currentlyViewedOffer">      <!-- Loading State -->
      <div v-if="currentlyViewedOffer.isLoading" class="loading-state">
        <n-spin size="large" />
        <p>{{ t('offerDetails.loadingOfferDetails') }}</p>
      </div>

      <!-- Error State -->
      <n-alert
        v-else-if="currentlyViewedOffer.fetchError"
        type="error"
        :title="t('offerDetails.errorLoadingOffer')"
        :description="currentlyViewedOffer.fetchError"
      />

      <!-- Inactive State -->
      <n-alert
        v-else-if="currentlyViewedOffer.isNoLongerActive"
        type="warning"
        :title="inactiveTitle"
      >
        {{ inactiveMessage }}
      </n-alert>

      <!-- Offer Content -->
      <div v-else class="offer-content">        <n-descriptions :column="1" bordered>
          <n-descriptions-item :label="t('offerDetails.type')">
            {{ currentlyViewedOffer.type === 'BUY' ? t('app.buy') : t('app.sell') }}
          </n-descriptions-item>
          <n-descriptions-item :label="t('offerDetails.amount')">
            {{ formatAmount(currentlyViewedOffer.amount) }} CAD
          </n-descriptions-item>
          <n-descriptions-item :label="t('offerDetails.baseRate')">
            {{ formatRate(currentlyViewedOffer.baseRate) }} IRR/CAD
          </n-descriptions-item>
          <n-descriptions-item :label="t('offerDetails.yourRate')">
            <n-number-animation
              :from="currentlyViewedOffer.baseRate"
              :to="currentlyViewedOffer.calculatedApplicableRate"
              :duration="1000"
              :precision="2"
            />
            IRR/CAD
          </n-descriptions-item>
        </n-descriptions>

        <!-- Rate Advantage Alert -->
        <n-alert
          v-if="rateAdvantageInfo.show"
          class="mt-4"
          :type="rateAdvantageInfo.type"
          :title="rateAdvantageInfo.title"
        >
          {{ rateAdvantageInfo.message }}
        </n-alert>

        <!-- Creator Info -->        <div class="creator-info mt-4">
          <n-descriptions :column="1" label-placement="left">
            <n-descriptions-item :label="t('offerDetails.createdBy')">
              {{ currentlyViewedOffer.offerCreatorUsername }}
            </n-descriptions-item>            <n-descriptions-item :label="t('offerDetails.reputationLevel')">
              <n-tag :type="getReputationTagType(currentlyViewedOffer.offerCreatorReputationLevel)">
                {{ t('offerDetails.level') }} {{ currentlyViewedOffer.offerCreatorReputationLevel }}
              </n-tag>
            </n-descriptions-item>
          </n-descriptions>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons mt-4">          <!-- Phone verification warning -->
          <n-alert
            v-if="!user?.phoneVerified"
            type="warning"
            :show-icon="true"
            class="mb-3"
          >
            <template #header>{{ t('offerDetails.phoneVerificationRequired') }}</template>
            <div class="flex flex-col gap-2">
              <p>{{ t('offerDetails.phoneVerificationMessage') }}</p>
              <n-button 
                size="small" 
                type="primary"
                @click="goToProfile"
              >
                {{ t('offerDetails.goToProfileToVerify') }}
              </n-button>
            </div>
          </n-alert>
          
          <n-button
            type="primary"
            size="large"
            block
            :disabled="isInterestButtonDisabled"
            :loading="isShowingInterest"
            @click="handleShowInterest"
          >
            {{ interestButtonText }}
          </n-button>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { computed, ref, onUnmounted, onMounted, watch } from 'vue';
import {
  NModal,
  NButton,
  NSpin,
  NAlert,
  NDescriptions,
  NDescriptionsItem,
  NTag,
  NNumberAnimation,
  useMessage,
} from 'naive-ui';
import { useOfferStore } from '../stores/offerStore';
import { useAuthStore } from '../stores/auth';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import type { OfferStatusFrontend } from '../types/offer'; // Correct import for type usage
import { offerService } from '../services/offerService';
import { EventBus, EventTypes } from '../services/eventBus';
import { formatAmount as formatCurrencyAmount, formatAmountForDisplay } from '@/utils/currencyUtils';
import { useI18n } from 'vue-i18n';

const props = defineProps<{
  show: boolean
}>();

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void
}>();

const offerStore = useOfferStore();
const authStore = useAuthStore();
const router = useRouter();
const { t } = useI18n();
const { currentlyViewedOfferDetails } = storeToRefs(offerStore);
const { user } = storeToRefs(authStore);
const message = useMessage();
const isShowingInterest = ref(false);

let removeStatusChangedListener: (() => void) | null = null;
let removeUpdatedListener: (() => void) | null = null;
let removeInterestShownListener: (() => void) | null = null;

onMounted(() => {
  removeStatusChangedListener = EventBus.on(EventTypes.OFFER_STATUS_CHANGED, ({ status }) => {
    if (status !== 'ACTIVE') { // Use string literal for comparison
      message.warning(t('offerDetails.offerNoLongerActive', { status: status.toLowerCase() }));
    }
  });

  removeUpdatedListener = EventBus.on(EventTypes.OFFER_UPDATED, () => {
    message.info(t('offerDetails.offerDetailsUpdated'));
  });

  removeInterestShownListener = EventBus.on(EventTypes.OFFER_INTEREST_SHOWN, (payload?: { offerId?: string }) => {
    message.success(t('offerDetails.interestShownSuccessfully'));
    if (payload?.offerId) {
      if (currentlyViewedOffer.value && currentlyViewedOffer.value.id === payload.offerId) {
        currentlyViewedOffer.value.currentUserHasShownInterest = true;
      }
      const offerInList = offerStore.offers.find(o => o.id === payload.offerId);
      if (offerInList) {
        offerInList.currentUserHasShownInterest = true;
      }
    }
  });
});

onUnmounted(() => {
  removeStatusChangedListener?.();
  removeUpdatedListener?.();
  removeInterestShownListener?.();
  offerStore.clearViewedOfferDetails();
});

const isModalVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

const currentlyViewedOffer = computed(() => currentlyViewedOfferDetails.value);

const modalTitle = computed(() => {
  if (!currentlyViewedOffer.value) return t('offerDetails.title');
  if (currentlyViewedOffer.value.isLoading) return t('offerDetails.loading');
  if (currentlyViewedOffer.value.isNoLongerActive) return t('offerDetails.offerUnavailable');
  const translatedType = currentlyViewedOffer.value.type === 'BUY' ? t('app.buy') : t('app.sell');
  return `${translatedType} ${formatAmount(currentlyViewedOffer.value.amount)} CAD`;
});

const inactiveTitle = computed(() => {
  if (!currentlyViewedOffer.value) return t('offerDetails.offerUnavailable');
  switch (currentlyViewedOffer.value.status as OfferStatusFrontend) { // Cast to type for switch
    case 'CANCELLED':
      return t('offerDetails.offerCancelled');
    case 'COMPLETED':
      return t('offerDetails.offerCompleted');
    case 'DEACTIVATED':
      return t('offerDetails.offerDeactivated');
    default:
      return t('offerDetails.offerNoLongerAvailable');
  }
});

const inactiveMessage = computed(() => {
  if (!currentlyViewedOffer.value) return '';
  switch (currentlyViewedOffer.value.status as OfferStatusFrontend) { // Cast to type for switch
    case 'CANCELLED':
      return t('offerDetails.offerCancelledMessage');
    case 'COMPLETED':
      return t('offerDetails.offerCompletedMessage');
    case 'DEACTIVATED':
      return t('offerDetails.offerDeactivatedMessage');
    default:
      return t('offerDetails.offerNotAvailableMessage');
  }
});

const rateAdvantageInfo = computed(() => {
  if (!currentlyViewedOffer.value || currentlyViewedOffer.value.isNoLongerActive) {
    return { show: false, type: 'info' as const, title: '', message: '' };
  }

  const offer = currentlyViewedOffer.value;
  const baseRate = offer.baseRate;
  const calculatedRate = offer.calculatedApplicableRate;
  const rateDiff = calculatedRate - baseRate;
  if (Math.abs(rateDiff) < 0.00001) {
    return {
      show: true,
      type: 'info' as const,
      title: t('offerDetails.baseRateApplies'),
      message: t('offerDetails.baseRateMessage')
    };
  }
  const isBuy = offer.type === 'BUY';
  const isBonus = isBuy ? rateDiff > 0 : rateDiff < 0;
  const formattedAbsDiff = formatAmountForDisplay(Math.abs(rateDiff), 'IRR', true);
  let title: string;
  let type: 'success' | 'warning' | 'info';
  let alertMessage: string;

  if (!isBonus) {
    title = t('offerDetails.rateAdjustment');
    type = 'warning';
    alertMessage = t('offerDetails.rateAdjustmentMessage', { amount: formattedAbsDiff });
  } else {
    title = t('offerDetails.rateAdvantage');
    type = 'success';
    alertMessage = t('offerDetails.rateAdvantageMessage', { amount: formattedAbsDiff });
  }

  return { show: true, type, title, message: alertMessage };
});

const isInterestButtonDisabled = computed(() => {
  if (!currentlyViewedOffer.value) return true;
  return (
    currentlyViewedOffer.value.isNoLongerActive ||
    currentlyViewedOffer.value.status !== 'ACTIVE' || // Use string literal for comparison
    currentlyViewedOffer.value.currentUserHasShownInterest ||
    isShowingInterest.value ||
    !user.value?.phoneVerified  // Disable if phone not verified
  );
});

const interestButtonText = computed(() => {
  if (!currentlyViewedOffer.value) return t('offerDetails.showInterest');
  if (currentlyViewedOffer.value.currentUserHasShownInterest) return t('offerDetails.interestAlreadyShown');
  if (isShowingInterest.value) return t('offerDetails.showingInterest');
  if (!user.value?.phoneVerified) return t('offerDetails.verifyPhoneToShowInterest');
  return t('offerDetails.showInterest');
});

function formatAmount(amount: number): string {
  return formatCurrencyAmount(amount, 'CAD');
}

function formatRate(rate: number): string {
  return new Intl.NumberFormat('en-CA', { 
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(rate);
}

function getReputationTagType(level: number | null): 'default' | 'success' | 'warning' | 'error' {
  if (!level) return 'default';
  if (level >= 4) return 'success';
  if (level >= 2) return 'warning';
  return 'error';
}

async function handleShowInterest() {
  if (!currentlyViewedOffer.value?.id) return;
  
  // Double-check phone verification before making the request
  if (!user.value?.phoneVerified) {
    message.warning(t('offerDetails.pleaseVerifyPhoneFirst'));
    return;
  }

  isShowingInterest.value = true;
  try {
    await offerService.showInterest(currentlyViewedOffer.value.id);
    EventBus.emit(EventTypes.OFFER_INTEREST_SHOWN, { offerId: currentlyViewedOffer.value.id });
    if (currentlyViewedOffer.value) {
      currentlyViewedOffer.value.currentUserHasShownInterest = true;
    }
  } catch (error: any) {
    if (error?.response?.status === 409) {
      message.info(t('offerDetails.alreadyShownInterest'));
      if (currentlyViewedOffer.value) {
        currentlyViewedOffer.value.currentUserHasShownInterest = true;
      }
    } else if (error?.response?.status === 403) {
      message.warning(t('offerDetails.phoneVerificationRequiredToShow'));
    } else {
      message.error(error?.message || t('offerDetails.failedToShowInterest'));
    }
  } finally {
    isShowingInterest.value = false;
  }
}

function handleClose() {
  emit('update:show', false);
}

function goToProfile() {
  handleClose(); // Close the modal first
  router.push('/profile'); // Navigate to profile page
}

watch(() => props.show, (newValue) => {
  if (newValue && currentlyViewedOffer.value?.id) {
    const mainListOffer = offerStore.offers.find(o => o.id === currentlyViewedOffer.value?.id);
    if (mainListOffer && mainListOffer.currentUserHasShownInterest) {
      if(currentlyViewedOffer.value) { // Check if currentlyViewedOffer.value is not null
         currentlyViewedOffer.value.currentUserHasShownInterest = true;
      }
    }
  }
});
</script>

<style scoped>
.mt-4 {
  margin-top: 1rem;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.creator-info {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 1rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}
</style>
