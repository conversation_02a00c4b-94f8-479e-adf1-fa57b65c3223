import { computed, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { TransactionStatusEnum, type Transaction } from '@/types/transaction'

/**
 * Options for the useTimerDisplay composable
 */
interface UseTimerDisplayOptions {
  /** The current transaction as a reactive reference */
  currentTransaction: Ref<Transaction | null>
  /** The time left string (e.g., "02:30:15" or "+01:15:30") */
  timeLeft: Ref<string>
  /** Whether the timer is in critical state (red) */
  isTimerCritical: Ref<boolean>
  /** Whether the timer has expired */
  isTimerExpired: Ref<boolean>
  /** Whether this is an elapsed timer (counting up) */
  isElapsedTimer: Ref<boolean>
}

/**
 * Shared composable for timer display logic across transaction components.
 * 
 * This composable centralizes the logic for:
 * - Determining when to show the timer
 * - Calculating timer color classes based on state
 * - Generating appropriate timer labels
 * 
 * @param options - The timer state options from useTransactionFlowLogic
 * @returns Computed properties for timer display
 */
export function useTimerDisplay({
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
}: UseTimerDisplayOptions) {
  const { t } = useI18n()

  // Timer display value logic - when to show the timer
  const timerDisplayValue = computed(() => {
    const tx = currentTransaction.value
    if (!tx) return null

    // Define the statuses where the timer should be visible
    const showTimerForStatuses = [
      TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT,
      TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION,
      TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT,
      TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION
    ]

    return showTimerForStatuses.includes(tx.status) ? timeLeft.value : null
  })

  // Timer color class based on state
  const timerColorClass = computed(() => {
    if (isTimerExpired.value) return 'timer-expired'
    if (isElapsedTimer.value) return 'timer-elapsed'
    if (isTimerCritical.value) return 'timer-critical'
    
    // For elapsed timers during confirmation, use time-based colors
    const tx = currentTransaction.value
    if (tx && (tx.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION || 
               tx.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION)) {
      
      // Parse time left to determine color (for elapsed timer, this shows +HH:MM format)
      const timeStr = timeLeft.value
      if (timeStr && timeStr.startsWith('+')) {
        // Extract elapsed time from +HH:MM:SS format
        const timeParts = timeStr.substring(1).split(':')
        if (timeParts.length >= 2) {
          const [hours, minutes] = timeParts.map(Number)
          const totalMinutes = hours * 60 + minutes
          
          if (totalMinutes < 30) return 'timer-plenty' // Less than 30 minutes elapsed - green
          if (totalMinutes < 60) return 'timer-moderate' // 30-60 minutes elapsed - yellow
          if (totalMinutes < 120) return 'timer-warning' // 1-2 hours elapsed - orange
          // More than 2 hours elapsed is handled by isTimerCritical (red)
        }
      }
    }
    
    return 'timer-default'
  })

  // Timer label logic
  const timerLabel = computed(() => {
    const tx = currentTransaction.value
    if (!tx) return ''
    
    if (tx.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT || 
        tx.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT) {
      if (isElapsedTimer.value) {
        return t('transactionFlow.timer.timeElapsed')
      } else {
        return t('transactionFlow.timer.paymentWindow')
      }
    } else if (tx.status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION || 
               tx.status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION) {
      return t('transactionFlow.timer.timeElapsed')
    }
    
    return t('transactionFlow.timer.timeRemaining')
  })

  return {
    timerDisplayValue,
    timerColorClass,
    timerLabel
  }
}
