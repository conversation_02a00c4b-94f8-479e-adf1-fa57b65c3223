{"modal": {"title": "Payment Method Required", "description": "Add your {currency} receiving details for this transaction."}, "header": {"infoTitle": "Payment Information Required", "loadingMethods": "Loading payment methods..."}, "selectMode": {"title": "Your {currency} Payment Methods", "description": "Select an existing method or create a new one", "useSelectedMethod": "Use Selected Method", "addNewMethod": "Add New Method", "cancel": "Cancel"}, "createMode": {"title": "Add New {currency} Payment Method", "description": "Enter your payment receiving details"}, "editMode": {"title": "Edit Payment Method", "description": "Update your payment receiving details"}, "methodItem": {"default": "<PERSON><PERSON><PERSON>", "verified": "✓", "warning": "!", "missingFields": "Missing: {fields}", "edit": "Edit", "delete": "Delete"}, "noMethods": {"title": "No {currency} payment methods found", "description": "Create your first {currency} payment method to proceed", "addButton": "Add Payment Method"}, "actions": {"confirmSelection": "Use Selected Method", "createMethod": "Add Payment Method", "updateMethod": "Update Method", "deleteConfirm": "Delete Payment Method", "deleteWarning": "Are you sure you want to delete this payment method?\n\nBank: {bankName}\nAccount: {accountNumber}\n\nThis action cannot be undone.", "deleteButton": "Delete", "cancelButton": "Cancel"}, "messages": {"selectionSuccess": "Payment method selected successfully", "createSuccess": "Payment method created and selected successfully", "updateSuccess": "Payment method updated successfully", "deleteSuccess": "Payment method deleted successfully", "selectionError": "Failed to select payment method. Please try again.", "createError": "Failed to create payment method. Please try again.", "updateError": "Failed to update payment method. Please try again.", "deleteError": "Failed to delete payment method. Please try again."}, "form": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "currencyPlaceholder": "Payment details for {currency}", "bankName": "Bank Name", "bankNamePlaceholder": "Enter your bank name", "accountNumber": "Account Number", "accountNumberPlaceholder": "Enter your account number", "accountHolderName": "Name on Account", "accountHolderNamePlaceholder": "Enter the name as it appears on your account", "cancel": "Cancel", "create": "Create Payment Method", "update": "Update Payment Method", "validation": {"bankNameRequired": "Bank name is required", "bankNameLength": "Bank name must be between 1-100 characters", "accountNumberRequired": "Account number is required", "accountNumberLength": "Account number must be between 1-50 characters", "accountHolderNameRequired": "Name on account is required", "accountHolderNameLength": "Name on account must be between 1-100 characters"}}}