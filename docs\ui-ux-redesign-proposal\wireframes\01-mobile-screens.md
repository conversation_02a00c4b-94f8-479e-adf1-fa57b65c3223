# Mobile Wireframe Specifications

## 📱 Home Screen (Mobile-First Design)

### Landing Screen Wireframe
```
┌─────────────────────────────┐
│ ☰ MUNygo        🔔 EN 🌙   │ ← <PERSON><PERSON> (56px height)
├─────────────────────────────┤
│                             │
│     Welcome to MUNygo      │ ← Welcome message
│   Simplest currency exchange│ 
│                             │
│  ┌─────────────────────────┐│
│  │                         ││
│  │     💵 I NEED USD       ││ ← Primary action button
│  │                         ││ ← (56px height, full width)
│  └─────────────────────────┘│
│                             │
│  ┌─────────────────────────┐│
│  │                         ││
│  │     💷 I HAVE USD       ││ ← Secondary action button
│  │                         ││ ← (56px height, full width)
│  └─────────────────────────┘│
│                             │
│     Recent Activity         │
│  ┌─────────────────────────┐│
│  │ 👤 Ahmad wants $500     ││ ← Activity card
│  │ 🕐 2 hours ago          ││ ← (44px min height)
│  └─────────────────────────┘│
│  ┌─────────────────────────┐│
│  │ 👤 Sara has €200        ││
│  │ 🕐 5 hours ago          ││
│  └─────────────────────────┘│
│                             │
├─────────────────────────────┤
│ 💱    🔍    💬    👤      │ ← Bottom navigation
│Exchange Browse Chat Profile │ ← (60px height)
└─────────────────────────────┘
```

### Design Specifications
```css
/* Home Screen Styles */
.home-container {
  padding: 20px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: calc(100vh - 116px); /* Account for header + nav */
}

.welcome-section {
  text-align: center;
  margin-bottom: 32px;
  color: white;
}

.primary-action {
  background: #10B981;
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.secondary-action {
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 32px;
  border-radius: 12px;
}

.activity-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 8px;
  backdrop-filter: blur(10px);
}
```

## 💰 Amount Entry Screen

### "I Need USD" Amount Input
```
┌─────────────────────────────┐
│ ← I Need USD               │ ← Header with back button
├─────────────────────────────┤
│                             │
│                             │
│     How much USD?          │ ← Clear question
│                             │
│  ┌─────────────────────────┐│
│  │                         ││ ← Large input field
│  │       $500              ││ ← (80px height)
│  │                         ││ ← Large, readable text
│  └─────────────────────────┘│
│                             │
│     💱 Current Rate         │
│     1 USD = 82,700 ﷼       │ ← Auto-fetched rate
│     📊 From Bonbast         │ ← Source indicator
│                             │
│     You'll pay:             │
│     🏷️ 41,350,000 ﷼        │ ← Calculated amount
│                             │
│                             │
│  ┌─────────────────────────┐│
│  │                         ││
│  │    FIND SELLERS         ││ ← Primary action
│  │                         ││ ← (56px height)
│  └─────────────────────────┘│
│                             │
│  ┌─────────────────────────┐│
│  │    POST & WAIT          ││ ← Secondary action
│  └─────────────────────────┘│
│                             │
├─────────────────────────────┤
│ 💱    🔍    💬    👤      │
└─────────────────────────────┘
```

### Input Field Specifications
```css
.amount-input {
  width: 100%;
  height: 80px;
  font-size: 32px;
  font-weight: 700;
  text-align: center;
  border: 3px solid #E5E7EB;
  border-radius: 12px;
  background: white;
  color: #111827;
}

.amount-input:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.rate-display {
  text-align: center;
  padding: 20px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  margin: 20px 0;
}

.calculated-amount {
  font-size: 24px;
  font-weight: 700;
  color: #059669;
  text-align: center;
}
```

## 📋 Browse Offers Screen

### Available Offers List
```
┌─────────────────────────────┐
│ ← USD Sellers      🔍 ⚙️   │ ← Header with search/filter
├─────────────────────────────┤
│                             │
│  ┌─────────────────────────┐│
│  │ 👤 Ahmad        ⭐ 4.8  ││ ← Offer card
│  │ 💵 $500 ready           ││ ← (120px min height)
│  │ 💰 41,350,000 ﷼        ││
│  │ 📍 2km away    🕐 Online││
│  │ ┌─────────────────────┐ ││
│  │ │     CONNECT         │ ││ ← Action button
│  │ └─────────────────────┘ ││ ← (44px height)
│  └─────────────────────────┘│
│                             │
│  ┌─────────────────────────┐│
│  │ 👤 Sara         ⭐ 4.6  ││
│  │ 💵 $800 available       ││
│  │ 💰 66,160,000 ﷼        ││
│  │ 📍 5km away    🕐 1h ago││
│  │ ┌─────────────────────┐ ││
│  │ │     CONNECT         │ ││
│  │ └─────────────────────┘ ││
│  └─────────────────────────┘│
│                             │
│  ┌─────────────────────────┐│
│  │ 👤 Reza         ⭐ 4.2  ││
│  │ 💵 $1,200 available     ││
│  │ 💰 99,240,000 ﷼        ││
│  │ 📍 8km away    🕐 Online││
│  │ ┌─────────────────────┐ ││
│  │ │     CONNECT         │ ││
│  │ └─────────────────────┘ ││
│  └─────────────────────────┘│
│                             │
├─────────────────────────────┤
│ 💱    🔍    💬    👤      │
└─────────────────────────────┘
```

### Offer Card Component Specs
```css
.offer-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin: 8px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #E5E7EB;
}

.offer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #3B82F6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
}

.connect-button {
  background: #10B981;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 16px;
  width: 100%;
  margin-top: 12px;
}

.connect-button:active {
  background: #059669;
  transform: scale(0.98);
}
```

## 💬 Chat Interface

### Mobile Chat Screen
```
┌─────────────────────────────┐
│ ← Ahmad          • online   │ ← Chat header
├─────────────────────────────┤
│                             │
│ 👤 Ahmad:                   │ ← Message bubble
│ Hi! I have $500 ready.      │ ← (Auto-sizing)
│ Where should we meet?       │
│ 🕐 2:15 PM                  │
│                             │
│                      You: 👤│ ← Own message
│                  Valiasr    │ ← (Right-aligned)
│                  Subway?    │
│                 🕐 2:16 PM  │
│                             │
│ 👤 Ahmad:                   │
│ Perfect! 2pm tomorrow?      │
│ 🕐 2:17 PM                  │
│                             │
│ ┌─────────────────────────┐ │
│ │  💰 AGREE TO EXCHANGE   │ │ ← Transaction button
│ └─────────────────────────┘ │ ← (48px height)
│                             │
│                             │
│                             │
├─────────────────────────────┤
│ ┌─────────────────────┐ 📤 │ ← Input area
│ │  Type a message...  │ │  │ ← (44px min height)
│ └─────────────────────┘    │
└─────────────────────────────┘
```

### Chat Message Styling
```css
.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #F9FAFB;
}

.message {
  margin-bottom: 16px;
  max-width: 80%;
}

.message-own {
  margin-left: auto;
  text-align: right;
}

.message-bubble {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-own .message-bubble {
  background: #3B82F6;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #6B7280;
  margin-top: 4px;
}

.transaction-button {
  background: #F59E0B;
  color: white;
  font-weight: 600;
  margin: 16px;
  border-radius: 8px;
}
```

## ⚡ Transaction Flow Screens

### Payment Readiness Screen
```
┌─────────────────────────────┐
│ ← Transaction               │ ← Header
├─────────────────────────────┤
│                             │
│        🤝 Agreement         │
│                             │
│     💵 Amount: $500         │ ← Transaction details
│     💰 Price: 41,350,000﷼  │ ← (Centered layout)
│      Time: 2:00 PM        │
│                             │
│                             │
│     ✅ Ahmad ready          │ ← Status indicators
│     ⏳ Waiting for you      │
│                             │
│                             │
│     My payment info:        │
│     🏦 Melli Bank           │ ← Payment details
│     💳 1234-****-****      │
│                             │
│  ┌─────────────────────────┐│
│  │                         ││
│  │      I'M READY          ││ ← Primary action
│  │                         ││ ← (56px height)
│  └─────────────────────────┘│
│                             │
├─────────────────────────────┤
│ 💱    🔍    💬    👤      │
└─────────────────────────────┘
```

### Payment Declaration Screen
```
┌─────────────────────────────┐
│ ← Payment                   │
├─────────────────────────────┤
│                             │
│         💳 Payment          │
│                             │
│     ✅ You're ready         │ ← Status display
│     ✅ Ahmad ready          │
│                             │
│     Next step:              │
│     Transfer 41,350,000﷼   │ ← Clear instruction
│     to Ahmad's account      │
│                             │
│     Ahmad's details:        │
│     🏦 Tejarat Bank         │ ← Recipient info
│     💳 5678-9012-3456      │
│     👤 Ahmad Mohammadi      │
│                             │
│                             │
│  ┌─────────────────────────┐│
│  │                         ││
│  │     I TRANSFERRED       ││ ← Declaration button
│  │                         ││ ← (56px height)
│  └─────────────────────────┘│
│                             │
│     ℹ️ Only click after     │ ← Instruction
│       completing transfer   │
│                             │
├─────────────────────────────┤
│ 💱    🔍    💬    👤      │
└─────────────────────────────┘
```

## 🎉 Success & Completion Screens

### Transaction Complete Screen
```
┌─────────────────────────────┐
│ ← Complete                  │
├─────────────────────────────┤
│                             │
│                             │
│         ✅ Success!         │ ← Success icon
│                             │
│     🎉 Transaction          │
│        Complete!            │ ← Celebration message
│                             │
│     💵 You received         │
│        $500 USD             │ ← Transaction summary
│                             │
│     Thank you for using     │
│         MUNygo!             │
│                             │
│                             │
│       Rate Ahmad?           │
│     ⭐⭐⭐⭐⭐            │ ← Rating component
│                             │
│  ┌─────────────────────────┐│
│  │                         ││
│  │        DONE             ││ ← Completion button
│  │                         ││ ← (56px height)
│  └─────────────────────────┘│
│                             │
│     📱 Share your           │ ← Social sharing
│       experience?          │
│                             │
├─────────────────────────────┤
│ 💱    🔍    💬    👤      │
└─────────────────────────────┘
```

## 📱 Responsive Behavior Specifications

### Viewport Breakpoints
```css
/* Mobile Portrait (320px - 767px) */
@media (max-width: 767px) {
  .container {
    padding: 16px;
    font-size: 16px; /* Prevent iOS zoom */
  }
  
  .button {
    min-height: 56px; /* Touch-friendly */
    width: 100%;
  }
  
  .input {
    min-height: 56px;
    font-size: 16px; /* Prevent iOS zoom */
  }
}

/* Mobile Landscape (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    max-width: 600px;
    margin: 0 auto;
    padding: 24px;
  }
  
  .button {
    min-height: 48px;
    max-width: 300px;
  }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
  .container {
    max-width: 400px; /* Keep mobile-like width */
    margin: 0 auto;
    padding: 32px;
  }
}
```

### Touch Target Guidelines
```css
/* Minimum touch target sizes */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

.primary-touch-target {
  min-height: 56px; /* Larger for important actions */
}

.input-touch-target {
  min-height: 56px;
  padding: 16px;
}

/* Thumb-friendly spacing */
.thumb-zone {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px; /* Natural thumb reach area */
}
```

### Safe Area Support
```css
/* iPhone notch and home indicator support */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.bottom-navigation {
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
}
```

---

*These wireframes provide detailed specifications for implementing the mobile-first interface, ensuring consistent touch-friendly interactions and optimal mobile user experience.*
