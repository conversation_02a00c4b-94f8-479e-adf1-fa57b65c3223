/**
 * Database verification script to check if tag origins were stored correctly
 */

const { PrismaClient } = require('@prisma/client');

async function verifyTagOrigins() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking database for tag origins...');
      // Get the test report we just created
    const testReport = await prisma.debugReport.findFirst({
      where: {
        title: 'Test Report with Mixed Tag Origins'
      },
      include: {
        tags: true
      }
    });
      if (!testReport) {
      console.log('❌ Test report not found in database');
      return;
    }
    
    console.log(`📋 Report ID: ${testReport.id}`);
    console.log(`📝 Title: ${testReport.title}`);
    console.log(`🏷️  Tags found: ${testReport.tags.length}`);
    
    if (testReport.tags.length > 0) {
      console.log('\n🏷️  Tag details:');
      testReport.tags.forEach((tag, index) => {
        console.log(`  ${index + 1}. "${tag.tag}" - Origin: ${tag.origin}`);
      });
      
      // Verify we have all three origins
      const origins = testReport.tags.map(t => t.origin);
      const hasPredefi = origins.includes('PREDEFINED');
      const hasAI = origins.includes('AI_SUGGESTED');
      const hasUser = origins.includes('USER_DEFINED');
      
      console.log('\n✅ Origin verification:');
      console.log(`   PREDEFINED: ${hasPredefi ? '✅' : '❌'}`);
      console.log(`   AI_SUGGESTED: ${hasAI ? '✅' : '❌'}`);
      console.log(`   USER_DEFINED: ${hasUser ? '✅' : '❌'}`);
      
      if (hasPredefi && hasAI && hasUser) {
        console.log('\n🎉 SUCCESS: All tag origins are working correctly!');
      } else {
        console.log('\n⚠️  Some tag origins are missing');
      }
    } else {
      console.log('❌ No tags found for this report');
    }
    
  } catch (error) {
    console.error('❌ Database verification failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

verifyTagOrigins();
