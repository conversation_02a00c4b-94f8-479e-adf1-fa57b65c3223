# MUNygo MVP (Bronze Level)

[![License: MIT](https://img.shields.io/badge/License-MIT-blue.svg)](https://opensource.org/licenses/MIT) <!-- Optional: Choose a license -->

**A peer-to-peer currency exchange matchmaking platform designed for Iranian students and expatriates, initially focusing on the CAD <> IRR corridor. Built with TypeScript end-to-end.**

This MVP implements the "Bronze" service level: It helps users find exchange partners, view personalized rates based on reputation, and connect via secure chat to arrange transfers externally. **MUNygo Bronze does NOT handle or process actual financial transactions.**

---

**Table of Contents**

1.  [Introduction](#introduction)
2.  [Problem Solved](#problem-solved)
3.  [MVP Core Features](#mvp-core-features)
4.  [Technology Stack](#technology-stack)
5.  [Project Structure](#project-structure)
6.  [Getting Started](#getting-started)
    *   [Prerequisites](#prerequisites)
    *   [Installation](#installation)
    *   [Database Setup](#database-setup)
    *   [Environment Variables](#environment-variables)
7.  [Running the Application](#running-the-application)
8.  [Running Tests](#running-tests)
9.  [Key Documents](#key-documents)
10. [Contributing](#contributing)
11. [License](#license)

---

## Introduction

MUNygo aims to provide a safer, more transparent, and potentially more cost-effective alternative to traditional exchanges (صرافی) and risky informal P2P methods for Iranians abroad needing to exchange currencies like CAD and IRR.

This MVP focuses on validating the core user need and the effectiveness of a reputation-based tiered pricing system for P2P matchmaking, built using a modern, type-safe TypeScript stack.

## Problem Solved

Iranian students/expats often face challenges exchanging funds:
*   High fees and unfavorable rates at official exchanges.
*   Lack of trust, potential for scams, and inefficiency in informal P2P groups (e.g., Telegram).
*   Difficulty finding reliable exchange partners for specific amounts and rates.

MUNygo Bronze addresses this by providing a structured platform for discovery and connection, enhanced by a user reputation system.

## MVP Core Features

*   **User Authentication:** Secure Email/Password registration & login. Mandatory Email & Phone (SMS OTP) verification.
*   **Offer Management:** Users can create/manage Buy/Sell offers for CAD-IRR.
*   **Tiered Pricing:** Users set a base rate and simplified adjustments for lower/higher reputation counterparties.
*   **Offer Discovery:** Browse active offers with **dynamically calculated rates** shown based on the viewer's reputation level. Basic filtering.
*   **Reputation System:** Users gain points for verification and rating interactions, mapping to 5 distinct levels (visible on profiles/offers).
*   **P2P Connection:** Users express interest in offers; mutual acceptance required to proceed.
*   **Secure In-App Chat:** **Text-only** chat for confirming details and **manually exchanging payment information** (e.g., Shaba numbers, Interac details) after mutual acceptance. Built with Socket.IO. *No file uploads.*
*   **User Rating:** Simple 1-5 star rating system after concluding a chat interaction.
*   **Basic Reporting:** Flag users/offers for manual admin review.

**(Note: This MVP explicitly does NOT include payment processing, escrow, file uploads, or saving of bank details.)**

## Technology Stack

*   **Language:** **TypeScript** (End-to-End)
*   **Backend Framework:** **Node.js** with **Hono** (Fast, lightweight, TS-first)
*   **Frontend Framework:** **Vue.js 3** (Composition API with `<script setup>`)
*   **Database:** **SQLite** (Development), **PostgreSQL** (Production)
*   **ORM:** **Prisma** (Type-safe database access & migrations)
*   **Real-time Chat:** **Socket.IO** (Server & Client)
*   **Validation:** **Zod** (Schema declaration & validation)
*   **Frontend Build Tool:** **Vite**
*   **Frontend UI Library:** **Naive UI**
*   **Frontend State Management:** **Pinia**
*   **Frontend Routing:** **Vue Router**
*   **Verification:**
    *   Email: **SendGrid** / Mailgun (or Nodemailer for dev)
    *   SMS: **Twilio**
*   **Deployment (Planned):** **Render** / Heroku / Other PaaS supporting Node.js
*   **Package Manager:** npm / yarn / pnpm *(Choose one and be consistent)*
*   **Testing:** Vitest / Jest (Unit/Integration), Playwright / Cypress (E2E - Future)

## Project Structure

```
.
├── backend/            # Hono backend source code (TypeScript)
│   ├── prisma/         # Prisma schema and migrations
│   ├── src/            # Source files (routes, middleware, services, etc.)
│   ├── tests/          # Backend tests
│   ├── package.json    # Backend dependencies
│   ├── tsconfig.json   # TypeScript configuration
│   └── ...
├── frontend/           # Vue.js frontend source code (TypeScript)
│   ├── public/         # Static assets
│   ├── src/            # Source files (components, views, stores, router, etc.)
│   ├── package.json    # Frontend dependencies
│   ├── tsconfig.json   # TypeScript configuration
│   ├── vite.config.ts  # Vite configuration
│   └── ...
├── docs/               # Project documentation (PRD, User Stories)
├── .env.example        # Example environment variables file (for backend)
├── .gitignore          # Git ignore rules
└── README.md           # This file
```

*(Adjust structure based on your actual project layout, especially within `backend/src` and `frontend/src`)*

## Getting Started

Follow these instructions to set up the project locally for development.

### Prerequisites

*   [Git](https://git-scm.com/)
*   [Node.js](https://nodejs.org/) version [e.g., 18+ or LTS]
*   [npm](https://npmjs.com/) or [yarn](https://yarnpkg.com/) or [pnpm](https://pnpm.io/) *(Choose one)*
*   [PostgreSQL](https://www.postgresql.org/) installed and running (or rely on SQLite via Prisma for initial dev)
*   [Optionally: Docker & Docker Compose]

### Installation

1.  **Clone the repository:**
    ```bash
    git clone [Your Repository URL]
    cd munygo-mvp
    ```

2.  **Backend Setup:**
    ```bash
    cd backend
    # Install dependencies (use your chosen package manager)
    npm install
    # or: yarn install
    # or: pnpm install
    cd ..
    ```

3.  **Frontend Setup:**
    ```bash
    cd frontend
    # Install dependencies (use your chosen package manager)
    npm install
    # or: yarn install
    # or: pnpm install
    cd ..
    ```

### Database Setup

1.  **Configure Database Connection:** Update the `DATABASE_URL` in your `backend/.env` file (see Environment Variables below). For initial development, you can often keep the default SQLite setting in `prisma/schema.prisma`.
2.  **Generate Prisma Client:** After installing dependencies, generate the Prisma client:
    ```bash
    cd backend
    npx prisma generate
    cd ..
    ```
3.  **Run Database Migrations:** Apply schema changes to your database. Prisma will create the database file if using SQLite.
    ```bash
    cd backend
    # Creates/updates DB schema based on prisma/schema.prisma
    npx prisma migrate dev --name init # Use a descriptive name for subsequent migrations
    cd ..
    ```

### Environment Variables

1.  Create a `.env` file in the `backend` directory (copy from `.env.example` if provided, or create it).
    ```bash
    # Example: cp backend/.env.example backend/.env
    touch backend/.env # If no example exists
    ```
2.  **IMPORTANT:** Open `backend/.env` and fill in the required values. **Never commit your `.env` file to Git.**
    ```dotenv
    # Database (Prisma reads this)
    # For PostgreSQL: DATABASE_URL="postgresql://USER:PASSWORD@HOST:PORT/DATABASE?schema=public"
    # For SQLite (default often works): DATABASE_URL="file:./dev.db"

    # Application Secrets
    NODE_ENV=development
    PORT=8000 # Port for the backend server
    JWT_SECRET=[Generate a strong random secret for JWT]
    SESSION_SECRET=[Generate another strong secret if using sessions alongside/instead of JWT]

    # Email Service
    EMAIL_PROVIDER= # e.g., sendgrid
    SENDGRID_API_KEY= # If using SendGrid
    MAIL_DEFAULT_SENDER=<EMAIL>

    # SMS Service
    TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    TWILIO_AUTH_TOKEN=your_auth_token
    TWILIO_PHONE_NUMBER=+***********

    # Frontend URL (for CORS, redirects, etc.)
    FRONTEND_URL=http://localhost:5173 # Default Vite port, adjust if needed

    # Add any other Hono or application-specific variables
    ```

## Running the Application

1.  **Start the Backend Server (with Hot-Reloading):**
    ```bash
    cd backend
    # Using package.json script (common setup)
    npm run dev
    # or: yarn dev
    # or: pnpm dev
    ```
    *(This typically uses a tool like `tsx` or `ts-node-dev`. Check your `package.json`. Runs on the port defined in `.env`, e.g., 8000)*

2.  **Start the Frontend Development Server:**
    *   Open a **new terminal window**.
    ```bash
    cd frontend
    npm run dev
    # or: yarn dev
    # or: pnpm dev
    ```
    *(This starts the Vite development server, typically on port 5173)*

3.  Open your web browser and navigate to the frontend URL (e.g., `http://localhost:5173`).

## Running Tests

*(Provide instructions based on your chosen testing framework, e.g., Vitest)*

```bash
# Example for backend tests
cd backend
npm test
# or: yarn test
# or: pnpm test

# Example for frontend tests
cd frontend
npm test
# or: yarn test
# or: pnpm test
```

## Key Documents

*   **Product Requirements Document (PRD):** [PRD](prd.md)
*   **User Stories:** [User Stories](user_stories.md)

## Contributing

This is currently a solo project for the MVP phase. Contributions are not open at this time. For issues or suggestions, please use the repository's issue tracker if available.

## License

This project is licensed under the **[MIT License]** - see the [LICENSE.md](LICENSE.md) file for details. *(Choose a license like MIT and add a LICENSE.md file, or state otherwise)*
