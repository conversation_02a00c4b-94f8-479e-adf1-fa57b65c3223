import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Hono } from 'hono';
import { PrismaClient } from '@prisma/client';
import { sign } from 'jsonwebtoken';
import createPaymentMethodRoutes from '../../routes/paymentMethods';

// Test environment setup
const JWT_SECRET = process.env.JWT_SECRET || 'test-secret';
const prisma = new PrismaClient();
const app = new Hono();

// Setup routes
app.route('/payment-methods', createPaymentMethodRoutes(prisma));

describe('Payment Methods API with Transactions', () => {
  let testUser: any;
  let authToken: string;

  beforeEach(async () => {
    // Clean up existing test data
    await prisma.paymentReceivingInfo.deleteMany({
      where: { user: { email: { endsWith: '@test.com' } } }
    });
    await prisma.user.deleteMany({
      where: { email: { endsWith: '@test.com' } }
    });

    // Create test user
    testUser = await prisma.user.create({
      data: {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'hashedpassword',
        phoneNumber: '+**********',
        phoneVerified: true,
        emailVerified: true,
        reputationLevel: 1
      }
    });

    // Generate auth token
    authToken = sign({ userId: testUser.id, email: testUser.email }, JWT_SECRET);
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.paymentReceivingInfo.deleteMany({
      where: { user: { email: { endsWith: '@test.com' } } }
    });
    await prisma.user.deleteMany({
      where: { email: { endsWith: '@test.com' } }
    });
  });

  describe('POST /payment-methods - Transaction Implementation', () => {
    it('should create payment method within a transaction successfully', async () => {
      const paymentMethodData = {
        currency: 'USD',
        paymentMethodType: 'BANK_TRANSFER',
        bankName: 'Test Bank',
        accountNumber: '**********',
        accountHolderName: 'Test User',
        notes: 'Test payment method'
      };

      const response = await app.request('/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(paymentMethodData)
      });

      expect(response.status).toBe(201);
      
      const result = await response.json() as any;
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.currency).toBe('USD');
      expect(result.data.isDefaultForUser).toBe(true); // First payment method should be default
      expect(result.message).toBe('Payment method created successfully');

      // Verify the payment method exists in database
      const dbPaymentMethod = await prisma.paymentReceivingInfo.findUnique({
        where: { id: result.data.id }
      });
      expect(dbPaymentMethod).toBeDefined();
      expect(dbPaymentMethod?.userId).toBe(testUser.id);
    });

    it('should handle transaction rollback on failure', async () => {
      // Mock Prisma to simulate a database error during the transaction
      const originalTransaction = prisma.$transaction;
      
      // Create a spy that throws an error to simulate transaction failure
      const transactionSpy = vi.spyOn(prisma, '$transaction').mockRejectedValueOnce(
        new Error('Simulated database error')
      );

      const paymentMethodData = {
        currency: 'EUR',
        paymentMethodType: 'BANK_TRANSFER',
        bankName: 'Test Bank',
        accountNumber: '**********',
        accountHolderName: 'Test User'
      };

      const response = await app.request('/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(paymentMethodData)
      });

      expect(response.status).toBe(500);
      
      const result = await response.json() as any;
      expect(result.error).toBe('Failed to create payment method');

      // Verify no payment method was created in the database
      const paymentMethods = await prisma.paymentReceivingInfo.findMany({
        where: { userId: testUser.id }
      });
      expect(paymentMethods).toHaveLength(0);

      // Restore the original $transaction method
      transactionSpy.mockRestore();
    });

    it('should ensure atomicity when creating first payment method (auto-default)', async () => {
      const paymentMethodData = {
        currency: 'CAD',
        paymentMethodType: 'DIGITAL_WALLET',
        bankName: 'Digital Bank',
        accountNumber: 'wallet123',
        accountHolderName: 'Test User'
      };

      const response = await app.request('/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(paymentMethodData)
      });

      expect(response.status).toBe(201);
      
      const result = await response.json() as any;
      
      // Verify the payment method was created and set as default in a single transaction
      const dbPaymentMethod = await prisma.paymentReceivingInfo.findUnique({
        where: { id: result.data.id }
      });
      
      expect(dbPaymentMethod).toBeDefined();
      expect(dbPaymentMethod?.isDefaultForUser).toBe(true);
      expect(dbPaymentMethod?.isActive).toBe(true);
      
      // Verify no other payment methods exist for this user and currency
      const allPaymentMethods = await prisma.paymentReceivingInfo.findMany({
        where: { 
          userId: testUser.id,
          currency: 'CAD'
        }
      });
      
      expect(allPaymentMethods).toHaveLength(1);
      expect(allPaymentMethods[0].isDefaultForUser).toBe(true);
    });

    it('should require authentication', async () => {
      const paymentMethodData = {
        currency: 'USD',
        paymentMethodType: 'BANK_TRANSFER',
        bankName: 'Test Bank',
        accountNumber: '**********',
        accountHolderName: 'Test User'
      };

      const response = await app.request('/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          // No Authorization header
        },
        body: JSON.stringify(paymentMethodData)
      });

      expect(response.status).toBe(401);
      
      const result = await response.json() as any;
      expect(result.error).toBe('Unauthorized: Missing or invalid token');
    });

    it('should validate input data', async () => {
      const invalidPaymentMethodData = {
        currency: 'INVALID', // Invalid currency
        paymentMethodType: 'INVALID_TYPE', // Invalid type
        bankName: '', // Empty bank name
        accountNumber: '', // Empty account number
        accountHolderName: '' // Empty account holder name
      };

      const response = await app.request('/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(invalidPaymentMethodData)
      });

      expect(response.status).toBe(400);
    });
  });
});
