import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { testClient } from 'hono/testing'
import { PrismaClient, MatchResponse, MatchStatus } from '@prisma/client'
import { sign } from 'jsonwebtoken'
import { Hono } from 'hono'
import createMatchRoutes from '../../routes/matchRoutes'
import { MatchingService } from '../../services/matchingService'
import { NotificationService } from '../../services/notificationService'
import { Server } from 'socket.io'

const prisma = new PrismaClient()
const JWT_SECRET = process.env.JWT_SECRET || 'test-secret'

// Mock Socket.IO server
const mockIo = {
  to: vi.fn().mockReturnThis(),
  emit: vi.fn()
} as unknown as Server

// Create mock services
const mockNotificationService = {
  createNotification: vi.fn(),
  emitNotificationToUser: vi.fn()
} as unknown as NotificationService

// Create MatchingService instance with mocked dependencies
const matchingService = new MatchingService(prisma, mockIo, mockNotificationService)

// Create test app with match routes
const matchRoutes = createMatchRoutes(matchingService)
const testApp = new Hono().route('/matches', matchRoutes)
const client = testClient(testApp) as any

describe('Match Routes Integration Tests', () => {
  let testUser1: any
  let testUser2: any
  let testUser3: any
  let testOffer1: any
  let testOffer2: any
  let testOffer3: any
  let testOffer4: any
  let testMatch: any
  let authToken1: string
  let authToken2: string
  let authToken3: string

  beforeEach(async () => {
    // Clear mocks
    vi.clearAllMocks()

    // Clean up existing test data - correct order to respect foreign key constraints
    // Only delete test-specific data to avoid affecting development users
    await prisma.chatMessage.deleteMany({
      where: {
        OR: [
          { chatSession: { userOne: { email: { endsWith: '@test.com' } } } },
          { chatSession: { userTwo: { email: { endsWith: '@test.com' } } } },
          { chatSession: { userOne: { username: { contains: 'user' } } } },
          { chatSession: { userTwo: { username: { contains: 'user' } } } }
        ]
      }
    })
    await prisma.payerNegotiation.deleteMany({
      where: {
        OR: [
          { transaction: { offer: { user: { email: { endsWith: '@test.com' } } } } },
          { transaction: { offer: { user: { username: { contains: 'user' } } } } }
        ]
      }
    })
    await prisma.transaction.deleteMany({
      where: {
        OR: [
          { offer: { user: { email: { endsWith: '@test.com' } } } },
          { offer: { user: { username: { contains: 'user' } } } }
        ]
      }
    })
    await prisma.chatSession.deleteMany({
      where: {
        OR: [
          { userOne: { email: { endsWith: '@test.com' } } },
          { userTwo: { email: { endsWith: '@test.com' } } },
          { userOne: { username: { contains: 'user' } } },
          { userTwo: { username: { contains: 'user' } } }
        ]
      }
    })
    await prisma.offerMatch.deleteMany({
      where: {
        OR: [
          { offerA: { user: { email: { endsWith: '@test.com' } } } },
          { offerB: { user: { email: { endsWith: '@test.com' } } } },
          { offerA: { user: { username: { contains: 'user' } } } },
          { offerB: { user: { username: { contains: 'user' } } } }
        ]
      }
    })
    await prisma.matchConfiguration.deleteMany({
      where: {
        configKey: { contains: 'test' }
      }
    })
    await prisma.interest.deleteMany({
      where: {
        OR: [
          { interestedUser: { email: { endsWith: '@test.com' } } },
          { interestedUser: { username: { contains: 'user' } } },
          { offer: { user: { email: { endsWith: '@test.com' } } } },
          { offer: { user: { username: { contains: 'user' } } } }
        ]
      }
    })
    await prisma.paymentReceivingInfo.deleteMany({
      where: {
        user: { email: { endsWith: '@test.com' } }
      }
    })
    await prisma.offer.deleteMany({
      where: {
        user: { email: { endsWith: '@test.com' } }
      }
    })
    await prisma.user.deleteMany({
      where: {
        OR: [
          { email: { endsWith: '@test.com' } },
          { username: { contains: 'user' } }
        ]
      }
    })

    // Create test users
    testUser1 = await prisma.user.create({
      data: {
        username: 'user1',
        email: '<EMAIL>',
        password: 'hashedpass1',
        emailVerified: true,
        reputationLevel: 5
      }
    })

    testUser2 = await prisma.user.create({
      data: {
        username: 'user2',
        email: '<EMAIL>',
        password: 'hashedpass2',
        emailVerified: true,
        reputationLevel: 4
      }
    })

    testUser3 = await prisma.user.create({
      data: {
        username: 'user3',
        email: '<EMAIL>',
        password: 'hashedpass3',
        emailVerified: true,
        reputationLevel: 3
      }
    })

    // Create JWT tokens with required fields (userId and email)
    authToken1 = sign({ userId: testUser1.id, email: testUser1.email }, JWT_SECRET)
    authToken2 = sign({ userId: testUser2.id, email: testUser2.email }, JWT_SECRET)
    authToken3 = sign({ userId: testUser3.id, email: testUser3.email }, JWT_SECRET)

    // Create test offers
    testOffer1 = await prisma.offer.create({
      data: {
        type: 'SELL',
        currencyPair: 'USD-EUR',
        amount: 1000,
        baseRate: 0.85,
        adjustmentForLowerRep: 0.02,
        adjustmentForHigherRep: 0.01,
        status: 'ACTIVE',
        userId: testUser1.id
      }
    })

    testOffer2 = await prisma.offer.create({
      data: {
        type: 'BUY',
        currencyPair: 'USD-EUR',
        amount: 1000,
        baseRate: 0.85,
        adjustmentForLowerRep: 0.02,
        adjustmentForHigherRep: 0.01,
        status: 'ACTIVE',
        userId: testUser2.id
      }
    })

    // Create additional test offers for other tests to avoid unique constraint violations
    testOffer3 = await prisma.offer.create({
      data: {
        type: 'SELL',
        currencyPair: 'GBP-USD',
        amount: 500,
        baseRate: 1.25,
        adjustmentForLowerRep: 0.015,
        adjustmentForHigherRep: 0.005,
        status: 'ACTIVE',
        userId: testUser1.id
      }
    })

    testOffer4 = await prisma.offer.create({
      data: {
        type: 'BUY',
        currencyPair: 'GBP-USD',
        amount: 500,
        baseRate: 1.25,
        adjustmentForLowerRep: 0.015,
        adjustmentForHigherRep: 0.005,
        status: 'ACTIVE',
        userId: testUser3.id
      }
    })

    // Create a test match
    testMatch = await prisma.offerMatch.create({
      data: {
        matchId: 'match-123',
        offerAId: testOffer1.id,
        offerBId: testOffer2.id,
        userAId: testUser1.id,
        userBId: testUser2.id,
        status: MatchStatus.PENDING,
        compatibilityScore: 0.955, // Fixed: must be between 0.000-9.999 for Decimal(4,3)
        matchCriteria: { exactRate: true, withinThreshold: false },
        currencyA: 'USD',
        currencyB: 'EUR',
        amountA: 1000,
        amountB: 850,
        rateAToB: 0.85,
        rateBToA: 1.176,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        userAResponse: null,
        userBResponse: null
      }    })
  })
  afterEach(async () => {
    // Clean up test data - correct order to respect foreign key constraints
    // Only delete test-specific data to avoid affecting development users
    await prisma.chatMessage.deleteMany({
      where: {
        OR: [
          { chatSession: { userOne: { email: { endsWith: '@test.com' } } } },
          { chatSession: { userTwo: { email: { endsWith: '@test.com' } } } },
          { chatSession: { userOne: { username: { contains: 'user' } } } },
          { chatSession: { userTwo: { username: { contains: 'user' } } } }
        ]
      }
    })
    await prisma.payerNegotiation.deleteMany({
      where: {
        OR: [
          { transaction: { offer: { user: { email: { endsWith: '@test.com' } } } } },
          { transaction: { offer: { user: { username: { contains: 'user' } } } } }
        ]
      }
    })
    await prisma.transaction.deleteMany({
      where: {
        OR: [
          { offer: { user: { email: { endsWith: '@test.com' } } } },
          { offer: { user: { username: { contains: 'user' } } } }
        ]
      }
    })
    await prisma.chatSession.deleteMany({
      where: {
        OR: [
          { userOne: { email: { endsWith: '@test.com' } } },
          { userTwo: { email: { endsWith: '@test.com' } } },
          { userOne: { username: { contains: 'user' } } },
          { userTwo: { username: { contains: 'user' } } }
        ]
      }
    })
    await prisma.offerMatch.deleteMany({
      where: {
        OR: [
          { offerA: { user: { email: { endsWith: '@test.com' } } } },
          { offerB: { user: { email: { endsWith: '@test.com' } } } },
          { offerA: { user: { username: { contains: 'user' } } } },
          { offerB: { user: { username: { contains: 'user' } } } }
        ]
      }
    })
    await prisma.matchConfiguration.deleteMany({
      where: {
        configKey: { contains: 'test' }
      }
    })
    await prisma.interest.deleteMany({
      where: {
        OR: [
          { interestedUser: { email: { endsWith: '@test.com' } } },
          { interestedUser: { username: { contains: 'user' } } },
          { offer: { user: { email: { endsWith: '@test.com' } } } },
          { offer: { user: { username: { contains: 'user' } } } }
        ]
      }
    })
    await prisma.paymentReceivingInfo.deleteMany({
      where: {
        user: { email: { endsWith: '@test.com' } }
      }
    })
    await prisma.offer.deleteMany({
      where: {
        user: { email: { endsWith: '@test.com' } }
      }
    })
    await prisma.user.deleteMany({
      where: {
        OR: [
          { email: { endsWith: '@test.com' } },
          { username: { contains: 'user' } }
        ]
      }
    })
  })

  describe('GET /matches - Get user matches', () => {
    it('should return all matches for authenticated user', async () => {
      const response = await client.matches.$get(
        {},
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      expect(result.success).toBe(true)
      expect(result.matches).toHaveLength(1)
      
      expect(result.matches[0]).toMatchObject({
        id: testMatch.id,
        matchId: 'match-123',
        status: MatchStatus.PENDING,
        compatibilityScore: 0.955,
        currencyA: 'USD',
        currencyB: 'EUR',
        amountA: 1000,
        amountB: 850,
        userAResponse: null,
        userBResponse: null
      })
      
      expect(result.matches[0].otherUser).toMatchObject({
        id: testUser2.id,
        username: 'user2',
        email: '<EMAIL>'
      })
    })

    it('should return filtered matches by status', async () => {
      // Create an accepted match with different offers to avoid unique constraint
      await prisma.offerMatch.create({
        data: {
          matchId: 'match-accepted',
          offerAId: testOffer3.id,
          offerBId: testOffer4.id,
          userAId: testUser1.id,          userBId: testUser3.id,
          status: MatchStatus.BOTH_ACCEPTED,
          compatibilityScore: 0.980,
          matchCriteria: { exactRate: true },
          currencyA: 'GBP',
          currencyB: 'USD',
          amountA: 500,
          amountB: 625,
          rateAToB: 1.25,
          rateBToA: 0.8,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
          userAResponse: MatchResponse.ACCEPTED,
          userBResponse: MatchResponse.ACCEPTED
        }
      })

      const response = await client.matches.$get(
        { query: { status: 'BOTH_ACCEPTED' } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      
      expect(result.success).toBe(true)
      expect(result.matches).toHaveLength(1)
      expect(result.matches[0].status).toBe('BOTH_ACCEPTED')
      expect(result.matches[0].matchId).toBe('match-accepted')
    })

    it('should return empty array when user has no matches', async () => {
      const response = await client.matches.$get(
        {},
        { headers: { Authorization: `Bearer ${authToken3}` } }
      )

      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      
      expect(result.success).toBe(true)
      expect(result.matches).toHaveLength(0)
    })

    it('should require authentication', async () => {
      const response = await client.matches.$get({})

      expect(response.status).toBe(401)
    })

    it('should handle invalid status filter gracefully', async () => {
      const response = await client.matches.$get(
        { query: { status: 'INVALID_STATUS' as any } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(400)
    })
  })

  describe('GET /matches/:matchId - Get match details', () => {
    it('should return detailed match information for participant', async () => {
      const response = await client.matches[':matchId'].$get(
        { param: { matchId: testMatch.id } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      
      expect(result.success).toBe(true)
      expect(result.match).toMatchObject({        id: testMatch.id,
        matchId: 'match-123',
        status: MatchStatus.PENDING,
        compatibilityScore: 0.955,
        matchCriteria: { exactRate: true, withinThreshold: false },
        currencyA: 'USD',
        currencyB: 'EUR',
        amountA: 1000,
        amountB: 850,
        rateAToB: 0.85,
        rateBToA: 1.176
      })
      expect(result.match.userA).toMatchObject({
        id: testUser1.id,
        username: 'user1'
      })
      expect(result.match.userB).toMatchObject({
        id: testUser2.id,
        username: 'user2'
      })
      expect(result.match.offerA).toMatchObject({
        id: testOffer1.id,
        type: 'SELL'
      })
      expect(result.match.offerB).toMatchObject({
        id: testOffer2.id,
        type: 'BUY'
      })
    })

    it('should return 404 for non-existent match', async () => {
      const response = await client.matches[':matchId'].$get(
        { param: { matchId: 'non-existent-match' } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(404)
      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('Match not found or access denied')
    })

    it('should return 404 when user is not a participant in the match', async () => {
      const response = await client.matches[':matchId'].$get(
        { param: { matchId: testMatch.id } },
        { headers: { Authorization: `Bearer ${authToken3}` } }
      )

      expect(response.status).toBe(404)      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('Match not found or access denied')
    })

    it('should require authentication', async () => {
      const response = await client.matches[':matchId'].$get(
        { param: { matchId: testMatch.id } }
      )

      expect(response.status).toBe(401)
    })    
    it('should handle malformed match ID', async () => {
      const response = await client.matches[':matchId'].$get(
        { param: { matchId: 'invalid-match-id-format' } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(404)
      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('Match not found or access denied')
    })
  })

  describe('POST /matches/:matchId/accept - Accept match', () => {
    it('should successfully accept a match (first user)', async () => {      const response = await client.matches[':matchId'].accept.$post(
        { param: { matchId: testMatch.id }, json: {} },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      expect(result.success).toBe(true);
      expect(result.status).toBe('partial_accept');
      expect(result.message).toBe('Match accepted! Waiting for the other user to accept.');
      expect(result.match.userAResponse).toBe('ACCEPTED');
      expect(result.match.userBResponse).toBe(null);      // Verify database was updated
      const updatedMatch = await prisma.offerMatch.findUnique({
        where: { id: testMatch.id }
      })

      expect(updatedMatch?.userAResponse).toBe('ACCEPTED')
      expect(updatedMatch?.userARespondedAt).toBeTruthy()
    })

    it('should create transaction when both users accept', async () => {
      // First user accepts
      await prisma.offerMatch.update({
        where: { id: testMatch.id },
        data: {
          userAResponse: MatchResponse.ACCEPTED,
          userARespondedAt: new Date()
        }
      })

      // Second user accepts
      const response = await client.matches[':matchId'].accept.$post(
        { param: { matchId: testMatch.id }, json: {} },
        { headers: { Authorization: `Bearer ${authToken2}` } }
      )      
      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      expect(result.success).toBe(true)
      expect(result.status).toBe('both_accepted')
      expect(result.message).toBe('Match accepted! A transaction has been created.')
      expect(result.match.status).toBe('CONVERTED')
      // Transaction and chat session should be created
      expect(result.match.transactionId).toBeDefined()
      expect(result.match.chatSessionId).toBeDefined()
    })

    it('should return 404 for non-existent match', async () => {
      const response = await client.matches[':matchId'].accept.$post(
        { param: { matchId: 'non-existent' }, json: {} },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(404)
      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('Match not found')
    })

    it('should return 403 for unauthorized user', async () => {
      const response = await client.matches[':matchId'].accept.$post(
        { param: { matchId: testMatch.id }, json: {} },
        { headers: { Authorization: `Bearer ${authToken3}` } }
      )

      expect(response.status).toBe(403)
      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('Unauthorized to accept this match')
    })

    it('should return 409 when user has already responded', async () => {
      // Accept once
      await client.matches[':matchId'].accept.$post(
        { param: { matchId: testMatch.id }, json: {} },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      // Try to accept again
      const response = await client.matches[':matchId'].accept.$post(
        { param: { matchId: testMatch.id }, json: {} },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(409)
      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('You have already responded to this match')
    })

    it('should return 410 for expired match', async () => {
      // Update match to be expired
      await prisma.offerMatch.update({
        where: { id: testMatch.id },
        data: {
          expiresAt: new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
        }
      })

      const response = await client.matches[':matchId'].accept.$post(
        { param: { matchId: testMatch.id }, json: {} },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(410)
      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('This match has expired')
    })

    it('should require authentication', async () => {
      const response = await client.matches[':matchId'].accept.$post(
        { param: { matchId: testMatch.id }, json: {} }
      )

      expect(response.status).toBe(401)
    })
  })

  describe('POST /matches/:matchId/decline - Decline match', () => {
    it('should successfully decline a match without reason', async () => {
      const response = await client.matches[':matchId'].decline.$post(
        { param: { matchId: testMatch.id }, json: {} },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('Match declined successfully')
      expect(result.match.status).toBe('DECLINED')
      expect(result.match.declineReason).toBeNull()

      // Verify database was updated
      const updatedMatch = await prisma.offerMatch.findUnique({
        where: { id: testMatch.id }
      })
      expect(updatedMatch?.status).toBe('DECLINED')
    })

    it('should successfully decline a match with reason', async () => {
      const response = await client.matches[':matchId'].decline.$post(
        { param: { matchId: testMatch.id }, json: { reason: 'Rate not favorable' } },
        { headers: { Authorization: `Bearer ${authToken2}` } }
      )

      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('Match declined successfully')
      expect(result.match.status).toBe('DECLINED')
      expect(result.match.declineReason).toBe('Rate not favorable')

      // Verify database was updated
      const updatedMatch = await prisma.offerMatch.findUnique({
        where: { id: testMatch.id }
      })
      expect(updatedMatch?.declineReason).toBe('Rate not favorable')
    })

    it('should return 404 for non-existent match', async () => {
      const response = await client.matches[':matchId'].decline.$post(
        { param: { matchId: 'non-existent' }, json: {} },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(404)
      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('Match not found')
    })

    it('should return 403 for unauthorized user', async () => {
      const response = await client.matches[':matchId'].decline.$post(
        { param: { matchId: testMatch.id }, json: {} },
        { headers: { Authorization: `Bearer ${authToken3}` } }
      )

      expect(response.status).toBe(403)
      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('Unauthorized to decline this match')
    })

    it('should return 410 for already processed match', async () => {
      // Update match to already be accepted
      await prisma.offerMatch.update({
        where: { id: testMatch.id },
        data: {
          status: 'BOTH_ACCEPTED',
          userAResponse: 'ACCEPTED',
          userBResponse: 'ACCEPTED'
        }
      })

      const response = await client.matches[':matchId'].decline.$post(
        { param: { matchId: testMatch.id }, json: {} },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(410)
      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('This match is no longer available')
    })

    it('should require authentication', async () => {
      const response = await client.matches[':matchId'].decline.$post(
        { param: { matchId: testMatch.id }, json: {} }
      )

      expect(response.status).toBe(401)
    })
  })

  describe('POST /matches/find-for-offer/:offerId - Manual match finding', () => {
    it('should find matches for valid offer', async () => {
      const response = await client.matches['find-for-offer'][':offerId'].$post(
        { param: { offerId: testOffer1.id } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      
      expect(result.success).toBe(true)
      expect(typeof result.matchesCount).toBe('number')
      expect(result.message).toContain('Found')
      expect(Array.isArray(result.matches)).toBe(true)
    })

    it('should handle non-existent offer', async () => {
      const response = await client.matches['find-for-offer'][':offerId'].$post(
        { param: { offerId: 'non-existent-offer' } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )
      // Non-existent offer should return success with 0 matches, not an error
      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      expect(result.success).toBe(true)
      expect(result.matchesCount).toBe(0)
    })

    it('should require authentication', async () => {
      const response = await client.matches['find-for-offer'][':offerId'].$post(
        { param: { offerId: testOffer1.id } }
      )

      expect(response.status).toBe(401)
    })
  })

  describe('POST /matches/cleanup-expired - Cleanup expired matches', () => {
    it('should cleanup expired matches', async () => {
      // Create an expired match with different offers to avoid unique constraint
      await prisma.offerMatch.create({
        data: {
          matchId: 'expired-match',
          offerAId: testOffer3.id,
          offerBId: testOffer4.id,
          userAId: testUser1.id,
          userBId: testUser3.id,
          status: MatchStatus.PENDING,
          compatibilityScore: 0.900,
          matchCriteria: { exactRate: true },
          currencyA: 'GBP',
          currencyB: 'USD',
          amountA: 500,
          amountB: 625,          rateAToB: 1.25,
          rateBToA: 0.8,
          expiresAt: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
          userAResponse: null,
          userBResponse: null
        }
      })

      const response = await client.matches['cleanup-expired'].$post(
        {},
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(200)
      
      const result = await response.json() as any
      
      expect(result.success).toBe(true)
      expect(typeof result.cleanedCount).toBe('number')
      expect(result.message).toContain('Cleaned up')
    })

    it('should require authentication', async () => {
      const response = await client.matches['cleanup-expired'].$post({})

      expect(response.status).toBe(401)
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // Mock prisma to throw an error
      const originalMethod = prisma.offerMatch.findMany
      prisma.offerMatch.findMany = vi.fn().mockRejectedValue(new Error('Database connection failed'))

      const response = await client.matches.$get(
        {},
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(500)
      
      const result = await response.json() as any
      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to retrieve matches')
      
      // Restore original method
      prisma.offerMatch.findMany = originalMethod
    })

    it('should handle missing authorization header', async () => {
      const response = await client.matches[':matchId'].decline.$post({
        param: { matchId: testMatch.id },
        json: { reason: 'Test reason' }
      })
      
      expect(response.status).toBe(401)
      
      const result = await response.json() as any
      expect(result.error).toBe('Unauthorized: Missing or invalid token')
    })
  })

  describe('Performance and Data Integrity', () => {
    it('should handle concurrent match responses correctly', async () => {
      // Simulate concurrent accept requests from both users
      const responses = await Promise.allSettled([
        client.matches[':matchId'].accept.$post(
          { param: { matchId: testMatch.id }, json: {} },
          { headers: { Authorization: `Bearer ${authToken1}` } }
        ),
        client.matches[':matchId'].accept.$post(
          { param: { matchId: testMatch.id }, json: {} },
          { headers: { Authorization: `Bearer ${authToken2}` } }
        )
      ])

      // Both requests should succeed
      responses.forEach(response => {
        if (response.status === 'fulfilled') {
          expect(response.value.status).toBe(200)
        }
      })

      // Wait a bit for database updates to complete
      await new Promise(resolve => setTimeout(resolve, 100))      // Verify final state is correct
      const finalMatch = await prisma.offerMatch.findUnique({
        where: { id: testMatch.id }
      })
      expect(finalMatch?.userAResponse).toBe('ACCEPTED')
      expect(finalMatch?.userBResponse).toBe('ACCEPTED')
      // When both users accept, the system automatically converts the match to a transaction
      // The status progresses: partial_accept → both_accepted → CONVERTED
      expect(['BOTH_ACCEPTED', 'PARTIAL_ACCEPT', 'CONVERTED']).toContain(finalMatch?.status)
    })

    it('should return consistent data across multiple requests', async () => {
      const responses = await Promise.all([
        client.matches[':matchId'].$get(
          { param: { matchId: testMatch.id } },
          { headers: { Authorization: `Bearer ${authToken1}` } }
        ),        client.matches[':matchId'].$get(
          { param: { matchId: testMatch.id } },
          { headers: { Authorization: `Bearer ${authToken2}` } }
        )
      ])

      const [response1, response2] = responses
      const [result1, result2] = await Promise.all([
        response1.json(),
        response2.json()
      ]) as any[]
        // Core match data should be identical
      expect(result1.match.id).toBe(result2.match.id)
      expect(result1.match.matchId).toBe(result2.match.matchId)
      expect(result1.match.status).toBe(result2.match.status)
      expect(result1.match.compatibilityScore).toBe(result2.match.compatibilityScore)
      expect(result1.match.rateAToB).toBe(result2.match.rateAToB)
    })
  })
})
