-- Create<PERSON><PERSON>
CREATE TYPE "OfferType" AS ENUM ('BUY', 'SELL');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "OfferStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'DEACTIVATED', 'COMPLETED', 'CANCELLED');

-- C<PERSON><PERSON>num
CREATE TYPE "TransactionStatus" AS ENUM ('AWAITING_FIRST_PAYER_DESIGNATION', 'AWAITING_FIRST_PAYER_PAYMENT', 'AWAITING_SECOND_PAYER_CONFIRMATION', 'AWAITING_SECOND_PAYER_PAYMENT', 'AWAITING_FIRST_PAYER_CONFIRMATION', 'COMPLETED', 'CANCELLED', 'DISPUTED');

-- Create<PERSON>num
CREATE TYPE "ReceivingInfoStatus" AS ENUM ('PENDING_INPUT', 'PROVIDED', 'CONFIRMED_FROM_PROFILE');

-- Create<PERSON><PERSON>
CREATE TYPE "NegotiationStatus" AS ENUM ('PENDING_RECEIVING_INFO', 'AWAITING_PARTY_A_RECEIVING_INFO', 'AWAITING_PARTY_B_RECEIVING_INFO', 'READY_TO_NEGOTIATE', 'PENDING_RESPONSE', 'FINALIZED', 'EXPIRED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "InterestStatus" AS ENUM ('PENDING', 'ACCEPTED', 'DECLINED');

-- CreateEnum
CREATE TYPE "NotificationType" AS ENUM ('NEW_INTEREST_ON_YOUR_OFFER', 'YOUR_INTEREST_ACCEPTED', 'YOUR_INTEREST_DECLINED', 'CHAT_MESSAGE_RECEIVED', 'OFFER_STATUS_UPDATED_BY_OWNER', 'OFFER_STATUS_CHANGED', 'TRANSACTION_STARTED', 'TRANSACTION_ACTION_REQUIRED', 'TRANSACTION_UPDATE', 'TRANSACTION_PAYMENT_DECLARED', 'TRANSACTION_PAYMENT_CONFIRMED', 'TRANSACTION_COMPLETED', 'TRANSACTION_CANCELLED', 'TRANSACTION_DISPUTED', 'TRANSACTION_AUTO_CANCELLED_TIMER');

-- CreateEnum
CREATE TYPE "debug_report_type" AS ENUM ('BUG', 'FEATURE_REQUEST', 'PERFORMANCE', 'UI_UX', 'IMPROVEMENT', 'QUESTION', 'OTHER');

-- CreateEnum
CREATE TYPE "debug_report_severity" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- CreateEnum
CREATE TYPE "debug_report_status" AS ENUM ('NOT_REVIEWED', 'IN_PROGRESS', 'COMPLETED', 'ARCHIVED', 'DUPLICATE', 'WONT_FIX');

-- CreateEnum
CREATE TYPE "tag_origin" AS ENUM ('PREDEFINED', 'AI_SUGGESTED', 'USER_DEFINED');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "emailVerified" BOOLEAN DEFAULT false,
    "verificationToken" TEXT,
    "phoneNumber" TEXT,
    "phoneVerified" BOOLEAN DEFAULT false,
    "otpSecret" TEXT,
    "otpTimestamp" TIMESTAMP(3),
    "username" TEXT,
    "reputationScore" INTEGER NOT NULL DEFAULT 0,
    "reputationLevel" INTEGER NOT NULL DEFAULT 1,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Offer" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "OfferType" NOT NULL,
    "currencyPair" TEXT NOT NULL DEFAULT 'CAD-IRR',
    "amount" DOUBLE PRECISION NOT NULL,
    "baseRate" DOUBLE PRECISION NOT NULL,
    "adjustmentForLowerRep" DOUBLE PRECISION NOT NULL,
    "adjustmentForHigherRep" DOUBLE PRECISION NOT NULL,
    "status" "OfferStatus" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Offer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Interest" (
    "id" TEXT NOT NULL,
    "offerId" TEXT NOT NULL,
    "interestedUserId" TEXT NOT NULL,
    "status" "InterestStatus" NOT NULL DEFAULT 'PENDING',
    "declineReasonCode" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Interest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChatSession" (
    "id" TEXT NOT NULL,
    "offerId" TEXT NOT NULL,
    "userOneId" TEXT NOT NULL,
    "userTwoId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "interestId" TEXT,

    CONSTRAINT "ChatSession_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChatMessage" (
    "id" TEXT NOT NULL,
    "chatSessionId" TEXT NOT NULL,
    "senderId" TEXT,
    "content" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "isSystemMessage" BOOLEAN NOT NULL DEFAULT false,
    "transactionId" TEXT,

    CONSTRAINT "ChatMessage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "NotificationType" NOT NULL,
    "message" TEXT NOT NULL,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "relatedEntityType" TEXT,
    "relatedEntityId" TEXT,
    "actorId" TEXT,
    "actorUsername" TEXT,
    "data" TEXT,

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Transaction" (
    "id" TEXT NOT NULL,
    "offerId" TEXT,
    "chatSessionId" TEXT NOT NULL,
    "currencyA" TEXT NOT NULL,
    "amountA" DOUBLE PRECISION NOT NULL,
    "currencyAProviderId" TEXT NOT NULL,
    "currencyB" TEXT NOT NULL,
    "amountB" DOUBLE PRECISION NOT NULL,
    "currencyBProviderId" TEXT NOT NULL,
    "status" "TransactionStatus" NOT NULL DEFAULT 'AWAITING_FIRST_PAYER_DESIGNATION',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "termsAgreementTimestampPayer1" TIMESTAMP(3),
    "termsAgreementTimestampPayer2" TIMESTAMP(3),
    "agreedFirstPayerId" TEXT,
    "firstPayerDesignationTimestamp" TIMESTAMP(3),
    "paymentExpectedByPayer1" TIMESTAMP(3),
    "paymentDeclaredAtPayer1" TIMESTAMP(3),
    "paymentTrackingNumberPayer1" TEXT,
    "firstPaymentConfirmedByPayer2At" TIMESTAMP(3),
    "paymentExpectedByPayer2" TIMESTAMP(3),
    "paymentDeclaredAtPayer2" TIMESTAMP(3),
    "paymentTrackingNumberPayer2" TEXT,
    "secondPaymentConfirmedByPayer1At" TIMESTAMP(3),
    "cancellationReason" TEXT,
    "cancelledByUserId" TEXT,
    "disputeReason" TEXT,
    "disputedByUserId" TEXT,
    "disputeResolvedAt" TIMESTAMP(3),
    "disputeResolutionNotes" TEXT,

    CONSTRAINT "Transaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PayerNegotiation" (
    "negotiationId" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "partyA_Id" TEXT NOT NULL,
    "partyB_Id" TEXT NOT NULL,
    "partyA_receivingInfoStatus" "ReceivingInfoStatus" NOT NULL DEFAULT 'PENDING_INPUT',
    "partyB_receivingInfoStatus" "ReceivingInfoStatus" NOT NULL DEFAULT 'PENDING_INPUT',
    "partyA_PaymentReceivingInfoId" TEXT,
    "partyB_PaymentReceivingInfoId" TEXT,
    "systemRecommendedPayerId" TEXT,
    "systemRecommendationRule" TEXT,
    "systemRecommendationReason" TEXT,
    "systemRecommendationDetails" TEXT,
    "currentProposal_PayerId" TEXT,
    "currentProposal_ById" TEXT,
    "currentProposal_Message" TEXT,
    "partyA_agreedToCurrentProposal" BOOLEAN NOT NULL DEFAULT false,
    "partyB_agreedToCurrentProposal" BOOLEAN NOT NULL DEFAULT false,
    "negotiationStatus" "NegotiationStatus" NOT NULL DEFAULT 'PENDING_RECEIVING_INFO',
    "finalizedPayerId" TEXT,
    "paymentTimerDueDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PayerNegotiation_pkey" PRIMARY KEY ("negotiationId")
);

-- CreateTable
CREATE TABLE "PaymentReceivingInfo" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "bankName" TEXT NOT NULL,
    "accountNumber" TEXT NOT NULL,
    "accountHolderName" TEXT NOT NULL,
    "isDefaultForUser" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PaymentReceivingInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "debug_reports" (
    "id" TEXT NOT NULL,
    "report_id" TEXT NOT NULL,
    "user_id" TEXT,
    "type" "debug_report_type" NOT NULL,
    "severity" "debug_report_severity" NOT NULL,
    "status" "debug_report_status" NOT NULL DEFAULT 'NOT_REVIEWED',
    "priority" INTEGER NOT NULL DEFAULT 3,
    "title" VARCHAR(200) NOT NULL,
    "description" TEXT NOT NULL,
    "steps_to_reproduce" TEXT,
    "expected_behavior" TEXT,
    "actual_behavior" TEXT,
    "additional_notes" TEXT,
    "assigned_to" TEXT,
    "assigned_at" TIMESTAMP(3),
    "session_id" TEXT,
    "current_url" TEXT,
    "user_agent" TEXT,
    "viewport_width" INTEGER,
    "viewport_height" INTEGER,
    "diagnostic_data" JSONB,
    "logs" JSONB,
    "user_actions" JSONB,
    "client_timestamp" TIMESTAMP(3) NOT NULL,
    "server_received_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "debug_reports_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "debug_report_tags" (
    "id" TEXT NOT NULL,
    "report_id" TEXT NOT NULL,
    "tag" VARCHAR(50) NOT NULL,
    "tag_origin" "tag_origin" NOT NULL DEFAULT 'USER_DEFINED',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "debug_report_tags_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "debug_report_status_history" (
    "id" TEXT NOT NULL,
    "report_id" TEXT NOT NULL,
    "old_status" "debug_report_status",
    "new_status" "debug_report_status" NOT NULL,
    "changed_by" TEXT,
    "comment" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "debug_report_status_history_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "debug_report_comments" (
    "id" TEXT NOT NULL,
    "report_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "comment" TEXT NOT NULL,
    "is_internal" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "debug_report_comments_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_verificationToken_key" ON "User"("verificationToken");

-- CreateIndex
CREATE UNIQUE INDEX "User_phoneNumber_key" ON "User"("phoneNumber");

-- CreateIndex
CREATE INDEX "Interest_offerId_idx" ON "Interest"("offerId");

-- CreateIndex
CREATE INDEX "Interest_interestedUserId_idx" ON "Interest"("interestedUserId");

-- CreateIndex
CREATE UNIQUE INDEX "Interest_offerId_interestedUserId_key" ON "Interest"("offerId", "interestedUserId");

-- CreateIndex
CREATE UNIQUE INDEX "ChatSession_interestId_key" ON "ChatSession"("interestId");

-- CreateIndex
CREATE INDEX "ChatSession_userOneId_idx" ON "ChatSession"("userOneId");

-- CreateIndex
CREATE INDEX "ChatSession_userTwoId_idx" ON "ChatSession"("userTwoId");

-- CreateIndex
CREATE INDEX "ChatSession_offerId_idx" ON "ChatSession"("offerId");

-- CreateIndex
CREATE INDEX "ChatMessage_chatSessionId_idx" ON "ChatMessage"("chatSessionId");

-- CreateIndex
CREATE INDEX "ChatMessage_senderId_idx" ON "ChatMessage"("senderId");

-- CreateIndex
CREATE INDEX "ChatMessage_transactionId_idx" ON "ChatMessage"("transactionId");

-- CreateIndex
CREATE INDEX "Notification_userId_idx" ON "Notification"("userId");

-- CreateIndex
CREATE INDEX "Notification_userId_isRead_createdAt_idx" ON "Notification"("userId", "isRead", "createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "Transaction_offerId_key" ON "Transaction"("offerId");

-- CreateIndex
CREATE UNIQUE INDEX "Transaction_chatSessionId_key" ON "Transaction"("chatSessionId");

-- CreateIndex
CREATE INDEX "Transaction_currencyAProviderId_idx" ON "Transaction"("currencyAProviderId");

-- CreateIndex
CREATE INDEX "Transaction_currencyBProviderId_idx" ON "Transaction"("currencyBProviderId");

-- CreateIndex
CREATE INDEX "Transaction_agreedFirstPayerId_idx" ON "Transaction"("agreedFirstPayerId");

-- CreateIndex
CREATE INDEX "Transaction_status_idx" ON "Transaction"("status");

-- CreateIndex
CREATE INDEX "Transaction_cancelledByUserId_idx" ON "Transaction"("cancelledByUserId");

-- CreateIndex
CREATE INDEX "Transaction_disputedByUserId_idx" ON "Transaction"("disputedByUserId");

-- CreateIndex
CREATE UNIQUE INDEX "PayerNegotiation_transactionId_key" ON "PayerNegotiation"("transactionId");

-- CreateIndex
CREATE INDEX "PayerNegotiation_partyA_Id_idx" ON "PayerNegotiation"("partyA_Id");

-- CreateIndex
CREATE INDEX "PayerNegotiation_partyB_Id_idx" ON "PayerNegotiation"("partyB_Id");

-- CreateIndex
CREATE INDEX "PayerNegotiation_transactionId_idx" ON "PayerNegotiation"("transactionId");

-- CreateIndex
CREATE INDEX "PaymentReceivingInfo_userId_idx" ON "PaymentReceivingInfo"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "debug_reports_report_id_key" ON "debug_reports"("report_id");

-- CreateIndex
CREATE INDEX "debug_reports_status_idx" ON "debug_reports"("status");

-- CreateIndex
CREATE INDEX "debug_reports_type_idx" ON "debug_reports"("type");

-- CreateIndex
CREATE INDEX "debug_reports_severity_idx" ON "debug_reports"("severity");

-- CreateIndex
CREATE INDEX "debug_reports_assigned_to_idx" ON "debug_reports"("assigned_to");

-- CreateIndex
CREATE INDEX "debug_reports_user_id_idx" ON "debug_reports"("user_id");

-- CreateIndex
CREATE INDEX "debug_reports_created_at_idx" ON "debug_reports"("created_at");

-- CreateIndex
CREATE INDEX "debug_report_tags_tag_idx" ON "debug_report_tags"("tag");

-- CreateIndex
CREATE INDEX "debug_report_tags_tag_origin_idx" ON "debug_report_tags"("tag_origin");

-- CreateIndex
CREATE UNIQUE INDEX "debug_report_tags_report_id_tag_key" ON "debug_report_tags"("report_id", "tag");

-- CreateIndex
CREATE INDEX "debug_report_status_history_report_id_idx" ON "debug_report_status_history"("report_id");

-- CreateIndex
CREATE INDEX "debug_report_comments_report_id_idx" ON "debug_report_comments"("report_id");

-- AddForeignKey
ALTER TABLE "Offer" ADD CONSTRAINT "Offer_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Interest" ADD CONSTRAINT "Interest_offerId_fkey" FOREIGN KEY ("offerId") REFERENCES "Offer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Interest" ADD CONSTRAINT "Interest_interestedUserId_fkey" FOREIGN KEY ("interestedUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatSession" ADD CONSTRAINT "ChatSession_offerId_fkey" FOREIGN KEY ("offerId") REFERENCES "Offer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatSession" ADD CONSTRAINT "ChatSession_userOneId_fkey" FOREIGN KEY ("userOneId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatSession" ADD CONSTRAINT "ChatSession_userTwoId_fkey" FOREIGN KEY ("userTwoId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatSession" ADD CONSTRAINT "ChatSession_interestId_fkey" FOREIGN KEY ("interestId") REFERENCES "Interest"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatMessage" ADD CONSTRAINT "ChatMessage_chatSessionId_fkey" FOREIGN KEY ("chatSessionId") REFERENCES "ChatSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatMessage" ADD CONSTRAINT "ChatMessage_senderId_fkey" FOREIGN KEY ("senderId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_offerId_fkey" FOREIGN KEY ("offerId") REFERENCES "Offer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_chatSessionId_fkey" FOREIGN KEY ("chatSessionId") REFERENCES "ChatSession"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_currencyAProviderId_fkey" FOREIGN KEY ("currencyAProviderId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_currencyBProviderId_fkey" FOREIGN KEY ("currencyBProviderId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_agreedFirstPayerId_fkey" FOREIGN KEY ("agreedFirstPayerId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_cancelledByUserId_fkey" FOREIGN KEY ("cancelledByUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_disputedByUserId_fkey" FOREIGN KEY ("disputedByUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PayerNegotiation" ADD CONSTRAINT "PayerNegotiation_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PayerNegotiation" ADD CONSTRAINT "PayerNegotiation_partyA_Id_fkey" FOREIGN KEY ("partyA_Id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PayerNegotiation" ADD CONSTRAINT "PayerNegotiation_partyB_Id_fkey" FOREIGN KEY ("partyB_Id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PayerNegotiation" ADD CONSTRAINT "PayerNegotiation_partyA_PaymentReceivingInfoId_fkey" FOREIGN KEY ("partyA_PaymentReceivingInfoId") REFERENCES "PaymentReceivingInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PayerNegotiation" ADD CONSTRAINT "PayerNegotiation_partyB_PaymentReceivingInfoId_fkey" FOREIGN KEY ("partyB_PaymentReceivingInfoId") REFERENCES "PaymentReceivingInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentReceivingInfo" ADD CONSTRAINT "PaymentReceivingInfo_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "debug_reports" ADD CONSTRAINT "debug_reports_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "debug_reports" ADD CONSTRAINT "debug_reports_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "debug_report_tags" ADD CONSTRAINT "debug_report_tags_report_id_fkey" FOREIGN KEY ("report_id") REFERENCES "debug_reports"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "debug_report_status_history" ADD CONSTRAINT "debug_report_status_history_report_id_fkey" FOREIGN KEY ("report_id") REFERENCES "debug_reports"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "debug_report_status_history" ADD CONSTRAINT "debug_report_status_history_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "debug_report_comments" ADD CONSTRAINT "debug_report_comments_report_id_fkey" FOREIGN KEY ("report_id") REFERENCES "debug_reports"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "debug_report_comments" ADD CONSTRAINT "debug_report_comments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
