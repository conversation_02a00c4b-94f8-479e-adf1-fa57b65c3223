import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import { createTestingPinia } from '@pinia/testing'
import PaymentReadinessGate from '../PaymentReadinessGate.vue'
import { ReceivingInfoStatus } from '@/types/payerNegotiation'

describe('Payment Persistence Complete Fix Verification', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(PaymentReadinessGate, {
      props: {
        receivingInfoStatus: ReceivingInfoStatus.PENDING_INPUT,
        profileDetails: null,
      },
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })]
      }
    })
  })

  describe('Complete saveToProfile + isDefaultForUser Fix', () => {
    it('should send both saveToProfile and isDefaultForUser when saving new details', async () => {
      // Show new details form
      await wrapper.vm.showNewDetailsForm()
      await nextTick()
      
      // Verify form is shown and saveToProfile is true by default
      expect(wrapper.vm.showForm).toBe(true)
      expect(wrapper.vm.formModel.saveToProfile).toBe(true)
      
      // Fill out the form
      wrapper.vm.formModel.bankName = 'Test Bank'
      wrapper.vm.formModel.accountNumber = '*********'
      wrapper.vm.formModel.accountHolderName = 'John Doe'
      await nextTick()
      
      // Mock form validation to succeed
      wrapper.vm.formRef = {
        validate: vi.fn().mockResolvedValue(true)
      }
      
      // Submit the form by calling handleSubmit directly
      const mockEvent = { preventDefault: vi.fn() } as unknown as Event
      await wrapper.vm.handleSubmit(mockEvent)
      await nextTick()
      
      // Verify the correct payload was emitted with both flags
      const emittedEvents = wrapper.emitted('submit')
      expect(emittedEvents).toBeTruthy()
      expect(emittedEvents?.[0]?.[0]).toEqual({
        bankName: 'Test Bank',
        accountNumber: '*********',
        accountHolderName: 'John Doe',
        saveToProfile: true,
        isDefaultForUser: true, // Critical fix - when saveToProfile is true, isDefaultForUser should also be true
        id: undefined
      })
      
      console.log('✅ SUCCESS: Payload includes both saveToProfile: true AND isDefaultForUser: true')
      console.log('📊 Complete payload:', JSON.stringify(emittedEvents[0][0], null, 2))
    })

    it('should send isDefaultForUser: false when saveToProfile is false', async () => {
      // Show form first
      await wrapper.vm.showNewDetailsForm()
      await nextTick()
      
      // Manually set saveToProfile to false to test the logic
      wrapper.vm.formModel.saveToProfile = false
      wrapper.vm.formModel.bankName = 'Test Bank'
      wrapper.vm.formModel.accountNumber = '*********'
      wrapper.vm.formModel.accountHolderName = 'John Doe'
      await nextTick()

      // Mock form validation
      wrapper.vm.formRef = {
        validate: vi.fn().mockResolvedValue(true)
      }

      // Submit form using handleSubmit
      const mockEvent = { preventDefault: vi.fn() } as unknown as Event
      await wrapper.vm.handleSubmit(mockEvent)
      await nextTick()

      const emittedEvents = wrapper.emitted('submit')
      expect(emittedEvents).toBeTruthy()
      expect(emittedEvents?.[0]?.[0]).toEqual({
        bankName: 'Test Bank',
        accountNumber: '*********',
        accountHolderName: 'John Doe',
        saveToProfile: false,
        isDefaultForUser: false, // When saveToProfile is false, isDefaultForUser should also be false
        id: undefined
      })
      
      console.log('✅ SUCCESS: When saveToProfile is false, isDefaultForUser is also false')
    })

    it('should set saveToProfile to true when editing existing details', async () => {
      // Set up component with existing profile details
      const mockProfileDetails = {
        id: 'existing-id',
        bankName: 'Existing Bank',
        accountNumber: '*********',
        accountHolderName: 'Jane Doe',
        saveToProfile: false
      }

      await wrapper.setProps({
        receivingInfoStatus: ReceivingInfoStatus.PROVIDED,
        profileDetails: mockProfileDetails
      })
      await nextTick()

      // Call showEditDetails - this should set saveToProfile to true
      await wrapper.vm.showEditDetails()
      await nextTick()

      // Verify saveToProfile was set to true for editing
      expect(wrapper.vm.formModel.saveToProfile).toBe(true)
      expect(wrapper.vm.formModel.bankName).toBe('Existing Bank')
      expect(wrapper.vm.formModel.id).toBe('existing-id')

      console.log('✅ SUCCESS: showEditDetails sets saveToProfile to true for editing')
    })
  })

  describe('Backend Integration Expectation', () => {
    it('should demonstrate the expected backend behavior', () => {
      console.log('\n🔍 Expected Backend Behavior:')
      console.log('1. When saveToProfile: true + isDefaultForUser: true -> Payment info is saved to profile AND becomes default')
      console.log('2. When saveToProfile: false + isDefaultForUser: false -> Payment info is used only for this transaction')
      console.log('3. Backend query: User.findUnique({ where: { id }, include: { paymentMethods: { where: { isDefaultForUser: true } } } })')
      console.log('4. If default payment method exists, user doesn\'t see payment form again')
      
      // This test always passes - it's just documentation
      expect(true).toBe(true)
    })
  })
})
