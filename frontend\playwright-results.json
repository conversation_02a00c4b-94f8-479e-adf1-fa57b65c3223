{"config": {"configFile": "C:\\Code\\MUNygo\\frontend\\playwright.config.ts", "rootDir": "C:/Code/MUNygo/frontend/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "playwright-results.json"}], ["junit", {"outputFile": "playwright-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Code/MUNygo/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Code/MUNygo/frontend/tests/e2e", "testIgnore": ["**/node_modules/**", "**/dist/**", "**/build/**"], "testMatch": ["**/*.spec.ts", "**/*.test.ts", "**/*.e2e.ts"], "timeout": 30000}, {"outputDir": "C:/Code/MUNygo/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Code/MUNygo/frontend/tests/e2e", "testIgnore": ["**/node_modules/**", "**/dist/**", "**/build/**"], "testMatch": ["**/*.spec.ts", "**/*.test.ts", "**/*.e2e.ts"], "timeout": 30000}, {"outputDir": "C:/Code/MUNygo/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Small Mobile", "name": "Small Mobile", "testDir": "C:/Code/MUNygo/frontend/tests/e2e", "testIgnore": ["**/node_modules/**", "**/dist/**", "**/build/**"], "testMatch": ["**/*.spec.ts", "**/*.test.ts", "**/*.e2e.ts"], "timeout": 30000}, {"outputDir": "C:/Code/MUNygo/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Code/MUNygo/frontend/tests/e2e", "testIgnore": ["**/node_modules/**", "**/dist/**", "**/build/**"], "testMatch": ["**/*.spec.ts", "**/*.test.ts", "**/*.e2e.ts"], "timeout": 30000}, {"outputDir": "C:/Code/MUNygo/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Desktop Chrome", "name": "Desktop Chrome", "testDir": "C:/Code/MUNygo/frontend/tests/e2e", "testIgnore": ["**/node_modules/**", "**/dist/**", "**/build/**"], "testMatch": ["**/*.spec.ts", "**/*.test.ts", "**/*.e2e.ts"], "timeout": 30000}, {"outputDir": "C:/Code/MUNygo/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome Persian", "name": "Mobile Chrome Persian", "testDir": "C:/Code/MUNygo/frontend/tests/e2e", "testIgnore": ["**/node_modules/**", "**/dist/**", "**/build/**"], "testMatch": ["**/*.spec.ts", "**/*.test.ts", "**/*.e2e.ts"], "timeout": 30000}, {"outputDir": "C:/Code/MUNygo/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Accessibility", "name": "Mobile Accessibility", "testDir": "C:/Code/MUNygo/frontend/tests/e2e", "testIgnore": ["**/node_modules/**", "**/dist/**", "**/build/**"], "testMatch": ["**/*.spec.ts", "**/*.test.ts", "**/*.e2e.ts"], "timeout": 30000}, {"outputDir": "C:/Code/MUNygo/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Slow Network", "name": "Mobile Slow Network", "testDir": "C:/Code/MUNygo/frontend/tests/e2e", "testIgnore": ["**/node_modules/**", "**/dist/**", "**/build/**"], "testMatch": ["**/*.spec.ts", "**/*.test.ts", "**/*.e2e.ts"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments.", "stack": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments."}], "stats": {"startTime": "2025-07-02T22:29:45.252Z", "duration": 112.72600000000011, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}