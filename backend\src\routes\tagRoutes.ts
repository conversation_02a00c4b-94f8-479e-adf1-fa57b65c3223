import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { TagService } from '../services/tagService';
import { TagQueryFiltersSchema, CreatePredefinedTagSchema, UpdatePredefinedTagSchema } from '../types/tagTypes';

export default function createTagRoutes(tagService: TagService) {
  const app = new Hono();

  /**
   * GET /categories - Get all tag categories
   */
  app.get('/categories', async (c) => {
    try {
      const result = await tagService.getCategories();
      return c.json(result);
    } catch (error) {
      console.error('Error fetching tag categories:', error);
      return c.json({ error: 'Failed to fetch tag categories' }, 500);
    }
  });

  /**
   * GET /predefined - Get all predefined tags with optional filtering
   */
  app.get('/predefined', 
    zValidator('query', TagQueryFiltersSchema.partial()),
    async (c) => {
      try {
        const filters = c.req.valid('query');
        const result = await tagService.getPredefinedTags(filters);
        return c.json(result);
      } catch (error) {
        console.error('Error fetching predefined tags:', error);
        return c.json({ error: 'Failed to fetch predefined tags' }, 500);
      }
    }
  );

  /**
   * GET /predefined/:reportType - Get tags for specific report type
   */
  app.get('/predefined/:reportType',
    zValidator('param', z.object({ reportType: z.string() })),
    zValidator('query', z.object({ language: z.enum(['en', 'fa']).optional() })),
    async (c) => {
      try {
        const { reportType } = c.req.valid('param');
        const { language = 'en' } = c.req.valid('query');
        
        const result = await tagService.getTagsForReportType(reportType, language);
        return c.json(result);
      } catch (error) {
        console.error('Error fetching tags for report type:', error);
        return c.json({ error: 'Failed to fetch tags for report type' }, 500);
      }
    }
  );

  /**
   * GET /ai-context - Get tags formatted for AI context
   */
  app.get('/ai-context',
    zValidator('query', z.object({ language: z.enum(['en', 'fa']).optional() })),
    async (c) => {
      try {
        const { language = 'en' } = c.req.valid('query');
        const result = await tagService.getTagsForAiContext(language);
        return c.json({ predefinedTags: result });
      } catch (error) {
        console.error('Error fetching tags for AI context:', error);
        return c.json({ error: 'Failed to fetch tags for AI context' }, 500);
      }
    }
  );

  return app;
}

/**
 * Admin-only tag management routes
 */
export function createAdminTagRoutes(tagService: TagService) {
  const app = new Hono();

  // TODO: Add admin authentication middleware here
  // app.use('*', adminAuthMiddleware);

  /**
   * POST /predefined - Create new predefined tag
   */  app.post('/predefined',
    zValidator('json', CreatePredefinedTagSchema),
    async (c) => {
      try {
        const data = c.req.valid('json');
        
        // Transform the schema data to match service interface
        const serviceData = {
          name: data.tagKey,
          displayName: {
            en: data.defaultLabelEn,
            fa: data.defaultLabelFa || data.defaultLabelEn
          },
          description: data.descriptionEn || data.descriptionFa ? {
            en: data.descriptionEn || '',
            fa: data.descriptionFa || ''
          } : undefined,
          color: data.color,
          weight: data.sortOrder,
          isActive: true,
          isSystem: false
        };
        
        const result = await tagService.createPredefinedTag(serviceData);
        return c.json(result, 201);
      } catch (error) {
        console.error('Error creating predefined tag:', error);
        if (error instanceof Error && error.message.includes('duplicate key')) {
          return c.json({ error: 'Tag key already exists' }, 409);
        }
        return c.json({ error: 'Failed to create predefined tag' }, 500);
      }
    }
  );

  /**
   * PUT /predefined/:id - Update predefined tag
   */
  app.put('/predefined/:id',
    zValidator('param', z.object({ id: z.string().uuid() })),
    zValidator('json', UpdatePredefinedTagSchema.omit({ id: true })),
    async (c) => {
      try {
        const { id } = c.req.valid('param');
        const data = c.req.valid('json');
        
        const result = await tagService.updatePredefinedTag({ ...data, id });
        return c.json(result);
      } catch (error) {
        console.error('Error updating predefined tag:', error);
        return c.json({ error: 'Failed to update predefined tag' }, 500);
      }
    }
  );

  /**
   * DELETE /predefined/:id - Delete (deactivate) predefined tag
   */
  app.delete('/predefined/:id',
    zValidator('param', z.object({ id: z.string().uuid() })),
    async (c) => {
      try {
        const { id } = c.req.valid('param');
        await tagService.deletePredefinedTag(id);
        return c.json({ success: true });
      } catch (error) {
        console.error('Error deleting predefined tag:', error);
        return c.json({ error: 'Failed to delete predefined tag' }, 500);
      }
    }
  );

  return app;
}
