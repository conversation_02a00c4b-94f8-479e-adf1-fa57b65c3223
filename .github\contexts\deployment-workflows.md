# Deployment Workflows for MUNygo

## Development Environment

### Quick Start Scripts

```powershell
# Start development environment
.\dev-start.ps1    # Starts PostgreSQL container + setup instructions

# Stop development environment  
.\dev-stop.ps1     # Cleanly stops all services
```

### Development Servers

- **Backend**: `cd backend && npm run dev` (http://localhost:3000)
- **Frontend**: `cd frontend && npm run dev` (http://localhost:5173)
- **Database**: PostgreSQL on localhost:5433 (Docker container)

### Environment Variables

```env
# Backend (.env)
DATABASE_URL="postgresql://munygo:password@localhost:5433/munygo_dev"
JWT_SECRET="your-jwt-secret"
TWILIO_ACCOUNT_SID="your-twilio-sid"
TWILIO_AUTH_TOKEN="your-twilio-token"
SMTP_HOST="smtp.gmail.com"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Frontend (.env)
VITE_API_BASE_URL="http://localhost:3000"
VITE_SOCKET_URL="http://localhost:3000"
```

## Build Process

### TypeScript Compilation

```powershell
# Backend build - TypeScript compilation
cd backend
npm run build

# Frontend build - Vue-tsc type checking + Vite build
cd frontend  
npm run build
```

### Docker Build

```powershell
# Build Docker images
npm run docker:build    # Available in both frontend/backend

# Build specific service
cd backend
docker build -t munygo-backend .

cd frontend
docker build -t munygo-frontend .
```

## Production Deployment

### Safe Deployment Script

```powershell
# Complete deployment with migration checks
.\deploy-complete.ps1

# Alternative for Linux/macOS
./deploy-production.sh
```

### Docker Compose Deployment

```yaml
# docker-compose.yml
version: '3.8'
services:
  backend:
    build: ./backend
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
    
  frontend:
    build: ./frontend
    environment:
      - VITE_API_BASE_URL=${API_BASE_URL}
    
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=munygo
      - POSTGRES_USER=munygo
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
```

### Migration Workflow

**Critical**: Always handle migrations properly in production

```powershell
# 1. Backup database before deployment
pg_dump $DATABASE_URL > backup-$(date +%Y%m%d).sql

# 2. Deploy new code (without starting services)
git pull origin main
docker-compose build

# 3. Run migrations
cd backend
npm run prisma:deploy

# 4. Start services
docker-compose up -d
```

### Zero-Downtime Deployment

```powershell
# 1. Build new images with versioning
docker build -t munygo-backend:v1.2.0 ./backend
docker build -t munygo-frontend:v1.2.0 ./frontend

# 2. Update docker-compose with new tags
# 3. Rolling update
docker-compose up -d --no-deps backend
docker-compose up -d --no-deps frontend
```

## Database Migration Management

### Development Migrations

```powershell
# Create new migration
cd backend
npm run prisma:migrate
# Enter descriptive migration name: "add_user_profile_fields"

# Reset database (development only)
npm run prisma:reset
```

### Production Migration Checklist

1. **Backup Database**: Always backup before migration
2. **Test Migration**: Run on staging environment first
3. **Check Dependencies**: Ensure all services are compatible
4. **Rollback Plan**: Have rollback strategy ready
5. **Monitor**: Watch for errors after deployment

```powershell
# Production migration workflow
cd backend

# 1. Generate and review migration
npm run prisma:migrate dev --create-only

# 2. Review SQL in prisma/migrations/
# 3. Test on staging database

# 4. Deploy to production
npm run prisma:deploy
```

## Environment-Specific Configurations

### Staging Environment

```yaml
# docker-compose.staging.yml
version: '3.8'
services:
  backend:
    build: ./backend
    environment:
      - NODE_ENV=staging
      - DATABASE_URL=${STAGING_DATABASE_URL}
      - LOG_LEVEL=debug
    
  frontend:
    build: ./frontend
    environment:
      - NODE_ENV=staging
      - VITE_API_BASE_URL=${STAGING_API_URL}
```

### Production Environment

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  backend:
    build: ./backend
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${PROD_DATABASE_URL}
      - LOG_LEVEL=error
    restart: always
    
  frontend:
    build: ./frontend
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=${PROD_API_URL}
    restart: always
```

## Monitoring and Logging

### Application Monitoring

```typescript
// Add to backend services
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'app.log' }),
    new winston.transports.Console()
  ]
});

// Usage in services
logger.info('Transaction created', { 
  transactionId, 
  userId, 
  amount 
});
```

### Health Checks

```typescript
// Add health check endpoint
router.get('/health', async (c) => {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;
    
    return c.json({
      status: 'healthy',
      timestamp: new Date(),
      database: 'connected'
    });
  } catch (error) {
    return c.json({
      status: 'unhealthy',
      error: error.message
    }, 500);
  }
});
```

## Backup and Recovery

### Automated Backups

```bash
#!/bin/bash
# backup.sh - Run as cron job

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="munygo"

# Create backup
pg_dump $DATABASE_URL > "$BACKUP_DIR/backup_$DATE.sql"

# Compress backup
gzip "$BACKUP_DIR/backup_$DATE.sql"

# Keep only last 7 days of backups
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: backup_$DATE.sql.gz"
```

### Recovery Process

```powershell
# 1. Stop application services
docker-compose down

# 2. Restore database
psql $DATABASE_URL < backup_20241230.sql

# 3. Run any pending migrations
cd backend
npm run prisma:deploy

# 4. Restart services
docker-compose up -d
```

## CI/CD Pipeline

### GitHub Actions Workflow

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run Backend Tests
        run: |
          cd backend
          npm install
          npm test
          
      - name: Run Frontend Tests
        run: |
          cd frontend
          npm install
          npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Production
        run: |
          # Deployment script here
          ./deploy-production.sh
```

## Security Considerations

### SSL/TLS Configuration

```nginx
# nginx.conf for HTTPS
server {
    listen 443 ssl;
    server_name munygo.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://frontend:3000;
    }
    
    location /api {
        proxy_pass http://backend:3000;
    }
}
```

### Environment Secrets

- Use environment variables for all secrets
- Never commit secrets to version control
- Use Docker secrets or encrypted environment files
- Rotate secrets regularly (JWT keys, API tokens, etc.)

## Troubleshooting

### Common Deployment Issues

1. **Migration Failures**: Check schema compatibility
2. **Container Startup**: Verify environment variables
3. **Database Connection**: Check connection string and network
4. **Port Conflicts**: Ensure ports are available
5. **Permission Issues**: Check file/directory permissions

### Debug Commands

```powershell
# Check container logs
docker-compose logs backend
docker-compose logs frontend

# Check database connection
docker-compose exec backend npm run prisma:studio

# Verify environment variables
docker-compose exec backend printenv
```
