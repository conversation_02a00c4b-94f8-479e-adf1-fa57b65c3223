# MUNygo Docker Setup Guide

This guide helps you containerize and test your MUNygo application locally before deploying to the cloud.

## Prerequisites

- Docker Desktop installed and running
- Git (for version control)
- PowerShell (Windows) or Bash (Linux/Mac)

## Quick Start

### 1. Test Everything is Working

First, let's make sure your Docker setup works:

```powershell
# Navigate to your project
cd C:\Code\MUNygo

# Run the automated test script
.\docker-test.ps1 start
```

This will:
- Clean up any existing containers
- Build your Docker images
- Start all services (PostgreSQL, Backend, Frontend)
- Check health of all services
- Show logs
- Open the app in your browser

### 2. Manual Step-by-Step Testing

If you prefer to do it manually:

```powershell
# Clean up any existing containers
docker-compose down -v
docker system prune -f

# Build and start all services
docker-compose up --build -d

# Check if services are running
docker-compose ps

# View logs
docker-compose logs -f

# Test the application
# - Frontend: http://localhost
# - Backend API: http://localhost:3000
# - Backend Health: http://localhost:3000/health
```

## Service URLs

- **Frontend (Vue.js)**: http://localhost (port 80)
- **Backend API (Hono)**: http://localhost:3000
- **Database (PostgreSQL)**: localhost:5432
- **Health Check**: http://localhost:3000/health

## Useful Commands

### Development Workflow

```powershell
# Start all services
docker-compose up -d

# Rebuild specific service
docker-compose build backend
docker-compose up backend -d

# View logs for specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres

# Stop all services
docker-compose down

# Stop and remove volumes (fresh start)
docker-compose down -v
```

### Debugging

```powershell
# Check running containers
docker-compose ps

# Execute commands in running container
docker-compose exec backend sh
docker-compose exec postgres psql -U munygo -d munygo

# Check container logs
docker-compose logs backend --tail=100

# Inspect container
docker inspect munygo-backend
```

### Database Operations

```powershell
# Connect to PostgreSQL
docker-compose exec postgres psql -U munygo -d munygo

# Run Prisma migrations
docker-compose exec backend npx prisma migrate deploy

# Generate Prisma client
docker-compose exec backend npx prisma generate
```

## Configuration

### Environment Variables

The Docker setup uses these key environment variables (defined in docker-compose.yml):

- `DATABASE_URL`: PostgreSQL connection string
- `JWT_SECRET`: Secret for JWT tokens
- `FRONTEND_URL`: Frontend URL for CORS
- `NODE_ENV`: Environment (development/production)
- `MOCK_TWILIO_OTP`: Enable mock OTP for testing

### Customizing for Your Environment

1. Copy `.env.docker` to `.env` and modify values:
```powershell
cp .env.docker .env
# Edit .env with your specific values
```

2. Update docker-compose.yml environment variables as needed

## Testing Your Changes

### When You Update Backend Code:

```powershell
# Rebuild and restart backend
docker-compose build backend
docker-compose up backend -d

# Check health
curl http://localhost:3000/health
```

### When You Update Frontend Code:

```powershell
# Rebuild and restart frontend
docker-compose build frontend
docker-compose up frontend -d

# Check the app
# Open http://localhost in browser
```

### When You Update Database Schema:

```powershell
# Generate new migration
cd backend
npx prisma migrate dev --name "your_migration_name"

# Rebuild backend (includes migration)
cd ..
docker-compose build backend
docker-compose up backend -d
```

## Troubleshooting

### Common Issues

1. **Port already in use**:
   ```powershell
   # Stop existing services
   docker-compose down
   # Check what's using the port
   netstat -ano | findstr :3000
   ```

2. **Database connection issues**:
   ```powershell
   # Check PostgreSQL logs
   docker-compose logs postgres
   
   # Verify database is ready
   docker-compose exec postgres pg_isready -U munygo
   ```

3. **Frontend not loading**:
   ```powershell
   # Check nginx logs
   docker-compose logs frontend
   
   # Verify build completed successfully
   docker-compose exec frontend ls -la /usr/share/nginx/html
   ```

4. **Health check failures**:
   ```powershell
   # Check backend health endpoint
   curl http://localhost:3000/health
   
   # Check backend logs
   docker-compose logs backend
   ```

### Complete Reset

If something goes wrong and you want to start fresh:

```powershell
# Nuclear option - remove everything
docker-compose down -v
docker system prune -a -f
docker volume prune -f

# Then rebuild
docker-compose up --build -d
```

## What's Next?

Once you've confirmed everything works locally:

1. **Test all features**: Registration, login, offers, chat, etc.
2. **Performance testing**: Check response times and resource usage
3. **Security review**: Ensure environment variables are properly set
4. **Prepare for cloud deployment**: Choose your hosting provider
5. **Set up CI/CD**: Automate deployments

## Production Deployment

For cloud deployment, you'll need to:

1. Set up environment variables for production
2. Configure domain and SSL certificates
3. Set up monitoring and logging
4. Configure backups for PostgreSQL
5. Set up load balancing if needed

See the main README.md for cloud deployment instructions.

## ✅ SETUP COMPLETION SUMMARY

### Issue Resolution Completed (May 26, 2025)

**Problems Fixed:**
1. ✅ **FRONTEND_URL Environment Variable**: Corrected from `http://localhost:3000` to `http://localhost` in docker-compose.yml
2. ✅ **Prisma Migration Conflicts**: Resolved by removing conflicting migration files and using `npx prisma db push --force-reset`
3. ✅ **Database Schema Deployment**: Successfully deployed all tables using Prisma schema
4. ✅ **Container Startup**: All three containers (postgres, backend, frontend) now start successfully

**Verification Results:**
- ✅ Frontend accessible at: http://localhost
- ✅ Backend health check: http://localhost:3000/health returns HTTP 200
- ✅ Database connectivity: PostgreSQL connected and schema deployed
- ✅ Email service: Registration emails sent successfully via Ethereal Email
- ✅ Email verification links: Now correctly use `http://localhost` instead of `http://localhost:3000`

**Test Registration:**
- ✅ User registration API working: POST /api/auth/register
- ✅ Email verification link format: `http://localhost/verify-email?token=<verification_token>`
- ✅ Ethereal Email preview: https://ethereal.email (check logs for preview URLs)

### Current Configuration

**Environment Variables (docker-compose.yml):**
```yaml
backend:
  environment:
    - FRONTEND_URL=http://localhost  # ✅ CORRECTED
    - NODE_ENV=development
    - MOCK_TWILIO_OTP=true
    # ... other variables
```

**Dockerfile Changes:**
- Backend now uses: `CMD ["sh", "-c", "npx prisma generate && npx prisma db push --accept-data-loss && npm start"]`
- This ensures proper schema deployment without migration conflicts

**Services Status:**
- 🟢 PostgreSQL: Running on port 5432 (internal)
- 🟢 Backend API: Running on port 3000 (http://localhost:3000)
- 🟢 Frontend: Running on port 80 (http://localhost)
- 🟢 Email Service: Ethereal Email configured for development

### Next Steps

1. **Continue Development**: All core services are working properly
2. **Test Features**: Email verification, user registration, authentication
3. **Monitor Logs**: Use `docker-compose logs -f` to watch real-time output
4. **Database Management**: Use `docker exec munygo-backend npx prisma studio` for GUI database access

The Docker environment is now fully operational and ready for development work!
