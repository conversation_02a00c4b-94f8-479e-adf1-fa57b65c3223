# Manual Testing Guide for Payment Persistence Fix

## Overview
This guide will help you verify that the payment persistence fix is working correctly with the actual backend.

## What Was Fixed
- **Before**: Payment info was saved to profile (saveToProfile: true) but NOT marked as default (isDefaultForUser: false)
- **After**: Payment info is saved to profile AND marked as default (isDefaultForUser: true) when user chooses to save

## Backend Logic
When a user logs in, the backend fetches their default payment method using this logic:
```typescript
// From auth.ts
const userWithDefaults = await prisma.user.findUnique({
  where: { id: userId },
  include: {
    paymentMethods: {
      where: { isDefaultForUser: true }
    }
  }
});
```

If `isDefaultForUser: true` exists, the user won't see the payment form.
If `isDefaultForUser: false` (or no payment methods), the user will see the payment form.

## Manual Testing Steps

### Step 1: Start the Application
```powershell
cd C:\Code\MUNygo
.\dev-start-fixed.ps1
```

### Step 2: Test Scenario - New User
1. Register a new user or use an existing user with no default payment methods
2. Create an offer that requires payment information
3. When prompted for payment details, fill out the form
4. **Key Check**: Ensure "Save to Profile" checkbox is checked (should be by default)
5. Submit the form
6. Complete the offer creation
7. **Verification**: Check database to confirm `isDefaultForUser: true` was set

### Step 3: Test Scenario - Returning User
1. Login with the same user from Step 2
2. Try to create another offer that requires payment information
3. **Expected Result**: You should NOT see the payment form - it should use your saved default payment details automatically

### Step 4: Database Verification (Optional)
Check the database to verify the fix:

```sql
-- Check payment methods for your user
SELECT id, bankName, accountNumber, accountHolderName, isDefaultForUser, userId 
FROM PaymentMethod 
WHERE userId = 'your-user-id';
```

You should see:
- At least one record with `isDefaultForUser: true`
- This represents the payment info you saved in Step 2

### Step 5: Test Edge Case - Edit Details
1. When creating an offer, if you have existing payment details, click "Provide new details"
2. Fill out new details and ensure "Save to Profile" is checked
3. Submit
4. **Expected**: The new details become your default (old default gets isDefaultForUser: false)

## Troubleshooting

### If you still see the payment form after saving details:
1. Check the database - ensure `isDefaultForUser: true` exists for your user
2. Check browser console for any errors
3. Verify the backend received the correct payload with both `saveToProfile: true` AND `isDefaultForUser: true`

### Database Query to Check User's Default Payment Method:
```sql
SELECT u.id as userId, u.email, pm.bankName, pm.isDefaultForUser 
FROM User u 
LEFT JOIN PaymentMethod pm ON u.id = pm.userId AND pm.isDefaultForUser = true 
WHERE u.email = '<EMAIL>';
```

### Reset Test Data (if needed):
```sql
-- Remove all payment methods for a user to test from scratch
DELETE FROM PaymentMethod WHERE userId = 'your-user-id';
```

## Expected Behavior Summary

✅ **Before Fix**: Payment saved but user sees form again next time
✅ **After Fix**: Payment saved AND user doesn't see form again next time

The key difference is that `isDefaultForUser: true` is now being sent to the backend when `saveToProfile: true`, making the payment info truly persistent for the user.
