import { describe, it, expect, beforeEach, vi, type MockedFunction } from 'vitest';
import { createTestingPinia, type TestingPinia } from '@pinia/testing';
import { setActivePinia } from 'pinia';
import { useMatchStore } from '@/stores/matchStore';
import { matchService } from '@/services/matchService';
import centralizedSocketManager from '@/services/centralizedSocketManager';
import type { OfferMatch, MatchResponse } from '@/types/api';
import type { 
  MatchFoundPayload, 
  MatchAcceptedPayload,
  MatchDeclinedPayload,
  MatchExpiredPayload 
} from '@/types/socketEvents';

// Track callbacks for testing - must be declared before vi.mock
const mockOnCallbacks: Record<string, Function> = {};

// Mock dependencies
vi.mock('@/services/matchService');
vi.mock('@/services/centralizedSocketManager', () => {
  const mockOn = vi.fn((event: string, callback: Function) => {
    mockOnCallbacks[event] = callback;
    return vi.fn(); // Return unsubscribe function
  });
  
  return {
    default: {
      on: mockOn,
      emit: vi.fn(),
      isConnected: true
    }
  };
});
vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    user: { id: 'user123', email: '<EMAIL>' },
    isAuthenticated: true
  })
}));

vi.mock('@/stores/notificationStore', () => ({
  useNotificationStore: () => ({
    addOrUpdateNotification: vi.fn()
  }),
  FrontendNotificationType: {
    MATCH_FOUND: 'match_found',
    MATCH_ACCEPTED: 'match_accepted',
    MATCH_DECLINED: 'match_declined',
    MATCH_EXPIRED: 'match_expired'
  }
}));

vi.mock('@/composables/useClientLogger', () => ({
  useClientLogger: () => ({
    logInfo: vi.fn(),
    logError: vi.fn(),
    logWarn: vi.fn()
  })
}));

describe('matchStore', () => {
  let pinia: TestingPinia;
  let matchStore: ReturnType<typeof useMatchStore>;
  let mockMatchService: typeof matchService;
  let mockSocketManager: typeof centralizedSocketManager;
  const mockMatch: OfferMatch = {
    id: 'match123',
    matchId: 'match123',
    status: 'PENDING',
    compatibilityScore: 0.955,
    currencyA: 'USD',
    currencyB: 'EUR',
    amountA: 1000,
    amountB: 850,
    rateAToB: 0.85,
    rateBToA: 1.18,
    offerA: {
      id: 'offer1',
      type: 'BUY',
      user: {
        id: 'user1',
        username: 'user1',
        reputationLevel: 3
      }
    },
    offerB: {
      id: 'offer2',
      type: 'SELL',
      user: {
        id: 'user2',
        username: 'user2',
        reputationLevel: 4
      }
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    expiresAt: '2024-01-02T00:00:00Z',
    isExpired: false,
    isCurrentUserInvolved: true,
    canCurrentUserRespond: true
  };

  beforeEach(() => {
    pinia = createTestingPinia({
      createSpy: vi.fn,
      stubActions: false
    });
    setActivePinia(pinia);

    matchStore = useMatchStore();
    mockMatchService = vi.mocked(matchService);
    mockSocketManager = vi.mocked(centralizedSocketManager);

    vi.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      expect(matchStore.matches).toEqual([]);
      expect(matchStore.isLoading).toBe(false);
      expect(matchStore.error).toBe(null);
      expect(matchStore.lastFetchTime).toBe(null);
    });
  });  describe('computed properties', () => {
    beforeEach(() => {
      // Add test matches to store using updateMatch to ensure proper enhancement
      matchStore.updateMatch({ ...mockMatch, id: 'match123', matchId: 'match123', status: 'PENDING', userAId: 'user123', userAResponse: undefined, expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() });
      matchStore.updateMatch({ ...mockMatch, id: 'match2', matchId: 'match2', status: 'BOTH_ACCEPTED', userAId: 'user123', userAResponse: 'ACCEPTED', userBResponse: 'ACCEPTED', expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() });
      matchStore.updateMatch({ ...mockMatch, id: 'match3', matchId: 'match3', status: 'PENDING', userAId: 'user123', userAResponse: undefined, expiresAt: new Date(Date.now() - 1000).toISOString() }); // Expired
      matchStore.updateMatch({ ...mockMatch, id: 'match4', matchId: 'match4', status: 'PARTIAL_ACCEPT', userAId: 'user123', userBId: 'otherUser', userAResponse: undefined, userBResponse: 'ACCEPTED', expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() });
    });    it('should correctly compute pendingMatches', () => {
      const pending = matchStore.pendingMatches;
      expect(pending).toHaveLength(2); // PENDING matches that are not expired
      expect(pending.map(m => m.matchId)).toContain('match123');
      expect(pending.map(m => m.matchId)).toContain('match4');
    });

    it('should correctly compute matchesNeedingResponse', () => {
      const needingResponse = matchStore.matchesNeedingResponse;
      expect(needingResponse).toHaveLength(2); // PENDING and PARTIAL_ACCEPT matches where current user can respond
      expect(needingResponse.map(m => m.matchId)).toContain('match123');
      expect(needingResponse.map(m => m.matchId)).toContain('match4');
    });

    it('should correctly compute activeMatches', () => {
      const active = matchStore.activeMatches;
      expect(active).toHaveLength(3); // PENDING, BOTH_ACCEPTED, and PARTIAL_ACCEPT
    });
  });
  describe('loadMatches', () => {
    it('should fetch matches successfully', async () => {
      const mockResponse = {
        matches: [mockMatch],
        total: 1,
        hasMore: false
      };
      
      (mockMatchService.fetchMatches as MockedFunction<any>)
        .mockResolvedValueOnce(mockResponse);

      await matchStore.loadMatches();

      expect(matchStore.isLoading).toBe(false);
      expect(matchStore.matches).toHaveLength(1);
      expect(matchStore.error).toBe(null);
      expect(matchStore.lastFetchTime).toBeInstanceOf(Date);
    });

    it('should handle fetch errors', async () => {
      const errorMessage = 'Failed to fetch matches';
      (mockMatchService.fetchMatches as MockedFunction<any>)
        .mockRejectedValueOnce(new Error(errorMessage));

      await matchStore.loadMatches();

      expect(matchStore.isLoading).toBe(false);
      expect(matchStore.error).toBe(errorMessage);
      expect(matchStore.matches).toEqual([]);
    });

    it('should set loading state during fetch', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise(resolve => {
        resolvePromise = resolve;
      });

      (mockMatchService.fetchMatches as MockedFunction<any>)
        .mockReturnValueOnce(promise);

      const fetchPromise = matchStore.loadMatches();
      
      // Check loading state is true during fetch
      expect(matchStore.isLoading).toBe(true);

      // Resolve the promise
      resolvePromise!({ matches: [], total: 0, hasMore: false });
      await fetchPromise;

      expect(matchStore.isLoading).toBe(false);
    });
  });

  describe('acceptMatch', () => {
    beforeEach(() => {
      matchStore.matches = [mockMatch];
    });    it('should accept match successfully', async () => {
      // Ensure match is in store before calling acceptMatch
      matchStore.matches = [mockMatch];
      
      const mockResponse: MatchResponse = {
        success: true,
        match: { ...mockMatch, status: 'PARTIAL_ACCEPT' },
        message: 'Match accepted',
        chatSessionId: 'chat123',
        transactionId: 'tx123'
      };

      (mockMatchService.acceptMatch as MockedFunction<any>)
        .mockResolvedValueOnce(mockResponse);

      const result = await matchStore.acceptMatch('match123');

      expect(result.success).toBe(true);
      expect(matchStore.error).toBe(null);
      
      // Should update match in store
      const updatedMatch = matchStore.matches.find(m => m.matchId === 'match123');
      expect(updatedMatch?.status).toBe('PARTIAL_ACCEPT');
    });it('should handle accept errors', async () => {
      const errorMessage = 'Failed to accept match';
      (mockMatchService.acceptMatch as MockedFunction<any>)
        .mockRejectedValueOnce(new Error(errorMessage));

      try {
        await matchStore.acceptMatch('match123');
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(matchStore.error).toBe(errorMessage);
      }
    });

    it('should handle match not found', async () => {
      (mockMatchService.acceptMatch as MockedFunction<any>)
        .mockResolvedValueOnce(undefined);

      try {
        await matchStore.acceptMatch('nonexistent');
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('declineMatch', () => {
    beforeEach(() => {
      matchStore.matches = [mockMatch];
    });    it('should decline match successfully', async () => {
      // Ensure match is in store before calling declineMatch
      matchStore.matches = [mockMatch];
      
      const mockResponse: MatchResponse = {
        success: true,
        match: { ...mockMatch, status: 'DECLINED' },
        message: 'Match declined'
      };

      (mockMatchService.declineMatch as MockedFunction<any>)
        .mockResolvedValueOnce(mockResponse);

      const result = await matchStore.declineMatch('match123', 'Not interested');

      expect(result.success).toBe(true);
      expect(matchStore.error).toBe(null);
      
      // Should update match in store
      const updatedMatch = matchStore.matches.find(m => m.matchId === 'match123');
      expect(updatedMatch?.status).toBe('DECLINED');
    });it('should handle decline errors', async () => {
      const errorMessage = 'Failed to decline match';
      (mockMatchService.declineMatch as MockedFunction<any>)
        .mockRejectedValueOnce(new Error(errorMessage));

      try {
        await matchStore.declineMatch('match123', 'Not interested');
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(matchStore.error).toBe(errorMessage);
      }
    });
  });
  describe('socket event handling', () => {
    beforeEach(() => {
      // Initialize socket listeners
      matchStore.attachSocketListeners();
    });    it('should handle MATCH_FOUND event', () => {
      const payload: MatchFoundPayload = {
        matchId: 'match123',
        match: {
          id: 'match123',
          matchId: 'match123',
          expiresAt: '2024-01-02T00:00:00Z',
          compatibilityScore: 95,
          offerA: {
            id: 'offer1',
            type: 'SELL',
            currencyPair: 'USD-EUR',
            amount: 1000,
            baseRate: 0.85,
            user: { id: 'user1', username: 'John', reputationLevel: 3 }
          },
          offerB: {
            id: 'offer2', 
            type: 'BUY',
            currencyPair: 'EUR-USD',
            amount: 850,
            baseRate: 1.18,
            user: { id: 'user2', username: 'Jane', reputationLevel: 4 }
          }
        },
        message: 'New match found!'
      };

      // Call the registered callback
      const callback = mockOnCallbacks['MATCH_FOUND'];
      if (callback) {
        callback(payload);
      }

      // Should add match to store
      expect(matchStore.matches.length).toBeGreaterThan(0);
    });    it('should handle MATCH_ACCEPTED event', () => {
      matchStore.matches = [mockMatch];

      const payload: MatchAcceptedPayload = {
        matchId: 'match123',
        userId: 'user1',
        status: 'PARTIAL_ACCEPT',
        match: { 
          ...mockMatch, 
          status: 'PARTIAL_ACCEPT',
          userAResponse: 'ACCEPTED'
        },
        message: 'Match accepted by user'
      };

      // Call the registered callback
      const callback = mockOnCallbacks['MATCH_ACCEPTED'];
      if (callback) {
        callback(payload);
      }

      // Should update match status
      const updatedMatch = matchStore.matches.find(m => m.matchId === 'match123');
      expect(updatedMatch?.status).toBe('PARTIAL_ACCEPT');
    });    it('should handle MATCH_DECLINED event', () => {
      matchStore.matches = [mockMatch];

      const payload: MatchDeclinedPayload = {
        matchId: 'match123',
        userId: 'user1',
        reason: 'Not interested',
        message: 'Match declined by user'
      };

      // Call the registered callback
      const callback = mockOnCallbacks['MATCH_DECLINED'];
      if (callback) {
        callback(payload);
      }

      // Should update match status
      const updatedMatch = matchStore.matches.find(m => m.matchId === 'match123');
      expect(updatedMatch?.status).toBe('DECLINED');
    });    it('should handle MATCH_EXPIRED event', () => {
      matchStore.matches = [mockMatch];

      const payload: MatchExpiredPayload = {
        matchId: 'match123',
        expiredAt: '2024-01-02T00:00:00Z',
        message: 'Match has expired'
      };

      // Call the registered callback
      const callback = mockOnCallbacks['MATCH_EXPIRED'];
      if (callback) {
        callback(payload);
      }

      // Should mark match as expired
      const expiredMatch = matchStore.matches.find(m => m.matchId === 'match123');
      expect(expiredMatch?.status).toBe('EXPIRED');
    });
  });
  describe('cleanup', () => {
    it('should properly cleanup socket listeners', () => {
      // Initialize and then cleanup
      matchStore.attachSocketListeners();
      matchStore.detachSocketListeners();

      // Should have attached listeners
      expect(matchStore.matches).toBeDefined();
    });

    it('should not fail when cleaning up without initialization', () => {
      expect(() => matchStore.detachSocketListeners()).not.toThrow();
    });
  });
  describe('error handling', () => {
    it('should clear errors when calling clearError', () => {
      matchStore.error = 'Some error';
      matchStore.error = null; // Simulate clearing
      expect(matchStore.error).toBe(null);
    });

    it('should handle network errors gracefully', async () => {
      const networkError = new Error('Network Error');
      (mockMatchService.fetchMatches as MockedFunction<any>)
        .mockRejectedValueOnce(networkError);

      await matchStore.loadMatches();

      expect(matchStore.error).toBe('Network Error');
      expect(matchStore.isLoading).toBe(false);
    });
  });

  describe('data consistency', () => {
    it('should prevent duplicate matches in store', () => {
      // Add initial match
      matchStore.updateMatch(mockMatch);
      expect(matchStore.matches).toHaveLength(1);

      // Add same match again
      matchStore.updateMatch(mockMatch);
      expect(matchStore.matches).toHaveLength(1);
    });

    it('should update existing match when receiving updated data', () => {
      matchStore.updateMatch(mockMatch);
      
      const updatedMatch = { ...mockMatch, status: 'PARTIAL_ACCEPT' as const };
      matchStore.updateMatch(updatedMatch);

      expect(matchStore.matches).toHaveLength(1);
      expect(matchStore.matches[0].status).toBe('PARTIAL_ACCEPT');
    });
  });
  describe('match enhancement logic', () => {
    it('should allow current user to respond when match status is PENDING', () => {
      const pendingMatch = {
        ...mockMatch,
        status: 'PENDING' as const,
        userAId: 'user123', // Current user (from auth store mock)
        userAResponse: undefined,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
      };

      matchStore.updateMatch(pendingMatch);
      
      const enhancedMatch = matchStore.matches[0];
      expect(enhancedMatch.canCurrentUserRespond).toBe(true);
      expect(enhancedMatch.isCurrentUserInvolved).toBe(true);
    });

    it('should allow current user to respond when match status is PARTIAL_ACCEPT and user has not responded', () => {
      const partialAcceptMatch = {
        ...mockMatch,
        status: 'PARTIAL_ACCEPT' as const,
        userAId: 'user123', // Current user (from auth store mock)
        userBId: 'otherUser',
        userAResponse: undefined, // Current user has not responded
        userBResponse: 'ACCEPTED', // Other user has accepted
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      };

      matchStore.updateMatch(partialAcceptMatch);
      
      const enhancedMatch = matchStore.matches[0];
      expect(enhancedMatch.canCurrentUserRespond).toBe(true);
      expect(enhancedMatch.isCurrentUserInvolved).toBe(true);
      expect(enhancedMatch.currentUserResponse).toBeUndefined();
      expect(enhancedMatch.otherUserResponse).toBe('ACCEPTED');
    });

    it('should not allow current user to respond when they have already responded', () => {
      const respondedMatch = {
        ...mockMatch,
        status: 'PARTIAL_ACCEPT' as const,
        userAId: 'user123', // Current user (from auth store mock)
        userBId: 'otherUser',
        userAResponse: 'ACCEPTED', // Current user has already responded
        userBResponse: undefined,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      };

      matchStore.updateMatch(respondedMatch);
      
      const enhancedMatch = matchStore.matches[0];
      expect(enhancedMatch.canCurrentUserRespond).toBe(false);
      expect(enhancedMatch.currentUserResponse).toBe('ACCEPTED');
    });

    it('should not allow current user to respond when match is expired', () => {
      const expiredMatch = {
        ...mockMatch,
        status: 'PARTIAL_ACCEPT' as const,
        userAId: 'user123', // Current user (from auth store mock)
        userAResponse: undefined,
        expiresAt: new Date(Date.now() - 1000).toISOString() // 1 second ago (expired)
      };

      matchStore.updateMatch(expiredMatch);
      
      const enhancedMatch = matchStore.matches[0];
      expect(enhancedMatch.canCurrentUserRespond).toBe(false);
      expect(enhancedMatch.isExpired).toBe(true);
    });

    it('should not allow current user to respond when match status is BOTH_ACCEPTED', () => {
      const bothAcceptedMatch = {
        ...mockMatch,
        status: 'BOTH_ACCEPTED' as const,
        userAId: 'user123', // Current user (from auth store mock)
        userAResponse: 'ACCEPTED',
        userBResponse: 'ACCEPTED',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      };

      matchStore.updateMatch(bothAcceptedMatch);
      
      const enhancedMatch = matchStore.matches[0];
      expect(enhancedMatch.canCurrentUserRespond).toBe(false);
    });
  });
});
