# Enhanced Transaction Flow & First Payer Designation - Feature Overview & Status

**Date of Summary:** May 24, 2025

## 1. Feature Overview

This document outlines the "Enhanced Transaction Flow" feature for the MUNygo project, a multi-stage, guided process for peer-to-peer currency exchanges within the application's chat interface. It includes explicit user agreements on terms, a robust "First Payer Designation" mechanism with a "Payment Readiness Gate," timed payment windows, declaration and confirmation of payments (with optional tracking numbers), a persistent transaction history log, and integrated notifications.

**Key Objectives:**
*   Enhance user trust, security, and clarity in P2P transactions.
*   Ensure both parties are prepared for the transaction by confirming payment receiving information upfront.
*   Provide a clear and fair mechanism for deciding who pays first.
*   Keep users informed throughout the transaction lifecycle via in-app and real-time notifications.
*   Maintain a verifiable log of all transaction stages.

## 2. Core Components & Flow

The transaction process is envisioned as follows:

1.  **Initial Offer & Chat:** An offer leads to a chat session where parties decide to transact.
2.  **Transaction Initialization:** A `Transaction` record is created, linking to the chat session and offer details.
3.  **Agree to Terms:** Both parties must agree to the fundamental terms of the exchange (e.g., amounts, currencies).
4.  **First Payer Designation & Payment Readiness Gate:**
    *   **Initiation:** The payer negotiation process begins.
    *   **Payment Readiness Gate:** Each party must provide or confirm their payment *receiving* information for the currency they are due to receive. This is a prerequisite for participating in the designation of the first payer. The system securely handles this information.
    *   **Negotiation:** Once both parties have passed the readiness gate, they can propose who should make the first payment. The system may offer an initial recommendation.
    *   **Agreement:** Parties agree on a `finalizedPayerId`.
5.  **Timed Payment Window (Payer 1):** Once the first payer is finalized, a **countdown timer** starts for them to make their payment. The recipient's payment details (from the readiness gate) are shown to the payer.
6.  **Declare Payment (Payer 1):** The first payer declares they have made the payment, optionally providing a tracking number.
7.  **Timed Confirmation Window (Payer 2):** After Payer 1 declares payment, an **elapsing timer** starts for Payer 2 to confirm receipt.
8.  **Confirm Receipt (Payer 2):** The second party confirms receipt of the payment.
9.  **Timed Payment Window (Payer 2):** Upon Payer 2's confirmation, a **countdown timer** starts for the second payer (who was Payer 1 in the first leg) to make their payment.
10. **Declare Payment (Payer 2):** The second payer declares their payment.
11. **Timed Confirmation Window (Payer 1):** After Payer 2 declares payment, an **elapsing timer** starts for Payer 1 to confirm receipt.
12. **Confirm Receipt (Payer 1):** The first party (who was Payer 2 in the first leg) confirms receipt.
13. **Transaction Completion:** The transaction is marked as completed.
14. **Transaction History Log:** All significant events and state changes are logged.
15. **Cancellation/Dispute:** Mechanisms are available for users to cancel (under specific conditions) or dispute the transaction.

## 2.1 Transaction Flow Diagram

```mermaid
graph TD
    A[Start: Offer & Chat] --> B(Transaction Initialization);
    B --> C{Agree to Terms?};
    C -- Both Agree --> D(Payer Negotiation Initiation);

    subgraph "Negotiation Process"
        D --> DR("System Recommends Payer (e.g., Payer X)");

        DR --> PA_Gate["Party A: Payment Readiness Gate (Provide Receiving Info)"];
        DR --> PB_Gate["Party B: Payment Readiness Gate (Provide Receiving Info)"];

        PA_Gate -- Info Provided --> PA_Ready["Party A: Ready (Sees Rec, Can Propose/Agree)"];
        PB_Gate -- Info Provided --> PB_Ready["Party B: Ready (Sees Rec, Can Propose/Agree)"];

        PA_Ready --> NegotiationDecision{"Parties Negotiate/Agree on First Payer"};
        PB_Ready --> NegotiationDecision;
        DR --> NegotiationDecision;

        NegotiationDecision -- Agreement Reached --> K(First Payer Finalized);
    end

    K --> L["Payer 1: Timed Payment Window (Countdown)"];
    L --> M[Payer 1 Declares Payment];
    M --> N["Payer 2: Timed Confirmation Window (Elapsing)"];
    N -- Payment Confirmed --> O["Payer 2: Timed Payment Window (Countdown)"];
    O --> P[Payer 2 Declares Payment];
    P --> Q["Payer 1: Timed Confirmation Window (Elapsing)"];
    Q -- Payment Confirmed --> R(Transaction Completed);

    subgraph "Payment Leg 1"
        L; M; N;
    end
    subgraph "Payment Leg 2"
        O; P; Q;
    end

    C -- "Disagreement/Timeout" --> X(Flow Stalled/Cancelled);
    NegotiationDecision -- "Disagreement/Timeout" --> X;
    PA_Gate -- "Timeout/Leaves" --> X;
    PB_Gate -- "Timeout/Leaves" --> X;

    L -- "Timeout/Issue" --> Y{Cancel/Dispute?};
    M -- "Issue" --> Y;
    N -- "Timeout/Issue" --> Y;
    O -- "Timeout/Issue" --> Y;
    P -- "Issue" --> Y;
    Q -- "Timeout/Issue" --> Y;
    Y -- Cancel --> Z(Transaction Cancelled);
    Y -- Dispute --> ZD(Transaction Disputed);

    S[Transaction History Log Updated Throughout];
    B --> S; C --> S; D --> S; K --> S; M --> S; N --> S; P --> S; Q --> S; R --> S; X --> S; Z --> S; ZD --> S;
```

## 3. Implementation Status (as of May 2025)


The enhanced transaction flow is **substantially implemented and functional** in both backend and frontend. Below is a due diligence status as of May 24, 2025:

### Core Transaction Flow Stages

* **Initial Transaction Setup:**
    * **Backend:** [DONE] — `Transaction` model, creation logic, and API endpoints are implemented.
    * **Frontend:** [DONE] — Transaction is initialized and loaded in the UI.

* **Agree to Terms:**
    * **Backend:** [DONE] — Agreement logic, state tracking, and notifications are implemented.
    * **Frontend:** [DONE] — UI for agreeing to terms is present in `TransactionFlowCardV3.vue`.

* **First Payer Designation & Payment Readiness Gate:**
    * **Backend:** [DONE] — `PayerNegotiation` model, service (`payerNegotiationService.ts`), API routes, and all related logic are implemented. System recommendation logic is present. Real-time updates via Socket.IO are functional.
    * **Frontend:** [DONE] — `PaymentReadinessGate.vue` and negotiation UI in `TransactionFlowCardV3.vue` are fully integrated. Pinia store (`payerNegotiation.ts`) manages negotiation state. Users can independently provide payment info and participate in negotiation as soon as their info is provided. Real-time updates are handled.

* **Declare Payment (Payer 1 & Payer 2):**
    * **Backend:** [DONE] — Declaration logic, state updates, and notifications are implemented.
    * **Frontend:** [DONE] — UI for declaring payment is present and functional.

* **Confirm Receipt (Payer 1 & Payer 2):**
    * **Backend:** [DONE] — Confirmation logic, state updates, and notifications are implemented.
    * **Frontend:** [DONE] — UI for confirming receipt is present and functional.

* **Transaction Completion:**
    * **Backend:** [DONE] — Completion logic and state updates are implemented.
    * **Frontend:** [DONE] — UI reflects completed state.

* **Cancellation & Dispute:**
    * **Backend:** [DONE] — Cancellation and dispute logic, state updates, and notifications are implemented.
    * **Frontend:** [DONE] — UI for cancel/dispute is present and functional.

### Supporting Features

* **Timed Payment Windows (Countdown) & Confirmation Windows (Elapsed):**
    * **Backend:** [DONE] — Timers are set and tracked in the model and service logic.
    * **Frontend:** [DONE] — Countdown and elapsed timers are displayed in `TransactionFlowCardV3.vue`.

* **Visual Stage Progression:**
    * **Frontend:** [DONE] — `TransactionFlowCardV3.vue` uses Naive UI `NSteps` for clear stage progression.

* **Integrated Notifications (Real-time & In-app):**
    * **Backend:** [DONE] — All relevant events emit notifications and Socket.IO events.
    * **Frontend:** [DONE] — `notificationStore.ts` and UI display notifications and update in real time.

* **Socket.IO Real-time Updates:**
    * **Backend & Frontend:** [DONE] — All transaction and negotiation state changes are pushed and handled in real time.

* **Persistent Transaction History Log:**
    * **Backend:** [DONE] — All relevant timestamps are stored in the `Transaction` model.
    * **Frontend:** [STARTED] — `TransactionTimelineLog.vue` renders timeline items based on transaction timestamps. **Comprehensiveness can be improved** (e.g., more granular event logging, copy-to-clipboard for tracking numbers is still pending).

* **Testing:**
    * **Backend:** [PENDING] — Unit and integration tests for transaction and negotiation services/routes are not comprehensive.
    * **Frontend:** [PENDING] — Unit tests for `TransactionFlowCardV3.vue`, `transactionStore.ts`, and negotiation logic are not comprehensive. E2E tests are not yet implemented.

* **UI/UX Refinements:**
    * [ONGOING] — Some enhancements (e.g., copy-to-clipboard for tracking numbers, more detailed timeline log, seamless navigation from notifications) are still in progress or planned.

### Summary Table

| Feature/Stage                                 | Backend | Frontend | Notes |
|-----------------------------------------------|---------|----------|-------|
| Transaction Setup & Agreement                 |   ✅    |    ✅    |       |
| Payer Negotiation & Readiness Gate            |   ✅    |    ✅    |       |
| Payment Declaration & Confirmation            |   ✅    |    ✅    |       |
| Timers (Countdown/Elapsed)                    |   ✅    |    ✅    |       |
| Real-time Updates (Socket.IO)                 |   ✅    |    ✅    |       |
| Notifications (In-app/Real-time)              |   ✅    |    ✅    |       |
| Transaction Timeline Log                      |   ✅    |  🟡STARTED| Needs more granular event logging, copy-to-clipboard pending |
| Cancellation/Dispute                          |   ✅    |    ✅    |       |
| Unit/Integration/E2E Testing                  |  🟡PENDING| 🟡PENDING| Not comprehensive |
| UI/UX Refinements                            |  🟡ONGOING| 🟡ONGOING| Copy-to-clipboard, timeline, notification navigation |

Legend: ✅ = Complete, 🟡 = Started/Ongoing/Pending

## 4. Key Technical Components

*   **Backend (`backend/src/`)**
    *   **Prisma Models:** `Transaction`, `TransactionStatus`, `PayerNegotiation`, `PayerNegotiationStatus`, `ReceivingInfoStatus`, `PaymentReceivingInfo`.
    *   **Services:** `transactionService.ts`, `payerNegotiationService.ts`, `notificationService.ts`.
    *   **Routes:** `routes/transactionRoutes.ts`, `routes/payerNegotiationRoutes.ts`.
    *   **Socket Events (via `index.ts` and services):**
        *   `TRANSACTION_STATUS_UPDATED` (Payload: full `Transaction` object)
        *   `NEGOTIATION_STATE_CHANGE` (Payload: full `PayerNegotiation` object)
        *   `RECEIVING_INFO_STATUS_UPDATED` (Payload: `{ negotiationId, userId, status }`)
        *   `NEW_NOTIFICATION` (Payload: notification object for specific user)
        *   `CHAT_MESSAGE_RECEIVE` (Used for system messages in chat regarding transaction events)

*   **Frontend (`frontend/src/`)**
    *   **Core UI Component:** `components/TransactionFlowCard.vue` (likely `TransactionFlowCardV3.vue` or similar, managing the overall flow display).
    *   **Sub-Components:**
        *   `components/TransactionTimelineLog.vue`
        *   `components/PaymentReadinessGate.vue` (or integrated logic within `TransactionFlowCard.vue`)
    *   **Stores (Pinia):** `stores/transactionStore.ts`, `stores/payerNegotiationStore.ts`, `stores/notificationStore.ts`.
    *   **API Services:** `services/transactionApiService.ts`, `services/payerNegotiationApiService.ts` (or combined).
    *   **Socket Handling:** `services/socketService.ts` (listens to backend events and updates stores).

## 5. API Endpoint Summary (Consolidated)

All endpoints are assumed to be protected by authentication middleware.

*   **Transaction Lifecycle:**
    *   `GET /api/transactions/:chatSessionId`: Fetch transaction by chat session ID.
    *   `POST /api/transactions/:id/agree-terms`: User agrees to transaction terms.
    *   `POST /api/transactions/:id/declare-payment`: User declares payment made (Body: `{ trackingNumber?: string }`).
    *   `POST /api/transactions/:id/confirm-receipt`: User confirms payment received.
    *   `POST /api/transactions/:id/cancel`: User cancels transaction (Body: `{ reason: string }`).
    *   `POST /api/transactions/:id/dispute`: User disputes transaction (Body: `{ reason: string }`).

*   **Payer Negotiation:**
    *   `POST /api/transactions/:transactionId/payer-negotiation/init`: Initialize or retrieve the negotiation state for a transaction.
    *   `GET /api/transactions/:transactionId/payer-negotiation`: Get current negotiation state.
    *   `POST /api/transactions/:transactionId/payer-negotiation/receiving-info`: Submit/confirm payment receiving information (Body: e.g., `{ infoType: string, details: object, saveToProfile?: boolean }`).
    *   `POST /api/transactions/:transactionId/payer-negotiation/propose`: Propose a first payer (Body: `{ proposedPayerId: string }`).
    *   `POST /api/transactions/:transactionId/payer-negotiation/agree`: Agree to the current proposal.

## 6. Next Steps & Focus Areas

*   **Comprehensive Testing:**
    *   Backend: Unit tests for `transactionService.ts` and `payerNegotiationService.ts`.
    *   Frontend: Unit tests for `TransactionFlowCard.vue`, `payerNegotiationStore.ts`, `transactionStore.ts`.
    *   API Integration Tests for all transaction and negotiation endpoints.
    *   End-to-End (E2E) testing simulating the full flow with two users.
*   **Refinement of `TransactionTimelineLog.vue`:** Ensure all relevant events are logged and displayed clearly.
*   **Notification Navigation:** Ensure clicking a global notification (from `notificationStore`) navigates directly to the relevant transaction/chat.
*   **UI Enhancements:** Implement "copy to clipboard" for tracking numbers and other relevant details.
*   **Address Open Questions:** Review and resolve items listed in "Potential Challenges & Open Questions."

## 7. Potential Challenges & Open Questions (Consolidated)

*   **State Management Complexity (Frontend):** Ensuring `TransactionFlowCard.vue` and associated stores manage complex states robustly.
*   **Timer Accuracy & Synchronization:** Maintaining accurate and synchronized timers across clients and server, especially considering network latency.
*   **Defining Timer Durations:** Finalizing appropriate default durations for payment windows.
*   **Dispute Resolution Mechanism:** Current implementation is basic; a full dispute resolution system is beyond MVP.
*   **Atomicity of Backend Operations:** Ensuring complex state changes in the backend are atomic (e.g., when confirming receipt and potentially creating a new payment expectation).
*   **Initial Transaction Trigger Flow:** Clarifying the exact UI/UX flow for how a chat conversation transitions into an initiated transaction.
*   **Payment Receiving Information Security:**
    *   How are sensitive payment receiving details (bank info, crypto addresses) stored and managed securely if `saveToProfile` is true? Does a sufficiently secure mechanism exist, or does it need to be built/enhanced?
*   **System Recommended Payer Logic (for Payer Negotiation):**
    *   What is the business logic for `systemRecommendedPayerId`? (e.g., offer creator, random, based on transaction type, user reputation). This needs to be defined for backend implementation if not already.
*   **Definition of "Payment Receiving Information":**
    *   What specific pieces of information are required for each supported payment method/currency type? This impacts UI forms and backend validation/storage.
*   **Payer Negotiation Expiration:**
    *   Are there conditions or a timer for the negotiation process itself to expire if parties don't agree or provide information?
*   **User Experience for Counter-Proposals (Payer Negotiation):**
    *   How are counter-proposals explicitly handled in the UI and backend logic? Does a new proposal simply override the previous one?
*   **Integration between `PayerNegotiation` and `Transaction` Models:** Ensuring status fields and foreign key relationships are correctly managed and reflect the overall process state.
