import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createPinia, setActivePinia } from 'pinia';
import { useAuthStore } from '@/stores/auth';
import centralizedSocketManager from '../centralizedSocketManager';

// Mock socket.io-client
const mockSocket = {
  connected: false,
  id: 'mock-socket-id',
  on: vi.fn(),
  emit: vi.fn(),
  disconnect: vi.fn(),
  io: {
    engine: {
      transport: {
        name: 'websocket'
      }
    }
  }
};

const mockIo = vi.fn(() => mockSocket);

vi.mock('socket.io-client', () => ({
  io: mockIo
}));

// Mock stores
vi.mock('@/stores/connection', () => ({
  useConnectionStore: () => ({
    setConnected: vi.fn(),
    setDisconnected: vi.fn(),
    setReconnecting: vi.fn(),
    setTransportType: vi.fn(),
    resetReconnectAttempts: vi.fn(),
  })
}));

describe('CentralizedSocketManager Reconnection', () => {
  let authStore: any;

  beforeEach(() => {
    setActivePinia(createPinia());
    authStore = useAuthStore();
    vi.clearAllMocks();
    
    // Reset socket manager state
    (centralizedSocketManager as any).socket = null;
    (centralizedSocketManager as any).initializationPromise = null;
    (centralizedSocketManager as any).lastAuthToken = null;
    (centralizedSocketManager as any).authErrorCount = 0;
    (centralizedSocketManager as any).isAuthenticationError = false;
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('Token Change Detection', () => {
    it('should detect token changes and force reconnection', async () => {
      // Set initial token
      authStore.token = 'old-token';
      (centralizedSocketManager as any).lastAuthToken = 'old-token';
      
      // Mock connected socket
      mockSocket.connected = true;
      (centralizedSocketManager as any).socket = mockSocket;

      // Change token
      authStore.token = 'new-token';

      // Initialize with new token
      const initPromise = centralizedSocketManager.initializeSocket();

      // Should detect token change and force disconnect
      expect(mockSocket.disconnect).toHaveBeenCalled();
      expect((centralizedSocketManager as any).socket).toBeNull();
    });

    it('should clear initialization promise when token changes', async () => {
      // Set up existing initialization promise
      authStore.token = 'old-token';
      (centralizedSocketManager as any).lastAuthToken = 'old-token';
      (centralizedSocketManager as any).initializationPromise = Promise.resolve(mockSocket);

      // Change token
      authStore.token = 'new-token';

      // Initialize with new token
      await centralizedSocketManager.initializeSocket();

      // Should have cleared the old promise
      expect((centralizedSocketManager as any).initializationPromise).toBeNull();
    });
  });

  describe('Authentication Error Handling', () => {
    it('should reset auth error state when token changes', async () => {
      // Set up auth error state
      (centralizedSocketManager as any).authErrorCount = 2;
      (centralizedSocketManager as any).isAuthenticationError = true;
      (centralizedSocketManager as any).lastAuthToken = 'old-token';

      // Change token
      authStore.token = 'new-token';

      // Initialize with new token
      await centralizedSocketManager.initializeSocket();

      // Should have reset auth error state
      expect((centralizedSocketManager as any).authErrorCount).toBe(0);
      expect((centralizedSocketManager as any).isAuthenticationError).toBe(false);
    });
  });

  describe('Socket Ready State', () => {
    it('should correctly report ready state', () => {
      // Not ready when no socket
      expect(centralizedSocketManager.isReady()).toBe(false);

      // Not ready when socket exists but not connected
      mockSocket.connected = false;
      (centralizedSocketManager as any).socket = mockSocket;
      expect(centralizedSocketManager.isReady()).toBe(false);

      // Ready when socket is connected
      mockSocket.connected = true;
      expect(centralizedSocketManager.isReady()).toBe(true);
    });

    it('should wait for ready state with timeout', async () => {
      vi.useFakeTimers();

      // Start waiting
      const readyPromise = centralizedSocketManager.waitForReady(1000);

      // Simulate socket becoming ready after 500ms
      setTimeout(() => {
        mockSocket.connected = true;
        (centralizedSocketManager as any).socket = mockSocket;
      }, 500);

      vi.advanceTimersByTime(500);
      
      const result = await readyPromise;
      expect(result).toBe(true);

      vi.useRealTimers();
    });

    it('should timeout when socket never becomes ready', async () => {
      vi.useFakeTimers();

      const readyPromise = centralizedSocketManager.waitForReady(1000);

      vi.advanceTimersByTime(1100);
      
      const result = await readyPromise;
      expect(result).toBe(false);

      vi.useRealTimers();
    });
  });

  describe('Force Reconnection', () => {
    it('should reset auth error state on force reconnect', async () => {
      // Set up auth error state
      (centralizedSocketManager as any).authErrorCount = 3;
      (centralizedSocketManager as any).isAuthenticationError = true;

      // Set up auth token
      authStore.token = 'test-token';

      // Force reconnect
      await centralizedSocketManager.forceReconnect();

      // Should have reset auth error state
      expect((centralizedSocketManager as any).authErrorCount).toBe(0);
      expect((centralizedSocketManager as any).isAuthenticationError).toBe(false);
    });

    it('should clear existing initialization promise on force reconnect', async () => {
      // Set up existing promise
      (centralizedSocketManager as any).initializationPromise = Promise.resolve(mockSocket);
      authStore.token = 'test-token';

      // Force reconnect
      await centralizedSocketManager.forceReconnect();

      // Should have cleared the promise
      expect((centralizedSocketManager as any).initializationPromise).toBeNull();
    });
  });
});
