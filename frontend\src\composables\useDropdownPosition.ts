export function useDropdownPosition() {
  const centerDropdownOnButton = (dropdown: HTMLElement, button: Element) => {
    const buttonRect = button.getBoundingClientRect();
    const buttonCenter = buttonRect.left + buttonRect.width / 2;
    const targetTop = buttonRect.bottom + 5; // 5px offset
    
    // Add the centering class
    dropdown.classList.add('dropdown-centered');
    
    // Set the dynamic position values
    dropdown.style.left = `${buttonCenter}px`;
    dropdown.style.top = `${targetTop}px`;
  };
  
  const positionAllDropdowns = () => {
    const dropdownMenus = document.querySelectorAll('.n-dropdown-menu');
    const binderFollowers = document.querySelectorAll('.v-binder-follower-content');
    
    // Position dropdown menus
    dropdownMenus.forEach(menu => {
      const triggerButton = findTriggerButton(menu);
      if (triggerButton) {
        centerDropdownOnButton(menu as HTMLElement, triggerButton);
      }
    });
    
    // Position popovers
    binderFollowers.forEach(follower => {
      const notificationButton = document.querySelector('.notification-button');
      if (notificationButton && follower.contains(document.querySelector('.n-popover-content'))) {
        centerDropdownOnButton(follower as HTMLElement, notificationButton);
      }
    });
  };
  
  const findTriggerButton = (dropdown: Element): Element | null => {
    // Find the parent dropdown container and then the trigger button
    const container = dropdown.closest('.n-dropdown');
    if (container) {
      return container.querySelector('button');
    }
    return null;
  };
  
  return {
    centerDropdownOnButton,
    positionAllDropdowns,
    findTriggerButton
  };
}