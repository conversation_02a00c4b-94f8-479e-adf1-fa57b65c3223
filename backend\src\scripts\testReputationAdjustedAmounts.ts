/**
 * Test script to verify that reputation-adjusted amounts are correctly calculated
 * when a transaction is created from an accepted interest.
 */

import { PrismaClient, OfferType } from '@prisma/client';

const prisma = new PrismaClient();

async function testReputationAdjustedAmounts() {
  console.log('Testing reputation-adjusted amounts in transaction creation...');
  
  try {
    // Create test users with different reputation levels
    const user1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser1',
        password: 'dummy-password',
        reputationLevel: 2, // Lower reputation
        emailVerified: true,
        phoneVerified: true,
      }
    });

    const user2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser2',
        password: 'dummy-password',
        reputationLevel: 4, // Higher reputation
        emailVerified: true,
        phoneVerified: true,
      }
    });

    // Create an offer with penalties for lower reputation
    const offer = await prisma.offer.create({
      data: {
        userId: user2.id, // High reputation user creates offer
        type: OfferType.SELL,
        currencyPair: 'CAD-IRR',
        amount: 100,
        baseRate: 880.6347, // Base rate
        adjustmentForLowerRep: 3.0, // 3% penalty for lower reputation
        adjustmentForHigherRep: 1.0, // 1% bonus for higher reputation
        status: 'ACTIVE',
      }
    });

    console.log(`Created offer with base rate: ${offer.baseRate}`);
    console.log(`Adjustment for lower rep: ${offer.adjustmentForLowerRep}%`);
    console.log(`Adjustment for higher rep: ${offer.adjustmentForHigherRep}%`);

    // Calculate expected adjusted rate
    // Since user1 (taker) has lower reputation than user2 (creator), 
    // and this is a SELL offer, the rate should be increased by 3%
    const expectedAdjustedRate = offer.baseRate * (1 + offer.adjustmentForLowerRep / 100);
    const expectedAmountB = offer.amount * expectedAdjustedRate;

    console.log(`Expected adjusted rate: ${expectedAdjustedRate}`);
    console.log(`Expected amountB (IRR): ${expectedAmountB}`);

    // Create an interest
    const interest = await prisma.interest.create({
      data: {
        offerId: offer.id,
        interestedUserId: user1.id, // Lower reputation user shows interest
        status: 'PENDING',
      }
    });

    console.log(`Created interest: ${interest.id}`);
    console.log('This would normally trigger transaction creation in acceptInterest...');

    // Simulate what acceptInterest would calculate
    const creatorRepLevel = user2.reputationLevel ?? 3;
    const takerRepLevel = user1.reputationLevel ?? 3;
    
    let agreedExchangeRate = offer.baseRate;
    
    if (takerRepLevel !== creatorRepLevel) {
      if (offer.type === OfferType.SELL) { 
        if (takerRepLevel > creatorRepLevel) { 
          agreedExchangeRate = offer.baseRate * (1 - (offer.adjustmentForHigherRep / 100));
        } else { 
          agreedExchangeRate = offer.baseRate * (1 + (offer.adjustmentForLowerRep / 100));
        }
      }
    }

    const calculatedAmountB = offer.amount * agreedExchangeRate;

    console.log('=== REPUTATION ADJUSTMENT CALCULATION ===');
    console.log(`Creator reputation: ${creatorRepLevel}`);
    console.log(`Taker reputation: ${takerRepLevel}`);
    console.log(`Taker has ${takerRepLevel < creatorRepLevel ? 'LOWER' : 'HIGHER'} reputation`);
    console.log(`Agreed exchange rate: ${agreedExchangeRate}`);
    console.log(`Final amount B (what lower rep user pays): ${calculatedAmountB}`);
    console.log(`Expected amount B: ${expectedAmountB}`);
    console.log(`Match: ${Math.abs(calculatedAmountB - expectedAmountB) < 0.001 ? 'YES' : 'NO'}`);

    // Clean up
    console.log('Cleaning up test data...');
    try {
      await prisma.interest.deleteMany({ where: { id: interest.id } });
      await prisma.offer.deleteMany({ where: { id: offer.id } });
      await prisma.user.deleteMany({ where: { id: { in: [user1.id, user2.id] } } });
    } catch (cleanupError) {
      console.error('Warning: Cleanup failed (this is not critical):', cleanupError);
    }

    console.log('Test completed successfully!');

  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testReputationAdjustedAmounts();
