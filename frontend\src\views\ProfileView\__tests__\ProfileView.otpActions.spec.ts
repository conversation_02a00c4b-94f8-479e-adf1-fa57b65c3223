import { describe, it, expect, vi, beforeEach, afterEach, type Mock } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { ref, computed, nextTick } from 'vue';
import ProfileView from '@/views/ProfileView/ProfileView.vue';
import { createTesting<PERSON>inia } from '@pinia/testing';
import type { UserInfo } from '@/types/auth';
import type { Ref } from 'vue';

// Mock message API from Naive UI
const messageSuccessSpy = vi.fn();
const messageErrorSpy = vi.fn();
vi.mock('naive-ui', async (importOriginal) => {
  const originalNaive = await importOriginal<typeof import('naive-ui')>();
  return {
    ...originalNaive,
    useMessage: () => ({ success: messageSuccessSpy, error: messageErrorSpy }),
  };
});

// Mock API client
vi.mock('@/services/apiClient', () => ({
  default: {
    post: vi.fn()
  }
}));

// Mock error handler - CORRECTED
vi.mock('@/utils/errorHandler', () => ({
  handleError: vi.fn((
    err: any,
    messageMock: any, // Represents the Naive UI message object
    defaultMessage?: string // The third argument is the default message string
  ): string => { // It must return a string
    let displayMessage = defaultMessage || 'An unknown error occurred in test (mock)';

    // Prioritize more specific messages from the error object
    if (err?.response?.data?.message) {
      displayMessage = err.response.data.message;
    } else if (err?.message) {
      displayMessage = err.message;
    }
    // Add other conditions from your actual handleError if necessary (e.g., err.response.statusText)

    // Simulate Naive UI message call if messageMock is provided
    if (messageMock && typeof messageMock.error === 'function') {
      messageMock.error(displayMessage);
    }
    
    return displayMessage; // Crucially, return the string
  })
}));

// Mock auth store
const mockUserRef = ref<UserInfo | null>(null);
const fetchUserProfileMock = vi.fn();
const updatePhoneVerificationStatusMock = vi.fn();
vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    user: mockUserRef,
    fetchUserProfile: fetchUserProfileMock,
    isLoggedIn: computed(() => !!mockUserRef.value),
    updatePhoneVerificationStatus: updatePhoneVerificationStatusMock,
  }),
}));

// Import the mocks after they've been defined
import apiClient from '@/services/apiClient';
// Ensure you are using the mocked version of handleError
import { handleError } from '@/utils/errorHandler'; // This will now be the mock

// Assign imported mocks to variables
const apiClientPostMock = apiClient.post as Mock;
const handleApiErrorMock = handleError as Mock; // This is now correctly the mocked function

describe('ProfileView.vue - OTP Form Actions', () => {
  const testPhoneNumber = '+15551234567';
  let wrapper: any;

  beforeEach(async () => {
    // Reset all mocks
    vi.resetAllMocks();
    vi.useFakeTimers();
    
    // Setup initial user
    mockUserRef.value = {
      id: 'user1',
      email: '<EMAIL>',
      emailVerified: true,
      phoneVerified: false,
      phoneNumber: null,
      reputation: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    // Setup API response for successful OTP send
    apiClientPostMock.mockResolvedValue({ 
      data: { message: 'OTP sent successfully!' }
    });
    
    // Mount component
    wrapper = mount(ProfileView, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
        stubs: { InformationCircleOutline: true }
      }
    });
    
    await flushPromises();
    await nextTick();
    
    // Enter phone number and submit form
    const phoneInput = wrapper.find('[data-testid="phone-input"] input');
    await phoneInput.setValue(testPhoneNumber);
    
    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
    await sendOtpButton.trigger('click');
    
    await flushPromises();
    await nextTick();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.useRealTimers();
    if (wrapper) wrapper.unmount();
  });

  it('shows OTP form after phone form submission', async () => {
    // Verify API was called correctly
    expect(apiClientPostMock).toHaveBeenCalledWith('/auth/phone/send-otp', { phoneNumber: testPhoneNumber });
    expect(messageSuccessSpy).toHaveBeenCalledWith('OTP sent successfully!');
    
    // Check if OTP form elements are present and visible
    const otpForm = wrapper.find('[data-testid="otp-form"]');
    expect(otpForm.exists()).toBe(true);
    
    // Check if important OTP form controls are accessible
    const otpInput = wrapper.find('[data-testid="otp-input"] input');
    expect(otpInput.exists()).toBe(true);
    
    const verifyButton = wrapper.find('[data-testid="verify-otp-button"]');
    expect(verifyButton.exists()).toBe(true);
    
    const resendButton = wrapper.find('[data-testid="resend-otp-button"]');
    expect(resendButton.exists()).toBe(true);
  });

  it('handles successful OTP resend', async () => {
    // Clear previous API calls
    apiClientPostMock.mockClear();
    messageSuccessSpy.mockClear();
    
    // Setup new API response
    apiClientPostMock.mockResolvedValueOnce({ 
      data: { message: 'OTP sent successfully!' } 
    });

    // Advance timers to allow resend button to become enabled
    vi.advanceTimersByTime(30000); // 30 seconds for resendDisabledTime
    await nextTick(); // Allow Vue to update component state (e.g., button disabled status)
    
    // Find and click the resend button
    const resendButton = wrapper.find('[data-testid="resend-otp-button"]');
    // Optional: Add an assertion to ensure the button is not disabled
    // expect(resendButton.attributes('disabled')).toBeUndefined(); 
    await resendButton.trigger('click');
    
    await flushPromises(); // Ensure API call promise resolves
    await nextTick(); // Ensure Vue updates based on API call completion
    
    // Verify API call and success message
    expect(apiClientPostMock).toHaveBeenCalledTimes(1);
    expect(apiClientPostMock).toHaveBeenCalledWith('/auth/phone/send-otp', { phoneNumber: testPhoneNumber });
    expect(messageSuccessSpy).toHaveBeenCalledWith('OTP sent successfully!');
  });

  it('handles rate limited OTP resend (429)', async () => {
    // Clear previous API calls
    apiClientPostMock.mockClear();
    messageErrorSpy.mockClear();
    handleApiErrorMock.mockClear(); // Clear the mock before setting a specific implementation if needed
    
    const blockDurationSeconds = 25;
    const timeOffsetForResendCooldown = 30000; 
    const blockedUntilDate = new Date(Date.now() + timeOffsetForResendCooldown + (blockDurationSeconds * 1000));
    const errorMessageFromApi = 'Rate limit exceeded on resend.';
    
    const apiError = {
      isAxiosError: true, // Good to include for type guards if any
      response: {
        status: 429,
        data: { 
          message: errorMessageFromApi, 
          remainingAttempts: 0, 
          blockedUntil: blockedUntilDate.toISOString() 
        }
      },
      message: 'Request failed with status code 429' // Axios errors also have a top-level message
    };
    
    // The mock for handleError is already defined globally for this test file.
    // If you needed a very specific return for just this call, you could use:
    // handleApiErrorMock.mockReturnValueOnce(errorMessageFromApi);
    // However, the global mock should correctly extract errorMessageFromApi.

    apiClientPostMock.mockRejectedValueOnce(apiError);

    vi.advanceTimersByTime(30000); 
    await nextTick(); 
    
    const resendButton = wrapper.find('[data-testid="resend-otp-button"]');
    await resendButton.trigger('click');
    
    await flushPromises(); 
    await nextTick(); 
    
    expect(apiClientPostMock).toHaveBeenCalledTimes(1);
    expect(apiClientPostMock).toHaveBeenCalledWith('/auth/phone/send-otp', { phoneNumber: testPhoneNumber });
    
    // Verify error handler was called and it passed the correct message to Naive UI
    expect(handleApiErrorMock).toHaveBeenCalledWith(
      apiError, // The error object
      expect.anything(), // The message instance
      'Failed to send OTP. Please try again.' // The default message from ProfileView.vue
    );
    expect(messageErrorSpy).toHaveBeenCalledWith(errorMessageFromApi); // Naive UI should show the specific API error message

    // Check that the component's internal error state was set
    // This is crucial for the rate-limit-alert to appear
    const componentVm = wrapper.vm;
    expect(componentVm.error).toBe(errorMessageFromApi);
    expect(componentVm.remainingAttempts).toBe(0);
    expect(componentVm.blockedUntil).toBe(blockedUntilDate.getTime());
    
    const rateLimitAlert = wrapper.find('[data-testid="rate-limit-alert"]');
    expect(rateLimitAlert.exists(), 'Rate limit alert should exist after 429 error').toBe(true); // This should now pass
    
    // Check if UI elements are disabled
    const otpInput = wrapper.find('[data-testid="otp-input"] input');
    const verifyButton = wrapper.find('[data-testid="verify-otp-button"]');
    // const resendButtonAfterError = wrapper.find('[data-testid="resend-otp-button"]'); // Re-find if needed
    const changeNumberButton = wrapper.find('[data-testid="change-number-button"]');
    
    expect(otpInput.attributes('disabled')).toBeDefined();
    expect(verifyButton.attributes('disabled')).toBeDefined();
    expect(resendButton.attributes('disabled')).toBeDefined(); // resendButton should also be disabled
    expect(changeNumberButton.attributes('disabled')).toBeDefined();
  });
  it('handles "Change Number" click', async () => {
    // Find and click the change number button
    const changeNumberButton = wrapper.find('[data-testid="change-number-button"]');
    expect(changeNumberButton.exists()).toBe(true);
    
    // Enter some value in OTP input to verify it gets cleared
    const otpInput = wrapper.find('[data-testid="otp-input"] input');
    await otpInput.setValue('111111');
    
    // Click change number button
    await changeNumberButton.trigger('click');
    await nextTick();
    await nextTick();
    
    // Verify phone form elements are visible
    const phoneInput = wrapper.find('[data-testid="phone-input"] input');
    expect(phoneInput.exists()).toBe(true);
    
    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
    expect(sendOtpButton.exists()).toBe(true);
    
    // Now we'll enter a new value in OTP input and verify it works
    // This confirms the form has been reset correctly
    const newOtpInput = wrapper.find('[data-testid="otp-input"] input');
    expect(newOtpInput.exists()).toBe(true);
  });
});
