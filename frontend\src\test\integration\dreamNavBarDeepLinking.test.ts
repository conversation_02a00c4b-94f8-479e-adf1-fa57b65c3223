import { describe, it, expect, beforeEach, vi } from 'vitest';
import { FrontendNotificationType } from '@/stores/notificationStore';
import type { FrontendNotification } from '@/stores/notificationStore';

// Mock the translation composable
vi.mock('@/composables/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string, params?: any) => {
      return key;
    }
  })
}));

// Mock the stores
const mockAuthStore = { isAuthenticated: true, user: { email: '<EMAIL>', username: 'testuser' } };
const mockThemeStore = { isDark: false, toggleTheme: vi.fn() };
const mockLanguageStore = { currentLanguage: 'en', isRTL: false, setLanguage: vi.fn() };
const mockConnectionStore = { isConnected: true, connectionQuality: 'good', transportType: 'websocket' };
const mockMatchStore = { matchesNeedingResponse: [] };

// Mock the router
const mockRouter = {
  push: vi.fn().mockResolvedValue(undefined)
};

// Mock the notification store
const mockNotificationStore = {
  markNotificationAsRead: vi.fn().mockResolvedValue(undefined),
  notifications: [],
  unreadNotificationsCount: 0
};

// Create test notifications
const createTestNotification = (
  type: FrontendNotificationType,
  relatedEntityType?: string,
  relatedEntityId?: string,
  data?: any
): FrontendNotification => ({
  id: `test-${Date.now()}`,
  type,
  title: 'Test Notification',
  message: 'This is a test notification',
  timestamp: new Date().toISOString(),
  isRead: false,
  relatedEntityType,
  relatedEntityId,
  data
});

// DreamNavBar deep linking handler function (this is what we're testing)
const createDreamNavBarNotificationHandler = (router: any, notificationStore: any) => {
  const closeNotifications = vi.fn();
    return async (notification: FrontendNotification) => {
    try {
      console.log('[DreamNavBar] Notification clicked:', notification.type, notification.id);
      
      // Close the notification dropdown first
      closeNotifications()
      
      // Mark as read if not already read
      if (!notification.isRead) {
        await notificationStore.markNotificationAsRead(notification.id)
      }
      
      let targetRoute: string | null = null;
      
      // Special case: Handle NEW_INTEREST_ON_YOUR_OFFER notifications
      if (notification.type === FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER) {
        if (notification.data?.interestId && notification.data?.showActionButtons) {
          console.log('[DreamNavBar] Interest notification with action buttons - not navigating');
          return;
        } else {
          // Navigate to my-offers where user can see and manage interests
          console.log('[DreamNavBar] Interest notification - navigating to my-offers for interest management');
          targetRoute = '/my-offers';
        }
      }
      
      // Primary routing: Use relatedEntityType and relatedEntityId (only if not handled above)
      if (!targetRoute && notification.relatedEntityType && notification.relatedEntityId) {
        switch (notification.relatedEntityType) {
          case 'OFFER':
            targetRoute = `/offers/${notification.relatedEntityId}`;
            break;
          case 'CHAT_SESSION':
          case 'CHAT':
            targetRoute = `/chat/${notification.relatedEntityId}`;
            break;        case 'TRANSACTION':
          targetRoute = `/transactions/${notification.relatedEntityId}`;
          break;
        case 'MATCH':
          targetRoute = `/matches`;
          break;
        default:
          console.warn('[DreamNavBar] Unknown entity type:', notification.relatedEntityType);
        }
      }
      
      // Fallback routing: Use data fields for legacy notifications
      if (!targetRoute && notification.data) {
        if (notification.data.offerId) {
          targetRoute = `/offers/${notification.data.offerId}`;
        } else if (notification.data.chatSessionId) {
          targetRoute = `/chat/${notification.data.chatSessionId}`;      } else if (notification.data.transactionId) {
        targetRoute = `/transactions/${notification.data.transactionId}`;
      } else if (notification.data.matchId) {
        targetRoute = `/matches`;
      }
      }
      
      // Type-based fallback routing
      if (!targetRoute) {
        switch (notification.type) {
          case FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER:
            targetRoute = '/my-offers';
            break;
          case FrontendNotificationType.YOUR_INTEREST_ACCEPTED:
          case FrontendNotificationType.YOUR_INTEREST_DECLINED:
          case FrontendNotificationType.CHAT_MESSAGE_RECEIVED:
          case FrontendNotificationType.OFFER_STATUS_UPDATED_BY_OWNER:        case FrontendNotificationType.OFFER_STATUS_CHANGED:
        case FrontendNotificationType.TRANSACTION_STATUS_CHANGED:
          targetRoute = '/home';
          break;
        case FrontendNotificationType.MATCH_FOUND:
        case FrontendNotificationType.MATCH_ACCEPTED:
        case FrontendNotificationType.MATCH_DECLINED:
        case FrontendNotificationType.MATCH_EXPIRED:
        case FrontendNotificationType.MATCH_CONVERTED:
        case FrontendNotificationType.MATCH_ACCEPTED_BY_OTHER:
        case FrontendNotificationType.MATCH_DECLINED_BY_OTHER:
        case FrontendNotificationType.MATCH_CONVERTED_TO_CHAT:
          targetRoute = '/matches';
          break;
          default:
            console.warn('[DreamNavBar] Unknown notification type for fallback:', notification.type);
            targetRoute = '/home';
        }
      }
      
      // Navigate to the target route
      if (targetRoute) {
        console.log('[DreamNavBar] Navigating to:', targetRoute);
        await router.push(targetRoute);
      } else {
        console.error('[DreamNavBar] Could not determine target route for notification:', notification);
      }
      
    } catch (error) {
      console.error('[DreamNavBar] Error handling notification click:', error);
    }
  };
};

describe('DreamNavBar Deep Linking - Frontend', () => {
  let handleNotificationClick: any;

  beforeEach(() => {
    vi.clearAllMocks();
    handleNotificationClick = createDreamNavBarNotificationHandler(mockRouter, mockNotificationStore);
  });
  describe('Entity-based navigation in DreamNavBar', () => {
    it('should navigate to my-offers for NEW_INTEREST_ON_YOUR_OFFER notifications', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER,
        'OFFER',
        'offer-123'
      );      await handleNotificationClick(notification);

      expect(mockRouter.push).toHaveBeenCalledWith('/my-offers');
    });

    it('should navigate to offer details for OFFER entity type with non-interest notifications', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.OFFER_STATUS_CHANGED,
        'OFFER',
        'offer-123'
      );

      await handleNotificationClick(notification);

      expect(mockRouter.push).toHaveBeenCalledWith('/offers/offer-123');
    });

    it('should navigate to chat for CHAT_SESSION entity type', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.CHAT_MESSAGE_RECEIVED,
        'CHAT_SESSION',
        'chat-456'
      );

      await handleNotificationClick(notification);

      expect(mockRouter.push).toHaveBeenCalledWith('/chat/chat-456');
    });    it('should navigate to transaction for TRANSACTION entity type', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.TRANSACTION_STATUS_CHANGED,
        'TRANSACTION',
        'transaction-789'
      );

      await handleNotificationClick(notification);

      expect(mockRouter.push).toHaveBeenCalledWith('/transactions/transaction-789');
    });

    it('should navigate to matches for MATCH entity type', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.MATCH_FOUND,
        'MATCH',
        'match-123'
      );

      await handleNotificationClick(notification);

      expect(mockRouter.push).toHaveBeenCalledWith('/matches');
    });
  });

  describe('Fallback navigation using data fields in DreamNavBar', () => {
    it('should use data.offerId as fallback for offer navigation', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.YOUR_INTEREST_ACCEPTED,
        undefined,
        undefined,
        { offerId: 'offer-fallback-123' }
      );

      await handleNotificationClick(notification);

      expect(mockRouter.push).toHaveBeenCalledWith('/offers/offer-fallback-123');
    });

    it('should use data.chatSessionId as fallback for chat navigation', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.CHAT_MESSAGE_RECEIVED,
        undefined,
        undefined,
        { chatSessionId: 'chat-fallback-456' }
      );

      await handleNotificationClick(notification);

      expect(mockRouter.push).toHaveBeenCalledWith('/chat/chat-fallback-456');
    });
  });

  describe('Type-based fallback navigation in DreamNavBar', () => {
    it('should navigate to /my-offers for interest notifications without entity data', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER
      );

      await handleNotificationClick(notification);

      expect(mockRouter.push).toHaveBeenCalledWith('/my-offers');
    });

    it('should navigate to /home for accepted/declined interest notifications', async () => {
      const acceptedNotification = createTestNotification(
        FrontendNotificationType.YOUR_INTEREST_ACCEPTED
      );

      await handleNotificationClick(acceptedNotification);      expect(mockRouter.push).toHaveBeenCalledWith('/home');
    });

    it('should navigate to /matches for match notifications', async () => {
      const matchNotification = createTestNotification(
        FrontendNotificationType.MATCH_FOUND
      );

      await handleNotificationClick(matchNotification);

      expect(mockRouter.push).toHaveBeenCalledWith('/matches');
    });
  });

  describe('Special cases in DreamNavBar', () => {
    it('should not navigate for NEW_INTEREST_ON_YOUR_OFFER with action buttons', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER,
        'OFFER',
        'offer-123',
        { 
          interestId: 'interest-123',
          showActionButtons: true
        }
      );

      await handleNotificationClick(notification);      expect(mockRouter.push).not.toHaveBeenCalled();
    });

    it('should navigate to /my-offers for NEW_INTEREST_ON_YOUR_OFFER without action buttons', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER,
        'OFFER',
        'offer-123',
        { 
          interestId: 'interest-123'
          // No showActionButtons - should navigate
        }
      );

      await handleNotificationClick(notification);

      expect(mockRouter.push).toHaveBeenCalledWith('/my-offers');
    });

    it('should mark notification as read when clicked', async () => {
      const notification = createTestNotification(
        FrontendNotificationType.YOUR_INTEREST_ACCEPTED,
        'OFFER',
        'offer-123'
      );

      await handleNotificationClick(notification);

      expect(mockNotificationStore.markNotificationAsRead).toHaveBeenCalledWith(notification.id);
    });
  });

  describe('All notification types coverage in DreamNavBar', () => {
    const notificationTypes = [
      FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER,
      FrontendNotificationType.YOUR_INTEREST_ACCEPTED,
      FrontendNotificationType.YOUR_INTEREST_DECLINED,
      FrontendNotificationType.CHAT_MESSAGE_RECEIVED,
      FrontendNotificationType.OFFER_STATUS_UPDATED_BY_OWNER,
      FrontendNotificationType.OFFER_STATUS_CHANGED,
      FrontendNotificationType.TRANSACTION_STATUS_CHANGED,
      FrontendNotificationType.MATCH_FOUND,
      FrontendNotificationType.MATCH_ACCEPTED,
      FrontendNotificationType.MATCH_DECLINED,
      FrontendNotificationType.MATCH_EXPIRED,
      FrontendNotificationType.MATCH_CONVERTED,
      FrontendNotificationType.MATCH_ACCEPTED_BY_OTHER,
      FrontendNotificationType.MATCH_DECLINED_BY_OTHER,
      FrontendNotificationType.MATCH_CONVERTED_TO_CHAT
    ];

    it.each(notificationTypes)('should handle navigation for %s in DreamNavBar', async (type) => {
      const notification = createTestNotification(type);

      await handleNotificationClick(notification);

      // Should navigate somewhere (either entity-based, data-based, or type-based fallback)
      expect(mockRouter.push).toHaveBeenCalled();
    });
  });
});
