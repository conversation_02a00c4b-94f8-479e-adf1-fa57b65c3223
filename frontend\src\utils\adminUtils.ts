/**
 * Admin utility functions for access control
 */

// Admin configuration - read from environment variable for security
const ADMIN_EMAILS = (() => {
  const adminEmailsEnv = import.meta.env.VITE_ADMIN_EMAILS;
  if (adminEmailsEnv) {
    return adminEmailsEnv.split(',').map((email: string) => email.trim()).filter((email: string) => email.length > 0);
  }
  // Fallback to empty array if environment variable is not set
  return [];
})();

/**
 * Validate if a string is a valid email format
 * @param email - The email string to validate
 * @returns boolean indicating if email format is valid
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Check if a user has admin privileges
 * @param userEmail - The user's email address
 * @returns boolean indicating if user is admin
 */
export function isAdmin(userEmail?: string): boolean {
  if (!userEmail) return false;
  
  // Normalize email: trim whitespace and convert to lowercase
  const normalizedEmail = userEmail.trim().toLowerCase();
  
  // Validate email format before checking against admin list
  if (!isValidEmail(normalizedEmail)) {
    return false;
  }
    // Check if normalized email is in the admin list (which should also be normalized)
  const normalizedAdminEmails = ADMIN_EMAILS.map((email: string) => email.toLowerCase());
  return normalizedAdminEmails.includes(normalizedEmail);
}

/**
 * Check if a user has admin privileges and log the check
 * @param userEmail - The user's email address
 * @returns boolean indicating if user is admin
 */
export function checkAdminAccess(userEmail?: string): boolean {
  const hasAccess = isAdmin(userEmail);
  console.log(`[Admin Check] User ${userEmail} admin access: ${hasAccess}`);
  return hasAccess;
}

/**
 * Get list of admin emails (for debugging - don't expose in production)
 */
export function getAdminEmails(): string[] {
  return [...ADMIN_EMAILS];
}
