# Vue.js Performance Optimization TODOs

This document lists the remaining Vue.js components that need performance optimizations, particularly focusing on moving method calls from templates to computed properties.

## Why These Changes Matter

When methods are called directly in templates, they are re-executed on every component re-render, even if their inputs haven't changed. Converting these to computed properties ensures the values are cached and only recalculated when their dependencies change.

## Components to Optimize

### 1. OfferSummaryCard.vue
- [ ] Replace `toLocaleString()` call in amount display:
```vue{1,3}
<template>
  {{ Number(offer.amount).toLocaleString() }} CAD
</template>
```
- [ ] Replace `toFixed(2)` calls in rate displays:
```vue
<template>
  {{ Number(offer.baseRate).toFixed(2) }} IRR/CAD
  {{ Number(offer.calculatedApplicableRate).toFixed(2) }} IRR/CAD
</template>
```
- [ ] Add computed properties:
  - `formattedAmount`
  - `formattedBaseRate`
  - `formattedCalculatedRate`

### 2. OfferForm.vue
- [ ] Replace direct calls to `getCurrentTierLabel()`
- [ ] Replace direct calls to `getLowerTierLabel()`
- [ ] Replace direct calls to `getHigherTierLabel()`
- [ ] Replace direct calls to `calcAdjustedRate('lower'/'higher')`
- [ ] Add computed properties:
  - `currentTierLabel`
  - `lowerTierLabel`
  - `higherTierLabel`
  - `lowerTierAdjustedRate`
  - `higherTierAdjustedRate`

### 3. OfferDetailsModal.vue
- [ ] Replace `formatAmount()` calls with computed property
- [ ] Replace `formatRate()` calls with computed property
- [ ] Add computed properties:
  - `formattedAmount`
  - `formattedRate`

### 4. OfferCard.vue
- [ ] Replace `toLocaleString()` call in amount display
- [ ] Replace conditional text in template with computed property
- [ ] Add computed properties:
  - `formattedAmount`
  - `interestButtonText`

### 5. NotificationBell.vue
- [ ] Replace `formatTimestamp()` calls in template
- [ ] Replace `getNotificationMessage()` calls in template
- [ ] Add computed properties or v-memo directive for list items
- [ ] Consider adding notification message formatting at the data level rather than template level

## Implementation Guidelines

1. For each component:
   - First, verify the component's usage and current behavior
   - Read the template for all method calls
   - Create appropriate computed properties
   - Update templates to use computed properties
   - Test the changes with different data scenarios

2. For number formatting:

Instead of inline formatting in template:
```vue
<template>
  {{ number.toLocaleString() }}
</template>
```

Create a computed property:
```typescript
const formattedNumber = computed(() => 
  props.number?.toLocaleString() ?? '0'
)
```

3. For date formatting:

Instead of inline date formatting:
```vue
<template>
  {{ new Date(timestamp).toLocaleString() }}
</template>
```

Create a computed property:
```ts
const formattedDate = computed(() => 
  props.timestamp ? new Date(props.timestamp).toLocaleString() : 'N/A'
)
```

4. For expensive list rendering:
```vue
<template>
  <!-- Add v-memo to prevent unnecessary re-renders -->
  <div v-for="item in items" 
       :key="item.id" 
       v-memo="[item.id, item.updatedAt]">
    {{ item.someComputedValue }}
  </div>
</template>
```

## Additional Considerations

1. Development Mode Logging
   - [ ] Review and standardize debug logging across components
   - [ ] Ensure all debug logs are wrapped in development mode checks:     ```ts
     if (import.meta.env.DEV) {
       console.debug('[ComponentName]', 'Debug message');
     }
     ```

2. Performance Monitoring
   - [ ] Consider adding Vue DevTools performance metrics
   - [ ] Monitor component re-render frequency
   - [ ] Track computed property evaluation counts

## References

- [Vue.js Performance Documentation](https://vuejs.org/guide/best-practices/performance.html)
- [Vue.js Computed Properties Guide](https://vuejs.org/guide/essentials/computed.html)
- [v-memo Documentation](https://vuejs.org/api/built-in-directives.html#v-memo)
