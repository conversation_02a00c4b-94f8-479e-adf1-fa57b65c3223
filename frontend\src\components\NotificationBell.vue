<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted, nextTick } from 'vue';
import {
  NBadge,
  NButton,
  NIcon,
  NScrollbar,
  useMessage
} from 'naive-ui';
import { 
  NotificationsOutline, 
  CheckmarkDoneOutline, 
  CheckmarkOutline,
  CloseOutline,
  HeartOutline,
  InformationCircleOutline,
  TrendingUpOutline
} from '@vicons/ionicons5';
import { useNotificationStore, FrontendNotificationType } from '@/stores/notificationStore';
import type { FrontendNotification } from '@/stores/notificationStore';
import { useMyOffersStore } from '@/stores/myOffersStore';

import { useRouter } from 'vue-router';
import { useTranslation } from '@/composables/useTranslation';
import { useRtl } from '@/utils/rtl';

const { t } = useTranslation();
const notificationStore = useNotificationStore();
const myOffersStore = useMyOffersStore();
const message = useMessage();
const router = useRouter();

const { direction } = useRtl();
const isRtl = computed(() => direction.value === 'rtl');

// Reactive window width for responsive placement
const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1024);

// Custom popover positioning
const showNotifications = ref(false);
const bellButtonRef = ref<HTMLElement>();
const customPopoverRef = ref<HTMLElement>();

// Reactive popover position
const popoverPosition = ref({
  left: '20px',
  top: '60px',
  width: '350px'
});

// Calculate popover position when needed
const calculatePopoverPosition = () => {
  if (!bellButtonRef.value) {
    console.log('[NotificationBell] bellButtonRef not ready for positioning');
    return;
  }

  try {    // Get the actual DOM element from the Vue component instance
    const bellElement = (bellButtonRef.value as any)?.$el || bellButtonRef.value;
    if (!bellElement || typeof bellElement.getBoundingClientRect !== 'function') {
      console.log('[NotificationBell] Bell DOM element not ready for positioning');
      return;
    }const rect = bellElement.getBoundingClientRect();
    const isMobile = windowWidth.value <= 768;
    const isSmallMobile = windowWidth.value <= 480;
    
    // Enhanced margin calculation for mobile RTL/LTR
    const baseMargin = isMobile ? 16 : 20; // Slightly smaller margin on mobile for more space
    const margin = isSmallMobile ? 12 : baseMargin; // Even smaller on very small screens
    
    // Enhanced width calculation considering RTL text rendering
    let popoverWidth: number;
    if (isMobile) {
      const maxMobileWidth = isSmallMobile ? 300 : 320;
      const availableWidth = window.innerWidth - (margin * 2);
      popoverWidth = Math.min(maxMobileWidth, availableWidth);
    } else {
      popoverWidth = 350;
    }
    
    // Position directly under the bell icon with enhanced RTL/LTR mobile alignment
    let left: number;
    let top = rect.bottom + 12; // 12px gap below the bell for better spacing
    
    if (isRtl.value) {
      // RTL: Enhanced mobile positioning
      if (isMobile) {
        // On mobile RTL, prefer centering under the bell with slight right bias
        const preferredLeft = rect.right - (popoverWidth * 0.75); // 75% of popover width from right edge of bell
        left = preferredLeft;
        
        // Ensure popover doesn't go off left edge with margin
        if (left < margin) {
          left = margin;
        }
        // Ensure popover doesn't go off right edge
        if (left + popoverWidth > window.innerWidth - margin) {
          left = window.innerWidth - popoverWidth - margin;
        }
      } else {
        // Desktop RTL: Align popover to the right of the bell icon
        left = rect.right - popoverWidth;
        if (left < margin) {
          left = margin;
        }
        if (left + popoverWidth > window.innerWidth - margin) {
          left = window.innerWidth - popoverWidth - margin;
        }
      }
    } else {
      // LTR: Enhanced mobile positioning
      if (isMobile) {
        // On mobile LTR, prefer centering under the bell with slight left bias
        const preferredLeft = rect.left - (popoverWidth * 0.25); // 25% of popover width before left edge of bell
        left = preferredLeft;
        
        // Ensure popover doesn't go off right edge with margin
        if (left + popoverWidth > window.innerWidth - margin) {
          left = window.innerWidth - popoverWidth - margin;
        }
        // Ensure popover doesn't go off left edge
        if (left < margin) {
          left = margin;
        }
      } else {
        // Desktop LTR: Align popover to the left edge of the bell icon
        left = rect.left;
        if (left + popoverWidth > window.innerWidth - margin) {
          left = window.innerWidth - popoverWidth - margin;
        }
        if (left < margin) {
          left = margin;
        }
      }
    }popoverPosition.value = {
      left: `${left}px`,
      top: `${top}px`,
      width: `${popoverWidth}px`
    };
      console.log('[NotificationBell] Calculated position:', popoverPosition.value);
    console.log('[NotificationBell] Bell rect:', rect);
    console.log('[NotificationBell] Window width:', window.innerWidth);
    console.log('[NotificationBell] Popover width:', popoverWidth);
    console.log('[NotificationBell] Left position:', left);
    console.log('[NotificationBell] Will extend to:', left + popoverWidth);
    console.log('[NotificationBell] isRtl:', isRtl.value);
    console.log('[NotificationBell] isMobile:', isMobile);
    console.log('[NotificationBell] Margin used:', margin);
  } catch (error) {
    console.error('[NotificationBell] Error calculating popover position:', error);
  }
};

// Static styles for the popover
const customPopoverStyle = computed(() => ({
  position: 'fixed' as const,
  left: popoverPosition.value.left,
  top: popoverPosition.value.top,
  width: popoverPosition.value.width,
  zIndex: 9999,
  backgroundColor: 'var(--n-color)',
  border: '1px solid var(--n-border-color)',
  borderRadius: '6px',
  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12)',
  maxHeight: '80vh',
  overflow: 'hidden' as const,
  // Ensure no content gets clipped and border-radius is preserved
  boxSizing: 'border-box' as const,
  contain: 'layout' as const
}));

// Toggle popover visibility
const togglePopover = async () => {
  // Don't stop propagation here as it might interfere with click-outside detection
  console.log('[NotificationBell] Toggle clicked, current showNotifications:', showNotifications.value);
  console.log('[NotificationBell] bellButtonRef.value:', (bellButtonRef.value as any)?.$el);
  
  if (!showNotifications.value) {
    // About to show popover - calculate position first and prevent background scroll
    showNotifications.value = true;
    preventBackgroundScroll();
    await nextTick();
    // Add a small delay to ensure DOM is fully ready
    setTimeout(() => {
      calculatePopoverPosition();
    }, 10);
  } else {
    // Hide popover and restore background scroll
    showNotifications.value = false;
    restoreBackgroundScroll();
  }
  
  console.log('[NotificationBell] New showNotifications:', showNotifications.value);
};

// Scroll prevention for mobile
let originalBodyStyle = '';
let originalScrollY = 0;

const preventBackgroundScroll = () => {
  if (typeof window === 'undefined') return;
  
  // Store original scroll position and body styles
  originalScrollY = window.scrollY;
  originalBodyStyle = document.body.style.cssText;
  
  // Apply scroll lock styles
  document.body.style.position = 'fixed';
  document.body.style.top = `-${originalScrollY}px`;
  document.body.style.width = '100%';
  document.body.style.overflow = 'hidden';
  
  console.log('[NotificationBell] Background scroll prevented');
};

const restoreBackgroundScroll = () => {
  if (typeof window === 'undefined') return;
  
  // Restore original body styles
  document.body.style.cssText = originalBodyStyle;
  
  // Restore scroll position
  window.scrollTo(0, originalScrollY);
  
  console.log('[NotificationBell] Background scroll restored');
};

// Handle touch events to allow scrolling within popover but prevent background scroll
const handleTouchMove = (event: TouchEvent) => {
  // Allow the scroll within the popover itself
  event.stopPropagation();
};

const handleTouchStart = (event: TouchEvent) => {
  event.stopPropagation();
};

const handleTouchEnd = (event: TouchEvent) => {
  event.stopPropagation();
};

// Close popover
const closePopover = () => {
  console.log('[NotificationBell] closePopover called');
  showNotifications.value = false;
  restoreBackgroundScroll();
};

// Handle clicks outside to close popover
const handleClickOutside = (event: Event) => {
  console.log('[NotificationBell] ⭐ handleClickOutside called!');
  console.log('[NotificationBell] ⭐ showNotifications:', showNotifications.value);
  console.log('[NotificationBell] ⭐ event.target:', event.target);
  
  if (!showNotifications.value) {
    console.log('[NotificationBell] ⭐ Popover already closed, returning');
    return; // Popover is already closed
  }

  const target = event.target as HTMLElement;
  
  // Check if click is inside the popover
  if (customPopoverRef.value && customPopoverRef.value.contains(target)) {
    console.log('[NotificationBell] ⭐ Click inside popover, ignoring');
    return;
  }
  
  // Check if click is on the notification bell container or its children
  const bellContainer = target.closest('.notification-bell');
  if (bellContainer) {
    console.log('[NotificationBell] ⭐ Click on notification bell container, ignoring');
    return;
  }
  
  // Click is outside - close the popover
  console.log('[NotificationBell] ⭐ Click outside detected, closing popover');
  closePopover();
};

// Alternative approach using mousedown instead of click
const handleMouseDownOutside = (event: Event) => {
  console.log('[NotificationBell] 🎯 handleMouseDownOutside called!');
  handleClickOutside(event);
};

// Update window width on resize and set up click outside handler
onMounted(() => {
  console.log('[NotificationBell] 🚀🚀🚀 onMounted called - NOTIFICATION BELL IS MOUNTING');
  console.log('[NotificationBell] 🚀🚀🚀 Window object available:', typeof window);
  console.log('[NotificationBell] 🚀🚀🚀 Document object available:', typeof document);
  
  if (typeof window !== 'undefined') {
    const updateWindowWidth = () => {
      windowWidth.value = window.innerWidth;
      // Recalculate position if popover is visible
      if (showNotifications.value) {
        calculatePopoverPosition();
      }
    };
    
    // Add multiple event listeners for better coverage
    window.addEventListener('resize', updateWindowWidth);
    document.addEventListener('click', handleClickOutside, true);
    document.addEventListener('mousedown', handleMouseDownOutside, true);
    
    console.log('[NotificationBell] ✅✅✅ Event listeners added successfully');
    console.log('[NotificationBell] ✅ handleClickOutside function:', typeof handleClickOutside);
    console.log('[NotificationBell] ✅ document.addEventListener available:', typeof document.addEventListener);
    
    // Test the event listener immediately
    setTimeout(() => {
      console.log('[NotificationBell] 🧪 Testing event listener setup...');
      console.log('[NotificationBell] 🧪 Current showNotifications state:', showNotifications.value);
    }, 1000);
    
    onUnmounted(() => {
      console.log('[NotificationBell] 🧹 Cleaning up event listeners');
      window.removeEventListener('resize', updateWindowWidth);
      document.removeEventListener('click', handleClickOutside, true);
      document.removeEventListener('mousedown', handleMouseDownOutside, true);
      
      // Ensure scroll is restored if component is unmounted while popover is open
      if (showNotifications.value) {
        restoreBackgroundScroll();
      }
    });
  } else {
    console.log('[NotificationBell] ❌ Window not available');
  }
});

const unreadCount = computed(() => notificationStore.unreadNotificationsCount);

// Use the store's filtered unread notifications for the visible list
const visibleNotifications = computed(() => notificationStore.unreadNotifications);

watch(
  () => notificationStore.notifications,
  (newVal) => {
    console.log('[NotificationBell] Watched notifications from store. New count:', newVal.length);
  },
  { deep: true, immediate: true }
);

// Watch for popover close to ensure scroll is restored
watch(
  () => showNotifications.value,
  (newValue, oldValue) => {
    if (oldValue && !newValue) {
      // Popover was closed
      restoreBackgroundScroll();
    }
  }
);

// Cleanup on unmount
onUnmounted(() => {
  if (showNotifications.value) {
    restoreBackgroundScroll();
  }
});

// Deep linking notification click handler
const handleNotificationClick = async (notification: FrontendNotification) => {
  try {
    console.log('[NotificationBell] Notification clicked:', notification.type, notification.id);
    
    // Close the popover first
    showNotifications.value = false;
    restoreBackgroundScroll();
      // Mark as read if not already read
    if (!notification.isRead) {
      await notificationStore.markNotificationAsRead(notification.id);
    }
    
    let targetRoute: string | null = null;
      // Special case: Handle NEW_INTEREST_ON_YOUR_OFFER notifications
    if (notification.type === FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER) {
      // Always navigate to /my-offers for interest notifications, where users can accept/decline interests
      console.log('[NotificationBell] Interest notification - navigating to my-offers for interest management');
      targetRoute = '/my-offers';
    }
    
    // Primary routing: Use relatedEntityType and relatedEntityId (only if not handled above)
    if (!targetRoute && notification.relatedEntityType && notification.relatedEntityId) {
      switch (notification.relatedEntityType) {
        case 'OFFER':
          targetRoute = `/offers/${notification.relatedEntityId}`;
          break;
        case 'CHAT_SESSION':
        case 'CHAT':
          targetRoute = `/chat/${notification.relatedEntityId}`;
          break;
        case 'TRANSACTION':
          targetRoute = `/transactions/${notification.relatedEntityId}`;
          break;
        case 'MATCH':
          targetRoute = `/matches`;
          break;
        default:
          console.warn('[NotificationBell] Unknown entity type:', notification.relatedEntityType);
      }
    }
      // Fallback routing: Use data fields for legacy notifications
    if (!targetRoute && notification.data) {
      if (notification.data.offerId) {
        targetRoute = `/offers/${notification.data.offerId}`;
      } else if (notification.data.chatSessionId) {
        targetRoute = `/chat/${notification.data.chatSessionId}`;
      } else if (notification.data.transactionId) {
        targetRoute = `/transactions/${notification.data.transactionId}`;
      } else if (notification.data.matchId) {
        targetRoute = `/matches`;
      }
    }    // Type-based fallback routing
    if (!targetRoute) {
      switch (notification.type) {
        case FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER:
          targetRoute = '/my-offers';
          break;
        case FrontendNotificationType.YOUR_INTEREST_ACCEPTED:
        case FrontendNotificationType.YOUR_INTEREST_DECLINED:
        case FrontendNotificationType.CHAT_MESSAGE_RECEIVED:
        case FrontendNotificationType.OFFER_STATUS_UPDATED_BY_OWNER:
        case FrontendNotificationType.OFFER_STATUS_CHANGED:
        case FrontendNotificationType.TRANSACTION_STATUS_CHANGED:
          targetRoute = '/home';
          break;
        case FrontendNotificationType.MATCH_FOUND:
        case FrontendNotificationType.MATCH_ACCEPTED:
        case FrontendNotificationType.MATCH_DECLINED:
        case FrontendNotificationType.MATCH_EXPIRED:
        case FrontendNotificationType.MATCH_CONVERTED:
        case FrontendNotificationType.MATCH_ACCEPTED_BY_OTHER:
        case FrontendNotificationType.MATCH_DECLINED_BY_OTHER:
        case FrontendNotificationType.MATCH_CONVERTED_TO_CHAT:
          targetRoute = '/matches';
          break;
        default:
          console.warn('[NotificationBell] Unknown notification type for fallback:', notification.type);
          targetRoute = '/home';
      }
    }// Navigate to the target route
    if (targetRoute) {
      console.log('[NotificationBell] Navigating to:', targetRoute);
      await router.push(targetRoute);
    } else {
      console.error('[NotificationBell] Could not determine target route for notification:', notification);
      message.warning(t('notifications.navigationError') || 'Could not navigate to notification target');
    }
    
  } catch (error) {
    console.error('[NotificationBell] Error handling notification click:', error);
    message.error(t('notifications.navigationError') || 'Navigation failed');
  }
};

// Handle mark as read
const handleMarkAsRead = async (notificationId: string) => {
  try {
    await notificationStore.markNotificationAsRead(notificationId);
    message.success(t('notifications.markedAsRead') || 'Marked as read');
  } catch (error) {
    console.error('[NotificationBell] Error marking notification as read:', error);
    message.error(t('notifications.markAsReadError') || 'Failed to mark as read');
  }
};

// Handle mark all as read
const handleMarkAllAsRead = async () => {
  try {
    await notificationStore.markAllNotificationsAsRead();
    message.success(t('notifications.allMarkedAsRead') || 'All notifications marked as read');
  } catch (error) {
    console.error('[NotificationBell] Error marking all notifications as read:', error);
    message.error(t('notifications.markAllAsReadError') || 'Failed to mark all as read');
  }
};

// Handle interest acceptance in notification
const handleAcceptInterestInNotification = async (notification: FrontendNotification) => {
  try {
    const interestId = notification.data?.interestId;
    if (!interestId) {
      console.error('[NotificationBell] No interestId found in notification data');
      return;
    }
    
    console.log('[NotificationBell] Accepting interest:', interestId);
    await myOffersStore.acceptInterest(interestId);
    message.success(t('interests.acceptedSuccessfully') || 'Interest accepted successfully');
    
    // Close popover after successful action
    showNotifications.value = false;
    restoreBackgroundScroll();
  } catch (error) {
    console.error('[NotificationBell] Error accepting interest:', error);
    message.error(t('interests.acceptError') || 'Failed to accept interest');
  }
};

// Handle interest decline in notification
const handleDeclineInterestInNotification = async (notification: FrontendNotification) => {
  try {
    const interestId = notification.data?.interestId;
    if (!interestId) {
      console.error('[NotificationBell] No interestId found in notification data');
      return;
    }
    
    console.log('[NotificationBell] Declining interest:', interestId);
    await myOffersStore.declineInterest(interestId);
    message.success(t('interests.declinedSuccessfully') || 'Interest declined successfully');
    
    // Close popover after successful action
    showNotifications.value = false;
    restoreBackgroundScroll();
  } catch (error) {
    console.error('[NotificationBell] Error declining interest:', error);
    message.error(t('interests.declineError') || 'Failed to decline interest');
  }
};

// Helper functions for notification display
const getNotificationTitle = (notification: FrontendNotification): string => {
  switch (notification.type) {
    case FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER:
      return t('notifications.titles.newInterest') || 'New Interest';
    case FrontendNotificationType.YOUR_INTEREST_ACCEPTED:
      return t('notifications.titles.interestAccepted') || 'Interest Accepted';
    case FrontendNotificationType.YOUR_INTEREST_DECLINED:
      return t('notifications.titles.interestDeclined') || 'Interest Declined';
    case FrontendNotificationType.CHAT_MESSAGE_RECEIVED:
      return t('notifications.titles.newMessage') || 'New Message';
    case FrontendNotificationType.OFFER_STATUS_UPDATED_BY_OWNER:
      return t('notifications.titles.offerUpdated') || 'Offer Updated';
    case FrontendNotificationType.OFFER_STATUS_CHANGED:
      return t('notifications.titles.offerChanged') || 'Offer Status Changed';
    case FrontendNotificationType.TRANSACTION_STATUS_CHANGED:
      return t('notifications.titles.transactionUpdate') || 'Transaction Update';
    case FrontendNotificationType.MATCH_FOUND:
      return t('notifications.titles.matchFound') || 'Match Found';
    case FrontendNotificationType.MATCH_ACCEPTED:
      return t('notifications.titles.matchAccepted') || 'Match Accepted';
    case FrontendNotificationType.MATCH_DECLINED:
      return t('notifications.titles.matchDeclined') || 'Match Declined';
    case FrontendNotificationType.MATCH_EXPIRED:
      return t('notifications.titles.matchExpired') || 'Match Expired';
    case FrontendNotificationType.MATCH_CONVERTED:
      return t('notifications.titles.matchConverted') || 'Match Converted';
    case FrontendNotificationType.MATCH_ACCEPTED_BY_OTHER:
      return t('notifications.titles.matchAcceptedByOther') || 'Match Accepted';
    case FrontendNotificationType.MATCH_DECLINED_BY_OTHER:
      return t('notifications.titles.matchDeclinedByOther') || 'Match Declined';
    case FrontendNotificationType.MATCH_CONVERTED_TO_CHAT:
      return t('notifications.titles.matchConvertedToChat') || 'Match Converted to Chat';
    default:
      return t('notifications.titles.notification') || 'Notification';
  }
};

const getNotificationMessage = (notification: FrontendNotification): string => {
  return notification.message || t('notifications.noMessage') || 'No message available';
};

const getNotificationIcon = (type: FrontendNotificationType) => {
  switch (type) {
    case FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER:
    case FrontendNotificationType.YOUR_INTEREST_ACCEPTED:
    case FrontendNotificationType.YOUR_INTEREST_DECLINED:
      return HeartOutline;
    case FrontendNotificationType.CHAT_MESSAGE_RECEIVED:
      return InformationCircleOutline;
    case FrontendNotificationType.OFFER_STATUS_UPDATED_BY_OWNER:
    case FrontendNotificationType.OFFER_STATUS_CHANGED:
      return TrendingUpOutline;
    case FrontendNotificationType.TRANSACTION_STATUS_CHANGED:
      return CheckmarkOutline;
    case FrontendNotificationType.MATCH_FOUND:
    case FrontendNotificationType.MATCH_ACCEPTED:
    case FrontendNotificationType.MATCH_DECLINED:
    case FrontendNotificationType.MATCH_EXPIRED:
    case FrontendNotificationType.MATCH_CONVERTED:
    case FrontendNotificationType.MATCH_ACCEPTED_BY_OTHER:
    case FrontendNotificationType.MATCH_DECLINED_BY_OTHER:
    case FrontendNotificationType.MATCH_CONVERTED_TO_CHAT:
      return TrendingUpOutline;
    default:
      return NotificationsOutline;
  }
};

const getNotificationTypeClass = (type: FrontendNotificationType): string => {
  switch (type) {
    case FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER:
    case FrontendNotificationType.YOUR_INTEREST_ACCEPTED:
      return 'interest';
    case FrontendNotificationType.YOUR_INTEREST_DECLINED:
      return 'declined';
    case FrontendNotificationType.CHAT_MESSAGE_RECEIVED:
      return 'message';
    case FrontendNotificationType.OFFER_STATUS_UPDATED_BY_OWNER:
    case FrontendNotificationType.OFFER_STATUS_CHANGED:
      return 'offer';
    case FrontendNotificationType.TRANSACTION_STATUS_CHANGED:
      return 'transaction';
    case FrontendNotificationType.MATCH_FOUND:
    case FrontendNotificationType.MATCH_ACCEPTED:
    case FrontendNotificationType.MATCH_CONVERTED:
    case FrontendNotificationType.MATCH_ACCEPTED_BY_OTHER:
    case FrontendNotificationType.MATCH_CONVERTED_TO_CHAT:
      return 'match-success';
    case FrontendNotificationType.MATCH_DECLINED:
    case FrontendNotificationType.MATCH_EXPIRED:
    case FrontendNotificationType.MATCH_DECLINED_BY_OTHER:
      return 'match-declined';
    default:
      return 'default';
  }
};

const formatTimestamp = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return t('time.justNow') || 'Just now';
    } else if (diffInMinutes < 60) {
      return t('time.minutesAgo', { count: diffInMinutes }) || `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return t('time.hoursAgo', { count: hours }) || `${hours}h ago`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return t('time.daysAgo', { count: days }) || `${days}d ago`;
    }
  } catch (error) {
    console.error('[NotificationBell] Error formatting timestamp:', error);
    return timestamp;
  }
};

// Test function to verify template binding

</script>

<template>
  <div class="notification-bell" :class="{ rtl: isRtl }">    <!-- Custom positioned popover using Teleport -->
    <Teleport to="body">
      <div
        v-if="showNotifications"
        ref="customPopoverRef"
        class="custom-notification-popover"
        :style="customPopoverStyle"
        @click.stop
        @touchmove="handleTouchMove"
        @touchstart="handleTouchStart"
        @touchend="handleTouchEnd"
      >
        <!-- Beautiful notification content with proper theming -->
        <div class="notification-dropdown">
          <!-- Header with title and mark all as read -->
          <div class="notification-dropdown-header">
            <div class="header-title">
              <n-icon size="18" class="header-icon">
                <NotificationsOutline />
              </n-icon>
              <span class="title-text">{{ t('notifications.title') }}</span>
              <n-badge 
                v-if="unreadCount > 0" 
                :value="unreadCount" 
                :max="9" 
                type="info" 
                size="small"
                class="header-badge"
              />
            </div>            <n-button
              v-if="visibleNotifications.length > 0"
              text
              size="small"
              @click="handleMarkAllAsRead"
              class="mark-all-btn"
              :style="markAllButtonStyle"
              :disabled="unreadCount === 0"
            >
              <template #icon>
                <n-icon size="16"><CheckmarkDoneOutline /></n-icon>
              </template>
              {{ t('notifications.markAllAsRead') }}
            </n-button>
          </div>

          <!-- Notification list with beautiful styling -->
          <div class="notification-dropdown-body">
            <n-scrollbar class="notification-scrollbar" style="max-height: 400px;" trigger="hover">              <template v-if="visibleNotifications.length > 0">
                <div class="notification-list">
                  <div
                    v-for="notification in visibleNotifications"
                    :key="notification.id"
                    @click="handleNotificationClick(notification)"
                    :class="[
                      'notification-item',
                      { 
                        'notification-item--unread': !notification.isRead,
                        'notification-item--interactive': notification.type === FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER && notification.data?.interestId
                      }
                    ]"
                  >
                    <!-- Notification type indicator -->
                    <div class="notification-indicator">
                      <n-icon 
                        size="16" 
                        :class="[
                          'indicator-icon',
                          `indicator-icon--${getNotificationTypeClass(notification.type)}`
                        ]"
                      >
                        <component :is="getNotificationIcon(notification.type)" />
                      </n-icon>
                      <div v-if="!notification.isRead" class="unread-dot"></div>
                    </div>

                    <!-- Notification content -->
                    <div class="notification-content-wrapper">
                      <div class="notification-header-info">
                        <h4 class="notification-title">{{ getNotificationTitle(notification) }}</h4>
                        <span class="notification-timestamp">{{ formatTimestamp(notification.createdAt) }}</span>
                      </div>
                      
                      <p class="notification-message">{{ getNotificationMessage(notification) }}</p>

                      <!-- Action buttons for NEW_INTEREST_ON_YOUR_OFFER -->
                      <div 
                        v-if="notification.type === FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER && notification.data?.interestId"
                        class="notification-actions"
                        @click.stop
                      >
                        <n-button 
                          size="small" 
                          type="error" 
                          secondary
                          @click="handleDeclineInterestInNotification(notification)"
                          class="action-btn action-btn--decline"
                        >
                          <template #icon>
                            <n-icon size="14"><CloseOutline /></n-icon>
                          </template>
                          {{ t('notifications.decline') }}
                        </n-button>
                        <n-button 
                          size="small" 
                          type="primary"
                          @click="handleAcceptInterestInNotification(notification)"
                          class="action-btn action-btn--accept"
                        >
                          <template #icon>
                            <n-icon size="14"><CheckmarkOutline /></n-icon>
                          </template>
                          {{ t('notifications.accept') }}
                        </n-button>
                      </div>

                      <!-- Mark as read button for unread notifications -->
                      <n-button
                        v-if="!notification.isRead && !(notification.type === FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER && notification.data?.interestId)"
                        text
                        size="tiny"
                        @click.stop="handleMarkAsRead(notification.id)"
                        class="mark-read-btn"
                      >
                        <template #icon>
                          <n-icon size="12"><CheckmarkDoneOutline /></n-icon>
                        </template>
                        {{ t('notifications.markRead') }}
                      </n-button>
                    </div>
                  </div>
                </div>
              </template>
              
              <!-- Empty state with beautiful styling -->
              <template v-else>
                <div class="notification-empty">
                  <n-icon size="48" class="empty-icon">
                    <NotificationsOutline />
                  </n-icon>
                  <h3 class="empty-title">{{ t('notifications.noNotifications') }}</h3>
                  <p class="empty-description">{{ t('notifications.noNotificationsDescription') || 'You\'re all caught up! New notifications will appear here.' }}</p>
                </div>
              </template>
            </n-scrollbar>
          </div>
        </div>
      </div>
    </Teleport>    <!-- Notification bell trigger -->
    <n-button
      ref="bellButtonRef"
      class="notification-button"
      circle
      text
      :class="{ 'has-notifications': unreadCount > 0 }"
      @click.stop="togglePopover"
    
    >
      <n-badge
        :value="unreadCount"
        :show="unreadCount > 0"
        :max="9"
        type="error"
        :color="unreadCount > 0 ? '#ff4757' : undefined"
        :offset="[-2, 2]"
        class="notification-badge"
      >
        <n-icon size="20" class="notification-icon">
          <NotificationsOutline />
        </n-icon>
      </n-badge>
    </n-button>
  </div>
</template>

<style scoped>
/* ===== NOTIFICATION BADGE STYLING ===== */
.notification-badge :deep(.n-badge) {
  position: relative !important;
}

.notification-badge :deep(.n-badge-sup) {
  position: absolute !important;
  top: -10px !important;
  right: -10px !important;
  min-width: 26px !important;
  height: 20px !important;
  line-height: 20px !important;
  font-size: 11px !important;
  font-weight: 700 !important;
  padding: 0 5px !important;
  border-radius: 10px !important;
  background: #ff4757 !important;
  color: white !important;
  border: 2px solid white !important;
  box-sizing: border-box !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3) !important;
  z-index: 10 !important;
  white-space: nowrap !important;
  text-align: center !important;
}

/* Dark mode badge border */
:deep(.dark) .notification-badge :deep(.n-badge-sup) {
  border-color: var(--n-color) !important;
}

/* ===== NOTIFICATION BELL BUTTON ===== */
.notification-bell {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 8px; /* Increased padding to ensure full visibility */
}

.notification-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  position: relative;
  overflow: visible; /* Changed from hidden to prevent badge cropping */
}

.notification-button:hover {
  background-color: rgba(24, 160, 88, 0.1);
  transform: scale(1.05);
}

.notification-button.has-notifications {
  animation: subtle-pulse 2s ease-in-out infinite;
}

.notification-icon {
  transition: all 0.3s ease;
}

.notification-button:hover .notification-icon {
  color: #18a058;
  transform: rotate(10deg);
}

/* Subtle pulse animation for notifications */
@keyframes subtle-pulse {
  0%, 100% { 
    transform: scale(1); 
  }
  50% { 
    transform: scale(1.02); 
  }
}

/* ===== NOTIFICATION DROPDOWN ===== */
:deep(.notification-popover .n-popover) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  z-index: 9999 !important;
  position: fixed !important; /* Use fixed positioning to prevent layout shifts */
}

/* Ensure popover stays within viewport - let Naive UI handle positioning */
:deep(.notification-popover .n-popover__content) {
  max-width: var(--popover-max-width, 95vw) !important;
  z-index: 9999 !important;
}

:deep(.notification-popover .n-popover--show) {
  transform-origin: top center !important; /* Use center instead of right for RTL */
  z-index: 9999 !important;
}

.notification-dropdown {
  width: 100%;
  max-height: 80vh;  
  background: #FFFFFF;
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
  box-shadow: var(--n-box-shadow-2);
  overflow: hidden;
  position: relative;
  z-index: 10000 !important;
  box-sizing: border-box;
}

[data-theme='dark'] .notification-dropdown {
  background: #1E293B;
  border-color: rgba(255, 255, 255, 0.15);
}

/* ===== DROPDOWN HEADER ===== */
.notification-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;  
  padding: 16px 20px;
  border-bottom: 1px solid var(--n-border-color);
  background: linear-gradient(to bottom, #FAFBFC, #F8F9FA);
  position: relative;
  z-index: 10001 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

[data-theme='dark'] .notification-dropdown-header {
  background: linear-gradient(to bottom, #2D3A4F, #1E293B);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  font-size: 16px;
  color: var(--n-text-color);
  letter-spacing: -0.01em;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

[data-theme='dark'] .header-title {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.header-icon {
  color: var(--n-color-primary);
  background: linear-gradient(135deg, var(--n-color-primary), var(--n-color-info));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
}

[data-theme='dark'] .header-icon {
  background: linear-gradient(135deg, #63E2B7, #18A058);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.title-text {
  margin: 0;
}

.header-badge {
  margin-left: 4px;
}

/* RTL header adjustments */
.rtl .header-badge {
  margin-left: 0;
  margin-right: 4px;
}

.rtl .notification-dropdown-header {
  gap: 12px; /* Increase gap between title and button for RTL */
}

.rtl .header-title {
  flex: 1; /* Allow title to take available space */
  min-width: 0; /* Allow title to shrink if needed */
}

.mark-all-btn {
  font-size: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: var(--n-color-primary-hover);
  color: var(--n-color-primary);
  border: 1px solid var(--n-color-primary);
  white-space: nowrap;
  min-width: max-content; /* Ensure button is wide enough for content */
  padding: 6px 12px; /* Add explicit padding for better text spacing */
}

.mark-all-btn:hover {
  background: var(--n-color-primary);
  color: var(--n-color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--n-box-shadow);
}

.mark-all-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* RTL-specific button styling */
.rtl .mark-all-btn {
  white-space: normal; /* Allow text wrapping for longer RTL text */
  min-width: 120px; /* Minimum width for Persian text */
  max-width: 180px; /* Maximum width to prevent excessive stretching */
  text-align: center;
  line-height: 1.3; /* Better line height for potential wrapping */
  padding: 8px 16px; /* Slightly more padding for RTL */
}

/* Aggressive override for Naive UI button in RTL */
.rtl .notification-dropdown-header .n-button.mark-all-btn {
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  text-overflow: unset !important;
  flex-wrap: wrap !important;
}

/* Target the button directly with attribute selector for maximum specificity */
.rtl .notification-dropdown-header button.mark-all-btn {
  white-space: normal !important;
  min-width: 120px !important;
  max-width: 180px !important;
  height: auto !important;
  min-height: 36px !important;
}

/* ===== DROPDOWN BODY ===== */
.notification-dropdown-body {
  max-height: 400px;
  min-height: 200px;
}

.notification-scrollbar {
  height: 100%;
}

/* ===== ENHANCED SCROLLBAR STYLING ===== */
.notification-scrollbar :deep(.n-scrollbar-rail) {
  right: 4px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 6px !important;
  width: 6px !important;
}

.notification-scrollbar :deep(.n-scrollbar-rail--vertical) {
  width: 6px !important;
}

.notification-scrollbar :deep(.n-scrollbar-handle) {
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 6px !important;
  width: 6px !important;
  transition: all 0.3s ease !important;
}

.notification-scrollbar :deep(.n-scrollbar-handle):hover {
  background: rgba(255, 255, 255, 0.5) !important;
}

/* Dark mode scrollbar */
:deep(.dark) .notification-scrollbar :deep(.n-scrollbar-rail) {
  background: rgba(255, 255, 255, 0.05) !important;
}

:deep(.dark) .notification-scrollbar :deep(.n-scrollbar-handle) {
  background: rgba(255, 255, 255, 0.2) !important;
}

:deep(.dark) .notification-scrollbar :deep(.n-scrollbar-handle):hover {
  background: rgba(255, 255, 255, 0.3) !important;
}

/* ===== NOTIFICATION LIST ===== */
.notification-list {
  padding: 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid var(--n-border-color);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background: #FFFFFF;
}

.notification-item:hover {
  background: #F5F7FA;
  transform: translateX(4px);
}

[data-theme='dark'] .notification-item {
  background: #1E293B;
  border-color: rgba(255, 255, 255, 0.15);
}

[data-theme='dark'] .notification-item:hover {
  background: #2D3A4F;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item--unread {
  background: var(--n-color-primary-hover);
  border-left: 3px solid var(--n-color-primary);
}

.notification-item--unread:hover {
  background: var(--n-color-primary-pressed);
}

.notification-item--interactive {
  padding-bottom: 20px;
}

/* ===== NOTIFICATION INDICATOR ===== */
.notification-indicator {
  position: relative;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--n-color-modal);
  border: 2px solid var(--n-border-color);
  box-shadow: var(--n-box-shadow);
}

.indicator-icon {
  transition: all 0.3s ease;
}

.indicator-icon--interest {
  color: #ff6b7a;
}

.indicator-icon--accepted {
  color: var(--n-color-success);
}

.indicator-icon--declined {
  color: var(--n-color-error);
}

.indicator-icon--status {
  color: var(--n-color-warning);
}

.indicator-icon--info {
  color: var(--n-color-info);
}

.unread-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: var(--n-color-error);
  border-radius: 50%;
  border: 2px solid var(--n-color-modal);
  animation: pulse-dot 2s ease-in-out infinite;
  box-shadow: var(--n-box-shadow);
}

@keyframes pulse-dot {
  0%, 100% { 
    transform: scale(1); 
    opacity: 1;
  }
  50% { 
    transform: scale(1.2); 
    opacity: 0.8;
  }
}

/* ===== NOTIFICATION CONTENT ===== */
.notification-content-wrapper {
  flex: 1;
  min-width: 0;
}

.notification-header-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
  gap: 8px;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--n-text-color);
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

.notification-timestamp {
  font-size: 12px;
  color: var(--n-text-color-disabled);
  white-space: nowrap;
  flex-shrink: 0;
}

.notification-message {
  font-size: 13px;
  color: var(--n-text-color-depth-2);
  line-height: 1.5;
  margin: 0 0 8px 0;
  word-wrap: break-word;
}

/* ===== NOTIFICATION ACTIONS ===== */
.notification-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid var(--n-divider-color);
}

.action-btn {
  border-radius: 8px;
  font-size: 12px;
  min-width: 70px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--n-box-shadow);
}

.action-btn--decline {
  background: var(--n-color-error-hover);
  color: var(--n-color-error);
  border: 1px solid var(--n-color-error);
}

.action-btn--decline:hover {
  background: var(--n-color-error);
  color: var(--n-color-error-hover);
}

.action-btn--accept {
  background: var(--n-color-success-hover);
  color: var(--n-color-success);
  border: 1px solid var(--n-color-success);
}

.action-btn--accept:hover {
  background: var(--n-color-success);
  color: var(--n-color-success-hover);
}

/* ===== MARK AS READ BUTTON ===== */
.mark-read-btn {
  margin-top: 4px;
  font-size: 11px;
  color: var(--n-text-color-disabled);
  border-radius: 6px;
  transition: all 0.3s ease;
  background: var(--n-color-hover);
  border: 1px solid var(--n-border-color);
}

.mark-read-btn:hover {
  color: var(--n-color-primary);
  background: var(--n-color-primary-hover);
  border: 1px solid var(--n-color-primary);
}

/* ===== EMPTY STATE ===== */
.notification-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  background: var(--n-color-modal);
  min-height: 200px; /* Maintain panel dimensions */
  max-width: 320px;
  margin: 0 auto;
}

.empty-icon {
  color: var(--n-text-color-disabled);
  margin-bottom: 20px;
  opacity: 0.5;
  transition: opacity 0.3s ease, color 0.3s ease;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.empty-description {
  font-size: 14px;
  color: var(--n-text-color-depth-2);
  margin: 0;
  line-height: 1.6;
  max-width: 280px;
  opacity: 0.8;
}

/* Dark theme empty state refinements */
[data-theme='dark'] .notification-empty {
  background: var(--n-color-modal);
}

[data-theme='dark'] .empty-icon {
  opacity: 0.4;
}

[data-theme='dark'] .empty-title {
  color: var(--n-text-color);
}

[data-theme='dark'] .empty-description {
  color: var(--n-text-color-depth-2);
  opacity: 0.75;
}

/* Mobile responsive empty state */
@media (max-width: 768px) {
  .notification-empty {
    padding: 36px 20px;
    min-height: 180px;
  }
  
  .empty-title {
    font-size: 15px;
  }
  
  .empty-description {
    font-size: 13px;
    max-width: 260px;
  }
}

@media (max-width: 480px) {
  .notification-empty {
    padding: 32px 16px;
    min-height: 160px;
  }
  
  .empty-title {
    font-size: 14px;
  }
  
  .empty-description {
    font-size: 12px;
    max-width: 240px;
  }
}

/* RTL support for empty state */
.rtl .notification-empty {
  direction: rtl;
}

.rtl .empty-title,
.rtl .empty-description {
  text-align: center; /* Keep centered regardless of RTL */
}

/* ===== RTL SUPPORT ===== */
.rtl .notification-dropdown {
  direction: rtl;
  /* Let Naive UI popover handle transform-origin based on placement */
}

.rtl .notification-item {
  direction: rtl;
}

.rtl .notification-item:hover {
  transform: none; /* Disable transform on mobile touch devices */
}

.rtl .notification-item--unread {
  border-left: none;
  border-right: 3px solid var(--n-color-primary);
}

.rtl .notification-header-info {
  flex-direction: row-reverse;
}

.rtl .notification-actions {
  justify-content: flex-start;
}

/* Enhanced RTL mobile support */
.rtl .notification-content-wrapper {
  text-align: right;
}

.rtl .notification-title {
  text-align: right;
}

.rtl .notification-message {
  text-align: right;
}

.rtl .unread-dot {
  right: auto;
  left: -2px;
}

/* Enhanced RTL notification indicator positioning */
.rtl .notification-indicator {
  margin-left: 12px;
  margin-right: 0;
}

/* Enhanced RTL action button layouts */
.rtl .notification-actions {
  flex-direction: row-reverse;
}

.rtl .action-btn:first-child {
  margin-left: 8px;
  margin-right: 0;
}

.rtl .action-btn:last-child {
  margin-left: 0;
  margin-right: 0;
}

/* ===== MOBILE RESPONSIVE ===== */
@media (max-width: 768px) {
  .notification-dropdown {
    width: 320px;
    max-width: 90vw;
    /* Remove static transform - handled by enhanced positioning logic */
  }
  
  .notification-dropdown-header {
    padding: 16px 20px;
  }
  
  .notification-item {
    padding: 16px 20px !important;
  }

  .notification-bell {
    padding: 8px; /* Increased padding for better touch target */
  }
  
  .notification-badge :deep(.n-badge-sup) {
    min-width: 24px !important;
    height: 18px !important;
    line-height: 18px !important;
    font-size: 10px !important;
    top: -8px !important;
    right: -8px !important;
    padding: 0 4px !important;
  }

  /* Mobile RTL optimizations */
  .rtl .notification-item:hover {
    transform: none; /* Disable transform on mobile touch devices */
  }

  .rtl .notification-badge :deep(.n-badge-sup) {
    right: auto !important;
    left: -8px !important;
  }

  /* Enhanced touch targets for mobile */
  .action-btn {
    min-height: 40px; /* Larger touch target */
    min-width: 80px;
    font-size: 13px; /* Slightly larger for better readability */
  }

  .mark-read-btn {
    min-height: 32px;
    padding: 6px 12px;
    font-size: 12px;
  }
  .mark-all-btn {
    min-height: 36px;
    padding: 8px 16px;
    font-size: 13px;
  }

  /* Enhanced RTL mobile button styling */
  .rtl .mark-all-btn {
    min-width: 140px; /* Larger minimum width on mobile for Persian text */
    max-width: 200px; /* Allow more space on mobile */
    font-size: 12px; /* Slightly smaller font to fit better */
    line-height: 1.4; /* Better line height for mobile */
    padding: 10px 18px; /* More generous padding on mobile */
  }
}

/* Extra small screens with enhanced RTL support */
@media (max-width: 480px) {
  .notification-dropdown {
    width: 300px;
    max-width: 90vw; /* Consistent with mobile margins */
    margin: 0 auto; /* Center the dropdown */
  }
  
  .notification-dropdown-header {
    padding: 16px;
  }
  
  .notification-item {
    padding: 14px 16px !important;
  }
    /* Ensure touch targets are easily tappable with RTL consideration */
  .mark-all-btn,
  .action-btn {
    min-height: 36px;
    padding: 0 12px;
  }

  /* RTL button optimization for small screens */
  .rtl .mark-all-btn {
    min-width: 130px; /* Adjusted for very small screens */
    max-width: 170px;
    font-size: 11px; /* Smaller font for small screens */
    padding: 8px 14px;
    white-space: normal; /* Allow wrapping on very small screens */
  }

  /* RTL-specific small mobile adjustments */
  .rtl .notification-dropdown-header {
    text-align: right;
  }

  .rtl .notification-actions {
    flex-direction: row-reverse;
  }

  .rtl .header-title {
    flex-direction: row-reverse;
  }

  /* Improved spacing for RTL on small screens */
  .rtl .notification-item {
    text-align: right;
  }

  .rtl .notification-item--unread {
    border-right: 2px solid var(--n-color-primary); /* Slightly thinner on small screens */
  }
}

/* ===== THEME TRANSITIONS ===== */
.notification-dropdown,
.notification-item,
.notification-dropdown-header,
.mark-all-btn,
.action-btn,
.mark-read-btn {
  transition: background-color 0.3s ease, 
              border-color 0.3s ease, 
              color 0.3s ease,
              box-shadow 0.3s ease;
}

/* ===== CUSTOM POPOVER STYLES ===== */
.custom-notification-popover {
  /* Position and z-index are handled by inline styles */
  box-sizing: border-box;
  contain: layout;
  /* Ensure no content gets clipped */
  min-width: 300px;
  /* Enhanced shadow for mobile visibility */
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
  /* Ensure popover is above everything when scroll is locked */
  z-index: 9999 !important;
  /* Enable hardware acceleration for smooth scrolling */
  transform: translateZ(0);
  -webkit-overflow-scrolling: touch;
}

/* Enhanced shadow for RTL layouts */
.rtl .custom-notification-popover {
  /* Slightly different shadow for RTL to account for reading direction */
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), -2px 4px 8px rgba(0, 0, 0, 0.08);
}

/* Mobile-specific popover enhancements */
@media (max-width: 768px) {
  .custom-notification-popover {
    /* Stronger shadow on mobile for better visibility */
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15), 0 6px 12px rgba(0, 0, 0, 0.1);
    border-radius: 12px; /* Slightly more rounded on mobile */
  }

  .rtl .custom-notification-popover {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15), -3px 6px 12px rgba(0, 0, 0, 0.1);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
</style>
