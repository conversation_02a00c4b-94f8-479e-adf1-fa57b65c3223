# Playwright MCP Testing Guide for SmartNegotiationSection

## 🎯 Manual Testing with <PERSON>wright MCP

Since you have the Microsoft Playwright MCP server installed, you can now use browser automation commands directly in VS Code with GitHub Copilot.

## 🚀 Quick Start Commands

### 1. Navigate to Your Application
```
browser_navigate: { url: "http://localhost:5173" }
```

### 2. Set Mobile Viewport (Your Mobile-First Design)
```
browser_resize: { width: 375, height: 667 }
```

### 3. Take Initial Screenshot
```
browser_take_screenshot: { filename: "munygo-mobile-homepage.png" }
```

### 4. Capture Page Accessibility Snapshot
```
browser_snapshot: {}
```

## 📱 Testing SmartNegotiationSection Component

### Navigation to Component
If your SmartNegotiationSection is accessible via direct navigation:

```typescript
// Navigate to transactional chat or negotiation page
browser_navigate: { url: "http://localhost:5173/transaction/some-id" }

// Or click navigation elements to reach the component
browser_click: { 
  element: "Transaction menu", 
  ref: "[data-testid*='transaction']" 
}
```

### Component Interaction Tests

```typescript
// 1. Test Loading State Detection
browser_snapshot: {} // Check for loading skeleton

// 2. Test Accept Recommendation Button
browser_click: { 
  element: "Accept recommendation button", 
  ref: "[data-testid='accept-recommendation-btn']" 
}

// 3. Test Counter Proposal Button
browser_click: { 
  element: "Propose counter button", 
  ref: "[data-testid='propose-counter-btn']" 
}

// 4. Test Custom Message Input
browser_type: { 
  element: "Custom message input", 
  ref: "[data-testid='custom-message-input']", 
  text: "I would prefer to pay second due to security reasons" 
}

// 5. Test Form Submission
browser_click: { 
  element: "Submit counter offer button", 
  ref: ".submit-counter-btn" 
}
```

### Mobile-First Testing Commands

```typescript
// Test different mobile breakpoints
browser_resize: { width: 320, height: 568 } // Small mobile
browser_take_screenshot: { filename: "negotiation-320px.png" }

browser_resize: { width: 375, height: 667 } // iPhone SE
browser_take_screenshot: { filename: "negotiation-375px.png" }

browser_resize: { width: 768, height: 1024 } // Tablet
browser_take_screenshot: { filename: "negotiation-tablet.png" }
```

### Internationalization Testing

```typescript
// Test Persian language (if you have language switcher)
browser_click: { 
  element: "Language switcher", 
  ref: "[data-testid*='language']" 
}

// Wait for language change
browser_wait_for: { time: 2 }

// Take screenshot of Persian version
browser_take_screenshot: { filename: "negotiation-persian.png" }
```

### Socket.IO Real-time Testing

```typescript
// Monitor network requests for Socket.IO
browser_network_requests: {}

// Check console for Socket.IO connections
browser_console_messages: {}

// Wait for real-time updates
browser_wait_for: { text: "Negotiation updated" }
```

### Accessibility Testing Commands

```typescript
// Test keyboard navigation
browser_press_key: { key: "Tab" }
browser_press_key: { key: "Tab" }
browser_press_key: { key: "Enter" }

// Test with reduced motion
// (You'll need to configure this in browser settings)

// Test with high contrast
// (Browser accessibility settings)
```

## 🔧 Advanced Testing Scenarios

### File Upload Testing (if applicable)
```typescript
browser_file_upload: { 
  paths: ["C:\\Code\\MUNygo\\test-files\\sample-document.pdf"] 
}
```

### PDF Generation Testing
```typescript
browser_pdf_save: { filename: "negotiation-summary.pdf" }
```

### Dialog Handling
```typescript
browser_handle_dialog: { 
  accept: true, 
  promptText: "Yes, I want to proceed" 
}
```

## 📊 Automated Test Scripts

You can also create automated test scripts:

### Example: Complete Negotiation Flow Test
```typescript
// 1. Setup
browser_navigate: { url: "http://localhost:5173" }
browser_resize: { width: 375, height: 667 }

// 2. Navigate to negotiation
// (Adjust based on your app's navigation)

// 3. Test loading states
browser_snapshot: {}

// 4. Test user interactions
browser_click: { element: "Accept recommendation", ref: "[data-testid='accept-recommendation-btn']" }

// 5. Verify results
browser_wait_for: { text: "Recommendation accepted" }
browser_take_screenshot: { filename: "negotiation-completed.png" }
```

## 🚨 Important Notes

1. **Data Test IDs**: Your component already follows the required pattern with `data-testid` attributes
2. **Mobile-First**: Always test on mobile viewports first (320px-768px)
3. **Skeleton Loading**: Look for skeleton placeholders during loading states
4. **Socket.IO**: Monitor network requests for real-time functionality
5. **i18n**: Test both English and Persian versions

## 🎯 Next Steps

1. **Manual Testing**: Use the commands above in VS Code with GitHub Copilot
2. **Automated Tests**: Create Playwright test files using the patterns shown
3. **CI Integration**: Add tests to your deployment workflow
4. **Visual Regression**: Compare screenshots across deployments

## 📝 Example VS Code Usage

In VS Code, you can now ask GitHub Copilot:

> "Using Playwright MCP, navigate to http://localhost:5173, set mobile viewport to 375x667, and take a screenshot of the homepage"

Or:

> "Use browser_click to test the accept recommendation button with data-testid='accept-recommendation-btn'"

The MCP server will execute these commands and show you the results!
