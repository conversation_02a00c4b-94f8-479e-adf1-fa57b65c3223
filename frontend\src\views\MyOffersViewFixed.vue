<template>
  <n-page-header title="My Offers" />
  
  <!-- Main offers table -->
  <n-data-table :columns="columns" :data="offers" :loading="isLoading" />
  
  <!-- Interest requests section -->
  <div v-if="hasInterests" class="interest-requests-container">
    <n-divider>Interest Requests</n-divider>
    
    <div v-for="offer in offersWithInterests" :key="offer.id" class="offer-interest-block">
      <n-card :title="`Offer: ${offer.type} ${offer.amount} CAD at rate ${offer.baseRate}`" class="offer-card">
        <p class="offer-timestamp">Created: {{ formatDate(offer.createdAt) }}</p>
        
        <div class="interest-requests-list">
          <InterestRequestCard
            v-for="interest in offer.interests"
            :key="interest.id"
            :interest="interest"
          />
        </div>
      </n-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ref, onMounted, h } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { NPageHeader, NDataTable, NButton, NCard, NDivider, useMessage } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import { offerService } from '@/services/offerService';
import InterestRequestCard from '../components/InterestRequestCard.vue';
import { updateOfferStatus } from '@/services/offerStatusService';
import { NSwitch } from 'naive-ui';

// Define an interface for your offer object
interface Offer {
  id: string;
  type: string;
  amount: number;
  baseRate: number;
  adjustmentForLowerRep?: number;
  adjustmentForHigherRep?: number;
  status: string;
  createdAt: string;
  interests?: {
    id: string;
    interestedUserId: string;
    username: string;
    reputationLevel: number;
    status: 'PENDING' | 'ACCEPTED' | 'DECLINED';
    chatSessionId?: string;
    reasonCode?: string;
  }[];
}

const authStore = useAuthStore();
const message = useMessage();
const router = useRouter();
const offers = ref<(Offer & { _statusLoading?: boolean })[]>([]); // Add local loading state
const isLoading = ref(false);

// Computed properties to check for interest requests
const hasInterests = computed(() => offers.value.some(offer => 
  offer.interests && offer.interests.length > 0
));

const offersWithInterests = computed(() => 
  offers.value.filter(offer => offer.interests && offer.interests.length > 0)
);

const userTier = computed(() => authStore.user?.reputationLevel ?? 3);
function getLowerTierLabel() {
  if (!userTier.value) return 'L1-L2';
  if (userTier.value === 1) return 'No Lower Tiers';
  if (userTier.value === 2) return 'L1';
  if (userTier.value === 3) return 'L1-L2';
  if (userTier.value === 4) return 'L1-L3';
  if (userTier.value === 5) return 'L1-L4';
  return 'Lower';
}
function getHigherTierLabel() {
  if (!userTier.value) return 'L4-L5';
  if (userTier.value === 5) return 'No Higher Tiers';
  if (userTier.value === 4) return 'L5';
  if (userTier.value === 3) return 'L4-L5';
  if (userTier.value === 2) return 'L3-L5';
  if (userTier.value === 1) return 'L2-L5';
  return 'Higher';
}

const columns = computed<DataTableColumns<Offer>>(() => {
  const cols: DataTableColumns<Offer> = [
    { title: 'Type', key: 'type' },
    { title: 'Amount (CAD)', key: 'amount' },
    { title: 'Base Rate', key: 'baseRate' },
  ];
  // Only show lower rep adjustment if user is not at tier 1
  if (userTier.value !== 1) {
    cols.push({
      title: `${getLowerTierLabel()} Adj. (%)`,
      key: 'adjustmentForLowerRep',
      render(row: Offer) {
        return row.adjustmentForLowerRep?.toFixed(2) ?? '-';
      },
    });
  }
  // Only show higher rep adjustment if user is not at tier 5
  if (userTier.value !== 5) {
    cols.push({
      title: `${getHigherTierLabel()} Adj. (%)`,
      key: 'adjustmentForHigherRep',
      render(row: Offer) {
        return row.adjustmentForHigherRep?.toFixed(2) ?? '-';
      },
    });
  }
  cols.push({
    title: 'Status',
    key: 'status',
    render(row: Offer & { _statusLoading?: boolean }) {
      return h(
        NSwitch,
        {
          value: row.status === 'ACTIVE',
          checkedValue: true,
          uncheckedValue: false,
          loading: !!row._statusLoading,
          disabled: !!row._statusLoading,
          onUpdateValue: async (checked: boolean) => {
            row._statusLoading = true;
            try {
              const newStatus = checked ? 'ACTIVE' : 'INACTIVE';
              await updateOfferStatus(row.id, newStatus);
              row.status = newStatus;
              message.success(`Offer status set to ${newStatus === 'ACTIVE' ? 'Active' : 'Inactive'}`);
            } catch (err: any) {
              message.error('Failed to update offer status.');
            } finally {
              row._statusLoading = false;
            }
          },
        },
        {
          default: () => row.status === 'ACTIVE' ? 'Active' : 'Inactive',
        }
      );
    },
  });
  cols.push({
    title: 'Created At',
    key: 'createdAt',
    render(row: Offer) {
      return formatDate(row.createdAt);
    },
  });
  
  cols.push({
    title: 'Actions',
    key: 'actions',
    render(row: Offer) {
      return h(
        NButton,
        {
          size: 'small',
          onClick: () => router.push({ name: 'EditOffer', params: { offerId: row.id } }),
        },
        { default: () => 'Edit' }
      );
    },
  });
  return cols;
});

// Format ISO date string to a readable local string
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  // Example: 'May 9, 2025, 11:08 PM' (locale-aware)
  return date.toLocaleString();
}

onMounted(async () => {
  if (!authStore.isAuthenticated || !authStore.user?.id) {
    message.error('You must be logged in to view your offers.');
    router.push({ name: 'Login' });
    return;
  }
  isLoading.value = true;
  try {
    const data = await offerService.getMyOffers(); // Corrected call
    offers.value = data.map(offer => ({ ...offer, _statusLoading: false }));
  } catch (error) {
    message.error('Failed to load your offers.');
    console.error('Error fetching my offers:', error);
  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped>
.interest-requests-container {
  margin-top: 2rem;
}

.offer-interest-block {
  margin-bottom: 1.5rem;
}

.offer-card {
  background-color: #f9f9f9;
}

.offer-timestamp {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.interest-requests-list {
  margin-top: 1rem;
}
</style>
