# MUNygo Production Deployment Script (PowerShell)
# =================================================

Write-Host "🚀 MUNygo Production Deployment Script" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Function to check if a command exists
function Test-CommandExists {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Check prerequisites
Write-Host "📋 Checking prerequisites..." -ForegroundColor Yellow

if (-not (Test-CommandExists "docker")) {
    Write-Host "❌ Docker is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

if (-not (Test-CommandExists "docker-compose")) {
    Write-Host "❌ Docker Compose is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Prerequisites check passed" -ForegroundColor Green

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "❌ .env file not found! Please create it with required variables." -ForegroundColor Red
    exit 1
}

Write-Host "📄 Environment file found" -ForegroundColor Green

# Load and verify environment variables
Write-Host "🔍 Verifying critical environment variables..." -ForegroundColor Yellow

$envContent = Get-Content ".env" | Where-Object { $_ -match "^[^#].*=" }
$envVars = @{}
foreach ($line in $envContent) {
    if ($line -match "^([^=]+)=(.*)$") {
        $envVars[$matches[1]] = $matches[2]
    }
}

$requiredVars = @(
    "POSTGRES_DB",
    "POSTGRES_USER", 
    "POSTGRES_PASSWORD",
    "DATABASE_URL",
    "JWT_SECRET",
    "VITE_BACKEND_URL_FOR_CLIENT",
    "VITE_ADMIN_EMAILS",
    "GEMINI_API_KEY"
)

foreach ($var in $requiredVars) {
    if (-not $envVars.ContainsKey($var) -or [string]::IsNullOrWhiteSpace($envVars[$var])) {
        Write-Host "❌ Required environment variable $var is not set" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ All required environment variables are set" -ForegroundColor Green

# Show current configuration
Write-Host ""
Write-Host "📊 Current Configuration:" -ForegroundColor Cyan
Write-Host "  Database: $($envVars['POSTGRES_DB'])" -ForegroundColor White
Write-Host "  Database User: $($envVars['POSTGRES_USER'])" -ForegroundColor White
Write-Host "  Backend URL: $($envVars['VITE_BACKEND_URL_FOR_CLIENT'])" -ForegroundColor White
Write-Host "  Debug Reports: $($envVars['VITE_ENABLE_DEBUG_REPORT'])" -ForegroundColor White
Write-Host "  Admin Emails: $($envVars['VITE_ADMIN_EMAILS'])" -ForegroundColor White
$geminiKey = $envVars['GEMINI_API_KEY']
Write-Host "  Gemini API: $($geminiKey.Substring(0, [Math]::Min(10, $geminiKey.Length)))..." -ForegroundColor White
Write-Host ""

# Ask for confirmation
$confirmation = Read-Host "🤔 Do you want to proceed with deployment? (y/N)"
if ($confirmation -notmatch "^[Yy]$") {
    Write-Host "🛑 Deployment cancelled by user" -ForegroundColor Yellow
    exit 0
}

try {
    Write-Host "🧹 Step 1: Cleaning up existing containers and volumes..." -ForegroundColor Yellow
    docker-compose down -v --remove-orphans
    docker system prune -f
    Write-Host "✅ Cleanup completed" -ForegroundColor Green

    Write-Host "🔧 Step 2: Building images without cache..." -ForegroundColor Yellow
    docker-compose build --no-cache --progress=plain
    Write-Host "✅ Build completed" -ForegroundColor Green

    Write-Host "🗄️ Step 3: Starting PostgreSQL and waiting for it to be ready..." -ForegroundColor Yellow
    docker-compose up -d postgres
    Write-Host "⏳ Waiting for PostgreSQL to be ready..." -ForegroundColor Yellow

    # Wait for PostgreSQL to be ready
    $maxAttempts = 30
    $attempt = 1
    do {
        $pgReady = docker-compose exec postgres pg_isready -U $envVars['POSTGRES_USER'] -d $envVars['POSTGRES_DB'] 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PostgreSQL is ready" -ForegroundColor Green
            break
        }
        Write-Host "  Still waiting... ($attempt/$maxAttempts)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempt++
    } while ($attempt -le $maxAttempts)

    if ($attempt -gt $maxAttempts) {
        Write-Host "❌ PostgreSQL failed to start within 60 seconds" -ForegroundColor Red
        docker-compose logs postgres
        exit 1
    }

    Write-Host "🗃️ Step 4: Running database migrations..." -ForegroundColor Yellow
    docker-compose run --rm backend npx prisma migrate deploy
    Write-Host "✅ Database migrations completed" -ForegroundColor Green

    Write-Host "🎯 Step 5: Starting all services..." -ForegroundColor Yellow
    docker-compose up -d
    Write-Host "✅ All services started" -ForegroundColor Green

    Write-Host "🏥 Step 6: Health checks..." -ForegroundColor Yellow
    Write-Host "⏳ Waiting for services to be healthy..." -ForegroundColor Yellow

    # Wait for backend health check
    $maxAttempts = 60
    $attempt = 1
    do {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Backend is healthy" -ForegroundColor Green
                break
            }
        } catch {
            # Continue waiting
        }
        Write-Host "  Waiting for backend... ($attempt/$maxAttempts)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempt++
    } while ($attempt -le $maxAttempts)

    # Wait for frontend
    $maxAttempts = 30
    $attempt = 1
    do {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 2 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Frontend is accessible" -ForegroundColor Green
                break
            }
        } catch {
            # Continue waiting
        }
        Write-Host "  Waiting for frontend... ($attempt/$maxAttempts)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempt++
    } while ($attempt -le $maxAttempts)

    Write-Host ""
    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📍 Service URLs:" -ForegroundColor Cyan
    Write-Host "  🌐 Frontend: http://localhost:8080" -ForegroundColor White
    Write-Host "  🔧 Backend API: http://localhost:3000" -ForegroundColor White
    Write-Host "  📊 Admin Debug Dashboard: http://localhost:8080/admin/debug-dashboard" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 Debug Features:" -ForegroundColor Cyan
    Write-Host "  • Debug Report Button: Enabled ($($envVars['VITE_ENABLE_DEBUG_REPORT']))" -ForegroundColor White
    Write-Host "  • Admin Emails: $($envVars['VITE_ADMIN_EMAILS'])" -ForegroundColor White
    Write-Host "  • AI Analysis: Enabled (Gemini API configured)" -ForegroundColor White
    Write-Host ""
    Write-Host "🧪 Quick Tests:" -ForegroundColor Cyan
    Write-Host "  1. Open http://localhost:8080 and register/login" -ForegroundColor White
    Write-Host "  2. Check for debug report button (bug icon) in the navbar" -ForegroundColor White
    Write-Host "  3. If you're an admin, access: http://localhost:8080/admin/debug-dashboard" -ForegroundColor White
    Write-Host ""
    Write-Host "📋 To check service status:" -ForegroundColor Cyan
    Write-Host "  docker-compose ps" -ForegroundColor White
    Write-Host ""
    Write-Host "📜 To view logs:" -ForegroundColor Cyan
    Write-Host "  docker-compose logs -f [service_name]" -ForegroundColor White
    Write-Host ""
    Write-Host "🛑 To stop all services:" -ForegroundColor Cyan
    Write-Host "  docker-compose down" -ForegroundColor White

} catch {
    Write-Host ""
    Write-Host "❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 Troubleshooting steps:" -ForegroundColor Yellow
    Write-Host "  1. Check Docker logs: docker-compose logs" -ForegroundColor White
    Write-Host "  2. Verify .env file has all required variables" -ForegroundColor White
    Write-Host "  3. Ensure Docker daemon is running" -ForegroundColor White
    Write-Host "  4. Check available disk space and memory" -ForegroundColor White
    exit 1
}
