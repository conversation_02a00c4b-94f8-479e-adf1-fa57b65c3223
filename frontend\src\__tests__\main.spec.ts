import { describe, it, expect, vi } from 'vitest';

vi.mock('vue', async (importOriginal) => {
  const actual = await importOriginal() as Record<string, unknown>;
  return {
    ...actual,
    createApp: vi.fn(() => ({
      use: vi.fn().mockReturnThis(),
      mount: vi.fn()
    })),
  };
});
vi.mock('pinia', () => ({
  createPinia: vi.fn(),
  defineStore: vi.fn(() => vi.fn()) // Add this line to mock defineStore
}));
vi.mock('../router', () => ({
  __esModule: true,
  default: {},
}));

// Import after mocks
import '../main';

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import type { Mock } from 'vitest';


describe('main.ts', () => {
  it('creates app, installs pinia and router, and mounts', () => {
    expect(createApp).toHaveBeenCalled();
    expect(createPinia).toHaveBeenCalled();
    // Check that use and mount were called
    const mockedCreateApp = createApp as unknown as Mock;
    const appInstance = mockedCreateApp.mock.results[0].value;
    expect(appInstance.use).toHaveBeenCalled();
    expect(appInstance.mount).toHaveBeenCalledWith('#app');
  });
});
