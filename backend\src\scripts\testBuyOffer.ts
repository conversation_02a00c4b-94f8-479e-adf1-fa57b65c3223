import { PrismaClient } from '@prisma/client';
import type { TransactionService } from '../services/transactionService';
import type { PayerNegotiationService } from '../services/payerNegotiationService';
import { createInitialTransaction } from '../services/transactionService';

const prisma = new PrismaClient();

async function testBuyOfferTransaction() {
  console.log('🚀 Testing BUY offer transaction mapping...\n');

  let testUser1: any = null, testUser2: any = null, testOffer: any = null, 
      testInterest: any = null, testChatSession: any = null, transaction: any = null;

  try {
    // Step 1: Create test users
    console.log('📝 Step 1: Creating test users...');
    testUser1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword1',
        username: 'BuyTestUser1',
        reputationLevel: 5,
        phoneVerified: true,
        emailVerified: true
      }
    });

    testUser2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword2',
        username: 'BuyTestUser2', 
        reputationLevel: 3,
        phoneVerified: true,
        emailVerified: true
      }
    });

    console.log(`   ✅ User1 created: ${testUser1.username} (ID: ${testUser1.id})`);
    console.log(`   ✅ User2 created: ${testUser2.username} (ID: ${testUser2.id})\n`);

    // Step 2: Create BUY offer - User1 wants to BUY 100 CAD for 200 IRR
    console.log('📝 Step 2: Creating BUY offer...');
    
    testOffer = await prisma.offer.create({
      data: {
        userId: testUser1.id,
        type: 'BUY',  // User1 wants to BUY CAD
        currencyPair: 'CAD-IRR',
        amount: 100,  // 100 CAD
        baseRate: 2,  // 1 CAD = 2 IRR
        adjustmentForLowerRep: 2.0,
        adjustmentForHigherRep: 1.0,
        status: 'ACTIVE'
      }
    });

    console.log(`   ✅ Offer created: User1 wants to ${testOffer.type} ${testOffer.amount} CAD for ${testOffer.amount * 2} IRR`);
    console.log(`   ✅ This means: User1 gives IRR, User2 gives CAD\n`);

    // Step 3: Create interest and chat session
    console.log('📝 Step 3: Creating interest and chat session...');
    
    testInterest = await prisma.interest.create({
      data: {
        offerId: testOffer.id,
        interestedUserId: testUser2.id,
        status: 'ACCEPTED'
      }
    });

    testChatSession = await prisma.chatSession.create({
      data: {
        offerId: testOffer.id,
        userOneId: testUser1.id, // Offer creator (wants to buy CAD)
        userTwoId: testUser2.id, // Interested user (has CAD to sell)
        interestId: testInterest.id
      }
    });

    console.log(`   ✅ Interest created and accepted`);
    console.log(`   ✅ Chat session created (ID: ${testChatSession.id})\n`);

    // Step 4: Create transaction manually to see the logic
    console.log('📝 Step 4: Creating transaction to test currency providers...');
    
    // Based on BUY logic from interestService.ts:
    // For BUY offers: 
    // - currencyAProvider = interestedUser (User2 provides CAD)
    // - currencyBProvider = offerCreator (User1 provides IRR)
    
    transaction = await prisma.$transaction(async (tx) => {
      return await createInitialTransaction(
        tx,
        testChatSession.id,
        testOffer.id,
        'CAD', // Currency A (what User1 wants to buy)
        100,   // Amount A 
        testUser2.id, // Currency A Provider (User2 gives CAD)
        'IRR', // Currency B (what User1 pays with)
        200,   // Amount B (100 * 2 rate)
        testUser1.id  // Currency B Provider (User1 gives IRR)
      );
    });

    console.log(`   ✅ Transaction created (ID: ${transaction.id})`);
    console.log(`   ✅ Currency A (CAD) Provider: ${transaction.currencyAProviderId} (should be User2: ${testUser2.id})`);
    console.log(`   ✅ Currency B (IRR) Provider: ${transaction.currencyBProviderId} (should be User1: ${testUser1.id})`);
    
    // Verify the mapping
    const isCorrect = (
      transaction.currencyAProviderId === testUser2.id && 
      transaction.currencyBProviderId === testUser1.id
    );
    
    console.log(`   ${isCorrect ? '✅' : '❌'} Currency provider mapping is ${isCorrect ? 'CORRECT' : 'WRONG'}\n`);

    // Step 5: Test what the frontend would receive
    console.log('📝 Step 5: Testing frontend transaction info endpoint...');
// Simulate the chat route transaction endpoint logic
    const offer = await prisma.offer.findUnique({
      where: { id: testOffer.id },
      include: {
        user: { 
          select: { id: true, username: true, reputationLevel: true, email: true },
        },
      },
    });

    if (!offer) {
      throw new Error('Offer not found');
    }

    const session = await prisma.chatSession.findUnique({
      where: { id: testChatSession.id },
      include: {
        transaction: true,
        userOne: { 
          select: { id: true, username: true, reputationLevel: true, email: true },
        },
        userTwo: { 
          select: { id: true, username: true, reputationLevel: true, email: true },
        },
      },
    });

    if (!session) {
      throw new Error('Chat session not found');
    }

    // Determine offer creator and taker
    const transactionOfferCreator = offer.user;
    let transactionOfferTaker;
    if (session.userOneId === transactionOfferCreator.id) {
      transactionOfferTaker = session.userTwo;
    } else {
      transactionOfferTaker = session.userOne;
    }
    // Calculate cadSellerId based on offer type
    let cadSellerId = null;
    if (offer!.type === 'BUY') {
      // User1 buys CAD (pays with IRR), so User2 sells CAD
      cadSellerId = transactionOfferTaker!.id; // User2 sells CAD
    }

    console.log(`   ✅ Offer Creator: ${transactionOfferCreator.username} (${transactionOfferCreator.id})`);
    console.log(`   ✅ Offer Taker: ${transactionOfferTaker!.username} (${transactionOfferTaker!.id})`);
    console.log(`   ✅ CAD Seller ID: ${cadSellerId} (should be User2: ${testUser2.id})`);
    
    const frontendPayload = {
      offerType: offer!.type,
      amount: offer!.amount,
      currencyPair: offer!.currencyPair,
      cadSellerId: cadSellerId,
      finalCadAmount: 100,
      finalAssetAmount: 200,
      finalAssetCurrency: 'IRR',
      offerCreator: {
        id: transactionOfferCreator.id,
        username: transactionOfferCreator.username,
      },
      otherUser: {
        id: transactionOfferTaker!.id,
        username: transactionOfferTaker!.username,
      }
    };

    console.log('\n📊 Frontend would receive:');
    console.log(`   Offer Type: ${frontendPayload.offerType}`);
    console.log(`   Offer Creator: ${frontendPayload.offerCreator.username} (wants to BUY CAD)`);
    console.log(`   Other User: ${frontendPayload.otherUser.username} (will sell CAD)`);
    console.log(`   CAD Seller ID: ${frontendPayload.cadSellerId}`);
    console.log(`   Final CAD Amount: ${frontendPayload.finalCadAmount}`);
    console.log(`   Final Asset Amount: ${frontendPayload.finalAssetAmount} ${frontendPayload.finalAssetCurrency}`);

    console.log('\n🎯 CORRECT PAYMENT MAPPING:');
    console.log(`   ${frontendPayload.offerCreator.username} pays: ${frontendPayload.finalAssetAmount} ${frontendPayload.finalAssetCurrency}`);
    console.log(`   ${frontendPayload.otherUser.username} pays: ${frontendPayload.finalCadAmount} CAD`);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Cleanup
    console.log('\n🧹 Cleaning up test data...');
    
    if (transaction) {
      try {
        await prisma.transaction.delete({ where: { id: transaction.id } });
      } catch (error) {
        console.warn('Failed to delete transaction:', error);
      }
    }
    if (testChatSession) {
      try {
        await prisma.chatSession.delete({ where: { id: testChatSession.id } });
      } catch (error) {
        console.warn('Failed to delete chat session:', error);
      }
    }
    if (testInterest) {
      try {
        await prisma.interest.delete({ where: { id: testInterest.id } });
      } catch (error) {
        console.warn('Failed to delete interest:', error);
      }
    }
    if (testOffer) {
      try {
        await prisma.offer.delete({ where: { id: testOffer.id } });
      } catch (error) {
        console.warn('Failed to delete offer:', error);
      }
    }
    if (testUser1) {
      try {
        await prisma.user.delete({ where: { id: testUser1.id } });
      } catch (error) {
        console.warn('Failed to delete user1:', error);
      }
    }
    if (testUser2) {
      try {
        await prisma.user.delete({ where: { id: testUser2.id } });
      } catch (error) {
        console.warn('Failed to delete user2:', error);
      }
    }
    
    console.log('   ✅ Cleanup completed');
    
    await prisma.$disconnect();
  }
}

testBuyOfferTransaction().catch(console.error);
