# MUNygo Frontend: Features & Processes (2025)

## 📱 Core Application Pages & Views

### Authentication & User Management
- **LandingView**: Public homepage with app introduction
- **RegisterView**: User registration with email/password
- **LoginView**: User authentication
- **VerifyEmailView**: Email verification process
- **ProfileView**: User profile management with phone verification

### Offer Management System
- **CreateOfferView**: Create new currency exchange offers (requires phone verification)
- **EditOfferView**: Edit existing offers with real-time updates
- **MyOffersView**: Manage personal offers with desktop table & mobile card views
- **BrowseOffersView**: Browse available offers with responsive grid layout
- **OfferDetailsView**: Detailed offer information display

### Real-Time Communication
- **ChatView**: Advanced chat interface with:
  - Transaction integration
  - Real-time messaging
  - System message handling
  - Glassmorphism floating transaction section
  - Collapsible transaction details

### Transaction & Matching System
- **MatchListView**: Comprehensive match management with:
  - Filter tabs (Needing Response, Active, Completed, All)
  - Pull-to-refresh functionality
  - Status-based filtering
  - Real-time match updates

### Dashboard & Navigation
- **HomeView**: Feature-rich dashboard with:
  - Hero section with primary CTAs
  - Statistics display with animations
  - Quick action cards
  - Recent activity feed
  - User reputation display

## 🔧 Advanced Components & Features

### Mobile-First UI Components
- **ModernNavBar** / **DreamNavBar**: Multiple navigation implementations
- **OfferCard** / **OfferSummaryCard**: Mobile-optimized offer displays
- **TransactionFlowCardV2/V3**: Advanced transaction management
- **TransactionDetailCard**: Transaction information display
- **TransactionTimelineLog**: Transaction progress tracking
- **PaymentReadinessGate**: Payment collection workflow

### Real-Time Features
- **NotificationBell**: Real-time notification system
- **ConnectionStatus**: Socket connection monitoring
- **InterestRequestCard**: Interest management
- **MatchNotificationCard**: Match system notifications

### Enhanced User Experience
- **ReputationIcon**: User reputation display
- **LanguageSelector**: Multi-language support (EN/FA)
- **ThemeToggle**: Light/dark theme switching
- **DeclineInterestModal**: Interest rejection workflow

### 🎯 Advanced AI & Debug Features
- **DebugReportButton** / **DebugReportButtonEnhanced**: Bug reporting system
- **VoiceRecorder**: Voice-to-text bug reporting
- **TagSelector** (V1/V2/New): AI-powered tag selection
- **EnhancedDebugReportTest**: Advanced debugging tools

### 🔬 Admin & Development Tools
- **DebugDashboardViewCustom**: Admin debug dashboard
- **DiagnosticDataCard**: System diagnostics
- **ReportDetailsModal**: Debug report management
- **NavBarDemoView**: Component development testing

## 📊 State Management & Data Stores

### Core Business Logic Stores
- **authStore**: Authentication & user management
- **offerStore**: Browsable offers management
- **myOffersStore**: Personal offers management
- **interestStore**: Interest expression & processing
- **transactionStore**: Transaction flow management
- **matchStore**: Offer matching system
- **chatStore**: Real-time chat functionality

### System & UI Stores
- **notificationStore**: Real-time notifications (13+ notification types)
- **payerNegotiation**: Payment designation system
- **connectionStore**: Socket connection status
- **languageStore**: Internationalization
- **themeStore**: Theme management

### 🤖 Advanced Feature Stores
- **aiAnalysisStore**: AI-powered voice analysis
- **tagStore**: AI tag management system
- **adminDebugStore**: Admin debugging features

## 🚀 Real-Time Features & Socket Events

### Live Updates Via Socket.IO
- Offer creation/updates/status changes
- Interest expression/acceptance/decline
- Match found/accepted/declined/expired/converted
- Transaction status changes & timer updates
- Chat message delivery
- Notification delivery (13+ types)
- Payment declarations & confirmations

## 🎨 Mobile-First Design Implementation

### Responsive Design Features
- Mobile viewport optimization (320px-768px base)
- Progressive enhancement for tablet (768px+) and desktop (1024px+)
- Touch-friendly 44px minimum touch targets
- Thumb-zone optimized navigation
- Vertical-first layouts with horizontal enhancements
- Mobile-optimized forms and interactions

### Performance Optimizations
- Lazy-loaded route components
- Centralized socket management (prevents connection conflicts)
- Real-time state synchronization
- Mobile-optimized asset loading

## 🔒 Security & Authentication

### Verification Systems
- Email verification with token validation
- Phone verification with OTP (Twilio integration)
- JWT-based session management
- Route guards (auth required, phone verification required, admin access)

## 🌐 Internationalization & Accessibility
- Complete i18n support (Persian FA + English EN)
- RTL text support for Persian
- Mobile-optimized text sizing
- Screen reader compatibility
- High contrast theme support

## ⚡ Performance & Development

### Development Features
- Hot reload for instant development feedback
- Comprehensive TypeScript integration
- Mobile-first testing environments
- Component isolation testing
- Socket event debugging tools

---

This document provides a comprehensive, up-to-date overview of the MUNygo frontend's features, processes, and architecture as of June 2025.
