import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { authMiddleware, AuthVariables } from '../middleware/auth';
import { PaymentInfoService } from '../services/paymentInfoService';

// Validation schemas
const CreatePaymentInfoSchema = z.object({
  bankName: z.string().min(1, 'Bank name is required').max(100),
  accountNumber: z.string().min(1, 'Account number is required').max(50),
  accountHolderName: z.string().min(1, 'Account holder name is required').max(100),
  isDefault: z.boolean().optional()
});

const UpdatePaymentInfoSchema = z.object({
  bankName: z.string().min(1).max(100).optional(),
  accountNumber: z.string().min(1).max(50).optional(),
  accountHolderName: z.string().min(1).max(100).optional(),
  isDefaultForUser: z.boolean().optional()
});

// Variables available to handlers in this route module
type PaymentInfoRoutesVariables = AuthVariables & {
  paymentInfoService: PaymentInfoService;
};

export function createPaymentInfoRoutes(prisma: PrismaClient) {
  const paymentInfoRoutes = new Hono<{ Variables: PaymentInfoRoutesVariables }>();

  // Middleware to inject PaymentInfoService with shared PrismaClient
  paymentInfoRoutes.use('*', authMiddleware, async (c, next) => {
    const paymentInfoService = new PaymentInfoService(prisma);
    c.set('paymentInfoService', paymentInfoService);
    await next();
  });

  /**
   * GET /api/payment-info
   * Get all payment info for the authenticated user
   */
  paymentInfoRoutes.get('/', async (c) => {
    const { userId } = c.get('jwtPayload');
    const paymentInfoService = c.get('paymentInfoService');

    try {
      console.log(`[PaymentInfoRoutes] GET / for user ${userId}`);
      
      const paymentInfoList = await paymentInfoService.getUserPaymentInfo(userId);

      return c.json({
        success: true,
        data: paymentInfoList
      });
    } catch (error: any) {
      console.error(`[PaymentInfoRoutes] Error fetching payment info:`, error);
      
      return c.json({ 
        success: false, 
        error: 'Failed to fetch payment information' 
      }, 500);
    }
  });

  /**
   * GET /api/payment-info/default
   * Get default payment info for the authenticated user
   */
  paymentInfoRoutes.get('/default', async (c) => {
    const { userId } = c.get('jwtPayload');
    const paymentInfoService = c.get('paymentInfoService');

    try {
      console.log(`[PaymentInfoRoutes] GET /default for user ${userId}`);
      
      const defaultPaymentInfo = await paymentInfoService.getDefaultPaymentInfo(userId);

      return c.json({
        success: true,
        data: defaultPaymentInfo
      });
    } catch (error: any) {
      console.error(`[PaymentInfoRoutes] Error fetching default payment info:`, error);
      
      return c.json({ 
        success: false, 
        error: 'Failed to fetch default payment information' 
      }, 500);
    }
  });

  /**
   * POST /api/payment-info
   * Create new payment info for the authenticated user
   */
  paymentInfoRoutes.post(
    '/',
    zValidator('json', CreatePaymentInfoSchema),
    async (c) => {
      const { userId } = c.get('jwtPayload');
      const { bankName, accountNumber, accountHolderName, isDefault } = c.req.valid('json');
      const paymentInfoService = c.get('paymentInfoService');

      try {
        console.log(`[PaymentInfoRoutes] POST / for user ${userId}`);
        
        const paymentInfo = await paymentInfoService.createPaymentInfo(
          userId,
          bankName,
          accountNumber,
          accountHolderName,
          isDefault || false
        );

        return c.json({
          success: true,
          data: paymentInfo,
          message: 'Payment information created successfully'
        });
      } catch (error: any) {
        console.error(`[PaymentInfoRoutes] Error creating payment info:`, error);
        
        return c.json({ 
          success: false, 
          error: 'Failed to create payment information' 
        }, 500);
      }
    }
  );

  /**
   * PUT /api/payment-info/:id
   * Update payment info for the authenticated user
   */
  paymentInfoRoutes.put(
    '/:id',
    zValidator('json', UpdatePaymentInfoSchema),
    async (c) => {
      const { id } = c.req.param();
      const { userId } = c.get('jwtPayload');
      const updates = c.req.valid('json');
      const paymentInfoService = c.get('paymentInfoService');

      try {
        console.log(`[PaymentInfoRoutes] PUT /:id - ${id} for user ${userId}`);
        
        const paymentInfo = await paymentInfoService.updatePaymentInfo(
          id,
          userId,
          updates
        );

        return c.json({
          success: true,
          data: paymentInfo,
          message: 'Payment information updated successfully'
        });
      } catch (error: any) {
        console.error(`[PaymentInfoRoutes] Error updating payment info:`, error);
        
        if (error.message?.includes('not found') || error.message?.includes('access denied')) {
          return c.json({ 
            success: false, 
            error: 'Payment information not found or access denied' 
          }, 404);
        }
        
        return c.json({ 
          success: false, 
          error: 'Failed to update payment information' 
        }, 500);
      }
    }
  );

  /**
   * DELETE /api/payment-info/:id
   * Delete payment info for the authenticated user
   */
  paymentInfoRoutes.delete('/:id', async (c) => {
    const { id } = c.req.param();
    const { userId } = c.get('jwtPayload');
    const paymentInfoService = c.get('paymentInfoService');

    try {
      console.log(`[PaymentInfoRoutes] DELETE /:id - ${id} for user ${userId}`);
      
      await paymentInfoService.deletePaymentInfo(id, userId);

      return c.json({
        success: true,
        message: 'Payment information deleted successfully'
      });
    } catch (error: any) {
      console.error(`[PaymentInfoRoutes] Error deleting payment info:`, error);
      
      if (error.message?.includes('not found') || error.message?.includes('access denied')) {
        return c.json({ 
          success: false, 
          error: 'Payment information not found or access denied' 
        }, 404);
      }
      
      return c.json({ 
        success: false, 
        error: 'Failed to delete payment information' 
      }, 500);
    }
  });

  /**
   * POST /api/payment-info/:id/set-default
   * Set payment info as default for the authenticated user
   */
  paymentInfoRoutes.post('/:id/set-default', async (c) => {
    const { id } = c.req.param();
    const { userId } = c.get('jwtPayload');
    const paymentInfoService = c.get('paymentInfoService');

    try {
      console.log(`[PaymentInfoRoutes] POST /:id/set-default - ${id} for user ${userId}`);
      
      const paymentInfo = await paymentInfoService.setDefaultPaymentInfo(id, userId);

      return c.json({
        success: true,
        data: paymentInfo,
        message: 'Default payment information updated successfully'
      });
    } catch (error: any) {
      console.error(`[PaymentInfoRoutes] Error setting default payment info:`, error);
      
      if (error.message?.includes('not found') || error.message?.includes('access denied')) {
        return c.json({ 
          success: false, 
          error: 'Payment information not found or access denied' 
        }, 404);
      }
      
      return c.json({ 
        success: false, 
        error: 'Failed to set default payment information' 
        }, 500);
    }
  });

  return paymentInfoRoutes;
}
