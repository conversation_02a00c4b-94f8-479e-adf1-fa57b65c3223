import { z } from 'zod';

// Tag Category schemas
export const TagCategorySchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(50),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  icon: z.string().max(50).optional(),
  sortOrder: z.number().int().default(0),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date()
});

export const CreateTagCategorySchema = z.object({
  name: z.string().min(1).max(50),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  icon: z.string().max(50).optional(),
  sortOrder: z.number().int().default(0)
});

// Base Predefined Tag schema without relations
export const BasePredefinedTagSchema = z.object({
  id: z.string().uuid(),
  tagKey: z.string().min(1).max(50),
  categoryId: z.string().uuid().optional(),
  parentTagId: z.string().uuid().optional(),
  defaultLabelEn: z.string().min(1).max(100),
  defaultLabelFa: z.string().max(100).optional(),
  descriptionEn: z.string().optional(),
  descriptionFa: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  sortOrder: z.number().int().default(0),
  applicableReportTypes: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date()
});

// Predefined Tag schema with relations - using lazy evaluation for circular references
export const PredefinedTagSchema: z.ZodType<{
  id: string;
  tagKey: string;
  categoryId?: string;
  parentTagId?: string;
  defaultLabelEn: string;
  defaultLabelFa?: string;
  descriptionEn?: string;
  descriptionFa?: string;
  color?: string;
  sortOrder?: number;
  applicableReportTypes?: string[];
  isActive?: boolean;
  createdAt: Date;
  updatedAt: Date;
  category?: any;
  parentTag?: any;
  childTags?: any[];
  metadata?: any[];
}> = z.lazy(() => BasePredefinedTagSchema.extend({
  category: TagCategorySchema.optional(),
  parentTag: PredefinedTagSchema.optional(),
  childTags: z.array(PredefinedTagSchema).optional(),
  metadata: z.array(z.object({
    metadataKey: z.string(),
    metadataValue: z.string()
  })).optional()
}));

export const CreatePredefinedTagSchema = z.object({
  tagKey: z.string().min(1).max(50),
  categoryId: z.string().uuid().optional(),
  parentTagId: z.string().uuid().optional(),
  defaultLabelEn: z.string().min(1).max(100),
  defaultLabelFa: z.string().max(100).optional(),
  descriptionEn: z.string().optional(),
  descriptionFa: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  sortOrder: z.number().int().default(0),
  applicableReportTypes: z.array(z.string()).default([]),
  metadata: z.array(z.object({
    metadataKey: z.string(),
    metadataValue: z.string()
  })).optional() // array of key-value objects for metadata
});

export const UpdatePredefinedTagSchema = CreatePredefinedTagSchema.partial().extend({
  id: z.string().uuid()
});

// Tag Metadata schemas
export const TagMetadataSchema = z.object({
  id: z.string().uuid(),
  tagId: z.string().uuid(),
  metadataKey: z.string().min(1).max(50),
  metadataValue: z.string(),
  createdAt: z.date()
});

// API Response schemas
export const TagCategoriesResponseSchema = z.object({
  categories: z.array(TagCategorySchema),
  total: z.number()
});

export const PredefinedTagsResponseSchema = z.object({
  tags: z.array(PredefinedTagSchema),
  total: z.number(),
  categories: z.array(TagCategorySchema).optional()
});

export const TagsByReportTypeResponseSchema = z.object({
  reportType: z.string(),
  tags: z.array(PredefinedTagSchema),
  total: z.number()
});

// Query filters
export const TagQueryFiltersSchema = z.object({
  reportType: z.string().optional(),
  categoryId: z.string().uuid().optional(),
  isActive: z.preprocess((val) => {
    if (typeof val === 'boolean') return val;
    if (typeof val === 'string') {
      const lowerVal = val.toLowerCase();
      if (lowerVal === 'true') return true;
      if (lowerVal === 'false') return false;
      throw new Error(`Invalid boolean value: "${val}". Expected 'true', 'false', or boolean.`);
    }
    throw new Error(`Invalid type for boolean field. Expected string or boolean, got ${typeof val}.`);
  }, z.boolean()).optional(),
  language: z.enum(['en', 'fa']).default('en'),
  includeMetadata: z.string().default('false').transform(val => val === 'true').pipe(z.boolean()),
  includeHierarchy: z.string().default('false').transform(val => val === 'true').pipe(z.boolean())
});

// Types
export type TagCategory = z.infer<typeof TagCategorySchema>;
export type CreateTagCategory = z.infer<typeof CreateTagCategorySchema>;
export type BasePredefinedTag = z.infer<typeof BasePredefinedTagSchema>;
export type PredefinedTag = BasePredefinedTag & {
  category?: TagCategory;
  parentTag?: PredefinedTag;
  childTags?: PredefinedTag[];
  metadata?: Array<{ metadataKey: string; metadataValue: string; }>;
};
export type CreatePredefinedTag = z.infer<typeof CreatePredefinedTagSchema>;
export type UpdatePredefinedTag = z.infer<typeof UpdatePredefinedTagSchema>;
export type TagMetadata = z.infer<typeof TagMetadataSchema>;
export type TagQueryFilters = z.infer<typeof TagQueryFiltersSchema>;
export type TagCategoriesResponse = z.infer<typeof TagCategoriesResponseSchema>;
export type PredefinedTagsResponse = z.infer<typeof PredefinedTagsResponseSchema>;
export type TagsByReportTypeResponse = z.infer<typeof TagsByReportTypeResponseSchema>;
