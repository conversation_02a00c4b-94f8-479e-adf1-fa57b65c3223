#!/usr/bin/env pwsh

Write-Host "🔧 Testing AI Service JSON Schema Fix" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

$ErrorActionPreference = "Stop"

try {
    # Change to backend directory
    Set-Location "C:\Code\MUNygo\backend"
    
    Write-Host "`n🏗️  Building backend to check for TypeScript errors..." -ForegroundColor Yellow
    npm run build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Backend build successful!" -ForegroundColor Green
    } else {
        throw "Backend build failed"
    }
    
    Write-Host "`n🎉 AI JSON Schema fix applied successfully!" -ForegroundColor Green
    Write-Host "`nCritical fixes applied:" -ForegroundColor Cyan
    Write-Host "✅ Fixed responseSchema to match nested structure expected by prompt" -ForegroundColor White
    Write-Host "✅ All bug report fields now properly nested under 'report' object" -ForegroundColor White
    Write-Host "✅ Schema enforces required fields: title, description, severity, type, tags, confidence" -ForegroundColor White
    Write-Host "✅ Updated optimization logic to work with nested structure" -ForegroundColor White
    Write-Host "✅ Fallback processing uses gemini-2.0-flash-lite without thinkingConfig" -ForegroundColor White
    
    Write-Host "`nSchema structure now matches:" -ForegroundColor Cyan
    Write-Host '{' -ForegroundColor Gray
    Write-Host '  "analysis_status": "SUCCESS",' -ForegroundColor Gray
    Write-Host '  "transcription": "...",' -ForegroundColor Gray
    Write-Host '  "report": {' -ForegroundColor Gray
    Write-Host '    "title": "...",' -ForegroundColor Gray
    Write-Host '    "description": "...",' -ForegroundColor Gray
    Write-Host '    "tags": [{"tag": "...", "origin": "PREDEFINED|AI_SUGGESTED"}],' -ForegroundColor Gray
    Write-Host '    "severity": "low|medium|high|critical",' -ForegroundColor Gray
    Write-Host '    "type": "bug|feature-request|...",' -ForegroundColor Gray
    Write-Host '    "confidence": 0.0-1.0' -ForegroundColor Gray
    Write-Host '  }' -ForegroundColor Gray
    Write-Host '}' -ForegroundColor Gray
    
    Write-Host "`nExpected behavior:" -ForegroundColor Cyan
    Write-Host "- Gemini model will be forced to return proper nested JSON structure" -ForegroundColor White
    Write-Host "- Tags array will be properly populated with origin information" -ForegroundColor White
    Write-Host "- No more missing report objects or flat response structures" -ForegroundColor White
    Write-Host "- Debug logs will show proper response structure validation" -ForegroundColor White
    
} catch {
    Write-Host "`n❌ Error during testing: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n🚀 Ready for testing - JSON schema now enforces proper structure!" -ForegroundColor Green
