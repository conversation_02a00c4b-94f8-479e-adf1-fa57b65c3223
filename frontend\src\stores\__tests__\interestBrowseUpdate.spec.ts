import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createPinia, setActivePinia } from 'pinia';
import { useInterestStore } from '../interestStore';
import { useOfferStore } from '../offerStore';
import { useAuthStore } from '../auth';
import centralizedSocketManager from '@/services/centralizedSocketManager';
import { INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY } from '@/types/socketEvents';
import type { InterestRequestAcceptedAndChatReadyPayload } from '@/types/socketEvents';

// Mock the centralized socket manager
vi.mock('@/services/centralizedSocketManager', () => ({
  default: {
    on: vi.fn(),
    getSocket: vi.fn(),
    initializeSocket: vi.fn(),
    disconnect: vi.fn(),
  },
}));

// Mock API client
vi.mock('@/services/apiClient', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock the socket instance
const mockSocket = {
  emit: vi.fn(),
  disconnect: vi.fn(),
};

describe('Interest Acceptance Update Flow in Browse View', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
    
    // Reset the socket manager mock
    vi.mocked(centralizedSocketManager.on).mockImplementation((event, handler) => {
      // Store the handler for later use
      if (event === INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY) {
        globalThis.testInterestAcceptedHandler = handler;
      }
      return vi.fn(); // Return unsubscribe function
    });
    
    vi.mocked(centralizedSocketManager.getSocket).mockReturnValue(mockSocket);
  });

  it('should register interest accepted handler when interestStore initializes socket listeners', () => {
    const interestStore = useInterestStore();
    
    // Initialize socket listeners
    interestStore.initializeSocketListeners();
    
    // Verify that the handler was registered
    expect(centralizedSocketManager.on).toHaveBeenCalledWith(
      INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY,
      expect.any(Function)
    );
  });

  it('should update browse offer when user\'s interest is accepted', async () => {
    // Setup authenticated user
    const authStore = useAuthStore();
    authStore.user = {
      id: 'user1',
      email: '<EMAIL>',
      username: 'user1',
      isPhoneVerified: true,
      isEmailVerified: true,
      profilePhotoUrl: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Setup offer store with mock offer
    const offerStore = useOfferStore();
    const interestStore = useInterestStore();
    
    // Mock offer data where user1 showed interest
    const mockOffer = {
      id: 'offer123',
      title: 'Test Offer',
      description: 'Test Description',
      amount: 100,
      rate: 5.0,
      status: 'ACTIVE' as const,
      creator: {
        id: 'user2',
        username: 'user2',
        email: '<EMAIL>',
        reputation: 85,
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      currentUserInterestStatus: 'PENDING' as const,
      chatSessionId: undefined,
    };

    // Set up initial offer state
    offerStore.offers.push(mockOffer);
    
    // Initialize socket listeners
    interestStore.initializeSocketListeners();
    offerStore.initializeSocketListeners();

    // Create the payload that would be sent when user2 accepts user1's interest
    const acceptedPayload: InterestRequestAcceptedAndChatReadyPayload = {
      interestId: 'interest123',
      offerId: 'offer123',
      chatSessionId: 'chat123',
      interestedUser: {
        userId: 'user1', // This is the current user
        username: 'user1',
        email: '<EMAIL>',
      },
      offerCreator: {
        userId: 'user2',
        username: 'user2', 
        email: '<EMAIL>',
      },
      offer: {
        id: 'offer123',
        title: 'Test Offer',
        description: 'Test Description',
        amount: 100,
      },
    };

    // Simulate the socket event being received
    if (globalThis.testInterestAcceptedHandler) {
      await globalThis.testInterestAcceptedHandler(acceptedPayload);
    }

    // Verify that the offer was updated with accepted status and chat session ID
    const updatedOffer = offerStore.offers.find(o => o.id === 'offer123');
    expect(updatedOffer).toBeDefined();
    expect(updatedOffer?.currentUserInterestStatus).toBe('ACCEPTED');
    expect(updatedOffer?.chatSessionId).toBe('chat123');
  });

  it('should NOT update offer when event is for different user', async () => {
    // Setup authenticated user
    const authStore = useAuthStore();
    authStore.user = {
      id: 'user1',
      email: '<EMAIL>',
      username: 'user1',
      isPhoneVerified: true,
      isEmailVerified: true,
      profilePhotoUrl: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const offerStore = useOfferStore();
    const interestStore = useInterestStore();
    
    // Mock offer data
    const mockOffer = {
      id: 'offer123',
      title: 'Test Offer',
      description: 'Test Description',
      amount: 100,
      rate: 5.0,
      status: 'ACTIVE' as const,
      creator: {
        id: 'user2',
        username: 'user2',
        email: '<EMAIL>',
        reputation: 85,
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      currentUserInterestStatus: 'PENDING' as const,
      chatSessionId: undefined,
    };

    offerStore.offers.push(mockOffer);
    
    // Initialize socket listeners
    interestStore.initializeSocketListeners();

    // Create payload for different user's interest being accepted
    const acceptedPayload: InterestRequestAcceptedAndChatReadyPayload = {
      interestId: 'interest123',
      offerId: 'offer123',
      chatSessionId: 'chat123',
      interestedUser: {
        userId: 'user3', // Different user
        username: 'user3',
        email: '<EMAIL>',
      },
      offerCreator: {
        userId: 'user2',
        username: 'user2',
        email: '<EMAIL>',
      },
      offer: {
        id: 'offer123',
        title: 'Test Offer',
        description: 'Test Description',
        amount: 100,
      },
    };

    // Simulate the socket event being received
    if (globalThis.testInterestAcceptedHandler) {
      await globalThis.testInterestAcceptedHandler(acceptedPayload);
    }

    // Verify that the offer was NOT updated since it's for a different user
    const updatedOffer = offerStore.offers.find(o => o.id === 'offer123');
    expect(updatedOffer?.currentUserInterestStatus).toBe('PENDING'); // Should remain unchanged
    expect(updatedOffer?.chatSessionId).toBeUndefined(); // Should remain unchanged
  });

  it('should verify that both stores handle the same event differently', async () => {
    // This test ensures that the myOffersStore ignores events for non-creators
    // while interestStore handles events for interested users
    
    const authStore = useAuthStore();
    authStore.user = {
      id: 'user1', // User1 showed interest in user2's offer
      email: '<EMAIL>',
      username: 'user1',
      isPhoneVerified: true,
      isEmailVerified: true,
      profilePhotoUrl: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const payload: InterestRequestAcceptedAndChatReadyPayload = {
      interestId: 'interest123',
      offerId: 'offer123',
      chatSessionId: 'chat123',
      interestedUser: {
        userId: 'user1', // Current user
        username: 'user1',
        email: '<EMAIL>',
      },
      offerCreator: {
        userId: 'user2', // Different user (offer creator)
        username: 'user2',
        email: '<EMAIL>',
      },
      offer: {
        id: 'offer123',
        title: 'Test Offer',
        description: 'Test Description',
        amount: 100,
      },
    };

    // For interestStore: should handle this event (user1 is the interested user)
    // For myOffersStore: should ignore this event (user1 is not the offer creator)
    
    expect(payload.interestedUser.userId).toBe('user1'); // Current user
    expect(payload.offerCreator.userId).toBe('user2'); // Different user
    expect(payload.interestedUser.userId).not.toBe(payload.offerCreator.userId);
  });
});

// Add to global types for the test handler
declare global {
  var testInterestAcceptedHandler: ((payload: InterestRequestAcceptedAndChatReadyPayload) => void) | undefined;
}
