import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import axios from 'axios'
import type { AxiosResponse } from 'axios'

// Integration test for payment info persistence
// This test simulates the complete flow of saving and loading payment information

interface PaymentInfo {
  bankName: string
  accountNumber: string
  accountHolderName: string
}

interface UserInfo {
  id: string
  email: string
  username: string
  isPhoneVerified: boolean
  reputation: number
  defaultPaymentReceivingInfo: PaymentInfo | null
}

interface AuthResponse {
  success: boolean
  user: UserInfo
  token?: string
}

interface PaymentUpdateResponse {
  success: boolean
  message: string
}

// Mock axios for testing
vi.mock('axios')
const mockedAxios = vi.mocked(axios)

describe('Payment Info Persistence Integration Tests', () => {
  const mockBaseUrl = 'http://localhost:3000/api'
  const mockToken = 'test-jwt-token'
  const mockUserId = 'test-user-123'
  const mockNegotiationId = 'test-negotiation-456'

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default axios mock
    mockedAxios.create = vi.fn(() => mockedAxios)
    mockedAxios.defaults = { baseURL: mockBaseUrl, headers: { common: {} } }
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Complete Payment Persistence Flow', () => {
    it('should save payment info to profile and retrieve it on subsequent requests', async () => {
      // Step 1: User initially has no payment info
      const initialUserResponse: AxiosResponse<AuthResponse> = {
        data: {
          success: true,
          user: {
            id: mockUserId,
            email: '<EMAIL>',
            username: 'testuser',
            isPhoneVerified: true,
            reputation: 5.0,
            defaultPaymentReceivingInfo: null
          }
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any
      }

      mockedAxios.get = vi.fn().mockResolvedValueOnce(initialUserResponse)

      // Simulate fetching user profile initially
      const initialResponse = await mockedAxios.get('/auth/me')
      expect(initialResponse.data.user.defaultPaymentReceivingInfo).toBeNull()

      // Step 2: User provides payment info with saveToProfile: true
      const paymentUpdateData = {
        bankName: 'Test Bank',
        accountNumber: '*********',
        accountHolderName: 'John Doe',
        saveToProfile: true
      }

      const paymentUpdateResponse: AxiosResponse<PaymentUpdateResponse> = {
        data: {
          success: true,
          message: 'Payment information updated successfully'
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any
      }

      mockedAxios.post = vi.fn().mockResolvedValueOnce(paymentUpdateResponse)

      // Simulate updating payment info
      const updateResponse = await mockedAxios.post(
        `/payer-negotiations/${mockNegotiationId}/payment-info`,
        paymentUpdateData
      )
      expect(updateResponse.data.success).toBe(true)

      // Step 3: User profile should now include the saved payment info
      const updatedUserResponse: AxiosResponse<AuthResponse> = {
        data: {
          success: true,
          user: {
            id: mockUserId,
            email: '<EMAIL>',
            username: 'testuser',
            isPhoneVerified: true,
            reputation: 5.0,
            defaultPaymentReceivingInfo: {
              bankName: 'Test Bank',
              accountNumber: '*********',
              accountHolderName: 'John Doe'
            }
          }
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any
      }

      mockedAxios.get = vi.fn().mockResolvedValueOnce(updatedUserResponse)

      // Simulate fetching user profile after payment update
      const updatedResponse = await mockedAxios.get('/auth/me')
      const savedPaymentInfo = updatedResponse.data.user.defaultPaymentReceivingInfo

      expect(savedPaymentInfo).toBeDefined()
      expect(savedPaymentInfo?.bankName).toBe('Test Bank')
      expect(savedPaymentInfo?.accountNumber).toBe('*********')
      expect(savedPaymentInfo?.accountHolderName).toBe('John Doe')

      // Verify the correct API calls were made
      expect(mockedAxios.get).toHaveBeenCalledWith('/auth/me')
      expect(mockedAxios.post).toHaveBeenCalledWith(
        `/payer-negotiations/${mockNegotiationId}/payment-info`,
        paymentUpdateData
      )
    })

    it('should not save payment info to profile when saveToProfile is false', async () => {
      // Step 1: User has existing payment info
      const userWithPaymentResponse: AxiosResponse<AuthResponse> = {
        data: {
          success: true,
          user: {
            id: mockUserId,
            email: '<EMAIL>',
            username: 'testuser',
            isPhoneVerified: true,
            reputation: 5.0,
            defaultPaymentReceivingInfo: {
              bankName: 'Original Bank',
              accountNumber: '*********',
              accountHolderName: 'Original User'
            }
          }
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any
      }

      mockedAxios.get = vi.fn().mockResolvedValueOnce(userWithPaymentResponse)

      const initialResponse = await mockedAxios.get('/auth/me')
      expect(initialResponse.data.user.defaultPaymentReceivingInfo?.bankName).toBe('Original Bank')

      // Step 2: User provides different payment info with saveToProfile: false
      const paymentUpdateData = {
        bankName: 'Temporary Bank',
        accountNumber: '*********',
        accountHolderName: 'Temporary User',
        saveToProfile: false
      }

      const paymentUpdateResponse: AxiosResponse<PaymentUpdateResponse> = {
        data: {
          success: true,
          message: 'Payment information updated successfully'
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any
      }

      mockedAxios.post = vi.fn().mockResolvedValueOnce(paymentUpdateResponse)

      const updateResponse = await mockedAxios.post(
        `/payer-negotiations/${mockNegotiationId}/payment-info`,
        paymentUpdateData
      )
      expect(updateResponse.data.success).toBe(true)

      // Step 3: User profile should still have original payment info
      mockedAxios.get = vi.fn().mockResolvedValueOnce(userWithPaymentResponse)

      const subsequentResponse = await mockedAxios.get('/auth/me')
      const paymentInfo = subsequentResponse.data.user.defaultPaymentReceivingInfo

      expect(paymentInfo?.bankName).toBe('Original Bank') // Should remain unchanged
      expect(paymentInfo?.accountNumber).toBe('*********')
      expect(paymentInfo?.accountHolderName).toBe('Original User')
    })

    it('should handle payment info updates correctly', async () => {
      // Step 1: User has existing payment info
      const originalPaymentInfo = {
        bankName: 'Old Bank',
        accountNumber: '*********',
        accountHolderName: 'Old Name'
      }

      const userResponse: AxiosResponse<AuthResponse> = {
        data: {
          success: true,
          user: {
            id: mockUserId,
            email: '<EMAIL>',
            username: 'testuser',
            isPhoneVerified: true,
            reputation: 5.0,
            defaultPaymentReceivingInfo: originalPaymentInfo
          }
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any
      }

      mockedAxios.get = vi.fn().mockResolvedValueOnce(userResponse)

      const initialResponse = await mockedAxios.get('/auth/me')
      expect(initialResponse.data.user.defaultPaymentReceivingInfo?.bankName).toBe('Old Bank')

      // Step 2: User updates payment info with saveToProfile: true
      const updatedPaymentData = {
        bankName: 'New Bank',
        accountNumber: '*********',
        accountHolderName: 'New Name',
        saveToProfile: true
      }

      const paymentUpdateResponse: AxiosResponse<PaymentUpdateResponse> = {
        data: {
          success: true,
          message: 'Payment information updated successfully'
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any
      }

      mockedAxios.post = vi.fn().mockResolvedValueOnce(paymentUpdateResponse)

      const updateResponse = await mockedAxios.post(
        `/payer-negotiations/${mockNegotiationId}/payment-info`,
        updatedPaymentData
      )
      expect(updateResponse.data.success).toBe(true)

      // Step 3: User profile should reflect the updated payment info
      const updatedUserResponse: AxiosResponse<AuthResponse> = {
        data: {
          success: true,
          user: {
            id: mockUserId,
            email: '<EMAIL>',
            username: 'testuser',
            isPhoneVerified: true,
            reputation: 5.0,
            defaultPaymentReceivingInfo: {
              bankName: 'New Bank',
              accountNumber: '*********',
              accountHolderName: 'New Name'
            }
          }
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any
      }

      mockedAxios.get = vi.fn().mockResolvedValueOnce(updatedUserResponse)

      const finalResponse = await mockedAxios.get('/auth/me')
      const finalPaymentInfo = finalResponse.data.user.defaultPaymentReceivingInfo

      expect(finalPaymentInfo?.bankName).toBe('New Bank')
      expect(finalPaymentInfo?.accountNumber).toBe('*********')
      expect(finalPaymentInfo?.accountHolderName).toBe('New Name')
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors when saving payment info', async () => {
      const paymentUpdateData = {
        bankName: 'Test Bank',
        accountNumber: '*********',
        accountHolderName: 'John Doe',
        saveToProfile: true
      }

      const errorResponse = {
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid payment information'
          }
        }
      }

      mockedAxios.post = vi.fn().mockRejectedValueOnce(errorResponse)

      await expect(
        mockedAxios.post(`/payer-negotiations/${mockNegotiationId}/payment-info`, paymentUpdateData)
      ).rejects.toEqual(errorResponse)
    })

    it('should handle API errors when fetching user profile', async () => {
      const errorResponse = {
        response: {
          status: 401,
          data: {
            success: false,
            message: 'Invalid token'
          }
        }
      }

      mockedAxios.get = vi.fn().mockRejectedValueOnce(errorResponse)

      await expect(mockedAxios.get('/auth/me')).rejects.toEqual(errorResponse)
    })
  })
})
