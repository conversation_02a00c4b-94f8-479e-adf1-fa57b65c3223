import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { useScrollToElement } from '@/composables/useScrollToElement';

// Mock DOM methods
const mockScrollIntoView = vi.fn();
const mockQuerySelector = vi.fn();
const mockClassListAdd = vi.fn();
const mockClassListRemove = vi.fn();

// Mock element with classList
const createMockElement = () => ({
  scrollIntoView: mockScrollIntoView,
  classList: {
    add: mockClassListAdd,
    remove: mockClassListRemove
  }
});

describe('useScrollToElement', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock document.querySelector
    Object.defineProperty(global, 'document', {
      value: {
        querySelector: mockQuerySelector
      },
      writable: true
    });
    
    // Mock setTimeout
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
  });

  describe('scrollToElementBySelector', () => {
    it('should scroll to element and add highlight class when element exists', async () => {
      // Arrange
      const mockElement = createMockElement();
      mockQuerySelector.mockReturnValue(mockElement);
      
      const { scrollToElementBySelector } = useScrollToElement();

      // Act
      const result = await scrollToElementBySelector('.test-selector');

      // Assert
      expect(mockQuerySelector).toHaveBeenCalledWith('.test-selector');
      expect(mockScrollIntoView).toHaveBeenCalledWith({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      });
      expect(mockClassListAdd).toHaveBeenCalledWith('highlight-card');
      expect(result).toBe(true);

      // Check that highlight class is removed after duration
      vi.advanceTimersByTime(2000);
      expect(mockClassListRemove).toHaveBeenCalledWith('highlight-card');
    });

    it('should return false when element does not exist', async () => {
      // Arrange
      mockQuerySelector.mockReturnValue(null);
      
      const { scrollToElementBySelector } = useScrollToElement();

      // Act
      const resultPromise = scrollToElementBySelector('.non-existent');
      
      // Advance time to trigger retry
      vi.advanceTimersByTime(100);
      
      const result = await resultPromise;

      // Assert
      expect(mockQuerySelector).toHaveBeenCalledWith('.non-existent');
      expect(mockScrollIntoView).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });

    it('should use custom options when provided', async () => {
      // Arrange
      const mockElement = createMockElement();
      mockQuerySelector.mockReturnValue(mockElement);
      
      const { scrollToElementBySelector } = useScrollToElement();
      const customOptions = {
        behavior: 'auto' as ScrollBehavior,
        block: 'start' as ScrollLogicalPosition,
        highlightClass: 'custom-highlight',
        highlightDuration: 1000
      };

      // Act
      const result = await scrollToElementBySelector('.test-selector', customOptions);

      // Assert
      expect(mockScrollIntoView).toHaveBeenCalledWith({
        behavior: 'auto',
        block: 'start',
        inline: 'nearest'
      });
      expect(mockClassListAdd).toHaveBeenCalledWith('custom-highlight');
      expect(result).toBe(true);

      // Check custom duration
      vi.advanceTimersByTime(1000);
      expect(mockClassListRemove).toHaveBeenCalledWith('custom-highlight');
    });
  });

  describe('scrollToElementByTestId', () => {
    it('should convert testId to proper selector', async () => {
      // Arrange
      const mockElement = createMockElement();
      mockQuerySelector.mockReturnValue(mockElement);
      
      const { scrollToElementByTestId } = useScrollToElement();

      // Act
      const result = await scrollToElementByTestId('action-bar');

      // Assert
      expect(mockQuerySelector).toHaveBeenCalledWith('[data-testid="action-bar"]');
      expect(result).toBe(true);
    });
  });

  describe('scrollToElementByCardId', () => {
    it('should convert cardId to proper selector', async () => {
      // Arrange
      const mockElement = createMockElement();
      mockQuerySelector.mockReturnValue(mockElement);
      
      const { scrollToElementByCardId } = useScrollToElement();

      // Act
      const result = await scrollToElementByCardId('card-123');

      // Assert
      expect(mockQuerySelector).toHaveBeenCalledWith('[data-card-id="card-123"]');
      expect(result).toBe(true);
    });
  });

  describe('addHighlightEffect', () => {
    it('should add and remove highlight class after duration', () => {
      // Arrange
      const mockElement = createMockElement();
      const { addHighlightEffect } = useScrollToElement();

      // Act
      addHighlightEffect(mockElement as unknown as Element, 'test-highlight', 500);

      // Assert
      expect(mockClassListAdd).toHaveBeenCalledWith('test-highlight');
      
      vi.advanceTimersByTime(500);
      expect(mockClassListRemove).toHaveBeenCalledWith('test-highlight');
    });
  });

  describe('retry mechanism', () => {
    it('should retry once when element is not found initially', async () => {
      // Arrange
      const mockElement = createMockElement();
      mockQuerySelector
        .mockReturnValueOnce(null) // First call returns null
        .mockReturnValueOnce(mockElement); // Second call returns element
      
      const { scrollToElementBySelector } = useScrollToElement();

      // Act
      const resultPromise = scrollToElementBySelector('.test-selector');
      
      // Advance time to trigger retry
      vi.advanceTimersByTime(100);
      
      const result = await resultPromise;

      // Assert
      expect(mockQuerySelector).toHaveBeenCalledTimes(2);
      expect(result).toBe(true);
    });

    it('should return false after retry fails', async () => {
      // Arrange
      mockQuerySelector.mockReturnValue(null); // Always returns null
      
      const { scrollToElementBySelector } = useScrollToElement();

      // Act
      const resultPromise = scrollToElementBySelector('.test-selector');
      
      // Advance time to trigger retry
      vi.advanceTimersByTime(100);
      
      const result = await resultPromise;

      // Assert
      expect(mockQuerySelector).toHaveBeenCalledTimes(2);
      expect(result).toBe(false);
    });
  });
});
