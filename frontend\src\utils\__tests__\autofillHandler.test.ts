// filepath: c:\Code\MUNygo\frontend\src\utils\__tests__\autofillHandler.test.ts

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AutofillHandler } from '../autofillHandler';

// Mock the client logger
vi.mock('@/composables/useClientLogger', () => ({
  useClientLogger: () => ({
    logInfo: vi.fn(),
    logError: vi.fn(),
    logWarn: vi.fn()
  })
}));

describe('AutofillHandler', () => {
  let autofillHandler: AutofillHandler;
  let originalInsertBefore: typeof Node.prototype.insertBefore;
  let originalRemoveChild: typeof Node.prototype.removeChild;

  beforeEach(() => {
    // Store original DOM methods
    originalInsertBefore = Node.prototype.insertBefore;
    originalRemoveChild = Node.prototype.removeChild;
    
    // Create a new instance for each test
    autofillHandler = new AutofillHandler({
      enableMutationObserver: false, // Disable for testing
      enableFormStabilization: false, // Disable for testing
      debugMode: true
    });

    // Initialize and store original methods for safe method tests
    autofillHandler.initialize();
    autofillHandler['originalInsertBefore'] = originalInsertBefore;
    autofillHandler['originalRemoveChild'] = originalRemoveChild;
  });

  afterEach(() => {
    // Cleanup after each test
    autofillHandler.cleanup();
    
    // Restore original DOM methods
    Node.prototype.insertBefore = originalInsertBefore;
    Node.prototype.removeChild = originalRemoveChild;
  });

  describe('initialization', () => {
    it('should initialize without errors', () => {
      expect(() => autofillHandler.initialize()).not.toThrow();
    });

    it('should not initialize twice', () => {
      autofillHandler.initialize();
      expect(() => autofillHandler.initialize()).not.toThrow();
    });
  });

  describe('DOM method interception', () => {
    beforeEach(() => {
      autofillHandler.initialize();
    });

    it('should intercept insertBefore and handle autofill errors gracefully', () => {
      // Force patching support for this test
      autofillHandler['shouldApplyPatching'] = true;
      
      const mockParent = document.createElement('div');
      const mockChild = document.createElement('span');
      const mockReference = document.createElement('p');

      // Add the reference node to the parent first
      mockParent.appendChild(mockReference);

      // Mock the original method to throw an autofill error
      autofillHandler['originalInsertBefore'] = vi.fn().mockImplementation(() => {
        const error = new Error('Failed to execute insertBefore on Node: bootstrap-autofill-overlay');
        error.name = 'NotFoundError';
        throw error;
      });

      // Test safe method - should not throw an error due to interception
      expect(() => {
        autofillHandler.safeInsertBefore(mockParent, mockChild, mockReference);
      }).not.toThrow();
    });

    it('should intercept removeChild and handle autofill errors gracefully', () => {
      // Force patching support for this test
      autofillHandler['shouldApplyPatching'] = true;
      
      const mockParent = document.createElement('div');
      const mockChild = document.createElement('span');

      // Add the child to the parent first
      mockParent.appendChild(mockChild);

      // Mock the original method to throw an autofill error
      autofillHandler['originalRemoveChild'] = vi.fn().mockImplementation(() => {
        const error = new Error('Failed to execute removeChild on Node: AutofillInlineMenuContentService');
        error.name = 'NotFoundError';
        throw error;
      });

      // Test safe method - should not throw an error due to interception
      expect(() => {
        autofillHandler.safeRemoveChild(mockParent, mockChild);
      }).not.toThrow();
    });

    it('should re-throw non-autofill errors through safe method', () => {
      // Force patching support for this test
      autofillHandler['shouldApplyPatching'] = true;
      
      const mockParent = document.createElement('div');
      const mockChild = document.createElement('span');

      // Add the child to the parent first
      mockParent.appendChild(mockChild);

      // Mock the original method to throw a non-autofill error
      autofillHandler['originalRemoveChild'] = vi.fn().mockImplementation(() => {
        throw new Error('Some other DOM error');
      });

      // Test the safe method which should throw non-autofill errors
      expect(() => {
        autofillHandler.safeRemoveChild(mockParent, mockChild);
      }).toThrow('Some other DOM error');
    });
  });

  describe('error detection', () => {
    it('should correctly identify autofill errors', () => {
      const autofillErrors = [
        new Error('bootstrap-autofill-overlay.js:16'),
        new Error('AutofillInlineMenuContentService error'),
        new Error('Failed to execute insertBefore on Node: not a child'),
        new Error('Failed to execute removeChild on Node: not a child'),
        new Error('autofill overlay manipulation failed')
      ];

      autofillErrors.forEach(error => {
        expect(AutofillHandler['isAutofillError'](error)).toBe(true);
      });
    });

    it('should not identify non-autofill errors as autofill errors', () => {
      const nonAutofillErrors = [
        new Error('Regular DOM error'),
        new Error('Network request failed'),
        new Error('Validation error'),
        null,
        undefined,
        'string error'
      ];

      nonAutofillErrors.forEach(error => {
        expect(AutofillHandler['isAutofillError'](error)).toBe(false);
      });
    });
  });

  describe('cleanup', () => {
    it('should cleanup properly', () => {
      autofillHandler.initialize();
      expect(() => autofillHandler.cleanup()).not.toThrow();
    });

    it('should cleanup properly and not affect global DOM methods', () => {
      autofillHandler.initialize();
      
      // DOM methods should NOT be intercepted globally anymore
      expect(Node.prototype.insertBefore).toBe(originalInsertBefore);
      expect(Node.prototype.removeChild).toBe(originalRemoveChild);
      
      autofillHandler.cleanup();
      
      // Methods should still be the original ones
      expect(Node.prototype.insertBefore).toBe(originalInsertBefore);
      expect(Node.prototype.removeChild).toBe(originalRemoveChild);
      
      // Verify handler state is properly reset
      expect(autofillHandler['isInitialized']).toBe(false);
    });
  });

  describe('configuration', () => {
    it('should respect configuration options', () => {
      const handler = new AutofillHandler({
        enableErrorSuppression: false,
        enableMutationObserver: false,
        enableFormStabilization: false,
        debugMode: false
      });

      handler.initialize();
      
      // Methods should not be intercepted when error suppression is disabled
      expect(Node.prototype.insertBefore).toBe(originalInsertBefore);
      expect(Node.prototype.removeChild).toBe(originalRemoveChild);
      
      handler.cleanup();
    });
  });

  describe('form stabilization', () => {
    it('should add stabilization styles when enabled', () => {
      // Mock document.head for testing environment
      const mockHead = document.createElement('head');
      Object.defineProperty(document, 'head', {
        value: mockHead,
        writable: true
      });

      const handler = new AutofillHandler({
        enableFormStabilization: true,
        enableErrorSuppression: false,
        enableMutationObserver: false
      });

      handler.initialize();

      // Check if styles were added
      const styles = mockHead.querySelectorAll('style');
      const autofillStyles = Array.from(styles).find(style =>
        style.textContent?.includes('autofill-stable')
      );

      expect(autofillStyles).toBeTruthy();

      handler.cleanup();
    });
  });

  describe('element protection', () => {
    beforeEach(() => {
      autofillHandler.initialize();
    });

    afterEach(() => {
      autofillHandler.cleanup();
    });

    it('should protect and unprotect elements', () => {
      // Force patching support for this test
      autofillHandler['shouldApplyPatching'] = true;
      
      const element = document.createElement('form');
      const input = document.createElement('input');
      element.appendChild(input);

      // Initially not protected
      expect(autofillHandler.isElementProtected(element)).toBe(false);
      expect(autofillHandler.isElementProtected(input)).toBe(false);

      // Protect the element
      autofillHandler.protectElement(element);
      expect(autofillHandler.isElementProtected(element)).toBe(true);
      expect(autofillHandler.isElementProtected(input)).toBe(true);

      // Unprotect the element
      autofillHandler.unprotectElement(element);
      expect(autofillHandler.isElementProtected(element)).toBe(false);
      expect(autofillHandler.isElementProtected(input)).toBe(false);
    });

    it('should provide safe DOM operations', () => {
      const parent = document.createElement('div');
      const newNode = document.createElement('span');
      const child = document.createElement('p');
      
      parent.appendChild(child);

      // Safe operations should work normally
      expect(() => {
        const result = autofillHandler.safeInsertBefore(parent, newNode, child);
        expect(result).toBe(newNode);
      }).not.toThrow();

      expect(() => {
        const result = autofillHandler.safeRemoveChild(parent, newNode);
        expect(result).toBe(newNode);
      }).not.toThrow();
    });
  });

  describe('browser detection', () => {
    it('should expose patching support status', () => {
      expect(typeof autofillHandler.isPatchingSupported()).toBe('boolean');
    });

    it('should expose configuration', () => {
      const config = autofillHandler.getConfig();
      expect(config).toHaveProperty('enableErrorSuppression');
      expect(config).toHaveProperty('enableMutationObserver');
      expect(config).toHaveProperty('enableFormStabilization');
    });
  });
});
