import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export type Theme = 'light' | 'dark'

export const useThemeStore = defineStore('theme', () => {
  // State
  const currentTheme = ref<Theme>('light')

  // Load theme from localStorage on initialization
  const loadTheme = () => {
    try {
      const savedTheme = localStorage.getItem('munygo-theme') as Theme
      if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
        currentTheme.value = savedTheme
      } else {
        // Check system preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
          currentTheme.value = 'dark'
        }
      }
    } catch (error) {
      // Check system preference
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        currentTheme.value = 'dark'
      }
    }
    applyTheme()
  }
  // Apply theme to HTML element
  const applyTheme = () => {
    const html = document.documentElement
    if (currentTheme.value === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  // Toggle theme
  const toggleTheme = () => {
    currentTheme.value = currentTheme.value === 'light' ? 'dark' : 'light'
  }

  // Set specific theme
  const setTheme = (theme: Theme) => {
    currentTheme.value = theme
  }

  // Watch for theme changes and persist
  watch(currentTheme, (newTheme) => {
    try {
      localStorage.setItem('munygo-theme', newTheme)
    } catch (error) {
      console.warn('Failed to save theme preference to localStorage:', error)
    }
    applyTheme()
  })

  // Initialize theme on store creation
  loadTheme()

  return {
    // State
    currentTheme,
    
    // Actions
    toggleTheme,
    setTheme,
    loadTheme
  }
})
