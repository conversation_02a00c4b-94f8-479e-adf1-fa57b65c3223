import { PrismaClient } from '@prisma/client';
import * as jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

async function testAuthMeEndpoint() {
  try {
    const email = '<EMAIL>';
    
    // First, get the user from database
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        phoneNumber: true,
        phoneVerified: true,
        createdAt: true,
        username: true,
        reputationScore: true,
        reputationLevel: true,
      },
    });

    if (!user) {
      console.log(`User ${email} not found`);
      return;
    }

    console.log('\n=== USER DATA FROM DATABASE ===');
    console.log(JSON.stringify(user, null, 2));

    // Generate a JWT token like the login would do
    const JWT_SECRET = process.env.JWT_SECRET || 'your-default-secret-key';
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log('\n=== GENERATED JWT TOKEN ===');
    console.log('Token generated for API testing');

    // Now simulate what the /auth/me endpoint would return
    const createdAt = user.createdAt || new Date('2025-05-01');

    // Calculate reputation score (from auth.ts logic)
    let calculatedScore = 0;
    if (user.emailVerified) {
      calculatedScore += 10;
    }
    if (user.phoneVerified) {
      calculatedScore += 15;
    }
    const accountAgeInDays = Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24));
    calculatedScore += Math.min(accountAgeInDays, 30);

    // Calculate reputation level (from auth.ts logic)
    let calculatedLevel = 1;
    if (calculatedScore < 10) calculatedLevel = 1; // Newcomer
    else if (calculatedScore < 25) calculatedLevel = 2; // Verified
    else if (calculatedScore < 40) calculatedLevel = 3; // Reliable
    else if (calculatedScore < 60) calculatedLevel = 4; // Trusted
    else calculatedLevel = 5; // Elite

    const derivedUsername = user.username || user.email.split('@')[0];

    // Simulate the /auth/me response
    const { reputationScore: dbScore, reputationLevel: dbLevel, ...userWithoutReputation } = user;
    
    const authMeResponse = {
      ...userWithoutReputation,
      reputationScore: calculatedScore,
      reputationLevel: calculatedLevel,
      createdAt,
      username: derivedUsername,
      defaultPaymentReceivingInfo: null,
    };

    console.log('\n=== SIMULATED /auth/me RESPONSE ===');
    console.log(JSON.stringify(authMeResponse, null, 2));

    console.log('\n=== MAKE A REAL API CALL ===');
    console.log('To test the actual endpoint, run this curl command:');
    console.log(`curl -H "Authorization: Bearer ${token}" http://localhost:3001/api/auth/me`);

  } catch (error) {
    console.error('Error testing auth/me endpoint:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuthMeEndpoint();
