const jwt = require('jsonwebtoken');

// Use the current valid token
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************.O6aTnkiMAmK7CPBpC8j1l7NqaoSVJMOS5PgPSjEEKcA';

async function testApiCall() {
  try {
    console.log('\n=== TESTING ACTUAL /auth/me API ENDPOINT ===');
    
    const response = await fetch('http://localhost:3001/api/auth/me', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      console.log(`❌ API call failed with status: ${response.status}`);
      const errorText = await response.text();
      console.log('Error response:', errorText);
      return;
    }

    const data = await response.json();
    console.log('\n✅ API Response:');
    console.log(JSON.stringify(data, null, 2));

    console.log('\n🔍 Key Values:');
    console.log(`- reputationScore: ${data.reputationScore}`);
    console.log(`- reputationLevel: ${data.reputationLevel}`);
    console.log(`- emailVerified: ${data.emailVerified}`);
    console.log(`- phoneVerified: ${data.phoneVerified}`);

  } catch (error) {
    console.error('❌ Error making API call:', error);
  }
}

testApiCall();
