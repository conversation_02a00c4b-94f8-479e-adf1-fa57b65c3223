{"proposal": {"from": "Proposal from {username}: \"{message}\" (Regarding who pays first).", "agreed": "{username} agreed to the proposal.", "bothAgreed": "Both parties have agreed. {firstPayer} will make the first payment. Payment due by {dueDate}.", "bothAgreedSystemDesignated": "Both parties have agreed to the transaction terms. System has designated {firstPayer} as the first payer. Payment due by {dueDate}."}, "payment": {"declared": "{username} has declared their payment. Waiting for {otherUser} to confirm receipt.", "confirmedFirst": "{username} has confirmed receipt of the first payment. Now waiting for {payerUser} to make their payment to {receiverUser}. Payment due by {dueDate}.", "confirmedSecond": "{username} has confirmed receipt of the second payment. Transaction is now complete!"}, "transaction": {"complete": "Transaction is now complete! 🎉", "cancelled": "Transaction has been cancelled by {username}.", "disputed": "Transaction has been disputed by {username}."}, "raw": {"h agreed to the proposal.": "h agreed to the proposal.", "Proposal from h2: \"no you pay first\" (Regarding who pays first).": "Proposal from h2: \"no you pay first\" (Regarding who pays first).", "Both parties have agreed. h will make the first payment. Payment due by 28/06/2025, 20:59:14.": "Both parties have agreed. h will make the first payment. Payment due by 28/06/2025, 20:59:14.", "h has declared their payment. Waiting for h2 to confirm receipt.": "h has declared their payment. Waiting for h2 to confirm receipt.", "h2 has confirmed receipt of the first payment. Now waiting for h2 to make their payment to h. Payment due by 28/06/2025, 21:00:45.": "h2 has confirmed receipt of the first payment. Now waiting for h2 to make their payment to h. Payment due by 28/06/2025, 21:00:45.", "h2 has declared their payment. Waiting for h to confirm receipt.": "h2 has declared their payment. Waiting for h to confirm receipt.", "h has confirmed receipt of the second payment. Transaction is now complete!": "h has confirmed receipt of the second payment. Transaction is now complete!"}}