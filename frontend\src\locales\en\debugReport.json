{"sendReport": "Send Debug Report", "modalTitle": "Debug Report", "modalDescription": "This will send application logs and diagnostic information to help developers identify and fix issues. No personal data will be included.", "reportType": "Report Type", "selectType": "Select report type...", "selectPredefinedTags": "Select from predefined tags", "selectTagsManually": "Select Tags Manually", "aiSuggestionsNotAvailable": "AI tag suggestions are not available", "aiSuggestionsError": "Failed to get AI tag suggestions", "totalLogs": "Total Logs", "currentPage": "Current Page", "sessionId": "Session ID", "browser": "Browser", "logLevels": "Log Levels", "additionalNotes": "Additional Notes (Optional)", "notesPlaceholder": "Describe what you were doing when the issue occurred, or provide any additional context that might help...", "sendSuccess": "Debug report sent successfully! Thank you for helping us improve the application.", "sendError": "Failed to send debug report. Please try again or contact support.", "noLogsWarning": "No logs available to send.", "tagSelector": {"predefinedTags": "Available Tags", "loadingTags": "Loading tags...", "noTagsAvailable": "No tags available", "customTags": "Custom Tags", "addCustomTag": "Add your own tag...", "selectedCount": "{count} tags selected", "aiSuggestionsHint": "{count} AI-suggested tags are preselected"}}