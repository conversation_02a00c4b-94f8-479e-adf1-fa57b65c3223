import dotenvFlow from 'dotenv-flow';

/**
 * Configures the test environment to use an isolated test database
 * and validates that we're not accidentally using production/development databases
 */
export function setupTestEnvironment(): void {
  // Load test environment configuration
  dotenvFlow.config({
    node_env: 'test',
    default_node_env: 'test',
    path: process.cwd(),
    pattern: '.env[.node_env][.local]',
    purge_dotenv: true
  });

  validateTestDatabase();
}

/**
 * Validates that the current DATABASE_URL is safe for testing
 * Throws an error if the database URL doesn't meet test safety requirements
 */
export function validateTestDatabase(): void {
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    throw new Error('DATABASE_URL is not defined');
  }

  // Guard against using production or development databases
  const isTestDatabase = 
    databaseUrl.includes('test') || 
    databaseUrl.includes(':memory:') ||
    databaseUrl.includes('_test') ||
    databaseUrl.includes('.test.');

  if (!isTestDatabase) {
    throw new Error(
      `Unsafe database URL detected: ${databaseUrl}. ` +
      'Test database URL must contain "test", "_test", ".test.", or be an in-memory database (:memory:) ' +
      'to prevent accidental data loss during tests.'
    );
  }

  console.log('✅ Test database configuration validated:', databaseUrl);
}

/**
 * Applies database migrations for the test environment
 */
export async function setupTestDatabase(): Promise<void> {
  const { execSync } = require('child_process');
  const databaseUrl = process.env.DATABASE_URL;

  console.log('🔄 Setting up test database schema...');
  
  try {
    // Try to apply migrations first
    execSync('npx prisma migrate deploy', { 
      stdio: 'inherit',
      cwd: process.cwd(),
      env: { ...process.env, DATABASE_URL: databaseUrl }
    });
    console.log('✅ Database migrations applied successfully');
  } catch (migrateError) {
    console.warn('⚠️ Migration failed, attempting to push schema directly...');
    try {
      execSync('npx prisma db push --force-reset', { 
        stdio: 'inherit',
        cwd: process.cwd(),
        env: { ...process.env, DATABASE_URL: databaseUrl }
      });
      console.log('✅ Database schema pushed successfully');
    } catch (pushError) {
      console.error('❌ Database setup failed:', pushError);
      throw new Error(`Database setup failed: ${pushError}`);
    }
  }
}

/**
 * Cleans up the test database file after tests complete
 */
export function cleanupTestDatabase(): void {
  if (process.env.DATABASE_URL?.includes('file:')) {
    try {
      const fs = require('fs');
      const path = require('path');
      const dbPath = process.env.DATABASE_URL.replace('file:', '');
      const fullPath = path.resolve(dbPath);
      
      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
        console.log('🗑️ Test database file removed:', fullPath);
      }
    } catch (error) {
      console.log('⚠️ Could not remove test database file:', error);
    }
  }
}
