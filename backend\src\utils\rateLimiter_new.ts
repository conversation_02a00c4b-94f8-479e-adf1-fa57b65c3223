/**
 * Phone-based OTP Rate Limiter - Prevents abuse and brute force attacks
 * 
 * This utility provides rate limiting for OTP operations to secure phone verification,
 * with features including:
 * - Limits on send/verify attempts within time windows
 * - Cooldown periods between consecutive sends
 * - Blocking periods for exceeded limits
 * - Automatic cleanup of stale entries
 */

/**
 * Represents a rate limit entry for a specific phone number
 */
interface RateLimitEntry {
  /** Number of OTP send attempts within the current window */
  sendAttempts: number;
  /** Number of verify attempts for the current OTP */
  verifyAttempts: number;
  /** Timestamp of the last OTP send attempt */
  lastSendTime: number;
  /** Timestamp until which the number is blocked (if applicable) */
  blockUntil: number;
  /** Current OTP value (if tracking is enabled) */
  currentOtp?: string;
  /** Last time this entry was accessed (for cleanup) */
  lastAccessed: number;
}

/**
 * Rate limiter for OTP operations to prevent abuse and protect against brute-force attacks.
 * Uses the Singleton pattern to ensure a single, application-wide rate limiter instance.
 */
class OtpRateLimiter {
  private static instance: OtpRateLimiter;
  private limits: Map<string, RateLimitEntry>;
  
  // Rate limit settings (configurable via environment variables in a production setup)
  private readonly MAX_SEND_ATTEMPTS = 3; // Max OTP send requests per hour
  private readonly MAX_VERIFY_ATTEMPTS = 5; // Max verify attempts per OTP
  private readonly BLOCK_DURATION = 60 * 60 * 1000; // 1 hour block in milliseconds
  private readonly WINDOW_DURATION = 60 * 60 * 1000; // 1 hour rolling window in milliseconds
  private readonly COOLDOWN_BETWEEN_SENDS = 60 * 1000; // 1 minute cooldown in milliseconds
  private readonly CLEANUP_INTERVAL = 6 * 60 * 60 * 1000; // Run cleanup every 6 hours
  private readonly ENTRY_TTL = 24 * 60 * 60 * 1000; // Remove entries older than 24 hours
  
  /**
   * Private constructor to prevent direct instantiation
   */
  private constructor() {
    this.limits = new Map();
    // Schedule periodic cleanup of stale entries
    setInterval(() => this.cleanupStaleEntries(), this.CLEANUP_INTERVAL);
  }

  /**
   * Get the singleton instance of the rate limiter
   * @returns The OtpRateLimiter instance
   */
  public static getInstance(): OtpRateLimiter {
    if (!OtpRateLimiter.instance) {
      OtpRateLimiter.instance = new OtpRateLimiter();
    }
    return OtpRateLimiter.instance;
  }

  /**
   * Check if a phone number is allowed to request a new OTP
   * 
   * Implements three levels of rate limiting:
   * 1. Block period check: If the number is blocked, no sends are allowed
   * 2. Cooldown period check: A minimum time between consecutive sends
   * 3. Maximum sends per window check: Limits the total number of sends in a period
   * 
   * @param phoneNumber The phone number to check (E.164 format)
   * @returns Object containing whether the send is allowed, remaining attempts, and block time if applicable
   */
  public checkSendLimit(phoneNumber: string): { allowed: boolean; remainingAttempts: number; blockedUntil?: number } {
    try {
      // Normalize the phone number to prevent bypass attempts
      phoneNumber = this.normalizePhoneNumber(phoneNumber);
      const now = Date.now();
      const entry = this.getEntry(phoneNumber);

      // Check if blocked
      if (entry.blockUntil > now) {
        return {
          allowed: false,
          remainingAttempts: 0,
          blockedUntil: entry.blockUntil
        };
      }

      // Enforce cooldown between sends
      if (entry.lastSendTime > 0 && now - entry.lastSendTime < this.COOLDOWN_BETWEEN_SENDS) {
        return {
          allowed: false,
          remainingAttempts: this.MAX_SEND_ATTEMPTS - entry.sendAttempts,
          blockedUntil: entry.lastSendTime + this.COOLDOWN_BETWEEN_SENDS
        };
      }

      // Reset attempts if window has expired
      if (now - entry.lastSendTime >= this.WINDOW_DURATION) {
        entry.sendAttempts = 0;
        entry.verifyAttempts = 0;
      }

      const remainingAttempts = this.MAX_SEND_ATTEMPTS - entry.sendAttempts;

      if (remainingAttempts <= 0) {
        // Block for 1 hour if max attempts reached
        entry.blockUntil = now + this.BLOCK_DURATION;
        return {
          allowed: false,
          remainingAttempts: 0,
          blockedUntil: entry.blockUntil
        };
      }

      return {
        allowed: true,
        remainingAttempts
      };
    } catch (error) {
      console.error('Error in checkSendLimit:', error);
      // Default to allowed in case of errors, to prevent blocking legitimate users
      return {
        allowed: true,
        remainingAttempts: this.MAX_SEND_ATTEMPTS
      };
    }
  }

  /**
   * Record an OTP send attempt for a phone number
   * 
   * @param phoneNumber The phone number that requested an OTP (E.164 format)
   * @param otp Optional OTP value to store for verification tracking
   */  public recordSendAttempt(phoneNumber: string, otp?: string): void {
    try {
      // Normalize the phone number
      phoneNumber = this.normalizePhoneNumber(phoneNumber);
      
      // To handle race conditions, read the entry atomically (as much as JS allows)
      // and then update it atomically with proper bounds checking
      const entry = this.getEntry(phoneNumber);
      
      // Make sure we never exceed the maximum attempts due to concurrent operations
      if (entry.sendAttempts < this.MAX_SEND_ATTEMPTS) {
        entry.sendAttempts++;
      }
      
      entry.lastSendTime = Date.now();
      entry.lastAccessed = Date.now();
      entry.verifyAttempts = 0; // Reset verify attempts for new OTP
      
      if (otp) {
        entry.currentOtp = otp;
      }
    } catch (error) {
      console.error('Error in recordSendAttempt:', error);
    }
  }

  /**
   * Check if a phone number is allowed to verify an OTP
   * 
   * @param phoneNumber The phone number to check (E.164 format)
   * @returns Object containing whether the verification is allowed, remaining attempts, and block time if applicable
   */
  public checkVerifyLimit(phoneNumber: string): { allowed: boolean; remainingAttempts: number; blockedUntil?: number } {
    try {
      // Normalize the phone number
      phoneNumber = this.normalizePhoneNumber(phoneNumber);
      const now = Date.now();
      const entry = this.getEntry(phoneNumber);

      // Check if blocked
      if (entry.blockUntil > now) {
        return {
          allowed: false,
          remainingAttempts: 0,
          blockedUntil: entry.blockUntil
        };
      }

      const remainingAttempts = this.MAX_VERIFY_ATTEMPTS - entry.verifyAttempts;

      if (remainingAttempts <= 0) {
        // Block for 1 hour if max attempts reached
        entry.blockUntil = now + this.BLOCK_DURATION;
        return {
          allowed: false,
          remainingAttempts: 0,
          blockedUntil: entry.blockUntil
        };
      }

      return {
        allowed: true,
        remainingAttempts
      };
    } catch (error) {
      console.error('Error in checkVerifyLimit:', error);
      // Default to allowed in case of errors, to prevent blocking legitimate users
      return {
        allowed: true,
        remainingAttempts: this.MAX_VERIFY_ATTEMPTS
      };
    }
  }

  /**
   * Record an OTP verification attempt for a phone number
   * 
   * @param phoneNumber The phone number attempting verification (E.164 format)
   */
  public recordVerifyAttempt(phoneNumber: string): void {
    try {
      // Normalize the phone number
      phoneNumber = this.normalizePhoneNumber(phoneNumber);
      const entry = this.getEntry(phoneNumber);
      entry.verifyAttempts++;
      entry.lastAccessed = Date.now();
    } catch (error) {
      console.error('Error in recordVerifyAttempt:', error);
    }
  }

  /**
   * Get or create a rate limit entry for a phone number
   * 
   * @param phoneNumber The normalized phone number
   * @returns The rate limit entry for the phone number
   */
  private getEntry(phoneNumber: string): RateLimitEntry {
    if (!this.limits.has(phoneNumber)) {
      this.limits.set(phoneNumber, {
        sendAttempts: 0,
        verifyAttempts: 0,
        lastSendTime: 0,
        blockUntil: 0,
        lastAccessed: Date.now()
      });
    }
    
    const entry = this.limits.get(phoneNumber)!;
    // Update last accessed time
    entry.lastAccessed = Date.now();
    return entry;
  }

  /**
   * Clear all rate limit entries (for testing/debugging)
   */
  public clearLimits(): void {
    this.limits.clear();
  }  /**
   * Clean up stale entries to prevent memory growth
   * Removes entries that haven't been accessed in the configured TTL period
   * and are no longer blocked.
   */  private cleanupStaleEntries(): void {
    try {
      const now = Date.now();
      const staleTime = now - this.ENTRY_TTL;
      let removedCount = 0;
      
      // Remove entries that haven't been accessed in the TTL period and are not blocked
      // Use Array.from to avoid TypeScript iteration compatibility issues
      Array.from(this.limits.keys()).forEach(phoneNumber => {
        const entry = this.limits.get(phoneNumber);
        
        // First check if the entry is still blocked - if so, never remove it
        if (entry && entry.blockUntil > now) {
          return; // Skip this entry - it's still blocked
        }
        
        // If not blocked and hasn't been accessed in a while, clean it up
        if (entry && entry.lastAccessed < staleTime) {
          this.limits.delete(phoneNumber);
          removedCount++;
        }
      });
      
      if (removedCount > 0) {
        console.log(`OtpRateLimiter: Cleaned up ${removedCount} stale entries. Current size: ${this.limits.size}`);
      }
    } catch (error) {
      console.error('Error in cleanupStaleEntries:', error);
    }
  }
    /**
   * Normalize a phone number to ensure consistent formats for rate limiting
   * This helps prevent bypassing limits with slightly different formats
   * 
   * @param phoneNumber The phone number to normalize
   * @returns The normalized phone number
   */  private normalizePhoneNumber(phoneNumber: string): string {
    if (!phoneNumber) {
      return '';
    }
    
    try {
      // First trim whitespace
      let normalized = phoneNumber.trim();
      
      // Advanced normalization to handle various formats
      // Remove all non-digit characters except the leading +
      if (normalized.startsWith('+')) {
        normalized = '+' + normalized.substring(1).replace(/\D/g, '');
      } else {
        normalized = normalized.replace(/\D/g, '');
        if (normalized) {
          normalized = '+' + normalized;
        }
      }
      
      // Validate phone number format (basic check)
      if (!normalized.match(/^\+[1-9]\d{1,14}$/)) {
        console.warn(`OtpRateLimiter: Invalid phone number format: ${normalized}`);
      }
      
      return normalized;
    } catch (error) {
      console.error('Error normalizing phone number:', error);
      // Return a safe default value in case of unexpected errors
      return phoneNumber.toString().trim();
    }
  }
}

export default OtpRateLimiter;
