const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkTransactions() {
  try {
    console.log('Checking current transaction data...');
    
    const transactions = await prisma.transaction.findMany({
      include: {
        currencyAProvider: {
          select: { id: true, username: true, email: true }
        },
        currencyBProvider: {
          select: { id: true, username: true, email: true }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });
    
    console.log(`Found ${transactions.length} transactions:`);
    
    transactions.forEach((tx, index) => {
      console.log(`\n--- Transaction ${index + 1} ---`);
      console.log(`ID: ${tx.id.substring(0, 8)}...`);
      console.log(`Status: ${tx.status}`);
      console.log(`First Payer ID: ${tx.agreedFirstPayerId}`);
      console.log(`Currency A Provider: ${tx.currencyAProvider.username || tx.currencyAProvider.email} (${tx.currencyAProviderId})`);
      console.log(`Currency B Provider: ${tx.currencyBProvider.username || tx.currencyBProvider.email} (${tx.currencyBProviderId})`);
      console.log(`Payer 1 Payment Declared: ${tx.paymentDeclaredAtPayer1 ? 'Yes' : 'No'}`);
      console.log(`Payer 2 Payment Declared: ${tx.paymentDeclaredAtPayer2 ? 'Yes' : 'No'}`);
      console.log(`Created: ${tx.createdAt}`);
      
      // Analyze the status
      if (tx.agreedFirstPayerId) {
        const isFirstPayerCurrencyA = tx.agreedFirstPayerId === tx.currencyAProviderId;
        console.log(`First Payer is: ${isFirstPayerCurrencyA ? 'Currency A' : 'Currency B'} Provider`);
      }
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTransactions();
