/**
 * Test script to verify the new tag origin system works end-to-end
 * This script simulates the frontend sending a debug report with mixed tag origins
 */

const axios = require('axios');

const testReport = {
  logs: [
    {
      timestamp: new Date().toISOString(),
      level: 'ERROR',
      message: 'Test error for tag origin verification',
      context: { testData: true }
    }
  ],
  reportDetails: {
    type: 'bug',
    severity: 'medium',
    title: 'Test Report with Mixed Tag Origins',
    description: 'This is a test report to verify the new tag origin system works correctly.',
    stepsToReproduce: '1. Run test script\n2. Check database\n3. Verify tag origins',
    expectedBehavior: 'Tags should be stored with correct origins',
    actualBehavior: 'Testing the implementation',
    additionalNotes: 'This is a test of the hybrid AI tag system',
    reportTags: [
      { tag: 'urgent', origin: 'PREDEFINED' },
      { tag: 'login-issue', origin: 'AI_SUGGESTED' },
      { tag: 'custom-user-tag', origin: 'USER_DEFINED' }
    ]
  },
  timestamp: new Date().toISOString(),
  sessionId: 'test-session-' + Date.now(),
  userIdentification: {
    userId: 'test-user-123',
    email: '<EMAIL>',
    username: 'test-user'
  }
};

async function testTagOrigins() {
  try {
    console.log('🧪 Testing tag origin system...');
    console.log('📝 Report data:', JSON.stringify(testReport, null, 2));

    const response = await axios.post('http://localhost:3000/api/debug/report-issue', testReport, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Report submitted successfully!');
    console.log('📋 Response:', response.data);

    if (response.data.reportId) {
      console.log(`🔍 Report ID: ${response.data.reportId}`);
      console.log('💡 You can verify the tag origins in the database by checking the DebugReportTag table');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.status === 400) {
      console.error('💡 This might indicate a validation error with the new tag schema');
    }
  }
}

// Run the test
testTagOrigins();
