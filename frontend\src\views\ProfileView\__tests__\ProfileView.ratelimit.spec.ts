import { describe, it, expect, vi, beforeEach, afterEach, type Mock } from 'vitest';
import { mount, flushPromises, VueWrapper } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
// import { useAuthStore } from '@/stores/auth'; 
import ProfileView from '../ProfileView.vue';
import {
  NConfigProvider, NMessageProvider, NAlert
} from 'naive-ui';
import type { UserInfo } from '@/types/auth';
import { ref, computed, nextTick, type Ref } from 'vue';
import { AxiosError } from 'axios';
import type { RateLimitInfo } from '@/types/api';

// --- Mocks ---

const messageSuccessSpy = vi.fn();
const messageErrorSpy = vi.fn();

vi.mock('naive-ui', async (importOriginal) => {
  const originalNaive = await importOriginal<typeof import('naive-ui')>();
  const mockNFormValidateInFactory = vi.fn().mockResolvedValue(undefined);
  const mockNFormRestoreValidationInFactory = vi.fn();
  const mockNInputFocusInFactory = vi.fn();

  (originalNaive as any).mockNFormValidate = mockNFormValidateInFactory;
  (originalNaive as any).mockNFormRestoreValidation = mockNFormRestoreValidationInFactory;
  (originalNaive as any).mockNInputFocus = mockNInputFocusInFactory;

  return {
    ...originalNaive,
    useMessage: () => ({ success: messageSuccessSpy, error: messageErrorSpy }),
    NSpin: { template: '<div><slot /></div>' },
    NCard: { template: '<div><slot /></div>' },
    NDescriptions: { template: '<div><slot /></div>' },
    NDescriptionsItem: { template: '<div><slot /></div>' },
    NTag: { template: '<span><slot /></span>' },
    NDivider: { template: '<hr />' },
    NH3: { template: '<h3><slot /></h3>' },
    NAlert: {
      inheritAttrs: false, // Ensure attributes like data-testid are passed
      props: ['type', 'title', 'closable', 'showIcon'],
      emits: ['close'],
      template: `
        <div v-bind="$attrs" :data-type="type" :data-title="title">
          <button v-if="closable" @click="$emit('close')" data-testid="nalert-close-button">X</button>
          <strong>{{ title }}</strong>
          <slot />
        </div>
      `,
    },
    NForm: {
      name: 'StubbedNForm', 
      template: '<form @submit.prevent="$emit(\'submit\', $event)"><slot /></form>', 
      methods: {
        validate: mockNFormValidateInFactory, 
        restoreValidation: mockNFormRestoreValidationInFactory, 
      },
    },
    NInput: {
      inheritAttrs: false,
      props: ['value', 'disabled', 'type', 'placeholder', 'showPasswordOn', 'inputProps'],
      emits: ['update:value'],
      template: '<input v-bind="$attrs" :type="type || \'text\'" :value="value" :disabled="disabled" :placeholder="placeholder" @input="$emit(\'update:value\', $event.target.value)" />',
      methods: {
        focus: mockNInputFocusInFactory, 
      },
    },
    NButton: {
      props: ['type', 'disabled', 'loading', 'ghost', 'iconPlacement', 'secondary', 'strong', 'block', 'htmlType'],
      template: '<button :type="htmlType" :disabled="disabled || loading"><slot /></button>', // Use htmlType
    },
    NIcon: { template: '<i><slot /></i>' },
    NTooltip: { template: '<span><slot name="trigger" /><slot /></span>' },
  };
});

// Mock apiClient - Simplified and aligned with other working files
vi.mock('@/services/apiClient', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn(),
  }
}));

// Mock the auth store module
const mockUserRef = ref<UserInfo | null>(null);
const fetchUserProfileMock = vi.fn();
const updatePhoneVerificationStatusMock = vi.fn((status: boolean, phoneNumber: string | null) => {
  if (mockUserRef.value) {
    mockUserRef.value.phoneVerified = status;
    mockUserRef.value.phoneNumber = phoneNumber;
  }
});

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    user: mockUserRef,
    fetchUserProfile: fetchUserProfileMock,
    isLoggedIn: computed(() => !!mockUserRef.value),
    updatePhoneVerificationStatus: updatePhoneVerificationStatusMock,
  }),
}));

// CORRECTED Mock for errorHandler
vi.mock('@/utils/errorHandler', () => {
  const handleErrorMockInFactory = vi.fn((
    err: any,
    messageMock: any, // Represents the Naive UI message object
    defaultMessage?: string // The third argument is the default message string
  ): string => {
    let displayMessage = defaultMessage || 'An unknown error occurred in test (mock)';

    // Prioritize more specific messages from the error object
    if (err?.response?.data?.message) {
      displayMessage = err.response.data.message;
    } else if (err?.message) {
      displayMessage = err.message;
    }
    // Add other conditions from your actual handleError if necessary (e.g., err.response.statusText)

    // Simulate Naive UI message call if messageMock is provided
    if (messageMock && typeof messageMock.error === 'function') {
      messageMock.error(displayMessage);
    }

    return displayMessage; // Crucially, return the string
  });

  return {
    handleError: handleErrorMockInFactory,
  };
});
// Import MOCKED modules AFTER vi.mock calls
import apiClient from '@/services/apiClient';
// --- BEGIN: Added imports for mock module access ---
import * as NaiveUIMockedModule from 'naive-ui';
import * as ErrorHandlerMockedModule from '@/utils/errorHandler';
// --- END: Added imports for mock module access ---


const TestWrapper = {
  template: `
    <n-config-provider>
      <n-message-provider>
        <profile-view />
      </n-message-provider>
    </n-config-provider>
  `,
  components: {
    ProfileView, NConfigProvider, NMessageProvider,
  },
};

describe('ProfileView.vue - Rate Limiting & Blocking', () => {
  let wrapper: VueWrapper<any>;
  let profileViewVm: InstanceType<typeof ProfileView>;

  const baseUserUnverified: UserInfo = {
    id: 'rl-user-base',
    email: '<EMAIL>',
    emailVerified: true,
    phoneVerified: false,
    phoneNumber: null,
    reputation: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const setupTest = async (
    initialUser: UserInfo,
    initialRateLimit?: {
      blockedUntil?: Date | null;
      remainingAttempts?: number | null;
      error?: string | null; 
    }
  ) => {
    mockUserRef.value = initialUser;
    fetchUserProfileMock.mockResolvedValue(initialUser);
    (apiClient.get as Mock).mockResolvedValue({ data: initialUser });

    wrapper = mount(TestWrapper, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
    });

    profileViewVm = wrapper.findComponent(ProfileView).vm;

    if (initialRateLimit) {
      if (initialRateLimit.blockedUntil !== undefined) {
        profileViewVm.blockedUntil = initialRateLimit.blockedUntil ? initialRateLimit.blockedUntil.getTime() : null;
      }
      if (initialRateLimit.remainingAttempts !== undefined) {
        profileViewVm.remainingAttempts = initialRateLimit.remainingAttempts;
      }
      profileViewVm.error = initialRateLimit.error !== undefined ? initialRateLimit.error : null;

      if (profileViewVm.blockedUntil && profileViewVm.blockedUntil > Date.now()) {
        profileViewVm.currentTime = Date.now(); 
        profileViewVm.startBlockTimer(); 
      }
    } else {
      profileViewVm.blockedUntil = null;
      profileViewVm.remainingAttempts = null;
      profileViewVm.error = null;
    }

    await flushPromises();
    await nextTick(); 
    await nextTick(); 
  };

  beforeEach(() => {
    vi.resetAllMocks();

    const naiveAsAny = NaiveUIMockedModule as any;
    if (naiveAsAny.mockNFormValidate) {
      naiveAsAny.mockNFormValidate.mockClear().mockResolvedValue(undefined);
    }
    if (naiveAsAny.mockNFormRestoreValidation) {
      naiveAsAny.mockNFormRestoreValidation.mockClear();
    }
    if (naiveAsAny.mockNInputFocus) {
      naiveAsAny.mockNInputFocus.mockClear();
    }

    (apiClient.post as Mock).mockClear();
    (apiClient.get as Mock).mockClear();
    
    // CORRECTED Reset and re-implementation for handleError mock in beforeEach
    const errorHandlerAsAny = ErrorHandlerMockedModule as any; // Use the imported mocked module
    if (errorHandlerAsAny.handleError && typeof (errorHandlerAsAny.handleError as Mock).mockClear === 'function') {
        (errorHandlerAsAny.handleError as Mock).mockClear();
        (errorHandlerAsAny.handleError as Mock).mockImplementation((
          err: any,
          messageMock: any, // Represents the Naive UI message object
          defaultMessage?: string // The third argument is the default message string
        ): string => {
            let displayMessage = defaultMessage || 'An unknown error occurred in test (mock beforeEach)';

            if (err?.response?.data?.message) {
              displayMessage = err.response.data.message;
            } else if (err?.message) {
              displayMessage = err.message;
            }
            // Add other conditions from your actual handleError if necessary

            if (messageMock && typeof messageMock.error === 'function') {
              messageMock.error(displayMessage);
            }
            return displayMessage; // Return the processed error string
        });
    }
    
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers(); 
    if (wrapper) {
      wrapper.unmount();
    }
    mockUserRef.value = null; 
  });

  const createRateLimitError = (blockedUntilISO?: string, remainingAttempts: number = 0): AxiosError => {
    const error = new AxiosError('Too Many Requests');
    error.response = {
      data: {
        message: 'Rate limit exceeded.',
        remainingAttempts: remainingAttempts,
        blockedUntil: blockedUntilISO,
      },
      status: 429,
      statusText: 'Too Many Requests',
      headers: {},
      config: {} as any, 
    };
    return error;
  };

  it('handles 429 on requestOtp, shows alert, disables input/button, and starts countdown', async () => {
    const initialUser: UserInfo = { ...baseUserUnverified, id: 'rl-user-1' };
    await setupTest(initialUser);
    const testPhoneNumber = '+15551112222';
    const blockDurationSeconds = 30;
    const blockedUntilDate = new Date(Date.now() + blockDurationSeconds * 1000); 
    const apiError = createRateLimitError(blockedUntilDate.toISOString(), 0);
    (apiClient.post as Mock).mockRejectedValueOnce(apiError);

    const phoneInput = wrapper.find('input[data-testid="phone-input"]');
    expect(phoneInput.exists()).toBe(true);
    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]'); 
    expect(sendOtpButton.exists()).toBe(true);

    await phoneInput.setValue(testPhoneNumber);
    await sendOtpButton.trigger('click');
    await flushPromises();
    await nextTick();

    expect(apiClient.post).toHaveBeenCalledTimes(1); 
    expect(apiClient.post).toHaveBeenCalledWith('/auth/phone/send-otp', { phoneNumber: testPhoneNumber });
    // The messageErrorSpy is called by the mocked handleError
    expect(messageErrorSpy).toHaveBeenCalledWith('Rate limit exceeded.'); 
    // Check that the component's error state was set
    expect(profileViewVm.error).toBe('Rate limit exceeded.');


    const rateLimitAlert = wrapper.find('[data-testid="rate-limit-alert"]');
    expect(rateLimitAlert.exists(), 'Rate limit alert should exist after 429 error').toBe(true); // This should now pass
    expect(rateLimitAlert.attributes('data-title')).toBe('Rate Limit Exceeded');
    expect(rateLimitAlert.text()).toContain('Too many attempts.');
    expect(rateLimitAlert.text()).toMatch(/Please wait \d+ seconds before trying again\./);

    const updatedPhoneInput = wrapper.find('input[data-testid="phone-input"]');
    const updatedSendOtpButton = wrapper.find('[data-testid="send-otp-button"]');

    expect(updatedPhoneInput.attributes('disabled')).toBeDefined();
    expect(updatedSendOtpButton.attributes('disabled')).toBeDefined();

    await vi.advanceTimersByTimeAsync(10 * 1000);
    await nextTick(); 
    expect(rateLimitAlert.text()).toMatch(/Please wait [12]\d seconds before trying again\./);

    expect(updatedPhoneInput.attributes('disabled')).toBeDefined();
    expect(updatedSendOtpButton.attributes('disabled')).toBeDefined();
  });

  it('re-enables inputs/buttons and hides alert after rate limit block expires', async () => {
    const initialUser: UserInfo = { ...baseUserUnverified, id: 'rl-user-2' };
    await setupTest(initialUser);
    const testPhoneNumber = '+15553334444';
    const blockDurationSeconds = 15;
    const blockedUntilDate = new Date(Date.now() + blockDurationSeconds * 1000); 
    const apiError = createRateLimitError(blockedUntilDate.toISOString(), 0);
    (apiClient.post as Mock).mockRejectedValueOnce(apiError);

    const phoneInput = wrapper.find('input[data-testid="phone-input"]');
    expect(phoneInput.exists()).toBe(true);
    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]'); 
    expect(sendOtpButton.exists()).toBe(true);

    await phoneInput.setValue(testPhoneNumber);
    await sendOtpButton.trigger('click'); // CLICK THE BUTTON
    await flushPromises();
    await nextTick();

    expect(apiClient.post).toHaveBeenCalledTimes(1); 

    expect(wrapper.find('[data-testid="rate-limit-alert"]').exists()).toBe(true);
    expect(phoneInput.attributes('disabled')).toBeDefined();
    expect(sendOtpButton.attributes('disabled')).toBeDefined();

    await vi.advanceTimersByTimeAsync((blockDurationSeconds + 1) * 1000);
    await nextTick(); 
    await nextTick(); 

    expect(wrapper.find('[data-testid="rate-limit-alert"]').exists()).toBe(false);
    const finalPhoneInput = wrapper.find('input[data-testid="phone-input"]');
    const finalSendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
    expect(finalPhoneInput.attributes('disabled')).toBeUndefined();
    expect(finalSendOtpButton.attributes('disabled')).toBeUndefined();
  });

  it('handles 429 on verifyOtp, shows alert, disables input/button, and starts countdown', async () => {
    const initialUser: UserInfo = { ...baseUserUnverified, id: 'rl-user-3' };
    await setupTest(initialUser);
    const testPhoneNumber = '+15555556666';
    const testOtpCode = '112233';
    const blockDurationSeconds = 20;
    const blockedUntilDate = new Date(Date.now() + blockDurationSeconds * 1000);
    const verifyApiError = createRateLimitError(blockedUntilDate.toISOString(), 0);

    (apiClient.post as Mock)
      .mockResolvedValueOnce({ data: { message: 'OTP sent' } }) 
      .mockRejectedValueOnce(verifyApiError); 

    const phoneInput = wrapper.find('input[data-testid="phone-input"]');
    expect(phoneInput.exists()).toBe(true);
    const initialSendOtpButton = wrapper.find('[data-testid="send-otp-button"]'); 
    expect(initialSendOtpButton.exists()).toBe(true);

    await phoneInput.setValue(testPhoneNumber);
    await initialSendOtpButton.trigger('click'); // CLICK THE BUTTON
    await flushPromises();
    await nextTick(); 
    await nextTick(); 

    const otpInput = wrapper.find('input[data-testid="otp-input"]');
    expect(otpInput.exists()).toBe(true);
    const verifyOtpButton = wrapper.find('[data-testid="verify-otp-button"]'); 
    expect(verifyOtpButton.exists()).toBe(true);
    const changeNumberButton = wrapper.find('[data-testid="change-number-button"]');
    expect(changeNumberButton.exists()).toBe(true);
    
    await otpInput.setValue(testOtpCode);
    await verifyOtpButton.trigger('click'); // CLICK THE VERIFY BUTTON

    await flushPromises();
    await nextTick();

    expect(apiClient.post).toHaveBeenCalledTimes(2);
    expect(apiClient.post).toHaveBeenNthCalledWith(1, '/auth/phone/send-otp', { phoneNumber: testPhoneNumber });
    expect(apiClient.post).toHaveBeenNthCalledWith(2, '/auth/phone/verify-otp', { otpCode: testOtpCode });
    expect(messageErrorSpy).toHaveBeenCalledWith('Rate limit exceeded.');
    expect(profileViewVm.error).toBe('Rate limit exceeded.'); // Check component error state

    const rateLimitAlert = wrapper.find('[data-testid="rate-limit-alert"]');
    expect(rateLimitAlert.exists(), 'Rate limit alert should exist after verifyOtp 429 error').toBe(true); // This should now pass
    expect(rateLimitAlert.attributes('data-title')).toBe('Rate Limit Exceeded');
    expect(rateLimitAlert.text()).toMatch(/Too many attempts. Please wait \d+ seconds before trying again\./);

    expect(otpInput.attributes('disabled')).toBeDefined();
    expect(verifyOtpButton.attributes('disabled')).toBeDefined();
    if (changeNumberButton.exists()) {
        expect(changeNumberButton.attributes('disabled')).toBeDefined();
    }

    await vi.advanceTimersByTimeAsync(5 * 1000);
    await nextTick();
    expect(rateLimitAlert.text()).toMatch(/Too many attempts. Please wait [1]\d seconds before trying again\./);
  });

  it('shows remaining attempts message when attempts > 0 and no time block', async () => {
    const initialUser: UserInfo = { ...baseUserUnverified, id: 'rl-user-4' };
    await setupTest(initialUser);
    const testPhoneNumber = '+15557778888';
    const remainingAttemptsVal = 2;
    const apiError = createRateLimitError(undefined, remainingAttemptsVal); 
    (apiClient.post as Mock).mockRejectedValueOnce(apiError);

    const phoneInput = wrapper.find('input[data-testid="phone-input"]');
    expect(phoneInput.exists()).toBe(true);
    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]'); 
    expect(sendOtpButton.exists()).toBe(true);

    await phoneInput.setValue(testPhoneNumber);
    await sendOtpButton.trigger('click'); // CLICK THE BUTTON
    await flushPromises();
    await nextTick();

    expect(apiClient.post).toHaveBeenCalledTimes(1); 
    expect(apiClient.post).toHaveBeenCalledWith('/auth/phone/send-otp', { phoneNumber: testPhoneNumber });
    expect(messageErrorSpy).toHaveBeenCalledWith('Rate limit exceeded.');
    expect(profileViewVm.error).toBe('Rate limit exceeded.'); // Check component error state
    expect(profileViewVm.remainingAttempts).toBe(remainingAttemptsVal);
    expect(profileViewVm.blockedUntil).toBeNull();


    const rateLimitAlert = wrapper.find('[data-testid="rate-limit-alert"]');
    expect(rateLimitAlert.exists(), 'Rate limit alert should exist for remaining attempts message').toBe(true); // This should now pass
    expect(rateLimitAlert.attributes('data-title')).toBe('Rate Limit Exceeded');
    // The component logic shows "You have X attempts remaining." when blockTimeRemaining is 0
    expect(rateLimitAlert.text()).toContain(`You have ${remainingAttemptsVal} attempts remaining.`);
    expect(rateLimitAlert.text()).not.toContain('Please wait'); 

    expect(phoneInput.attributes('disabled')).toBeUndefined();
    expect(sendOtpButton.attributes('disabled')).toBeUndefined();
  });


  describe('Pre-existing Rate Limits and Blocks on Load', () => {
    it('Scenario V.14: renders correctly and disables inputs if initially blocked by time', async () => {
      const futureBlockTime = Date.now() + 60 * 1000 * 5;
      await setupTest(
        { ...baseUserUnverified, id: 'v14-user' },
        { blockedUntil: new Date(futureBlockTime), remainingAttempts: 0, error: null }
      );
      
      await nextTick();

      const errorAlert = wrapper.find('[data-testid="rate-limit-alert"]');
      expect(errorAlert.exists(), 'Error alert (rate-limit-alert) should not exist when error is null').toBe(false);

      const infoAlert = wrapper.findAllComponents(NAlert).find(a => a.props('type') === 'info');
      expect(infoAlert?.exists(), 'Info alert should exist when blocked by time without an error message').toBe(true);
      expect(infoAlert?.text()).toContain('Please wait');
      expect(infoAlert?.text()).toMatch(/before requesting or verifying again\./);


      const phoneInput = wrapper.find('input[data-testid="phone-input"]');
      expect(phoneInput.exists()).toBe(true);
      const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
      expect(sendOtpButton.exists()).toBe(true);


      expect(phoneInput.attributes('disabled')).toBeDefined();
      expect(sendOtpButton.attributes('disabled')).toBeDefined();

      profileViewVm.showOtpForm = true; 
      await nextTick();
      const otpInput = wrapper.find('input[data-testid="otp-input"]');
      expect(otpInput.exists()).toBe(true);
      const verifyOtpButton = wrapper.find('[data-testid="verify-otp-button"]');
      expect(verifyOtpButton.exists()).toBe(true);
      const resendOtpButton = wrapper.find('[data-testid="resend-otp-button"]');
      expect(resendOtpButton.exists()).toBe(true);

      expect(otpInput.attributes('disabled')).toBeDefined();
      expect(verifyOtpButton.attributes('disabled')).toBeDefined();
      expect(resendOtpButton.attributes('disabled')).toBeDefined();
    });

    it('Scenario V.16: renders correctly and disables inputs if initially out of attempts (no time block)', async () => {
      // Ensure this error message will be caught by isRateLimitError in ProfileView.vue
      const specificMessage = 'You are out of attempts. Please try again later.';
      const errorMessageForAlert = `Too many attempts. ${specificMessage}`; 
      await setupTest(
        { ...baseUserUnverified, id: 'v16-user' },
        { blockedUntil: null, remainingAttempts: 0, error: errorMessageForAlert }
      );
      await nextTick();

      const rateLimitAlert = wrapper.find('[data-testid="rate-limit-alert"]');
      expect(rateLimitAlert.exists(), 'Error alert (rate-limit-alert) should exist when error is set appropriately').toBe(true);
      expect(rateLimitAlert.attributes('data-title')).toBe('Rate Limit Exceeded');
      // The component's slot logic will display the full error message when no block time and no remaining attempts > 0
      expect(rateLimitAlert.text()).toContain(errorMessageForAlert);

      const phoneInput = wrapper.find('input[data-testid="phone-input"]');
      expect(phoneInput.exists()).toBe(true);
      const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
      expect(sendOtpButton.exists()).toBe(true);

      expect(phoneInput.attributes('disabled')).toBeDefined(); 
      expect(sendOtpButton.attributes('disabled')).toBeDefined(); 

      profileViewVm.showOtpForm = true;
      await nextTick();
      const otpInput = wrapper.find('input[data-testid="otp-input"]');
      expect(otpInput.exists()).toBe(true);
      const verifyOtpButton = wrapper.find('[data-testid="verify-otp-button"]');
      expect(verifyOtpButton.exists()).toBe(true);
      const resendOtpButton = wrapper.find('[data-testid="resend-otp-button"]');
      expect(resendOtpButton.exists()).toBe(true);

      expect(otpInput.attributes('disabled')).toBeDefined();
      expect(verifyOtpButton.attributes('disabled')).toBeDefined();
      expect(resendOtpButton.attributes('disabled')).toBeDefined();
    });
  });

  describe('UI Interactions for Alerts', () => {
    it('Scenario VIII.23: allows closing the rate limit alert and clears messages', async () => {
      const futureBlockTime = Date.now() + 60000;
      const specificMessageDetail = 'Closing test scenario.';
      const initialErrorMsg = `Rate limit exceeded. ${specificMessageDetail}`;
      
      await setupTest(
        { ...baseUserUnverified, id: 'v23-user' },
        { blockedUntil: new Date(futureBlockTime), remainingAttempts: 0, error: initialErrorMsg }
      );
      await nextTick();

      let rateLimitAlert = wrapper.find('[data-testid="rate-limit-alert"]');
      expect(rateLimitAlert.exists(), 'Rate limit alert should exist initially').toBe(true);
      expect(profileViewVm.error).toBe(initialErrorMsg);

      // Ensure the 'closable' prop is true for the NAlert in the component for this test case
      // In ProfileView.vue, the <n-alert> has `closable` attribute, which means it's true.
      // The mock for NAlert has `v-if="closable"` for the button.

      const closeButton = rateLimitAlert.find('[data-testid="nalert-close-button"]');
      expect(closeButton.exists(), 'Alert close button should exist').toBe(true);

      await closeButton.trigger('click');
      await nextTick();

      expect(profileViewVm.error, 'profileViewVm.error should be null after closing alert').toBeNull();
      
      rateLimitAlert = wrapper.find('[data-testid="rate-limit-alert"]'); 
      expect(rateLimitAlert.exists(), 'Rate limit alert should not exist after closing because error is cleared').toBe(false);
    });
  });
});