/**
 * Stagewise toolbar configuration for development mode
 * This file handles the integration of stagewise dev-tool with the MUNygo Vue application
 */

// Note: @stagewise-plugins/vue doesn't exist, using a placeholder
// The actual VuePlugin functionality is handled by @stagewise/toolbar-vue
const VuePlugin = {};

/**
 * Basic stagewise configuration
 * You can extend this with additional plugins and settings as needed
 */
export const stagewiseConfig = {
  plugins: [
    VuePlugin
  ],
  // Additional configuration options
  enabled: import.meta.env.DEV,
  // Reduce connection timeout to fail faster if server is not available
  connectionTimeout: 5000,
  // Reduce reconnection attempts to avoid spam
  maxReconnectAttempts: 3,
  // Set a custom port if needed (uncomment and adjust if you have conflicts)
  // port: 5746,
  // Additional configuration options can be added here
  // For example:
  // theme: 'dark' | 'light' | 'auto',
  // position: 'top' | 'bottom' | 'left' | 'right',
  // etc.
};

/**
 * Check if we should enable stagewise toolbar
 * Only enable in development mode and when Vue is available
 * Can be disabled via VITE_DISABLE_STAGEWISE environment variable
 */
export const shouldEnableStagewise = (): boolean => {
  // Allow disabling via environment variable
  if (import.meta.env.VITE_DISABLE_STAGEWISE === 'true') {
    return false;
  }
  
  // Check if we're in development mode
  if (!import.meta.env.DEV || import.meta.env.MODE !== 'development') {
    return false;
  }
  
  // Check if Vue is available globally (helps with the Vue detection issue)
  if (typeof window !== 'undefined' && (window as any).__VUE__) {
    return true;
  }
  
  // Still allow if Vue dev tools are available
  if (typeof window !== 'undefined' && (window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__) {
    return true;
  }
  
  // Default to true in development, let stagewise handle its own detection
  return true;
};
