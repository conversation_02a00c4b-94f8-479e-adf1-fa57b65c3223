// Interest Store Socket Diagnostics
// Run this in the browser console to debug the issue

function debugInterestStoreSocket() {
  console.log('🔍 [DEBUG] Starting Interest Store Socket Diagnostics...');
  
  // Check if stores are available
  const app = document.querySelector('#app').__vueParentComponent?.ctx || window.__VUE__;
  if (!app) {
    console.error('❌ [DEBUG] Cannot access Vue app instance');
    return;
  }

  try {
    // Get centralized socket manager
    const centralizedManager = window.centralizedSocketManager || 
      (await import('/src/services/centralizedSocketManager.js')).default;
    
    console.log('🔍 [DEBUG] Centralized Socket Manager:', centralizedManager);
    console.log('🔍 [DEBUG] Current socket:', centralizedManager.getSocket());
    
    // Check registered handlers
    const socket = centralizedManager.getSocket();
    if (socket) {
      console.log('🔍 [DEBUG] Socket connected:', socket.connected);
      console.log('🔍 [DEBUG] Socket ID:', socket.id);
      
      // Check event listeners on the socket
      const listeners = socket.listeners('INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY');
      console.log('🔍 [DEBUG] INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY listeners:', listeners.length);
    }
    
    // Try to access stores
    try {
      const { useInterestStore } = await import('/src/stores/interestStore.js');
      const { useOfferStore } = await import('/src/stores/offerStore.js');
      const { useAuthStore } = await import('/src/stores/auth.js');
      
      const interestStore = useInterestStore();
      const offerStore = useOfferStore();
      const authStore = useAuthStore();
      
      console.log('🔍 [DEBUG] Current user:', authStore.user?.id);
      console.log('🔍 [DEBUG] InterestStore available:', !!interestStore);
      console.log('🔍 [DEBUG] OfferStore available:', !!offerStore);
      console.log('🔍 [DEBUG] Current offers count:', offerStore.offers?.length || 0);
      
      // Try to manually initialize the interest store listeners
      console.log('🔍 [DEBUG] Manually calling interestStore.initializeSocketListeners()...');
      interestStore.initializeSocketListeners();
      
      console.log('✅ [DEBUG] Interest store listeners initialized');
      
      // Test sending a mock event
      if (socket && socket.connected) {
        console.log('🔍 [DEBUG] Testing with mock event...');
        
        const mockPayload = {
          interestId: 'test-interest',
          offerId: 'test-offer',
          chatSessionId: 'test-chat',
          interestedUser: {
            userId: authStore.user?.id,
            username: authStore.user?.username,
            email: authStore.user?.email,
          },
          offerCreator: {
            userId: 'other-user',
            username: 'otheruser',
            email: '<EMAIL>',
          },
          offer: {
            id: 'test-offer',
            title: 'Test Offer',
            description: 'Test Description',
            amount: 100,
          },
        };
        
        // Emit the event to test the handler
        socket.emit('INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY', mockPayload);
        console.log('🔍 [DEBUG] Sent test event with payload:', mockPayload);
      }
      
    } catch (storeError) {
      console.error('❌ [DEBUG] Error accessing stores:', storeError);
    }
    
  } catch (error) {
    console.error('❌ [DEBUG] General error:', error);
  }
}

// Also add a function to check the current route and component
function debugCurrentView() {
  console.log('🔍 [DEBUG] Current URL:', window.location.href);
  console.log('🔍 [DEBUG] Current route path:', window.location.pathname);
  
  // Check if we're on the browse offers page
  if (window.location.pathname.includes('browse') || window.location.pathname === '/') {
    console.log('✅ [DEBUG] On browse offers page - should have interest store listeners');
  } else {
    console.log('ℹ️ [DEBUG] Not on browse offers page');
  }
}

// Export functions to window for easy access
window.debugInterestStoreSocket = debugInterestStoreSocket;
window.debugCurrentView = debugCurrentView;

console.log('🔧 [DEBUG] Diagnostic functions loaded. Run:');
console.log('  - debugInterestStoreSocket() to check socket setup');
console.log('  - debugCurrentView() to check current page');

export { debugInterestStoreSocket, debugCurrentView };
