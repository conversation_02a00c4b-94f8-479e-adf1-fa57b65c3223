#!/bin/bash

# PRODUCTION DEPLOYMENT SCRIPT FOR CENTOS 9
# ==========================================
# This script deploys the debug report system to production CentOS 9
# while preserving all existing data

echo "🚀 Production Deployment - CentOS 9"
echo "===================================="

# Exit on any error
set -e

# Check if we're running as root or with sudo
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  Running as root. Make sure Docker daemon is accessible."
fi

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command_exists docker; then
    echo "❌ Docker is not installed"
    echo "📥 Install with: sudo dnf install docker"
    exit 1
fi

if ! command_exists docker-compose; then
    echo "❌ Docker Compose is not installed"
    echo "📥 Install with: sudo dnf install docker-compose-plugin"
    exit 1
fi

# Check if Docker daemon is running
if ! systemctl is-active --quiet docker; then
    echo "❌ Docker daemon is not running"
    echo "🔧 Start with: sudo systemctl start docker"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Load environment variables
if [ -f .env ]; then
    echo "📄 Loading environment variables from .env..."
    set -a
    source .env
    set +a
    echo "✅ Environment variables loaded"
else
    echo "❌ .env file not found! Please create it with required variables."
    exit 1
fi

# Verify critical environment variables
echo "🔍 Verifying critical environment variables..."

REQUIRED_VARS=(
    "POSTGRES_DB"
    "POSTGRES_USER" 
    "POSTGRES_PASSWORD"
    "DATABASE_URL"
    "JWT_SECRET"
    "VITE_BACKEND_URL_FOR_CLIENT"
    "VITE_ADMIN_EMAILS"
    "GEMINI_API_KEY"
)

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Required environment variable $var is not set"
        exit 1
    fi
done

echo "✅ All required environment variables are set"

# Show current configuration
echo ""
echo "📊 Current Configuration:"
echo "  Database: $POSTGRES_DB"
echo "  Database User: $POSTGRES_USER"
echo "  Backend URL: $VITE_BACKEND_URL_FOR_CLIENT"
echo "  Debug Reports: ${VITE_ENABLE_DEBUG_REPORT:-true}"
echo "  Admin Emails: $VITE_ADMIN_EMAILS"
echo "  Node Environment: ${NODE_ENV:-production}"
echo ""

# Check if this is a new deployment or update
if [ "$(docker ps -aq -f name=munygo)" ]; then
    echo "🔄 Existing MUNygo containers detected. This will be an UPDATE deployment."
    DEPLOYMENT_TYPE="UPDATE"
else
    echo "🆕 No existing containers found. This will be a FRESH deployment."
    DEPLOYMENT_TYPE="FRESH"
fi

echo ""

# Ask for confirmation
read -p "🤔 Do you want to proceed with $DEPLOYMENT_TYPE deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "🛑 Deployment cancelled by user"
    exit 0
fi

echo "🧹 Step 1: Preparing for deployment..."

if [ "$DEPLOYMENT_TYPE" = "UPDATE" ]; then
    echo "💾 Backing up current container data..."
    
    # Create a timestamp
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    
    # Export current database if accessible
    if docker-compose exec -T postgres pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" >/dev/null 2>&1; then
        echo "📦 Creating database backup before update..."
        docker-compose exec -T postgres pg_dump -U "$POSTGRES_USER" -d "$POSTGRES_DB" > "backup_before_update_$TIMESTAMP.sql"
        echo "✅ Database backup created: backup_before_update_$TIMESTAMP.sql"
    fi
    
    # Stop containers gracefully
    echo "🛑 Stopping existing containers..."
    docker-compose down
else
    # Fresh deployment - clean up any orphaned containers
    echo "🧹 Cleaning up any orphaned containers..."
    docker-compose down --remove-orphans
fi

echo "✅ Preparation completed"

echo "🔧 Step 2: Building new images..."
echo "⚠️  This may take several minutes for the first build..."

# Build with no cache to ensure we get the latest changes
docker-compose build --no-cache --progress=plain

if [ $? -ne 0 ]; then
    echo "❌ Build failed! Check the error messages above."
    exit 1
fi

echo "✅ Build completed successfully"

echo "🗄️ Step 3: Starting database service..."
docker-compose up -d postgres

echo "⏳ Waiting for PostgreSQL to be ready..."

# Wait for PostgreSQL to be ready with more robust checking
MAX_ATTEMPTS=60
ATTEMPT=1

while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
    if docker-compose exec postgres pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" >/dev/null 2>&1; then
        echo "✅ PostgreSQL is ready"
        break
    fi
    
    echo "  Still waiting for PostgreSQL... ($ATTEMPT/$MAX_ATTEMPTS)"
    sleep 2
    ATTEMPT=$((ATTEMPT + 1))
done

if [ $ATTEMPT -gt $MAX_ATTEMPTS ]; then
    echo "❌ PostgreSQL failed to start within $((MAX_ATTEMPTS * 2)) seconds"
    echo "📜 PostgreSQL logs:"
    docker-compose logs postgres
    exit 1
fi

echo "🗃️ Step 4: Running database migrations (safe mode)..."

# Use Prisma to handle database migrations safely
# This will only apply new migrations without affecting existing data
docker-compose run --rm backend npx prisma migrate deploy

if [ $? -ne 0 ]; then
    echo "❌ Database migration failed!"
    echo "📜 Backend logs:"
    docker-compose logs backend
    exit 1
fi

echo "✅ Database migrations completed successfully"

echo "🎯 Step 5: Starting all services..."
docker-compose up -d

echo "✅ All services started"

echo "🏥 Step 6: Health checks..."
echo "⏳ Waiting for services to be healthy..."

# Wait for backend health check
MAX_ATTEMPTS=60
ATTEMPT=1

echo "  Checking backend health..."
while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
    if curl -s -f http://localhost:3000/health >/dev/null 2>&1; then
        echo "✅ Backend is healthy"
        break
    fi
    
    echo "    Waiting for backend... ($ATTEMPT/$MAX_ATTEMPTS)"
    sleep 2
    ATTEMPT=$((ATTEMPT + 1))
done

if [ $ATTEMPT -gt $MAX_ATTEMPTS ]; then
    echo "⚠️  Backend health check timeout. Checking if it's responding..."
    docker-compose logs --tail=20 backend
fi

# Wait for frontend
MAX_ATTEMPTS=30
ATTEMPT=1

echo "  Checking frontend availability..."
while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
    if curl -s -f http://localhost:8080 >/dev/null 2>&1; then
        echo "✅ Frontend is accessible"
        break
    fi
    
    echo "    Waiting for frontend... ($ATTEMPT/$MAX_ATTEMPTS)"
    sleep 2
    ATTEMPT=$((ATTEMPT + 1))
done

if [ $ATTEMPT -gt $MAX_ATTEMPTS ]; then
    echo "⚠️  Frontend accessibility timeout. Checking container status..."
    docker-compose ps
fi

echo ""
echo "🎉 Production deployment completed!"
echo ""
echo "📍 Service URLs:"
echo "  🌐 Frontend: http://localhost:8080"
echo "  🔧 Backend API: http://localhost:3000"
echo "  📊 Admin Debug Dashboard: http://localhost:8080/admin/debug-dashboard"
echo ""
echo "🔧 New Debug Features:"
echo "  • Debug Report Button: Visible to all users (enabled: ${VITE_ENABLE_DEBUG_REPORT:-true})"
echo "  • Admin Panel: Available to: $VITE_ADMIN_EMAILS"
echo "  • AI-Powered Analysis: Enabled (Gemini API configured)"
echo "  • Voice Bug Reporting: Available in debug reports"
echo ""
echo "📋 Service Status:"
docker-compose ps
echo ""
echo "🧪 Testing Instructions:"
echo "  1. Open http://localhost:8080"
echo "  2. Login with existing account or register new one"
echo "  3. Look for debug report button (🐛 bug icon) in navigation bar"
echo "  4. If you're an admin, try accessing: http://localhost:8080/admin/debug-dashboard"
echo "  5. Test submitting a debug report to verify the system works"
echo ""
echo "📜 Useful Commands:"
echo "  • Check logs: docker-compose logs -f [service_name]"
echo "  • Check status: docker-compose ps"
echo "  • Stop services: docker-compose down"
echo "  • Restart service: docker-compose restart [service_name]"
echo ""

# Final status check
if docker-compose ps | grep -q "Up"; then
    echo "✅ Deployment appears successful! Services are running."
    
    # Check if we can access the debug dashboard
    if curl -s -f http://localhost:8080/admin/debug-dashboard >/dev/null 2>&1; then
        echo "✅ Admin debug dashboard is accessible"
    else
        echo "ℹ️  Admin debug dashboard requires authentication"
    fi
else
    echo "⚠️  Some services may not be running properly. Check with: docker-compose ps"
fi
