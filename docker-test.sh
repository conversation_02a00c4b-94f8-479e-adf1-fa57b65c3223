#!/bin/bash

# Docker Local Testing Script for MUNygo
# This script helps you test your Docker setup locally

echo "🚀 MUNygo Docker Local Testing Script"
echo "====================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

echo "✅ Docker is running"

# Function to cleanup previous containers
cleanup() {
    echo "🧹 Cleaning up previous containers and volumes..."
    docker-compose down -v
    docker system prune -f
}

# Function to build and start services
start_services() {
    echo "🔨 Building and starting MUNygo services..."
    docker-compose up --build -d
}

# Function to check service health
check_health() {
    echo "🏥 Checking service health..."
    
    echo "Waiting for services to start..."
    sleep 30
    
    # Check PostgreSQL
    if docker-compose exec -T postgres pg_isready -U munygo -d munygo > /dev/null 2>&1; then
         echo "✅ PostgreSQL is healthy"
     else
         echo "❌ PostgreSQL is not healthy"
        echo "💡 Try: docker-compose logs postgres"
     fi
    
    # Check Backend
    if timeout 10 curl -f http://localhost:3000/health > /dev/null 2>&1; then
         echo "✅ Backend is healthy"
     else
         echo "❌ Backend is not healthy"
        echo "💡 Try: docker-compose logs backend"
     fi
    
    # Check Frontend
    if curl -f http://localhost:80 > /dev/null 2>&1; then
        echo "✅ Frontend is healthy"
    else
        echo "❌ Frontend is not healthy"
    fi
}

# Function to show logs
show_logs() {
    echo "📋 Service logs:"
    docker-compose logs --tail=50
}

# Function to open browser
open_app() {
    echo "🌐 Opening MUNygo in your browser..."
    case "$(uname -s)" in
        Darwin*)
            open http://localhost
            ;;
        MINGW*|CYGWIN*|MSYS*)
            start http://localhost
            ;;
        Linux*)
            xdg-open http://localhost 2>/dev/null || echo "Open http://localhost in your browser to access MUNygo"
            ;;
        *)
            echo "Open http://localhost in your browser to access MUNygo"
            ;;
    esac
}

# Main execution
case "${1:-start}" in
    "cleanup")
        cleanup
        ;;
    "start")
        cleanup
        start_services
        check_health
        show_logs
        open_app
        ;;
    "stop")
        echo "🛑 Stopping all services..."
        docker-compose down
        ;;
    "logs")
        show_logs
        ;;
    "health")
        check_health
        ;;
    *)
        echo "Usage: $0 {start|stop|cleanup|logs|health}"
        echo "  start   - Clean, build and start all services (default)"
        echo "  stop    - Stop all services"
        echo "  cleanup - Remove all containers and volumes"
        echo "  logs    - Show service logs"
        echo "  health  - Check service health"
        ;;
esac
