import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import { NButton, NCheckbox, NForm, NFormItem, NInput } from 'naive-ui'
import PaymentReadinessGate from '../PaymentReadinessGate.vue'
import { usePayerNegotiationStore } from '@/stores/payerNegotiation'

// Mock the stores
vi.mock('@/stores/payerNegotiation')

const createWrapper = (props = {}) => {
  const defaultProps = {
    negotiationId: 'test-negotiation-id',
    profileDetails: null,
    bankDetailsProvided: false
  }

  return mount(PaymentReadinessGate, {
    props: { ...defaultProps, ...props },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false
        })
      ],
      components: {
        NButton,
        NCheckbox,
        NForm,
        NFormItem,
        NInput
      }
    }
  })
}

describe('PaymentReadinessGate', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Payment Persistence Tests', () => {
    it('should set saveToProfile to true when showing new details form', async () => {
      const wrapper = createWrapper({
        profileDetails: null,
        bankDetailsProvided: false
      })

      // Find and click the "Add payment details" button
      const addButton = wrapper.find('[data-testid="add-payment-details"]')
      if (addButton.exists()) {
        await addButton.trigger('click')
      } else {
        // If button doesn't exist, the form should already be showing
        // Trigger showNewDetailsForm manually
        await wrapper.vm.showNewDetailsForm()
      }

      await wrapper.vm.$nextTick()

      // Verify that saveToProfile is set to true
      expect(wrapper.vm.formModel.saveToProfile).toBe(true)
      expect(wrapper.vm.showForm).toBe(true)
    })

    it('should set saveToProfile to true when editing existing details', async () => {
      const mockProfileDetails = {
        bankName: 'Test Bank',
        accountNumber: '*********',
        accountHolderName: 'John Doe'
      }

      const wrapper = createWrapper({
        profileDetails: mockProfileDetails,
        bankDetailsProvided: true
      })

      // Trigger showEditDetails
      await wrapper.vm.showEditDetails()
      await wrapper.vm.$nextTick()

      // Verify that saveToProfile is set to true and form is populated
      expect(wrapper.vm.formModel.saveToProfile).toBe(true)
      expect(wrapper.vm.formModel.bankName).toBe('Test Bank')
      expect(wrapper.vm.formModel.accountNumber).toBe('*********')
      expect(wrapper.vm.formModel.accountHolderName).toBe('John Doe')
      expect(wrapper.vm.showForm).toBe(true)
    })

    it('should set saveToProfile to false when canceling to existing profile data', async () => {
      const mockProfileDetails = {
        bankName: 'Test Bank',
        accountNumber: '*********',
        accountHolderName: 'John Doe'
      }

      const wrapper = createWrapper({
        profileDetails: mockProfileDetails,
        bankDetailsProvided: true
      })

      // First show edit form
      await wrapper.vm.showEditDetails()
      expect(wrapper.vm.formModel.saveToProfile).toBe(true)

      // Then cancel
      await wrapper.vm.cancelNewDetails()
      await wrapper.vm.$nextTick()

      // Verify that saveToProfile is set to false (using existing profile data)
      expect(wrapper.vm.formModel.saveToProfile).toBe(false)
      expect(wrapper.vm.showForm).toBe(false)
    })

    it('should call updatePaymentInfo with saveToProfile: true when submitting new details', async () => {
      const mockStore = usePayerNegotiationStore()
      const updatePaymentInfoSpy = vi.spyOn(mockStore, 'updatePaymentInfo').mockResolvedValue(undefined)

      const wrapper = createWrapper({
        profileDetails: null,
        bankDetailsProvided: false
      })

      // Show new details form
      await wrapper.vm.showNewDetailsForm()
      
      // Fill form
      wrapper.vm.formModel = {
        bankName: 'New Bank',
        accountNumber: '*********',
        accountHolderName: 'Jane Doe',
        saveToProfile: true
      }

      // Submit form
      await wrapper.vm.savePaymentInfo()

      // Verify updatePaymentInfo was called with saveToProfile: true
      expect(updatePaymentInfoSpy).toHaveBeenCalledWith('test-negotiation-id', {
        bankName: 'New Bank',
        accountNumber: '*********',
        accountHolderName: 'Jane Doe',
        saveToProfile: true
      })
    })

    it('should call updatePaymentInfo with saveToProfile: true when submitting edited details', async () => {
      const mockStore = usePayerNegotiationStore()
      const updatePaymentInfoSpy = vi.spyOn(mockStore, 'updatePaymentInfo').mockResolvedValue(undefined)

      const mockProfileDetails = {
        bankName: 'Original Bank',
        accountNumber: '*********',
        accountHolderName: 'Original Name'
      }

      const wrapper = createWrapper({
        profileDetails: mockProfileDetails,
        bankDetailsProvided: true
      })

      // Show edit form
      await wrapper.vm.showEditDetails()
      
      // Modify form
      wrapper.vm.formModel.bankName = 'Modified Bank'

      // Submit form
      await wrapper.vm.savePaymentInfo()

      // Verify updatePaymentInfo was called with saveToProfile: true
      expect(updatePaymentInfoSpy).toHaveBeenCalledWith('test-negotiation-id', {
        bankName: 'Modified Bank',
        accountNumber: '*********',
        accountHolderName: 'Original Name',
        saveToProfile: true
      })
    })
  })

  describe('UI State Tests', () => {
    it('should display existing payment details when profile has payment info', () => {
      const mockProfileDetails = {
        bankName: 'Test Bank',
        accountNumber: '*********',
        accountHolderName: 'John Doe'
      }

      const wrapper = createWrapper({
        profileDetails: mockProfileDetails,
        bankDetailsProvided: true
      })

      expect(wrapper.text()).toContain('Test Bank')
      expect(wrapper.text()).toContain('*********')
      expect(wrapper.text()).toContain('John Doe')
    })

    it('should show add payment details prompt when no payment info exists', () => {
      const wrapper = createWrapper({
        profileDetails: null,
        bankDetailsProvided: false
      })

      expect(wrapper.text()).toContain('Add payment details')
    })

    it('should toggle form visibility correctly', async () => {
      const wrapper = createWrapper()

      expect(wrapper.vm.showForm).toBe(false)

      await wrapper.vm.showNewDetailsForm()
      expect(wrapper.vm.showForm).toBe(true)

      await wrapper.vm.cancelNewDetails()
      expect(wrapper.vm.showForm).toBe(false)
    })
  })

  describe('Form Validation Tests', () => {
    it('should validate required fields', async () => {
      const wrapper = createWrapper()
      await wrapper.vm.showNewDetailsForm()

      // Try to submit empty form
      wrapper.vm.formModel = {
        bankName: '',
        accountNumber: '',
        accountHolderName: '',
        saveToProfile: true
      }

      // Mock form validation failure
      const formRef = { validate: vi.fn().mockResolvedValue(false) }
      wrapper.vm.$refs.formRef = formRef

      await wrapper.vm.savePaymentInfo()

      expect(formRef.validate).toHaveBeenCalled()
    })
  })
})
