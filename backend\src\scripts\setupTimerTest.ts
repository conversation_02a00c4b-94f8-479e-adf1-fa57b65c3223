import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function setupTimerTest() {
  try {
    console.log('Setting up timer test transaction...');

    // Find existing users
    const users = await prisma.user.findMany({
      take: 2,
      select: { id: true, username: true, email: true }
    });

    if (users.length < 2) {
      console.log('Need at least 2 users. Creating test users...');
      // Create test users if needed
      const user1 = await prisma.user.create({
        data: {
          username: 'testuser1',
          email: '<EMAIL>',
          passwordHash: 'dummy',
          emailVerified: true,
          phoneVerified: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      
      const user2 = await prisma.user.create({
        data: {
          username: 'testuser2',
          email: '<EMAIL>',
          passwordHash: 'dummy',
          emailVerified: true,
          phoneVerified: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      
      users.push(user1, user2);
    }    console.log('Using users:', users.map(u => ({ id: u.id, username: u.username })));

    // Find or create an offer first
    let offer = await prisma.offer.findFirst({
      where: { userId: users[0].id }
    });

    if (!offer) {
      offer = await prisma.offer.create({
        data: {
          userId: users[0].id,
          type: 'SELLING',
          currencyPair: 'USD-EUR',
          amount: 100,
          baseRate: 0.85,
          adjustmentForLowerRep: 0.05,
          adjustmentForHigherRep: -0.02,
          status: 'ACTIVE'
        }
      });
    }

    // Find or create a chat session
    let chatSession = await prisma.chatSession.findFirst({
      where: {
        offerId: offer.id,
        userOneId: users[0].id,
        userTwoId: users[1].id
      }
    });

    if (!chatSession) {
      chatSession = await prisma.chatSession.create({
        data: {
          offerId: offer.id,
          userOneId: users[0].id,
          userTwoId: users[1].id
        }
      });
    }

    console.log('Using chat session:', chatSession.id);

    // Create transaction in payment stage with timer
    const now = new Date();
    const paymentDue = new Date(now.getTime() + 2 * 60 * 60 * 1000); // 2 hours from now

    const transaction = await prisma.transaction.create({
      data: {
        chatSessionId: chatSession.id,
        currencyAProviderId: users[0].id,
        currencyBProviderId: users[1].id,
        currencyA: 'USD',
        currencyB: 'EUR',
        amountA: 100,
        amountB: 85,
        exchangeRate: 0.85,
        status: 'AWAITING_FIRST_PAYER_PAYMENT',
        agreedTermsUser1At: now,
        agreedTermsUser2At: now,
        agreedFirstPayerId: users[0].id,
        paymentExpectedByPayer1: paymentDue,
        createdAt: now,
        updatedAt: now
      }
    });

    console.log('Created transaction with timer:');
    console.log(`- Transaction ID: ${transaction.id}`);
    console.log(`- Chat Session ID: ${chatSession.id}`);
    console.log(`- Status: ${transaction.status}`);
    console.log(`- First Payer: ${users[0].username} (${users[0].id})`);
    console.log(`- Payment Due: ${paymentDue.toISOString()}`);
    console.log(`- Minutes until due: ${Math.round((paymentDue.getTime() - now.getTime()) / (1000 * 60))}`);
    
    console.log('\n🎯 To test the timer:');
    console.log(`1. Go to: http://localhost:5174/`);
    console.log(`2. Login as: ${users[0].email} (first payer)`);
    console.log(`3. Navigate to chat: ${chatSession.id}`);
    console.log(`4. You should see a countdown timer showing ~2 hours`);

  } catch (error) {
    console.error('Error setting up timer test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setupTimerTest();
