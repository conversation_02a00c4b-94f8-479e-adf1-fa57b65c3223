/**
 * Represents the structure of rate limit information potentially returned by the API.
 * Allows for flexibility in backend naming conventions (e.g., remaining vs remainingAttempts).
 */
export interface RateLimitInfo {
  remainingAttempts?: number | null;
  remaining?: number | null; // Alternative key
  blockedUntil?: number | null;
  blocked_until?: number | null; // Alternative key
  [key: string]: any; // Allow other potential fields
}

/**
 * Structure for successful OTP request/resend responses.
 */
export interface OtpSuccessResponse {
  message: string;
  rateLimitInfo?: RateLimitInfo | null;
}

/**
 * Structure for successful OTP verification responses.
 */
export interface VerifySuccessResponse {
  message: string;
  // Typically no rateLimitInfo needed on final success, but could be added if backend sends it
}

/**
 * Structure for common API error responses.
 * Covers various potential keys for the error message itself.
 */
export interface ApiErrorResponse {
  error?: string;
  message?: string;
  detail?: string;
  rateLimitInfo?: RateLimitInfo | null;
}

/**
 * Represents the structure of the Axios error object, specifically the response part.
 * Useful for typing mock rejections.
 */
export interface MockApiError {
  response: {
    status: number;
    data: ApiErrorResponse;
  };
}

/**
 * Represents a generic successful API response where only a message is expected.
 */
export interface GenericSuccessResponse {
  message: string;
}

/**
 * Tag category structure as returned by the API
 */
export interface TagCategory {
  id: string;
  name: string;
  description: string | null;
  color: string | null;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Tag structure as returned by the API
 */
export interface Tag {
  id: string;
  name: string;
  description: string | null;
  color: string | null;
  icon: string | null;
  categoryId: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  // Populated when fetching with relations
  category?: TagCategory;
  // Analytics data (when requested)
  _count?: {
    reports: number;
  };
}

/**
 * Tag with full relations for frontend display
 */
export interface TagWithRelations extends Tag {
  category: TagCategory;
  reportTypes: string[];
  usageCount: number;
}

/**
 * Request payload for creating a new tag category
 */
export interface CreateTagCategoryRequest {
  name: string;
  description?: string;
  color?: string;
  order?: number;
}

/**
 * Request payload for updating an existing tag category
 */
export interface UpdateTagCategoryRequest {
  name?: string;
  description?: string;
  color?: string;
  order?: number;
  isActive?: boolean;
}

/**
 * Request payload for creating a new tag
 */
export interface CreateTagRequest {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  categoryId: string;
  order?: number;
  reportTypes?: string[];
}

/**
 * Request payload for updating an existing tag
 */
export interface UpdateTagRequest {
  name?: string;
  description?: string;
  color?: string;
  icon?: string;
  categoryId?: string;
  order?: number;
  isActive?: boolean;
  reportTypes?: string[];
}

/**
 * Response structure for tag analytics
 */
export interface TagAnalytics {
  totalTags: number;
  totalCategories: number;
  topTags: Array<{
    id: string;
    name: string;
    count: number;
    category: string;
  }>;
  categoryDistribution: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  reportTypeDistribution: Array<{
    reportType: string;
    count: number;
    percentage: number;
  }>;
  recentActivity: Array<{
    date: string;
    count: number;
  }>;
}

/**
 * Response structure for AI tag suggestions
 */
export interface TagSuggestionResponse {
  suggestions: Array<{
    tagId: string;
    tagName: string;
    confidence: number;
    reason: string;
  }>;
  confidence: number;
}

/**
 * Request payload for getting AI tag suggestions
 */
export interface TagSuggestionRequest {
  title: string;
  description: string;
  reportType: string;
  userAgent?: string;
  url?: string;
}

/**
 * Response structure for fetching tags with filters
 */
export interface TagsResponse {
  tags: TagWithRelations[];
  total: number;
  categories: TagCategory[];
}

/**
 * Query parameters for fetching tags
 */
export interface TagsQuery {
  categoryId?: string;
  reportType?: string;
  isActive?: boolean;
  search?: string;
  limit?: number;
  offset?: number;
  includeAnalytics?: boolean;
}

/**
 * Response structure for tag validation
 */
export interface TagValidationResponse {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// =============================================================================
// AUTOMATIC OFFER MATCHING SYSTEM TYPES
// =============================================================================

/**
 * Match status enum - must match backend MatchStatus
 */
export enum MatchStatus {
  PENDING = 'PENDING',
  BOTH_ACCEPTED = 'BOTH_ACCEPTED',
  PARTIAL_ACCEPT = 'PARTIAL_ACCEPT',
  DECLINED = 'DECLINED',
  EXPIRED = 'EXPIRED',
  CONVERTED = 'CONVERTED',
  CANCELLED = 'CANCELLED'
}

/**
 * Match response enum - must match backend MatchResponse
 */
export enum MatchResponse {
  ACCEPTED = 'ACCEPTED',
  DECLINED = 'DECLINED',
  IGNORED = 'IGNORED'
}

/**
 * Pure API data structure for offer matches - contains only backend data fields
 */
export interface OfferMatchAPI {
  id: string;
  matchId: string; // Human-readable ID like MATCH_20241201_001
  status: MatchStatus;
  compatibilityScore: number; // 0.000-1.000
  
  // Currency and amount info
  currencyA: string;
  currencyB: string;
  amountA: number;
  amountB: number;
  rateAToB: number;
  rateBToA: number;
  
  // User IDs (needed to determine current user's role)
  userAId: string;
  userBId: string;
  
  // Timestamps
  createdAt: string;
  expiresAt: string;
  
  // User responses
  userAResponse?: MatchResponse;
  userARespondedAt?: string;
  userBResponse?: MatchResponse;
  userBRespondedAt?: string;
  
  // User and offer info (optimized structure from API)
  otherUser: {
    id: string;
    username: string;
    email: string;
    reputationLevel: number;
  };
  myOffer: {
    id: string;
    type: 'BUY' | 'SELL';
    currencyPair: string;
    amount: number;
    baseRate: number;
  };
  otherOffer: {
    id: string;
    type: 'BUY' | 'SELL';
    currencyPair: string;
    amount: number;
    baseRate: number;
  };
  
  // Results
  chatSessionId?: string;
  transactionId?: string;
  declineReason?: string;
}

/**
 * UI-enhanced offer match interface - extends API data with frontend helper fields
 */
export interface OfferMatchUI extends OfferMatchAPI {
  // UI helpers (computed by frontend)
  isExpired?: boolean;
  timeRemaining?: number; // seconds until expiration
  isCurrentUserInvolved?: boolean;
  currentUserResponse?: MatchResponse;
  otherUserResponse?: MatchResponse;
  canCurrentUserRespond?: boolean;
}

/**
 * @deprecated Use OfferMatchAPI for API operations and OfferMatchUI for UI components
 * This alias maintains backward compatibility during migration
 */
export type OfferMatch = OfferMatchUI;

/**
 * Response structure for fetching user matches
 */
export interface MatchesResponse {
  success: boolean;
  matches: OfferMatch[];
  total: number;
}

/**
 * Response structure for match actions (accept/decline)
 */
export interface MatchActionResponse {
  success: boolean;
  message: string;
  match: OfferMatch;
  chatSessionId?: string;
  transactionId?: string;
}

/**
 * Query parameters for fetching matches
 */
export interface MatchesQuery {
  status?: MatchStatus;
  limit?: number;
  offset?: number;
}

/**
 * Request body for declining a match
 */
export interface DeclineMatchRequest {
  reason?: string;
}
