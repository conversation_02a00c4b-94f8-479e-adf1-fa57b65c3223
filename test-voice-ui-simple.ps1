Write-Host "Voice Debug Report UI Integration Test" -ForegroundColor Cyan
Write-Host ("=" * 50) -ForegroundColor Gray

function Test-ServiceHealth($name, $url) {
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "$name is running on $url" -ForegroundColor Green
            return $true
        } else {
            Write-Host "$name is not running (unexpected status code)" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "$name is not running" -ForegroundColor Yellow
        return $false
    }
}

$frontendRunning = Test-ServiceHealth "Frontend" "http://localhost:5174"
$backendRunning = Test-ServiceHealth "Backend" "http://localhost:3000/health"

Write-Host ""
Write-Host "UI Integration Test Instructions:" -ForegroundColor Cyan
Write-Host "1. Navigate to http://localhost:5174" -ForegroundColor White
Write-Host "2. Login or register an account" -ForegroundColor White
Write-Host "3. Click the Debug Report button (bug icon)" -ForegroundColor White
Write-Host "4. In the modal, click the Voice Recording tab" -ForegroundColor White
Write-Host "5. Grant microphone permissions when prompted" -ForegroundColor White
Write-Host "6. Record a voice message describing a bug" -ForegroundColor White
Write-Host "7. Check that the UI updates with the generated report" -ForegroundColor White

Write-Host ""
Write-Host "Expected Behavior:" -ForegroundColor Cyan
Write-Host "- Recording indicator appears during recording" -ForegroundColor White
Write-Host "- Processing spinner shows after recording stops" -ForegroundColor White
Write-Host "- Debug report form auto-fills with AI-generated content" -ForegroundColor White
Write-Host "- Direct audio processing (no transcription step)" -ForegroundColor White

if ($frontendRunning -and $backendRunning) {
    Write-Host ""
    Write-Host "Both services are running - Ready for testing!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "Services need to be started:" -ForegroundColor Red
    if (-not $backendRunning) {
        Write-Host "  Start backend: cd backend; npm run dev" -ForegroundColor White
    }
    if (-not $frontendRunning) {
        Write-Host "  Start frontend: cd frontend; npm run dev" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "Test complete" -ForegroundColor Cyan
