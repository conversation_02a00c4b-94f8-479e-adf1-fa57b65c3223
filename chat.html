<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>P2P Swap UI Mockup</title>
    <style>
        /* --- Basic Setup & Fonts --- */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        :root {
            --primary-blue: #007AFF;
            --success-green: #34C759;
            --warning-orange: #FF9500;
            --light-grey: #F2F2F7;
            --medium-grey: #E5E5EA;
            --dark-grey: #8E8E93;
            --text-primary: #1C1C1E;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background-color: #dcdcdc;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            color: var(--text-primary);
        }

        /* --- The Phone Mockup Container --- */
        .mobile-container {
            width: 375px;
            height: 812px;
            background-color: #ffffff;
            border-radius: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* --- [1] Header Bar (Fixed) --- */
        .header-bar {
            background-color: var(--light-grey);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            flex-shrink: 0;
            border-bottom: 1px solid var(--medium-grey);
        }
        .header-bar .back-arrow { font-size: 22px; font-weight: 500; }
        .header-bar .profile-pic { width: 36px; height: 36px; background-color: var(--primary-blue); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; }
        .header-bar .user-info { flex-grow: 1; }
        .header-bar .user-name { font-weight: 600; font-size: 16px; }
        .header-bar .user-stats { font-size: 12px; color: var(--dark-grey); }
        .header-bar .more-options { font-size: 22px; font-weight: 600; }
        
        /* --- [2] Smart Status Bar (Fixed) --- */
        .smart-status-bar {
            background-color: #ffffff;
            padding: 12px 16px;
            flex-shrink: 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            z-index: 10;
        }
        .progress-segments { display: flex; gap: 4px; }
        .progress-step {
            height: 6px;
            flex-grow: 1;
            border-radius: 3px;
        }
        .progress-step.completed { background-color: var(--success-green); }
        .progress-step.active {
            background-color: var(--primary-blue);
            animation: pulse 2s infinite;
        }
        .progress-step.upcoming { border: 1px solid var(--medium-grey); }
        .status-label {
            text-align: center;
            font-size: 13px;
            font-weight: 500;
            margin-top: 8px;
        }
        #timer { color: var(--dark-grey); font-weight: 400; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        /* --- [3] Unified Feed (Scrollable) --- */
        .unified-feed {
            flex-grow: 1;
            padding: 16px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        .chat-bubble {
            padding: 10px 14px;
            border-radius: 18px;
            max-width: 75%;
            font-size: 15px;
            line-height: 1.4;
        }
        .chat-bubble.them {
            background-color: var(--light-grey);
            align-self: flex-start;
            border-bottom-left-radius: 4px;
        }
        .system-message {
            align-self: center;
            text-align: center;
            font-size: 12px;
            color: var(--dark-grey);
            background-color: #fafafa;
            padding: 4px 12px;
            border-radius: 12px;
        }
        .action-card {
            background-color: #ffffff;
            border: 1px solid var(--medium-grey);
            border-radius: 14px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .action-card .title { font-size: 18px; font-weight: 700; margin-bottom: 8px; }
        .action-card .warning {
            font-size: 13px;
            color: var(--warning-orange);
            background-color: #fff9f0;
            padding: 8px;
            border-radius: 8px;
            margin-bottom: 12px;
            display: flex; align-items: center; gap: 8px;
        }
        .action-card .info-section {
            font-size: 14px;
            border-top: 1px solid var(--light-grey);
            padding-top: 12px;
        }
        .action-card .info-section .label { font-weight: 600; margin-bottom: 4px; }
        .action-card .info-section .value { color: var(--dark-grey); }

        /* --- [4] Dynamic Action Bar (Fixed) --- */
        .dynamic-action-bar {
            background-color: var(--light-grey);
            padding: 8px 16px;
            border-top: 1px solid var(--medium-grey);
            display: flex;
            gap: 8px;
            align-items: center;
        }
        #action-mode, #chat-mode { display: flex; width: 100%; align-items: center; gap: 8px;}
        #chat-icon-btn {
            height: 44px;
            width: 44px;
            border: 1px solid var(--medium-grey);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            cursor: pointer;
            flex-shrink: 0;
            background-color: white;
        }
        #cta-button {
            height: 44px;
            flex-grow: 1;
            border-radius: 22px;
            background-color: var(--primary-blue);
            color: white;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
        }
        #chat-input {
            height: 44px;
            flex-grow: 1;
            border-radius: 22px;
            border: 1px solid var(--medium-grey);
            padding: 0 16px;
            font-size: 15px;
        }
        #send-btn {
            height: 44px;
            width: 60px;
            border-radius: 22px;
            background-color: var(--primary-blue);
            color: white;
            font-weight: 600;
            border: none;
            cursor: pointer;
            flex-shrink: 0;
        }

        /* Logic to hide/show modes */
        .dynamic-action-bar:not(.chat-active) #chat-mode {
            display: none;
        }
        .dynamic-action-bar.chat-active #action-mode {
            display: none;
        }
    </style>
</head>
<body>

    <div class="mobile-container">

        <!-- [1] Header Bar -->
        <div class="header-bar">
            <div class="back-arrow"><</div>
            <div class="profile-pic">AJ</div>
            <div class="user-info">
                <div class="user-name">Alex J.</div>
                <div class="user-stats">⭐️ 4.7 | 82 Trades</div>
            </div>
            <div class="more-options">…</div>
        </div>

        <!-- [2] Smart Status Bar -->
        <div class="smart-status-bar">
            <div class="progress-segments">
                <div class="progress-step completed"></div>
                <div class="progress-step completed"></div>
                <div class="progress-step completed"></div>
                <div class="progress-step active"></div>
                <div class="progress-step upcoming"></div>
                <div class="progress-step upcoming"></div>
                <div class="progress-step upcoming"></div>
            </div>
            <div class="status-label">
                Your Action: Confirm Receipt | <span id="timer">01:48:32</span>
            </div>
        </div>

        <!-- [3] Unified Feed -->
        <div class="unified-feed" id="feed">
            <div class="chat-bubble them">
                Hey, I just sent it. Should be there soon!
            </div>
            <div class="system-message">
                ➡️ Alex has marked the 85 EUR as sent. (10:45 AM)
            </div>
            <div class="action-card">
                <div class="title">Confirm You Have Received 85 EUR</div>
                <div class="warning">
                    <span>⚠️</span>
                    <span>Please check your bank account directly. Only confirm if you have received the full amount as this action is final.</span>
                </div>
                <div class="info-section">
                    <div class="label">Check Your Account:</div>
                    <div class="value">My First Bank, Account **** 1234</div>
                </div>
            </div>
        </div>

        <!-- [4] Dynamic Action Bar -->
        <div class="dynamic-action-bar" id="actionBar">
            <!-- State 1: Action Mode (Default) -->
            <div id="action-mode">
                <button id="chat-icon-btn">💬</button>
                <button id="cta-button">Confirm Payment Received</button>
            </div>
            <!-- State 2: Chat Mode (Hidden by default) -->
            <div id="chat-mode">
                <input type="text" id="chat-input" placeholder="Type a message...">
                <button id="send-btn">Send</button>
            </div>
        </div>
    </div>

    <script>
        // --- Timer Logic ---
        const timerDisplay = document.getElementById('timer');
        let durationInSeconds = 1 * 60 * 60 + 48 * 60 + 32; // 1h 48m 32s

        const timerInterval = setInterval(() => {
            if (durationInSeconds <= 0) {
                clearInterval(timerInterval);
                timerDisplay.textContent = 'Time Expired';
                return;
            }
            durationInSeconds--;

            const hours = Math.floor(durationInSeconds / 3600);
            const minutes = Math.floor((durationInSeconds % 3600) / 60);
            const seconds = durationInSeconds % 60;

            timerDisplay.textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);

        // --- Action Bar Logic ---
        const actionBar = document.getElementById('actionBar');
        const chatIconBtn = document.getElementById('chat-icon-btn');
        const sendBtn = document.getElementById('send-btn');
        const chatInput = document.getElementById('chat-input');
        
        function switchToChatMode() {
            actionBar.classList.add('chat-active');
            chatInput.focus(); // Automatically focus the input field
        }

        function switchToActionMode() {
            actionBar.classList.remove('chat-active');
            chatInput.value = ''; // Clear the input field
        }

        chatIconBtn.addEventListener('click', switchToChatMode);
        sendBtn.addEventListener('click', switchToActionMode);
        // Bonus: allow pressing Enter to send
        chatInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                switchToActionMode();
            }
        });
        
        // --- Scroll feed to bottom on load ---
        const feed = document.getElementById('feed');
        feed.scrollTop = feed.scrollHeight;

    </script>
</body>
</html>