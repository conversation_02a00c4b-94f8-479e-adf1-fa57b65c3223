import { z } from 'zod';
import { LogEntrySchema, type LogEntry } from './debugSchemas';

/**
 * Zod schemas for admin debug dashboard API
 */

// Query parameters for getAllReports
export const GetReportsQuerySchema = z.object({
  page: z.coerce.number().int().min(1).optional().default(1),
  limit: z.coerce.number().int().min(1).max(100).optional().default(10),
  sortBy: z.enum(['serverReceivedAt', 'clientTimestamp', 'reportType', 'reportSeverity', 'reportTitle', 'logCount']).optional().default('serverReceivedAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
  filterByType: z.enum(['bug', 'feature-request', 'performance', 'ui-ux', 'improvement', 'question', 'other']).optional(),
  filterBySeverity: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  filterByStatus: z.enum(['new', 'todo', 'done']).optional(),
  filterByDateStart: z.string().datetime().optional(),
  filterByDateEnd: z.string().datetime().optional(),
  searchQuery: z.string().max(200).optional(),
  source: z.enum(['client', 'debug']).optional(), // Optional parameter to choose data source
});

// Schema for parsed report response
export const ParsedReportSchema = z.object({
  reportId: z.string(),
  timestamp: z.string(),
  serverReceivedAt: z.string(),
  clientTimestamp: z.string(),
  sessionId: z.string(),
  userAgent: z.string().optional(),
  currentUrl: z.string().optional(),
  userNotes: z.string().nullable().optional(),
  reportType: z.string().optional(),
  reportSeverity: z.string().optional(),
  reportTitle: z.string().optional(),
  reportDescription: z.string().optional(),
  stepsToReproduce: z.string().optional(),
  expectedBehavior: z.string().optional(),
  actualBehavior: z.string().optional(),
  status: z.string().optional(),  tags: z.array(z.string()).optional(),
  hasTags: z.boolean().optional(),  logCount: z.number(),
  logs: z.array(LogEntrySchema),
  // User identification fields with comprehensive validation and PII protection
  // Security measures: email normalization, character restrictions, length limits, pattern validation
  userId: z.string()
    .min(1)
    .max(100)
    .regex(/^[a-zA-Z0-9_-]+$/, 'User ID must contain only alphanumeric characters, underscores, and hyphens')
    .refine(val => !val.includes('..'), 'User ID cannot contain consecutive dots')
    .optional(),
  userEmail: z.string()
    .email('Invalid email format')
    .max(255)
    .regex(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Email must follow standard format')
    .transform(val => val?.toLowerCase()) // Normalize email case for consistency
    .refine(val => val && !val.includes('..'), 'Email cannot contain consecutive dots')
    .optional(),
  username: z.string()
    .min(1)
    .max(100)
    .regex(/^[a-zA-Z0-9_.-]+$/, 'Username must contain only alphanumeric characters, underscores, periods, and hyphens')
    .transform(val => val?.trim()) // Remove potential whitespace
    .refine(val => !val.startsWith('.') && !val.endsWith('.'), 'Username cannot start or end with a dot')
    .refine(val => !val.includes('..'), 'Username cannot contain consecutive dots')
    .optional(),
});

// Schema for paginated reports response
export const GetReportsResponseSchema = z.object({
  reports: z.array(ParsedReportSchema),
  total: z.number(),
  totalPages: z.number(),
  currentPage: z.number(),
});

// Schema for single report response
export const GetReportByIdResponseSchema = z.object({
  success: z.boolean(),
  data: ParsedReportSchema.nullable(),
  message: z.string().optional(),
});

// Schema for reports list response
export const GetAllReportsResponseSchema = z.object({
  success: z.boolean(),
  data: GetReportsResponseSchema.optional(),
  message: z.string().optional(),
});

// Type exports
export type { LogEntry };
export type GetReportsQuery = z.infer<typeof GetReportsQuerySchema>;
export type ParsedReport = z.infer<typeof ParsedReportSchema>;
export type GetReportsResponse = z.infer<typeof GetReportsResponseSchema>;
export type GetReportByIdResponse = z.infer<typeof GetReportByIdResponseSchema>;
export type GetAllReportsResponse = z.infer<typeof GetAllReportsResponseSchema>;
