<template>
  <div class="voice-recorder">
    <!-- Recording Controls -->
    <div class="recording-controls">
      <!-- Record Button -->
      <n-button
        v-if="!isRecording && !hasRecording"
        type="primary"
        size="large"
        circle
        :disabled="!isSupported || aiAnalysisStore.isProcessing"
        @click="handleStartRecording"
        class="record-button"
      >
        <template #icon>
          <n-icon size="24">
            <MicrophoneIcon />
          </n-icon>
        </template>
      </n-button>

      <!-- Stop Button -->
      <n-button
        v-if="isRecording"
        type="error"
        size="large"
        circle
        @click="handleStopRecording"
        class="stop-button"
      >
        <template #icon>
          <n-icon size="24">
            <StopIcon />
          </n-icon>
        </template>
      </n-button>

      <!-- Pause/Resume Button -->
      <n-button
        v-if="isRecording"
        type="warning"
        size="medium"
        circle
        @click="handlePauseResume"
        class="pause-resume-button"
      >
        <template #icon>
          <n-icon size="20">
            <PauseIcon v-if="!isPaused" />
            <PlayIcon v-else />
          </n-icon>
        </template>
      </n-button>
    </div>

    <!-- Recording Status -->
    <div v-if="isRecording || hasRecording" class="recording-status">
      <!-- Duration Display -->
      <div class="duration-display">
        <n-icon class="duration-icon" :class="{ 'recording-pulse': isRecording && !isPaused }">
          <MicrophoneIcon />
        </n-icon>
        <span class="duration-text">{{ formattedDuration }}</span>
        <span v-if="isPaused" class="paused-indicator">{{ $t('voice.paused') }}</span>
      </div>

      <!-- Progress Bar -->
      <n-progress
        type="line"
        :percentage="Math.min((duration / maxDuration) * 100, 100)"
        :color="getProgressColor()"
        :show-indicator="false"
        class="duration-progress"
      />

      <!-- Max Duration Warning -->
      <div v-if="duration > maxDuration * 0.8" class="duration-warning">
        <n-icon size="16" color="#f0a020">
          <WarningIcon />
        </n-icon>
        <span>{{ $t('voice.nearMaxDuration', { remaining: remainingTime }) }}</span>
      </div>
    </div>

    <!-- Playback Controls -->
    <div v-if="hasRecording && !isRecording" class="playback-controls">
      <n-button
        size="small"
        @click="playRecording"
        :disabled="!audioUrl"
      >
        <template #icon>
          <n-icon>
            <PlayIcon />
          </n-icon>
        </template>
        {{ $t('voice.playback') }}
      </n-button>

      <!-- Resend Button - Show when there's an error or processing failed -->
      <n-button
        v-if="showResendButton"
        size="small"
        type="primary"
        ghost
        @click="handleResendRecording"
        :loading="aiAnalysisStore.isProcessing"
        :disabled="!audioUrl"
      >
        <template #icon>
          <n-icon>
            <RefreshIcon />
          </n-icon>
        </template>
        {{ $t('voice.resend') }}
      </n-button>

      <n-button
        size="small"
        type="error"
        ghost
        @click="handleCancelRecording"
      >
        <template #icon>
          <n-icon>
            <DeleteIcon />
          </n-icon>
        </template>
        {{ $t('voice.delete') }}
      </n-button>
    </div>

    <!-- Processing Status -->
    <div v-if="aiAnalysisStore.isProcessing" class="processing-status">
      <n-spin size="small" />
      <span class="processing-text">
        <span v-if="aiAnalysisStore.isTranscribing">{{ $t('voice.transcribing') }}</span>
        <span v-else-if="aiAnalysisStore.isAnalyzing">{{ $t('voice.analyzing') }}</span>
        <span v-else>{{ $t('voice.processing') }}</span>
      </span>
    </div>

    <!-- Error Display - Only show for generic errors, not analysis failures -->
    <div v-if="error" class="error-display">
      <n-alert type="error" :show-icon="false" closable @close="clearError">
        {{ error }}
      </n-alert>
    </div>

    <!-- Not Supported Warning -->
    <div v-if="!isSupported" class="not-supported-warning">
      <n-alert type="warning" :show-icon="true">
        {{ $t('voice.notSupported') }}
      </n-alert>
    </div>

    <!-- Hidden Audio Element for Playback -->
    <audio
      ref="audioElement"
      :src="audioUrl || ''"
      @ended="onPlaybackEnded"
      style="display: none;"
    />
    
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useMessage } from 'naive-ui';
import { useVoiceRecording } from '@/composables/useVoiceRecording';
import { useAiAnalysisStore } from '@/stores/aiAnalysisStore';
import { useThemeStore } from '@/stores/theme';
import {
  Microphone as MicrophoneIcon,
  PlayerStop as StopIcon,
  PlayerPause as PauseIcon,
  PlayerPlay as PlayIcon,
  AlertTriangle as WarningIcon,
  Trash as DeleteIcon,
  Refresh as RefreshIcon,
} from '@vicons/tabler';

// Props
interface Props {
  language?: string;
  autoProcess?: boolean;
  predefinedTags?: Record<string, string[]>;
}

const props = withDefaults(defineProps<Props>(), {
  language: 'en',
  autoProcess: true,
  predefinedTags: () => ({}),
});

// Emits
interface Emits {
  (e: 'recording-started'): void;
  (e: 'recording-stopped', audioData: { base64: string; mimeType: string; duration: number }): void;
  (e: 'recording-cancelled'): void;
  (e: 'transcription-complete', transcription: string): void;
  (e: 'analysis-complete', report: any): void;
  (e: 'processing-error', error: string | {
    type: string;
    reason?: string;
    message: string;
    suggestions?: string[];
    transcription?: string;
  }): void;
}

const emit = defineEmits<Emits>();

const { t } = useI18n();
const message = useMessage();
const themeStore = useThemeStore();

// Composables
const {
  isRecording,
  isPaused,
  isSupported,
  duration,
  formattedDuration,
  audioUrl,
  hasRecording,
  startRecording,
  stopRecording,
  pauseRecording,
  resumeRecording,
  cancelRecording,
  getAudioAsBase64,
  getAudioMetadata,
  maxDuration,
} = useVoiceRecording();

const aiAnalysisStore = useAiAnalysisStore();

// Local state
const audioElement = ref<HTMLAudioElement | null>(null);
const error = ref<string>('');
const hasProcessingError = ref<boolean>(false);
const lastRecordingData = ref<{ base64: string; mimeType: string; duration: number } | null>(null);
const analysisFailure = ref<any>(null);

// Computed properties
const remainingTime = computed(() => Math.ceil(maxDuration - duration.value));

const showResendButton = computed(() => {
  return hasRecording.value && (
    hasProcessingError.value || 
    error.value || 
    aiAnalysisStore.error || 
    aiAnalysisStore.hasAnalysisFailure
  );
});

// Methods
const handleStartRecording = async () => {
  const success = await startRecording();
  if (success) {
    emit('recording-started');
    clearError();
    aiAnalysisStore.clearState();
  }
};

const handleStopRecording = async () => {
  stopRecording();
  
  // Wait a bit for the recording to be processed
  setTimeout(async () => {
    const audioData = await getAudioAsBase64();
    const metadata = getAudioMetadata();
    
    if (audioData && metadata) {
      const recordingData = {
        base64: audioData,
        mimeType: metadata.mimeType,
        duration: metadata.duration,
      };
      
      // Store the recording data for potential resend
      lastRecordingData.value = recordingData;
      
      emit('recording-stopped', recordingData);

      // Auto-process if enabled
      if (props.autoProcess) {
        await processRecording(audioData, metadata.mimeType, metadata.duration);
      }
    }
  }, 100);
};

const handlePauseResume = () => {
  if (isPaused.value) {
    resumeRecording();
  } else {
    pauseRecording();
  }
};

const handleCancelRecording = () => {
  cancelRecording();
  clearError();
  clearProcessingState();
  aiAnalysisStore.clearState();
  emit('recording-cancelled');
};

const handleResendRecording = async () => {
  if (!lastRecordingData.value) {
    message.error(t('voice.noRecordingToResend'));
    return;
  }
  
  clearError();
  clearProcessingState();
  aiAnalysisStore.clearState();
  
  console.log('[VoiceRecorder] Resending recording for processing...');
  
  const { base64, mimeType, duration } = lastRecordingData.value;
  await processRecording(base64, mimeType, duration);
};

const processRecording = async (audioBase64: string, mimeType: string, duration: number) => {
  try {
    clearProcessingState(); // Clear any previous error states
    console.log('[VoiceRecorder] Starting voice processing...', {
      mimeType,
      duration,
      dataSize: audioBase64.length,
      timestamp: new Date().toISOString()
    });
    const success = await aiAnalysisStore.processVoiceToReport(audioBase64, mimeType, duration, props.language, props.predefinedTags);
    
    console.log('[VoiceRecorder] Processing result:', success);
    
    // Always emit transcription if available
    if (aiAnalysisStore.transcription) {
      console.log('[VoiceRecorder] Emitting transcription-complete event');
      emit('transcription-complete', aiAnalysisStore.transcription);
    }
    
    // Handle analysis failure cases
    if (aiAnalysisStore.hasAnalysisFailure && aiAnalysisStore.analysisFailure) {
      const failure = aiAnalysisStore.analysisFailure;
      console.log('[VoiceRecorder] Analysis failed:', failure.reason);
      hasProcessingError.value = true;
      analysisFailure.value = failure;
      error.value = ''; // Clear generic error to prevent double modal
      
      // Emit error event for parent to handle
      emit('processing-error', {
        type: 'ANALYSIS_FAILED',
        reason: failure.reason,
        message: failure.userMessageKey ? t(failure.userMessageKey) : t('voiceAnalysis.errors.generic'),
        suggestions: (failure.suggestionKeys || []).map(key => t(key)),
        transcription: aiAnalysisStore.transcription
      });
      
      return;
    }
    
    // Handle successful analysis
    if (success && aiAnalysisStore.generatedReport) {
      console.log('[VoiceRecorder] Emitting analysis-complete event');
      clearProcessingState(); // Clear error state on success
      emit('analysis-complete', aiAnalysisStore.generatedReport);
    } else {
      console.warn('[VoiceRecorder] Processing was not successful or no report generated');
      if (aiAnalysisStore.error) {
        error.value = aiAnalysisStore.error; // Set error for generic error display
        hasProcessingError.value = true;
        emit('processing-error', {
          type: 'PROCESSING_ERROR',
          message: aiAnalysisStore.error
        });
      }
    }
  } catch (err: any) {
    console.error('[VoiceRecorder] Processing error:', err);
    error.value = err.message || t('voice.processingError');
    hasProcessingError.value = true;
    emit('processing-error', {
      type: 'EXCEPTION',
      message: error.value
    });
  }
};

const playRecording = () => {
  if (audioElement.value && audioUrl.value) {
    audioElement.value.play().catch((err) => {
      console.error('Failed to play audio:', err);
      message.error(t('voice.playbackError'));
    });
  }
};

const onPlaybackEnded = () => {
  // Playback ended - could emit an event if needed
};

const clearError = () => {
  error.value = '';
};

const clearProcessingState = () => {
  hasProcessingError.value = false;
  error.value = '';
};

const getProgressColor = () => {
  const percentage = (duration.value / maxDuration) * 100;
  if (percentage > 90) return themeStore.isDark ? '#f87171' : '#ef4444'; // Red
  if (percentage > 80) return themeStore.isDark ? '#fbbf24' : '#f59e0b'; // Orange
  return themeStore.isDark ? '#34d399' : '#10b981'; // Green
};

// Watch for AI errors
watch(() => aiAnalysisStore.error, (newError) => {
  if (newError) {
    error.value = newError;
  }
});

// Expose methods for parent component
defineExpose({
  startRecording: handleStartRecording,
  stopRecording: handleStopRecording,
  cancelRecording: handleCancelRecording,
  resendRecording: handleResendRecording,
  processRecording,
  clearError,
  clearProcessingState,
});
</script>

<style scoped>
.voice-recorder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: transparent;
  border-radius: var(--n-border-radius);
  transition: all 0.3s ease;
}

.recording-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.record-button {
  width: 64px;
  height: 64px;
}

.stop-button {
  width: 64px;
  height: 64px;
}

.pause-resume-button {
  width: 48px;
  height: 48px;
}

.recording-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  max-width: 300px;
}

.duration-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--n-text-color);
}

.duration-icon {
  color: var(--n-error-color, #ef4444);
}

.recording-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.paused-indicator {
  font-size: 0.9rem;
  color: var(--n-warning-color, #f59e0b);
  font-style: italic;
}

.duration-progress {
  width: 100%;
}

.duration-warning {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--n-warning-color, #f59e0b);
}

.playback-controls {
  display: flex;
  gap: 0.5rem;
}

.processing-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--n-text-color-2, #6b7280);
  font-size: 0.9rem;
}

.processing-text {
  color: var(--n-text-color-2);
}

.error-display {
  width: 100%;
  max-width: 400px;
}

.not-supported-warning {
  width: 100%;
  max-width: 400px;
}

.duration-text {
  color: var(--n-text-color);
  font-weight: 600;
}

/* Light theme specific styles */
[data-theme="light"] .duration-icon {
  color: #ef4444;
}

[data-theme="light"] .paused-indicator {
  color: #f59e0b;
}

[data-theme="light"] .duration-warning {
  color: #f59e0b;
}

[data-theme="light"] .processing-status {
  color: #6b7280;
}

/* Dark theme specific styles */
[data-theme="dark"] .duration-icon {
  color: #f87171;
}

[data-theme="dark"] .paused-indicator {
  color: #fbbf24;
}

[data-theme="dark"] .duration-warning {
  color: #fbbf24;
}

[data-theme="dark"] .processing-status {
  color: var(--n-text-color-3);
}

.voice-analysis-failure-modal {
  .modal-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .modal-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--n-text-color);
  }

  .failure-content {
    padding: 1rem 0;

    .failure-message {
      font-size: 1rem;
      color: var(--error-color);
      margin-bottom: 1rem;
    }

    .suggestions {
      margin-bottom: 1rem;

      h4 {
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--n-text-color);
        margin-bottom: 0.5rem;
      }

      ul {
        list-style-type: disc;
        padding-left: 1.5rem;

        li {
          font-size: 0.9rem;
          color: var(--n-text-color);
        }
      }
    }

    .transcription {
      font-size: 0.9rem;
      color: var(--n-text-color);
      background-color: var(--n-bg-color-2);
      padding: 0.5rem;
      border-radius: var(--n-border-radius);
    }
  }
}
</style>
