import { test, expect } from '@playwright/test'

/**
 * E2E Tests for SmartNegotiationSection Component
 * 
 * Tests the smart negotiation functionality in your P2P currency exchange platform.
 * Covers mobile-first UI, real-time updates, and user interactions.
 */

test.describe('Smart Negotiation Section', () => {
  
  // Mobile-first testing setup
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport (your primary target)
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Navigate to negotiation page (adjust URL as needed)
    await page.goto('/transaction/123/negotiation')
    
    // Wait for initial load and skeleton screens
    await expect(page.locator('[data-testid="smart-negotiation-section"]')).toBeVisible()
  })

  test('should display loading state initially', async ({ page }) => {
    // Test skeleton loading state
    await expect(page.locator('[data-testid="content-skeleton"]')).toBeVisible()
    
    // Wait for content to load
    await expect(page.locator('[data-testid="content-loaded"]')).toBeVisible({ timeout: 10000 })
  })

  test('should show system recommendation banner for recommended user', async ({ page }) => {
    // Wait for negotiation data to load
    await page.waitForSelector('[data-testid="smart-negotiation-section"]')
    
    // Check if AI recommendation banner is visible
    const banner = page.locator('.recommendation-banner')
    await expect(banner).toBeVisible()
    
    // Verify recommendation content
    await expect(banner.locator('.recommendation-icon')).toContainText('🤖')
    await expect(banner.locator('.recommendation-title')).toBeVisible()
    await expect(banner.locator('.recommendation-summary')).toBeVisible()
    
    // Check mobile-friendly touch targets (minimum 44px)
    const badgeIcon = banner.locator('.recommendation-badge')
    const boundingBox = await badgeIcon.boundingBox()
    expect(boundingBox?.width).toBeGreaterThanOrEqual(44)
    expect(boundingBox?.height).toBeGreaterThanOrEqual(44)
  })

  test('should display transaction summary with correct currency formatting', async ({ page }) => {
    // Wait for transaction summary
    const transactionSummary = page.locator('.transaction-summary')
    await expect(transactionSummary).toBeVisible()
    
    // Check currency formatting (should show IRR and CAD amounts)
    const sendAmount = transactionSummary.locator('.amount-item.send .amount-text')
    const receiveAmount = transactionSummary.locator('.amount-item.receive .amount-text')
    
    await expect(sendAmount.first()).toBeVisible()
    await expect(receiveAmount.first()).toBeVisible()
    
    // Verify currency symbols/codes are present
    const sendText = await sendAmount.first().textContent()
    const receiveText = await receiveAmount.first().textContent()
    
    expect(sendText).toMatch(/IRR|CAD|\$|ریال/)
    expect(receiveText).toMatch(/IRR|CAD|\$|ریال/)
  })

  test('should handle accept recommendation action (recommended user)', async ({ page }) => {
    // Wait for actions to be available
    const acceptButton = page.locator('[data-testid="accept-recommendation-btn"]')
    await expect(acceptButton).toBeVisible()
    
    // Verify button is properly sized for mobile (44px minimum)
    const buttonBox = await acceptButton.boundingBox()
    expect(buttonBox?.height).toBeGreaterThanOrEqual(44)
    
    // Test button interaction
    await acceptButton.click()
    
    // Should show loading state
    await expect(acceptButton.locator('.button-spinner')).toBeVisible()
    
    // Should show success message (adjust selector based on your notification system)
    await expect(page.locator('.n-message--success, .notification--success')).toBeVisible({ timeout: 5000 })
  })

  test('should handle counter proposal with custom message', async ({ page }) => {
    // Click propose counter button
    const counterButton = page.locator('[data-testid="propose-counter-btn"]')
    await expect(counterButton).toBeVisible()
    await counterButton.click()
    
    // Message input should appear
    const messageInput = page.locator('[data-testid="custom-message-input"]')
    await expect(messageInput).toBeVisible()
    
    // Type custom message
    const testMessage = 'I prefer to pay first due to my higher reputation score.'
    await messageInput.fill(testMessage)
    
    // Check character count
    const charCount = page.locator('.character-count')
    await expect(charCount).toContainText(`${testMessage.length}/300`)
    
    // Submit counter proposal
    const submitButton = page.locator('.submit-counter-btn')
    await submitButton.click()
    
    // Should show loading state
    await expect(submitButton.locator('.button-spinner')).toBeVisible()
    
    // Should show success message
    await expect(page.locator('.n-message--success, .notification--success')).toBeVisible({ timeout: 5000 })
    
    // Message input should be hidden and cleared
    await expect(messageInput).not.toBeVisible()
  })

  test('should be responsive on different screen sizes', async ({ page }) => {
    // Test mobile layout (375px)
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('.action-buttons')).toHaveCSS('grid-template-columns', '1fr')
    
    // Test tablet layout (768px)
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(page.locator('.transaction-flow')).toBeVisible()
    
    // Test desktop layout (1280px)
    await page.setViewportSize({ width: 1280, height: 720 })
    await expect(page.locator('.action-buttons')).toHaveCSS('grid-template-columns', '1fr 1fr')
  })

  test('should support RTL layout for Persian language', async ({ page }) => {
    // Set RTL direction
    await page.evaluate(() => {
      document.documentElement.setAttribute('dir', 'rtl')
    })
    
    // Set Persian locale (if your app supports language switching)
    await page.evaluate(() => {
      localStorage.setItem('locale', 'fa')
    })
    
    // Reload to apply RTL styles
    await page.reload()
    
    // Check RTL-specific styles
    const recommendationContent = page.locator('.recommendation-content')
    await expect(recommendationContent).toHaveCSS('flex-direction', 'row-reverse')
  })

  test('should handle real-time negotiation updates via Socket.IO', async ({ page }) => {
    // Listen for Socket.IO events (you'll need to expose this in your app for testing)
    await page.evaluate(() => {
      // Mock Socket.IO update
      window.dispatchEvent(new CustomEvent('negotiation-updated', {
        detail: {
          transactionId: '123',
          status: 'counter-proposed',
          systemRecommendationReason: 'Updated recommendation based on new data'
        }
      }))
    })
    
    // Wait for UI to update
    await page.waitForTimeout(1000)
    
    // Verify that the UI reflects the real-time update
    const reasonText = page.locator('.reason-text')
    await expect(reasonText).toContainText('Updated recommendation')
  })

  test('should handle network errors gracefully', async ({ page }) => {
    // Simulate network failure
    await page.route('**/api/negotiations/**', route => {
      route.abort('failed')
    })
    
    // Try to accept recommendation
    const acceptButton = page.locator('[data-testid="accept-recommendation-btn"]')
    await acceptButton.click()
    
    // Should show error message
    await expect(page.locator('.n-message--error, .notification--error')).toBeVisible({ timeout: 5000 })
    
    // Button should not be stuck in loading state
    await expect(acceptButton.locator('.button-spinner')).not.toBeVisible()
  })

  test('should meet accessibility standards', async ({ page }) => {
    // Check for ARIA labels and roles
    const negotiationSection = page.locator('[data-testid="smart-negotiation-section"]')
    
    // All interactive elements should be keyboard accessible
    const acceptButton = page.locator('[data-testid="accept-recommendation-btn"]')
    await acceptButton.focus()
    await expect(acceptButton).toBeFocused()
    
    // Should be operable with keyboard
    await page.keyboard.press('Enter')
    
    // Check color contrast (you might need additional accessibility testing tools)
    const primaryButton = page.locator('.action-button.primary')
    const backgroundColor = await primaryButton.evaluate(el => 
      getComputedStyle(el).backgroundColor
    )
    expect(backgroundColor).toBeTruthy()
    
    // Verify text is readable
    await expect(primaryButton).toHaveCSS('color', 'rgb(255, 255, 255)')
  })

  test('should handle concurrent user interactions', async ({ page }) => {
    // Simulate rapid button clicks
    const acceptButton = page.locator('[data-testid="accept-recommendation-btn"]')
    
    // First click should work
    await acceptButton.click()
    
    // Button should be disabled during processing
    await expect(acceptButton).toBeDisabled()
    
    // Additional clicks should be ignored
    await acceptButton.click({ force: true })
    await acceptButton.click({ force: true })
    
    // Should only process once
    await expect(page.locator('.button-spinner')).toHaveCount(1)
  })

  // Performance test for mobile devices
  test('should load and interact within performance budgets', async ({ page }) => {
    const startTime = Date.now()
    
    // Navigate and wait for initial load
    await page.goto('/transaction/123/negotiation')
    await expect(page.locator('[data-testid="smart-negotiation-section"]')).toBeVisible()
    
    const loadTime = Date.now() - startTime
    
    // Should load within 3 seconds on mobile
    expect(loadTime).toBeLessThan(3000)
    
    // Test interaction responsiveness
    const interactionStart = Date.now()
    await page.locator('[data-testid="propose-counter-btn"]').click()
    await expect(page.locator('[data-testid="custom-message-input"]')).toBeVisible()
    const interactionTime = Date.now() - interactionStart
    
    // Interactions should respond within 100ms
    expect(interactionTime).toBeLessThan(100)
  })
})
