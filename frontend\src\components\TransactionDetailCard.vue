<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { NCard, NAvatar, NText, NIcon, NFlex, NTag } from 'naive-ui';
import { useTranslation } from '@/composables/useTranslation';
import { formatAmount as formatCurrencyAmount } from '@/utils/currencyUtils';
import { 
  StarOutline as StarIcon, 
  ShieldCheckmarkOutline as ShieldCheckmarkIcon, 
  ArrowForwardOutline as ArrowForward,
  ArrowBackOutline as ArrowBack
} from '@vicons/ionicons5';

const { t, isRTL } = useTranslation(); // Use isRTL directly from the composable

export type LevelType = 'star' | 'shield' | 'default';

export interface UserTransactionInfo {
  name: string;
  avatarUrl?: string;
  level: number;
  levelType: LevelType;
  avatarBorderColor: string;
}

interface Props {
  title?: string;
  userA: UserTransactionInfo;
  userB: UserTransactionInfo;
  userAPaysAmount: number;
  userAPaysCurrency: string;
  userBPaysAmount: number;
  userBPaysCurrency: string;
  exchangeRateText: string;
}

const props = defineProps<Props>();

// Responsive sizing
const isMobile = ref(false);
const isExtraSmall = ref(false);

const checkMobile = () => {
  const width = window.innerWidth;
  isMobile.value = width <= 768;
  isExtraSmall.value = width <= 480;
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

// Computed properties for responsive sizing
const avatarSize = computed(() => {
  if (isExtraSmall.value) return 50;
  if (isMobile.value) return 55;
  return 80;
});

const avatarBorderWidth = computed(() => {
  if (isExtraSmall.value) return 2;
  if (isMobile.value) return 2.5;
  return 4;
});

const arrowSize = computed(() => {
  if (isExtraSmall.value) return 20;
  if (isMobile.value) return 22;
  return 32;
});

const tagSize = computed(() => {
  if (isExtraSmall.value) return 'small';
  if (isMobile.value) return 'small';
  return 'medium';
});

// Hide title on mobile to save space
const cardTitle = computed(() => {
  return isMobile.value ? undefined : (props.title || 'Transaction Details');
});

const formatAmount = (amount: number, currency: string) => {
  return formatCurrencyAmount(amount, currency);
};

const formattedUserAPaysAmount = computed(() => formatAmount(props.userAPaysAmount, props.userAPaysCurrency));
const formattedUserBPaysAmount = computed(() => formatAmount(props.userBPaysAmount, props.userBPaysCurrency));

// Top slot is for payment from the user visually on the LEFT to the user visually on the RIGHT
const topSlotPaysAmount = computed(() => {
  return isRTL.value ? formattedUserBPaysAmount.value : formattedUserAPaysAmount.value;
});
const topSlotPaysCurrency = computed(() => {
  return isRTL.value ? props.userBPaysCurrency : props.userAPaysCurrency;
});

// Bottom slot is for payment from the user visually on the RIGHT to the user visually on the LEFT
const bottomSlotPaysAmount = computed(() => {
  return isRTL.value ? formattedUserAPaysAmount.value : formattedUserBPaysAmount.value;
});
const bottomSlotPaysCurrency = computed(() => {
  return isRTL.value ? props.userAPaysCurrency : props.userBPaysCurrency;
});

// Arrow for top slot (payment from left user to right user)
// This arrow should always point visually to the right, as it represents flow from left user to right user.
const topArrowIcon = computed(() => ArrowForward);

// Arrow for bottom slot (payment from right user to left user)
// This arrow should always point visually to the left, as it represents flow from right user to left user.
const bottomArrowIcon = computed(() => ArrowBack);

</script>

<template>
  <n-card :title="cardTitle" bordered class="transaction-detail-card">
    <n-flex justify="space-around" align="center" class="main-content-flex">      <!-- User A Section (props.userA) -->
      <n-flex vertical align="center" class="user-display-section">
        <n-avatar
          round
          :size="avatarSize"
          :src="props.userA.avatarUrl"
          :img-props="{ alt: props.userA.name }"
          :style="{ border: `${avatarBorderWidth}px solid ${props.userA.avatarBorderColor}` }"
          object-fit="cover"
        />
        <n-text strong class="username">{{ props.userA.name }}</n-text>
        <n-tag :type="props.userA.levelType === 'star' ? 'warning' : (props.userA.levelType === 'shield' ? 'success' : 'info')" round :size="tagSize" class="level-tag">
          {{ t('level.current', { value: props.userA.level }) }}
          <template #icon>
            <n-icon :component="props.userA.levelType === 'star' ? StarIcon : (props.userA.levelType === 'shield' ? ShieldCheckmarkIcon : undefined)" />
          </template>
        </n-tag>
      </n-flex>      <!-- Middle Section (Transaction Flow) -->
      <n-flex vertical align="center" justify="center" class="transaction-flow-section">
        <n-text strong class="payment-text payment-text-top">
          Pays {{ topSlotPaysAmount }} {{ topSlotPaysCurrency }}
        </n-text>
        <n-icon :component="topArrowIcon" :size="arrowSize" class="arrow-icon" />
        
        <n-tag type="default" class="exchange-rate-tag">
          {{ props.exchangeRateText }}
        </n-tag>
        
        <n-icon :component="bottomArrowIcon" :size="arrowSize" class="arrow-icon" />
        <n-text strong class="payment-text payment-text-bottom">
          Pays {{ bottomSlotPaysAmount }} {{ bottomSlotPaysCurrency }}
        </n-text>
      </n-flex>      <!-- User B Section (props.userB) -->
      <n-flex vertical align="center" class="user-display-section">
        <n-avatar
          round
          :size="avatarSize"
          :src="props.userB.avatarUrl"
          :img-props="{ alt: props.userB.name }"
          :style="{ border: `${avatarBorderWidth}px solid ${props.userB.avatarBorderColor}` }"
          object-fit="cover"
        />
        <n-text strong class="username">{{ props.userB.name }}</n-text>
        <n-tag :type="props.userB.levelType === 'star' ? 'warning' : (props.userB.levelType === 'shield' ? 'success' : 'info')" round :size="tagSize" class="level-tag">
          {{ t('level.current', { value: props.userB.level }) }}
          <template #icon>
            <n-icon :component="props.userB.levelType === 'star' ? StarIcon : (props.userB.levelType === 'shield' ? ShieldCheckmarkIcon : undefined)" />
          </template>
        </n-tag>
      </n-flex>
    </n-flex>
  </n-card>
</template>

<style scoped>
.transaction-detail-card {
  margin: auto;
  padding: 16px;
}

.main-content-flex {
  gap: 16px;
}

.user-display-section {
  width: 150px;
  text-align: center;
}

.username {
  font-size: 1.15em;
  margin-top: 8px;
  font-weight: bold;
}

.level-tag {
  margin-top: 6px;
}
.level-tag :deep(.n-tag__icon) {
  margin-inline-end: 6px;
}
[dir="rtl"] .level-tag :deep(.n-tag__icon) {
  margin-inline-start: 6px;
}

.n-avatar {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.transaction-flow-section {
  flex-grow: 1;
  gap: 8px;
}

.payment-text {
  font-size: 1.05em;
  font-weight: 500;
}
.payment-text-top {
  color: var(--n-error-color);
}
.payment-text-bottom {
  color: var(--n-success-color);
}

.arrow-icon {
  color: #555;
  margin: 4px 0;
}

.exchange-rate-tag {
  padding: 8px 12px;
  font-size: 1em;
  border: 1px solid var(--n-border-color);
  border-radius: var(--n-border-radius);
  background-color: var(--n-tag-color-disabled);
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  direction: ltr;
  text-align: center;
}

.n-flex > :deep(*) {
  max-width: 100%;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .transaction-detail-card {
    padding: 8px;
  }
  
  .main-content-flex {
    gap: 8px;
  }
  
  .user-display-section {
    width: 90px;
    min-width: 90px;
  }
  
  .username {
    font-size: 0.85em;
    margin-top: 4px;
    line-height: 1.1;
    word-break: break-word;
  }
    .level-tag {
    margin-top: 3px;
  }
  
  .level-tag :deep(.n-tag) {
    font-size: 0.7em !important;
    padding: 2px 6px !important;
  }
  
  .transaction-flow-section {
    gap: 4px;
    min-width: 0;
    flex-shrink: 1;
  }
  
  .payment-text {
    font-size: 0.8em;
    text-align: center;
    line-height: 1.1;
    word-break: break-word;
    margin: 0;
    padding: 0 2px;
  }
  
  .arrow-icon {
    margin: 1px 0;
  }
  
  .exchange-rate-tag {
    padding: 4px 6px;
    font-size: 0.75em;
    white-space: nowrap;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* Extra small mobile screens */
@media (max-width: 480px) {
  .transaction-detail-card {
    padding: 6px;
  }
  
  .main-content-flex {
    gap: 6px;
  }
  
  .user-display-section {
    width: 75px;
    min-width: 75px;
  }
  
  .username {
    font-size: 0.75em;
    margin-top: 3px;
  }
    .level-tag {
    margin-top: 2px;
  }
  
  .level-tag :deep(.n-tag) {
    font-size: 0.65em !important;
    padding: 1px 4px !important;
  }
  
  .transaction-flow-section {
    gap: 3px;
  }
  
  .payment-text {
    font-size: 0.7em;
    line-height: 1.0;
  }
  
  .exchange-rate-tag {
    padding: 3px 5px;
    font-size: 0.7em;
  }
  
  .arrow-icon {
    margin: 0;
  }
}

/* Very small screens - ultra compact */
@media (max-width: 360px) {
  .transaction-detail-card {
    padding: 4px;
  }
  
  .main-content-flex {
    gap: 4px;
  }
  
  .user-display-section {
    width: 65px;
    min-width: 65px;
  }
  
  .username {
    font-size: 0.7em;
    margin-top: 2px;
  }
  
  .payment-text {
    font-size: 0.65em;
  }
  
  .exchange-rate-tag {
    padding: 2px 4px;
    font-size: 0.65em;
  }
}

/* Dark mode styling for TransactionDetailCard */
[data-theme="dark"] .transaction-detail-card {
  background-color: var(--n-card-color);
  border-color: var(--n-divider-color);
}

[data-theme="dark"] .username {
  color: var(--n-text-color);
}

[data-theme="dark"] .payment-text-top {
  color: var(--n-error-color-suppl);
}

[data-theme="dark"] .payment-text-bottom {
  color: var(--n-success-color-suppl);
}

[data-theme="dark"] .arrow-icon {
  color: var(--n-text-color-disabled);
}

[data-theme="dark"] .exchange-rate-tag {
  background-color: var(--n-color-embedded);
  border-color: var(--n-divider-color);
  color: var(--n-text-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .n-avatar {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* Enhanced hover effects for dark mode */
[data-theme="dark"] .transaction-detail-card:hover {
  border-color: var(--n-primary-color);
  transition: border-color 0.3s ease;
}
</style>
