/**
 * Debug Report Fixes Verification
 * 
 * This file contains utilities to verify that the circular reference
 * and initialization issues have been resolved.
 */

import { useOfflineReports } from '@/composables/useOfflineReports';
import { useFormDrafts } from '@/composables/useFormDrafts';

/**
 * Debug logger that only logs in development mode
 */
const debugLog = {
  log: (...args: any[]) => {
    if (import.meta.env.DEV) {
      console.log(...args);
    }
  },
  error: (...args: any[]) => {
    if (import.meta.env.DEV) {
      console.error(...args);
    }
  },
  warn: (...args: any[]) => {
    if (import.meta.env.DEV) {
      console.warn(...args);
    }
  }
};

/**
 * Test that composables can be imported without errors
 */
export function testComposableImports() {
  try {
    debugLog.log('Testing composable imports...');
    
    // These should not throw errors during import
    const offlineReports = useOfflineReports();
    const formDrafts = useFormDrafts();
    
    debugLog.log('✅ Composables imported successfully');
    
    // Test that computed values can be accessed without circular reference errors
    debugLog.log('Testing computed value access...');
    
    // These should not cause circular reference errors
    const offlineCount = offlineReports.offlineReportCount.value;
    const draftCount = formDrafts.draftCount.value;
    const hasOffline = offlineReports.hasOfflineReports.value;
    const hasDrafts = formDrafts.hasDrafts.value;
    
    debugLog.log('✅ Computed values accessed successfully:', {
      offlineCount,
      draftCount,
      hasOffline,
      hasDrafts
    });
    
    return {
      success: true,
      offlineReports,
      formDrafts,
      stats: {
        offlineCount,
        draftCount,
        hasOffline,
        hasDrafts
      }
    };
  } catch (error) {
    debugLog.error('❌ Error testing composables:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test i18n interpolation with computed values
 */
export function testI18nInterpolation() {
  try {
    debugLog.log('Testing i18n interpolation...');
    
    const offlineReports = useOfflineReports();
    const formDrafts = useFormDrafts();
    
    // Test that these values can be used in i18n without circular reference errors
    const testValues = {
      offlineCount: offlineReports.offlineReportCount.value,
      draftCount: formDrafts.draftCount.value
    };
    
    // Simulate what happens in the template
    const interpolationTest = JSON.stringify(testValues);
    
    debugLog.log('✅ i18n interpolation test passed:', interpolationTest);
    
    return {
      success: true,
      testValues,
      serialized: interpolationTest
    };
  } catch (error) {
    debugLog.error('❌ Error testing i18n interpolation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Mock localStorage implementation for testing
 */
class MockLocalStorage implements Storage {
  private store: Record<string, string> = {};

  get length(): number {
    return Object.keys(this.store).length;
  }

  clear(): void {
    this.store = {};
  }

  getItem(key: string): string | null {
    return this.store[key] || null;
  }

  key(index: number): string | null {
    const keys = Object.keys(this.store);
    return keys[index] || null;
  }

  removeItem(key: string): void {
    delete this.store[key];
  }

  setItem(key: string, value: string): void {
    this.store[key] = value;
  }
}

/**
 * Test localStorage initialization with complete isolation using mock
 * This version provides complete isolation from real localStorage
 */
export function testLocalStorageInitializationWithMock() {
  // Store reference to original localStorage
  const originalLocalStorage = globalThis.localStorage;
  const mockStorage = new MockLocalStorage();
  
  try {
    debugLog.log('Testing localStorage initialization with mock isolation...');
    
    // Temporarily replace localStorage with mock
    Object.defineProperty(globalThis, 'localStorage', {
      value: mockStorage,
      writable: true,
      configurable: true
    });
    
    const offlineReports = useOfflineReports();
    const formDrafts = useFormDrafts();
    
    // Access computed properties to trigger initialization
    const offlineCount = offlineReports.offlineReportCount.value;
    const draftCount = formDrafts.draftCount.value;
    
    debugLog.log('✅ localStorage mock initialization test passed:', {
      offlineCount,
      draftCount,
      mockStorageContents: { ...mockStorage['store'] }
    });
    
    return {
      success: true,
      initialCounts: {
        offlineCount,
        draftCount
      },
      mockStorageState: { ...mockStorage['store'] }
    };
  } catch (error) {
    debugLog.error('❌ Error testing localStorage initialization with mock:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  } finally {
    // Always restore original localStorage
    Object.defineProperty(globalThis, 'localStorage', {
      value: originalLocalStorage,
      writable: true,
      configurable: true
    });
    debugLog.log('Original localStorage restored after mock test');
  }
}

/**
 * Test localStorage initialization with safe isolation
 */
export function testLocalStorageInitialization() {
  // Store existing localStorage data to restore later
  const existingOfflineReports = localStorage.getItem('munygo-offline-reports');
  const existingFormDrafts = localStorage.getItem('munygo-form-drafts');
  
  try {
    debugLog.log('Testing localStorage initialization with safe isolation...');
    
    // Temporarily clear data for clean test (will be restored)
    localStorage.removeItem('munygo-offline-reports');
    localStorage.removeItem('munygo-form-drafts');
    
    const offlineReports = useOfflineReports();
    const formDrafts = useFormDrafts();
    
    // Access computed properties to trigger initialization
    const offlineCount = offlineReports.offlineReportCount.value;
    const draftCount = formDrafts.draftCount.value;
    
    debugLog.log('✅ localStorage initialization test passed:', {
      offlineCount,
      draftCount
    });
    
    return {
      success: true,
      initialCounts: {
        offlineCount,
        draftCount
      }
    };
  } catch (error) {
    debugLog.error('❌ Error testing localStorage initialization:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  } finally {
    // Always restore original localStorage data to prevent side effects
    if (existingOfflineReports !== null) {
      localStorage.setItem('munygo-offline-reports', existingOfflineReports);
    }
    if (existingFormDrafts !== null) {
      localStorage.setItem('munygo-form-drafts', existingFormDrafts);
    }
    debugLog.log('localStorage state restored after test');
  }
}

/**
 * Run all tests
 */
export function runAllDebugReportTests() {
  debugLog.log('🧪 Running all debug report fix tests...');
  
  const results = {
    imports: testComposableImports(),
    i18n: testI18nInterpolation(),
    localStorage: testLocalStorageInitialization(),
    localStorageMock: testLocalStorageInitializationWithMock()
  };
  
  const allPassed = Object.values(results).every(result => result.success);
  
  debugLog.log(allPassed ? '✅ All tests passed!' : '❌ Some tests failed');
  debugLog.log('Test results:', results);
  
  return {
    allPassed,
    results
  };
}

/**
 * Result types for safe computed value access
 */
export type SafeComputedResult<T> = 
  | { success: true; value: T }
  | { success: false; error: Error };

/**
 * Utility to safely access computed values for templates
 * Returns a discriminated union indicating success or failure
 */
export function safeComputedValue<T>(computedRef: { value: T }): SafeComputedResult<T> {
  try {
    const value = computedRef.value;
    return { success: true, value };
  } catch (error) {
    debugLog.warn('Error accessing computed value:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error : new Error(String(error))
    };
  }
}

/**
 * Utility to safely format values for i18n
 */
export function safeI18nValue(value: any): string | number {
  if (value && typeof value === 'object' && 'value' in value) {
    return value.value;
  }
  return value;
}

/**
 * Helper function to extract value from SafeComputedResult with fallback
 * Provides backward compatibility for existing code
 */
export function getComputedValueOrFallback<T>(
  computedRef: { value: T }, 
  fallback: T
): T {
  const result = safeComputedValue(computedRef);
  return result.success ? result.value : fallback;
}

/**
 * Helper function to extract value from SafeComputedResult or throw
 * Use when you want to handle errors at a higher level
 */
export function getComputedValueOrThrow<T>(computedRef: { value: T }): T {
  const result = safeComputedValue(computedRef);
  if (result.success) {
    return result.value;
  }
  throw result.error;
}

/**
 * Run tests with specific isolation level
 */
export function runDebugReportTestsWithIsolation(isolationLevel: 'none' | 'restore' | 'mock' = 'restore') {
  debugLog.log(`🧪 Running debug report tests with ${isolationLevel} isolation...`);
  
  const baseResults = {
    imports: testComposableImports(),
    i18n: testI18nInterpolation()
  };
  
  let storageResult;
  switch (isolationLevel) {
    case 'mock':
      storageResult = testLocalStorageInitializationWithMock();
      break;
    case 'restore':
      storageResult = testLocalStorageInitialization();
      break;
    case 'none':
      // Skip localStorage tests to avoid any manipulation
      storageResult = { success: true, message: 'Skipped for isolation' };
      break;
    default:
      storageResult = testLocalStorageInitialization();
  }
  
  const results = {
    ...baseResults,
    localStorage: storageResult
  };
  
  const allPassed = Object.values(results).every(result => result.success);
  
  debugLog.log(allPassed ? '✅ All isolated tests passed!' : '❌ Some isolated tests failed');
  debugLog.log('Isolated test results:', results);
  
  return {
    allPassed,
    results,
    isolationLevel
  };
}
