import type { Router } from 'vue-router';
import { transactionalChatApiService } from '@/services/transactionalChatApi';

/**
 * Navigate to the appropriate chat interface based on the available IDs
 * This function handles the transition from the old chat system to the new transactional chat system
 */
export async function navigateToChat(
  router: Router,
  options: {
    chatSessionId?: string | null;
    transactionId?: string | null;
  }
): Promise<void> {
  const { chatSessionId, transactionId } = options;

  try {
    // If we have a transactionId, use the new transactional chat directly
    if (transactionId) {
      console.log('🔄 Navigating to transactional chat with transactionId:', transactionId);
      await router.push({ name: 'TransactionalChat', params: { transactionId } });
      return;
    }

    // If we have a chatSessionId but no transactionId, resolve it to transactionId
    if (chatSessionId) {
      console.log('🔄 Resolving chatSessionId to transactionId:', chatSessionId);
      
      try {
        const { transactionId: resolvedTransactionId } = await transactionalChatApiService.resolveChatSessionToTransaction(chatSessionId);
        console.log('✅ Resolved to transactionId:', resolvedTransactionId);
        
        // Navigate to the new transactional chat with the resolved transactionId
        await router.push({ name: 'TransactionalChat', params: { transactionId: resolvedTransactionId } });
        return;
      } catch (resolveError) {
        console.warn('⚠️ Failed to resolve chatSessionId to transactionId, falling back to old chat:', resolveError);
        
        // Fallback to old chat system if resolution fails
        await router.push({ name: 'ChatSession', params: { chatSessionId } });
        return;
      }
    }

    // If we have neither, we can't navigate
    throw new Error('Neither chatSessionId nor transactionId provided');
  } catch (error) {
    console.error('❌ Failed to navigate to chat:', error);
    throw error;
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use navigateToChat instead
 */
export function goToChatLegacy(router: Router, chatSessionId?: string | null): void {
  if (chatSessionId) {
    router.push({ name: 'ChatSession', params: { chatSessionId } });
  } else {
    console.error('Chat session ID not found.');
  }
}
