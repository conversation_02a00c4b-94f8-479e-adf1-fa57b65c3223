<template>
  <div class="table-responsive">
    <table class="data-table">      <thead>        <tr>
          <th @click="requestSort('reportId')" :class="getSortClass('reportId')">ID</th>
          <th @click="requestSort('reportTitle')" :class="getSortClass('reportTitle')">Title</th>
          <th @click="requestSort('reportType')" :class="getSortClass('reportType')">Type</th>
          <th @click="requestSort('reportSeverity')" :class="getSortClass('reportSeverity')">Severity</th>
          <th @click="requestSort('status')" :class="getSortClass('status')">Status</th>
          <th>Tags</th>
          <th @click="requestSort('serverReceivedAt')" :class="getSortClass('serverReceivedAt')">Received At</th>
          <th>User</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>        <tr v-for="report in reports" :key="report.reportId" @click="$emit('view-report', report)" class="table-row">
          <td class="report-id">{{ report.reportId?.substring(0, 8) }}...</td>
          <td class="report-title" :title="report.reportTitle">{{ formatTitle(report.reportTitle) }}</td>
          <td>{{ formatType(report.reportType) }}</td>          <td>
            <span
              :style="getSeverityBadgeStyle(report.reportSeverity || '')"
              class="severity-badge"
              :data-severity="report.reportSeverity"
            >
              {{ formatSeverity(report.reportSeverity) }}
            </span>
          </td>
          <td>            <div class="status-cell">
              <span
                :class="getStatusBadgeClass(report.status || 'NEW')"
                class="status-badge"
              >
                {{ getStatusDisplayText(report.status || 'new') }}
              </span>
              <select
                @click.stop
                @change="handleStatusChange(report, $event)"
                :value="mapToSimplifiedStatus(report.status)"
                class="status-select"
                :class="{ 'status-changing': isChangingStatus(report.reportId) }"
              >
                <option value="new">New/Unread</option>
                <option value="todo">To Do</option>
                <option value="done">Done</option>
              </select>
            </div>
          </td><td class="tags-cell">
            <div v-if="report.tags && report.tags.length > 0" class="tags-list">
              <span 
                v-for="tagItem in report.tags.slice(0, 2)" 
                :key="getTagDisplayName(tagItem)"
                class="tag-badge"
                :class="getTagOriginClass(tagItem.origin)"
                :title="getTagOriginTitle(tagItem.origin)"
              >
                {{ getTagDisplayName(tagItem) }}
              </span>
              <span 
                v-if="report.tags.length > 2" 
                class="tag-badge tag-more"
                :title="`${report.tags.length - 2} more tags`"
              >
                +{{ report.tags.length - 2 }}
              </span>
            </div>
            <span v-else class="no-tags">-</span>
          </td>
          <td>{{ formatDate(report.serverReceivedAt) }}</td>
          <td>{{ formatUser(report) }}</td>          <td class="actions-cell">
            <div class="actions-container">
              <button 
                @click.stop="$emit('view-report', report)" 
                class="btn btn-icon" 
                title="View Report"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                  <circle cx="12" cy="12" r="3"/>
                </svg>
              </button>
              <button 
                @click.stop="report.reportId && $emit('download-report', report.reportId)" 
                class="btn btn-icon" 
                :disabled="!report.reportId"
                title="Download Report"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { format } from 'date-fns';
import { getTagOriginClass, getTagOriginTitle, getTagDisplayName } from '@/utils/tagOriginUtils';
import { 
  mapToSimplifiedStatus, 
  getStatusDisplayText, 
  getStatusBadgeColor,
  mapFromSimplifiedStatus,
  getApiStatusFromSimplified
} from '@/types/admin';
import type { ParsedReport, DebugReportStatus, SimplifiedStatus } from '@/types/admin';

// Utility function to calculate contrast-based text color
function getContrastTextColor(hexColor: string): string {
  // Remove # if present
  const color = hexColor.replace('#', '');
  
  // Convert hex to RGB
  const r = parseInt(color.substr(0, 2), 16);
  const g = parseInt(color.substr(2, 2), 16);
  const b = parseInt(color.substr(4, 2), 16);
  
  // Calculate relative luminance using WCAG formula
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // Return white for dark backgrounds, black for light backgrounds
  return luminance > 0.5 ? '#000000' : '#ffffff';
}

// Function to get severity badge style with proper contrast
function getSeverityBadgeStyle(severity: string): Record<string, string> {
  const severityColors: Record<string, string> = {
    low: '#10b981',
    medium: '#f59e0b',
    high: '#ef4444',
    critical: '#dc2626'
  };
  
  const backgroundColor = severityColors[severity] || '#6b7280';
  const textColor = getContrastTextColor(backgroundColor);
  
  return {
    backgroundColor,
    color: textColor,
    borderColor: backgroundColor
  };
}

interface ReportSortPayload {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

defineOptions({
  name: 'DebugReportTable'
});

const props = defineProps<{
  reports: ParsedReport[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}>();

const emit = defineEmits<{
  'view-report': [report: ParsedReport];
  'download-report': [id: string];
  'sort': [criteria: ReportSortPayload];
  'update-status': [reportId: string, status: string];
}>();

// Track status changes
const changingStatuses = ref<Set<string>>(new Set());

function formatType(type?: string): string {
  if (!type) return 'N/A';
  return type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
}

function formatTitle(title?: string): string {
  if (!title) return 'No title';
  return title.length > 50 ? title.substring(0, 50) + '...' : title;
}

function formatSeverity(severity?: string): string {
  if (!severity) return 'N/A';
  return severity.charAt(0).toUpperCase() + severity.slice(1);
}

function formatDate(dateString?: string): string {
  return dateString ? format(new Date(dateString), 'MMM dd, yyyy HH:mm') : 'N/A';
}


function formatUser(report: ParsedReport): string {
  if (report.username) {
    return report.username;
  }
  if (report.userEmail) {
    return report.userEmail;
  }
  if (report.userId) {
    return `User ${report.userId.substring(0, 8)}...`;
  }
  return 'Anonymous';
}

function requestSort(field: string) {
  let newOrder: 'asc' | 'desc' = 'desc';
  if (props.sortBy === field) {
    newOrder = props.sortOrder === 'asc' ? 'desc' : 'asc';
  }
  emit('sort', { sortBy: field, sortOrder: newOrder });
}

function getSortClass(field: string): string {  if (props.sortBy !== field) return 'sortable';
  return `sortable sorted-${props.sortOrder}`;
}

// Status management functions
function getStatusBadgeClass(status?: string): string {
  const simplified = mapToSimplifiedStatus(status as DebugReportStatus);
  return `status-badge-${simplified}`;
}

function isChangingStatus(reportId: string): boolean {
  return changingStatuses.value.has(reportId);
}

async function handleStatusChange(report: ParsedReport, event: Event) {
  const select = event.target as HTMLSelectElement;
  const newSimplifiedStatus = select.value as SimplifiedStatus;
  const reportId = report.reportId;
  
  console.log('[DebugReportTable] Status change triggered:', {
    reportId,
    currentStatus: report.status,
    newSimplifiedStatus,
    selectValue: select.value
  });
  
  if (!reportId) return;
  
  // Store the original value in case we need to revert
  const originalSimplifiedStatus = mapToSimplifiedStatus(report.status as DebugReportStatus);
  
  // Map simplified status to API format (lowercase with underscores)
  const newApiStatus = getApiStatusFromSimplified(newSimplifiedStatus);
  
  console.log('[DebugReportTable] Mapped statuses:', {
    originalSimplifiedStatus,
    newApiStatus
  });
  
  changingStatuses.value.add(reportId);
  
  try {
    // Emit the update event - the parent will handle the async operation
    console.log('[DebugReportTable] Emitting update-status event');
    emit('update-status', reportId, newApiStatus);
    
  } catch (error) {
    console.error('Failed to update status:', error);
    // Reset select to previous value on error
    select.value = originalSimplifiedStatus;
  } finally {
    // Remove the loading state after a short delay to allow the parent to process
    setTimeout(() => {
      changingStatuses.value.delete(reportId);
    }, 500);
  }
}
</script>

<style scoped>
.table-responsive {
  overflow-x: auto;
  overflow-y: visible;
  max-width: 100%;
  border-radius: var(--radius-lg, 8px);
  border: 1px solid var(--border-base, #e0e0e0);
  background-color: var(--bg-surface, #ffffff);
}

.data-table {
  width: 100%;
  min-width: 800px; /* Minimum width to prevent excessive compression */
  border-collapse: collapse;
  font-size: var(--font-size-sm, 0.875rem);
  table-layout: fixed; /* Ensure consistent column widths */
}

.data-table th, 
.data-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-base, #e0e0e0);
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle; /* Ensure consistent vertical alignment */
}

/* Set specific column widths - Better balanced distribution */
.data-table th:nth-child(1), /* ID */
.data-table td:nth-child(1) {
  width: 10%;
  min-width: 85px;
}

.data-table th:nth-child(2), /* Title */
.data-table td:nth-child(2) {
  width: 22%;
  min-width: 180px;
}

.data-table th:nth-child(3), /* Type */
.data-table td:nth-child(3) {
  width: 9%;
  min-width: 75px;
}

.data-table th:nth-child(4), /* Severity */
.data-table td:nth-child(4) {
  width: 11%;
  min-width: 90px;
}

.data-table th:nth-child(5), /* Tags */
.data-table td:nth-child(5) {
  width: 16%;
  min-width: 130px;
}

.data-table th:nth-child(6), /* Received At */
.data-table td:nth-child(6) {
  width: 13%;
  min-width: 105px;
}

.data-table th:nth-child(7), /* User */
.data-table td:nth-child(7) {
  width: 10%;
  min-width: 80px;
}

.data-table th:nth-child(8), /* Actions */
.data-table td:nth-child(8) {
  width: 9%;
  min-width: 75px;
}

.data-table th {
  background-color: var(--bg-surface-hover, #f8f9fa);
  font-weight: var(--font-weight-semibold, 600);
  cursor: pointer;
  user-select: none;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table th.sortable:hover {
  background-color: var(--gray-200, #e2e8f0);
}

.data-table th.sorted-asc::after { 
  content: ' ▲'; 
  color: var(--primary-500, #3b82f6);
}

.data-table th.sorted-desc::after { 
  content: ' ▼';
  color: var(--primary-500, #3b82f6);
}

.table-row {
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.table-row:hover {
  background-color: var(--bg-surface-hover, #f8f9fa);
}

.report-id {
  font-family: var(--font-family-mono, monospace);
  font-size: var(--font-size-xs, 0.75rem);
}

.report-title {
  font-weight: var(--font-weight-medium, 500);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%; /* Use full available width instead of fixed 200px */
  line-height: 1.4;
}

.severity-badge {
  font-weight: var(--font-weight-semibold, 600);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full, 9999px);
  font-size: var(--font-size-xs, 0.75rem);
  border: 1px solid transparent;
  /* Dynamic colors are applied via inline styles for proper contrast */
}

/* Status management styles */
.status-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-start;
}

.status-badge {
  font-weight: var(--font-weight-semibold, 600);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full, 9999px);
  font-size: var(--font-size-xs, 0.75rem);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge-new {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #f59e0b;
}

.status-badge-todo {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #3b82f6;
}

.status-badge-done {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

.status-select {
  font-size: var(--font-size-xs, 0.75rem);
  padding: 0.125rem 0.25rem;
  border: 1px solid var(--border-base, #e0e0e0);
  border-radius: var(--radius-sm, 4px);
  background-color: var(--bg-surface, #ffffff);
  color: var(--text-primary, #1f2937);
  cursor: pointer;
}

.status-select:focus {
  outline: 2px solid var(--color-primary, #3b82f6);
  outline-offset: 1px;
}

.status-changing {
  opacity: 0.6;
  cursor: wait;
}

.actions-cell {
  text-align: left;
  vertical-align: middle;
  padding: 0.75rem !important; /* Ensure consistent padding with other cells */
}

.actions-container {
  display: inline-flex;
  gap: 0.375rem;
  align-items: center;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md, 6px);
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-semibold, 600);
  transition: all 0.2s ease-in-out;
}

.btn-icon {
  padding: 0.375rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  color: var(--primary-500, #3b82f6);
  border: 1px solid transparent;
  border-radius: var(--radius-md, 6px);
  min-width: 28px;
  min-height: 28px;
  flex-shrink: 0;
}

.btn-icon:hover:not(:disabled) {
  background-color: var(--primary-50, #eff6ff);
  color: var(--primary-600, #2563eb);
}

.btn-icon:disabled {
  color: var(--text-quaternary, #94a3b8);
  cursor: not-allowed;
}

.btn-icon svg {
  flex-shrink: 0;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-xs, 0.75rem);
}

.btn-ghost {
  background: transparent;
  color: var(--primary-500, #3b82f6);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--primary-50, #eff6ff);
}

.btn-ghost:disabled {
  color: var(--text-quaternary, #94a3b8);
  cursor: not-allowed;
}

/* Mobile and Tablet Responsive Design */
@media (max-width: 1024px) {
  .data-table {
    min-width: 700px;
  }
  
  .data-table th,
  .data-table td {
    padding: 0.625rem 0.5rem;
  }
  
  .report-title {
    max-width: 140px;
  }
  .actions-container {
    gap: 0.25rem;
  }
  
  .actions-cell {
    padding: 0.625rem 0.5rem !important; /* Match tablet td padding */
  }
}

@media (max-width: 768px) {
  .data-table {
    min-width: 600px;
    font-size: var(--font-size-xs, 0.75rem);
  }
  
  .data-table th,
  .data-table td {
    padding: 0.5rem 0.375rem;
  }

  .report-title {
    max-width: 100px;
  }  .actions-container {
    gap: 0.25rem;
  }

  .actions-cell {
    padding: 0.5rem 0.375rem !important; /* Match mobile td padding */
  }

  .btn-icon {
    padding: 0.25rem;
    min-width: 24px;
    min-height: 24px;
  }

  .btn-icon svg {
    width: 12px;
    height: 12px;
  }
  
  /* Adjust column widths for mobile */
  .data-table th:nth-child(2),
  .data-table td:nth-child(2) {
    width: 20%;
    min-width: 120px;
  }
  
  .data-table th:nth-child(5),
  .data-table td:nth-child(5) {
    width: 14%;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .data-table {
    min-width: 500px;
  }
  
  .data-table th,
  .data-table td {
    padding: 0.375rem 0.25rem;
  }
  .actions-cell {
    padding: 0.375rem 0.25rem !important; /* Match mobile td padding */
  }
}

/* ===== DARK MODE SUPPORT ===== */

[data-theme="dark"] .table-responsive {
  border-color: var(--border-base-dark, #374151);
  background-color: var(--bg-surface-dark, #1f2937);
}

[data-theme="dark"] .data-table th {
  background-color: var(--bg-surface-hover-dark, #374151);
  color: var(--text-primary-dark, #f9fafb);
  border-bottom-color: var(--border-base-dark, #4b5563);
}

[data-theme="dark"] .data-table th.sortable:hover {
  background-color: var(--bg-hover-dark, #4b5563);
}

[data-theme="dark"] .data-table th.sorted-asc::after,
[data-theme="dark"] .data-table th.sorted-desc::after {
  color: var(--primary-400, #60a5fa);
}

[data-theme="dark"] .data-table td {
  border-bottom-color: var(--border-base-dark, #4b5563);
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .table-row:hover {
  background-color: var(--bg-surface-hover-dark, #374151);
}

[data-theme="dark"] .report-id {
  color: var(--text-secondary-dark, #d1d5db);
}

[data-theme="dark"] .report-title {
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .btn-icon {
  color: var(--primary-400, #60a5fa);
}

[data-theme="dark"] .btn-icon:hover:not(:disabled) {
  background-color: var(--primary-900, #1e3a8a);
  color: var(--primary-300, #93c5fd);
}

[data-theme="dark"] .btn-icon:disabled {
  color: var(--text-quaternary-dark, #6b7280);
}

[data-theme="dark"] .btn-ghost {
  color: var(--primary-400, #60a5fa);
}

[data-theme="dark"] .btn-ghost:hover:not(:disabled) {
  background-color: var(--primary-900, #1e3a8a);
}

[data-theme="dark"] .btn-ghost:disabled {
  color: var(--text-quaternary-dark, #6b7280);
}

/* Dark mode support for status select */
[data-theme="dark"] .status-select {
  background-color: var(--bg-surface-dark, #1f2937);
  border-color: var(--border-base-dark, #4b5563);
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .status-select:focus {
  outline-color: var(--primary-400, #60a5fa);
}

[data-theme="dark"] .status-select option {
  background-color: var(--bg-surface-dark, #1f2937);
  color: var(--text-primary-dark, #f9fafb);
}

/* Tags column styling - Improved overflow handling */
.tags-cell {
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  position: relative;
}

.tags-list {
  display: flex;
  flex-wrap: nowrap;
  gap: 0.25rem;
  align-items: center;
  overflow: hidden;
  max-width: 100%;
}

.tag-badge {
  font-size: var(--font-size-xs, 0.75rem);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm, 4px);
  font-weight: var(--font-weight-medium, 500);
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  border: 1px solid transparent;
}

.tag-predefined {
  background-color: var(--primary-100, #dbeafe);
  color: var(--primary-700, #1d4ed8);
  border-color: var(--primary-200, #bfdbfe);
}

.tag-ai-suggested {
  background-color: var(--purple-100, #e9d5ff);
  color: var(--purple-700, #7c3aed);
  border-color: var(--purple-200, #c4b5fd);
}

.tag-user-defined {
  background-color: var(--emerald-100, #d1fae5);
  color: var(--emerald-700, #047857);
  border-color: var(--emerald-200, #a7f3d0);
}

.tag-unknown {
  background-color: var(--gray-100, #f3f4f6);
  color: var(--gray-700, #374151);
  border-color: var(--gray-200, #e5e7eb);
}

.tag-more {
  background-color: var(--gray-100, #f3f4f6);
  color: var(--gray-600, #4b5563);
  border-color: var(--gray-200, #e5e7eb);
  font-style: italic;
}

.no-tags {
  color: var(--text-quaternary, #94a3b8);
  font-style: italic;
}

/* Dark mode support for tags */
[data-theme="dark"] .tag-predefined {
  background-color: var(--primary-900, #1e3a8a);
  color: var(--primary-200, #bfdbfe);
  border-color: var(--primary-700, #1d4ed8);
}

[data-theme="dark"] .tag-ai-suggested {
  background-color: var(--purple-900, #581c87);
  color: var(--purple-200, #c4b5fd);
  border-color: var(--purple-700, #7c3aed);
}

[data-theme="dark"] .tag-user-defined {
  background-color: var(--emerald-900, #064e3b);
  color: var(--emerald-200, #a7f3d0);
  border-color: var(--emerald-700, #047857);
}

[data-theme="dark"] .tag-unknown,
[data-theme="dark"] .tag-more {
  background-color: var(--gray-800, #1f2937);
  color: var(--gray-200, #e5e7eb);
  border-color: var(--gray-600, #4b5563);
}

[data-theme="dark"] .no-tags {
  color: var(--text-quaternary-dark, #6b7280);
}

/* Dark mode support for status badges */
[data-theme="dark"] .status-badge-new {
  background-color: #451a03;
  color: #fef3c7;
  border-color: #f59e0b;
}

[data-theme="dark"] .status-badge-todo {
  background-color: #1e3a8a;
  color: #dbeafe;
  border-color: #3b82f6;
}

[data-theme="dark"] .status-badge-done {
  background-color: #064e3b;
  color: #d1fae5;
  border-color: #10b981;
}

/* ===== SEVERITY BADGE COLOR SYSTEM ===== */

/* CSS Custom Properties for Severity Colors - used by JavaScript for dynamic contrast calculation */
:root {
  --severity-low: #10b981;
  --severity-medium: #f59e0b;
  --severity-high: #ef4444;
  --severity-critical: #dc2626;
  --severity-fallback: #6b7280;
}

/* Base severity badge styling - colors applied dynamically for accessibility */
.severity-badge {
  font-weight: 700;
  border: 1px solid transparent;
  /* Background color, text color, and border color are set dynamically via inline styles
     to ensure proper contrast ratios for accessibility compliance */
}

</style>