import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import VerifyEmailView from '../VerifyEmailView.vue';
import { <PERSON>ard, NSpin, NButton, NResult } from 'naive-ui';
import { nextTick } from 'vue';

vi.mock('axios', () => ({
  default: {
    get: vi.fn(() => Promise.resolve({ data: {} }))
  },
  isAxiosError: () => false
}));

vi.mock('vue-router', () => ({
  useRoute: () => ({ query: { token: 'test-token' } }),
  useRouter: () => ({ push: vi.fn() })
}));

describe('VerifyEmailView.vue', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state initially', async () => {
    const wrapper = mount(VerifyEmailView, {
      global: { components: { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>on, NR<PERSON>ult } }
    });
    expect(wrapper.text()).toContain('Verifying your email');
  });

  it('shows success message after verification', async () => {
    const wrapper = mount(VerifyEmailView, {
      global: { components: { NCard, NSpin, NButton, NResult } }
    });
    await flushPromises();
    await nextTick();
    expect(wrapper.text()).toContain('Email Verified!');
  });
});
