import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { nextTick } from 'vue'; // Import nextTick from vue directly
import { createP<PERSON>, setActivePinia } from 'pinia';
import axios, { type AxiosError } from 'axios'; // <-- Import AxiosError type
import type { Pinia } from 'pinia';

import LoginView from '../LoginView.vue';
import { useAuthStore } from '@/stores/auth';

// --- Mocking Dependencies ---
vi.mock('axios', () => {
  const mockAxiosInstance = {
    // Define methods that an Axios instance returned by axios.create() might have.
    // These are not directly tested here but make the mock more complete for apiClient.
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
    head: vi.fn(),
    options: vi.fn(),
    request: vi.fn(),
    interceptors: {
      request: { use: vi.fn().mockReturnValue(1), eject: vi.fn() }, // mockReturnValue to mimic returning an interceptor ID
      response: { use: vi.fn().mockReturnValue(1), eject: vi.fn() },
    },
    defaults: {
      headers: {
        common: {},
        post: {},
        get: {},
      },
    },
  };

  const axiosDefaultMock = {
    // This is for `axios.create()`
    create: vi.fn(() => mockAxiosInstance),
    // This is for `axios.post()` directly, as used in the test
    post: vi.fn(),
    // This is for `axios.isAxiosError()`
    isAxiosError: vi.fn(), // Implementation will be set in tests as needed
    // Add other static axios methods if your codebase uses them directly (e.g., axios.get)
    get: vi.fn(),
    all: vi.fn(promises => Promise.all(promises)),
    spread: vi.fn(callback => (...args: any[]) => callback(...args)),
  };

  return {
    __esModule: true, // Important for ES module interop
    default: axiosDefaultMock,
    // If you use named exports like `import { isAxiosError } from 'axios'`, mock them here.
    // This test file uses `axios.isAxiosError`, so the one on `default` is primary.
    isAxiosError: axiosDefaultMock.isAxiosError,
  };
});

const mockedAxiosPost = axios.post as any; // This will now correctly point to default.post from the mock
const mockedAxiosIsAxiosError = axios.isAxiosError as any; // This will now correctly point to default.isAxiosError from the mock
// --- End isAxiosError mock ---

const mockRouterPush = vi.fn();
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockRouterPush,
  }),
  RouterLink: { template: '<a><slot /></a>' }
}));

// --- Remove Naive UI Stubs ---
// We will interact with the actual components rendered

describe('LoginView.vue', () => {
  let pinia: Pinia;

  beforeEach(() => {
    pinia = createPinia();
    setActivePinia(pinia);
    vi.clearAllMocks();
    // Reset mocks before each test
    mockedAxiosPost.mockReset();
    mockedAxiosIsAxiosError.mockReset(); // <-- Reset the spy
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const mountComponent = () => {
    return mount(LoginView, {
      global: {
        plugins: [pinia],
        // Remove Naive UI stubs - let the real components render (shallowly)
        stubs: {
          RouterLink: true // Keep non-Naive UI stubs if needed
        },
      },
    });
  };

  it('renders the login form correctly', () => {
    const wrapper = mountComponent();
    // console.log(wrapper.html()); // Keep for debugging if needed

    // Use class selectors based on actual rendered HTML
    expect(wrapper.find('.n-card').exists()).toBe(true);
    expect(wrapper.find('form.n-form').exists()).toBe(true);

    // Find the wrapper divs for inputs, then the actual input element inside
    const inputWrappers = wrapper.findAll('.n-input');
    expect(inputWrappers.length).toBe(2); // Assuming two n-input components
    expect(inputWrappers[0].find('input').exists()).toBe(true);
    expect(inputWrappers[1].find('input[type="password"]').exists()).toBe(true); // Be more specific for password

    // Find the button by its class
    const button = wrapper.find('button.n-button');
    expect(button.exists()).toBe(true);
    expect(button.text()).toContain('Login'); // Use contain if there's extra whitespace/elements

    // Alert should not exist initially
    expect(wrapper.find('.n-alert').exists()).toBe(false);
    expect(wrapper.text()).toContain("Don't have an account?");
  });

  it('calls login API and store action on successful submission', async () => {
    const wrapper = mountComponent();
    const authStore = useAuthStore();
    const loginSpy = vi.spyOn(authStore, 'login');

    const testEmail = '<EMAIL>';
    const testPassword = 'password123';
    const mockToken = 'mock-jwt-token';
    const mockUser = { id: '1', email: testEmail };

    vi.mocked(axios.post).mockResolvedValue({
      data: { token: mockToken, user: mockUser },
    });

    // Find native input elements within the .n-input wrappers
    const inputWrappers = wrapper.findAll('.n-input');
    const emailInput = inputWrappers[0].find('input');
    const passwordInput = inputWrappers[1].find('input[type="password"]'); // More specific

    await emailInput.setValue(testEmail);
    await passwordInput.setValue(testPassword);

    // Find the form and trigger submit
    await wrapper.find('form.n-form').trigger('submit');
    await flushPromises();

    expect(axios.post).toHaveBeenCalledTimes(1);
    expect(axios.post).toHaveBeenCalledWith('/api/auth/login', {
      email: testEmail,
      password: testPassword,
    });
    expect(loginSpy).toHaveBeenCalledTimes(1);
    expect(loginSpy).toHaveBeenCalledWith(mockToken, mockUser);
    expect(mockRouterPush).toHaveBeenCalledTimes(1);
    expect(mockRouterPush).toHaveBeenCalledWith('/');
    expect(wrapper.find('.n-alert').exists()).toBe(false);
  });

  it('displays an error message on failed API call', async () => {
    const wrapper = mountComponent();
    const authStore = useAuthStore();
    const loginSpy = vi.spyOn(authStore, 'login');

    // --- Mock formRef.validate to resolve successfully ---
    // Ensure formRef is exposed via defineExpose in LoginView.vue
    const instance = wrapper.vm as any;
    let validateSpy;
    if (instance.formRef && typeof instance.formRef.validate === 'function') {
        // Mock validate to resolve (simulate successful validation)
        validateSpy = vi.spyOn(instance.formRef, 'validate').mockResolvedValue(undefined);
    } else {
        console.warn("Could not find 'formRef.validate' on component instance to mock.");
        // Optionally fail the test if mocking is crucial and fails
        // throw new Error("formRef.validate could not be mocked");
    }
    // --- End formRef mock ---


    const testEmail = '<EMAIL>';
    const testPassword = 'wrongpassword';
    const errorMessage = 'Invalid credentials';

    const mockAxiosError = {
      isAxiosError: true,
      response: {
        data: { error: errorMessage },
        status: 401,
        statusText: 'Unauthorized',
        headers: {},
        config: {},
      },
      message: 'Request failed',
      name: 'AxiosError',
      code: 'ERR_BAD_REQUEST',
    } as AxiosError;

    mockedAxiosPost.mockRejectedValue(mockAxiosError);
    mockedAxiosIsAxiosError.mockImplementation((payload: any): boolean => {
      return payload && payload.isAxiosError === true;
    });

    const inputWrappers = wrapper.findAll('.n-input');
    await inputWrappers[0].find('input').setValue(testEmail);
    await inputWrappers[1].find('input[type="password"]').setValue(testPassword);

    await wrapper.find('form.n-form').trigger('submit');
    await flushPromises(); // Allow promises from validate() and axios.post() to settle
    await nextTick(); // Allow Vue reactivity updates

    // --- Verify validate was called ---
    if (validateSpy) {
        expect(validateSpy).toHaveBeenCalled();
    }
    // --- End verification ---

    expect(mockedAxiosPost).toHaveBeenCalledTimes(1); // Now axios.post should be called
    expect(mockedAxiosIsAxiosError).toHaveBeenCalled(); // This should now pass

    expect(loginSpy).not.toHaveBeenCalled();
    expect(mockRouterPush).not.toHaveBeenCalled();

    // Access error ref directly via instance
    expect(instance.error).toBe(errorMessage);

    const alert = wrapper.find('.n-alert');
    expect(alert.exists()).toBe(true);
    expect(alert.text()).toContain(errorMessage);
  });

   it('prevents submission if form validation fails (basic check)', async () => {
    const wrapper = mountComponent();
    const authStore = useAuthStore();
    const loginSpy = vi.spyOn(authStore, 'login');

    // --- Mocking Validation ---
    // This requires the component instance (`wrapper.vm`) to expose the form ref.
    const instance = wrapper.vm as any;
    let validateMock: ReturnType<typeof vi.spyOn> | undefined;

    if (instance.formRef && typeof instance.formRef.validate === 'function') {
        validateMock = vi.spyOn(instance.formRef, 'validate').mockImplementation(
            // Use a simpler function signature that's compatible with Vitest's typing
            function(this: unknown, ...args: unknown[]): Promise<void> {
                // Extract the callback from args if it exists
                const callback = args[0] as ((errors?: any[] | null) => void) | undefined;
                 const errors = [ // Define some mock errors
                    { field: 'email', message: 'Email is required' },
                    { field: 'password', message: 'Password is required' }
                 ];
                 // Call the callback if provided
                 if (callback) {
                     callback(errors);
                 }
                 // Return a rejected promise to simulate validation failure
                 return Promise.reject(errors);
            }
        );
    } else {
        console.warn("Could not find 'formRef.validate' on component instance to mock. Ensure formRef is exposed via defineExpose in LoginView.vue.");
    }

    // --- Trigger Form Submission ---
    const form = wrapper.find('form');
    await form.trigger('submit.prevent'); // Use .prevent if needed

    // --- Assertions ---
    // Expect the login action NOT to have been called because validation failed
    expect(loginSpy).not.toHaveBeenCalled();

    // Optional: Assert that validateMock was called
    expect(validateMock).toHaveBeenCalled();

    // Clean up the spy if it was created
    if (validateMock) {
        validateMock.mockRestore();
    }
}); // End of 'prevents submission if form validation fails' test
}); // Closing brace for the main describe block