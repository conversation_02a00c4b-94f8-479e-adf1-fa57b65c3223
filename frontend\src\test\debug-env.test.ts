import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import DebugReportButtonEnhanced from '../components/DebugReportButtonEnhanced.vue'
import { createI18n } from 'vue-i18n'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en: {
      debug: {
        reportIssue: 'Report Issue'
      }
    }
  }
})

// Mock all the necessary modules
vi.mock('@vicons/tabler', () => ({
  Bug: { name: 'Bug', render: () => 'bug-icon' },
  InfoCircle: { name: 'InfoCircle', render: () => 'info-icon' },
  Bulb: { name: 'Bulb', render: () => 'lightbulb-icon' },
  Bolt: { name: 'Bolt', render: () => 'zap-icon' },
  Palette: { name: 'Palette', render: () => 'palette-icon' },
  TrendingUp: { name: 'TrendingUp', render: () => 'trending-up-icon' },
  QuestionMark: { name: 'QuestionMark', render: () => 'help-circle-icon' },
  Check: { name: 'Check', render: () => 'check-icon' }
}))

// Don't mock Naive UI here - let setup.ts handle it

vi.mock('../composables/useClientLogger', () => ({
  useClientLogger: vi.fn(() => ({
    logUserAction: vi.fn(),
    sendDebugReport: vi.fn().mockResolvedValue({ reportId: 'test-123' }),
    sendLogsToServer: vi.fn().mockResolvedValue({ reportId: 'test-123' }),
    getLogCount: vi.fn(() => 5),
    getUserActions: vi.fn(() => [
      { action: 'test-action', timestamp: Date.now() }
    ])
  }))
}))

describe('Debug Environment Test', () => {  it('should show environment variables and component rendering', async () => {
    const fs = require('fs')
    const output: string[] = []
    
    output.push('=== Environment Variables ===')
    output.push(`import.meta.env: ${JSON.stringify((globalThis as any).import?.meta?.env)}`)
    output.push(`import.meta.env.DEV: ${(globalThis as any).import?.meta?.env?.DEV}`)
    output.push(`import.meta.env.VITE_ENABLE_DEBUG_REPORT: ${(globalThis as any).import?.meta?.env?.VITE_ENABLE_DEBUG_REPORT}`)
    
    const wrapper = mount(DebugReportButtonEnhanced, {
      global: {
        plugins: [i18n, createTestingPinia()]
      }
    })

    output.push('=== Component Rendering ===')
    output.push(`Component HTML: ${wrapper.html()}`)
    output.push(`Find button by data-testid: ${wrapper.find('[data-testid="n-button"]').exists()}`)
    output.push(`Find all buttons: ${wrapper.findAll('button').length}`)
    output.push(`Component text: ${wrapper.text()}`)

    // Write to temp file
    fs.writeFileSync('c:/temp/debug-output.txt', output.join('\n'))

    expect(wrapper.exists()).toBe(true)
  })
})
