import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createPinia, setActivePinia } from 'pinia';
import { useAuthStore } from '@/stores/auth';

// Mock centralized socket manager
vi.mock('@/services/centralizedSocketManager', () => ({
  default: {
    initializeSocket: vi.fn(),
    disconnect: vi.fn(),
    forceReconnect: vi.fn(),
    isInAuthErrorState: vi.fn(() => false),
  },
}));

// Mock notification store
vi.mock('@/stores/notificationStore', () => ({
  useNotificationStore: () => ({
    initializeNotificationSystem: vi.fn(() => Promise.resolve(true)),
    cleanupNotificationSystem: vi.fn(),
  }),
}));

// Mock API client
vi.mock('@/services/apiClient', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn(),
  },
}));

describe('JWT Token Parsing with jwt-decode', () => {
  let authStore: ReturnType<typeof useAuthStore>;

  beforeEach(() => {
    setActivePinia(createPinia());
    authStore = useAuthStore();
    // Clear localStorage
    localStorage.clear();
    vi.clearAllMocks();
  });

  it('should correctly parse a valid JWT token and determine refresh timing', () => {
    // Create a mock JWT token that expires in 15 minutes (900 seconds)
    const futureTimestamp = Math.floor(Date.now() / 1000) + 900; // 15 minutes from now
    const mockPayload = { exp: futureTimestamp, sub: 'user123' };
    const mockToken = `header.${btoa(JSON.stringify(mockPayload))}.signature`;

    authStore.token = mockToken;

    // Should NOT need refresh (expires in 15 minutes, buffer is 10 minutes)
    expect(authStore.shouldRefreshToken()).toBe(false);
  });

  it('should determine token needs refresh when expiring within buffer time', () => {
    // Create a mock JWT token that expires in 5 minutes (300 seconds)
    const futureTimestamp = Math.floor(Date.now() / 1000) + 300; // 5 minutes from now
    const mockPayload = { exp: futureTimestamp, sub: 'user123' };
    const mockToken = `header.${btoa(JSON.stringify(mockPayload))}.signature`;

    authStore.token = mockToken;

    // Should need refresh (expires in 5 minutes, buffer is 10 minutes)
    expect(authStore.shouldRefreshToken()).toBe(true);
  });

  it('should handle malformed tokens gracefully', () => {
    // Test with completely invalid token
    authStore.token = 'invalid-token';
    expect(authStore.shouldRefreshToken()).toBe(true);

    // Test with token missing parts
    authStore.token = 'header.only';
    expect(authStore.shouldRefreshToken()).toBe(true);

    // Test with token with invalid base64
    authStore.token = 'header.!!!invalid-base64!!!.signature';
    expect(authStore.shouldRefreshToken()).toBe(true);
  });

  it('should handle tokens without expiration gracefully', () => {
    // Create a mock JWT token without exp field
    const mockPayload = { sub: 'user123', iat: Math.floor(Date.now() / 1000) };
    const mockToken = `header.${btoa(JSON.stringify(mockPayload))}.signature`;

    authStore.token = mockToken;

    // Should need refresh when no expiration is present
    expect(authStore.shouldRefreshToken()).toBe(true);
  });

  it('should return false when no token is present', () => {
    authStore.token = null;
    expect(authStore.shouldRefreshToken()).toBe(false);

    authStore.token = '';
    expect(authStore.shouldRefreshToken()).toBe(false);
  });

  it('should handle expired tokens correctly', () => {
    // Create a mock JWT token that expired 5 minutes ago
    const pastTimestamp = Math.floor(Date.now() / 1000) - 300; // 5 minutes ago
    const mockPayload = { exp: pastTimestamp, sub: 'user123' };
    const mockToken = `header.${btoa(JSON.stringify(mockPayload))}.signature`;

    authStore.token = mockToken;

    // Should need refresh (token is already expired)
    expect(authStore.shouldRefreshToken()).toBe(true);
  });
});
