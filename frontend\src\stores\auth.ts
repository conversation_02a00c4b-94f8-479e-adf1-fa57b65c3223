import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { jwtDecode } from 'jwt-decode';
import apiClient from '@/services/apiClient'; // import your axios instance
import centralizedSocketManager from '@/services/centralizedSocketManager'; // import centralized socket manager
import { useNotificationStore } from '@/stores/notificationStore'; // Import notification store
import type { PaymentReceivingInfo } from '@/types/payerNegotiation'; // Adjust path if PaymentReceivingInfo is elsewhere

// Define the structure of the user object we store
interface UserInfo {
  id: string;
  email: string;
  emailVerified?: boolean; // Make optional if not always present initially
  phoneNumber?: string | null; // Add phone number
  phoneVerified?: boolean; // Add phone verification status
  username?: string; // Username (may be email or separate field)
  createdAt?: string; // Date when account was created
  reputationScore?: number; // Raw reputation score
  reputationLevel?: number; // Calculated level (1-5)
  profile?: { // Added nested profile object
    avatarUrl?: string | null;
    // defaultPaymentReceivingInfo could also be nested here if backend sends it nested
  } | null;
  defaultPaymentReceivingInfo?: PaymentReceivingInfo | null; // Added this line at the root
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('authToken'));
  const storedUserJson = localStorage.getItem('userInfo');
  const user = ref<UserInfo | null>(storedUserJson ? JSON.parse(storedUserJson) : null);  // Store timeout ID for socket connection retry to enable cleanup
  let socketRetryTimeoutId: number | null = null;
  
  // Flag to track socket initialization state and prevent duplicate connections
  let isSocketInitialized = false;

  const isAuthenticated = computed(() => {
    const auth = !!token.value && !!user.value;
    console.log(`🔍 [AuthStore] isAuthenticated computed: ${auth} (token: ${!!token.value}, user: ${!!user.value})`);
    return auth;
  });
  // Function to clear pending socket retry timeout
  function clearSocketRetryTimeout() {
    if (socketRetryTimeoutId) {
      clearTimeout(socketRetryTimeoutId);
      socketRetryTimeoutId = null;
      console.log('[AuthStore] Cleared pending socket retry timeout');
    }
  }

  // Function to reset socket initialization state
  function resetSocketState() {
    clearSocketRetryTimeout();
    isSocketInitialized = false;
    console.log('[AuthStore] Reset socket initialization state');
  }  function login(newToken: string, newUserInfo: UserInfo) {
    token.value = newToken;
    user.value = newUserInfo; // newUserInfo from backend should now include defaultPaymentReceivingInfo
    localStorage.setItem('authToken', newToken);
    localStorage.setItem('userInfo', JSON.stringify(newUserInfo));

    // Socket connection will be automatically initialized by the authentication state watcher
    console.log('[AuthStore] User logged in, socket connection will be initialized by state watcher');
    
    // Note: Notification initialization will be handled by the socket initialization process
    // This ensures proper timing and avoids race conditions
  }
  // Separate method for socket initialization that can be called from different places
  function initializeSocketConnection() {
    if (!isAuthenticated.value) {
      console.warn('[AuthStore] Cannot initialize socket: User not authenticated');
      return;
    }

    if (isSocketInitialized) {
      console.log('[AuthStore] Socket already initialized, skipping duplicate initialization');
      return;
    }

    console.log('[AuthStore] Initializing socket connection...');
    isSocketInitialized = true;

    // Add retry logic for socket initialization after authentication
    const maxRetries = 3;
    let retryCount = 0;    const attemptConnection = async () => {
      try {
        await centralizedSocketManager.initializeSocket();
        console.log('[AuthStore] Socket connection initialized successfully');
          // Initialize notification system after socket is ready
        try {
          console.log('🔔 [AuthStore] About to get notification store instance...');
          const notificationStore = useNotificationStore();
          console.log('🔔 [AuthStore] Got notification store instance:', notificationStore);
          console.log('🔔 [AuthStore] About to call initializeNotificationSystem...');
          const notificationInitialized = await notificationStore.initializeNotificationSystem();
          console.log('🔔 [AuthStore] initializeNotificationSystem returned:', notificationInitialized);
          if (notificationInitialized) {
            console.log('[AuthStore] Notification system initialized successfully');
          } else {
            console.warn('[AuthStore] Notification system initialization failed');
          }
        } catch (notificationError) {
          console.error('[AuthStore] Failed to initialize notification system:', notificationError);
        }
      } catch (error) {
        console.error(`[AuthStore] Failed to initialize socket (attempt ${retryCount + 1}/${maxRetries}):`, error);// Check if this is an authentication error
        if (centralizedSocketManager.isInAuthErrorState()) {
          console.warn('[AuthStore] Socket authentication failed - user may need to re-login');
          isSocketInitialized = false; // Reset flag on auth error
          // Don't automatically logout here as the user might still have a valid session
          // Let the API interceptor handle this when they make their next API call
          return;
        }

        // Retry if not an auth error and we haven't exceeded max retries
        if (retryCount < maxRetries - 1) {
          retryCount++;
          const delay = Math.min(1000 * Math.pow(2, retryCount), 5000); // Exponential backoff, max 5s
          console.log(`[AuthStore] Retrying socket connection in ${delay}ms...`);
          socketRetryTimeoutId = window.setTimeout(attemptConnection, delay);
        } else {
          console.error('[AuthStore] Max socket connection retries exceeded');
          isSocketInitialized = false; // Reset flag on permanent failure
        }
      }
    };

    attemptConnection();
  }  function logout() {
    // Clear any pending socket retry timeout and reset socket state
    resetSocketState();
    
    // Cleanup notification system before clearing auth state
    try {
      const notificationStore = useNotificationStore();
      notificationStore.cleanupNotificationSystem();
      console.log('[AuthStore] Notification system cleaned up');
    } catch (error) {
      console.error('[AuthStore] Error cleaning up notification system:', error);
    }
    
    // Clear reactive state first
    token.value = null;
    user.value = null;
    
    // Clear localStorage
    localStorage.removeItem('authToken');
    localStorage.removeItem('userInfo');
    
    // Disconnect socket and clean up listeners to prevent memory leaks
    // Wrap in try-catch to ensure logout completes even if socket disconnect fails
    try {
      centralizedSocketManager.disconnect();
    } catch (error) {
      console.error('[AuthStore] Error during socket disconnect:', error);
      // Continue with logout even if socket disconnect fails
    }
    
    // Navigation should be handled in the component
  }

  // Function to initialize state from localStorage on app load
  function initializeAuth() {
    const storedToken = localStorage.getItem('authToken');
    const storedUser = localStorage.getItem('userInfo');
    if (storedToken && storedUser) {
      token.value = storedToken;
      try {
        user.value = JSON.parse(storedUser) as UserInfo; // Cast to ensure type
      } catch (e) {
        console.error('Error parsing stored user info:', e);
        // Clear invalid stored data
        logout();
      }
    } else {
      // Ensure state is clear if nothing is stored
      token.value = null;
      user.value = null;
    }
  }

  // Action to update phone verification status in the store
  function updatePhoneVerificationStatus(status: boolean, phoneNumber?: string) {
    if (user.value) {
      user.value = {
        ...user.value,
        phoneVerified: status,
        // Optionally update phone number if provided and different
        ...(phoneNumber && { phoneNumber: phoneNumber }),
      };
      // Update localStorage as well
      localStorage.setItem('userInfo', JSON.stringify(user.value));
    }
  }  // fetchUserProfile action
  async function fetchUserProfile() {
    try {
      // apiClient.get<UserInfo> will expect the backend to return the UserInfo structure
      const response = await apiClient.get<UserInfo>('/auth/me');
      user.value = response.data; // This will now include defaultPaymentReceivingInfo if sent by backend
      localStorage.setItem('userInfo', JSON.stringify(user.value));
      return user.value;
    } catch (e) {
      console.error('fetchUserProfile error', e);
      // Potentially logout if 401, though apiClient might handle this
      throw e;
    }
  }

  // Token refresh action
  async function refreshToken() {
    try {
      console.log('[AuthStore] Attempting to refresh token...');
      const response = await apiClient.post<{ token: string }>('/auth/refresh');
      
      const newToken = response.data.token;
      token.value = newToken;
      localStorage.setItem('authToken', newToken);
      
      console.log('[AuthStore] Token refreshed successfully');
      
      // Force socket reconnection with new token
      if (isAuthenticated.value) {
        console.log('[AuthStore] Forcing socket reconnection with new token');
        try {
          await centralizedSocketManager.forceReconnect();
        } catch (error) {
          console.warn('[AuthStore] Failed to reconnect socket after token refresh:', error);
        }
      }
      
      return newToken;
    } catch (error) {
      console.error('[AuthStore] Token refresh failed:', error);
      // If refresh fails, logout the user
      logout();
      throw error;
    }
  }

  // Check if token needs refresh (call this before making API requests)
  function shouldRefreshToken(): boolean {
    if (!token.value) return false;
    
    try {
      // Use jwt-decode library for secure token parsing
      const payload = jwtDecode<{ exp: number }>(token.value);
      
      if (!payload.exp) {
        console.warn('[AuthStore] Token does not contain expiration time');
        return true; // If no expiration, assume we need to refresh
      }
      
      const expirationTime = payload.exp * 1000; // Convert to milliseconds
      const currentTime = Date.now();
      const timeUntilExpiry = expirationTime - currentTime;
      
      // Refresh if token expires within 10 minutes (increased from 5 minutes)
      // This provides better buffer for long-running operations
      const refreshBufferTime = 10 * 60 * 1000;
      
      console.log(`[AuthStore] Token expiry check: ${Math.floor(timeUntilExpiry / 1000 / 60)} minutes until expiration`);
      
      return timeUntilExpiry < refreshBufferTime;
    } catch (error) {
      console.error('[AuthStore] Error parsing token for expiration check:', error);
      // If we can't parse the token, it's likely malformed or invalid
      return true; // Trigger refresh/re-authentication
    }
  }  // Watch for authentication state changes and manage socket connection
  watch(
    () => isAuthenticated.value,
    (newAuth, oldAuth) => {
      console.log(`🔍 [AuthStore] Authentication state changed: ${oldAuth} -> ${newAuth}`);
      console.log(`🔍 [AuthStore] Current token: ${token.value ? 'exists' : 'null'}`);
      console.log(`🔍 [AuthStore] Current user: ${user.value ? user.value.email : 'null'}`);
      console.log(`🔍 [AuthStore] isSocketInitialized: ${isSocketInitialized}`);

      if (newAuth && (!oldAuth || oldAuth === newAuth)) {
        // User is authenticated (either just authenticated or was already authenticated on store init)
        console.log('🔥 [AuthStore] User authenticated, initializing socket connection');
        initializeSocketConnection();      } else if (!newAuth && oldAuth) {
        // User just logged out - disconnect socket and clear any pending retries
        console.log('[AuthStore] User logged out, disconnecting socket and cleaning up');
        
        // Cleanup notification system
        try {
          const notificationStore = useNotificationStore();
          notificationStore.cleanupNotificationSystem();
          console.log('[AuthStore] Notification system cleaned up on logout');
        } catch (error) {
          console.error('[AuthStore] Error cleaning up notification system on logout:', error);
        }
        
        resetSocketState(); // Clear any pending retry timeout and reset socket state
        try {
          centralizedSocketManager.disconnect();
        } catch (error) {
          console.error('[AuthStore] Error disconnecting socket on logout:', error);
        }
      }    },
    { immediate: true } // Change to true to run immediately on store creation
  );  return {
    token,
    user,
    isAuthenticated,
    login,
    logout,
    initializeAuth,
    initializeSocketConnection, // Add the new method
    updatePhoneVerificationStatus, // Expose the new action
    fetchUserProfile, // expose it here
    refreshToken, // Add the new token refresh method
    shouldRefreshToken, // Add the token expiration check method
    clearSocketRetryTimeout, // Expose cleanup function for component teardown
    resetSocketState, // Expose socket state reset function
  };
});
