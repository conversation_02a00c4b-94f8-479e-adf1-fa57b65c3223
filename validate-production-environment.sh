#!/bin/bash

# PRE-DEPLOYMENT VALIDATION SCRIPT
# =================================
# Run this script to verify your production environment is ready for deployment

echo "🔍 Pre-Deployment Validation"
echo "============================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Track validation status
validation_passed=true

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to validate result
validate() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        validation_passed=false
    fi
}

echo "📋 Checking system prerequisites..."

# Check Docker
if command_exists docker; then
    validate 0 "Docker is installed"
    
    # Check if Docker daemon is running
    if systemctl is-active --quiet docker 2>/dev/null || docker info >/dev/null 2>&1; then
        validate 0 "Docker daemon is running"
    else
        validate 1 "Docker daemon is running"
        echo -e "${YELLOW}💡 Start with: sudo systemctl start docker${NC}"
    fi
else
    validate 1 "Docker is installed"
    echo -e "${YELLOW}💡 Install with: sudo dnf install docker${NC}"
fi

# Check Docker Compose
if command_exists docker-compose; then
    validate 0 "Docker Compose is installed"
elif docker compose version >/dev/null 2>&1; then
    validate 0 "Docker Compose plugin is installed"
else
    validate 1 "Docker Compose is installed"
    echo -e "${YELLOW}💡 Install with: sudo dnf install docker-compose-plugin${NC}"
fi

# Check PostgreSQL access via Docker
if command_exists docker; then
    # Check if we can access postgres container
    if docker ps --format "{{.Names}}" | grep -q postgres; then
        validate 0 "PostgreSQL container is accessible via Docker"
    else
        validate 1 "PostgreSQL container is running"
        echo -e "${YELLOW}💡 Start with: docker-compose up -d postgres${NC}"
    fi
else
    validate 1 "Docker is available for database access"
fi

echo ""
echo "📄 Checking environment configuration..."

# Check .env file exists
if [ -f .env ]; then
    validate 0 ".env file exists"
    
    # Load environment variables
    set -a
    source .env
    set +a
    
    # Check required environment variables
    required_vars=("DATABASE_URL" "GEMINI_API_KEY" "VITE_ADMIN_EMAILS" "VITE_ENABLE_DEBUG_REPORT" "CLIENT_LOG_DIRECTORY" "JWT_SECRET")
    
    for var in "${required_vars[@]}"; do
        if [ -n "${!var}" ]; then
            validate 0 "$var is set"
        else
            validate 1 "$var is set"
        fi
    done
    
    # Check if admin emails are properly formatted
    if [[ "$VITE_ADMIN_EMAILS" == *"@"* ]]; then
        validate 0 "Admin emails contain valid format"
    else
        validate 1 "Admin emails contain valid format"
    fi
    
    # Check if Gemini API key looks valid (starts with AIza)
    if [[ "$GEMINI_API_KEY" == AIza* ]]; then
        validate 0 "Gemini API key format looks valid"
    else
        validate 1 "Gemini API key format looks valid"
    fi
    
else
    validate 1 ".env file exists"
fi

echo ""
echo "� Checking Docker database connectivity..."

# Test database connection using Docker if DATABASE_URL is set
if [ -n "$DATABASE_URL" ]; then
    # Check if postgres container is running
    if docker ps --format "{{.Names}}" | grep -q postgres; then
        POSTGRES_CONTAINER=$(docker ps --format "{{.Names}}" | grep postgres | head -1)
        
        # Test connection via Docker container
        if docker exec $POSTGRES_CONTAINER psql -U "${POSTGRES_USER:-postgres}" -d "${POSTGRES_DB:-postgres}" -c 'SELECT 1;' >/dev/null 2>&1; then
            validate 0 "Database is accessible via Docker"
        else
            validate 1 "Database is accessible via Docker"
            echo -e "${YELLOW}💡 Check your database environment variables${NC}"
        fi
    else
        validate 1 "PostgreSQL container is running"
        echo -e "${YELLOW}💡 Start with: docker-compose up -d postgres${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  DATABASE_URL not set, skipping database connectivity test${NC}"
fi

echo ""
echo "📁 Checking required files..."

# Check if migration script exists
if [ -f "migrate-production-safe.sh" ]; then
    validate 0 "Migration script (migrate-production-safe.sh) exists"
    
    # Check if it's executable
    if [ -x "migrate-production-safe.sh" ]; then
        validate 0 "Migration script is executable"
    else
        validate 1 "Migration script is executable"
        echo -e "${YELLOW}💡 Make executable with: chmod +x migrate-production-safe.sh${NC}"
    fi
else
    validate 1 "Migration script (migrate-production-safe.sh) exists"
fi

# Check if deployment script exists
if [ -f "deploy-production-centos9.sh" ]; then
    validate 0 "Deployment script (deploy-production-centos9.sh) exists"
    
    # Check if it's executable
    if [ -x "deploy-production-centos9.sh" ]; then
        validate 0 "Deployment script is executable"
    else
        validate 1 "Deployment script is executable"
        echo -e "${YELLOW}💡 Make executable with: chmod +x deploy-production-centos9.sh${NC}"
    fi
else
    validate 1 "Deployment script (deploy-production-centos9.sh) exists"
fi

# Check if docker-compose.yml exists
if [ -f "docker-compose.yml" ]; then
    validate 0 "docker-compose.yml exists"
else
    validate 1 "docker-compose.yml exists"
fi

echo ""
echo "🎯 Validation Summary"
echo "===================="

if [ "$validation_passed" = true ]; then
    echo -e "${GREEN}🎉 All validations passed! You're ready for deployment.${NC}"
    echo ""
    echo -e "${GREEN}Next steps:${NC}"
    echo "1. Run: ./migrate-production-safe.sh"
    echo "2. Run: ./deploy-production-centos9.sh"
    echo "3. Test the debug report feature"
    exit 0
else
    echo -e "${RED}❌ Some validations failed. Please fix the issues above before deployment.${NC}"
    echo ""
    echo -e "${YELLOW}Common fixes:${NC}"
    echo "• Install missing packages: sudo dnf install docker docker-compose-plugin postgresql"
    echo "• Start Docker: sudo systemctl start docker"
    echo "• Make scripts executable: chmod +x *.sh"
    echo "• Check your .env file for missing variables"
    exit 1
fi
