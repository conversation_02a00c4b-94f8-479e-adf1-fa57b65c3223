# Vue Component Integration Examples

This document shows how to integrate the new visual design system with existing MUNygo Vue components and Naive UI.

## 🔄 Naive UI Theme Integration

Update your theme store to work with the new design system:

### Enhanced Theme Store
```typescript
// frontend/src/stores/theme.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { darkTheme, lightTheme, type GlobalThemeOverrides } from 'naive-ui';

export const useThemeStore = defineStore('theme', () => {
  const isDark = ref(true);

  // NEW: Enhanced theme overrides matching our design system
  const lightThemeOverrides: GlobalThemeOverrides = {
    common: {
      // Primary colors from our design system
      primaryColor: '#334155',        // --blue-700
      primaryColorHover: '#1E293B',   // --blue-800
      primaryColorPressed: '#0F172A', // --blue-900
      
      // Success colors
      successColor: '#22C55E',        // --success-500
      successColorHover: '#16A34A',   // --success-600
      
      // Warning colors
      warningColor: '#F59E0B',        // --warning-500
      warningColorHover: '#D97706',   // --warning-600
      
      // Error colors
      errorColor: '#EF4444',          // --error-500
      errorColorHover: '#DC2626',     // --error-600
      
      // Background colors
      bodyColor: '#F9FAFB',           // --gray-50
      cardColor: '#FFFFFF',           // White cards
      modalColor: '#FFFFFF',
      popoverColor: '#FFFFFF',
      
      // Text colors
      textColorBase: '#111827',       // --gray-900
      textColor1: '#1F2937',          // --gray-800
      textColor2: '#374151',          // --gray-700
      textColor3: '#6B7280',          // --gray-500
      textColorDisabled: '#9CA3AF',   // --gray-400
      
      // Border colors
      borderColor: '#E5E7EB',         // --gray-200
      dividerColor: '#E5E7EB',
      
      // Professional font family
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      
      // Border radius
      borderRadius: '8px',
      borderRadiusMedium: '12px',
      borderRadiusLarge: '16px',
    },
    Button: {
      // Enhanced button styling
      heightMedium: '56px',           // Mobile-friendly height
      heightLarge: '64px',
      paddingMedium: '0 24px',
      paddingLarge: '0 32px',
      fontWeightStrong: '600',        // Semibold for better visibility
      borderRadius: '8px',
    },
    Input: {
      // Enhanced input styling
      heightMedium: '56px',           // Mobile-friendly
      heightLarge: '64px',
      paddingMedium: '16px',
      paddingLarge: '20px',
      fontSize: '16px',               // Prevents iOS zoom
      borderRadius: '8px',
      color: '#FFFFFF',
      colorFocus: '#FFFFFF',
      border: '2px solid #E5E7EB',
      borderHover: '2px solid #D1D5DB',
      borderFocus: '2px solid #334155',
    },
    Card: {
      borderRadius: '12px',
      paddingMedium: '20px',
      paddingLarge: '24px',
    },
    Modal: {
      borderRadius: '16px',
      padding: '24px',
    },
    Message: {
      borderRadius: '8px',
      padding: '16px 20px',
    }
  };

  const darkThemeOverrides: GlobalThemeOverrides = {
    common: {
      // Adapted colors for dark mode
      primaryColor: '#60A5FA',        // Brighter for dark backgrounds
      primaryColorHover: '#3B82F6',
      primaryColorPressed: '#2563EB',
      
      // Success colors for dark
      successColor: '#4ADE80',
      successColorHover: '#22C55E',
      
      // Warning colors for dark
      warningColor: '#FBBF24',
      warningColorHover: '#F59E0B',
      
      // Error colors for dark
      errorColor: '#F87171',
      errorColorHover: '#EF4444',
      
      // OLED-optimized backgrounds
      bodyColor: '#000000',           // True black
      cardColor: '#0A0A0A',           // Slightly elevated
      modalColor: '#000000',
      popoverColor: '#111111',
      
      // High contrast text
      textColorBase: 'rgba(255, 255, 255, 0.95)',
      textColor1: 'rgba(255, 255, 255, 0.90)',
      textColor2: 'rgba(255, 255, 255, 0.80)',
      textColor3: 'rgba(255, 255, 255, 0.60)',
      textColorDisabled: 'rgba(255, 255, 255, 0.40)',
      
      // Subtle borders for dark mode
      borderColor: 'rgba(255, 255, 255, 0.2)',
      dividerColor: 'rgba(255, 255, 255, 0.1)',
      
      // Same professional font
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    },
    Button: {
      heightMedium: '56px',
      heightLarge: '64px',
      paddingMedium: '0 24px',
      paddingLarge: '0 32px',
      fontWeightStrong: '600',
      borderRadius: '8px',
    },
    Input: {
      heightMedium: '56px',
      heightLarge: '64px',
      paddingMedium: '16px',
      paddingLarge: '20px',
      fontSize: '16px',
      borderRadius: '8px',
      color: '#1A1A1A',
      colorFocus: '#1A1A1A',
      border: '2px solid rgba(255, 255, 255, 0.2)',
      borderHover: '2px solid rgba(255, 255, 255, 0.3)',
      borderFocus: '2px solid #60A5FA',
    },
    Card: {
      borderRadius: '12px',
      paddingMedium: '20px',
      paddingLarge: '24px',
    }
  };

  // Rest of your existing theme store logic...
  const themeOverrides = computed(() => 
    isDark.value ? darkThemeOverrides : lightThemeOverrides
  );

  return {
    isDark,
    naiveTheme: computed(() => isDark.value ? darkTheme : lightTheme),
    themeOverrides,
    toggleTheme,
    initializeTheme
  };
});
```

## 🔘 Enhanced Button Components

### Updated OfferForm.vue Buttons
```vue
<template>
  <!-- Primary action button with new design system -->
  <n-button
    type="primary"
    size="large"
    block
    :loading="isSubmitting"
    @click="handleSubmit"
    class="btn-enhanced"
  >
    <template #icon>
      <n-icon><PlusOutline /></n-icon>
    </template>
    {{ isEditing ? $t('offers.updateOffer') : $t('offers.createOffer') }}
  </n-button>

  <!-- Secondary action button -->
  <n-button
    size="large"
    block
    @click="handleCancel"
    class="btn-enhanced btn-secondary"
  >
    {{ $t('common.cancel') }}
  </n-button>
</template>

<style scoped>
.btn-enhanced {
  /* Apply our design system styling */
  min-height: 56px !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  font-size: 16px !important;
}

.btn-secondary {
  margin-top: 12px;
}

/* Mobile-specific enhancements */
@media (max-width: 768px) {
  .btn-enhanced {
    min-height: 56px !important;
  }
}
</style>
```

## 📋 Enhanced Card Components

### Updated OfferCard.vue with New Design System
```vue
<template>
  <n-card 
    :hoverable="true"
    class="offer-card-enhanced"
    @click="handleCardClick"
  >
    <!-- Card header with user info -->
    <template #header>
      <div class="card-header-enhanced">
        <div class="user-info">
          <n-avatar 
            :size="40"
            :src="offer.user.profilePicture"
            class="user-avatar"
          >
            {{ offer.user.firstName?.[0] }}{{ offer.user.lastName?.[0] }}
          </n-avatar>
          <div class="user-details">
            <p class="user-name">{{ offer.user.firstName }} {{ offer.user.lastName }}</p>
            <div class="user-reputation">
              <ReputationIcon :reputation="offer.user.reputation" />
              <span class="reputation-text">{{ $t('common.reputation') }}: {{ offer.user.reputation }}</span>
            </div>
          </div>
        </div>
        <div class="offer-type-badge">
          <n-tag 
            :type="offer.type === 'sell' ? 'success' : 'info'"
            size="small"
            class="type-tag"
          >
            {{ $t(`offers.${offer.type}`) }}
          </n-tag>
        </div>
      </div>
    </template>

    <!-- Card content with exchange details -->
    <div class="exchange-details">
      <div class="currency-pair">
        <div class="currency-from">
          <span class="currency-label">{{ $t('offers.from') }}</span>
          <div class="currency-info">
            <span class="currency-code">{{ offer.fromCurrency }}</span>
            <span class="amount financial-data">{{ formatCurrency(offer.fromAmount) }}</span>
          </div>
        </div>
        
        <div class="exchange-arrow">
          <n-icon size="20" class="arrow-icon">
            <ArrowRightOutline />
          </n-icon>
        </div>
        
        <div class="currency-to">
          <span class="currency-label">{{ $t('offers.to') }}</span>
          <div class="currency-info">
            <span class="currency-code">{{ offer.toCurrency }}</span>
            <span class="amount financial-data">{{ formatCurrency(offer.toAmount) }}</span>
          </div>
        </div>
      </div>

      <div class="exchange-rate">
        <span class="rate-label">{{ $t('offers.rate') }}</span>
        <span class="rate-value financial-data">1 {{ offer.fromCurrency }} = {{ offer.rate }} {{ offer.toCurrency }}</span>
      </div>
    </div>

    <!-- Card footer with actions -->
    <template #footer>
      <div class="card-actions">
        <n-button
          type="primary"
          size="medium"
          @click.stop="handleExpressInterest"
          :loading="isExpressingInterest"
          class="action-btn"
        >
          <template #icon>
            <n-icon><HeartOutline /></n-icon>
          </template>
          {{ $t('offers.expressInterest') }}
        </n-button>
        
        <n-button
          size="medium"
          @click.stop="handleViewDetails"
          class="action-btn secondary"
        >
          {{ $t('offers.viewDetails') }}
        </n-button>
      </div>
    </template>
  </n-card>
</template>

<style scoped>
.offer-card-enhanced {
  /* Apply our design system */
  border-radius: 12px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: 1px solid var(--border-subtle) !important;
  box-shadow: var(--shadow-sm) !important;
}

.offer-card-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md) !important;
}

.card-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.user-reputation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reputation-text {
  font-size: 14px;
  color: var(--text-secondary);
}

.offer-type-badge {
  flex-shrink: 0;
}

.type-tag {
  font-weight: 600 !important;
}

.exchange-details {
  padding: 20px 0;
}

.currency-pair {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.currency-from,
.currency-to {
  flex: 1;
  text-align: center;
}

.currency-label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 8px;
}

.currency-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.currency-code {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-primary);
}

.amount {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.exchange-arrow {
  padding: 0 16px;
  color: var(--color-accent);
}

.arrow-icon {
  transform: rotate(0deg);
  transition: transform 0.2s ease;
}

.offer-card-enhanced:hover .arrow-icon {
  transform: rotate(5deg);
}

.exchange-rate {
  text-align: center;
  padding: 16px;
  background: var(--bg-elevated);
  border-radius: 8px;
  border: 1px solid var(--border-subtle);
}

.rate-label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-tertiary);
  margin-bottom: 4px;
}

.rate-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-accent);
}

.card-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  min-height: 48px !important;
  font-weight: 600 !important;
}

.action-btn.secondary {
  background: transparent !important;
  border: 2px solid var(--color-primary) !important;
  color: var(--color-primary) !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .currency-pair {
    flex-direction: column;
    gap: 16px;
  }
  
  .exchange-arrow {
    transform: rotate(90deg);
    padding: 8px 0;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .action-btn {
    min-height: 56px !important;
  }
}

/* Financial data styling */
.financial-data {
  font-family: var(--font-mono);
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}
</style>
```

## 📝 Enhanced Form Components

### Updated OfferForm.vue with New Design System
```vue
<template>
  <n-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    class="offer-form-enhanced"
    label-placement="top"
    size="large"
  >
    <!-- Amount input with enhanced styling -->
    <n-form-item 
      :label="$t('offers.fromAmount')" 
      path="fromAmount"
      class="form-item-enhanced"
    >
      <n-input-number
        v-model:value="formData.fromAmount"
        :placeholder="$t('offers.enterAmount')"
        :min="1"
        :step="1"
        class="amount-input"
        size="large"
      >
        <template #suffix>
          <span class="currency-suffix">{{ formData.fromCurrency }}</span>
        </template>
      </n-input-number>
    </n-form-item>

    <!-- Currency selectors with enhanced styling -->
    <div class="currency-selector-row">
      <n-form-item 
        :label="$t('offers.fromCurrency')" 
        path="fromCurrency"
        class="form-item-enhanced currency-item"
      >
        <n-select
          v-model:value="formData.fromCurrency"
          :options="currencyOptions"
          :placeholder="$t('offers.selectCurrency')"
          size="large"
          class="currency-select"
        />
      </n-form-item>

      <div class="exchange-icon">
        <n-icon size="24" class="swap-icon">
          <SwapHorizontalOutline />
        </n-icon>
      </div>

      <n-form-item 
        :label="$t('offers.toCurrency')" 
        path="toCurrency"
        class="form-item-enhanced currency-item"
      >
        <n-select
          v-model:value="formData.toCurrency"
          :options="currencyOptions"
          :placeholder="$t('offers.selectCurrency')"
          size="large"
          class="currency-select"
        />
      </n-form-item>
    </div>

    <!-- Exchange rate display -->
    <div class="rate-display" v-if="calculatedRate">
      <div class="rate-card">
        <span class="rate-label">{{ $t('offers.exchangeRate') }}</span>
        <span class="rate-value financial-data">
          1 {{ formData.fromCurrency }} = {{ calculatedRate }} {{ formData.toCurrency }}
        </span>
      </div>
    </div>

    <!-- Description with enhanced styling -->
    <n-form-item 
      :label="$t('offers.description')" 
      path="description"
      class="form-item-enhanced"
    >
      <n-input
        v-model:value="formData.description"
        type="textarea"
        :placeholder="$t('offers.descriptionPlaceholder')"
        :autosize="{ minRows: 3, maxRows: 6 }"
        size="large"
        class="description-input"
      />
    </n-form-item>

    <!-- Action buttons -->
    <div class="form-actions">
      <n-button
        type="primary"
        size="large"
        block
        :loading="isSubmitting"
        @click="handleSubmit"
        class="submit-btn"
      >
        <template #icon>
          <n-icon><CheckmarkOutline /></n-icon>
        </template>
        {{ isEditing ? $t('offers.updateOffer') : $t('offers.createOffer') }}
      </n-button>

      <n-button
        size="large"
        block
        @click="handleCancel"
        class="cancel-btn"
      >
        {{ $t('common.cancel') }}
      </n-button>
    </div>
  </n-form>
</template>

<style scoped>
.offer-form-enhanced {
  max-width: 600px;
  margin: 0 auto;
  padding: 24px;
}

.form-item-enhanced {
  margin-bottom: 24px;
}

.form-item-enhanced :deep(.n-form-item-label) {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: var(--text-primary) !important;
  margin-bottom: 8px !important;
}

.amount-input,
.currency-select,
.description-input {
  border-radius: 8px !important;
}

.amount-input :deep(.n-input-number-input) {
  font-family: var(--font-mono) !important;
  font-variant-numeric: tabular-nums !important;
  font-size: 18px !important;
  font-weight: 600 !important;
}

.currency-suffix {
  font-weight: 600;
  color: var(--color-primary);
  font-size: 14px;
}

.currency-selector-row {
  display: flex;
  align-items: end;
  gap: 16px;
  margin-bottom: 24px;
}

.currency-item {
  flex: 1;
  margin-bottom: 0 !important;
}

.exchange-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--color-accent);
  border-radius: 50%;
  color: white;
  margin-bottom: 6px;
}

.swap-icon {
  transition: transform 0.2s ease;
}

.exchange-icon:hover .swap-icon {
  transform: rotate(180deg);
}

.rate-display {
  margin: 24px 0;
}

.rate-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: var(--bg-elevated);
  border: 1px solid var(--border-subtle);
  border-radius: 12px;
}

.rate-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
}

.rate-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-accent);
}

.form-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 32px;
}

.submit-btn,
.cancel-btn {
  min-height: 56px !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
}

.cancel-btn {
  background: transparent !important;
  border: 2px solid var(--border-standard) !important;
  color: var(--text-secondary) !important;
}

.cancel-btn:hover {
  border-color: var(--border-emphasis) !important;
  color: var(--text-primary) !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .offer-form-enhanced {
    padding: 20px;
  }
  
  .currency-selector-row {
    flex-direction: column;
    gap: 20px;
  }
  
  .exchange-icon {
    align-self: center;
    transform: rotate(90deg);
  }
  
  .rate-card {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Financial data styling */
.financial-data {
  font-family: var(--font-mono);
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}
</style>
```

## 🏠 Enhanced Navigation Components

### Updated NavBar.vue with New Design System
```vue
<template>
  <nav class="navbar-enhanced">
    <div class="navbar-container">
      <!-- Logo section -->
      <div class="navbar-brand">
        <router-link to="/" class="brand-link">
          <div class="logo-container">
            <n-icon size="32" class="logo-icon">
              <CurrencyExchangeOutline />
            </n-icon>
            <span class="brand-text">MUNygo</span>
          </div>
        </router-link>
      </div>

      <!-- Desktop navigation -->
      <div class="navbar-nav desktop-nav">
        <router-link 
          v-for="item in navigationItems" 
          :key="item.path"
          :to="item.path" 
          class="nav-link"
          :class="{ 'nav-link-active': isCurrentRoute(item.path) }"
        >
          <n-icon size="20">
            <component :is="item.icon" />
          </n-icon>
          <span>{{ $t(item.label) }}</span>
        </router-link>
      </div>

      <!-- User actions -->
      <div class="navbar-actions">
        <ThemeToggle class="theme-toggle" />
        <LanguageSelector class="language-selector" />
        <NotificationBell class="notification-bell" />
        
        <!-- User menu -->
        <n-dropdown 
          :options="userMenuOptions" 
          @select="handleUserMenuSelect"
          class="user-dropdown"
        >
          <n-button circle size="large" class="user-avatar-btn">
            <template #icon>
              <n-avatar 
                :size="32"
                :src="user?.profilePicture"
                class="user-avatar"
              >
                {{ user?.firstName?.[0] }}{{ user?.lastName?.[0] }}
              </n-avatar>
            </template>
          </n-button>
        </n-dropdown>
      </div>

      <!-- Mobile menu button -->
      <n-button 
        class="mobile-menu-btn visible-mobile"
        @click="toggleMobileMenu"
        size="large"
        circle
      >
        <template #icon>
          <n-icon size="24">
            <MenuOutline v-if="!isMobileMenuOpen" />
            <CloseOutline v-else />
          </n-icon>
        </template>
      </n-button>
    </div>

    <!-- Mobile navigation drawer -->
    <n-drawer
      v-model:show="isMobileMenuOpen"
      placement="right"
      :width="280"
      class="mobile-nav-drawer"
    >
      <n-drawer-content 
        :title="$t('navigation.menu')"
        class="mobile-nav-content"
      >
        <div class="mobile-nav-items">
          <router-link 
            v-for="item in navigationItems" 
            :key="item.path"
            :to="item.path" 
            class="mobile-nav-link"
            :class="{ 'mobile-nav-link-active': isCurrentRoute(item.path) }"
            @click="closeMobileMenu"
          >
            <n-icon size="24" class="mobile-nav-icon">
              <component :is="item.icon" />
            </n-icon>
            <span class="mobile-nav-text">{{ $t(item.label) }}</span>
          </router-link>
        </div>
        
        <div class="mobile-nav-footer">
          <div class="mobile-nav-controls">
            <ThemeToggle />
            <LanguageSelector />
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>
  </nav>
</template>

<style scoped>
.navbar-enhanced {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-subtle);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 72px;
  max-width: 1280px;
  margin: 0 auto;
}

.navbar-brand {
  flex-shrink: 0;
}

.brand-link {
  text-decoration: none;
  color: inherit;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  color: var(--color-primary);
}

.brand-text {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.desktop-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  transition: all 0.2s ease;
  min-height: 48px;
}

.nav-link:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.nav-link-active {
  background: var(--color-primary);
  color: white;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.theme-toggle,
.language-selector,
.notification-bell {
  flex-shrink: 0;
}

.user-avatar-btn {
  background: transparent !important;
  border: 2px solid var(--border-subtle) !important;
}

.user-avatar-btn:hover {
  border-color: var(--color-primary) !important;
}

.mobile-menu-btn {
  background: transparent !important;
  border: 2px solid var(--border-subtle) !important;
}

.mobile-nav-drawer {
  z-index: 1000;
}

.mobile-nav-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px 0;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  border-radius: 8px;
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  transition: all 0.2s ease;
  min-height: 56px;
}

.mobile-nav-link:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.mobile-nav-link-active {
  background: var(--color-primary);
  color: white;
}

.mobile-nav-icon {
  flex-shrink: 0;
}

.mobile-nav-text {
  font-size: 16px;
}

.mobile-nav-footer {
  margin-top: auto;
  padding: 20px 0;
  border-top: 1px solid var(--border-subtle);
}

.mobile-nav-controls {
  display: flex;
  justify-content: center;
  gap: 16px;
}

/* Hide desktop nav on mobile */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 16px;
    height: 64px;
  }
  
  .desktop-nav,
  .navbar-actions .theme-toggle,
  .navbar-actions .language-selector {
    display: none;
  }
  
  .navbar-actions {
    gap: 8px;
  }
}

/* Hide mobile elements on desktop */
@media (min-width: 769px) {
  .mobile-menu-btn {
    display: none;
  }
}
</style>
```

## 🚀 Implementation Checklist

### Phase 1: Core System
- [ ] Update `theme.ts` with new color system
- [ ] Replace main CSS with design system foundation
- [ ] Test light/dark mode switching
- [ ] Verify mobile responsiveness

### Phase 2: Component Updates
- [ ] Update button components with new classes
- [ ] Enhance card components with new styling
- [ ] Update form components with new design
- [ ] Test component interactions

### Phase 3: Navigation & Layout
- [ ] Update navigation with new design
- [ ] Enhance mobile navigation drawer
- [ ] Test responsive behavior
- [ ] Verify accessibility

### Phase 4: Testing & Polish
- [ ] Test on actual mobile devices
- [ ] Verify touch target sizes (44px minimum)
- [ ] Check color contrast ratios
- [ ] Test with screen readers
- [ ] Verify reduced motion support

This integration guide provides practical examples of how to apply the new design system to real MUNygo components while maintaining Naive UI compatibility and mobile-first principles.
