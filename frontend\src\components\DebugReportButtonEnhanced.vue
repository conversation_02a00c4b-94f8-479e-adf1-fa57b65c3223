<template>
  <div>    <!-- Debug Report Button -->
    <n-button
      type="info"
      size="small"
      @click="handleModalOpen"
      :disabled="isLoading"
      v-if="isDevelopment"
      :aria-label="t('debug.reportIssue')"
      circle
    >
      <template #icon>
        <n-icon><BugIcon /></n-icon>
      </template>
    </n-button><!-- Enhanced Report Modal -->
    <n-modal 
      v-model:show="showModal" 
      preset="dialog" 
      style="width: 95%; max-width: 800px;"
      :mask-closable="false"
      :close-on-esc="true"
    >
      <template #header>
        <div class="flex items-center gap-2">
          <n-icon size="20"><BugIcon /></n-icon>
          <span>{{ t('debug.reportIssue') }}</span>
        </div>
      </template>
      
      <div class="space-y-6">
        <!-- AI Voice Recording Section - Moved to top for better UX -->
        <div class="ai-voice-section">
          <div class="flex items-center justify-between mb-3">
            <label class="block text-sm font-medium">{{ t('voice.recordBugReport') }}</label>
            <n-tag v-if="aiAnalysis.isAvailable" type="success" size="small">
              {{ t('voice.aiEnabled') }}
            </n-tag>
            <n-tag v-else type="warning" size="small">
              {{ t('voice.aiDisabled') }}
            </n-tag>
          </div>

          <div class="voice-recorder-container">            <VoiceRecorder
              ref="voiceRecorderRef"
              :language="currentLanguage"
              :auto-process="true"
              :predefined-tags="predefinedTagsForVoice"
              @transcription-complete="handleTranscriptionComplete"
              @analysis-complete="handleAnalysisComplete"
              @processing-error="handleVoiceError"
            />
          </div>

          <!-- AI Analysis Results -->
          <div v-if="aiAnalysis.hasTranscription || aiAnalysis.hasGeneratedReport" class="ai-results mt-4">
            <!-- Transcription Display -->
            <div v-if="aiAnalysis.hasTranscription" class="transcription-result mb-3">
              <label class="block text-sm font-medium mb-2">{{ t('voice.transcription') }}</label>
              <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border">
                <p class="text-sm">{{ aiAnalysis.transcription }}</p>
              </div>
            </div>

            <!-- AI Generated Report Preview -->
            <div v-if="aiAnalysis.hasGeneratedReport" class="ai-report-preview">
              <div class="flex items-center justify-between mb-2">
                <label class="block text-sm font-medium">{{ t('voice.aiGeneratedReport') }}</label>
                <div class="flex items-center gap-2">
                  <n-tag type="info" size="small">
                    {{ t('voice.confidence', { confidence: confidencePercentage }) }}
                  </n-tag>
                  <n-tag type="success" size="small">
                    {{ t('voice.appliedToForm') }}
                  </n-tag>
                </div>
              </div>

              <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                <div class="space-y-2 text-sm">
                  <div><strong>{{ t('debug.title') }}:</strong> {{ generatedReportTitle }}</div>
                  <div><strong>{{ t('debug.reportType') }}:</strong> {{ generatedReportTypeLabel }}</div>
                  <div><strong>{{ t('debug.severityText') }}:</strong> {{ generatedReportSeverityLabel }}</div>                  <div><strong>{{ t('debug.description') }}:</strong> {{ generatedReportDescription }}</div>                  <div v-if="generatedReportTags?.length" class="ai-tags-container">
                    <div class="ai-tags-label">
                      <strong>{{ t('debug.tagsText') }}:</strong>
                    </div>                    <div class="ai-tags-list">
                      <n-tag 
                        v-for="tag in generatedReportTags" 
                        :key="getTagDisplayName(tag)" 
                        size="small"
                        :type="getTagOriginType(tag)"
                      >
                        {{ getTagDisplayName(tag) }}
                        <n-icon v-if="tag.origin === 'PREDEFINED'" size="12" class="ml-1">
                          <component :is="CheckIcon" />
                        </n-icon>
                        <n-icon v-else-if="tag.origin === 'AI_SUGGESTED'" size="12" class="ml-1">
                          <component :is="LightbulbIcon" />
                        </n-icon>
                      </n-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>        <!-- Report Type Selection -->
        <div>
          <label class="block text-sm font-medium mb-3">{{ t('debug.reportType') }}</label>
          
          <!-- Option 1: Dropdown Style -->
          <div class="mb-3">
            <n-select
              v-model:value="reportForm.type"
              :options="reportTypeDropdownOptions"
              :placeholder="t('debug.selectReportType')"
              @update:value="handleTypeChange"
              class="w-full"
            />
          </div>
            <!-- Option 2: Grid Cards (Enhanced Visual Selection) -->
          <div class="text-xs text-gray-500 mb-2">{{ t('debug.orSelectFromCards') }}:</div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div
              v-for="type in reportTypes"
              :key="type.value"
              @click="handleTypeChange(type.value)"
              :class="[                'p-4 border-2 rounded-lg cursor-pointer transition-all duration-300 transform hover:scale-[1.02]',
                reportForm.type === type.value
                  ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 shadow-xl ring-4 ring-blue-300 dark:ring-blue-600 scale-[1.03] selected-card'
                  : 'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600 hover:shadow-md'
              ]"
            >
              <div class="flex items-center gap-3">                <div :class="[
                  'p-2 rounded-full transition-all',
                  reportForm.type === type.value
                    ? 'bg-blue-500 text-white shadow-md icon-glow'
                    : 'bg-gray-100 dark:bg-gray-700'
                ]">
                  <n-icon :size="20" :color="reportForm.type === type.value ? 'white' : type.color">
                    <component :is="getTypeIcon(type.icon)" />
                  </n-icon>
                </div>
                <div class="flex-1">
                  <div :class="[
                    'font-medium transition-colors',
                    reportForm.type === type.value
                      ? 'text-blue-700 dark:text-blue-300'
                      : 'text-gray-900 dark:text-gray-100'
                  ]">{{ type.label }}</div>
                  <div :class="[
                    'text-sm transition-colors',
                    reportForm.type === type.value
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-600 dark:text-gray-400'
                  ]">{{ type.description }}</div>
                </div>                <div v-if="reportForm.type === type.value" class="text-blue-500">
                  <div class="bg-blue-500 text-white rounded-full p-1.5 shadow-xl pulse-animation">
                    <n-icon size="18"><CheckIcon /></n-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Severity Level -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.severityText') }}</label>
          <n-select
            v-model:value="reportForm.severity"
            :options="severityOptions"
            :placeholder="t('debug.selectSeverity')"
            @update:value="handleFormInput"
          />        </div>        <!-- Enhanced Tag Selection -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.tags.selectTags') }}</label>            <TagSelectorV2
            :key="tagSelectorKey"
            v-model:selected-tag-ids="selectedTagIds"
            :report-type="reportForm.type"
            :debug-report-data="debugReportData"
            :ai-suggested-tags="generatedReportTags"
            :allow-custom-tags="true"
            :enable-ai-suggestions="true"
            :max-tags="15"
            :max-custom-tags="8"
            :compact="false"
            @tags-changed="onTagsChanged"
            @custom-tag-added="onCustomTagAdded"
          />
        </div>

        <!-- Auto-save notification and manual save -->
        <div class="space-y-2">
          <!-- Auto-save status -->
          <div v-if="formDrafts.hasDrafts || formDrafts.lastAutoSaveAt" class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                <n-icon size="16"><InfoIcon /></n-icon>
                <span class="text-sm">{{ t('debug.autoSaveEnabled') }}</span>
              </div>
              <n-button size="tiny" type="primary" ghost @click="handleManualSave" :disabled="!isFormValid">
                {{ t('debug.saveNow') }}
              </n-button>
            </div>
            <div v-if="lastAutoSaveTime" class="text-xs text-blue-600 dark:text-blue-400 mt-1">
              Last saved: {{ formatTime(lastAutoSaveTime) }}
            </div>
          </div>

          <!-- Save error notification -->
          <div v-if="formDrafts.lastSaveError" class="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800">
            <div class="flex items-center gap-2 text-red-700 dark:text-red-300">
              <n-icon size="16"><InfoIcon /></n-icon>
              <span class="text-sm">{{ formDrafts.lastSaveError }}</span>
            </div>
          </div>
        </div>

        <!-- Title -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.title') }}</label>
          <n-input
            v-model:value="reportForm.title"
            :placeholder="t('debug.titlePlaceholder')"
            clearable
            @input="handleFormInput"
          />
        </div>        <!-- Description -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.description') }}</label>
          <n-input
            v-model:value="reportForm.description"
            type="textarea"
            :placeholder="t('debug.descriptionPlaceholder')"
            :rows="4"
            clearable
            @input="handleFormInput"
          />
        </div>

        <!-- Dynamic Fields Based on Report Type -->
        <div v-if="reportForm.type === 'bug'">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium mb-2">{{ t('debug.stepsToReproduce') }}</label>
              <n-input
                v-model:value="reportForm.stepsToReproduce"
                type="textarea"
                :placeholder="t('debug.stepsPlaceholder')"
                :rows="3"
                clearable
                @input="handleFormInput"
              />
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">{{ t('debug.expectedBehavior') }}</label>
              <n-input
                v-model:value="reportForm.expectedBehavior"
                type="textarea"
                :placeholder="t('debug.expectedPlaceholder')"
                :rows="2"
                clearable
                @input="handleFormInput"
              />
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">{{ t('debug.actualBehavior') }}</label>
              <n-input
                v-model:value="reportForm.actualBehavior"
                type="textarea"
                :placeholder="t('debug.actualPlaceholder')"
                :rows="2"
                clearable
                @input="handleFormInput"
              />            </div>
          </div>
        </div>

        <!-- Additional Notes -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.additionalNotes') }}</label>
          <n-input
            v-model:value="reportForm.additionalNotes"
            type="textarea"
            :placeholder="t('debug.additionalNotesPlaceholder')"
            :rows="2"
            clearable
            @input="handleFormInput"
          />
        </div>

        <!-- Context Information Display (Collapsible) -->
        <n-collapse>
          <n-collapse-item title="Context Information" name="context">
            <template #header>
              <div class="flex items-center gap-2">
                <n-icon><InfoIcon /></n-icon>
                <span>{{ t('debug.contextInfo') }}</span>
              </div>
            </template>
            <div class="pt-2">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>{{ t('debug.currentPage') }}:</strong>
                  <div class="text-gray-600 dark:text-gray-400 break-all">{{ currentUrl }}</div>
                </div>
                <div>
                  <strong>{{ t('debug.logEntries') }}:</strong>
                  <div class="text-gray-600 dark:text-gray-400">{{ logCount }} entries</div>
                </div>
                <div>
                  <strong>{{ t('debug.userActions') }}:</strong>
                  <div class="text-gray-600 dark:text-gray-400">{{ userActionCount }} recent actions</div>
                </div>
                <div>
                  <strong>{{ t('debug.viewport') }}:</strong>
                  <div class="text-gray-600 dark:text-gray-400">{{ viewport.width }}x{{ viewport.height }}</div>
                </div>
              </div>

              <!-- Recent User Actions Preview -->
              <div v-if="recentActions.length > 0" class="mt-4">
                <h5 class="font-medium mb-2">{{ t('debug.recentActions') }}:</h5>
                <div class="max-h-32 overflow-y-auto space-y-1">
                  <div
                    v-for="action in recentActions.slice(0, 5)"
                    :key="action.timestamp"
                    class="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded"
                  >
                    <div class="flex justify-between">
                      <span class="font-medium">{{ action.action }}</span>
                      <span class="text-gray-500">{{ formatTime(action.timestamp) }}</span>
                    </div>
                    <div v-if="action.details" class="text-gray-600 dark:text-gray-400 mt-1">
                      {{ JSON.stringify(action.details, null, 2).slice(0, 100) }}...
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </n-collapse-item>
        </n-collapse>      </div><template #action>
        <div class="debug-modal-actions">
          <!-- Status indicators - always at top on mobile -->
          <div class="status-section" v-if="!connectionStore.isConnected || offlineReports.hasOfflineReports">
            <div v-if="!connectionStore.isConnected" class="status-item offline">
              <n-icon size="16"><InfoIcon /></n-icon>
              <span>{{ t('debug.offlineMode') }}</span>
            </div>
            <div v-if="offlineReports.hasOfflineReports" class="status-item pending">
              <n-icon size="16"><InfoIcon /></n-icon>
              <span>{{ t('debug.offlineReportsCount', { count: offlineReportCount }) }}</span>
            </div>
          </div>

          <!-- Action buttons -->
          <div class="buttons-section">
            <div class="secondary-buttons">
              <n-button @click="showModal = false" :disabled="isLoading">
                {{ t('common.cancel') }}
              </n-button>
              <n-button
                @click="handleResetForm"
                :disabled="isLoading"
                type="warning"
                ghost
              >
                {{ t('debug.resetForm') }}
              </n-button>
            </div>
            <div class="primary-button">
              <n-button
                type="primary"
                @click="submitReport"
                :loading="isLoading"
                :disabled="!isFormValid"
              >
                {{ t('debug.sendReport') }}
              </n-button>
            </div>
          </div>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick, h } from 'vue';
import { useI18n } from 'vue-i18n';
import { useMessage, useDialog } from 'naive-ui';
import { useClientLogger } from '@/composables/useClientLogger';
import { useOfflineReports } from '@/composables/useOfflineReports';
import { useFormDrafts } from '@/composables/useFormDrafts';
import { useConnectionStore } from '@/stores/connection';
import { useAiAnalysisStore } from '@/stores/aiAnalysisStore';
import { useTagStore } from '@/stores/tagStore';
import type { ReportDetails, ReportSeverity, ReportTypeOption, ReportTagWithOrigin } from '@/types/logging';
import VoiceRecorder from '@/components/VoiceRecorder.vue';
import TagSelectorV2 from '@/components/TagSelectorV2.vue';
import {
  Bug as BugIcon,
  InfoCircle as InfoIcon,
  Bulb as LightbulbIcon,
  Bolt as ZapIcon,
  Palette as PaletteIcon,
  TrendingUp as TrendingUpIcon,
  QuestionMark as HelpCircleIcon,
  Check as CheckIcon
} from '@vicons/tabler';

const { t, locale } = useI18n();
const message = useMessage();
const dialog = useDialog();
const logger = useClientLogger();
const offlineReports = useOfflineReports();
const formDrafts = useFormDrafts();
const connectionStore = useConnectionStore();
const aiAnalysis = useAiAnalysisStore();
const tagStore = useTagStore();

// Component refs
const voiceRecorderRef = ref<InstanceType<typeof VoiceRecorder> | null>(null);

// Component state
const showModal = ref(false);
const isLoading = ref(false);
const currentUrl = ref((window as any).location?.href || '');

// Development environment check - show in dev OR if explicitly enabled
const isDevelopment = computed(() => {
  const devMode = import.meta.env.DEV;
  const debugEnabled = import.meta.env.VITE_ENABLE_DEBUG_REPORT === 'true';
  console.log('Debug Report Button - DEV:', devMode, 'VITE_ENABLE_DEBUG_REPORT:', import.meta.env.VITE_ENABLE_DEBUG_REPORT, 'Show button:', devMode || debugEnabled);
  return devMode || debugEnabled;
});

// Static report types data structure - no translation calls to avoid circular refs
const reportTypesData = [
  {
    value: 'bug',
    icon: 'bug',
    color: '#ef4444'
  },
  {
    value: 'feature-request',
    icon: 'lightbulb',
    color: '#3b82f6'
  },
  {
    value: 'performance',
    icon: 'zap',
    color: '#f59e0b'
  },
  {
    value: 'ui-ux',
    icon: 'palette',
    color: '#8b5cf6'
  },
  {
    value: 'improvement',
    icon: 'trending-up',
    color: '#10b981'
  },
  {
    value: 'question',
    icon: 'help-circle',
    color: '#6b7280'
  },
  {
    value: 'other',
    icon: 'more',
    color: '#6b7280'
  }
];

// Static severity options data
const severityData = [
  { value: 'low' as ReportSeverity, color: '#10b981' },
  { value: 'medium' as ReportSeverity, color: '#f59e0b' },
  { value: 'high' as ReportSeverity, color: '#ef4444' },
  { value: 'critical' as ReportSeverity, color: '#dc2626' }
];

// Create static refs that are populated once to avoid circular dependencies
const reportTypes = ref<ReportTypeOption[]>([]);
const severityOptions = ref<any[]>([]);

// Dropdown options for report types (computed to match n-select format)
const reportTypeDropdownOptions = computed(() => 
  reportTypes.value.map(type => ({
    label: type.label,
    value: type.value,
    icon: type.icon,
    color: type.color
  }))
);

// Populate the static data once when component is created
const initializeStaticData = () => {
  try {    reportTypes.value = reportTypesData.map(type => ({
      ...type,
      value: type.value as any, // Cast to proper ReportType 
      label: t(`debug.reportTypes.${type.value === 'feature-request' ? 'feature' : type.value === 'ui-ux' ? 'uiux' : type.value}`),
      description: t(`debug.reportTypes.${type.value === 'feature-request' ? 'feature' : type.value === 'ui-ux' ? 'uiux' : type.value}Description`),
      tags: [] // No hardcoded tags - tags are now managed through the TagSelector
    }));

    severityOptions.value = severityData.map(severity => ({
      ...severity,
      label: t(`debug.severity.${severity.value}`)
    }));
  } catch (error) {
    console.error('Error initializing static data:', error);    // Fallback: use original data without translations
    reportTypes.value = reportTypesData.map(type => ({
      ...type,
      value: type.value as any, // Cast to proper ReportType 
      label: type.value,
      description: type.value,
      tags: [] // No hardcoded tags - tags are now managed through the TagSelector
    }));
    
    severityOptions.value = severityData.map(severity => ({
      ...severity,
      label: severity.value
    }));
  }
};

// Form data
const reportForm = ref<ReportDetails>({
  type: 'bug',
  severity: 'medium',
  title: '',
  description: '',
  stepsToReproduce: '',
  expectedBehavior: '',
  actualBehavior: '',
  additionalNotes: '',
  reportTags: []
});

// Tag handling for new TagSelector
const selectedTagIds = ref<string[]>([]);
const tagSelectorKey = ref(0); // Key to force TagSelectorV2 re-render when needed

// Computed properties
const logCount = computed(() => logger.getLogCount());
const userActionCount = computed(() => logger.getUserActions().length);
const recentActions = computed(() => logger.getUserActions().slice(-10));

const viewport = computed(() => ({
  width: window.innerWidth,
  height: window.innerHeight
}));

const isFormValid = computed(() => {
  return reportForm.value.title.trim().length > 0 &&
         reportForm.value.description.trim().length > 0;
});

const currentLanguage = computed(() => {
  return locale.value === 'fa' ? 'fa' : 'en';
});

// Get all predefined tags from tag store for TagSelector component
const allPredefinedTags = computed(() => {
  return tagStore.activeTags || [];
});

// Transform tags for VoiceRecorder component (expects Record<string, string[]>)
const predefinedTagsForVoice = computed(() => {
  const tagsByCategory: Record<string, string[]> = {};
  tagStore.activeTags.forEach(tag => {
    const categoryName = tag.category?.name || 'General';
    if (!tagsByCategory[categoryName]) {
      tagsByCategory[categoryName] = [];
    }
    tagsByCategory[categoryName].push(tag.name);
  });
  return tagsByCategory;
});

// Safe computed property to avoid circular references in template
const lastAutoSaveTime = computed(() => {
  const timestamp = formDrafts.lastAutoSaveAt;
  return typeof timestamp === 'object' && 'value' in timestamp ? timestamp.value : timestamp;
});

// Safe computed properties for AI analysis to avoid circular references
const generatedReportTitle = computed(() => {
  const aiReport = aiAnalysis.generatedReport;
  return aiReport?.title || '';
});

const generatedReportTypeLabel = computed(() => {
  const aiReport = aiAnalysis.generatedReport;
  return getTypeLabel(aiReport?.type);
});

const generatedReportSeverityLabel = computed(() => {
  const aiReport = aiAnalysis.generatedReport;
  return getSeverityLabel(aiReport?.severity);
});

const generatedReportDescription = computed(() => {
  const aiReport = aiAnalysis.generatedReport;
  return aiReport?.description || '';
});

const generatedReportTags = computed(() => {
  const aiReport = aiAnalysis.generatedReport;
  const tags = aiReport?.reportTags || [];
  console.log('[DebugReportButtonEnhanced] Generated report tags:', tags);
  return tags;
});

// Tag handling methods for new TagSelector
function onTagsChanged(tags: { 
  id?: string;
  name?: string;
  origin: ReportTagWithOrigin['origin'];
}[]): void {
  console.log('Debug: onTagsChanged called with tags:', tags);
  console.log('Debug: onTagsChanged - current selectedTagIds before:', selectedTagIds.value);
  
  // Keep tags in the UI format (tagId/tagName) for consistent display
  reportForm.value.reportTags = tags.map(tag => {
    if (tag.origin === 'PREDEFINED') {
      // For predefined tags, preserve as tagId
      const result = {
        tagId: tag.id || tag.name || '',
        origin: tag.origin
      };
      console.log('Debug: onTagsChanged - mapped predefined tag:', tag, '->', result);
      return result;
    } else {
      // For custom and AI-suggested tags, preserve as tagName
      const result = {
        tagName: tag.name || '',
        origin: tag.origin
      };
      console.log('Debug: onTagsChanged - mapped custom/AI tag:', tag, '->', result);
      return result;
    }
  }).filter(tag => (tag as any).tagId || (tag as any).tagName); // Remove any tags with empty values
  
  console.log('Debug: Final reportForm.reportTags:', reportForm.value.reportTags);
    // Update selectedTagIds for predefined tags only (for UI display)
  selectedTagIds.value = tags
  .filter(tag => tag.origin === 'PREDEFINED')
    .map(tag => tag.id)
    .filter((id): id is string => id !== undefined);
  
  console.log('Debug: Updated selectedTagIds (predefined only):', selectedTagIds.value);
  
  // Trigger form input handling for auto-save
  handleFormInput();
}

// Handle custom tag addition from TagSelectorV2
function onCustomTagAdded(tag: { id: string; name: string }): void {
  console.log('Debug: Custom tag added:', tag);
  // Custom tags are automatically handled in the onTagsChanged event  // This handler is mainly for logging and potential future enhancements
}

// Safe computed property for offline report count
const offlineReportCount = computed(() => {
  // Properly unwrap reactive values to avoid circular reference errors
  const count = offlineReports.offlineReportCount;
  return typeof count === 'object' && 'value' in count ? count.value : count;
});

// Safe computed property for AI confidence percentage
const confidencePercentage = computed(() =>
  Math.round((aiAnalysis.confidence ?? 0) * 100)
)

// Safe computed property for AI confidence value
const confidenceValue = computed<number>(() =>
  Number(aiAnalysis.confidence) || 0
)

// Computed property for debug report data to avoid template window access
const debugReportData = computed(() => ({
  title: reportForm.value.title || '',
  description: reportForm.value.description || '',
  userAgent: typeof window !== 'undefined' ? window.navigator?.userAgent || '' : '',
  url: typeof window !== 'undefined' ? window.location?.href || '' : ''
}));

// Helper functions
const getTypeIcon = (iconName: string) => {
  const icons: Record<string, any> = {
    'bug': BugIcon,
    'lightbulb': LightbulbIcon,
    'zap': ZapIcon,
    'palette': PaletteIcon,
    'trending-up': TrendingUpIcon,
    'help-circle': HelpCircleIcon,
    'more': HelpCircleIcon
  };
  return icons[iconName] || BugIcon;
};

const formatTime = (timestamp: string | null) => {
  if (!timestamp) return '';
  return new Date(timestamp).toLocaleTimeString();
};

// Helper functions for tag origins and display
const getTagDisplayName = (tag: ReportTagWithOrigin | any): string => {
  // Handle both the expected ReportTagWithOrigin format and the actual format from AI API
  const displayName = tag.tagId || tag.tagName || tag.tag || '';
  if (!displayName) {
    console.warn('[DebugReportButtonEnhanced] Tag with no display name:', tag);
  }
  return displayName;
};

const getTagOriginType = (tag: ReportTagWithOrigin): 'default' | 'info' | 'success' => {
  const origin = tag.origin;
  switch (origin) {
    case 'PREDEFINED': return 'success';
    case 'AI_SUGGESTED': return 'info';
    default: return 'default';
  }
};

// Submit report with offline support
const submitReport = async () => {
  if (!isFormValid.value) {
    message.error(t('debug.formValidationError'));
    return;
  }

  isLoading.value = true;
  logger.logUserAction('debug-report-submit', {
    reportType: reportForm.value.type,
    reportSeverity: reportForm.value.severity,
    hasTags: reportForm.value.reportTags && reportForm.value.reportTags.length > 0,
    isOnline: connectionStore.isConnected
  });
  try {    // Transform tags from UI format (tagId/tagName) to backend format (tag) before submission
    const reportForSubmission = {
      ...reportForm.value,
      reportTags: (reportForm.value.reportTags || []).map(tag => ({
        tag: (tag as any).tagId || (tag as any).tagName || '',
        origin: tag.origin
      })).filter(tag => tag.tag) // Remove empty tags
    };

    // Always try to submit online first, regardless of socket connection status
    const response = await logger.sendLogsToServer(reportForSubmission);

    if (response.success) {
      message.success(t('debug.reportSentSuccess', { reportId: response.reportId }));
      showModal.value = false;
      resetForm();
      formDrafts.clearActiveDraft();
    } else if (response.isNetworkError) {      // Network error detected - store offline
      const reportPayload = {
        logs: logger.getLogs(),
        reportDetails: reportForSubmission, // Use transformed report
        timestamp: new Date().toISOString(),
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        diagnosticData: logger.captureDiagnosticData(),
        userIdentification: logger.captureUserIdentification()
      };

      offlineReports.addOfflineReport(reportPayload);
      message.info(t('debug.offlineReportStored'));

      showModal.value = false;
      resetForm();
      formDrafts.clearActiveDraft();
    } else {
      // Server error or validation error - show error message
      message.error(response.message || t('debug.reportSentError'));
    }
  } catch (error) {
    console.error('Debug report submission error:', error);

    // Check if this is a network error by trying to determine the error type
    const isNetworkIssue = !navigator.onLine ||
      (error instanceof Error && (
        error.message.includes('Network Error') ||
        error.message.includes('Failed to fetch') ||
        error.message.includes('ERR_NETWORK')
      ));    if (isNetworkIssue) {      // Store offline for network errors (create the transformed report payload here too)
      const reportPayloadForOffline = {
        ...reportForm.value,
        reportTags: (reportForm.value.reportTags || []).map(tag => ({
          tag: (tag as any).tagId || (tag as any).tagName || '',
          origin: tag.origin
        })).filter(tag => tag.tag)
      };

      const reportPayload = {
        logs: logger.getLogs(),
        reportDetails: reportPayloadForOffline,
        timestamp: new Date().toISOString(),
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        diagnosticData: logger.captureDiagnosticData(),
        userIdentification: logger.captureUserIdentification()
      };

      offlineReports.addOfflineReport(reportPayload);
      message.info(t('debug.offlineReportStored'));

      showModal.value = false;
      resetForm();
      formDrafts.clearActiveDraft();
    } else {
      // Other errors - show error message
      message.error(t('debug.reportSentError'));
    }
  } finally {
    isLoading.value = false;
  }
};

const resetForm = () => {
  // Clear any auto-save timer first to prevent saving empty form
  formDrafts.clearActiveDraft();
  
  reportForm.value = {
    type: 'bug',
    severity: 'medium',
    title: '',
    description: '',
    stepsToReproduce: '',
    expectedBehavior: '',
    actualBehavior: '',
    additionalNotes: '',
    reportTags: []
  };
};

// Handle reset form with confirmation
const handleResetForm = () => {
  dialog.warning({
    title: t('debug.resetForm'),
    content: t('debug.resetFormConfirm'),
    positiveText: t('common.confirm'),
    negativeText: t('common.cancel'),
    onPositiveClick: () => {
      resetForm();
      formDrafts.clearActiveDraft();
      message.success(t('debug.resetFormSuccess'));
      logger.logUserAction('debug-form-reset');
    }
  });
};

// Handle form input changes for auto-save
const handleFormInput = () => {
  formDrafts.autoSaveDraft(reportForm.value);
};

// Handle manual save
const handleManualSave = () => {
  if (isFormValid.value) {
    formDrafts.manualSaveDraft(reportForm.value);
    logger.logUserAction('debug-draft-manual-save');
  }
};

// Handle report type change
const handleTypeChange = (newType: string) => {
  reportForm.value.type = newType as any;
  handleFormInput();
  logger.logUserAction('debug-report-type-changed', { newType });
};

// Handle modal opening with simplified single draft restoration
const handleModalOpen = () => {
  showModal.value = true;
  currentUrl.value = window.location.href;

  // Check for existing draft - use nextTick to avoid recursive updates
  nextTick(() => {
    const hasDraftsValue = typeof formDrafts.hasDrafts === 'object' && 'value' in formDrafts.hasDrafts
      ? formDrafts.hasDrafts.value
      : formDrafts.hasDrafts;
      
    console.log('Modal opened - checking for drafts:', {
      hasDrafts: hasDraftsValue,
      draftCount: formDrafts.draftCount,
      activeDraftId: formDrafts.activeDraftId
    });
    
    if (hasDraftsValue) {
      // Single draft - simple restore/discard dialog
      dialog.info({
        title: t('debug.foundSavedDraft'),
        content: t('debug.continueOrStartFresh'),
        positiveText: t('debug.continueEditing'),
        negativeText: t('debug.startFresh'),        onPositiveClick: () => {
          const currentDraft = formDrafts.getCurrentDraft();
          if (currentDraft) {
            // Transform old tag format to new format if needed
            const transformedDraft = {
              ...currentDraft,              reportTags: (currentDraft.reportTags || []).map(tag => {
                // Handle both old and new tag formats
                if ((tag as any).tag && !(tag as any).tagId && !(tag as any).tagName) {
                  // Old format: convert to new format
                  return {
                    ...((tag as any).origin === 'PREDEFINED' ? { tagId: (tag as any).tag } : { tagName: (tag as any).tag }),
                    origin: (tag as any).origin
                  };
                }
                return tag; // Already in new format
              })
            };
            reportForm.value = transformedDraft;
            message.success(t('debug.draftLoaded'));
            logger.logUserAction('debug-draft-restored');
          }
        },
        onNegativeClick: () => {
          formDrafts.clearActiveDraft();
          resetForm();
          logger.logUserAction('debug-draft-discarded');
        }
      });
    }
  });
};

// Note: Connection restored notification is now handled centrally in useOfflineReports composable
// to prevent duplicate messages when multiple DebugReportButtonEnhanced instances are mounted

// Watch for offline reports processing
watch(() => offlineReports.isProcessingQueue, (isProcessing) => {
  if (isProcessing) {
    message.loading(t('debug.processingOfflineReports'));
  }
});

// Voice recording handlers
const handleTranscriptionComplete = (transcription: string) => {
  logger.logUserAction('voice-transcription-complete', {
    transcriptionLength: transcription.length
  });
  message.success(t('voice.transcriptionComplete'));
};

const handleAnalysisComplete = (report: ReportDetails) => {  console.log('[DebugReportButtonEnhanced] Analysis complete event received:', report);  console.log('[DebugReportButtonEnhanced] aiAnalysis.hasGeneratedReport:', aiAnalysis.hasGeneratedReport);
  console.log('[DebugReportButtonEnhanced] aiAnalysis.generatedReport:', aiAnalysis.generatedReport);
    
  logger.logUserAction('voice-analysis-complete', {
    reportType: report.type,
    confidence: confidenceValue
  });
  
  // Auto-apply AI results to form immediately after successful analysis
  if (aiAnalysis.hasGeneratedReport && aiAnalysis.generatedReport) {
    console.log('[DebugReportButtonEnhanced] Auto-applying AI results to form...');
    applyAiReport();
    message.success(t('voice.analysisCompleteAndApplied', {
      confidence: Math.round(confidenceValue.value * 100)
    }));
  } else {
    message.success(t('voice.analysisComplete', {
      confidence: Math.round(confidenceValue.value * 100)
    }));
  }
};

const handleVoiceError = (errorInfo: any) => {
  // Handle both old string format and new detailed error format for backward compatibility
  if (typeof errorInfo === 'string') {
    logger.logUserAction('voice-processing-error', { error: errorInfo });
    message.error(errorInfo);
    return;
  }

  // Handle new detailed error format
  const { type, reason, message: errorMessage, suggestions, transcription } = errorInfo;
  
  if (type === 'ANALYSIS_FAILED') {
    logger.logUserAction('voice-analysis-failed', { 
      reason, 
      hasTranscription: !!transcription,
      transcriptionLength: transcription?.length || 0
    });
    
    // Show user-friendly error message
    message.error(errorMessage, { duration: 8000 });
    
    // If we have suggestions, show them in a dialog
    if (suggestions && suggestions.length > 0) {
      setTimeout(() => {
        dialog.create({
          title: t('voice.analysisFailedTitle'),
          content: () => h('div', { class: 'space-y-3' }, [
            h('p', { class: 'text-gray-600 dark:text-gray-300' }, errorMessage),
            h('div', [
              h('p', { class: 'font-medium mb-2' }, t('voice.improvementSuggestions')),
              h('ul', { class: 'list-disc list-inside space-y-1 text-sm' }, 
                suggestions.map((suggestion: string) => 
                  h('li', { class: 'text-gray-700 dark:text-gray-200' }, suggestion)
                )
              )
            ]),
            transcription && h('div', { class: 'mt-4 pt-3 border-t border-gray-200 dark:border-gray-600' }, [
              h('p', { class: 'font-medium mb-1 text-sm' }, t('voice.detectedSpeech')),
              h('p', { class: 'text-xs text-gray-600 dark:text-gray-400 italic' }, transcription)
            ])
          ]),
          positiveText: t('common.understood'),
          onPositiveClick: () => {
            // Focus back to manual form input
            const titleInput = document.querySelector('input[placeholder*="title"], input[placeholder*="Title"]') as HTMLInputElement;
            if (titleInput) {
              titleInput.focus();
            }
          }
        });
      }, 500); // Small delay to let the error message show first
    }
  } else {
    // Handle other error types
    logger.logUserAction('voice-processing-error', { 
      type, 
      error: errorMessage 
    });
    message.error(errorMessage || t('voice.processingError'));
  }
};

const applyAiReport = () => {
  const aiReport = aiAnalysis.generatedReport;
  
  if (aiReport) {
    // Convert AI-generated tags to the new format with origins
    const aiTags = (aiReport.reportTags || []).map((tag: any) => 
      typeof tag === 'string' 
        ? { tag, origin: 'AI_SUGGESTED' as const }
        : tag
    );

    // Only add predefined tags to the form's reportTags
    // AI-only tags will be handled by TagSelectorV2 separately
    const currentTags = [...(reportForm.value.reportTags || [])];
    const allPredefinedTagsList = allPredefinedTags.value.map(tag => tag.name);
    const predefinedTagsToSelect: string[] = [];
      console.log('Debug: AI suggested tags:', aiTags);
    console.log('Debug: All predefined tags:', allPredefinedTagsList);
    console.log('Debug: Current form tags before:', currentTags);
    
    // Debug: Let's also see the actual predefined tags objects
    console.log('Debug: All predefined tags objects:', allPredefinedTags.value.map(t => ({ id: t.id, name: t.name })));
      // Only add predefined tags that match AI suggestions to the form
    aiTags.forEach(aiTag => {
      // Extract tag name more carefully
      let tagName;
      if (typeof aiTag === 'string') {
        tagName = aiTag;
      } else if (aiTag.tag) {
        tagName = aiTag.tag;
      } else if (aiTag.tagName) {
        tagName = aiTag.tagName;
      } else if (aiTag.tagId) {
        tagName = aiTag.tagId;
      }
      
      console.log('Debug: Processing AI tag:', aiTag, 'extracted tagName:', tagName);
      
      // Skip if we couldn't extract a tag name
      if (!tagName) {
        console.log('Debug: Skipping AI tag with no extractable name:', aiTag);
        return;
      }
      
      // Check if this AI-suggested tag matches a predefined tag
      if (allPredefinedTagsList.includes(tagName)) {
        if (import.meta.env.DEV) {
          console.log('Debug: Found matching predefined tag:', tagName);
        }
        // Check if it's not already in the form
        const alreadyExists = currentTags.some(existingTag => {
          // For predefined tags, check tagId
          if (existingTag.origin === 'PREDEFINED') {
            const tagId = (existingTag as any).tagId;
            if (tagId) {
              const predefinedTag = allPredefinedTags.value.find(t => t.id === tagId);
              return predefinedTag?.name === tagName;
            }
          } else {
            // For other origins, check tagName
            const tagName_existing = (existingTag as any).tagName;
            return tagName_existing === tagName;
          }
          return false;
        });
        
        if (!alreadyExists) {
          // Add as predefined tag since it matches our predefined list
          console.log('Debug: Adding predefined tag to form:', tagName);
          
          // Find the predefined tag to get its ID
          const predefinedTag = allPredefinedTags.value.find(t => t.name === tagName);
          if (predefinedTag) {
            currentTags.push({ tagId: predefinedTag.id, origin: 'PREDEFINED' as const });
            predefinedTagsToSelect.push(predefinedTag.id);
            console.log('Debug: Successfully added predefined tag:', predefinedTag.id, 'for name:', tagName);
          } else {
            console.log('Debug: Could not find predefined tag object for name:', tagName);
          }
        } else {
          console.log('Debug: Predefined tag already exists:', tagName);
        }
      } else {
        console.log('Debug: AI tag not in predefined list, will be handled by TagSelectorV2:', tagName);
      }
    });
    
    console.log('Debug: Final form tags (predefined only):', currentTags);
    console.log('Debug: Predefined tags to select in UI:', predefinedTagsToSelect);

    // Update the form with basic data and predefined tags only
    reportForm.value = {
      ...reportForm.value,
      type: aiReport.type,
      severity: aiReport.severity,
      title: aiReport.title,
      description: aiReport.description,
      stepsToReproduce: aiReport.stepsToReproduce || '',
      expectedBehavior: aiReport.expectedBehavior || '',
      actualBehavior: aiReport.actualBehavior || '',
      additionalNotes: aiReport.additionalNotes || '',
      reportTags: currentTags, // Only predefined tags
    };    // Update selectedTagIds to show predefined tags as selected in the UI
    const newSelectedTagIds = [...selectedTagIds.value];
    predefinedTagsToSelect.forEach(tagId => {
      if (!newSelectedTagIds.includes(tagId)) {
        newSelectedTagIds.push(tagId);
      }    });
      console.log('Debug: About to update selectedTagIds from:', selectedTagIds.value, 'to:', newSelectedTagIds);
      // Force TagSelectorV2 to refresh by clearing and then setting selectedTagIds
    selectedTagIds.value = [];
    nextTick(() => {
      selectedTagIds.value = newSelectedTagIds;
      console.log('Debug: selectedTagIds updated to:', selectedTagIds.value);
      
      // Force TagSelectorV2 to re-render with the new selectedTagIds
      tagSelectorKey.value++;
      console.log('Debug: TagSelectorV2 key incremented to:', tagSelectorKey.value);
    });

    console.log('Debug: Form after applying AI report:', reportForm.value);
    console.log('Debug: Form tags after applying (predefined only):', reportForm.value.reportTags);
    console.log('Debug: Selected tag IDs after applying:', selectedTagIds.value);    // Use nextTick and a longer timeout to ensure the TagSelectorV2 component has time to react to selectedTagIds changes
    nextTick(() => {
      console.log('Debug: After nextTick - selectedTagIds:', selectedTagIds.value);
      
      // Add a small delay to ensure the TagSelectorV2 has processed the changes
      setTimeout(() => {
        console.log('Debug: After timeout - selectedTagIds should be reflected in UI:', selectedTagIds.value);
        console.log('Debug: TagSelectorV2 should now show these predefined tags as selected');
      }, 100);
      
      // The TagSelectorV2 will automatically handle AI-only tags via the :ai-suggested-tags prop
      // and will call onTagsChanged to merge predefined + AI-only tags
    });

    handleFormInput(); // Trigger auto-save
      
    logger.logUserAction('ai-report-applied', {      confidence: confidenceValue.value,
      predefinedTagsSelected: currentTags.filter(tag => 
        (typeof tag !== 'string' && tag.origin === 'PREDEFINED')
      ).length,
      aiOnlyTagsProvided: aiTags.filter(tag => {
        const tagName = typeof tag === 'string' ? tag : tag.tag;
        return !allPredefinedTagsList.includes(tagName);
      }).length,
      selectedTagIds: newSelectedTagIds.length
    });
    
    message.success(t('voice.reportApplied'));
  }
};

const getTypeLabel = (type?: string) => {
  if (!type) return '';

  // Map type values to translation keys directly - avoid passing reactive objects
  const typeLabels: Record<string, string> = {
    'bug': 'debug.reportTypes.bug',
    'feature-request': 'debug.reportTypes.feature', 
    'performance': 'debug.reportTypes.performance',
    'ui-ux': 'debug.reportTypes.uiux',
    'improvement': 'debug.reportTypes.improvement',
    'question': 'debug.reportTypes.question',
    'other': 'debug.reportTypes.other',
  };

  const translationKey = typeLabels[type];
  return translationKey ? t(translationKey) : type;
};

const getSeverityLabel = (severity?: string) => {
  if (!severity) return '';

  // Map severity values to translation keys directly - avoid passing reactive objects
  const severityLabels: Record<string, string> = {
    'low': 'debug.severity.low',
    'medium': 'debug.severity.medium', 
    'high': 'debug.severity.high',
    'critical': 'debug.severity.critical',
  };

  const translationKey = severityLabels[severity];
  return translationKey ? t(translationKey) : severity;
};

// Track modal interactions
onMounted(() => {
  // Initialize static data first to avoid circular refs
  initializeStaticData();
  // Setup emergency save for beforeunload events
  formDrafts.setupEmergencySave();
  logger.logUserAction('debug-report-button-mounted');
});

onUnmounted(() => {
  // Cleanup emergency save listener
  formDrafts.cleanupEmergencySave();
});
</script>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1 1 0%;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Voice Recording Section Styles */
.ai-voice-section {
  border: 2px dashed var(--n-border-color, #e5e7eb);
  border-radius: var(--n-border-radius, 0.75rem);
  padding: 1.5rem;
  background: var(--n-color);
  transition: all 0.3s ease;
  margin-top: 1.5rem;
}

.ai-voice-section:hover {
  border-color: var(--n-primary-color, #3b82f6);
  background: var(--n-color-embedded);
}

/* Enhanced selection styles */
.selected-card {
  position: relative;
  z-index: 5; /* Bring selected card to front */
  transition: all 0.3s ease-in-out;
}

.selected-card::after {
  content: "";
  position: absolute;
  inset: -3px;
  z-index: -1;
  border-radius: 10px;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.5), rgba(37, 99, 235, 0.2));
  filter: blur(8px);
  opacity: 0.7;
}

/* Pulse animation for the checkmark */
.pulse-animation {
  animation: pulse-ring 2s ease-out infinite;
  position: relative;
}

@keyframes pulse-ring {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Icon glow effect for selected item */
.icon-glow {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
}

/* Dark mode adjustments */
[data-theme="dark"] .selected-card::after {
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(30, 64, 175, 0.2));
  filter: blur(10px);
}

[data-theme="dark"] .icon-glow {
  box-shadow: 0 0 15px rgba(30, 64, 175, 0.7);
}

.voice-recorder-container {
  display: flex;
  justify-content: center;
  padding: 1rem 0;
}

.ai-results {
  border-top: 1px solid var(--n-border-color);
  padding-top: 1rem;
}

.transcription-result {
  animation: fadeInUp 0.5s ease-out;
}

.ai-report-preview {
  animation: fadeInUp 0.5s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Light theme specific styles */
[data-theme="light"] .ai-voice-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-color: #e5e7eb;
}

[data-theme="light"] .ai-voice-section:hover {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

[data-theme="light"] .ai-results {
  border-top-color: #e5e7eb;
}

/* Dark theme specific styles */
[data-theme="dark"] .ai-voice-section {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-color: #475569;
}

[data-theme="dark"] .ai-voice-section:hover {
  border-color: #60a5fa;
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
}

[data-theme="dark"] .ai-results {
  border-top-color: #475569;
}

/* AI Tags Responsive Layout */
.ai-tags-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ai-tags-label {
  display: flex;
  align-items: center;
  font-weight: 500;
  flex-shrink: 0;
}

.ai-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
  align-items: flex-start;
}

/* Mobile responsive styles */
@media (max-width: 640px) {
  .ai-tags-container {
    gap: 0.75rem;
  }
  
  .ai-tags-list {
    gap: 0.5rem;
  }
}

/* Medium screens */
@media (min-width: 641px) {
  .ai-tags-container {
    flex-direction: row;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .ai-tags-label {
    min-width: fit-content;
    margin-top: 0.125rem; /* Align with first tag */
  }
    .ai-tags-list {
    flex: 1;
  }
}

/* Modal Actions Layout - Responsive Design */
.modal-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  padding: 0;
}

/* Status Indicators Section */
.status-indicators {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid;
  background-color: rgba(255, 255, 255, 0.05);
}

.status-indicator.status-offline {
  color: #f97316;
  border-color: rgba(249, 115, 22, 0.3);
  background-color: rgba(249, 115, 22, 0.1);
}

.status-indicator.status-pending {
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.3);
  background-color: rgba(59, 130, 246, 0.1);
}

.status-text {
  flex: 1;
  line-height: 1.4;
}

/* Action Buttons Section */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

.secondary-actions {
  display: flex;
  gap: 0.5rem;
  width: 100%;
}

.primary-action {
  width: 100%;
}

.action-btn {
  flex: 1;
  min-height: 2.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn.primary {
  width: 100%;
}

.action-btn.secondary {
  flex: 1;
}

/* Simple, Clean Modal Actions Layout */
.debug-modal-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.status-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background: var(--n-color-embedded);
  border: 1px solid var(--n-border-color);
}

.status-item.offline {
  color: #f59e0b;
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(245, 158, 11, 0.1);
}

.status-item.pending {
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.1);
}

.buttons-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.secondary-buttons {
  display: flex;
  gap: 0.5rem;
}

.primary-button {
  display: flex;
}

.primary-button .n-button {
  flex: 1;
}

/* Desktop Layout */
@media (min-width: 768px) {
  .debug-modal-actions {
    flex-direction: row;
    align-items: flex-end;
    justify-content: space-between;
  }
  
  .status-section {
    flex: 1;
    max-width: 60%;
    margin-right: 1rem;
  }
  
  .buttons-section {
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
  }
  
  .primary-button .n-button {
    flex: none;
    min-width: 120px;
  }
}

/* Mobile specific adjustments */
@media (max-width: 767px) {
  .secondary-buttons {
    flex-direction: column;
  }
  
  .secondary-buttons .n-button {
    width: 100%;
  }
}
</style>
