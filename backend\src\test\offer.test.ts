import { describe, it, expect, beforeAll, afterAll, vi, beforeEach, afterEach } from 'vitest';
import { Hono } from 'hono';
import { PrismaClient, OfferStatus, OfferType, InterestStatus, NotificationType } from '@prisma/client';
import createOfferRoutes from '../routes/offer';
import jwt from 'jsonwebtoken';
import { Server } from 'socket.io';
import { NotificationService } from '../services/notificationService';
import { MatchingService } from '../services/matchingService';

const prisma = new PrismaClient();

// Create comprehensive mock dependencies
const mockSocketEvents: any[] = [];
const mockIo = {
  to: vi.fn().mockReturnThis(),
  emit: vi.fn((event, payload) => {
    mockSocketEvents.push({ event, payload, target: 'broadcast' });
    return mockIo;
  }),
  // Add to() method that returns an object with emit
  on: vi.fn(),
} as unknown as Server;

// Mock the to() method to return an object with emit
const mockToObject = {
  emit: vi.fn((event, payload) => {
    mockSocketEvents.push({ event, payload, target: 'specific' });
  }),
};
(mockIo.to as any).mockReturnValue(mockToObject);

const mockNotificationService = {
  createNotification: vi.fn().mockResolvedValue({ id: 'notification-id' }),
  emitNotification: vi.fn(),
} as unknown as NotificationService;

const mockMatchingService = {
  findMatchesForOffer: vi.fn(),
  scheduleMatchingJob: vi.fn(),
  findPotentialMatches: vi.fn().mockResolvedValue([]),
} as unknown as MatchingService;

const app = new Hono();
app.route('/offers', createOfferRoutes(mockIo, mockNotificationService, mockMatchingService));

// Helper to create a test user with specific properties
async function createTestUser(overrides: any = {}) {
  return await prisma.user.create({
    data: {
      email: `testuser_${Date.now()}@example.com`,
      password: 'hashedpassword',
      emailVerified: true,
      phoneVerified: true,
      username: `testuser_${Date.now()}`,
      reputationLevel: 3,
      ...overrides,
    },
  });
}

// Helper to create a test offer
async function createTestOffer(userId: string, overrides: any = {}) {
  return await prisma.offer.create({
    data: {
      userId,
      type: OfferType.BUY,
      amount: 100,
      baseRate: 50000,
      adjustmentForLowerRep: 5,
      adjustmentForHigherRep: 10,
      status: OfferStatus.ACTIVE,
      currencyPair: 'CAD-IRR',
      ...overrides,
    },
  });
}

// Helper to create a test interest
async function createTestInterest(offerId: string, interestedUserId: string, overrides: any = {}) {
  return await prisma.interest.create({
    data: {
      offerId,
      interestedUserId,
      status: InterestStatus.PENDING,
      ...overrides,
    },
  });
}

// Helper to generate JWT token
function generateToken(userId: string, email: string) {
  return jwt.sign(
    { userId, email }, 
    process.env.JWT_SECRET || 'your-default-secret-key', 
    { expiresIn: '1h' }
  );
}

// Helper to clean up test data
async function cleanupTestData(userIds: string[]) {
  // Delete in proper order to avoid foreign key constraints
  await prisma.transaction.deleteMany({
    where: { 
      OR: [
        { currencyAProviderId: { in: userIds } },
        { currencyBProviderId: { in: userIds } }
      ]
    }
  });  await prisma.chatMessage.deleteMany({
    where: { 
      OR: [
        { senderId: { in: userIds } },
        { chatSession: { userOneId: { in: userIds } } },
        { chatSession: { userTwoId: { in: userIds } } }
      ]
    }
  });  await prisma.payerNegotiation.deleteMany({
    where: { 
      OR: [
        { partyA_Id: { in: userIds } },
        { partyB_Id: { in: userIds } }
      ]
    }
  });
  await prisma.transaction.deleteMany({
    where: { 
      OR: [
        { currencyAProviderId: { in: userIds } },
        { currencyBProviderId: { in: userIds } }
      ]
    }
  });
  await prisma.chatSession.deleteMany({
    where: { 
      OR: [
        { userOneId: { in: userIds } },
        { userTwoId: { in: userIds } }
      ]
    }
  });
  await prisma.offerMatch.deleteMany({
    where: { 
      OR: [
        { userAId: { in: userIds } },
        { userBId: { in: userIds } }
      ]
    }
  });
  await prisma.notification.deleteMany({
    where: { userId: { in: userIds } }
  });
  await prisma.interest.deleteMany({
    where: { 
      OR: [
        { interestedUserId: { in: userIds } },
        { offer: { userId: { in: userIds } } }
      ]
    }
  });
  await prisma.paymentReceivingInfo.deleteMany({
    where: { userId: { in: userIds } }
  });
  await prisma.offer.deleteMany({
    where: { userId: { in: userIds } }
  });
  await prisma.user.deleteMany({
    where: { id: { in: userIds } }
  });
}

// Global test state
let testUsers: any[] = [];
let testOffers: any[] = [];

beforeEach(() => {
  // Clear mock call history before each test
  vi.clearAllMocks();
  mockSocketEvents.length = 0;
});

afterEach(async () => {
  // Clean up any test data created during tests
  if (testUsers.length > 0) {
    await cleanupTestData(testUsers.map(u => u.id));
    testUsers = [];
    testOffers = [];
  }
});

describe('PATCH /offers/:offerId/status - Update Offer Status', () => {
  let user: any;
  let otherUser: any;
  let token: string;
  let otherToken: string;
  let offer: any;

  beforeEach(async () => {
    user = await createTestUser();
    otherUser = await createTestUser();
    testUsers.push(user, otherUser);
    
    token = generateToken(user.id, user.email);
    otherToken = generateToken(otherUser.id, otherUser.email);
    
    offer = await createTestOffer(user.id);
    testOffers.push(offer);
  });

  it('should toggle offer status to INACTIVE', async () => {
    const res = await app.request(`/offers/${offer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'INACTIVE' }),
    });
    
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.status).toBe('INACTIVE');

    // Check socket event was emitted
    expect(mockSocketEvents).toHaveLength(1);
    expect(mockSocketEvents[0].event).toBe('OFFER_STATUS_CHANGED');
    expect(mockSocketEvents[0].payload.offerId).toBe(offer.id);
    expect(mockSocketEvents[0].payload.newStatus).toBe('INACTIVE');
  });

  it('should toggle offer status back to ACTIVE', async () => {
    // Set to INACTIVE first
    await prisma.offer.update({ where: { id: offer.id }, data: { status: 'INACTIVE' } });
    
    const res = await app.request(`/offers/${offer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'ACTIVE' }),
    });
    
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.status).toBe('ACTIVE');
  });

  it('should return 404 for non-existent offer', async () => {
    const res = await app.request(`/offers/nonexistentid/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'INACTIVE' }),
    });
    
    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.message).toBe('Offer not found');
  });

  it('should return 403 if user does not own the offer', async () => {
    const res = await app.request(`/offers/${offer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${otherToken}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'INACTIVE' }),
    });
    
    expect(res.status).toBe(403);
    const data = await res.json();
    expect(data.message).toBe('Forbidden');
  });

  it('should return 400 for invalid status value', async () => {
    const res = await app.request(`/offers/${offer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'INVALID_STATUS' }),
    });
    
    expect(res.status).toBe(400);
  });

  it('should return 400 for completed offers', async () => {
    await prisma.offer.update({ 
      where: { id: offer.id }, 
      data: { status: OfferStatus.COMPLETED } 
    });

    const res = await app.request(`/offers/${offer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'ACTIVE' }),
    });
    
    expect(res.status).toBe(400);
    const data = await res.json();
    expect(data.message).toContain('already COMPLETED');
  });

  it('should return 400 for cancelled offers', async () => {
    await prisma.offer.update({ 
      where: { id: offer.id }, 
      data: { status: OfferStatus.CANCELLED } 
    });

    const res = await app.request(`/offers/${offer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'ACTIVE' }),
    });
    
    expect(res.status).toBe(400);
    const data = await res.json();
    expect(data.message).toContain('already CANCELLED');
  });

  it('should return 200 if status is already the same', async () => {
    const res = await app.request(`/offers/${offer.id}/status`, {
      method: 'PATCH',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ status: 'ACTIVE' }),
    });
    
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.message).toContain('no change requested');
  });
});

describe('POST /offers - Create Offer', () => {
  let user: any;
  let token: string;

  beforeEach(async () => {
    user = await createTestUser();
    testUsers.push(user);
    token = generateToken(user.id, user.email);
  });

  it('should create a new offer successfully', async () => {
    const offerData = {
      type: OfferType.BUY,
      amount: 1000,
      baseRate: 45000,
      adjustmentForLowerRep: 5,
      adjustmentForHigherRep: 10,
      currencyPair: 'CAD-IRR'
    };

    const res = await app.request('/offers', {
      method: 'POST',
      headers: { 
        Authorization: `Bearer ${token}`, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(offerData),
    });

    expect(res.status).toBe(201);
    const data = await res.json();
    expect(data.type).toBe(OfferType.BUY);
    expect(data.amount).toBe(1000);
    expect(data.baseRate).toBe(45000);
    expect(data.status).toBe(OfferStatus.ACTIVE);
    expect(data.userId).toBe(user.id);
    expect(data.user.username).toBeDefined();

    // Check socket event was emitted
    expect(mockSocketEvents).toHaveLength(1);
    expect(mockSocketEvents[0].event).toBe('OFFER_CREATED');
    expect(mockSocketEvents[0].payload.offerId).toBe(data.id);

    // Check matching service was called
    expect(mockMatchingService.findPotentialMatches).toHaveBeenCalledWith(data.id);
  });

  it('should reject offer creation if phone not verified', async () => {
    const unverifiedUser = await createTestUser({ phoneVerified: false });
    testUsers.push(unverifiedUser);
    const unverifiedToken = generateToken(unverifiedUser.id, unverifiedUser.email);

    const offerData = {
      type: OfferType.BUY,
      amount: 1000,
      baseRate: 45000,
      adjustmentForLowerRep: 5,
      adjustmentForHigherRep: 10
    };

    const res = await app.request('/offers', {
      method: 'POST',
      headers: { 
        Authorization: `Bearer ${unverifiedToken}`, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(offerData),
    });

    expect(res.status).toBe(403);
    const data = await res.json();
    expect(data.message).toBe('Phone verification required to create offers.');
  });

  it('should validate required fields', async () => {
    const invalidOfferData = {
      type: OfferType.BUY,
      // Missing required fields
    };

    const res = await app.request('/offers', {
      method: 'POST',
      headers: { 
        Authorization: `Bearer ${token}`, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(invalidOfferData),
    });

    expect(res.status).toBe(400);
  });

  it('should validate positive amounts and rates', async () => {
    const invalidOfferData = {
      type: OfferType.BUY,
      amount: -100, // Negative amount
      baseRate: -1000, // Negative rate
      adjustmentForLowerRep: 5,
      adjustmentForHigherRep: 10
    };

    const res = await app.request('/offers', {
      method: 'POST',
      headers: { 
        Authorization: `Bearer ${token}`, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(invalidOfferData),
    });

    expect(res.status).toBe(400);
  });

  it('should use default currency pair when not provided', async () => {
    const offerData = {
      type: OfferType.SELL,
      amount: 500,
      baseRate: 46000,
      adjustmentForLowerRep: 3,
      adjustmentForHigherRep: 7
      // No currencyPair provided
    };

    const res = await app.request('/offers', {
      method: 'POST',
      headers: { 
        Authorization: `Bearer ${token}`, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(offerData),
    });

    expect(res.status).toBe(201);
    const data = await res.json();
    expect(data.currencyPair).toBe('CAD-IRR');
  });
});

describe('GET /offers/browse - Browse Offers', () => {
  let viewingUser: any;
  let offerCreator1: any;
  let offerCreator2: any;
  let token: string;
  let offer1: any;
  let offer2: any;
  beforeEach(async () => {
    // Clean up test-specific data first to ensure test isolation
    // Only delete test data to avoid affecting development users
    await prisma.transaction.deleteMany({
      where: {
        OR: [
          { offer: { user: { email: { contains: 'test' } } } },
          { offer: { user: { username: { contains: 'test' } } } }
        ]
      }
    });
    await prisma.chatMessage.deleteMany({
      where: {
        OR: [
          { chatSession: { userOne: { email: { contains: 'test' } } } },
          { chatSession: { userTwo: { email: { contains: 'test' } } } },
          { chatSession: { userOne: { username: { contains: 'test' } } } },
          { chatSession: { userTwo: { username: { contains: 'test' } } } }
        ]
      }
    });
    await prisma.chatSession.deleteMany({
      where: {
        OR: [
          { userOne: { email: { contains: 'test' } } },
          { userTwo: { email: { contains: 'test' } } },
          { userOne: { username: { contains: 'test' } } },
          { userTwo: { username: { contains: 'test' } } }
        ]
      }
    });
    await prisma.notification.deleteMany({
      where: {
        user: { email: { contains: 'test' } }
      }
    });
    await prisma.interest.deleteMany({
      where: {
        OR: [
          { interestedUser: { email: { contains: 'test' } } },
          { interestedUser: { username: { contains: 'test' } } },
          { offer: { user: { email: { contains: 'test' } } } },
          { offer: { user: { username: { contains: 'test' } } } }
        ]
      }
    });
    await prisma.offer.deleteMany({
      where: {
        user: { email: { contains: 'test' } }
      }
    });
    await prisma.user.deleteMany({
      where: {
        OR: [
          { email: { contains: 'test' } },
          { username: { contains: 'test' } }
        ]
      }
    });

    // Create users with different reputation levels
    viewingUser = await createTestUser({ reputationLevel: 3 });
    offerCreator1 = await createTestUser({ reputationLevel: 5 }); // Higher rep
    offerCreator2 = await createTestUser({ reputationLevel: 1 }); // Lower rep
    testUsers.push(viewingUser, offerCreator1, offerCreator2);

    token = generateToken(viewingUser.id, viewingUser.email);

    // Create offers
    offer1 = await createTestOffer(offerCreator1.id, {
      type: OfferType.SELL,
      baseRate: 45000,
      adjustmentForLowerRep: 5,
      adjustmentForHigherRep: 10
    });
    offer2 = await createTestOffer(offerCreator2.id, {
      type: OfferType.BUY,
      baseRate: 46000,
      adjustmentForLowerRep: 3,
      adjustmentForHigherRep: 7
    });
    testOffers.push(offer1, offer2);
  });

  it('should return active offers from other users with calculated rates', async () => {
    const res = await app.request('/offers/browse', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` },
    });

    expect(res.status).toBe(200);
    const offers = await res.json();
    expect(offers).toHaveLength(2);    // Check rate calculations
    const sellOffer = offers.find((o: any) => o.type === 'SELL');
    const buyOffer = offers.find((o: any) => o.type === 'BUY');

    // For SELL offer from higher rep user (5) to lower rep user (3)
    // Lower rep viewer pays MORE to higher rep seller: rate increased by adjustmentForLowerRep
    expect(sellOffer.calculatedApplicableRate).toBe(Math.round(45000 * 1.05)); // 47250

    // For BUY offer from lower rep user (1) viewed by higher rep user (3)  
    // Higher rep viewer receives MORE from lower rep buyer: rate increased by adjustmentForHigherRep
    expect(buyOffer.calculatedApplicableRate).toBe(Math.round(46000 * 1.07)); // 49220
  });

  it('should not return own offers', async () => {
    // Create an offer for the viewing user
    const ownOffer = await createTestOffer(viewingUser.id);
    testOffers.push(ownOffer);

    const res = await app.request('/offers/browse', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` },
    });

    expect(res.status).toBe(200);
    const offers = await res.json();
    
    // Should only see offers from other users, not own offer
    expect(offers).toHaveLength(2);
    expect(offers.find((o: any) => o.id === ownOffer.id)).toBeUndefined();
  });

  it('should include user interest status in offers', async () => {
    // Create interest for viewing user in offer1
    const interest = await createTestInterest(offer1.id, viewingUser.id);

    const res = await app.request('/offers/browse', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` },
    });

    expect(res.status).toBe(200);
    const offers = await res.json();
    
    const offerWithInterest = offers.find((o: any) => o.id === offer1.id);
    const offerWithoutInterest = offers.find((o: any) => o.id === offer2.id);

    expect(offerWithInterest.currentUserHasShownInterest).toBe(true);
    expect(offerWithInterest.currentUserInterestStatus).toBe('PENDING');
    expect(offerWithoutInterest.currentUserHasShownInterest).toBe(false);
    expect(offerWithoutInterest.currentUserInterestStatus).toBeNull();
  });

  it('should return 404 if viewing user not found', async () => {
    const nonExistentToken = generateToken('nonexistent-id', '<EMAIL>');

    const res = await app.request('/offers/browse', {
      method: 'GET',
      headers: { Authorization: `Bearer ${nonExistentToken}` },
    });

    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.message).toBe('User not found');
  });

  it('should not return inactive offers', async () => {
    // Create an inactive offer
    const inactiveOffer = await createTestOffer(offerCreator1.id, { 
      status: OfferStatus.INACTIVE 
    });
    testOffers.push(inactiveOffer);

    const res = await app.request('/offers/browse', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` },
    });

    expect(res.status).toBe(200);
    const offers = await res.json();
    
    // Should not include inactive offer
    expect(offers.find((o: any) => o.id === inactiveOffer.id)).toBeUndefined();
  });
});

describe('GET /offers/my - Get My Offers', () => {
  let user: any;
  let interestedUser: any;
  let token: string;
  let offer: any;

  beforeEach(async () => {
    user = await createTestUser();
    interestedUser = await createTestUser();
    testUsers.push(user, interestedUser);

    token = generateToken(user.id, user.email);
    offer = await createTestOffer(user.id);
    testOffers.push(offer);
  });

  it('should return user offers without interests by default', async () => {
    const res = await app.request('/offers/my', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` },
    });

    expect(res.status).toBe(200);
    const offers = await res.json();
    expect(offers).toHaveLength(1);
    expect(offers[0].id).toBe(offer.id);
    expect(offers[0].interests).toBeUndefined();
  });

  it('should include interests when requested', async () => {
    // Create interest in the offer
    const interest = await createTestInterest(offer.id, interestedUser.id);

    const res = await app.request('/offers/my?includeInterests=1', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` },
    });

    expect(res.status).toBe(200);
    const offers = await res.json();
    expect(offers).toHaveLength(1);
    expect(offers[0].interests).toHaveLength(1);
    expect(offers[0].interests[0].id).toBe(interest.id);
    expect(offers[0].interests[0].interestedUserId).toBe(interestedUser.id);
    expect(offers[0].interests[0].status).toBe('PENDING');
  });

  it('should only return offers for the authenticated user', async () => {
    // Create offer for another user
    const otherUser = await createTestUser();
    testUsers.push(otherUser);
    const otherOffer = await createTestOffer(otherUser.id);
    testOffers.push(otherOffer);

    const res = await app.request('/offers/my', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` },
    });

    expect(res.status).toBe(200);
    const offers = await res.json();
    expect(offers).toHaveLength(1);
    expect(offers[0].id).toBe(offer.id);
  });

  it('should order offers by creation date descending', async () => {
    // Create additional offers with delay to ensure different timestamps
    await new Promise(resolve => setTimeout(resolve, 10));
    const newerOffer = await createTestOffer(user.id, { amount: 200 });
    testOffers.push(newerOffer);

    const res = await app.request('/offers/my', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` },
    });

    expect(res.status).toBe(200);
    const offers = await res.json();
    expect(offers).toHaveLength(2);
    expect(offers[0].id).toBe(newerOffer.id); // Newer offer first
    expect(offers[1].id).toBe(offer.id);
  });
});

describe('GET /offers/:offerId - Get Single Offer', () => {
  let owner: any;
  let viewer: any;
  let ownerToken: string;
  let viewerToken: string;
  let offer: any;

  beforeEach(async () => {
    owner = await createTestUser();
    viewer = await createTestUser();
    testUsers.push(owner, viewer);

    ownerToken = generateToken(owner.id, owner.email);
    viewerToken = generateToken(viewer.id, viewer.email);
    offer = await createTestOffer(owner.id);
    testOffers.push(offer);
  });

  it('should return offer details for any authenticated user', async () => {
    const res = await app.request(`/offers/${offer.id}`, {
      method: 'GET',
      headers: { Authorization: `Bearer ${viewerToken}` },
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.id).toBe(offer.id);
    expect(data.type).toBe(offer.type);
    expect(data.amount).toBe(offer.amount);
    expect(data.isOwner).toBe(false);
  });

  it('should indicate ownership for offer owner', async () => {
    const res = await app.request(`/offers/${offer.id}`, {
      method: 'GET',
      headers: { Authorization: `Bearer ${ownerToken}` },
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.isOwner).toBe(true);
  });

  it('should include user interest if viewer has shown interest', async () => {
    const interest = await createTestInterest(offer.id, viewer.id);

    const res = await app.request(`/offers/${offer.id}`, {
      method: 'GET',
      headers: { Authorization: `Bearer ${viewerToken}` },
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.userInterest).toBeDefined();
    expect(data.userInterest.id).toBe(interest.id);
    expect(data.userInterest.status).toBe('PENDING');
  });

  it('should return 404 for non-existent offer', async () => {
    const res = await app.request('/offers/nonexistent-id', {
      method: 'GET',
      headers: { Authorization: `Bearer ${viewerToken}` },
    });

    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.message).toBe('Offer not found or user details missing');
  });
});

describe('PUT /offers/:offerId - Update Offer', () => {
  let owner: any;
  let otherUser: any;
  let ownerToken: string;
  let otherToken: string;
  let offer: any;

  beforeEach(async () => {
    owner = await createTestUser();
    otherUser = await createTestUser();
    testUsers.push(owner, otherUser);

    ownerToken = generateToken(owner.id, owner.email);
    otherToken = generateToken(otherUser.id, otherUser.email);
    offer = await createTestOffer(owner.id);
    testOffers.push(offer);
  });

  it('should update offer successfully for owner', async () => {
    const updateData = {
      amount: 2000,
      baseRate: 47000,
      adjustmentForLowerRep: 8
    };

    const res = await app.request(`/offers/${offer.id}`, {
      method: 'PUT',
      headers: { 
        Authorization: `Bearer ${ownerToken}`, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(updateData),
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.amount).toBe(2000);
    expect(data.baseRate).toBe(47000);
    expect(data.adjustmentForLowerRep).toBe(8);

    // Check socket event was emitted
    expect(mockSocketEvents).toHaveLength(1);
    expect(mockSocketEvents[0].event).toBe('OFFER_UPDATED');
    expect(mockSocketEvents[0].payload.offerId).toBe(offer.id);

    // Check matching service was called for active offer
    expect(mockMatchingService.findPotentialMatches).toHaveBeenCalledWith(offer.id);
  });

  it('should return 403 for non-owner', async () => {
    const updateData = { amount: 2000 };

    const res = await app.request(`/offers/${offer.id}`, {
      method: 'PUT',
      headers: { 
        Authorization: `Bearer ${otherToken}`, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(updateData),
    });

    expect(res.status).toBe(403);
    const data = await res.json();
    expect(data.message).toBe('Forbidden');
  });

  it('should return 404 for non-existent offer', async () => {
    const updateData = { amount: 2000 };

    const res = await app.request('/offers/nonexistent-id', {
      method: 'PUT',
      headers: { 
        Authorization: `Bearer ${ownerToken}`, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(updateData),
    });

    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.message).toBe('Offer not found');
  });

  it('should reset lastMatchedAt when updating offer', async () => {
    // First update the offer with a lastMatchedAt value
    await prisma.offer.update({
      where: { id: offer.id },
      data: { lastMatchedAt: new Date() }
    });

    const updateData = { amount: 1500 };

    const res = await app.request(`/offers/${offer.id}`, {
      method: 'PUT',
      headers: { 
        Authorization: `Bearer ${ownerToken}`, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(updateData),
    });

    expect(res.status).toBe(200);

    // Verify lastMatchedAt was reset
    const updatedOffer = await prisma.offer.findUnique({
      where: { id: offer.id }
    });
    expect(updatedOffer?.lastMatchedAt).toBeNull();
  });
});

describe('POST /offers/:offerId/interest - Show Interest', () => {
  let offerCreator: any;
  let interestedUser: any;
  let unverifiedUser: any;
  let creatorToken: string;
  let interestedToken: string;
  let unverifiedToken: string;
  let offer: any;

  beforeEach(async () => {
    offerCreator = await createTestUser();
    interestedUser = await createTestUser();
    unverifiedUser = await createTestUser({ phoneVerified: false });
    testUsers.push(offerCreator, interestedUser, unverifiedUser);

    creatorToken = generateToken(offerCreator.id, offerCreator.email);
    interestedToken = generateToken(interestedUser.id, interestedUser.email);
    unverifiedToken = generateToken(unverifiedUser.id, unverifiedUser.email);
    
    offer = await createTestOffer(offerCreator.id);
    testOffers.push(offer);
  });
  it('should create interest successfully', async () => {
    const res = await app.request(`/offers/${offer.id}/interest`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${interestedToken}` },
    });

    expect(res.status).toBe(201);
    const data = await res.json();
    expect(data.message).toBe('Interest shown');

    // Verify interest was created in database
    const interest = await prisma.interest.findFirst({
      where: { 
        offerId: offer.id,
        interestedUserId: interestedUser.id 
      }
    });
    expect(interest).toBeDefined();
    expect(interest?.status).toBe('PENDING');

    // Check socket event was emitted to offer creator
    expect(mockSocketEvents).toHaveLength(1);
    expect(mockSocketEvents[0].event).toBe('INTEREST_RECEIVED');
    expect(mockSocketEvents[0].target).toBe('specific');
    expect(mockSocketEvents[0].payload.interestId).toBeDefined();
    expect(mockSocketEvents[0].payload.offerId).toBe(offer.id);

    // Check notification was created
    expect(mockNotificationService.createNotification).toHaveBeenCalledWith({
      userId: offerCreator.id,
      type: NotificationType.NEW_INTEREST_ON_YOUR_OFFER,
      message: expect.stringContaining('showed interest'),
      relatedEntityType: 'OFFER',
      relatedEntityId: offer.id,
      actorId: interestedUser.id,
      actorUsername: expect.any(String),
      data: expect.any(String)
    });
  });

  it('should reject interest if phone not verified', async () => {
    const res = await app.request(`/offers/${offer.id}/interest`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${unverifiedToken}` },
    });

    expect(res.status).toBe(403);
    const data = await res.json();
    expect(data.message).toBe('Phone verification required');
  });

  it('should reject interest in own offer', async () => {
    const res = await app.request(`/offers/${offer.id}/interest`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${creatorToken}` },
    });

    expect(res.status).toBe(400);
    const data = await res.json();
    expect(data.message).toBe('Cannot show interest in own offer');
  });

  it('should reject interest in inactive offer', async () => {
    await prisma.offer.update({
      where: { id: offer.id },
      data: { status: OfferStatus.INACTIVE }
    });

    const res = await app.request(`/offers/${offer.id}/interest`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${interestedToken}` },
    });

    expect(res.status).toBe(400);
    const data = await res.json();
    expect(data.message).toBe('Offer not active');
  });

  it('should reject duplicate interest', async () => {
    // Create initial interest
    await createTestInterest(offer.id, interestedUser.id);

    const res = await app.request(`/offers/${offer.id}/interest`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${interestedToken}` },
    });

    expect(res.status).toBe(409);
    const data = await res.json();
    expect(data.message).toBe('Already shown interest');
  });

  it('should return 404 for non-existent offer', async () => {
    const res = await app.request('/offers/nonexistent-id/interest', {
      method: 'POST',
      headers: { Authorization: `Bearer ${interestedToken}` },
    });

    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.message).toBe('Offer not found or creator missing');
  });

  it('should return 404 for non-existent user', async () => {
    const nonExistentToken = generateToken('nonexistent-id', '<EMAIL>');

    const res = await app.request(`/offers/${offer.id}/interest`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${nonExistentToken}` },
    });

    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.message).toBe('User not found');
  });
});

describe('POST /offers/interests/:interestId/accept - Accept Interest', () => {
  let offerCreator: any;
  let interestedUser: any;
  let otherUser: any;
  let creatorToken: string;
  let otherToken: string;
  let offer: any;
  let interest: any;

  beforeEach(async () => {
    offerCreator = await createTestUser();
    interestedUser = await createTestUser();
    otherUser = await createTestUser();
    testUsers.push(offerCreator, interestedUser, otherUser);

    creatorToken = generateToken(offerCreator.id, offerCreator.email);
    otherToken = generateToken(otherUser.id, otherUser.email);
    
    offer = await createTestOffer(offerCreator.id);
    testOffers.push(offer);
    
    interest = await createTestInterest(offer.id, interestedUser.id);
  });

  it('should accept interest successfully and create chat session', async () => {
    const res = await app.request(`/offers/interests/${interest.id}/accept`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${creatorToken}` },
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.message).toBe('Interest accepted and chat session created');
    expect(data.chatSessionId).toBeDefined();
    expect(data.interestId).toBe(interest.id);
    expect(data.newStatus).toBe('ACCEPTED');

    // Verify interest status was updated
    const updatedInterest = await prisma.interest.findUnique({
      where: { id: interest.id }
    });
    expect(updatedInterest?.status).toBe('ACCEPTED');

    // Verify chat session was created
    const chatSession = await prisma.chatSession.findUnique({
      where: { id: data.chatSessionId }
    });
    expect(chatSession).toBeDefined();
    expect(chatSession?.userOneId).toBe(offerCreator.id);
    expect(chatSession?.userTwoId).toBe(interestedUser.id);
    expect(chatSession?.offerId).toBe(offer.id);
  });

  it('should return 404 for non-existent interest', async () => {
    const res = await app.request('/offers/interests/nonexistent-id/accept', {
      method: 'POST',
      headers: { Authorization: `Bearer ${creatorToken}` },
    });

    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.message).toBe('Interest not found');
  });

  it('should return 403 if user is not offer creator', async () => {
    const res = await app.request(`/offers/interests/${interest.id}/accept`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${otherToken}` },
    });

    expect(res.status).toBe(403);
    const data = await res.json();
    expect(data.message).toBe('Unauthorized to accept this interest');
  });

  it('should return 409 if interest already accepted', async () => {
    // First accept the interest
    await prisma.interest.update({
      where: { id: interest.id },
      data: { status: InterestStatus.ACCEPTED }
    });

    const res = await app.request(`/offers/interests/${interest.id}/accept`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${creatorToken}` },
    });

    expect(res.status).toBe(409);
    const data = await res.json();
    expect(data.message).toBe('Interest already accepted');
  });
});

describe('POST /offers/interests/:interestId/decline - Decline Interest', () => {
  let offerCreator: any;
  let interestedUser: any;
  let otherUser: any;
  let creatorToken: string;
  let otherToken: string;
  let offer: any;
  let interest: any;

  beforeEach(async () => {
    offerCreator = await createTestUser();
    interestedUser = await createTestUser();
    otherUser = await createTestUser();
    testUsers.push(offerCreator, interestedUser, otherUser);

    creatorToken = generateToken(offerCreator.id, offerCreator.email);
    otherToken = generateToken(otherUser.id, otherUser.email);
    
    offer = await createTestOffer(offerCreator.id);
    testOffers.push(offer);
    
    interest = await createTestInterest(offer.id, interestedUser.id);
  });

  it('should decline interest successfully', async () => {
    const declineData = { reasonCode: 'RATE_TOO_LOW' };

    const res = await app.request(`/offers/interests/${interest.id}/decline`, {
      method: 'POST',
      headers: { 
        Authorization: `Bearer ${creatorToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(declineData),
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.message).toBe('Interest declined');
    expect(data.interestId).toBe(interest.id);
    expect(data.reasonCode).toBe('RATE_TOO_LOW');

    // Verify interest status was updated
    const updatedInterest = await prisma.interest.findUnique({
      where: { id: interest.id }
    });
    expect(updatedInterest?.status).toBe('DECLINED');
    expect(updatedInterest?.declineReasonCode).toBe('RATE_TOO_LOW');

    // Check socket event was emitted
    expect(mockSocketEvents).toHaveLength(1);
    expect(mockSocketEvents[0].event).toBe('INTEREST_PROCESSED');
    expect(mockSocketEvents[0].payload.interestId).toBe(interest.id);
    expect(mockSocketEvents[0].payload.newStatus).toBe('DECLINED');
    expect(mockSocketEvents[0].payload.reasonCode).toBe('RATE_TOO_LOW');
  });

  it('should decline interest without reason code', async () => {
    const res = await app.request(`/offers/interests/${interest.id}/decline`, {
      method: 'POST',
      headers: { 
        Authorization: `Bearer ${creatorToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({}),
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.message).toBe('Interest declined');
    expect(data.reasonCode).toBeNull();
  });

  it('should return 404 for non-existent interest', async () => {
    const res = await app.request('/offers/interests/nonexistent-id/decline', {
      method: 'POST',
      headers: { 
        Authorization: `Bearer ${creatorToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({}),
    });

    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.message).toBe('Interest not found');
  });

  it('should return 403 if user is not offer creator', async () => {
    const res = await app.request(`/offers/interests/${interest.id}/decline`, {
      method: 'POST',
      headers: { 
        Authorization: `Bearer ${otherToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({}),
    });

    expect(res.status).toBe(403);
    const data = await res.json();
    expect(data.message).toBe('Forbidden');
  });

  it('should return 409 if interest is not pending', async () => {
    // First decline the interest
    await prisma.interest.update({
      where: { id: interest.id },
      data: { status: InterestStatus.DECLINED }
    });

    const res = await app.request(`/offers/interests/${interest.id}/decline`, {
      method: 'POST',
      headers: { 
        Authorization: `Bearer ${creatorToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({}),
    });

    expect(res.status).toBe(409);
    const data = await res.json();
    expect(data.message).toBe('Interest is not pending');
  });
});

describe('GET /offers/browse/:offerId - Get Browsable Offer', () => {
  let offerCreator: any;
  let viewingUser: any;
  let creatorToken: string;
  let viewerToken: string;
  let offer: any;

  beforeEach(async () => {
    offerCreator = await createTestUser({ reputationLevel: 5 });
    viewingUser = await createTestUser({ reputationLevel: 3 });
    testUsers.push(offerCreator, viewingUser);

    creatorToken = generateToken(offerCreator.id, offerCreator.email);
    viewerToken = generateToken(viewingUser.id, viewingUser.email);
    
    offer = await createTestOffer(offerCreator.id, {
      type: OfferType.SELL,
      baseRate: 45000,
      adjustmentForLowerRep: 5,
      adjustmentForHigherRep: 10
    });
    testOffers.push(offer);
  });

  it('should return offer with calculated rate for other users', async () => {
    const res = await app.request(`/offers/browse/${offer.id}`, {
      method: 'GET',
      headers: { Authorization: `Bearer ${viewerToken}` },
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.id).toBe(offer.id);
    expect(data.offerCreatorId).toBe(offerCreator.id);
    expect(data.offerCreatorUsername).toBeDefined();
    expect(data.offerCreatorReputationLevel).toBe(5);
    
    // Rate should be adjusted for lower rep user viewing higher rep user's offer
    expect(data.calculatedApplicableRate).toBe(Math.round(45000 * 1.05));
  });

  it('should not adjust rate for offer creator viewing own offer', async () => {
    const res = await app.request(`/offers/browse/${offer.id}`, {
      method: 'GET',
      headers: { Authorization: `Bearer ${creatorToken}` },
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.calculatedApplicableRate).toBe(45000); // No adjustment
  });

  it('should include current user interest status', async () => {
    const interest = await createTestInterest(offer.id, viewingUser.id);

    const res = await app.request(`/offers/browse/${offer.id}`, {
      method: 'GET',
      headers: { Authorization: `Bearer ${viewerToken}` },
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.currentUserHasShownInterest).toBe(true);
    expect(data.currentUserInterestStatus).toBe('PENDING');
  });

  it('should return 404 for non-existent offer', async () => {
    const res = await app.request('/offers/browse/nonexistent-id', {
      method: 'GET',
      headers: { Authorization: `Bearer ${viewerToken}` },
    });

    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.message).toBe('Offer not found or creator details missing');
  });

  it('should return 404 for non-existent viewing user', async () => {
    const nonExistentToken = generateToken('nonexistent-id', '<EMAIL>');

    const res = await app.request(`/offers/browse/${offer.id}`, {
      method: 'GET',
      headers: { Authorization: `Bearer ${nonExistentToken}` },
    });

    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.message).toBe('Viewing user not found');
  });
});

describe('DELETE /offers/all-for-test - Test Data Cleanup', () => {
  let user: any;
  let token: string;

  beforeEach(async () => {
    user = await createTestUser();
    testUsers.push(user);
    token = generateToken(user.id, user.email);
  });

  it('should delete all test data successfully', async () => {
    // Create some test data
    const offer = await createTestOffer(user.id);
    const interest = await createTestInterest(offer.id, user.id);
    
    const res = await app.request('/offers/all-for-test', {
      method: 'DELETE',
      headers: { Authorization: `Bearer ${token}` },
    });

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.message).toBe('Successfully deleted all offers and related test data.');

    // Verify data was actually deleted
    const remainingOffers = await prisma.offer.findMany();
    const remainingInterests = await prisma.interest.findMany();
    expect(remainingOffers).toHaveLength(0);
    expect(remainingInterests).toHaveLength(0);
  });

  it('should handle foreign key constraint violations gracefully', async () => {
    // This test simulates a constraint violation scenario
    // In practice, this might be hard to reproduce, but we test the error handling path
    
    const res = await app.request('/offers/all-for-test', {
      method: 'DELETE',
      headers: { Authorization: `Bearer ${token}` },
    });

    // Should succeed normally, but if there were constraint issues, it would return 409 or 500
    expect([200, 409, 500]).toContain(res.status);
  });
});

// Global cleanup after all tests
afterAll(async () => {
  try {
    // Final cleanup of any remaining test data - only test-specific data
    await prisma.transaction.deleteMany({
      where: {
        OR: [
          { offer: { user: { email: { contains: 'test' } } } },
          { offer: { user: { username: { contains: 'test' } } } }
        ]
      }
    });
    await prisma.chatMessage.deleteMany({
      where: {
        OR: [
          { chatSession: { userOne: { email: { contains: 'test' } } } },
          { chatSession: { userTwo: { email: { contains: 'test' } } } }
        ]
      }
    });
    await prisma.chatSession.deleteMany({
      where: {
        OR: [
          { userOne: { email: { contains: 'test' } } },
          { userTwo: { email: { contains: 'test' } } }
        ]
      }
    });
    await prisma.notification.deleteMany({
      where: {
        user: { email: { contains: 'test' } }
      }
    });
    await prisma.interest.deleteMany({
      where: {
        OR: [
          { interestedUser: { email: { contains: 'test' } } },
          { offer: { user: { email: { contains: 'test' } } } }
        ]
      }
    });
    await prisma.offer.deleteMany({
      where: {
        user: { email: { contains: 'test' } }
      }
    });
    await prisma.user.deleteMany({
      where: {
        OR: [
          { email: { contains: 'test' } },
          { username: { contains: 'test' } }
        ]
      }
    });
  } catch (error) {
    console.error('Error during global cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
});
