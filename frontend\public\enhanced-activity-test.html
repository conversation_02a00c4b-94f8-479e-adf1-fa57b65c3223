<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced ActivitySection Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .test-info {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .test-info h2 {
            margin-top: 0;
            color: #1e293b;
        }
        
        .test-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .test-info li {
            margin-bottom: 8px;
            color: #64748b;
            line-height: 1.6;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
        }
        
        .demo-title {
            font-size: 1.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1.5rem;
            color: #1e293b;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.8s ease 0.1s forwards;
        }
        
        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .activity-item {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }
        
        .activity-item:nth-child(1) { animation-delay: 0.45s; }
        .activity-item:nth-child(2) { animation-delay: 0.6s; }
        .activity-item:nth-child(3) { animation-delay: 0.75s; }
        
        .offer-card {
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.98);
            border: 1px solid rgba(0, 0, 0, 0.08);
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            position: relative;
            min-height: 100px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .offer-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.08);
        }
        
        .offer-card:active {
            transform: translateY(-2px) scale(0.98);
            transition: all 0.1s ease;
        }
        
        .offer-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 1.25rem;
            gap: 1rem;
        }
        
        .offer-info {
            flex: 1;
            min-width: 0;
        }
        
        .offer-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
            gap: 0.5rem;
        }
        
        .offer-title {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
        }
        
        .offer-icon {
            font-size: 1.125rem;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }
        
        .status-tag {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        .status-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .offer-description {
            margin: 0;
            color: #64748b;
            font-size: 0.875rem;
            line-height: 1.4;
        }
        
        .offer-amount {
            text-align: right;
            min-width: fit-content;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.25rem;
        }
        
        .amount-display {
            font-size: 1.25rem;
            font-weight: 700;
            color: #18a058;
            display: flex;
            align-items: baseline;
            gap: 0.125rem;
        }
        
        .currency-symbol {
            font-size: 1rem;
            opacity: 0.8;
        }
        
        .amount-label {
            font-size: 0.75rem;
            color: #666;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.8s ease 0.3s forwards;
        }
        
        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        
        .empty-state h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
        }
        
        .empty-state p {
            margin: 0;
            color: #64748b;
            font-size: 0.9rem;
            line-height: 1.6;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .offer-content {
                padding: 1rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }
            
            .offer-amount {
                align-self: flex-end;
                text-align: right;
            }
            
            .offer-card:hover {
                transform: translateY(-2px) scale(1.01);
            }
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .status.enhanced {
            background: #10b981;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Enhanced ActivitySection Test</h1>
        
        <div class="test-info">
            <h2>Enhanced Features <span class="status enhanced">✓ COMPLETE</span></h2>
            <ul>
                <li><strong>Mobile-First Design:</strong> Touch-friendly cards with responsive layout optimization</li>
                <li><strong>Skeleton Loading:</strong> Professional shimmer loading states with staggered reveals</li>
                <li><strong>Staggered Animations:</strong> Activity items appear progressively (0.45s, 0.6s, 0.75s delays)</li>
                <li><strong>Enhanced Card Layout:</strong> Improved info hierarchy with status tags and emoji icons</li>
                <li><strong>Touch Interactions:</strong> Haptic-like feedback with scale transforms</li>
                <li><strong>Empty State:</strong> Beautiful placeholder when no activity exists</li>
                <li><strong>Accessibility:</strong> Reduced motion and high contrast support</li>
                <li><strong>Theme Support:</strong> Light and dark theme compatibility</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">Recent Activity</h2>
            <div class="activity-list">
                <div class="activity-item" onclick="handleCardClick('Exchange Offer 1')">
                    <div class="offer-card">
                        <div class="offer-content">
                            <div class="offer-info">
                                <div class="offer-header">
                                    <h4 class="offer-title">
                                        <span class="offer-icon">💰</span>
                                        Buying USD
                                    </h4>
                                    <span class="status-tag status-success">ACTIVE</span>
                                </div>
                                <p class="offer-description">At rate 1.35 CAD/USD</p>
                            </div>
                            <div class="offer-amount">
                                <div class="amount-display">
                                    <span class="currency-symbol">$</span>
                                    <span>5,000</span>
                                </div>
                                <div class="amount-label">Amount</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="activity-item" onclick="handleCardClick('Exchange Offer 2')">
                    <div class="offer-card">
                        <div class="offer-content">
                            <div class="offer-info">
                                <div class="offer-header">
                                    <h4 class="offer-title">
                                        <span class="offer-icon">💸</span>
                                        Selling EUR
                                    </h4>
                                    <span class="status-tag status-warning">PENDING</span>
                                </div>
                                <p class="offer-description">At rate 1.48 CAD/EUR</p>
                            </div>
                            <div class="offer-amount">
                                <div class="amount-display">
                                    <span class="currency-symbol">$</span>
                                    <span>2,500</span>
                                </div>
                                <div class="amount-label">Amount</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="activity-item" onclick="handleCardClick('Exchange Offer 3')">
                    <div class="offer-card">
                        <div class="offer-content">
                            <div class="offer-info">
                                <div class="offer-header">
                                    <h4 class="offer-title">
                                        <span class="offer-icon">💰</span>
                                        Buying IRR
                                    </h4>
                                    <span class="status-tag status-success">ACTIVE</span>
                                </div>
                                <p class="offer-description">At rate 55,000 IRR/CAD</p>
                            </div>
                            <div class="offer-amount">
                                <div class="amount-display">
                                    <span class="currency-symbol">$</span>
                                    <span>1,200</span>
                                </div>
                                <div class="amount-label">Amount</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Example Empty State -->
        <div class="demo-section">
            <h2 class="demo-title">Empty State Example</h2>
            <div class="empty-state">
                <div class="empty-icon">📋</div>
                <h3>No Recent Activity</h3>
                <p>Your recent currency exchange activity will appear here</p>
            </div>
        </div>
    </div>
    
    <script>
        function handleCardClick(cardName) {
            // Haptic-like feedback simulation
            const card = event.currentTarget.querySelector('.offer-card');
            card.style.transform = 'scale(0.98)';
            
            setTimeout(() => {
                card.style.transform = '';
                alert(`Clicked: ${cardName}\n\nIn the real app, this would show offer details or navigate to the transaction.`);
            }, 150);
        }
        
        // Log test completion
        console.log('✅ Enhanced ActivitySection Test Loaded');
        console.log('📱 Mobile-first responsive design');
        console.log('🎨 Staggered entrance animations');
        console.log('💫 Enhanced touch interactions');
        console.log('♿ Accessibility and reduced motion support');
        
        // Test animation completion
        setTimeout(() => {
            console.log('🎬 All activity animations completed');
        }, 1500);
    </script>
</body>
</html>
