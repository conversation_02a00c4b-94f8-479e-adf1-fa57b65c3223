// Quick Interest Store Socket Debug Script
// Copy and paste this into the browser console while on the browse offers page

console.log('🔍 [QUICK DEBUG] Checking Interest Store Socket Setup...');

// Get Pinia stores
const stores = window.$nuxt?.$pinia || window.vue?.$pinia || (() => {
  try {
    // Try to get the pinia instance from the Vue app
    const app = document.querySelector('#app').__vueParentComponent;
    return app?.appContext?.app?.config?.globalProperties?.$pinia;
  } catch (e) {
    console.log('Could not access pinia directly');
    return null;
  }
})();

if (stores) {
  console.log('✅ [QUICK DEBUG] Found Pinia stores');
  
  // List all active stores
  const storeNames = Object.keys(stores._s || {});
  console.log('🔍 [QUICK DEBUG] Active stores:', storeNames);
  
  // Check if interestStore is active
  const hasInterestStore = storeNames.includes('interestStore');
  console.log('🔍 [QUICK DEBUG] InterestStore active:', hasInterestStore);
  
  if (hasInterestStore) {
    console.log('✅ [QUICK DEBUG] InterestStore is active');
  } else {
    console.log('⚠️ [QUICK DEBUG] InterestStore not active - this might be the issue!');
    console.log('💡 [QUICK DEBUG] The interestStore may not have been initialized on this page');
  }
} else {
  console.log('❌ [QUICK DEBUG] Could not access Pinia stores');
}

// Check centralized socket manager
try {
  const socket = window.centralizedSocketManager?.getSocket?.();
  if (socket) {
    console.log('✅ [QUICK DEBUG] Socket found:', socket.id);
    console.log('🔍 [QUICK DEBUG] Socket connected:', socket.connected);
    
    // Check if there are any listeners for our event
    const eventListeners = socket.listeners('INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY');
    console.log('🔍 [QUICK DEBUG] Event listeners for INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY:', eventListeners.length);
    
    if (eventListeners.length === 0) {
      console.log('⚠️ [QUICK DEBUG] No listeners registered for INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY');
      console.log('💡 [QUICK DEBUG] This confirms the issue - interestStore listeners not registered');
    }
  } else {
    console.log('❌ [QUICK DEBUG] No socket found');
  }
} catch (e) {
  console.log('❌ [QUICK DEBUG] Error checking socket:', e);
}

console.log('🔍 [QUICK DEBUG] Diagnosis complete. Check the messages above for clues.');
