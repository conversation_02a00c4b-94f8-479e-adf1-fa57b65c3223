const http = require('http');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/auth/me',
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN_HERE' // We'll replace this with the actual token
  }
};

const req = http.request(options, (res) => {
  console.log('Status Code:', res.statusCode);
  console.log('Headers:', res.headers);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('Response:', data);
  });
});

req.on('error', (error) => {
  console.error('Error:', error);
});

req.end();
