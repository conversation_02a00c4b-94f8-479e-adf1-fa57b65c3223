const { PrismaClient } = require('@prisma/client');

async function analyzeConstraintIssue() {
  const prisma = new PrismaClient();
  
  try {
    console.log('=== Analyzing Constraint Issue ===');
    
    const userId = 'cmbqrmwcv0001vl48wlfffva4';
    const currency = 'CAD';
    
    // Get all payment methods for this user/currency
    const allMethods = await prisma.paymentReceivingInfo.findMany({
      where: {
        userId: userId,
        currency: currency
      },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log(`Found ${allMethods.length} CAD payment methods for user ${userId}:`);
    allMethods.forEach(method => {
      console.log(`- ID: ${method.id.slice(-8)}`);
      console.log(`  Active: ${method.isActive}`);
      console.log(`  Default: ${method.isDefaultForUser}`);
      console.log(`  Created: ${method.createdAt}`);
      console.log('');
    });
    
    // Check if there's an active default method
    const activeDefault = allMethods.find(m => m.isActive && m.isDefaultForUser);
    const inactiveDefault = allMethods.find(m => !m.isActive && m.isDefaultForUser);
    
    console.log('Analysis:');
    console.log(`Active default method: ${activeDefault ? 'YES' : 'NO'}`);
    console.log(`Inactive default method: ${inactiveDefault ? 'YES' : 'NO'}`);
    
    if (activeDefault && inactiveDefault) {
      console.log('\n❌ PROBLEM: Both active and inactive methods marked as default!');
      console.log('This violates the unique constraint when trying to update the inactive one.');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

analyzeConstraintIssue();
