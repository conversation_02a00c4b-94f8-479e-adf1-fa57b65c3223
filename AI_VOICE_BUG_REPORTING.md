# AI-Powered Voice Bug Reporting System

## Overview

This document describes the implementation of an AI-powered voice bug reporting system for MUNygo, designed to make bug reporting extremely user-friendly for non-technical users through voice input and automatic form completion.

## Features

### 🎤 Voice Recording
- **Browser-based audio recording** using MediaRecorder API
- **Up to 60 seconds** of recording time with visual feedback
- **Multiple audio format support** (WebM Opus, OGG Opus, WAV, MP3)
- **Real-time duration tracking** with progress indicators
- **Pause/Resume functionality** during recording
- **Audio playback** for review before submission

### 🤖 AI Analysis with Gemini 2.0 Flash
- **Speech-to-text transcription** using Google Speech-to-Text API
- **Intelligent bug report generation** using Gemini 2.0 Flash Preview model
- **Automatic field extraction**:
  - Bug title and description
  - Steps to reproduce
  - Expected vs actual behavior
  - Severity assessment (low, medium, high, critical)
  - Report type classification (bug, feature-request, performance, etc.)
  - Relevant tags and categories
- **Confidence scoring** for AI-generated content
- **Multi-language support** (English and Persian)

### 🎯 User Experience
- **One-click recording** - just press the microphone button
- **AI-generated form preview** with confidence indicators
- **Manual review and editing** of AI suggestions before submission
- **Seamless integration** with existing debug report form
- **Fallback to manual entry** if AI services are unavailable
- **Real-time processing feedback** with progress indicators

## Technical Architecture

### Backend Components

#### 1. AI Service (`backend/src/services/aiService.ts`)
- **Google Gen AI SDK integration** using `@google/genai`
- **Gemini 2.0 Flash Preview model** for analysis
- **Structured JSON output** with response validation
- **Error handling and fallback responses**
- **Configurable generation parameters**

#### 2. Audio Transcription Service (`backend/src/services/audioTranscriptionService.ts`)
- **Google Speech-to-Text API integration**
- **Multiple audio format support**
- **Language detection and processing**
- **Audio validation and preprocessing**
- **Error handling for transcription failures**

#### 3. API Routes (`backend/src/routes/aiRoutes.ts`)
- `GET /api/ai/status` - Check service availability
- `GET /api/ai/config` - Get configuration for frontend
- `POST /api/ai/transcribe` - Audio transcription only
- `POST /api/ai/analyze` - Text analysis only
- `POST /api/ai/voice-to-report` - Combined transcription + analysis

#### 4. Type Definitions (`backend/src/types/schemas/aiSchemas.ts`)
- **Zod schemas** for request/response validation
- **TypeScript types** for type safety
- **Comprehensive error handling types**

### Frontend Components

#### 1. Voice Recorder Component (`frontend/src/components/VoiceRecorder.vue`)
- **MediaRecorder API integration**
- **Real-time audio visualization**
- **Recording controls (start, stop, pause, resume)**
- **Audio playback functionality**
- **Error handling and user feedback**

#### 2. AI Analysis Composable (`frontend/src/composables/useAiAnalysis.ts`)
- **API service integration**
- **State management for AI processing**
- **Error handling and retry logic**
- **Type-safe data transformation**

#### 3. Voice Recording Composable (`frontend/src/composables/useVoiceRecording.ts`)
- **Browser audio recording logic**
- **Audio format detection and optimization**
- **Base64 encoding for API transmission**
- **Recording state management**

#### 4. Enhanced Debug Report Button (`frontend/src/components/DebugReportButtonEnhanced.vue`)
- **Integrated voice recording interface**
- **AI-generated report preview**
- **Manual form editing capabilities**
- **Seamless fallback to traditional input**

## Configuration

### Environment Variables

#### Backend
```bash
# Required for AI features
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Separate Speech API key (falls back to GEMINI_API_KEY)
GOOGLE_SPEECH_API_KEY=your_speech_api_key_here
```

#### Frontend
```bash
# API base URL (defaults to http://localhost:3000)
VITE_API_BASE_URL=http://localhost:3000
```

### Dependencies

#### Backend
```bash
npm install @google/genai
```

#### Frontend
No additional dependencies required (uses existing VueUse and Naive UI)

## Usage

### For Users
1. **Click the microphone button** in the bug report form
2. **Record your issue description** (up to 60 seconds)
3. **Review the AI-generated report** with confidence indicators
4. **Edit or approve** the generated content
5. **Submit** the completed bug report

### For Developers

#### Testing AI Service
```bash
cd backend
npm test -- aiService.test.ts
```

#### Checking Service Status
```bash
curl http://localhost:3000/api/ai/status
```

#### Manual API Testing
```bash
# Transcribe audio
curl -X POST http://localhost:3000/api/ai/transcribe \
  -H "Content-Type: application/json" \
  -d '{"audioData":"base64_audio_data","mimeType":"audio/webm","duration":10}'

# Analyze text
curl -X POST http://localhost:3000/api/ai/analyze \
  -H "Content-Type: application/json" \
  -d '{"transcription":"The login button is not working"}'
```

## Internationalization

The system supports both English and Persian languages:

### Voice Recognition
- **English (en-US)** - Primary language
- **Persian (fa-IR)** - Full RTL support

### AI Analysis
- **Context-aware language processing**
- **Localized response generation**
- **Cultural and linguistic considerations**

## Error Handling

### Graceful Degradation
- **Service unavailable**: Falls back to manual form entry
- **Transcription failure**: Allows manual text input
- **AI analysis failure**: Provides basic form structure
- **Network issues**: Offline support with sync when online

### User Feedback
- **Clear error messages** in user's language
- **Retry mechanisms** for transient failures
- **Progress indicators** during processing
- **Confidence indicators** for AI-generated content

## Security Considerations

### API Key Management
- **Server-side only** - API keys never exposed to client
- **Environment variable configuration**
- **Secure key rotation support**

### Audio Data
- **Temporary processing** - audio not permanently stored
- **Base64 transmission** with size limits
- **Client-side recording** with user permission

### Privacy
- **No persistent audio storage**
- **Anonymized processing** when possible
- **User consent** for microphone access

## Performance Optimization

### Audio Processing
- **Optimized audio formats** (WebM Opus preferred)
- **Compression and quality balance**
- **Streaming where possible**

### AI Processing
- **Efficient prompt engineering**
- **Response caching** for similar requests
- **Timeout handling** for long-running requests

### Frontend
- **Lazy loading** of AI components
- **Progressive enhancement**
- **Responsive design** for all devices

## Future Enhancements

### Planned Features
- **Real-time transcription** during recording
- **Voice commands** for form navigation
- **Multi-modal input** (voice + screen recording)
- **Advanced AI analysis** with context awareness
- **Batch processing** for multiple reports

### Technical Improvements
- **WebRTC integration** for better audio quality
- **Edge AI processing** for faster responses
- **Advanced prompt engineering** for better accuracy
- **Integration with issue tracking systems**

## Troubleshooting

### Common Issues

#### "AI service not available"
- Check `GEMINI_API_KEY` environment variable
- Verify API key permissions and quotas
- Check network connectivity

#### "Microphone access denied"
- Ensure HTTPS connection (required for microphone access)
- Check browser permissions
- Verify microphone hardware

#### "Transcription failed"
- Check audio quality and clarity
- Verify supported audio formats
- Check Speech API quotas and permissions

#### "Analysis confidence low"
- Encourage clearer speech
- Provide more context in recording
- Use manual form entry for complex issues

## Support

For technical support or feature requests related to the AI voice bug reporting system, please:

1. Check this documentation first
2. Review the error logs in browser console
3. Test with manual form entry as fallback
4. Contact the development team with specific error messages

---

**Note**: This system requires active internet connection and valid API keys for full functionality. Manual form entry remains available as a fallback option.
