const axios = require('axios');
const { io } = require('socket.io-client');
const chalk = require('chalk');

const API_BASE_URL = 'http://localhost:3000/api';
const SOCKET_URL = 'http://localhost:3000';

// Demo user credentials
const DEMO_USER_1 = { email: '<EMAIL>', password: 'password123' };
const DEMO_USER_2 = { email: '<EMAIL>', password: 'password123' };

// Transaction details
let transactionId = ''; // Will be set after creating a transaction
let chatSessionId = ''; // Will be set after creating a transaction

// API clients for each user
const user1Api = axios.create({ baseURL: API_BASE_URL });
const user2Api = axios.create({ baseURL: API_BASE_URL });

// Socket.IO clients for each user
let user1Socket;
let user2Socket;

// Test state
let testStep = 0;
const testResults = [];

// Helper to log test steps and results
const logStep = (description) => {
  testStep++;
  console.log(chalk.bold.yellow(`
--- Step ${testStep}: ${description} ---`));
};

const logResult = (description, success) => {
  const status = success ? chalk.green('PASS') : chalk.red('FAIL');
  console.log(`${status}: ${description}`);
  testResults.push({ step: testStep, description, success });
};

// Helper to set up auth tokens for API clients
const setupAuth = async (user, apiClient) => {
  try {
    const response = await apiClient.post('/auth/login', user);
    const token = response.data.token;
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    logResult(`Authenticated user ${user.email}`, true);
    return token;
  } catch (error) {
    logResult(`Failed to authenticate user ${user.email}`, false);
    throw new Error('Authentication failed');
  }
};

// Helper to set up Socket.IO connection
const setupSocket = (token, userEmail) => {
  return new Promise((resolve, reject) => {
    const socket = io(SOCKET_URL, {
      auth: { token },
      transports: ['websocket'],
    });

    socket.on('connect', () => {
      logResult(`Socket connected for user ${userEmail}`, true);
      resolve(socket);
    });

    socket.on('connect_error', (error) => {
      logResult(`Socket connection error for user ${userEmail}: ${error.message}`, false);
      reject(error);
    });
  });
};

// Main test function
const runTest = async () => {
  try {
    // --- Test Setup ---
    logStep('Authenticating users and setting up sockets');
    const user1Token = await setupAuth(DEMO_USER_1, user1Api);
    const user2Token = await setupAuth(DEMO_USER_2, user2Api);

    user1Socket = await setupSocket(user1Token, DEMO_USER_1.email);
    user2Socket = await setupSocket(user2Token, DEMO_USER_2.email);

    // --- Create a new transaction for testing ---
    logStep('Creating a new transaction');
    const offerResponse = await user1Api.post('/offers', {
      type: 'SELL',
      amount: 100,
      baseRate: 1.2,
      adjustmentForLowerRep: 1,
      adjustmentForHigherRep: -1,
      currencyPair: 'USD-CAD'
    });
    const offerId = offerResponse.data.id;
    logResult('User 1 created an offer', true);

    const interestResponse = await user2Api.post(`/offers/${offerId}/interest`);
    const interestId = interestResponse.data.interestId;
    logResult('User 2 showed interest in the offer', true);

    const acceptInterestResponse = await user1Api.post(`/offers/interests/${interestId}/accept`);
    transactionId = acceptInterestResponse.data.transactionId;
    chatSessionId = acceptInterestResponse.data.chatSessionId;
    logResult('User 1 accepted the interest, transaction created', true);


    // --- Test Execution ---
    await testStep1_InitialState();
    await testStep2_SendMessage();
    await testStep3_DesignatePayer();
    await testStep4_ProvidePaymentInfo();
    await testStep5_ConfirmPayment();
    await testStep6_SecondPayment();
    await testStep7_FinalizeTransaction();

    // --- Test Teardown ---
    logStep('Test finished. Disconnecting sockets.');
    user1Socket.disconnect();
    user2Socket.disconnect();

    // --- Report Summary ---
    const successCount = testResults.filter(r => r.success).length;
    const failCount = testResults.length - successCount;
    console.log('--- Test Summary ---');
    console.log(chalk.green(`Passed: ${successCount}`));
    console.log(chalk.red(`Failed: ${failCount}`));

  } catch (error) {
    console.error(chalk.red.bold('--- A critical error occurred during the test ---'));
    console.error(error.message);
  }
};

// Test step functions
const testStep1_InitialState = async () => {
    logStep('Verifying initial transaction state');
    const response = await user1Api.get(`/transactional-chat/${transactionId}`);
    const details = response.data.data;

    logResult('Transaction status is PENDING_AGREEMENT', details.status === 'PENDING_AGREEMENT');
    logResult('Current step index is 0', details.currentStepIndex === 0);
};

const testStep2_SendMessage = async () => {
    logStep('Sending a chat message');
    const messageText = 'Hello, let us trade!';

    // Listen for the message on user 2's socket
    const messagePromise = new Promise(resolve => {
        user2Socket.on('CHAT_MESSAGE_RECEIVE', (payload) => {
            if (payload.content === messageText) {
                logResult('User 2 received the chat message via socket', true);
                resolve();
            }
        });
    });

    await user1Api.post(`/transactional-chat/${transactionId}/messages`, { messageText });
    await messagePromise;
};

const testStep3_DesignatePayer = async () => {
    logStep('Designating the first payer');
    const designatePayerPromise = new Promise(resolve => {
        user1Socket.on('TRANSACTION_STATUS_UPDATED', (payload) => {
            if (payload.status === 'AWAITING_FIRST_PAYER_PAYMENT') {
                logResult('User 1 received transaction status update via socket', true);
                resolve();
            }
        });
    });

    await user1Api.post(`/transactional-chat/${transactionId}/actions`, {
        actionType: 'negotiation',
        data: { designatedPayerId: DEMO_USER_1.email } // Assuming user 1 pays first
    });

    await designatePayerPromise;

    const response = await user1Api.get(`/transactional-chat/${transactionId}`);
    const details = response.data.data;
    logResult('Transaction status is AWAITING_FIRST_PAYER_PAYMENT', details.status === 'AWAITING_FIRST_PAYER_PAYMENT');
    logResult('Current step index is 2', details.currentStepIndex === 2);
};

const testStep4_ProvidePaymentInfo = async () => {
    logStep('Providing payment information');
    const paymentInfoPromise = new Promise(resolve => {
        user1Socket.on('TRANSACTION_STATUS_UPDATED', (payload) => {
            if (payload.status === 'AWAITING_FIRST_PAYER_DESIGNATION') {
                logResult('User 1 received transaction status update after providing payment info', true);
                resolve();
            }
        });
    });

    await user1Api.post(`/transactional-chat/${transactionId}/actions`, {
        actionType: 'paymentInfo',
        data: { paymentMethodId: 'some-payment-method-id' } // Replace with a valid payment method ID
    });

    await paymentInfoPromise;

    const response = await user1Api.get(`/transactional-chat/${transactionId}`);
    const details = response.data.data;
    logResult('Transaction status is AWAITING_FIRST_PAYER_DESIGNATION', details.status === 'AWAITING_FIRST_PAYER_DESIGNATION');
    logResult('Current step index is 1', details.currentStepIndex === 1);
};

const testStep5_ConfirmPayment = async () => {
    logStep('Confirming payment');
    const confirmPaymentPromise = new Promise(resolve => {
        user2Socket.on('TRANSACTION_STATUS_UPDATED', (payload) => {
            if (payload.status === 'AWAITING_SECOND_PAYER_PAYMENT') {
                logResult('User 2 received transaction status update after payment confirmation', true);
                resolve();
            }
        });
    });

    await user1Api.post(`/transactional-chat/${transactionId}/actions`, {
        actionType: 'confirmReceipt'
    });

    await confirmPaymentPromise;

    const response = await user2Api.get(`/transactional-chat/${transactionId}`);
    const details = response.data.data;
    logResult('Transaction status is AWAITING_SECOND_PAYER_PAYMENT', details.status === 'AWAITING_SECOND_PAYER_PAYMENT');
    logResult('Current step index is 4', details.currentStepIndex === 4);
};

const testStep6_SecondPayment = async () => {
    logStep('Second user making payment');
    const secondPaymentPromise = new Promise(resolve => {
        user1Socket.on('TRANSACTION_STATUS_UPDATED', (payload) => {
            if (payload.status === 'AWAITING_FIRST_PAYER_CONFIRMATION') {
                logResult('User 1 received transaction status update after second payment', true);
                resolve();
            }
        });
    });

    await user2Api.post(`/transactional-chat/${transactionId}/actions`, {
        actionType: 'secondPayment',
        data: { trackingNumber: 'some-tracking-number' }
    });

    await secondPaymentPromise;

    const response = await user1Api.get(`/transactional-chat/${transactionId}`);
    const details = response.data.data;
    logResult('Transaction status is AWAITING_FIRST_PAYER_CONFIRMATION', details.status === 'AWAITING_FIRST_PAYER_CONFIRMATION');
    logResult('Current step index is 5', details.currentStepIndex === 5);
};

const testStep7_FinalizeTransaction = async () => {
    logStep('Finalizing the transaction');
    const finalizePromise = new Promise(resolve => {
        user2Socket.on('TRANSACTION_STATUS_UPDATED', (payload) => {
            if (payload.status === 'COMPLETED') {
                logResult('User 2 received transaction status update after finalization', true);
                resolve();
            }
        });
    });

    await user1Api.post(`/transactional-chat/${transactionId}/actions`, {
        actionType: 'rateExperience',
        data: { rating: 5, comment: 'Great transaction!' }
    });

    await finalizePromise;

    const response = await user2Api.get(`/transactional-chat/${transactionId}`);
    const details = response.data.data;
    logResult('Transaction status is COMPLETED', details.status === 'COMPLETED');
    logResult('Current step index is 6', details.currentStepIndex === 6);
};

runTest();