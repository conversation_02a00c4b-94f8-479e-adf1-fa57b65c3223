**Product Requirements Document: MUNygo MVP (Bronze Level)**

**Version:** 1.0
**Date:** October 26, 2023
**Status:** Draft
**Author/Owner:** [Your Name/Project Lead]

**1. Introduction & Purpose**

*   **1.1. Overview:** MUNygo aims to simplify and secure peer-to-peer (P2P) currency exchange for Iranian students and expatriates abroad, initially focusing on the Canada-Iran corridor (CAD-IRR). Traditional methods like صرافی (Sarrafi - exchanges) are often costly with unfavorable rates, while informal P2P methods carry significant risk of scams and delays.
*   **1.2. Problem:** Iranian students/expats in Canada lack a trustworthy, efficient, and cost-effective platform to find partners for exchanging CAD to IRR (and vice-versa) via the "هم‌ارز" (Ham-arz - equivalent value transfer) method, where no money crosses international borders directly. Existing informal methods (e.g., Telegram groups) lack transparency, security, and structure.
*   **1.3. Solution (MVP Vision):** This MVP introduces MUNygo's "Bronze" service level as a responsive web application. It acts as a **matchmaking and reputation platform**. It allows users to post buy/sell offers, discover others' offers with prices adjusted based on a mutual reputation system, and connect via a secure in-app chat to manually exchange payment details and coordinate the external bank transfers. The MVP *does not* handle any financial transactions directly.
*   **1.4. Scope:** This document defines the requirements solely for the initial MVP launch, focusing on the core user flow for the CAD-IRR currency pair.

**2. Goals & Objectives (MVP)**

*   **2.1. Validate Core User Need:** Confirm significant user interest and adoption of a dedicated platform for P2P CAD-IRR exchange discovery.
*   **2.2. Validate Reputation System Concept:** Assess if users understand and value the reputation level system and the tiered pricing mechanism based on it. Does it influence their decision to connect?
*   **2.3. Test Core User Flow:** Ensure users can successfully register, verify, create/find offers, initiate chat, and rate interactions smoothly.
*   **2.4. Achieve Initial User Base:** Target [e.g., 100-200] registered and verified (Email + Phone) users within [e.g., 2 months] post-launch.
*   **2.5. Facilitate Connections:** Measure the number of mutually accepted chat initiations as an indicator of successful matchmaking.
*   **2.6. Gather Qualitative Feedback:** Collect user feedback on usability, trust perception, feature gaps, and overall satisfaction to inform V2 development.

**3. Target Audience (MVP)**

*   **3.1. Primary:** Iranian international students currently residing in Canada who need to frequently exchange CAD funds they possess for IRR in an Iranian bank account (or vice-versa).
*   **3.2. Secondary:** Other Iranian residents in Canada (e.g., new immigrants, professionals) with similar P2P exchange needs for the CAD-IRR pair.

**4. Features & Functionalities (MVP)**

*(Note: Detailed User Stories with specific Acceptance Criteria should be created separately based on this section. See User Story Template provided previously.)*

*   **4.1. User Account Management:**
    *   **4.1.1. Registration:** New users register using Email and Password.
    *   **4.1.2. Email Verification:** Mandatory step post-registration via a verification link sent to the user's email. Users cannot access core features before verifying.
    *   **4.1.3. Phone Verification:** Required step using SMS OTP verification. Users cannot create offers or initiate chats before verifying phone.
    *   **4.1.4. Login:** Registered and verified users can log in using Email and Password. Standard session/token management implemented.
    *   **4.1.5. Basic Profile View:** A non-editable screen displaying Username, Join Date, Email Verified Status, Phone Verified Status, and current **Reputation Level (1-5)**.

*   **4.2. Offer Management:**
    *   **4.2.1. Create Offer (Sell/Buy):**
        *   Select Type: Sell or Buy.
        *   Select Currency Pair: Fixed to CAD-IRR for MVP.
        *   Enter Amount: Specify the amount of the currency being offered (e.g., 100 CAD if selling CAD).
        *   Set Base Rate: Define the exchange rate (e.g., IRR per CAD) applicable if trading with a user of the *same* reputation level.
        *   **Set Simplified Tiered Adjustments:** Define *one* rate/percentage adjustment for trading with **Lower Reputation Users (Levels 1 & 2 combined)** and *one* rate/percentage adjustment for trading with **Higher Reputation Users (Levels 4 & 5 combined)** relative to the Base Rate.
        *   Set Status: Active (visible in listings) or Inactive (saved but hidden).
    *   **4.2.2. My Offers:** Users can view a list of their own created offers, see their status, and toggle them between Active and Inactive.

*   **4.3. Offer Discovery & Matching:**
    *   **4.3.1. Browse Offers:** Display a list of all *Active* offers from other users.
    *   **4.3.2. Basic Filtering:** Allow users to filter the list by Offer Type (Buy CAD / Sell CAD). Currency pair filter is implicit as only CAD-IRR is supported.
    *   **4.3.3. Offer Card Display:** Each offer in the list must clearly show: Amount & Currencies, Offer Type, Creator's Username, Creator's **Reputation Level**, and the **Dynamically Calculated Rate Applicable to the *Viewing User*** based on the viewer's reputation level and the offer's tiered settings.

*   **4.4. Connection & Communication:**
    *   **4.4.1. Initiate Contact:** A "Show Interest" button on offer cards allows a user to express interest in an offer.
    *   **4.4.2. Interest Notification & Acceptance:** The offer creator receives an in-app notification showing the interested user's username and reputation level. The creator must explicitly "Accept" the interest.
    *   **4.4.3. In-App Chat:** Upon mutual acceptance, a private, secure, 1-to-1 **text-only** chat window is opened between the two users within the application.
        *   Users can send and receive text messages in real-time.
        *   Chat history is persistent and accessible only to the two participants.
        *   **Critical Use:** Securely and manually exchange payment details (e.g., Canadian Interac email, Iranian Shaba number).
        *   **NO file attachments or saved payment details.**

*   **4.5. Reputation System (MVP Implementation):**
    *   **4.5.1. Level Display:** User's calculated level (1-5: Newcomer, Verified, Reliable, Trusted, Elite) is displayed on their profile and next to their username on offers/chat.
    *   **4.5.2. Simplified Scoring:** Points are awarded based on:
        *   Successful Email Verification (+X points).
        *   Successful Phone Verification (+Y points).
        *   Rating Received (+Z points per rating, potentially weighted slightly by star count).
        *   (Optional) Rating Given (+W points).
    *   **4.5.3. Rating Mechanism:** After an interaction (chat concluded by user action like "Mark Complete"), users are prompted to provide a 1-5 star rating and optional text feedback for the *other* user involved in that specific interaction.
    *   **4.5.4. Level Calculation:** Predetermined score ranges map total accumulated points to Levels 1-5 (Ranges TBD based on MVP scoring potential).

*   **4.6. Safety & Support:**
    *   **4.6.1. User Reporting:** A simple "Report User" button is available on profiles or within chat. User selects a basic reason (Spam, Scam Attempt, Abusive). Reports are flagged for manual admin review.
    *   **4.6.2. Legal:** Users must agree to Terms of Service and Privacy Policy during signup. Links accessible within the app (e.g., footer). ToS explicitly states MUNygo is a platform provider only and not liable for P2P transaction outcomes.

**5. Design & UX Considerations (MVP)**

*   **5.1. Interface:** Clean, simple, intuitive navigation. Minimalist aesthetic.
*   **5.2. Platform:** Responsive Web Application, designed mobile-first. Ensure usability on common mobile and desktop browser sizes.
*   **5.3. Clarity:** Key information (Applicable Rate, Reputation Levels, Amounts) must be displayed prominently and unambiguously.
*   **5.4. Onboarding:** Minimal guidance (tooltips or welcome message) explaining the core P2P flow and the importance of reputation.
*   **5.5. Language:** Primarily Persian interface, potentially with basic English support for key terms if needed for the target audience.

**6. Non-Functional Requirements (MVP)**

*   **6.1. Security:**
    *   All traffic served over HTTPS.
    *   Passwords securely hashed (e.g., bcrypt).
    *   Basic input validation on all user inputs.
    *   Protection against common web vulnerabilities (e.g., basic XSS, CSRF prevention mechanisms provided by the web framework).
    *   **No storage of user bank account numbers or full credit card details.** Chat messages containing potentially sensitive info need secure handling/storage.
*   **6.2. Performance:**
    *   Page load times should feel responsive, not sluggish (no specific metrics for MVP).
    *   Chat message delivery should be near real-time.
*   **6.3. Reliability:**
    *   Core features (Auth, Offer list/create, Chat initiation/messaging) must be functional and available during expected usage hours.
    *   Basic error handling for common issues (e.g., network errors, invalid input).
*   **6.4. Scalability:** While not a primary MVP focus, the chosen architecture should not prevent future scaling (e.g., use of a standard relational DB like PostgreSQL).
*   **6.5. Maintainability:** Code should follow basic best practices for readability and organization, facilitating future development.

**7. Release Criteria (MVP Launch)**

*   All features defined in Section 4 are implemented and functional according to their acceptance criteria (defined in separate User Stories).
*   The core user flow (Register -> Verify -> Create/Find Offer -> Chat -> Rate) is tested end-to-end and deemed usable.
*   No known critical or blocking bugs related to core functionality or security.
*   Terms of Service and Privacy Policy are finalized and integrated into the signup flow.
*   Basic security checks completed.
*   Application successfully deployed to the production environment and accessible.
*   Basic monitoring and error logging are in place.

**8. Out of Scope / Future Considerations**

*   Direct payment integration / Escrow services.
*   File attachments or image uploads in chat.
*   Storing user payment details for reuse.
*   Advanced user identity verification (document uploads, facial recognition).
*   Automated user matching or personalized offer alerts.
*   Advanced search, sorting, or filtering options for offers.
*   Support for additional currency pairs.
*   Detailed transaction history tracking beyond basic chat logs.
*   Comprehensive Admin Dashboard with analytics.
*   Native Mobile Applications (iOS/Android).
*   AI-based chat monitoring or support.
*   Automated dispute resolution.

---

This PRD provides a solid foundation. The next step is to break down Section 4 into detailed User Stories using the template provided earlier, filling in specific Acceptance Criteria for each. This detailed breakdown will be your primary guide for prompting the LLM and building the MVP.