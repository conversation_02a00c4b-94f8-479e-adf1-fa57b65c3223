# Force rebuild production environment without cache
# This ensures all environment variables and code changes are properly applied

Write-Host "🚀 Starting forced rebuild of production environment (no cache)..." -ForegroundColor Green

# Stop and remove all containers, networks, and volumes
Write-Host "📦 Stopping and removing existing containers..." -ForegroundColor Yellow
docker-compose down --volumes --remove-orphans

# Remove all related images to force complete rebuild
Write-Host "🗑️ Removing existing images..." -ForegroundColor Yellow
docker-compose rm -f
docker rmi munygo-backend:latest, munygo-frontend:latest 2>$null

# Clear Docker build cache
Write-Host "🧹 Clearing Docker build cache..." -ForegroundColor Yellow
docker builder prune -f

# Build and start with no cache
Write-Host "🔨 Building and starting services (no cache)..." -ForegroundColor Yellow
docker-compose build --no-cache --pull
docker-compose up -d

# Show status
Write-Host "📊 Checking service status..." -ForegroundColor Yellow
docker-compose ps

Write-Host "✅ Forced rebuild complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Wait 30-60 seconds for services to fully start"
Write-Host "2. Check logs: docker-compose logs -f"
Write-Host "3. Test application at your configured URL"
Write-Host "4. Check admin panel and debug report button visibility"
Write-Host ""
Write-Host "🔍 To verify environment variables in frontend:" -ForegroundColor Cyan
Write-Host "   docker-compose exec frontend sh -c 'printenv | grep VITE'"
Write-Host ""
Write-Host "🔍 To check backend logs:" -ForegroundColor Cyan
Write-Host "   docker-compose logs backend"
