// Test admin access functionality
const ADMIN_EMAILS = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';

function checkAdminAccess(userEmail) {
  if (!userEmail) return false;
  
  const adminEmails = ADMIN_EMAILS.split(',').map(email => email.trim().toLowerCase());
  const normalizedEmail = userEmail.trim().toLowerCase();
  
  const isAdmin = adminEmails.includes(normalizedEmail);
  console.log(`Checking admin access for ${userEmail}: ${isAdmin}`);
  console.log(`Admin emails: ${adminEmails.join(', ')}`);
  
  return isAdmin;
}

// Test with our admin users
const testUsers = [
  '<EMAIL>',
  '<EMAIL>', 
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

console.log('Testing admin access:');
testUsers.forEach(email => {
  const result = checkAdminAccess(email);
  console.log(`${email}: ${result ? '✅ ADMIN' : '❌ NOT ADMIN'}`);
});
