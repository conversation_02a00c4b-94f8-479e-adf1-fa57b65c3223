import { defineStore } from 'pinia';
import apiClient from '@/services/apiClient'; // Assuming apiClient is correctly set up
import type { Transaction, RawTransactionApiResponse } from '@/types/transaction'; // Adjust path if needed
import type { TransactionStatusUpdatePayload } from '@/types/socketEvents'; // Import the new type
import {
  declarePayment as apiDeclarePayment,
  confirmReceipt as apiConfirmReceipt,
  cancelTransaction as apiCancelTransaction,
  disputeTransaction as apiDisputeTransaction,
} from '@/services/transactionApiService';

export interface TransactionState {
  currentTransaction: Transaction | null;
  isLoading: boolean;
  error: string | null;
  userRole: 'currencyAProvider' | 'currencyBProvider' | null;
}

export const useTransactionStore = defineStore('transaction', {
  state: (): TransactionState => ({
    currentTransaction: null,
    isLoading: false,
    error: null,
    userRole: null,
  }),
  actions: {
    // Example action to fetch transaction data
    async fetchTransactionByChatSessionId(chatSessionId: string, currentUserId: string) {
      if (!chatSessionId) {
        this.error = 'Chat session ID is required to fetch transaction details.';
        this.currentTransaction = null;
        return;
      }
      this.isLoading = true;
      this.error = null;
      try {
        // The backend now returns currencyAProvider and currencyBProvider as nested objects
        const response = await apiClient.get<RawTransactionApiResponse>(`/transactions/chat/${chatSessionId}`);
        const rawTxData = response.data;        if (rawTxData) {
          // Use the Transaction interface as-is, which includes currencyAProvider and currencyBProvider nested objects
          const processedTxData: Transaction = {
            ...rawTxData,
            // Keep the nested provider objects for accessing username and other properties
            currencyAProvider: rawTxData.currencyAProvider,
            currencyBProvider: rawTxData.currencyBProvider,
          };

          this.currentTransaction = processedTxData;

          // Determine user role (example logic)
          if (this.currentTransaction.currencyAProviderId === currentUserId) {
            this.userRole = 'currencyAProvider';
          } else if (this.currentTransaction.currencyBProviderId === currentUserId) {
            this.userRole = 'currencyBProvider';
          } else {
            this.userRole = null;
          }
        } else {
          this.currentTransaction = null;
          this.userRole = null;
        }
      } catch (err: any) {
        console.error('Error fetching transaction by chat session ID:', err);
        this.error = err.response?.data?.error || err.message || 'Failed to fetch transaction details.';
        this.currentTransaction = null;
        this.userRole = null;
      } finally {
        this.isLoading = false;
      }
    },

    // Action to handle updates from socket events
    // This assumes the socket payload might also need processing if it contains the full transaction object
    // with nested provider details.
    // If the socket payload from backend is updated to include flat usernames, this mapping is simpler.
    // If socket payload does NOT include usernames, you should re-fetch via API.
    handleSocketTransactionUpdate(payload: TransactionStatusUpdatePayload) {
      console.log('[TransactionStore] Handling socket transaction update:', payload);
      if (this.currentTransaction && this.currentTransaction.id === payload.transactionId) {
        // Update existing transaction
        // The payload now directly contains the flat username properties
        this.currentTransaction = {
          ...this.currentTransaction, // Preserve any fields not in socket payload (e.g., logs if managed separately)
          ...payload, // Spread all properties from the socket payload
          id: payload.transactionId, // Ensure 'id' is correctly sourced if currentTransaction.id was used for matching
        };
        // If Transaction type does not have transactionId, and payload does,
        // you might want to remove it from the final object if strictness is required,
        // though spreading usually handles this fine by overwriting or adding.
        // The key is that 'id' must be correct.
        console.log('[TransactionStore] Updated currentTransaction from socket:', this.currentTransaction);
      } else if (payload.transactionId) {
        // New transaction or different one.
        // Transform payload to match the Transaction interface.
        const { transactionId, ...restOfPayload } = payload;
        const newTransactionData: Transaction = {
          id: transactionId, // Map transactionId from payload to id
          ...restOfPayload, // Spread the rest of the payload properties
          // Ensure all other required fields of 'Transaction' are present in 'restOfPayload'
          // or provide default values if necessary.
          // Based on previous changes, 'restOfPayload' should contain all necessary fields.
        };
        this.currentTransaction = newTransactionData;
        console.log('[TransactionStore] Set new currentTransaction from socket:', this.currentTransaction);
        // Optionally, re-determine userRole if needed based on authStore.user.id
        // const authStore = useAuthStore(); // Make sure to import useAuthStore
        // if (authStore.user && this.currentTransaction) {
        //   if (this.currentTransaction.currencyAProviderId === authStore.user.id) {
        //     this.userRole = 'currencyAProvider';
        //   } else if (this.currentTransaction.currencyBProviderId === authStore.user.id) {
        //     this.userRole = 'currencyBProvider';
        //   } else {
        //     this.userRole = null;
        //   }
        // }
      }
    },

    clearTransaction() {
      this.currentTransaction = null;
      this.error = null;
      this.userRole = null;
    },

    setError(message: string) {
      this.error = message;
    },

    clearError() {
      this.error = null;
    },

    async declarePayment(trackingNumber?: string) {
      if (!this.currentTransaction?.id) {
        this.setError('Transaction ID is missing.');
        throw new Error('Transaction ID is missing.');
      }
      this.isLoading = true;
      this.clearError();
      try {
        const updatedTx = await apiDeclarePayment(this.currentTransaction.id, { trackingNumber });
        this.currentTransaction = updatedTx;
      } catch (err: any) {
        this.setError(err.response?.data?.message || err.message || 'Failed to declare payment.');
        throw err;
      } finally {
        this.isLoading = false;
      }
    },

    async confirmReceipt() {
      if (!this.currentTransaction?.id) {
        this.setError('Transaction ID is missing.');
        throw new Error('Transaction ID is missing.');
      }
      this.isLoading = true;
      this.clearError();
      try {
        const updatedTx = await apiConfirmReceipt(this.currentTransaction.id);
        this.currentTransaction = updatedTx;
      } catch (err: any) {
        this.setError(err.response?.data?.message || err.message || 'Failed to confirm receipt.');
        throw err;
      } finally {
        this.isLoading = false;
      }
    },

    async cancelTransaction(reason: string) {
      if (!this.currentTransaction?.id) {
        this.setError('Transaction ID is missing.');
        throw new Error('Transaction ID is missing.');
      }
      this.isLoading = true;
      this.clearError();
      try {
        const updatedTx = await apiCancelTransaction(this.currentTransaction.id, { reason });
        this.currentTransaction = updatedTx;
      } catch (err: any) {
        this.setError(err.response?.data?.message || err.message || 'Failed to cancel transaction.');
        throw err;
      } finally {
        this.isLoading = false;
      }
    },

    async disputeTransaction(reason: string) {
      if (!this.currentTransaction?.id) {
        this.setError('Transaction ID is missing.');
        throw new Error('Transaction ID is missing.');
      }
      this.isLoading = true;
      this.clearError();
      try {
        const updatedTx = await apiDisputeTransaction(this.currentTransaction.id, { reason });
        this.currentTransaction = updatedTx;
      } catch (err: any) {
        this.setError(err.response?.data?.message || err.message || 'Failed to dispute transaction.');
        throw err;
      } finally {
        this.isLoading = false;
      }
    },
  },
  getters: {
    // ... your getters
  }
});