#!/usr/bin/env pwsh
# Test script to verify AI tag separation behavior in the debug report component

Write-Host "Testing AI Tag Separation Behavior..." -ForegroundColor Cyan
Write-Host ""

# Test scenario: AI suggests 5 tags (3 predefined, 2 AI-only)
Write-Host "Test Scenario:" -ForegroundColor Yellow
Write-Host "AI suggests 5 tags:"
Write-Host "  - 'error' (predefined)"
Write-Host "  - 'fix-needed' (predefined)" 
Write-Host "  - 'urgent' (predefined)"
Write-Host "  - 'network-timeout' (AI-only)"
Write-Host "  - 'api-response-slow' (AI-only)"
Write-Host ""

Write-Host "Expected Behavior After 'Apply to Form':" -ForegroundColor Green
Write-Host "✓ Predefined Tags Section:"
Write-Host "  - 'error' should be selected"
Write-Host "  - 'fix-needed' should be selected"  
Write-Host "  - 'urgent' should be selected"
Write-Host ""
Write-Host "✓ AI Suggestions Section:"
Write-Host "  - Should only show 'network-timeout' and 'api-response-slow'"
Write-Host "  - Both should appear as selected"
Write-Host "  - Should NOT show 'error', 'fix-needed', 'urgent'"
Write-Host ""

Write-Host "Starting frontend development server..." -ForegroundColor Blue
Write-Host "1. Navigate to http://localhost:5173"
Write-Host "2. Open Debug Report modal"
Write-Host "3. Use Voice Recorder to generate AI suggestions"
Write-Host "4. Click 'Apply to Form'"
Write-Host "5. Verify the tag separation behavior described above"
Write-Host ""

# Start the development server
Set-Location "C:\Code\MUNygo\frontend"
npm run dev
