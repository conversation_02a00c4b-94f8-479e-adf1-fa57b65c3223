import { Hono } from 'hono';
import { PrismaClient, ChatMessage, User } from '@prisma/client';
import { authMiddleware, JwtPayload } from '../middleware/auth'; // Import JwtPayload
import { TransactionService } from '../services/transactionService'; // Import TransactionService

const prisma = new PrismaClient();

// Function to create and configure chat routes
export const createChatRouter = (transactionService: TransactionService): Hono => {
  const chatRoutes = new Hono();

  // Middleware to ensure user is authenticated for all chat routes
  chatRoutes.use('/*', authMiddleware);
  // GET /api/chat/:chatSessionId/messages - Frontend expects messages endpoint
  chatRoutes.get('/:chatSessionId/messages', async (c) => {
    const { chatSessionId } = c.req.param();
    const jwtPayload = c.get('jwtPayload') as JwtPayload; // Correctly use JwtPayload
    const userId = jwtPayload?.userId;
    
    // Get the includeSystemMessages query parameter (for future filtering if needed)
    const includeSystemMessages = c.req.query('includeSystemMessages') === 'true';

    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    try {
      const chatSession = await prisma.chatSession.findUnique({
        where: { id: chatSessionId },
        select: { userOneId: true, userTwoId: true },
      });

      if (!chatSession) {
        return c.json({ error: 'Chat session not found' }, 404);
      }

      if (userId !== chatSession.userOneId && userId !== chatSession.userTwoId) {
        return c.json({ error: 'You are not a participant in this chat session' }, 403);
      }

      const messages = await prisma.chatMessage.findMany({
        where: { chatSessionId },
        orderBy: { createdAt: 'asc' },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              reputationLevel: true,
            },
          },
        },
      });

      type MessageWithSender = ChatMessage & {
        sender: Partial<Pick<User, 'id' | 'username' | 'reputationLevel'>>;
      };

      const formattedMessages = (messages as MessageWithSender[]).map((msg: MessageWithSender) => ({
        messageId: msg.id,
        chatSessionId: msg.chatSessionId,
        sender: {
          id: msg.sender?.id || 'SYSTEM', // Handle system messages where sender might be null or different
          username: msg.sender?.username || 'System',
          reputationLevel: msg.sender?.reputationLevel ?? 0,
        },
        content: msg.content,
        createdAt: msg.createdAt.toISOString(),
        isSystemMessage: msg.isSystemMessage, // Include isSystemMessage
      }));

      return c.json(formattedMessages);
    } catch (error) {
      console.error(`[ChatRoutes] Error fetching chat history for session ${chatSessionId}:`, error);
      return c.json({ error: 'Failed to fetch chat history' }, 500);
    }
  });

  // GET /api/chat/:chatSessionId/participants
  chatRoutes.get('/:chatSessionId/participants', async (c) => {
    const { chatSessionId } = c.req.param();
    const jwtPayload = c.get('jwtPayload') as JwtPayload;
    const userId = jwtPayload?.userId;

    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    try {
      const chatSession = await prisma.chatSession.findUnique({
        where: { id: chatSessionId },
        include: {
          userOne: { select: { id: true, username: true, reputationLevel: true, email: true } },
          userTwo: { select: { id: true, username: true, reputationLevel: true, email: true } },
        },
      });

      if (!chatSession) {
        return c.json({ error: 'Chat session not found' }, 404);
      }

      if (userId !== chatSession.userOneId && userId !== chatSession.userTwoId) {
        return c.json({ error: 'You are not a participant in this chat session' }, 403);
      }

      const currentUserIsUserOne = userId === chatSession.userOneId;
      const otherUser = currentUserIsUserOne ? chatSession.userTwo : chatSession.userOne;
      const currentUser = currentUserIsUserOne ? chatSession.userOne : chatSession.userTwo;

      return c.json({
        chatSessionId: chatSession.id,
        currentUser: {
          id: currentUser.id,
          username: currentUser.username || currentUser.email.split('@')[0],
          reputationLevel: currentUser.reputationLevel,
        },
        otherUser: {
          id: otherUser.id,
          username: otherUser.username || otherUser.email.split('@')[0],
          reputationLevel: otherUser.reputationLevel,
        },
      });
    } catch (error) {
      console.error(`[ChatRoutes] Error fetching participants for session ${chatSessionId}:`, error);
      return c.json({ error: 'Failed to fetch participant details' }, 500);
    }
  });

  // GET /api/chat/:chatSessionId/transaction
  chatRoutes.get('/:chatSessionId/transaction', async (c) => {
    const { chatSessionId } = c.req.param();
    const jwtPayload = c.get('jwtPayload') as JwtPayload;
    const currentRequestingUserId = jwtPayload?.userId;
  
    if (!currentRequestingUserId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }
  
    try {      const session = await prisma.chatSession.findUnique({
        where: { id: chatSessionId },
        include: {
          transaction: true,
          offer: {
            include: {
              user: { 
                select: { id: true, username: true, reputationLevel: true, email: true },
              },
            },
          },
          userOne: { 
            select: { id: true, username: true, reputationLevel: true, email: true },
          },
          userTwo: { 
            select: { id: true, username: true, reputationLevel: true, email: true },
          },
        },
      });
  
      if (!session) {
        return c.json({ error: 'Chat session not found' }, 404);
      }
  
      if (currentRequestingUserId !== session.userOneId && currentRequestingUserId !== session.userTwoId) {
        return c.json({ error: 'You are not a participant in this chat session' }, 403);
      }
  
      const offer = session.offer;
      if (!offer || !offer.user) {
        return c.json({ error: 'Offer or original offer creator not found for this chat session' }, 404);
      }
  
      const transactionOfferCreator = offer.user;
      let transactionOfferTaker;
      if (session.userOneId === transactionOfferCreator.id) {
        transactionOfferTaker = session.userTwo;
      } else if (session.userTwoId === transactionOfferCreator.id) {
        transactionOfferTaker = session.userOne;
      } else {
        console.error(`[ChatRoutes Transaction] Critical mismatch: Original offer creator ${transactionOfferCreator.id} not found directly as userOne (${session.userOneId}) or userTwo (${session.userTwoId}) in chat session ${session.id}.`);
        return c.json({ error: 'Chat session participant data inconsistent with offer creator' }, 500);
      }
      
      if (!transactionOfferTaker) {
          return c.json({ error: 'Could not determine offer taker for the transaction due to missing participant data' }, 500);
      }      const baseRate = offer.baseRate;
      
      // Validate adjustment values - ensure they are defined, numeric, and non-negative
      const validAdjustmentForLowerRep = (typeof offer.adjustmentForLowerRep === 'number' && 
        !isNaN(offer.adjustmentForLowerRep) && offer.adjustmentForLowerRep >= 0) ? offer.adjustmentForLowerRep : 0;
      const validAdjustmentForHigherRep = (typeof offer.adjustmentForHigherRep === 'number' && 
        !isNaN(offer.adjustmentForHigherRep) && offer.adjustmentForHigherRep >= 0) ? offer.adjustmentForHigherRep : 0;
      
      const offerType = offer.type;
  
      const creatorRepLevel = transactionOfferCreator.reputationLevel ?? 3;
      const takerRepLevel = transactionOfferTaker.reputationLevel ?? 3;
      
      let agreedExchangeRate = baseRate;      if (takerRepLevel !== creatorRepLevel) {
        if (offerType === 'SELL') { 
          if (takerRepLevel > creatorRepLevel) { 
            agreedExchangeRate = baseRate * (1 - (validAdjustmentForHigherRep / 100));
          } else { 
            agreedExchangeRate = baseRate * (1 + (validAdjustmentForLowerRep / 100));
          }
        } else { 
          if (takerRepLevel > creatorRepLevel) { 
            agreedExchangeRate = baseRate * (1 + (validAdjustmentForHigherRep / 100));
          } else { 
            agreedExchangeRate = baseRate * (1 - (validAdjustmentForLowerRep / 100));
          }
        }
      }
      
      const getUsername = (user: { username: string | null, email: string }): string => user.username || user.email.split('@')[0];
  
      // Calculate final amounts, currency, and cadSellerId
      let finalAssetCurrency: string | null = null;
      let finalAssetAmount: number | null = null;
      let finalCadAmount: number | null = null;
      let cadSellerId: string | null = null;

      if (offer.currencyPair && offer.amount != null && agreedExchangeRate != null) {
        const parts = offer.currencyPair.split('-');
        if (parts.length === 2) {
          const currency1 = parts[0].toUpperCase();
          const currency2 = parts[1].toUpperCase();
          const amount1 = offer.amount; // Amount of currency1
          // agreedExchangeRate is currency2 per unit of currency1
          const amount2 = amount1 * agreedExchangeRate;

          if (currency1 === 'CAD') {
            finalCadAmount = amount1;
            finalAssetAmount = amount2;
            finalAssetCurrency = parts[1]; // currency2 is the asset currency string, preserve original casing from pair
          } else if (currency2 === 'CAD') {
            finalCadAmount = amount2;
            finalAssetAmount = amount1;
            finalAssetCurrency = parts[0]; // currency1 is the asset currency string, preserve original casing from pair
          } else {
            // This case should ideally be prevented by offer validation
            // Or a clear convention for non-CAD pairs is needed.
            // For now, log a warning and make a best guess if necessary, or return error.
            console.warn(`[ChatRoutes Transaction] Currency pair ${offer.currencyPair} does not contain CAD. Transaction financial calculations might be ambiguous.`);
            // Defaulting: assuming currency1 is the "asset" if no CAD is found. This is a fallback.
            finalAssetCurrency = parts[0];
            finalAssetAmount = amount1;
            // finalCadAmount remains null or could be set based on a convention if this state is allowed.
          }

          // Determine cadSellerId
          if (offer.type === 'SELL') { // Creator is selling currency1
            if (currency1 === 'CAD') { // Creator sells CAD
              cadSellerId = transactionOfferCreator.id;
            } else { // Creator sells Asset (currency1 is Asset, so currency2 must be CAD)
              cadSellerId = transactionOfferTaker.id;
            }
          } else { // offer.type === 'BUY' - Creator is buying currency1 (paying with currency2)
            if (currency1 === 'CAD') { // Creator buys CAD (pays with Asset - currency2)
              cadSellerId = transactionOfferTaker.id;
            } else { // Creator buys Asset (currency1 is Asset, pays with CAD - currency2)
              cadSellerId = transactionOfferCreator.id;
            }
          }

        } else {
          console.warn(`[ChatRoutes Transaction] Invalid currency pair format: ${offer.currencyPair}`);
        }
      }

      const transactionInfoPayload = {
        offerId: offer.id, 
        chatSessionId: session.id, 
        offerType: offer.type,
        amount: offer.amount, 
        exchangeRate: agreedExchangeRate, // Use the calculated agreedExchangeRate
        currencyPair: offer.currencyPair,
        
        cadSellerId: cadSellerId, // Added cadSellerId
        finalCadAmount: finalCadAmount, 
        finalAssetAmount: finalAssetAmount, 
        finalAssetCurrency: finalAssetCurrency, 

        offerCreator: {
          id: transactionOfferCreator.id,
          username: getUsername(transactionOfferCreator),
          reputationLevel: transactionOfferCreator.reputationLevel,
        },
        otherUser: { 
          id: transactionOfferTaker.id,
          username: getUsername(transactionOfferTaker),
          reputationLevel: transactionOfferTaker.reputationLevel,        },        transactionStatus: session.transaction?.status || 'PENDING_TERMS_AGREEMENT',
        creatorAgreedTerms: session.transaction?.termsAgreementTimestampPayer1,
        interestedPartyAgreedTerms: session.transaction?.termsAgreementTimestampPayer2,
        creatorDeclaredPayment: session.transaction?.paymentDeclaredAtPayer1,
        interestedPartyConfirmedCreatorPayment: session.transaction?.firstPaymentConfirmedByPayer2At,
        interestedPartyDeclaredPayment: session.transaction?.paymentDeclaredAtPayer2,
        creatorConfirmedInterestedPartyPayment: session.transaction?.secondPaymentConfirmedByPayer1At,
        updatedAt: session.updatedAt.toISOString(), // Added updatedAt
      };
  
      return c.json(transactionInfoPayload);
  
    } catch (error: any) {
      console.error(`[ChatRoutes] Error fetching transaction info for session ${chatSessionId}:`, error);
      const errorMessage = process.env.NODE_ENV === 'production' ? 'Failed to fetch transaction info' : error.message;
      return c.json({ error: 'Failed to fetch transaction info', details: errorMessage }, 500);
    }
  });

  // --- New Transaction Status Routes ---
  chatRoutes.post('/:chatSessionId/agree-terms', async (c) => {
    const { chatSessionId } = c.req.param();
    const jwtPayload = c.get('jwtPayload') as JwtPayload;
    const userId = jwtPayload?.userId;

    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    try {
      const updatedSession = await transactionService.agreeToTerms(chatSessionId, userId);
      if (!updatedSession) {
        return c.json({ error: 'Failed to agree to terms or session not found' }, 404);
      }
      return c.json(updatedSession);
    } catch (error) {
      console.error(`Error in /:chatSessionId/agree-terms route for session ${chatSessionId}:`, error);
      return c.json({ error: 'Internal server error' }, 500);
    }
  });

  chatRoutes.post('/:chatSessionId/declare-payment', async (c) => {
    const { chatSessionId } = c.req.param();
    const jwtPayload = c.get('jwtPayload') as JwtPayload;
    const userId = jwtPayload?.userId;

    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    try {
      const updatedSession = await transactionService.declarePayment(chatSessionId, userId);
      if (!updatedSession) {
        return c.json({ error: 'Failed to declare payment or session not found' }, 404);
      }
      return c.json(updatedSession);
    } catch (error) {
      console.error(`Error in /:chatSessionId/declare-payment route for session ${chatSessionId}:`, error);
      return c.json({ error: 'Internal server error' }, 500);
    }
  });

  chatRoutes.post('/:chatSessionId/confirm-payment', async (c) => {
    const { chatSessionId } = c.req.param();
    const jwtPayload = c.get('jwtPayload') as JwtPayload;
    const confirmingUserId = jwtPayload?.userId;

    if (!confirmingUserId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    try {
      const updatedSession = await transactionService.confirmReceipt(chatSessionId, confirmingUserId);
      if (!updatedSession) {
        return c.json({ error: 'Failed to confirm payment or session not found' }, 404);
      }
      return c.json(updatedSession);
    } catch (error) {
      console.error(`Error in /:chatSessionId/confirm-payment route for session ${chatSessionId}:`, error);
      return c.json({ error: 'Internal server error' }, 500);
    }
  });

  // GET /api/chat/:chatSessionId/status - Specific endpoint for transaction status fields
  chatRoutes.get('/:chatSessionId/status', async (c) => {
    const { chatSessionId } = c.req.param();
    const jwtPayload = c.get('jwtPayload') as JwtPayload;
    const userId = jwtPayload?.userId;

    if (!userId) {
      return c.json({ error: 'User not authenticated' }, 401);
    }

    try {      const session = await prisma.chatSession.findUnique({
        where: { id: chatSessionId },
        select: {
          id: true,
          userOneId: true,
          userTwoId: true,
          updatedAt: true,
          offerId: true,
        },
      });

      if (!session) {
        return c.json({ error: 'Chat session not found' }, 404);
      }

      if (session.userOneId !== userId && session.userTwoId !== userId) {
        return c.json({ error: 'Forbidden' }, 403);
      }
      return c.json(session);
    } catch (error) {
      console.error(`Error fetching chat session status ${chatSessionId}:`, error);
      return c.json({ error: 'Internal server error' }, 500);
    }
  });

  return chatRoutes;
};
