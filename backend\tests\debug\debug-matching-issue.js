const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugMatchingIssue() {
  console.log('=== DEBUGGING MATCHING ISSUE ===\n');
  
  try {
    // 1. Check all active offers
    const offers = await prisma.offer.findMany({
      where: { status: 'ACTIVE' },
      include: { user: { select: { username: true, reputationLevel: true } } }
    });
    
    console.log(`Found ${offers.length} active offers:`);
    offers.forEach(offer => {
      console.log(`- Offer ${offer.id}: ${offer.type} ${offer.amount} ${offer.currencyPair} @ ${offer.baseRate} (User: ${offer.user.username})`);
    });
    
    // 2. Check all matches
    const matches = await prisma.offerMatch.findMany({
      include: {
        offerA: { select: { id: true, type: true, amount: true, baseRate: true } },
        offerB: { select: { id: true, type: true, amount: true, baseRate: true } },
        userA: { select: { username: true } },
        userB: { select: { username: true } }
      }
    });
    
    console.log(`\nFound ${matches.length} matches:`);
    matches.forEach(match => {
      console.log(`- Match ${match.matchId}: ${match.userA.username} (${match.offerA.type} ${match.offerA.amount}@${match.offerA.baseRate}) <-> ${match.userB.username} (${match.offerB.type} ${match.offerB.amount}@${match.offerB.baseRate}) [${match.status}]`);
    });
    
    // 3. Look for specific compatible offers
    console.log('\n=== COMPATIBILITY CHECK ===');
    for (let i = 0; i < offers.length; i++) {
      for (let j = i + 1; j < offers.length; j++) {
        const offerA = offers[i];
        const offerB = offers[j];
        
        // Check if they're compatible
        const isCompatible = 
          offerA.type !== offerB.type && // Opposite types
          offerA.currencyPair === offerB.currencyPair && // Same currency pair
          offerA.amount === offerB.amount && // Same amount
          Math.abs(offerA.baseRate - offerB.baseRate) < 0.01 && // Same rate (within tolerance)
          offerA.userId !== offerB.userId; // Different users
          
        if (isCompatible) {
          console.log(`✅ COMPATIBLE: ${offerA.user.username} (${offerA.type} ${offerA.amount}@${offerA.baseRate}) <-> ${offerB.user.username} (${offerB.type} ${offerB.amount}@${offerB.baseRate})`);
          
          // Check if match exists
          const existingMatch = await prisma.offerMatch.findFirst({
            where: {
              OR: [
                { offerAId: offerA.id, offerBId: offerB.id },
                { offerAId: offerB.id, offerBId: offerA.id }
              ]
            }
          });
          
          if (existingMatch) {
            console.log(`  └─ Match exists: ${existingMatch.matchId} [${existingMatch.status}]`);
          } else {
            console.log(`  └─ ❌ NO MATCH EXISTS - This should be created!`);
          }
        }
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugMatchingIssue();
