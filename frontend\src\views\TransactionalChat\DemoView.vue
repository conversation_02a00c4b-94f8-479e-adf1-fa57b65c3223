<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const demoTransactions = [
  {
    id: 'tx-001',
    name: 'Step 1: Payment Info Setup',
    description: 'User needs to provide payment information. Shows pinned banner with payment icon.',
    stepIndex: 0,
    hasPinnedBanner: true,
    bannerType: 'Regular ActionCard'
  },
  {
    id: 'tx-002', 
    name: 'Step 2: Negotiation',
    description: 'Users decide who pays first. Shows pinned banner with handshake icon.',
    stepIndex: 1,
    hasPinnedBanner: true,
    bannerType: 'Regular ActionCard'
  },
  {
    id: 'tx-003',
    name: 'Step 3: Waiting for First Payer',
    description: 'Waiting for <PERSON> to send payment. No pinned banner (waiting state).',
    stepIndex: 2,
    hasPinnedBanner: false,
    bannerType: 'None - Waiting'
  },
  {
    id: 'tx-004',
    name: 'Step 4: Confirm Receipt (KEY STEP)',
    description: 'User must confirm payment received. Shows pinned banner + smart action bar integration.',
    stepIndex: 3,
    hasPinnedBanner: true,
    bannerType: 'Dynamic Action Bar'
  },
  {
    id: 'tx-005',
    name: 'Step 5: Your Turn to Pay',
    description: 'User must send their payment. Shows pinned banner + single action button.',
    stepIndex: 4,
    hasPinnedBanner: true,
    bannerType: 'Dynamic Action Bar'
  },
  {
    id: 'tx-006',
    name: 'Step 6: Waiting for Confirmation',
    description: 'Waiting for other user to confirm receipt. No pinned banner (waiting state).',
    stepIndex: 5,
    hasPinnedBanner: false,
    bannerType: 'None - Waiting'
  },
  {
    id: 'tx-007',
    name: 'Step 7: Transaction Complete',
    description: 'Transaction finished. Shows pinned banner for rating experience.',
    stepIndex: 6,
    hasPinnedBanner: true,
    bannerType: 'Regular ActionCard'
  }
]

const openTransactionDemo = (transactionId: string) => {
  router.push({ name: 'TransactionalChat', params: { transactionId } })
}

// Removed the reset call - it was interfering with direct navigation to transaction pages
</script>

<template>
  <div class="demo-page">
    <div class="demo-container">
      <header class="demo-header">
        <h1 class="demo-title">🎯 Transactional Chat Cockpit Demo</h1>
        <p class="demo-subtitle">
          A mobile-first P2P currency exchange chat interface with step-by-step transaction guidance
        </p>
      </header>

      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">📱</div>
          <h3>Mobile-First Design</h3>
          <p>Optimized for mobile with touch-friendly interfaces and responsive design</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">🔄</div>
          <h3>7-Step Transaction Flow</h3>
          <p>Clear progress tracking through payment info, negotiation, payments, and completion</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">💬</div>
          <h3>Smart Action Bar</h3>
          <p>Dynamic bottom bar that switches between chat input and action buttons contextually</p>
        </div>
        
        <div class="feature-card pinned-banner-feature">
          <div class="feature-icon">📌</div>
          <h3>Pinned Action Banner (NEW!)</h3>
          <p>Sticky banner ensures user actions are never lost during chat scrolling. Always visible with smooth scroll-to-details.</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">🌙</div>
          <h3>Light & Dark Themes</h3>
          <p>Seamless theme switching with CSS variables and proper contrast ratios</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">🌍</div>
          <h3>Internationalization</h3>
          <p>Full i18n support with English and Persian languages, RTL-ready</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">⏱️</div>
          <h3>Timer Integration</h3>
          <p>Time-sensitive actions with visual countdown and status indicators</p>
        </div>
      </div>

      <div class="demo-section">
        <h2 class="section-title">🚀 Try Different Transaction Steps</h2>
        <p class="section-description">
          Click on any step below to experience the different states of the transactional chat interface.
          Pay special attention to <strong>Step 4</strong> which demonstrates the "smart" dynamic action bar and the new <strong>Pinned Action Banner</strong>.
        </p>
        
        <!-- NEW: Pinned Banner Demo Section -->
        <div class="pinned-banner-demo">
          <div class="demo-highlight">
            <h3>🎯 NEW: Pinned Action Banner Demo</h3>
            <p>
              The Pinned Action Banner is a <strong>smart visibility banner</strong> that appears at the top of the chat feed only when the action card is scrolled out of view. 
              It ensures users never lose track of what they need to do, while staying hidden when the action is already visible.
            </p>
            <div class="demo-steps">
              <div class="demo-step">
                <span class="step-number">1</span>
                <span>Choose a step that requires action (Steps 1, 2, 4, 5, or 7)</span>
              </div>
              <div class="demo-step">
                <span class="step-number">2</span>
                <span>Notice NO banner initially (action card is visible)</span>
              </div>
              <div class="demo-step">
                <span class="step-number">3</span>
                <span>Scroll down until action card goes out of view - banner appears!</span>
              </div>
              <div class="demo-step">
                <span class="step-number">4</span>
                <span>Click "View Details" to smoothly scroll back to the action</span>
              </div>
              <div class="demo-step">
                <span class="step-number">5</span>
                <span>Complete the action and watch the banner disappear permanently</span>
              </div>
            </div>
            <div class="recommended-steps">
              <strong>🎪 Best Demo Steps:</strong>
              <span class="step-tag">Step 1</span>
              <span class="step-tag">Step 2</span>
              <span class="step-tag special">Step 4 (Special)</span>
              <span class="step-tag">Step 7</span>
            </div>
          </div>
        </div>
        
        <div class="demo-grid">
          <div 
            v-for="demo in demoTransactions"
            :key="demo.id"
            class="demo-card"
            :class="{ 
              'key-step': demo.stepIndex === 3,
              'has-pinned-banner': demo.hasPinnedBanner,
              'waiting-step': !demo.hasPinnedBanner
            }"
            @click="openTransactionDemo(demo.id)"
          >
            <div class="demo-card-header">
              <div class="step-badge">Step {{ demo.stepIndex + 1 }}</div>
              <div class="badge-container">
                <div v-if="demo.stepIndex === 3" class="key-badge">KEY STEP</div>
                <div v-if="demo.hasPinnedBanner" class="banner-badge">📌 PINNED</div>
                <div v-else class="no-banner-badge">⏳ WAITING</div>
              </div>
            </div>
            <h3 class="demo-card-title">{{ demo.name }}</h3>
            <p class="demo-card-description">{{ demo.description }}</p>
            <div class="banner-type">
              <strong>Banner Type:</strong> {{ demo.bannerType }}
            </div>
            <div class="demo-card-footer">
              <span class="demo-link">Try Demo →</span>
            </div>
          </div>
        </div>
      </div>

      <div class="technical-section">
        <h2 class="section-title">🛠️ Technical Implementation</h2>
        <div class="tech-grid">
          <div class="tech-item">
            <strong>Vue 3 Composition API:</strong> Modern reactive architecture with script setup
          </div>
          <div class="tech-item">
            <strong>Pinia Store:</strong> Centralized state management for transaction data
          </div>
          <div class="tech-item">
            <strong>CSS Variables:</strong> Theme-aware design system with light/dark mode support
          </div>
          <div class="tech-item">
            <strong>Vue I18n:</strong> Complete internationalization with namespaced translations
          </div>
          <div class="tech-item">
            <strong>Component Architecture:</strong> Modular design with single-responsibility components
          </div>
          <div class="tech-item">
            <strong>Accessibility:</strong> WCAG compliant with proper ARIA labels and keyboard navigation
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.demo-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 48px;
  color: white;
}

.demo-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 16px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.demo-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card.pinned-banner-feature {
  background: linear-gradient(135deg, #e6fffa 0%, #ffffff 100%);
  border: 2px solid #38b2ac;
  position: relative;
  overflow: hidden;
}

.feature-card.pinned-banner-feature::before {
  content: 'NEW!';
  position: absolute;
  top: 10px;
  right: -30px;
  background: #e53e3e;
  color: white;
  padding: 4px 40px;
  font-size: 0.75rem;
  font-weight: bold;
  transform: rotate(45deg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.feature-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #2d3748;
}

.feature-card p {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

.demo-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 16px 0;
  text-align: center;
}

.section-description {
  color: #4a5568;
  line-height: 1.6;
  text-align: center;
  margin: 0 0 32px 0;
  font-size: 1.1rem;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.demo-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.demo-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  border-color: #3182ce;
}

.demo-card.key-step {
  border-color: #f6ad55;
  background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
}

.demo-card.key-step:hover {
  border-color: #ed8936;
  box-shadow: 0 12px 24px rgba(237, 137, 54, 0.3);
}

.demo-card.has-pinned-banner {
  border-left: 4px solid #38b2ac;
  background: linear-gradient(135deg, #e6fffa 0%, #ffffff 100%);
}

.demo-card.waiting-step {
  border-left: 4px solid #a0aec0;
  background: linear-gradient(135deg, #f7fafc 0%, #ffffff 100%);
  opacity: 0.8;
}

.demo-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.badge-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.step-badge {
  background: #3182ce;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.key-badge {
  background: #ed8936;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
}

.banner-badge {
  background: #38b2ac;
  color: white;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
}

.no-banner-badge {
  background: #a0aec0;
  color: white;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
}

.demo-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.demo-card-description {
  color: #4a5568;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.banner-type {
  background: #f7fafc;
  padding: 8px;
  border-radius: 6px;
  font-size: 0.85rem;
  color: #4a5568;
  margin-bottom: 12px;
  border-left: 3px solid #e2e8f0;
}

.banner-type strong {
  color: #2d3748;
}

.demo-card-footer {
  text-align: right;
}

.demo-link {
  color: #3182ce;
  font-weight: 600;
  font-size: 0.9rem;
}

.technical-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
}

.tech-item {
  background: #f7fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #3182ce;
  line-height: 1.6;
}

.tech-item strong {
  color: #2d3748;
}

/* Pinned Banner Demo Section */
.pinned-banner-demo {
  margin-bottom: 32px;
}

.demo-highlight {
  background: linear-gradient(135deg, #e6fffa 0%, #f0fff4 100%);
  border: 2px solid #38b2ac;
  border-radius: 16px;
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.demo-highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #38b2ac, #4fd1c7, #38b2ac);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.demo-highlight h3 {
  color: #234e52;
  margin: 0 0 16px 0;
  font-size: 1.4rem;
  font-weight: 700;
}

.demo-highlight p {
  color: #2d3748;
  line-height: 1.6;
  margin: 0 0 20px 0;
  font-size: 1rem;
}

.demo-steps {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin: 20px 0;
}

.demo-step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid #bee3f8;
}

.step-number {
  background: #3182ce;
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.recommended-steps {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid #bee3f8;
}

.recommended-steps strong {
  color: #234e52;
  margin-right: 8px;
}

.step-tag {
  background: #3182ce;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.step-tag.special {
  background: #ed8936;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .demo-page {
    padding: 12px;
  }
  
  .demo-title {
    font-size: 2rem;
  }
  
  .demo-subtitle {
    font-size: 1rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .demo-section,
  .technical-section {
    padding: 20px;
  }
  
  .section-title {
    font-size: 1.6rem;
  }
  
  .demo-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .tech-grid {
    grid-template-columns: 1fr;
  }
  
  .demo-steps {
    gap: 8px;
  }
  
  .demo-step {
    padding: 8px;
  }
  
  .step-number {
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
  }
  
  .recommended-steps {
    padding: 12px;
  }
  
  .badge-container {
    align-items: flex-end;
  }
}

@media (max-width: 480px) {
  .demo-title {
    font-size: 1.6rem;
  }
  
  .demo-subtitle {
    font-size: 0.9rem;
  }
  
  .feature-card,
  .demo-card {
    padding: 16px;
  }
  
  .demo-section,
  .technical-section {
    padding: 16px;
  }
  
  .demo-highlight {
    padding: 16px;
  }
  
  .demo-highlight h3 {
    font-size: 1.2rem;
  }
  
  .recommended-steps {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
