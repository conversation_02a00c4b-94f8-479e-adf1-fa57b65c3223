// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Basic user model
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Email Verification Fields
  emailVerified     Boolean? @default(false)
  verificationToken String?  @unique // Store the verification token

  // Phone Verification Fields
  phoneNumber   String?   @unique
  phoneVerified Boolean?  @default(false)
  otpSecret     String? // Secret used to generate time-based OTPs
  otpTimestamp  DateTime? // Timestamp when the OTP was generated/sent

  // Reputation Fields
  username        String? // Optional username, defaults to email-based if null
  reputationScore Int     @default(0) // Raw numerical score
  reputationLevel Int     @default(1) // Level 1-5 based on score ranges

  // Relations
  offers    Offer[] // Relation to offers created by the user
  interests Interest[] @relation("InterestedUsers")

  // ChatSession relations
  chatSessionsAsCreator            ChatSession[]  @relation("ChatSessionCreator")
  chatSessionsAsInterested         ChatSession[]  @relation("ChatSessionInterested")
  notifications                    Notification[] // Relation to notifications for the user
  sentChatMessages                 ChatMessage[]  @relation("SentMessages") // This is the other side
  transactionsAsCurrencyAProvider  Transaction[]  @relation("CurrencyAProvider")
  transactionsAsCurrencyBProvider  Transaction[]  @relation("CurrencyBProvider")
  transactionsWithAgreedFirstPayer Transaction[]  @relation("AgreedFirstPayer")
  transactionsCancelledBy          Transaction[]  @relation("CancelledByUser") // Back-relation for cancelled transactions
  transactionsDisputedBy           Transaction[]  @relation("DisputedByUser") // Back-relation for disputed transactions

  // New relations
  partyANegotiations   PayerNegotiation[]     @relation("PartyANegotiations")
  partyBNegotiations   PayerNegotiation[]     @relation("PartyBNegotiations")
  paymentReceivingInfo PaymentReceivingInfo[] @relation("UserPaymentReceivingInfo") // Corrected: Added relation name

  // Debug report relations
  debugReports         DebugReport[]              @relation("UserDebugReports")
  assignedDebugReports DebugReport[]              @relation("AssignedReports")
  debugReportComments  DebugReportComment[]       @relation("DebugReportComments")
  debugStatusChanges   DebugReportStatusHistory[] @relation("DebugStatusChanger")

  // Automatic Offer Matching relations
  matchesAsUserA       OfferMatch[] @relation("UserMatchA")
  matchesAsUserB       OfferMatch[] @relation("UserMatchB")
  matchesDeclined      OfferMatch[] @relation("MatchDeclinedBy")
  matchesAccepted      OfferMatch[] @relation("MatchAcceptedBy")
}

// Enums for Offer Type and Status
enum OfferType {
  BUY
  SELL
}

enum OfferStatus {
  ACTIVE
  INACTIVE
  DEACTIVATED // Assuming this was a typo and should be distinct from INACTIVE or a specific state
  COMPLETED
  CANCELLED
}

// Enum for Transaction Status
enum TransactionStatus {
  PENDING_AGREEMENT // Initial status: Awaiting payment info from both parties
  AWAITING_FIRST_PAYER_DESIGNATION // Parties need to designate who pays first.
  AWAITING_FIRST_PAYER_PAYMENT
  AWAITING_SECOND_PAYER_CONFIRMATION
  AWAITING_SECOND_PAYER_PAYMENT
  AWAITING_FIRST_PAYER_CONFIRMATION
  COMPLETED
  CANCELLED
  DISPUTED
}

// Enum for Receiving Info Status
enum ReceivingInfoStatus {
  PENDING_INPUT
  PROVIDED
  CONFIRMED_FROM_PROFILE
  // Add other statuses if needed, e.g., REJECTED
}

// Enum for Negotiation Status
enum NegotiationStatus {
  PENDING_RECEIVING_INFO          // Initial state, awaiting one or both parties' payment info.
  AWAITING_PARTY_A_RECEIVING_INFO // Party B provided, Party A pending.
  AWAITING_PARTY_B_RECEIVING_INFO // Party A provided, Party B pending.
  READY_TO_NEGOTIATE              // Both parties provided info. System recommendation is generated at this point.
                                  // This status is transient and should immediately move to PENDING_RESPONSE.
  PENDING_RESPONSE                // A proposal (system or user) is active, awaiting response.
  FINALIZED                       // Agreement reached on the first payer.
  EXPIRED                         // Negotiation timed out.
  CANCELLED                       // Negotiation was cancelled by a user or system.
}

model Offer {
  id                     String      @id @default(cuid())
  userId                 String
  user                   User        @relation(fields: [userId], references: [id])
  type                   OfferType
  currencyPair           String      @default("CAD-IRR")
  amount                 Float
  baseRate               Float
  adjustmentForLowerRep  Float
  adjustmentForHigherRep Float
  status                 OfferStatus @default(ACTIVE)
  createdAt              DateTime    @default(now())
  updatedAt              DateTime    @updatedAt
  lastMatchedAt          DateTime?   // Last time this offer was checked for matches
  interests              Interest[]

  // ChatSession relation
  chatSessions ChatSession[]
  transaction  Transaction? // Back-relation for the transaction linked to this offer

  // Automatic Offer Matching relations
  matchesAsOfferA OfferMatch[] @relation("OfferMatchA")
  matchesAsOfferB OfferMatch[] @relation("OfferMatchB")
}

// Enum for Interest Status
enum InterestStatus {
  PENDING
  ACCEPTED
  DECLINED
}

model Interest {
  id                String         @id @default(cuid())
  offerId           String
  offer             Offer          @relation(fields: [offerId], references: [id], onDelete: Cascade)
  interestedUserId  String
  interestedUser    User           @relation("InterestedUsers", fields: [interestedUserId], references: [id], onDelete: Cascade)
  status            InterestStatus @default(PENDING)
  declineReasonCode String?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  chatSession       ChatSession?   @relation("ChatToInterest") // Back relation for one-to-one

  @@unique([offerId, interestedUserId]) // Added composite unique constraint
  @@index([offerId])
  @@index([interestedUserId])
}

model ChatSession {
  id        String   @id @default(cuid())
  offerId   String
  offer     Offer    @relation(fields: [offerId], references: [id], onDelete: Cascade)
  userOneId String // Typically the offer creator
  userOne   User     @relation("ChatSessionCreator", fields: [userOneId], references: [id], onDelete: Cascade)
  userTwoId String // Typically the user who showed interest
  userTwo   User     @relation("ChatSessionInterested", fields: [userTwoId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  interestId  String?       @unique
  interest    Interest?     @relation("ChatToInterest", fields: [interestId], references: [id], onDelete: SetNull)
  messages    ChatMessage[] // Relation to chat messages
  transaction Transaction? // Relation to the transaction associated with this chat session

  // Automatic Offer Matching relation
  offerMatch  OfferMatch? // Back-relation for matches that create this chat session

  @@index([userOneId])
  @@index([userTwoId])
  @@index([offerId])
}

model ChatMessage {
  id              String      @id @default(cuid())
  chatSessionId   String
  chatSession     ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)
  senderId        String?     // Optional: null for system messages
  sender          User?       @relation("SentMessages", fields: [senderId], references: [id], onDelete: SetNull)
  content         String
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  isSystemMessage Boolean     @default(false) // This field is crucial
  transactionId   String?     // Link to transaction for context if needed

  @@index([chatSessionId])
  @@index([senderId])
  @@index([transactionId])
}

// Enum for Notification Types
enum NotificationType {
  NEW_INTEREST_ON_YOUR_OFFER
  YOUR_INTEREST_ACCEPTED
  YOUR_INTEREST_DECLINED
  CHAT_MESSAGE_RECEIVED // Example for future use
  OFFER_STATUS_UPDATED_BY_OWNER // e.g. offer creator cancelled their own offer
  OFFER_STATUS_CHANGED // Added for general offer status changes by owner
  TRANSACTION_STARTED
  TRANSACTION_ACTION_REQUIRED
  TRANSACTION_UPDATE
  TRANSACTION_PAYMENT_DECLARED // Added
  TRANSACTION_PAYMENT_CONFIRMED // Added
  TRANSACTION_COMPLETED
  TRANSACTION_CANCELLED
  TRANSACTION_DISPUTED
  TRANSACTION_AUTO_CANCELLED_TIMER // Added
  // Match-related notifications
  MATCH_FOUND
  MATCH_ACCEPTED_BY_OTHER
  MATCH_DECLINED_BY_OTHER
  MATCH_EXPIRED
  MATCH_CONVERTED_TO_CHAT
  // Add other types as needed
}

// Model for storing Notifications
model Notification {
  id        String           @id @default(uuid())
  userId    String // Recipient of the notification
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  type      NotificationType
  message   String // Display message for the notification
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Optional fields to link to related entities
  relatedEntityType String? // e.g., "OFFER", "INTEREST", "CHAT_SESSION"
  relatedEntityId   String? // ID of the entity the notification refers to

  actorId       String? // ID of the user who triggered the event (if applicable)
  // actor              User?            @relation("NotificationActor", fields: [actorId], references: [id], onDelete:SetNull) // Optional: if you need to link to actor User model
  actorUsername String? // Username of the actor for quick display

  data String? // For storing additional context, e.g., offer title, decline reason details

  @@index([userId])
  @@index([userId, isRead, createdAt]) // To quickly fetch and sort unread notifications for a user
}

model Transaction {
  id                  String            @id @default(cuid())
  offerId             String?           @unique // Link to the original offer if applicable, made unique for 1-to-1
  chatSessionId       String            @unique // Each transaction must belong to a chat session
  currencyA           String
  amountA             Float
  currencyAProviderId String // User ID of the provider for currency A
  currencyB           String
  amountB             Float
  currencyBProviderId String // User ID of the provider for currency B
  status              TransactionStatus @default(PENDING_AGREEMENT) // Default to payment info phase
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt

  // Timestamps for terms agreement (consider if these are still needed in the same way)
  termsAgreementTimestampPayer1 DateTime? // Timestamp when provider of currency A agreed
  termsAgreementTimestampPayer2 DateTime? // Timestamp when provider of currency B agreed

  // First payer designation
  agreedFirstPayerId             String? // User ID of the party agreed to pay first
  firstPayerDesignationTimestamp DateTime? // Timestamp when the first payer was agreed upon

  // Payment tracking for the first payer (who is `agreedFirstPayerId`)
  paymentExpectedByPayer1     DateTime? // Deadline for the first payment
  paymentDeclaredAtPayer1     DateTime? // Timestamp when the first payer declared payment
  paymentTrackingNumberPayer1 String? // Optional tracking number for the first payment
  paymentNotesPayer1          String? // Optional notes/message for the first payment

  // Confirmation of the first payment (by the other party)
  firstPaymentConfirmedByPayer2At DateTime? // Timestamp when the other party confirmed receipt of the first payment

  // Payment tracking for the second payer (who is NOT `agreedFirstPayerId`)
  paymentExpectedByPayer2     DateTime? // Deadline for the second payment
  paymentDeclaredAtPayer2     DateTime? // Timestamp when the second payer declared payment
  paymentTrackingNumberPayer2 String? // Optional tracking number for the second payment
  paymentNotesPayer2          String? // Optional notes/message for the second payment

  // Confirmation of the second payment (by the `agreedFirstPayerId`)
  secondPaymentConfirmedByPayer1At DateTime? // Timestamp when the first payer confirmed receipt of the second payment

  // Cancellation and Dispute
  cancellationReason     String?
  cancelledByUserId      String? // User ID of the party who initiated cancellation
  disputeReason          String?
  disputedByUserId       String? // User ID of the party who initiated dispute
  disputeResolvedAt      DateTime?
  disputeResolutionNotes String?

  // Relations
  offer             Offer?            @relation(fields: [offerId], references: [id])
  chatSession       ChatSession       @relation(fields: [chatSessionId], references: [id])
  currencyAProvider User              @relation("CurrencyAProvider", fields: [currencyAProviderId], references: [id])
  currencyBProvider User              @relation("CurrencyBProvider", fields: [currencyBProviderId], references: [id])
  agreedFirstPayer  User?             @relation("AgreedFirstPayer", fields: [agreedFirstPayerId], references: [id], onDelete: SetNull)
  cancelledByUser   User?             @relation("CancelledByUser", fields: [cancelledByUserId], references: [id], onDelete: SetNull)
  disputedByUser    User?             @relation("DisputedByUser", fields: [disputedByUserId], references: [id], onDelete: SetNull)
  payerNegotiation  PayerNegotiation?

  // Automatic Offer Matching relation
  offerMatch        OfferMatch? // Back-relation for matches that create this transaction

  @@index([currencyAProviderId])
  @@index([currencyBProviderId])
  @@index([agreedFirstPayerId])
  @@index([status])
  @@index([cancelledByUserId])
  @@index([disputedByUserId])
}

model PayerNegotiation {
  negotiationId    String   @id @default(cuid())
  transactionId    String   @unique // Each transaction has one negotiation
  transaction      Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  partyA_Id        String
  partyA           User     @relation("PartyANegotiations", fields: [partyA_Id], references: [id], onDelete: Cascade)
  partyB_Id        String
  partyB           User     @relation("PartyBNegotiations", fields: [partyB_Id], references: [id], onDelete: Cascade)

  partyA_receivingInfoStatus ReceivingInfoStatus @default(PENDING_INPUT)
  partyB_receivingInfoStatus ReceivingInfoStatus @default(PENDING_INPUT)

  // Store the ID of the PaymentReceivingInfo used by each party for this negotiation
  partyA_PaymentReceivingInfoId String?
  partyA_PaymentReceivingInfo   PaymentReceivingInfo? @relation("PartyAReceivingInfo", fields: [partyA_PaymentReceivingInfoId], references: [id], onDelete: SetNull)
  partyB_PaymentReceivingInfoId String?
  partyB_PaymentReceivingInfo   PaymentReceivingInfo? @relation("PartyBReceivingInfo", fields: [partyB_PaymentReceivingInfoId], references: [id], onDelete: SetNull)

  systemRecommendedPayerId    String?
  systemRecommendationRule    String? // e.g., "REPUTATION", "CURRENCY"
  systemRecommendationReason  String? // Textual reason
  systemRecommendationDetails String?   // Store specific details like reputation scores

  currentProposal_PayerId String? // Who is proposed to pay first
  currentProposal_ById    String? // Who made the current proposal ('system' or a userId)
  currentProposal_Message String? // Optional message with the proposal

  partyA_agreedToCurrentProposal Boolean @default(false)
  partyB_agreedToCurrentProposal Boolean @default(false)

  negotiationStatus NegotiationStatus @default(PENDING_RECEIVING_INFO) // This default will now be valid
  finalizedPayerId  String?
  paymentTimerDueDate DateTime? // Timer for the finalized payer to make their payment
  
  // Final offer loop prevention - marks when a proposal is the final counter-offer
  isFinalOffer Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([partyA_Id])
  @@index([partyB_Id])
  @@index([transactionId])
}

// Define the PaymentReceivingInfo model
// Enhanced payment method types
enum PaymentMethodType {
  BANK_TRANSFER    // Traditional bank transfer
  DIGITAL_WALLET   // Digital wallets like PayPal, Wise, etc.
  CRYPTO_WALLET    // Cryptocurrency wallets
  MOBILE_MONEY     // Mobile money services
  CASH_PICKUP      // Cash pickup locations
}

// Enhanced payment receiving info model with multi-currency support
model PaymentReceivingInfo {
  id                String            @id @default(cuid())
  userId            String
  user              User              @relation("UserPaymentReceivingInfo", fields: [userId], references: [id])
  
  // Currency and method type
  currency          String            @default("IRR") // Currency code (IRR, CAD, USD, etc.)
  paymentMethodType PaymentMethodType @default(BANK_TRANSFER)
  
  // Basic information (required for all types)
  bankName          String            // Bank name or service provider name
  accountNumber     String            // Account number or wallet address
  accountHolderName String            // Account holder or wallet owner name
  
  // Optional banking details (for international transfers)
  swiftCode         String?           // SWIFT/BIC code for international transfers
  iban              String?           // International Bank Account Number
  routingNumber     String?           // US routing number
  sortCode          String?           // UK sort code
  bsb               String?           // Australian BSB code
  
  // Metadata
  isDefaultForUser  Boolean           @default(false)
  isActive          Boolean           @default(true)
  notes             String?           // Additional notes or instructions
  
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  negotiationsAsPartyA PayerNegotiation[] @relation("PartyAReceivingInfo")
  negotiationsAsPartyB PayerNegotiation[] @relation("PartyBReceivingInfo")

  // Indexes
  @@index([userId])
  @@index([userId, currency])
  @@index([userId, currency, isActive])
  // Note: Conditional unique constraint for default methods is created manually:
  // CREATE UNIQUE INDEX "PaymentReceivingInfo_userId_currency_default_true_key" 
  // ON "PaymentReceivingInfo"("userId", "currency") WHERE "isDefaultForUser" = true;
}

// Automatic Offer Matching System Models

// Enums for match system
enum MatchStatus {
  PENDING        // Initial state, awaiting user responses
  BOTH_ACCEPTED  // Both users accepted the match → Creates ONE transaction
  PARTIAL_ACCEPT // One user accepted, waiting for other
  DECLINED       // At least one user declined
  EXPIRED        // Match timed out
  CONVERTED      // Successfully converted to chat/transaction
  CANCELLED      // System cancelled (e.g., offer deactivated)
}

enum MatchResponse {
  ACCEPTED
  DECLINED
  IGNORED
}

model OfferMatch {
  id       String @id @default(cuid())
  matchId  String @unique // Human-readable ID: MATCH_20241201_001
  
  // Offer references
  offerAId String @map("offer_a_id")
  offerBId String @map("offer_b_id")
  offerA   Offer  @relation("OfferMatchA", fields: [offerAId], references: [id], onDelete: Cascade)
  offerB   Offer  @relation("OfferMatchB", fields: [offerBId], references: [id], onDelete: Cascade)
  
  // User references (denormalized for performance)
  userAId String @map("user_a_id")
  userBId String @map("user_b_id")
  userA   User   @relation("UserMatchA", fields: [userAId], references: [id], onDelete: Cascade)
  userB   User   @relation("UserMatchB", fields: [userBId], references: [id], onDelete: Cascade)
  
  // Match metadata
  compatibilityScore Decimal @default(0.000) @db.Decimal(4, 3) // 0.000-1.000
  matchCriteria      Json?   @map("match_criteria") // Store matching criteria used
  
  // Currency and amount information (denormalized for performance)
  currencyA  String  @map("currency_a") @db.VarChar(3)
  currencyB  String  @map("currency_b") @db.VarChar(3)
  amountA    Decimal @map("amount_a") @db.Decimal(15, 2)
  amountB    Decimal @map("amount_b") @db.Decimal(15, 2)
  rateAToB   Decimal @map("rate_a_to_b") @db.Decimal(10, 6)
  rateBToA   Decimal @map("rate_b_to_a") @db.Decimal(10, 6)
  
  // Status and lifecycle
  status    MatchStatus @default(PENDING)
  createdAt DateTime    @default(now()) @map("created_at")
  updatedAt DateTime    @updatedAt @map("updated_at")
  expiresAt DateTime    @map("expires_at")
  
  // User actions
  userAResponse     MatchResponse? @map("user_a_response")
  userARespondedAt  DateTime?      @map("user_a_responded_at")
  userBResponse     MatchResponse? @map("user_b_response")
  userBRespondedAt  DateTime?      @map("user_b_responded_at")
  
  // Result tracking
  chatSessionId      String? @unique @map("chat_session_id")
  transactionId      String? @unique @map("transaction_id")
  declinedByUserId   String? @map("declined_by_user_id")
  declineReason      String? @map("decline_reason") @db.VarChar(100)
  
  // Race condition handling
  acceptedByUserId  String?  @map("accepted_by_user_id")
  processingLock    Boolean  @default(false) @map("processing_lock")
  
  // Relations
  chatSession       ChatSession? @relation(fields: [chatSessionId], references: [id], onDelete: SetNull)
  transaction       Transaction? @relation(fields: [transactionId], references: [id], onDelete: SetNull)
  declinedByUser    User?        @relation("MatchDeclinedBy", fields: [declinedByUserId], references: [id], onDelete: SetNull)
  acceptedByUser    User?        @relation("MatchAcceptedBy", fields: [acceptedByUserId], references: [id], onDelete: SetNull)
  
  // Indexes for performance
  @@index([userAId])
  @@index([userBId])
  @@index([offerAId])
  @@index([offerBId])
  @@index([status])
  @@index([createdAt])
  @@index([expiresAt])
  @@index([currencyA, currencyB])
  
  // Constraints
  @@unique([offerAId, offerBId])
  
  @@map("offer_matches")
}

model MatchConfiguration {
  id          String   @id @default(cuid())
  configKey   String   @unique @map("config_key") @db.VarChar(50)
  configValue Json     @map("config_value")
  description String?  @db.Text
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  @@index([configKey])
  @@index([isActive])
  
  @@map("match_configurations")
}

// Debug Report System Models
model DebugReport {
  id                String   @id @default(uuid())
  reportId          String   @unique @map("report_id")
  userId            String?  @map("user_id")
  user              User?    @relation("UserDebugReports", fields: [userId], references: [id], onDelete: SetNull)

  // Report details
  type              DebugReportType
  severity          DebugReportSeverity
  status            DebugReportStatus   @default(NOT_REVIEWED)
  priority          Int                 @default(3) // 1-5 scale
  title             String              @db.VarChar(200)
  description       String              @db.Text
  stepsToReproduce  String?             @map("steps_to_reproduce") @db.Text
  expectedBehavior  String?             @map("expected_behavior") @db.Text
  actualBehavior    String?             @map("actual_behavior") @db.Text
  additionalNotes   String?             @map("additional_notes") @db.Text

  // Assignment and workflow
  assignedToId      String?             @map("assigned_to")
  assignedTo        User?               @relation("AssignedReports", fields: [assignedToId], references: [id], onDelete: SetNull)
  assignedAt        DateTime?           @map("assigned_at")

  // Context data
  sessionId         String?             @map("session_id")
  currentUrl        String?             @map("current_url") @db.Text
  userAgent         String?             @map("user_agent") @db.Text
  viewportWidth     Int?                @map("viewport_width")
  viewportHeight    Int?                @map("viewport_height")

  // JSON data
  diagnosticData    Json?               @map("diagnostic_data")
  logs              Json?
  userActions       Json?               @map("user_actions")

  // Timestamps
  clientTimestamp   DateTime            @map("client_timestamp")
  serverReceivedAt  DateTime            @default(now()) @map("server_received_at")
  createdAt         DateTime            @default(now()) @map("created_at")
  updatedAt         DateTime            @updatedAt @map("updated_at")

  // Relations
  tags              DebugReportTag[]
  statusHistory     DebugReportStatusHistory[]
  comments          DebugReportComment[]

  @@index([status])
  @@index([type])
  @@index([severity])
  @@index([assignedToId])
  @@index([userId])
  @@index([createdAt])
  @@map("debug_reports")
}

model DebugReportTag {
  id        String      @id @default(uuid())
  reportId  String      @map("report_id")
  report    DebugReport @relation(fields: [reportId], references: [id], onDelete: Cascade)
  
  // Enhanced tag relationship - can reference a managed Tag or store custom tag
  tagId     String?     @map("tag_id") // Reference to managed Tag
  tag       Tag?        @relation("TagToReportTags", fields: [tagId], references: [id], onDelete: SetNull)
  
  // For backward compatibility and custom tags
  tagName   String?     @db.VarChar(50) @map("tag_name") // Direct tag name for custom tags
  origin    TagOrigin   @default(USER_DEFINED) @map("tag_origin")
  createdAt DateTime    @default(now()) @map("created_at")

  // Either tagId OR tagName must be present
  @@index([reportId])
  @@index([tagId])
  @@index([tagName])
  @@index([origin])
  @@map("debug_report_tags")
}

model DebugReportStatusHistory {
  id        String               @id @default(uuid())
  reportId  String               @map("report_id")
  report    DebugReport          @relation(fields: [reportId], references: [id], onDelete: Cascade)
  oldStatus DebugReportStatus?   @map("old_status")
  newStatus DebugReportStatus    @map("new_status")
  changedBy String?              @map("changed_by")
  changer   User?                @relation("DebugStatusChanger", fields: [changedBy], references: [id], onDelete: SetNull)
  comment   String?              @db.Text
  createdAt DateTime             @default(now()) @map("created_at")

  @@index([reportId])
  @@map("debug_report_status_history")
}

model DebugReportComment {
  id         String      @id @default(uuid())
  reportId   String      @map("report_id")
  report     DebugReport @relation(fields: [reportId], references: [id], onDelete: Cascade)
  userId     String?     @map("user_id")
  user       User?       @relation("DebugReportComments", fields: [userId], references: [id], onDelete: SetNull)
  comment    String      @db.Text
  isInternal Boolean     @default(true) @map("is_internal")
  createdAt  DateTime    @default(now()) @map("created_at")

  @@index([reportId])
  @@map("debug_report_comments")
}

// Debug Report Enums
enum DebugReportType {
  BUG
  FEATURE_REQUEST
  PERFORMANCE
  UI_UX
  IMPROVEMENT
  QUESTION
  OTHER

  @@map("debug_report_type")
}

enum DebugReportSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL

  @@map("debug_report_severity")
}

enum DebugReportStatus {
  NOT_REVIEWED
  IN_PROGRESS
  COMPLETED
  ARCHIVED
  DUPLICATE
  WONT_FIX

  @@map("debug_report_status")
}

enum TagOrigin {
  PREDEFINED      // Tags from predefined lists
  AI_SUGGESTED    // Tags generated by AI
  USER_DEFINED    // Tags manually added by user

  @@map("tag_origin")
}

// Enhanced Tag Management System Models
model TagCategory {
  id          String    @id @default(uuid())
  name        String    @unique @db.VarChar(50)
  description String?   @db.Text
  color       String?   @db.VarChar(7) // Hex color
  order       Int       @default(0)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  tags        Tag[]

  @@index([name])
  @@index([order])
  @@map("tag_categories")
}

model Tag {
  id           String      @id @default(uuid())
  name         String      @unique @db.VarChar(50)
  displayName  Json        @map("display_name") // Multilingual display names { "en": "Bug Fix", "fa": "رفع اشکال" }
  description  Json?       // Multilingual descriptions
  categoryId   String?     @map("category_id")
  category     TagCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  
  // Tag properties
  color        String?     @db.VarChar(7)
  icon         String?     @db.VarChar(50)
  weight       Int         @default(0) // For sorting/importance
  isActive     Boolean     @default(true) @map("is_active")
  isSystem     Boolean     @default(false) @map("is_system") // System vs user-defined
  
  // Usage analytics
  usageCount   Int         @default(0) @map("usage_count")
  lastUsedAt   DateTime?   @map("last_used_at")
  
  // AI integration
  aiRelevance  Float?      @default(0.0) @map("ai_relevance") // 0.0-1.0 AI relevance score
  
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")
  
  // Relations
  reportTags   DebugReportTag[] @relation("TagToReportTags")
  reportTypes  TagReportTypeAssociation[]

  @@index([name])
  @@index([categoryId])
  @@index([isActive])
  @@index([usageCount])
  @@index([aiRelevance])
  @@map("tags")
}

model TagReportTypeAssociation {
  id         String @id @default(uuid())
  tagId      String @map("tag_id")
  tag        Tag    @relation(fields: [tagId], references: [id], onDelete: Cascade)
  reportType String @db.VarChar(50) @map("report_type")
  weight     Int    @default(0) // Relevance weight for this report type
  createdAt  DateTime @default(now()) @map("created_at")
  
  @@unique([tagId, reportType])
  @@index([reportType])
  @@index([weight])
  @@map("tag_report_type_associations")
}
