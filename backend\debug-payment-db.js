const { PrismaClient } = require('@prisma/client');

async function debugPaymentMethods() {
  const prisma = new PrismaClient();
  
  try {
    // Get all payment methods for the user
    const userId = 'cmbqrmwcv0001vl48wlfffva4'; // From the logs
    
    console.log('=== Payment Methods for User ===');
    const methods = await prisma.paymentReceivingInfo.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log(`Found ${methods.length} payment methods:`);
    methods.forEach((method, index) => {
      console.log(`${index + 1}. ID: ${method.id}`);
      console.log(`   Currency: ${method.currency}`);
      console.log(`   Type: ${method.paymentMethodType}`);
      console.log(`   Active: ${method.isActive}`);
      console.log(`   Default: ${method.isDefaultForUser}`);
      console.log(`   Created: ${method.createdAt}`);
      console.log('   ---');
    });
    
    console.log('\n=== CAD Currency Methods ===');
    const cadMethods = methods.filter(m => m.currency === 'CAD');
    console.log(`Found ${cadMethods.length} CAD methods:`);
    cadMethods.forEach((method, index) => {
      console.log(`${index + 1}. ID: ${method.id}, Active: ${method.isActive}, Default: ${method.isDefaultForUser}`);
    });
    
    console.log('\n=== Constraint Analysis ===');
    // Check for duplicate default flags
    const duplicateDefaults = await prisma.paymentReceivingInfo.groupBy({
      by: ['userId', 'currency', 'isDefaultForUser'],
      where: {
        userId,
        isDefaultForUser: true
      },
      _count: {
        id: true
      },
      having: {
        id: {
          _count: {
            gt: 1
          }
        }
      }
    });
    
    console.log('Duplicate default flags found:', duplicateDefaults);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugPaymentMethods();
