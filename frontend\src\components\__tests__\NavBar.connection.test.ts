import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import { nextTick } from 'vue';
import NavBar from '../NavBar.vue';

// Mock the stores
const mockAuthStore = {
  isAuthenticated: true,
  user: {
    id: 'user-1',
    username: 'testuser',
    firstName: 'Test',
    lastName: 'User',
    reputation: 5.0
  },
  logout: vi.fn()
};

const mockConnectionStore = {
  connectionStatus: 'connected',
  isConnecting: false,
  reconnectAttempts: 0
};

const mockNotificationStore = {
  interestNotifications: [],
  unreadCount: 0
};

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => mockAuthStore
}));

vi.mock('@/stores/connection', () => ({
  useConnectionStore: () => mockConnectionStore
}));

vi.mock('@/stores/notification', () => ({
  useNotificationStore: () => mockNotificationStore
}));

// Mock the centralized socket manager
const mockSocketManager = {
  forceReconnect: vi.fn(),
  isConnected: vi.fn(() => false)
};

vi.mock('@/services/centralizedSocketManager', () => ({
  default: mockSocketManager
}));

// Mock Vue Router
const mockRouter = {
  push: vi.fn(),
  currentRoute: {
    value: { name: 'Home' }
  }
};

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter
}));

// Mock Naive UI components
vi.mock('naive-ui', () => ({
  NButton: { template: '<button><slot /></button>' },
  NDropdown: { template: '<div><slot /></div>' },
  NIcon: { template: '<span><slot /></span>' },
  NText: { template: '<span><slot /></span>' },
  NSpin: { template: '<div><slot /></div>' },
  useMessage: () => ({
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  })
}));

// Mock the message composable
const mockMessage = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn()
};

vi.mock('@/composables/useMessage', () => ({
  useMessage: () => mockMessage
}));

describe('NavBar - Connection Indicator and Reconnection', () => {
  let wrapper: VueWrapper<any>;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    // Reset mock states
    mockConnectionStore.connectionStatus = 'connected';
    mockConnectionStore.isConnecting = false;
    mockConnectionStore.reconnectAttempts = 0;
    mockSocketManager.isConnected.mockReturnValue(true);
    mockSocketManager.forceReconnect.mockReturnValue(true);
  });

  afterEach(() => {
    vi.useRealTimers();
    if (wrapper) {
      wrapper.unmount();
    }
  });

  const mountComponent = () => {
    return mount(NavBar, {
      global: {
        stubs: {
          'n-button': true,
          'n-dropdown': true,
          'n-icon': true,
          'n-text': true,
          'n-spin': true,
          'router-link': true
        }
      }
    });
  };

  describe('Connection Status Display', () => {
    it('should show connected status when connection is established', async () => {
      mockConnectionStore.connectionStatus = 'connected';
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.exists()).toBe(true);
      expect(connectionIndicator.text()).toContain('Connected');
    });

    it('should show disconnected status when connection is lost', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.exists()).toBe(true);
      expect(connectionIndicator.text()).toContain('Disconnected');
    });

    it('should show connecting status during connection attempts', async () => {
      mockConnectionStore.connectionStatus = 'connecting';
      mockConnectionStore.isConnecting = true;
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.exists()).toBe(true);
      expect(connectionIndicator.text()).toContain('Connecting');
    });

    it('should show reconnect attempts when greater than 0', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      mockConnectionStore.reconnectAttempts = 3;
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.text()).toContain('Disconnected (3 attempts)');
    });
  });

  describe('Connection Indicator Tooltip', () => {
    it('should show appropriate tooltip for connected status', async () => {
      mockConnectionStore.connectionStatus = 'connected';
      wrapper = mountComponent();
      await nextTick();

      const tooltipText = wrapper.vm.getConnectionTooltipText();
      expect(tooltipText).toBe('Connected to server');
    });

    it('should show reconnection instructions for disconnected status', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      wrapper = mountComponent();
      await nextTick();

      const tooltipText = wrapper.vm.getConnectionTooltipText();
      expect(tooltipText).toBe('Disconnected from server. Click to reconnect.');
    });

    it('should show connecting message during connection attempts', async () => {
      mockConnectionStore.connectionStatus = 'connecting';
      wrapper = mountComponent();
      await nextTick();

      const tooltipText = wrapper.vm.getConnectionTooltipText();
      expect(tooltipText).toBe('Connecting to server...');
    });
  });

  describe('Manual Reconnection', () => {
    it('should attempt reconnection when clicking disconnected indicator', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.exists()).toBe(true);

      await connectionIndicator.trigger('click');

      expect(mockSocketManager.forceReconnect).toHaveBeenCalledTimes(1);
    });

    it('should not attempt reconnection when clicking connected indicator', async () => {
      mockConnectionStore.connectionStatus = 'connected';
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      await connectionIndicator.trigger('click');

      expect(mockSocketManager.forceReconnect).not.toHaveBeenCalled();
    });

    it('should show success message after successful reconnection', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      mockSocketManager.forceReconnect.mockReturnValue(true);
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      await connectionIndicator.trigger('click');

      expect(mockMessage.success).toHaveBeenCalledWith('Reconnection attempted');
    });

    it('should show error message when reconnection fails', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      mockSocketManager.forceReconnect.mockReturnValue(false);
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      await connectionIndicator.trigger('click');

      expect(mockMessage.error).toHaveBeenCalledWith('Unable to reconnect. Please check your authentication.');
    });

    it('should handle reconnection loading state', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      wrapper = mountComponent();
      await nextTick();

      // Start reconnection
      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      const clickPromise = connectionIndicator.trigger('click');

      // Check loading state during reconnection
      expect(wrapper.vm.isReconnecting).toBe(true);

      // Wait for click to complete
      await clickPromise;
      await nextTick();

      // Loading should be cleared
      expect(wrapper.vm.isReconnecting).toBe(false);
    });
  });

  describe('Connection Status Reactivity', () => {
    it('should update display when connection status changes', async () => {
      mockConnectionStore.connectionStatus = 'connected';
      wrapper = mountComponent();
      await nextTick();

      let connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.text()).toContain('Connected');

      // Change connection status
      mockConnectionStore.connectionStatus = 'disconnected';
      await nextTick();

      connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.text()).toContain('Disconnected');
    });

    it('should update display when reconnect attempts change', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      mockConnectionStore.reconnectAttempts = 0;
      wrapper = mountComponent();
      await nextTick();

      let connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.text()).not.toContain('attempts');

      // Increase reconnect attempts
      mockConnectionStore.reconnectAttempts = 2;
      await nextTick();

      connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.text()).toContain('(2 attempts)');
    });
  });

  describe('Connection Indicator Styling', () => {
    it('should apply connected styling when connected', async () => {
      mockConnectionStore.connectionStatus = 'connected';
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.classes()).toContain('connected');
    });

    it('should apply disconnected styling when disconnected', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.classes()).toContain('disconnected');
    });

    it('should apply connecting styling when connecting', async () => {
      mockConnectionStore.connectionStatus = 'connecting';
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.classes()).toContain('connecting');
    });

    it('should show clickable cursor for disconnected status', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.classes()).toContain('clickable');
    });

    it('should not show clickable cursor for connected status', async () => {
      mockConnectionStore.connectionStatus = 'connected';
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.classes()).not.toContain('clickable');
    });
  });

  describe('Edge Cases', () => {
    it('should handle socket manager not being available', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      // Mock socket manager to be undefined
      vi.mocked(mockSocketManager.forceReconnect).mockImplementation(() => {
        throw new Error('Socket manager not available');
      });

      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      await connectionIndicator.trigger('click');

      expect(mockMessage.error).toHaveBeenCalledWith('Unable to reconnect. Please check your authentication.');
    });

    it('should handle multiple rapid clicks gracefully', async () => {
      mockConnectionStore.connectionStatus = 'disconnected';
      wrapper = mountComponent();
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      
      // Trigger multiple rapid clicks
      const clickPromise1 = connectionIndicator.trigger('click');
      const clickPromise2 = connectionIndicator.trigger('click');
      const clickPromise3 = connectionIndicator.trigger('click');

      await Promise.all([clickPromise1, clickPromise2, clickPromise3]);

      // Should only call force reconnect once due to loading state
      expect(mockSocketManager.forceReconnect).toHaveBeenCalledTimes(1);
    });

    it('should maintain component state when user logs out and back in', async () => {
      wrapper = mountComponent();
      await nextTick();

      // Simulate logout
      mockAuthStore.isAuthenticated = false;
      mockAuthStore.user = null;
      await nextTick();

      // Simulate login
      mockAuthStore.isAuthenticated = true;
      mockAuthStore.user = {
        id: 'user-1',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        reputation: 5.0
      };
      await nextTick();

      const connectionIndicator = wrapper.find('[data-testid="connection-indicator"]');
      expect(connectionIndicator.exists()).toBe(true);
    });
  });
});
