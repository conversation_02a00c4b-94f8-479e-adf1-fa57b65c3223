#!/bin/bash
# Production Deployment Script for Debug Report System Update
# Run this script on your CentOS 9 production host machine

set -e  # Exit on any error

# Configuration
BACKUP_FIRST=true
ZERO_DOWNTIME=true
BACKUP_PATH="./backups"
PROJECT_NAME="munygo"
DB_NAME="munygo_db"
DB_USER="munygo_user"
DB_PASS="U6^#A7sBp&tE%qgRt5Ra"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting MUNygo Production Deployment${NC}"
echo -e "${YELLOW}Debug Report System Update${NC}"

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}❌ docker-compose.yml not found. Are you in the MUNygo project directory?${NC}"
    exit 1
fi

# Create backup directory if it doesn't exist
if [ "$BACKUP_FIRST" = true ] && [ ! -d "$BACKUP_PATH" ]; then
    mkdir -p "$BACKUP_PATH"
fi

# Step 1: Create backup
if [ "$BACKUP_FIRST" = true ]; then
    echo -e "${YELLOW}📦 Creating database backup...${NC}"
    BACKUP_FILE="$BACKUP_PATH/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # Load environment variables
    if [ -f .env ]; then
        export $(grep -v '^#' .env | xargs)
    fi
    
    if docker exec munygo-postgres pg_dump -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE" 2>/dev/null; then
        echo -e "${GREEN}✅ Backup created: $BACKUP_FILE${NC}"
    else
        echo -e "${YELLOW}⚠️ Backup failed, but continuing. Manual backup recommended.${NC}"
    fi
fi

# Step 2: Pull latest code
echo -e "${YELLOW}📥 Pulling latest code...${NC}"
if git pull origin main; then
    echo -e "${GREEN}✅ Code updated successfully${NC}"
else
    echo -e "${RED}❌ Git pull failed. Please resolve conflicts manually.${NC}"
    exit 1
fi

# Step 3: Check environment variables
echo -e "${YELLOW}🔧 Checking environment variables...${NC}"
ENV_FILE=".env"

if [ -f "$ENV_FILE" ]; then
    # Check for required debug report variables
    if ! grep -q "CLIENT_LOG_DIRECTORY" "$ENV_FILE"; then
        echo "CLIENT_LOG_DIRECTORY=/app/logs" >> "$ENV_FILE"
        echo -e "${GREEN}✅ Added CLIENT_LOG_DIRECTORY to .env${NC}"
    fi
    
    if ! grep -q "VITE_ENABLE_DEBUG_REPORT" "$ENV_FILE"; then
        echo "VITE_ENABLE_DEBUG_REPORT=true" >> "$ENV_FILE"
        echo -e "${GREEN}✅ Added VITE_ENABLE_DEBUG_REPORT to .env${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ .env file not found. Please create it based on .env.example${NC}"
fi

# Step 4: Deploy based on strategy
if [ "$ZERO_DOWNTIME" = true ]; then
    echo -e "${YELLOW}🔄 Starting zero-downtime deployment...${NC}"
    
    # Stop only backend
    echo -e "${YELLOW}Stopping backend service...${NC}"
    docker compose stop backend
    
    # Run migration
    echo -e "${YELLOW}Running database migration...${NC}"
    docker compose run --rm backend npx prisma migrate deploy
    
    # Rebuild and start everything
    echo -e "${YELLOW}Rebuilding and starting services...${NC}"
    docker compose up -d --build
else
    echo -e "${YELLOW}🔄 Starting full restart deployment...${NC}"
    
    # Stop all services
    docker compose down
    
    # Rebuild and start with migration
    docker compose up -d --build
fi

# Step 5: Verify deployment
echo -e "${YELLOW}🔍 Verifying deployment...${NC}"

# Wait for services to be ready
echo -e "${YELLOW}Waiting for services to start...${NC}"
sleep 30

# Check container status
echo -e "${YELLOW}Checking container status...${NC}"
docker compose ps

# Check if all containers are running
EXPECTED_CONTAINERS=("${PROJECT_NAME}-postgres" "${PROJECT_NAME}-backend" "${PROJECT_NAME}-frontend")
ALL_RUNNING=true

for container in "${EXPECTED_CONTAINERS[@]}"; do
    if docker compose ps | grep -q "$container.*Up"; then
        echo -e "${GREEN}✅ Container $container is running${NC}"
    else
        echo -e "${YELLOW}⚠️ Container $container is not running${NC}"
        ALL_RUNNING=false
    fi
done

if [ "$ALL_RUNNING" = true ]; then
    echo -e "${GREEN}✅ All containers are running${NC}"
fi

# Test health endpoints
echo -e "${YELLOW}Testing health endpoints...${NC}"

# Get the backend port from docker-compose.yml or use default
BACKEND_PORT=$(grep -A 10 "backend:" docker-compose.yml | grep -E "ports:|^\s*-\s*\".*:3000\"" | head -1 | sed 's/.*"\([0-9]*\):3000".*/\1/' || echo "3004")

if curl -s --connect-timeout 10 "http://localhost:$BACKEND_PORT/health" > /dev/null; then
    echo -e "${GREEN}✅ Backend health check passed${NC}"
else
    echo -e "${YELLOW}⚠️ Backend health check failed${NC}"
fi

# Check database tables
echo -e "${YELLOW}Verifying database schema...${NC}"
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

if docker exec ${PROJECT_NAME}-postgres psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "\dt DebugReport*" 2>/dev/null | grep -q "DebugReport"; then
    echo -e "${GREEN}✅ Debug report tables found in database${NC}"
else
    echo -e "${YELLOW}⚠️ Debug report tables not found. Migration may have failed.${NC}"
fi

# Check logs for errors
echo -e "${YELLOW}Checking for recent errors...${NC}"
if docker compose logs backend --tail=20 | grep -i -E "(error|fatal|exception)" > /dev/null; then
    echo -e "${YELLOW}⚠️ Found errors in backend logs. Check with: docker compose logs backend${NC}"
else
    echo -e "${GREEN}✅ No errors found in recent backend logs${NC}"
fi

# Final status
echo ""
echo -e "${GREEN}🎉 Deployment completed!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Test the debug report feature in your frontend"
echo "2. Check admin dashboard for debug reports"
echo "3. Monitor logs: docker compose logs -f"
echo "4. Check application functionality"
echo ""

if [ "$BACKUP_FIRST" = true ]; then
    echo -e "${CYAN}💾 Backup location: $BACKUP_FILE${NC}"
fi

echo -e "${CYAN}📊 View logs with: docker compose logs backend --tail=50${NC}"
echo -e "${CYAN}🔍 Monitor containers: docker compose ps${NC}"

# Show next steps for verification
echo ""
echo -e "${BLUE}To run comprehensive verification:${NC}"
echo "./verify-deployment.sh"
