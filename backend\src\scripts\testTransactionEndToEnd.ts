/**
 * Comprehensive test to verify that transaction creation with reputation adjustments
 * stores the correct amounts that will be displayed in system messages.
 */

import { PrismaClient, OfferType } from '@prisma/client';
import { createInitialTransaction } from '../services/transactionService';

const prisma = new PrismaClient();

async function testTransactionWithReputationAdjustment() {
  console.log('Testing end-to-end transaction creation with reputation adjustments...');
  
  try {
    // Create test users with different reputation levels
    const user1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'h1', // Lower reputation user (like h1 in the issue)
        password: 'dummy-password',
        reputationLevel: 2, // Lower reputation
        emailVerified: true,
        phoneVerified: true,
      }
    });

    const user2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'h2', // Higher reputation user (like h2 in the issue)
        password: 'dummy-password',
        reputationLevel: 4, // Higher reputation
        emailVerified: true,
        phoneVerified: true,
      }
    });    // Create an offer where h2 (higher rep) creates a SELL offer
    const offer = await prisma.offer.create({
      data: {
        userId: user2.id, // h2 creates the offer
        type: OfferType.SELL,
        currencyPair: 'CAD-IRR',
        amount: 100, // 100 CAD
        baseRate: 880.6347, // Base rate: 880.6347 IRR per CAD
        adjustmentForLowerRep: 3.0, // 3% penalty for lower reputation
        adjustmentForHigherRep: 1.0, // 1% bonus for higher reputation
        status: 'ACTIVE',
      }
    });

    // Create a chat session
    const chatSession = await prisma.chatSession.create({
      data: {
        userOneId: user1.id,
        userTwoId: user2.id,
        offerId: offer.id,
      }
    });

    console.log('=== OFFER DETAILS ===');
    console.log(`Creator: h2 (reputation ${user2.reputationLevel})`);
    console.log(`Type: ${offer.type} (h2 sells CAD for IRR)`);
    console.log(`Amount: ${offer.amount} CAD`);
    console.log(`Base rate: ${offer.baseRate} IRR per CAD`);
    console.log(`Base total IRR: ${offer.amount * offer.baseRate}`);

    // h1 (lower reputation) shows interest in the offer
    // This should trigger transaction creation with reputation adjustment

    // Calculate what the adjusted rate should be
    const creatorRepLevel = user2.reputationLevel ?? 3;
    const takerRepLevel = user1.reputationLevel ?? 3;
    
    let agreedExchangeRate = offer.baseRate;
    
    if (takerRepLevel !== creatorRepLevel) {
      if (offer.type === OfferType.SELL) { 
        if (takerRepLevel > creatorRepLevel) { 
          agreedExchangeRate = offer.baseRate * (1 - (offer.adjustmentForHigherRep / 100));
        } else { 
          agreedExchangeRate = offer.baseRate * (1 + (offer.adjustmentForLowerRep / 100));
        }
      }
    }

    const finalAmountA = offer.amount; // CAD amount (unchanged)
    const finalAmountB = finalAmountA * agreedExchangeRate; // IRR amount (adjusted)

    console.log('=== REPUTATION ADJUSTMENT ===');
    console.log(`h1 reputation: ${takerRepLevel}`);
    console.log(`h2 reputation: ${creatorRepLevel}`);
    console.log(`h1 has LOWER reputation, so penalty applies`);
    console.log(`Adjusted rate: ${agreedExchangeRate} IRR per CAD`);
    console.log(`Final CAD amount (h2 provides): ${finalAmountA}`);
    console.log(`Final IRR amount (h1 provides): ${finalAmountB}`);

    // Create transaction using the updated logic (simulating acceptInterest)
    await prisma.$transaction(async (tx) => {
      const transaction = await createInitialTransaction(
        tx,
        chatSession.id,
        offer.id,
        'CAD',        // currencyA
        finalAmountA, // amountA (adjusted or base)
        user2.id,     // currencyAProviderId (h2 provides CAD)
        'IRR',        // currencyB
        finalAmountB, // amountB (adjusted)
        user1.id      // currencyBProviderId (h1 provides IRR)
      );

      console.log('=== CREATED TRANSACTION ===');
      console.log(`Transaction ID: ${transaction.id}`);
      console.log(`CurrencyA: ${transaction.currencyA}, AmountA: ${transaction.amountA}`);
      console.log(`CurrencyB: ${transaction.currencyB}, AmountB: ${transaction.amountB}`);
      console.log(`Provider A (${transaction.currencyA}): ${transaction.currencyAProviderId}`);
      console.log(`Provider B (${transaction.currencyB}): ${transaction.currencyBProviderId}`);

      // Verify that the transaction stores the reputation-adjusted amounts
      const expectedAmountB = offer.amount * agreedExchangeRate;
      const isCorrect = Math.abs(transaction.amountB - expectedAmountB) < 0.001;
      
      console.log('=== VERIFICATION ===');
      console.log(`Expected amountB: ${expectedAmountB}`);
      console.log(`Stored amountB: ${transaction.amountB}`);
      console.log(`Amounts match: ${isCorrect ? 'YES ✓' : 'NO ✗'}`);

      if (isCorrect) {
        console.log('SUCCESS: Transaction stores reputation-adjusted amounts!');
        console.log('This means system messages will show the correct amounts.');
      } else {
        console.log('ERROR: Transaction still stores base amounts!');
      }

      // Simulate what the system message would show
      // Assuming h1 is designated as first payer (lower reputation pays first)
      const firstPayerAmount = transaction.amountB; // h1 pays IRR first
      const firstPayerCurrency = transaction.currencyB;
      
      console.log('=== SYSTEM MESSAGE SIMULATION ===');
      console.log(`"h1 has declared payment of ${firstPayerAmount} ${firstPayerCurrency}. Please confirm receipt."`);
      console.log(`Original issue amount: 880.6347000000001 IRR (base rate)`);
      console.log(`Fixed amount: ${firstPayerAmount} IRR (reputation-adjusted)`);
      console.log(`User actually pays: ${finalAmountB} IRR`);
      console.log(`Message shows correct amount: ${Math.abs(firstPayerAmount - finalAmountB) < 0.001 ? 'YES ✓' : 'NO ✗'}`);
    });

    // Clean up
    await prisma.chatSession.delete({ where: { id: chatSession.id } });
    await prisma.offer.delete({ where: { id: offer.id } });
    await prisma.user.delete({ where: { id: user1.id } });
    await prisma.user.delete({ where: { id: user2.id } });

    console.log('\n✓ Test completed successfully!');

  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testTransactionWithReputationAdjustment();
