// filepath: c:\Code\MUNygo\frontend\src\views\MyOffersView.vue
<template>
  <div class="my-offers-view-enhanced">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">{{ t('offers.myOffers') }}</h1>
        <p class="hero-subtitle">{{ t('offers.manageYourOffers') }}</p>
        
        <!-- Quick Stats -->
        <div class="quick-stats">
          <div class="stat-card">
            <div class="stat-value">{{ myOffersDisplayData.length }}</div>
            <div class="stat-label">{{ t('offers.totalOffers') }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ activeOffersCount }}</div>
            <div class="stat-label">{{ t('offers.activeOffers') }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ totalInterestsCount }}</div>
            <div class="stat-label">{{ t('offers.totalInterests') }}</div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="hero-actions">
          <n-button 
            type="primary" 
            size="large"
            @click="router.push({ name: 'CreateOffer' })"
            class="primary-cta"
          >
            <template #icon>
              <n-icon><PlusOutlined /></n-icon>
            </template>
            {{ t('offers.createNewOffer') }}
          </n-button>
        </div>
      </div>
    </section>

    <!-- My Offers Section -->
    <section class="my-offers-section">
      <div class="section-header">
        <h2 class="section-title">{{ t('offers.yourOffers') }}</h2>
        <div class="section-filters" v-if="myOffersDisplayData.length > 0">
          <n-button-group size="small">
            <n-button 
              :type="offerFilter === 'ALL' ? 'primary' : 'default'"
              @click="offerFilter = 'ALL'"
            >
              {{ t('offers.all') }}
            </n-button>
            <n-button 
              :type="offerFilter === 'ACTIVE' ? 'primary' : 'default'"
              @click="offerFilter = 'ACTIVE'"
            >
              {{ t('offers.active') }}
            </n-button>
            <n-button 
              :type="offerFilter === 'INACTIVE' ? 'primary' : 'default'"
              @click="offerFilter = 'INACTIVE'"
            >
              {{ t('offers.inactive') }}
            </n-button>
            <n-button 
              :type="offerFilter === 'WITH_INTERESTS' ? 'primary' : 'default'"
              @click="offerFilter = 'WITH_INTERESTS'"
            >
              <template #icon>
                <n-icon><MessageOutlined /></n-icon>
              </template>
              {{ t('offers.withInterests') }}
            </n-button>
          </n-button-group>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoadingOffers" class="offers-skeleton">
        <div v-for="i in 3" :key="i" class="skeleton-card">
          <div class="skeleton-header"></div>
          <div class="skeleton-content">
            <div class="skeleton-line"></div>
            <div class="skeleton-line short"></div>
            <div class="skeleton-line medium"></div>
          </div>
          <div class="skeleton-footer"></div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="filteredOffers.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <h3 class="empty-title">{{ t('offers.noOffersFound') }}</h3>
        <p class="empty-description">
          {{ offerFilter === 'ALL' ? t('offers.noOffersCreated') : t('offers.noOffersInFilter') }}
        </p>
        <n-button 
          v-if="offerFilter === 'ALL'"
          type="primary" 
          @click="router.push({ name: 'CreateOffer' })"
          class="empty-cta"
        >
          {{ t('offers.createFirstOffer') }}
        </n-button>
      </div>

      <!-- Enhanced Offers Grid -->
      <div v-else class="offers-grid">
        <div 
          v-for="(offer, index) in filteredOffers" 
          :key="offer.id" 
          class="offer-card-container"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <FlippableOfferCard
            :offer="offer"
            :processing-interests="processingInterests"
            @status-toggle="handleStatusToggle"
            @edit="(offerId) => router.push({ name: 'EditOffer', params: { offerId } })"
            @accept-interest="handleAcceptInterest"
            @decline-interest="handleDeclineInterest"
            @go-to-chat="goToChat"
          />
        </div>
      </div>
    </section>

    <!-- Empty State for No Offers -->
    <section v-if="!isLoadingOffers && myOffersDisplayData.length === 0" class="empty-offers-section">
      <div class="empty-state">
        <div class="empty-icon">📋</div>
        <h3 class="empty-title">{{ t('offers.noOffersCreated') }}</h3>
        <p class="empty-description">{{ t('offers.startByCreatingFirstOffer') }}</p>
        <n-button 
          type="primary" 
          size="large"
          @click="router.push({ name: 'CreateOffer' })"
          class="empty-cta"
        >
          {{ t('offers.createFirstOffer') }}
        </n-button>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref as vueRef } from 'vue';
import type { Ref, ComputedRef } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useMyOffersStore } from '@/stores/myOffersStore';
import { useTranslation } from '@/composables/useTranslation';
import type { MyOffer } from '@/types/offer';
import { 
  NButton, 
  NButtonGroup,
  useMessage
} from 'naive-ui';
import { 
  PlusOutlined,
  MessageOutlined
} from '@vicons/antd';
import { updateOfferStatus } from '@/services/offerStatusService';
import { navigateToChat } from '@/utils/chatNavigation';
import FlippableOfferCard from '@/components/FlippableOfferCard.vue';

const authStore = useAuthStore();
console.log('🔥 [MyOffersView] About to call useMyOffersStore()');
const myOffersStore = useMyOffersStore();
console.log('🔥 [MyOffersView] myOffersStore created:', myOffersStore);
const message = useMessage();
const router = useRouter();
const { t } = useTranslation();

// Filter state
const offerFilter = vueRef<'ALL' | 'ACTIVE' | 'INACTIVE' | 'WITH_INTERESTS'>('ALL');

// Shared utility function for status toggle
async function toggleOfferStatus(offer: OfferDisplayRow, checked: boolean) {
  offer._statusLoading.value = true;
  try {
    const newStatus = checked ? 'ACTIVE' : 'INACTIVE';
    await updateOfferStatus(offer.id, newStatus);
    
    // Reflect change in the store for global reactivity
    const offerInStore = myOffersStore.myOffers.find(o => o.id === offer.id);
    if (offerInStore) {
      offerInStore.status = newStatus;
    }    message.success(t('offers.statusUpdated', { 
      status: t(`offers.statusTypes.${newStatus.toLowerCase()}`)
    }));
    return true; // Success
  } catch (err: any) {
    message.error('Failed to update offer status.');
    // Revert UI if backend call failed and status was not updated
    const offerInStore = myOffersStore.myOffers.find(o => o.id === offer.id);
    if (offerInStore) {
      offer.status = offerInStore.status; // Revert to store's state
    }
    return false; // Failure
  } finally {
    offer._statusLoading.value = false;
  }
}

// Reactive properties from the store
const isLoadingOffers = computed(() => myOffersStore.loading);
const storeError = computed(() => myOffersStore.error);

// Define the type for rows in the data table, including the added _statusLoading
// Using Ref<boolean> for _statusLoading
type OfferDisplayRow = MyOffer & { _statusLoading: Ref<boolean> };

// Create a display-specific version of offers that includes the local _statusLoading ref
const myOffersDisplayData: ComputedRef<OfferDisplayRow[]> = computed(() =>
  myOffersStore.myOffers.map(offer => ({
    ...offer, 
    _statusLoading: vueRef(false) 
  }))
);

// New computed properties for enhanced UI
const activeOffersCount = computed(() => 
  myOffersDisplayData.value.filter(offer => offer.status === 'ACTIVE').length
);

const totalInterestsCount = computed(() => 
  myOffersDisplayData.value.reduce((total, offer) => 
    total + (offer.interests?.length || 0), 0
  )
);

const filteredOffers = computed(() => {
  if (offerFilter.value === 'ALL') return myOffersDisplayData.value;
  if (offerFilter.value === 'WITH_INTERESTS') {
    return myOffersDisplayData.value.filter(offer => 
      offer.interests && offer.interests.length > 0
    );
  }
  return myOffersDisplayData.value.filter(offer => offer.status === offerFilter.value);
});

// function formatDate(dateString: string): string {
//   const date = new Date(dateString);
//   return date.toLocaleString();
// }

// Track which interests are being processed to prevent double-clicks
const processingInterests = vueRef<Set<string>>(new Set());

// --- Start: Interest Processing Functions ---

async function handleAcceptInterest(interestId: string) {
  // Prevent double-clicks
  if (processingInterests.value.has(interestId)) {
    console.log(`[MyOffersView] Interest ${interestId} is already being processed, ignoring click`);
    return;
  }

  try {
    processingInterests.value.add(interestId);
    await myOffersStore.acceptInterest(interestId);
    // Naive UI success notification is handled by myOffersStore via socket event
  } catch (error: any) {
    console.error('[MyOffersView] Error accepting interest:', error);
    
    // Handle specific error cases
    if (error.response?.status === 409) {
      message.warning('This interest has already been processed. The page will refresh to show the current state.');
      // Refresh the offers to get the current state
      setTimeout(() => {
        myOffersStore.fetchMyOffers();
      }, 1000);
    } else {
      message.error(error.response?.data?.message || error.message || 'Failed to accept interest.');
    }
  } finally {
    processingInterests.value.delete(interestId);
  }
}

// --- Start: Decline Interest Modal Logic ---
// This logic is now handled by the DeclineInterestModal.vue component
// Modified handleDeclineInterest to use store
async function handleDeclineInterest(interestId: string) {
  const offer = myOffersDisplayData.value.find(o => o.interests.some(i => i.id === interestId));
  if (offer) {
    const interest = offer.interests.find(i => i.id === interestId);
    if (interest) {
      myOffersStore.openDeclineInterestModal(interest); // This opens the modal component
    } else {
      message.error('Interest not found for declining.');
    }
  } else {
    message.error('Offer not found for the interest to be declined.');
  }
}
// --- End: Decline Interest Modal Logic ---

// Mobile card status toggle handler
async function handleStatusToggle(offer: OfferDisplayRow, checked: boolean) {
  await toggleOfferStatus(offer, checked);
}

const goToChat = async (chatSessionId?: string | null) => {
  if (chatSessionId) {
    await navigateToChat(router, { chatSessionId });
  } else {
    message.error('Chat session ID not found.');
  }
};

// Lifecycle
onMounted(async () => {
  if (!authStore.isAuthenticated || !authStore.user?.id) {
    message.error('You must be logged in to view your offers.');
    router.push({ name: 'Login' });
    return;
  }
  try {
    await myOffersStore.fetchMyOffers();
    if (storeError.value) {
      message.error(storeError.value);
    }
  } catch (error) {
    message.error('An unexpected error occurred while loading your offers.');
    console.error('Error fetching my offers on mount:', error);
  }
});

</script>

<style scoped>
/* Mobile-first Enhanced My Offers View */
.my-offers-view-enhanced {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* Light theme */
[data-theme="light"] .my-offers-view-enhanced {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
}

/* Dark theme */
[data-theme="dark"] .my-offers-view-enhanced {
  background: linear-gradient(135deg, #1a1b2e 0%, #16213e 50%, #0f1419 100%);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  color: white;
  padding: 2rem 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1" fill-rule="nonzero"><circle cx="30" cy="30" r="4"/></g></g></svg>');
  opacity: 0.1;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: 1rem;
  margin: 0 0 1.5rem 0;
  opacity: 0.9;
  line-height: 1.6;
}

/* Quick Stats */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin: 1.5rem 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.8;
}

.hero-actions {
  margin-top: 1.5rem;
}

.primary-cta {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(0);
  transition: all 0.3s ease;
}

.primary-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Section Styling */
.my-offers-section,
.interests-section,
.empty-interests-section {
  padding: 0 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #333;
}

[data-theme="dark"] .section-title {
  color: rgba(255, 255, 255, 0.9);
}

.section-subtitle {
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.25rem;
}

[data-theme="dark"] .section-subtitle {
  color: rgba(255, 255, 255, 0.7);
}

/* Section Filters Enhancement */
.section-filters {
  display: flex;
  align-items: center;
}

.section-filters .n-button-group {
  flex-wrap: wrap;
  gap: 0.25rem;
}

/* Enhanced Button Group Styling */
.section-filters .n-button-group .n-button {
  transition: all 0.2s ease;
  border-radius: 8px;
  font-weight: 500;
}

.section-filters .n-button-group .n-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .section-filters .n-button-group .n-button:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Loading Skeleton */
.offers-skeleton {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin: 0;
}

.skeleton-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] .skeleton-card {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.skeleton-header {
  height: 24px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-line.short { width: 60%; }
.skeleton-line.medium { width: 80%; }

.skeleton-footer {
  height: 32px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 16px;
  width: 120px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] .empty-state {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
}

[data-theme="dark"] .empty-title {
  color: rgba(255, 255, 255, 0.9);
}

.empty-description {
  color: #666;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

[data-theme="dark"] .empty-description {
  color: rgba(255, 255, 255, 0.7);
}

.empty-cta {
  margin-top: 1rem;
}

/* Offers Grid - Mobile First */
.offers-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin: 0;
}

.offer-card-container {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

/* Simplified interest management CSS since card flipping is now handled by FlippableOfferCard component */

/* Interest Management Cards */
.interests-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin: 0;
}

.interest-card-container {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

.enhanced-interest-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
}

[data-theme="dark"] .enhanced-interest-card {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Interest Card Header */
.interest-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .interest-card-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.offer-summary h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
}

[data-theme="dark"] .offer-summary h4 {
  color: rgba(255, 255, 255, 0.9);
}

.offer-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #666;
}

[data-theme="dark"] .offer-details {
  color: rgba(255, 255, 255, 0.7);
}

/* Interest List */
.interests-list {
  padding: 0 1.5rem 1.5rem;
}

.interest-item {
  padding: 1rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .interest-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.interest-item:last-child {
  border-bottom: none;
}

.user-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.username {
  font-weight: 600;
  color: #333;
}

[data-theme="dark"] .username {
  color: rgba(255, 255, 255, 0.9);
}

/* Status Content */
.status-content {
  margin-top: 0.75rem;
}

.decline-reason {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 8px;
  border-left: 3px solid #ff6b6b;
}

.reason-label {
  font-weight: 500;
  color: #666;
  font-size: 0.875rem;
}

[data-theme="dark"] .reason-label {
  color: rgba(255, 255, 255, 0.7);
}

.reason-text {
  font-weight: 600;
  color: #ff6b6b;
}

.chat-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(34, 197, 94, 0.1);
  border-radius: 8px;
  border-left: 3px solid #22c55e;
  gap: 1rem;
  flex-wrap: wrap;
}

.chat-details {
  flex: 1;
}

.chat-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #666;
}

[data-theme="dark"] .chat-id {
  color: rgba(255, 255, 255, 0.7);
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.75rem;
}

/* Responsive Design */
@media (min-width: 640px) {
  .hero-title {
    font-size: 3rem;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
  
  .quick-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-value {
    font-size: 2rem;
  }
  
  .stat-label {
    font-size: 0.875rem;
  }
  
  .my-offers-section,
  .interests-section,
  .empty-interests-section {
    padding: 0 1.5rem 2rem;
  }
  
  .section-header {
    flex-wrap: nowrap;
  }
}

/* Tablet Responsive Design (768px+) */
@media (min-width: 768px) {
  /* Hero Section */
  .hero-section {
    padding: 3rem 2rem;
  }
  
  .hero-title {
    font-size: 3rem;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
  
  .quick-stats {
    gap: 1.5rem;
    max-width: 700px;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-value {
    font-size: 2rem;
  }
  
  .stat-label {
    font-size: 0.875rem;
  }
  
  /* Main Content */
  .my-offers-section,
  .interests-section,
  .empty-interests-section {
    padding: 0 2rem 2rem;
  }
  
  .section-header {
    flex-wrap: nowrap;
  }
  
  /* Section Filters */
  .section-filters {
    display: flex;
    align-items: center;
  }
  
  .section-filters .n-button-group {
    flex-wrap: nowrap;
  }
  
  /* Grid Layout - 2 columns for tablet */
  .offers-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
  
  .interests-grid {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 1.5rem;
  }
  
  /* Interest Cards */
  .decline-reason,
  .chat-info {
    flex-direction: row;
    align-items: center;
  }
  
  .user-section {
    flex-wrap: nowrap;
  }
  
  .action-buttons .n-space {
    justify-content: flex-end;
  }
}

/* Desktop Responsive Design (1024px+) */
@media (min-width: 1024px) {
  /* Hero Section */
  .hero-section {
    padding: 4rem 2rem;
  }
  
  .hero-title {
    font-size: 3.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .quick-stats {
    max-width: 800px;
    gap: 2rem;
  }
  
  /* Main Content */
  .my-offers-section,
  .interests-section,
  .empty-interests-section {
    padding: 0 3rem 3rem;
    max-width: 1400px;
    margin: 0 auto;
  }
  
  /* Grid Layout - 3 columns for desktop */
  .offers-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  /* Enhanced spacing for larger screens */
  .section-header {
    margin-bottom: 2.5rem;
  }
  
  .section-filters {
    align-self: flex-end;
  }
}

/* Large Desktop Responsive Design (1440px+) */
@media (min-width: 1440px) {
  /* Grid Layout - 4 columns for very large screens */
  .offers-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
  }
  
  .my-offers-section,
  .interests-section,
  .empty-interests-section {
    max-width: 1600px;
    padding: 0 4rem 4rem;
  }
  
  .hero-section {
    padding: 5rem 4rem;
  }
  
  .hero-title {
    font-size: 4rem;
  }
  
  .quick-stats {
    max-width: 900px;
    gap: 2.5rem;
  }
  
  .stat-card {
    padding: 2rem;
  }
  
  .stat-value {
    font-size: 2.5rem;
  }
}

/* Ultra-wide screens (1920px+) */
@media (min-width: 1920px) {
  .my-offers-section,
  .interests-section,
  .empty-interests-section {
    max-width: 1800px;
  }
  
  .offers-grid {
    gap: 3rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
