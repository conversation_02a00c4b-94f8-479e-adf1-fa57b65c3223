// PaymentReadinessGate.vue
<template>
  <div class="payment-readiness-gate">
    <NAlert
      v-if="displayState === 'PENDING_INPUT'"
      type="info"
      title="Action Required"
      :bordered="false"
      class="mb-4"
    >
      To proceed, please provide/confirm your payment receiving details. This information will be shared with the other party if they are designated to pay you.
    </NAlert>    
    <div v-if="displayState === 'PENDING_INPUT'" class="receiving-info-form">
      
      <div v-if="hasProfileDetails" class="profile-details mb-4">
        <div class="masked-details mb-2">
          {{ maskedProfileDetails }}
        </div>
        <NSpace>
          <NButton
            type="primary"
            @click="useProfileDetails"
            :loading="isLoading"
          >
            Use these details
          </NButton>
          <NButton
            @click="showNewDetailsForm"
          >
            Provide new details
          </NButton>
        </NSpace>
      </div>

      <NForm
        v-if="showForm"
        ref="formRef"
        :model="formModel"
        :rules="rules"
        @submit.prevent="handleSubmit"
      >
        <NFormItem label="Bank Name" path="bankName">
          <NInput v-model:value="formModel.bankName" placeholder="Enter bank name" />
        </NFormItem>

        <NFormItem label="Account Number" path="accountNumber">
          <NInput v-model:value="formModel.accountNumber" placeholder="Enter account number" />
        </NFormItem>

        <NFormItem label="Account Holder Name" path="accountHolderName">
          <NInput v-model:value="formModel.accountHolderName" placeholder="Enter account holder name" />
        </NFormItem>

        <NFormItem>
          <NCheckbox v-model:checked="formModel.saveToProfile">
            Save these details to my profile for future use
          </NCheckbox>
        </NFormItem>

        <NSpace justify="end">
          <NButton
            v-if="hasProfileDetails && showForm"
            @click="cancelNewDetails"
          >
            Cancel
          </NButton>
          <NButton
            type="primary"
            attr-type="submit"
            :loading="isLoading"
          >
            Save and Continue to Negotiation
          </NButton>
        </NSpace>
      </NForm>
    </div>

    <div v-else-if="displayState === 'CONFIRMED'" class="info-status">
      <NAlert
        type="success"
        title="Ready for Negotiation"
        :bordered="false"
      >
        Your payment receiving details are confirmed.
        <template>
          <NButton text @click="showEditDetails">
            Edit Details
          </NButton>
        </template>
      </NAlert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { NForm, NFormItem, NInput, NCheckbox, NButton, NSpace, NAlert } from 'naive-ui';
import type { FormInst, FormRules } from 'naive-ui';
import { ReceivingInfoStatus } from '@/types/payerNegotiation';
import type { PaymentReceivingInfo, PaymentReceivingSubmitPayload } from '@/types/payerNegotiation';

const props = defineProps<{
  receivingInfoStatus?: ReceivingInfoStatus;
  profileDetails?: PaymentReceivingInfo | null;
}>();

const emit = defineEmits<{
  (e: 'submit', info: PaymentReceivingSubmitPayload): void;
  (e: 'useProfile'): void;
}>();


const formRef = ref<FormInst | null>(null);
const isLoading = ref(false);
const showForm = ref(!props.profileDetails);

interface PaymentReceivingFormState {
  id?: string;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  isDefaultForUser?: boolean;
  saveToProfile: boolean;
}

const formModel = ref<PaymentReceivingFormState>({
  id: props.profileDetails?.id,
  bankName: props.profileDetails?.bankName || '',
  accountNumber: props.profileDetails?.accountNumber || '',
  accountHolderName: props.profileDetails?.accountHolderName || '',
  isDefaultForUser: props.profileDetails?.isDefaultForUser || false,
  saveToProfile: true,
});

watch(() => props.profileDetails, (newDetails, oldDetails) => {
  if (newDetails) {
    formModel.value.id = newDetails.id;
    formModel.value.bankName = newDetails.bankName;
    formModel.value.accountNumber = newDetails.accountNumber;
    formModel.value.accountHolderName = newDetails.accountHolderName;
    formModel.value.isDefaultForUser = newDetails.isDefaultForUser;
    
    // If new details have loaded and the form was shown because no details were available initially,
    // hide the form. The "Use these details" option will be available.
    if (showForm.value && !oldDetails) {
      showForm.value = false;
    }
  } else if (!showForm.value) {
    // This condition means: if newDetails is null (they were removed or never existed) 
    // AND the form was previously hidden (e.g., user clicked "Cancel" while editing/providing new details
    // after initially having profile details).
    // In this case, clear the form model.
    formModel.value.id = undefined;
    formModel.value.bankName = '';
    formModel.value.accountNumber = '';
    formModel.value.accountHolderName = '';
    formModel.value.isDefaultForUser = false;
    formModel.value.saveToProfile = true;
  }
  // If newDetails is null and showForm.value was already true (e.g. no profile details from start),
  // the form should remain visible, so no specific action here for that case.
}, { immediate: true });

const displayState = computed(() => {
  if (!props.receivingInfoStatus || props.receivingInfoStatus === ReceivingInfoStatus.PENDING_INPUT) {
    return 'PENDING_INPUT';
  }
  if (props.receivingInfoStatus === ReceivingInfoStatus.PROVIDED || props.receivingInfoStatus === ReceivingInfoStatus.CONFIRMED_FROM_PROFILE) {
    return 'CONFIRMED';
  }
  return 'PENDING_INPUT';
});

const rules: FormRules = {
  bankName: [
    { required: true, message: 'Please enter bank name', trigger: ['input', 'blur'] }
  ],
  accountNumber: [
    { required: true, message: 'Please enter account number', trigger: ['input', 'blur'] }
  ],
  accountHolderName: [
    { required: true, message: 'Please enter account holder name', trigger: ['input', 'blur'] }
  ]
};

const hasProfileDetails = computed(() => !!props.profileDetails);

const maskedProfileDetails = computed(() => {
  if (!props.profileDetails) return '';
  const details = props.profileDetails;
  const accNum = details.accountNumber || '';
  const lastFour = accNum.length > 4 ? accNum.slice(-4) : accNum;
  return `${details.bankName || 'Bank'} - Account ending in ${lastFour}`;
});

const showNewDetailsForm = () => {
  formModel.value = {
    bankName: '',
    accountNumber: '',
    accountHolderName: '',
    isDefaultForUser: false,
    saveToProfile: true // Keep saveToProfile as true for new details so they get saved to profile
  };
  showForm.value = true;
};

const cancelNewDetails = () => {
  if (hasProfileDetails.value) {
    showForm.value = false;
    if (props.profileDetails) {
        formModel.value = { ...props.profileDetails, saveToProfile: false };
    }
  }
};

const useProfileDetails = () => {
  emit('useProfile');
};

const showEditDetails = () => {
  if (props.receivingInfoStatus === ReceivingInfoStatus.PROVIDED || props.receivingInfoStatus === ReceivingInfoStatus.CONFIRMED_FROM_PROFILE) {
    if (props.profileDetails) {
        formModel.value = { ...props.profileDetails, saveToProfile: true }; // Set to true to save updates to profile
    } else {
        formModel.value = { bankName: '', accountNumber: '', accountHolderName: '', saveToProfile: true }; // Set to true for new details
    }
  }
  showForm.value = true;
};

const handleSubmit = async (e: Event) => {
  e.preventDefault();
  if (!formRef.value) return;

  isLoading.value = true;
  try {
    await formRef.value.validate();    // Force explicit values to debug the issue
    const saveToProfileValue = formModel.value.saveToProfile;
    const isDefaultForUserValue = saveToProfileValue; // Should be true when saving to profile
    
    const payload: PaymentReceivingSubmitPayload = {
      id: formModel.value.id || undefined,
      bankName: formModel.value.bankName,
      accountNumber: formModel.value.accountNumber,      accountHolderName: formModel.value.accountHolderName,
      saveToProfile: saveToProfileValue,
      isDefaultForUser: isDefaultForUserValue, // Explicit assignment
    };
    
    emit('submit', payload);

  } catch (errors) {
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.payment-readiness-gate {
  padding: 1rem;
}

.receiving-info-form {
  max-width: 500px;
  margin: 0 auto;
}

.masked-details {
  padding: 0.5rem;
  background: var(--n-color-info-1);
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}

/* Dark mode styling for PaymentReadinessGate */
[data-theme="dark"] .masked-details {
  background: var(--n-color-embedded);
  border-color: var(--n-divider-color);
  color: var(--n-text-color);
}

[data-theme="dark"] .payment-readiness-gate {
  color: var(--n-text-color);
}

[data-theme="dark"] .receiving-info-form {
  background-color: transparent;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.text-lg {
  font-size: 1.125rem;
  font-weight: 500;
}
</style>
