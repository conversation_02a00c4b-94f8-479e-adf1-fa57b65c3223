import { defineStore } from 'pinia';
import { ref } from 'vue';
import { acceptInterest, declineInterest } from '../services/interestService';
import centralizedSocketManager from '../services/centralizedSocketManager';
import { EventBus, EventTypes, type EventPayloads } from '../services/eventBus'; // MODIFIED IMPORT
import { useAuthStore } from './auth';
import { useOfferStore } from './offerStore'; // Import offerStore
import { 
  INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY, 
  INTEREST_REQUEST_DECLINED,                
  type YourInterestDeclinedPayload, 
  type InterestRequestAcceptedAndChatReadyPayload
} from '../types/socketEvents';

interface InterestRequest {
  id: string;
  offerId: string;
  status: 'PENDING' | 'ACCEPTED' | 'DECLINED';
  chatSessionId?: string;
  reasonCode?: string;
  // Add other fields as needed
}

export const useInterestStore = defineStore('interestStore', () => {
  const interestRequests = ref<InterestRequest[]>([]);
  const loading = ref<{ [id: string]: boolean }>({});


  function updateInterestStatus(interestId: string, status: 'ACCEPTED' | 'DECLINED', chatSessionId?: string, reasonCode?: string) {
    const interestIndex = interestRequests.value.findIndex(r => r.id === interestId);
    if (interestIndex !== -1) {
      // Create a new object for the updated interest to ensure reactivity
      const updatedInterest = {
        ...interestRequests.value[interestIndex],
        status,
        chatSessionId: chatSessionId ?? interestRequests.value[interestIndex].chatSessionId, // Preserve existing if not provided
        reasonCode: reasonCode ?? interestRequests.value[interestIndex].reasonCode, // Preserve existing if not provided
      };
      // Replace the old interest object with the new one in the array
      interestRequests.value.splice(interestIndex, 1, updatedInterest);
      console.log(`[interestStore] Interest ${interestId} status updated to ${status} (by replacement).`);
    } else {
      // If the interest is not in the local store (e.g., first time loading or edge case),
      // and it's an acceptance, consider adding it. This might be more relevant if 
      // interestRequests are populated from a specific API call for "my interests".
      // For now, we'll just log if it's not found.
      console.warn(`[interestStore] Attempted to update status for interest ${interestId}, but it was not found in local store.`);
    }
  }

  async function handleAcceptInterest(interestId: string, notification: any | null = null) {
    loading.value[interestId] = true;
    try {
      const result = await acceptInterest(interestId);
      updateInterestStatus(interestId, 'ACCEPTED', result.chatSessionId);
      if (notification?.success) {
        try {
          notification.success({ content: 'Interest accepted. Chat is now ready.' });
        } catch (error) {
          console.error('[interestStore] Failed to show success notification:', error);
        }
      }
    } catch (e: any) {
      console.error('[interestStore] Error accepting interest:', e);
      if (notification?.error) {
        try {
          notification.error({ content: e?.message || 'Failed to accept interest.' });
        } catch (error) {
          console.error('[interestStore] Failed to show error notification:', error);
        }
      }
    } finally {
      loading.value[interestId] = false;
    }
  }

  async function handleDeclineInterest(interestId: string, reasonCode?: string, notification: any | null = null) {
    loading.value[interestId] = true;
    try {
      await declineInterest(interestId, reasonCode);
      updateInterestStatus(interestId, 'DECLINED', undefined, reasonCode);
      if (notification?.success) {
        try {
          notification.success({ content: 'Interest declined.' });
        } catch (error) {
          console.error('[interestStore] Failed to show success notification:', error);
        }
      }
    } catch (e: any) {
      console.error('[interestStore] Error declining interest:', e);
      if (notification?.error) {
        try {
          notification.error({ content: e?.message || 'Failed to decline interest.' });
        } catch (error) {
          console.error('[interestStore] Failed to show error notification:', error);
        }
      }
    } finally {
      loading.value[interestId] = false;
    }
  }  // Socket event listeners using centralized socket manager
  let interestAcceptedUnsubscribe: (() => void) | null = null;
  let interestDeclinedUnsubscribe: (() => void) | null = null;
  
  function initializeSocketListeners() {
    console.log('🔥 [interestStore] initializeSocketListeners called');
    
    const authStore = useAuthStore();
    
    // Clean up existing listeners
    if (interestAcceptedUnsubscribe) {
      interestAcceptedUnsubscribe();
      interestAcceptedUnsubscribe = null;
    }
    if (interestDeclinedUnsubscribe) {
      interestDeclinedUnsubscribe();
      interestDeclinedUnsubscribe = null;
    }
    
    // Handler for when an interest *you showed* is accepted
    const handleInterestAccepted = (payload: InterestRequestAcceptedAndChatReadyPayload) => {
      console.log('🔥 [interestStore] Received INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY event:', payload);
      const currentUser = authStore.user;
      console.log('🔥 [interestStore] Current user:', currentUser?.id, 'Payload interested user:', payload.interestedUser?.userId);
      
      // Check if the current user is the one who showed the interest
      if (currentUser && payload.interestedUser && payload.interestedUser.userId === currentUser.id) {
        console.log('🔥 [interestStore] Event is for current user - updating interest status to ACCEPTED');
        updateInterestStatus(payload.interestId, 'ACCEPTED', payload.chatSessionId);
        
        // Also update the offerStore so the UI shows the chat button
        const offerStore = useOfferStore();
        offerStore.updateInterestStatus(payload.offerId, 'ACCEPTED', payload.chatSessionId);
        console.log('🔥 [interestStore] Updated offerStore interest status to ACCEPTED');
        
        EventBus.emit(EventTypes.INTEREST_STATUS_CHANGED_BY_OTHERS, {
          offerId: payload.offerId,
          interestId: payload.interestId,
          newStatus: 'ACCEPTED',
          chatSessionId: payload.chatSessionId
        } as EventPayloads[typeof EventTypes.INTEREST_STATUS_CHANGED_BY_OTHERS]);
        console.log(`[interestStore] Received ${INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY} for current user, updated local status and emitted EventBus event.`, payload);
      } else {
        console.log(`🔥 [interestStore] Event NOT for current user - ignoring. CurrentUserId: ${currentUser?.id}, PayloadInterestedUserId: ${payload.interestedUser?.userId}`);
      }
    };
    
    // Handler for when an interest *you showed* is declined
    const handleInterestDeclined = (payload: YourInterestDeclinedPayload) => { 
      console.log(`🔥 [interestStore] Received ${INTEREST_REQUEST_DECLINED} with payload:`, JSON.stringify(payload));
      updateInterestStatus(payload.interestId, 'DECLINED', undefined, payload.reasonCode);
      EventBus.emit(EventTypes.INTEREST_STATUS_CHANGED_BY_OTHERS, {
        offerId: payload.offerId,
        interestId: payload.interestId,
        newStatus: 'DECLINED',
        reasonCode: payload.reasonCode
      } as EventPayloads[typeof EventTypes.INTEREST_STATUS_CHANGED_BY_OTHERS]);
      console.log('[interestStore] Updated local status for declined interest and emitted EventBus event. Offer:', payload.offer, 'Declined by:', payload.offerCreator);
    };
    
    // Register handlers with centralized socket manager
    interestAcceptedUnsubscribe = centralizedSocketManager.on(INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY, handleInterestAccepted);
    interestDeclinedUnsubscribe = centralizedSocketManager.on(INTEREST_REQUEST_DECLINED, handleInterestDeclined);
    
    console.log('🔥 [interestStore] Socket listeners for YOUR interests (acceptance/declines) registered successfully with centralized manager');
  }

  return {
    interestRequests,
    loading,
    handleAcceptInterest,
    handleDeclineInterest,
    initializeSocketListeners,
    updateInterestStatus,
  };
});
