/**
 * Test script to verify AI voice analysis error handling improvements
 * Ensures error messages are properly handled and displayed to users
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing AI Voice Analysis Error Handling...\n');

// Test 1: Check VoiceRecorder has new prop for disabling internal error modal
const voiceRecorderPath = path.join(__dirname, 'frontend', 'src', 'components', 'VoiceRecorder.vue');
const voiceRecorderContent = fs.readFileSync(voiceRecorderPath, 'utf8');

const hasDisableInternalErrorProp = voiceRecorderContent.includes('disableInternalErrorModal?: boolean');
const respectsDisableErrorProp = voiceRecorderContent.includes('showAnalysisFailure.value = !props.disableInternalErrorModal');
const translatesErrorSuggestions = voiceRecorderContent.includes('suggestions: (failure.suggestionKeys || []).map(key => t(key))');

console.log(`✅ VoiceRecorder has disable error modal prop: ${hasDisableInternalErrorProp ? 'PASS' : 'FAIL'}`);
console.log(`✅ VoiceRecorder respects disable error prop: ${respectsDisableErrorProp ? 'PASS' : 'FAIL'}`);
console.log(`✅ VoiceRecorder translates error suggestions: ${translatesErrorSuggestions ? 'PASS' : 'FAIL'}`);

// Test 2: Check DebugReportButtonEnhanced passes the prop
const debugReportPath = path.join(__dirname, 'frontend', 'src', 'components', 'DebugReportButtonEnhanced.vue');
const debugReportContent = fs.readFileSync(debugReportPath, 'utf8');

const passesDisableErrorProp = debugReportContent.includes(':disable-internal-error-modal="true"');
const hasErrorHandling = debugReportContent.includes('handleVoiceError');

console.log(`✅ DebugReportButtonEnhanced disables VoiceRecorder modal: ${passesDisableErrorProp ? 'PASS' : 'FAIL'}`);
console.log(`✅ DebugReportButtonEnhanced has error handling: ${hasErrorHandling ? 'PASS' : 'FAIL'}`);

// Test 3: Check translation keys exist
const enTranslationsPath = path.join(__dirname, 'frontend', 'src', 'locales', 'en.json');
const enTranslations = JSON.parse(fs.readFileSync(enTranslationsPath, 'utf8'));

const hasAnalysisFailedTitle = !!enTranslations.voice?.analysisFailedTitle;
const hasImprovementSuggestions = !!enTranslations.voice?.improvementSuggestions;
const hasDetectedSpeech = !!enTranslations.voice?.detectedSpeech;
const hasAnalysisFailures = !!enTranslations.voice?.analysisFailures;

console.log(`✅ Has analysisFailedTitle translation: ${hasAnalysisFailedTitle ? 'PASS' : 'FAIL'}`);
console.log(`✅ Has improvementSuggestions translation: ${hasImprovementSuggestions ? 'PASS' : 'FAIL'}`);
console.log(`✅ Has detectedSpeech translation: ${hasDetectedSpeech ? 'PASS' : 'FAIL'}`);
console.log(`✅ Has analysisFailures translations: ${hasAnalysisFailures ? 'PASS' : 'FAIL'}`);

// Test 4: Check error categories are supported
const hasErrorCategories = enTranslations.voice?.analysisFailures && 
  enTranslations.voice.analysisFailures.audioUnclear &&
  enTranslations.voice.analysisFailures.backgroundNoise &&
  enTranslations.voice.analysisFailures.irrelevantContent;

console.log(`✅ Has all error category translations: ${hasErrorCategories ? 'PASS' : 'FAIL'}`);

// Test 5: Check suggestion translations exist
const hasSuggestions = enTranslations.voice?.suggestions &&
  enTranslations.voice.suggestions.speakClearly &&
  enTranslations.voice.suggestions.quietEnvironment &&
  enTranslations.voice.suggestions.recordLonger;

console.log(`✅ Has suggestion translations: ${hasSuggestions ? 'PASS' : 'FAIL'}`);

console.log('\n📊 SUMMARY:');
const tests = [
  hasDisableInternalErrorProp,
  respectsDisableErrorProp, 
  translatesErrorSuggestions,
  passesDisableErrorProp,
  hasErrorHandling,
  hasAnalysisFailedTitle,
  hasImprovementSuggestions,
  hasDetectedSpeech,
  hasAnalysisFailures,
  hasErrorCategories,
  hasSuggestions
];

const passCount = tests.filter(Boolean).length;
const totalCount = tests.length;

if (passCount === totalCount) {
  console.log(`🎉 ALL TESTS PASSED (${passCount}/${totalCount})`);
  console.log('✅ Error handling system properly integrated');
  console.log('✅ User feedback for AI analysis failures is complete');
  console.log('✅ No duplicate error modals should appear');
} else {
  console.log(`⚠️  SOME TESTS FAILED (${passCount}/${totalCount})`);
  console.log('❌ Error handling may have issues');
  process.exit(1);
}
