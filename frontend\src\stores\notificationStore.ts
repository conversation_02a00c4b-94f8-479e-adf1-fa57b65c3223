// src/stores/notificationStore.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import apiClient from '@/services/apiClient';
import centralizedSocketManager from '@/services/centralizedSocketManager';
import { NEW_NOTIFICATION } from '@/types/socketEvents';

// Corresponds to the backend's Notification model and NotificationType enum
// Ensure these NotificationType values match exactly with your backend Prisma enum
export enum FrontendNotificationType {
  NEW_INTEREST_ON_YOUR_OFFER = 'NEW_INTEREST_ON_YOUR_OFFER',
  YOUR_INTEREST_ACCEPTED = 'YOUR_INTEREST_ACCEPTED',
  YOUR_INTEREST_DECLINED = 'YOUR_INTEREST_DECLINED',
  CHAT_MESSAGE_RECEIVED = 'CHAT_MESSAGE_RECEIVED', // If you handle chat notifications here
  OFFER_STATUS_UPDATED_BY_OWNER = 'OFFER_STATUS_UPDATED_BY_OWNER',
  OFFER_STATUS_CHANGED = 'OFFER_STATUS_CHANGED',
  // Transaction notifications
  TRANSACTION_STATUS_CHANGED = 'TRANSACTION_STATUS_CHANGED',
  // Match system notifications
  MATCH_FOUND = 'MATCH_FOUND',
  MATCH_ACCEPTED = 'MATCH_ACCEPTED',
  MATCH_DECLINED = 'MATCH_DECLINED',
  MATCH_EXPIRED = 'MATCH_EXPIRED',
  MATCH_CONVERTED = 'MATCH_CONVERTED',
  // New match notification types
  MATCH_ACCEPTED_BY_OTHER = 'MATCH_ACCEPTED_BY_OTHER',
  MATCH_DECLINED_BY_OTHER = 'MATCH_DECLINED_BY_OTHER',
  MATCH_CONVERTED_TO_CHAT = 'MATCH_CONVERTED_TO_CHAT',
  // Add other types as needed
}

export interface FrontendNotification {
  id: string; // Notification ID
  userId: string; // Recipient
  type: FrontendNotificationType;
  message: string;
  isRead: boolean;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string

  relatedEntityType?: string;
  relatedEntityId?: string;
  actorId?: string;
  actorUsername?: string;
  data?: Record<string, any>; // For additional context
}

export const useNotificationStore = defineStore('notificationStore', () => {
  const notifications = ref<FrontendNotification[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const lastFetchedTimestamp = ref<number | null>(null);
  const isInitialized = ref(false);

  // Socket event unsubscribe function for cleanup
  let newNotificationUnsubscribe: (() => void) | null = null;
  const unreadNotificationsCount = computed(() => {
    return notifications.value.filter(n => !n.isRead).length;
  });  // Computed property for unread notifications only (visible in main panel)
  const unreadNotifications = computed(() => {
    const unread = notifications.value.filter(n => !n.isRead);
    console.log(`🚨 [NotificationStore] unreadNotifications computed - Total: ${notifications.value.length}, Unread: ${unread.length}`);
    if (notifications.value.length > 0) {
      console.log(`🚨 [NotificationStore] Sample notification statuses:`, notifications.value.slice(0, 5).map(n => ({ id: n.id, isRead: n.isRead, message: n.message.substring(0, 30) })));
    } else {
      console.log(`🚨 [NotificationStore] No notifications in store at all`);
    }
    return unread;
  });
  async function fetchNotifications(options: { limit?: number; unreadOnly?: boolean } = {}) {
    // Enhanced debugging to track all calls
    console.log('[NotificationStore] 🔍 fetchNotifications called with options:', options);
    console.log('[NotificationStore] 🔍 Stack trace:', new Error().stack);
    
    const finalUnreadOnly = options.unreadOnly === undefined ? true : options.unreadOnly;
    console.log('[NotificationStore] 🔍 Final unreadOnly parameter:', finalUnreadOnly);
    
    isLoading.value = true;
    error.value = null;
    try {
      const requestParams = {
        limit: options.limit || 20, // Default limit
        unreadOnly: finalUnreadOnly, // Use the computed value
        // offset can be added if pagination beyond 'load more' or initial fetch is needed
      };
      
      console.log('[NotificationStore] 🔍 API request params:', requestParams);
      
      const response = await apiClient.get<FrontendNotification[]>('/notifications', {
        params: requestParams,
      });      if (response.data && Array.isArray(response.data)) {
        // Replace or merge strategies can be chosen. Replacing for simplicity here.
        notifications.value = response.data;
        lastFetchedTimestamp.value = Date.now();
        console.log(`[NotificationStore] 🔍 Fetched ${response.data.length} notifications with unreadOnly=${finalUnreadOnly}.`);
        console.log(`[NotificationStore] 🔍 Sample notifications:`, response.data.slice(0, 3).map(n => ({ id: n.id, isRead: n.isRead, message: n.message.substring(0, 50) })));
      } else {
        notifications.value = [];
        console.warn('[NotificationStore] No data or invalid data format received.');
      }
    } catch (err: any) {
      console.error('[NotificationStore] Failed to fetch notifications:', err);
      error.value = err.response?.data?.message || err.message || 'Failed to fetch notifications.';
    } finally {
      isLoading.value = false;
    }
  }  async function markNotificationAsRead(notificationId: string) {
    console.log(`🚨 [NotificationStore] markNotificationAsRead called for: ${notificationId}`);
    console.log(`🚨 [NotificationStore] Current notifications count before marking:`, notifications.value.length);
    console.log(`🚨 [NotificationStore] Notification being marked:`, notifications.value.find(n => n.id === notificationId));
    
    try {
      const response = await apiClient.post<FrontendNotification>(`/notifications/${notificationId}/mark-read`);
      // Only update local state if API call succeeds
      const index = notifications.value.findIndex(n => n.id === notificationId);
      if (index !== -1 && response.data) {
        notifications.value[index].isRead = true;
        console.log(`🚨 [NotificationStore] Successfully marked notification ${notificationId} as read locally.`);
        console.log(`🚨 [NotificationStore] Updated notification:`, notifications.value[index]);
        // Optionally, re-sort or move read notifications
      }
      console.log(`🚨 [NotificationStore] Notification ${notificationId} marked as read on backend.`);
      
      // Cleanup old notifications periodically
      cleanupOldReadNotifications();
      
    } catch (err: any) {
      console.error(`🚨 [NotificationStore] Failed to mark notification ${notificationId} as read:`, err);
      error.value = err.response?.data?.message || err.message || 'Failed to mark as read.';
      // Don't update local state on failure - this fixes the optimistic update issue
    }
  }
  async function markAllNotificationsAsRead() {
    console.log('[NotificationStore] Marking all notifications as read.');
    try {
      await apiClient.post<{ count: number }>('/notifications/mark-all-read');
      // Only update local state if API call succeeds
      notifications.value.forEach(n => n.isRead = true);
      console.log('[NotificationStore] All notifications marked as read locally.');
      
      // Cleanup old notifications after marking all as read
      cleanupOldReadNotifications();
      
    } catch (err: any) {
      console.error('[NotificationStore] Failed to mark all notifications as read:', err);
      error.value = err.response?.data?.message || err.message || 'Failed to mark all as read.';
      // Don't update local state on failure - this fixes the optimistic update issue
    }
  }

  function addOrUpdateNotification(notification: FrontendNotification) {
    console.log('[NotificationStore] addOrUpdateNotification called with:', JSON.stringify(notification));
    const existingIndex = notifications.value.findIndex(n => n.id === notification.id);
    if (existingIndex !== -1) {
      notifications.value[existingIndex] = notification;
      console.log(`[NotificationStore] Updated existing notification ${notification.id}.`);
    } else {
      notifications.value.unshift(notification); // Add new notifications to the top
      console.log(`[NotificationStore] Added new notification ${notification.id}.`);
    }

    if (notification.type === FrontendNotificationType.YOUR_INTEREST_DECLINED) {
      console.log(`[NotificationStore] Processed YOUR_INTEREST_DECLINED notification: ${notification.id}. Message: ${notification.message}`);
      // Log data associated with the decline for debugging
      if (notification.data) {
        console.log(`[NotificationStore] Decline data: offerId=${notification.data.offerId}, reasonCode=${notification.data.reasonCode}, declinedBy=${notification.data.declinedByUsername}`);
      }
    }

    // Automatically mark certain important notifications as read once received by the store
    // RESTORING THIS BLOCK
    /*
    if (
      (notification.type === FrontendNotificationType.YOUR_INTEREST_DECLINED ||
       notification.type === FrontendNotificationType.YOUR_INTEREST_ACCEPTED) &&
      !notification.isRead
    ) {
      // Call without awaiting to prevent blocking notification display flow.
      // Errors are logged by markNotificationAsRead itself.
      markNotificationAsRead(notification.id).catch(err => {
        console.error(`[NotificationStore] Auto-mark read failed for ${notification.id}:`, err);
      });
      // Optimistically update local state for immediate UI feedback
      const localNotif = notifications.value.find(n => n.id === notification.id);
      if (localNotif) {
        localNotif.isRead = true;
      }
    }
    */
    // Optional: sort notifications by date again if needed
    // notifications.value.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }
  
  function removeNotificationById(notificationId: string) {
    notifications.value = notifications.value.filter(n => n.id !== notificationId);
    console.log(`[NotificationStore] Notification ${notificationId} removed.`);
  }

  function clearAllLocalNotifications() {
    notifications.value = [];
    lastFetchedTimestamp.value = null;
    console.log('[NotificationStore] All local notifications cleared.');
  }  function initializeNotificationListeners() {
    console.log('🔔🔔🔔 [NotificationStore] initializeNotificationListeners() called!');
    console.log('[NotificationStore] Initializing notification listeners via centralized socket manager');
    console.log('[NotificationStore] centralizedSocketManager object:', centralizedSocketManager);
    console.log('[NotificationStore] centralizedSocketManager.on function:', centralizedSocketManager.on);
    
    // Clean up existing listener
    if (newNotificationUnsubscribe) {
      console.log('[NotificationStore] Cleaning up existing listener');
      newNotificationUnsubscribe();
      newNotificationUnsubscribe = null;
    }

    // Register NEW_NOTIFICATION handler using the centralized socket manager
    console.log(`🔔🔔🔔 [NotificationStore] About to register handler for event: ${NEW_NOTIFICATION}`);
    console.log(`🔔🔔🔔 [NotificationStore] NEW_NOTIFICATION constant value:`, NEW_NOTIFICATION);
    newNotificationUnsubscribe = centralizedSocketManager.on(NEW_NOTIFICATION, (notification: FrontendNotification) => {
      console.log('[NotificationStore] Received NEW_NOTIFICATION via centralized manager:', notification);
      addOrUpdateNotification(notification);
      // Potentially trigger a UI update or a subtle alert
    });

    console.log('[NotificationStore] Handler registered, unsubscribe function:', newNotificationUnsubscribe);
    console.log('[NotificationStore] Socket listeners initialized via centralized manager.');
  }

  // Cleanup function for socket listeners
  function cleanupNotificationListeners() {
    if (newNotificationUnsubscribe) {
      newNotificationUnsubscribe();
      newNotificationUnsubscribe = null;
    }
  }  // Function to initialize notification system after socket is ready
  async function initializeNotificationSystem() {
    console.log('🔔🔔🔔 [NotificationStore] initializeNotificationSystem() called!');
    console.log('🔔🔔🔔 [NotificationStore] Current socket state:', centralizedSocketManager.isConnected);
    console.log('🔔🔔🔔 [NotificationStore] Current isInitialized state:', isInitialized.value);
    
    // Don't initialize twice
    if (isInitialized.value) {
      console.log('🔔🔔🔔 [NotificationStore] Already initialized, skipping...');
      return true;
    }
    
    console.log('[NotificationStore] Initializing notification system...');
    try {
      // Wait for socket to be ready before registering listeners
      const socketReady = await centralizedSocketManager.waitForReady(15000); // 15 second timeout
      
      if (socketReady) {
        console.log('🔔🔔🔔 [NotificationStore] Socket is ready, calling initializeNotificationListeners()');
        initializeNotificationListeners();
        console.log('[NotificationStore] Socket listeners initialized successfully');
          // Mark as initialized
        isInitialized.value = true;
        console.log('🔔🔔🔔 [NotificationStore] Marked as initialized');
          // Fetch only unread notifications after successful socket connection
        await fetchNotifications({ unreadOnly: true, limit: 50 }); // Only fetch unread notifications
        console.log('[NotificationStore] Initial unread notifications fetched');
        return true;
      } else {
        console.warn('[NotificationStore] Socket not ready after timeout, notifications may not work');
        return false;
      }
    } catch (error) {
      console.error('[NotificationStore] Failed to initialize notification system:', error);
      return false;
    }
  }
  // Function to cleanup notification system on logout
  function cleanupNotificationSystem() {
    console.log('[NotificationStore] Cleaning up notification system');
    cleanupNotificationListeners();
    clearAllLocalNotifications();
    isInitialized.value = false; // Reset initialization state
    console.log('🔔🔔🔔 [NotificationStore] Reset isInitialized to false');
  } // Performance optimization: cleanup old read notifications to prevent memory bloat
  function cleanupOldReadNotifications(maxReadNotifications: number = 100) {
    const readNotifications = notifications.value.filter(n => n.isRead);
    if (readNotifications.length > maxReadNotifications) {
      // Sort by updatedAt and keep only the most recent read notifications
      const sortedRead = readNotifications.sort((a, b) => 
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      );
      const toKeep = sortedRead.slice(0, maxReadNotifications);
      const toKeepIds = new Set(toKeep.map(n => n.id));
      
      // Keep all unread notifications plus the most recent read ones
      notifications.value = notifications.value.filter(n => 
        !n.isRead || toKeepIds.has(n.id)
      );
      
      console.log(`[NotificationStore] Cleaned up old read notifications, kept ${toKeep.length} most recent`);
    }
  }

  // Method to batch mark multiple notifications as read (for performance)
  async function markMultipleNotificationsAsRead(notificationIds: string[]) {
    console.log(`[NotificationStore] Marking ${notificationIds.length} notifications as read.`);
    try {
      // For now, call individual mark-as-read endpoints
      // In future, this could be optimized with a batch endpoint
      const promises = notificationIds.map(id => 
        apiClient.post<FrontendNotification>(`/notifications/${id}/mark-read`)
      );
      
      await Promise.all(promises);
      
      // Update local state only after all API calls succeed
      notificationIds.forEach(id => {
        const index = notifications.value.findIndex(n => n.id === id);
        if (index !== -1) {
          notifications.value[index].isRead = true;
        }
      });
      
      console.log(`[NotificationStore] Successfully marked ${notificationIds.length} notifications as read.`);
      
      // Cleanup old notifications if we have too many
      cleanupOldReadNotifications();
      
    } catch (err: any) {
      console.error(`[NotificationStore] Failed to mark multiple notifications as read:`, err);
      error.value = err.response?.data?.message || err.message || 'Failed to mark notifications as read.';
    }
  }

  return {
    notifications,
    unreadNotifications, // Only unread notifications for main panel display
    isLoading,
    error,
    isInitialized,
    unreadNotificationsCount,
    fetchNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    markMultipleNotificationsAsRead, // New batch operation method
    cleanupOldReadNotifications, // Performance optimization method
    addOrUpdateNotification, // Expose if needed externally, e.g. for testing
    removeNotificationById,
    clearAllLocalNotifications,
    initializeNotificationListeners, // To be called from appropriate Vue component, e.g. App.vue onMount
    cleanupNotificationListeners,
    initializeNotificationSystem, // New method for proper initialization
    cleanupNotificationSystem, // New method for proper cleanup
    lastFetchedTimestamp,
  };
});
