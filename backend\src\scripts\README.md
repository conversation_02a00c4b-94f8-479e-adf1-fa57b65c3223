# Database Scripts

This directory contains various database management and seeding scripts for development and testing.

## Test User Scripts

### `seedTestUsers.ts`
Creates test users with different reputation levels:
- **<EMAIL>** (h) - Level 5 Elite user with high reputation
- **<EMAIL>** (h2) - Recent user with lower reputation 
- **<EMAIL>** (h3) - Mid-level reputation user

**Usage:**
```bash
npm run seed:test-users
```

All test users use the password: `11111111`

### `clearTestUsers.ts`
Removes all test users created by the seeding script.

**Usage:**
```bash
npm run clear:test-users
```

## Payment Method Scripts

### `seedTestPaymentMethods.ts`
Creates test payment methods for the seeded test users.

**Usage:**
```bash
npm run seed:payment-methods
```

## Offer Scripts

### `seedTestOffers.ts`
Creates sample currency exchange offers using the test users.

**Usage:**
```bash
npm run seed:test-offers
```

## Combined Scripts

### `seedAll.ts`
Runs all seeding scripts in the correct order:
1. Test users
2. Payment methods
3. Test offers

**Usage:**
```bash
npm run seed:all
```

### `resetDev.ts`
Completely resets the development database and re-seeds with test data.

**Usage:**
```bash
npm run reset:dev
```

## Migration Scripts

### `migrateDebugReports.ts`
Migrates debug reports data (existing script).

**Usage:**
```bash
npm run migrate:debug-reports
```
