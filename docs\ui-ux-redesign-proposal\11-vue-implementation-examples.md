# Smart Transaction-Chat Integration - Vue Implementation

This document provides complete Vue.js implementation examples for the innovative transaction-chat UI design.

## 📧 SmartSystemMessage Component

```vue
<template>
  <div 
    class="smart-system-message" 
    :class="[messageTypeClass, urgencyClass]"
  >
    <!-- Payment Declaration Message -->
    <div v-if="message.type === 'payment_declared'" class="payment-message">
      <div class="message-header">
        <n-avatar 
          :src="message.sender.avatar" 
          size="small" 
          class="sender-avatar"
        />
        <div class="sender-info">
          <span class="sender-name">{{ message.sender.name }}</span>
          <span class="action-text">declared payment</span>
        </div>
        <n-tag type="warning" size="small">Pending</n-tag>
      </div>
      
      <div class="payment-content">
        <div class="amount-section">
          <span class="amount financial-data">{{ formatAmount(message.amount) }}</span>
          <span class="currency">{{ message.currency }}</span>
          <span class="arrow">→</span>
          <span class="recipient">{{ message.recipient }}</span>
        </div>
        
        <div v-if="message.trackingNumber" class="tracking-section">
          <n-icon :component="ReceiptOutline" size="16" />
          <span class="tracking-label">Tracking:</span>
          <span class="tracking-number financial-data">{{ message.trackingNumber }}</span>
          <n-button size="tiny" text @click="copyTracking">Copy</n-button>
        </div>
      </div>
    </div>

    <!-- Status Update Message -->
    <div v-if="message.type === 'status_update'" class="status-message">
      <div class="status-header">
        <n-icon 
          :component="getStatusIcon(message.status)" 
          :class="getStatusIconClass(message.status)"
          size="20"
        />
        <div class="status-info">
          <span class="status-title">{{ getStatusTitle(message.status) }}</span>
          <span class="status-description">{{ message.description }}</span>
        </div>
      </div>
      
      <div v-if="message.actionRequired" class="status-actions">
        <n-button 
          :type="getActionButtonType(message.primaryAction.type)"
          size="small"
          @click="$emit('action', message.primaryAction)"
        >
          <template #icon>
            <n-icon :component="getActionIcon(message.primaryAction.type)" />
          </template>
          {{ message.primaryAction.text }}
        </n-button>
      </div>
    </div>

    <!-- Timer Warning Message -->
    <div v-if="message.type === 'timer_warning'" class="timer-message">
      <div class="timer-header">
        <n-icon 
          :component="ClockOutline" 
          class="timer-icon"
          :class="{ 'timer-critical': message.critical }"
          size="18"
        />
        <div class="timer-info">
          <span class="timer-title">{{ message.title }}</span>
          <span 
            class="timer-countdown financial-data" 
            :class="getTimerClass(message.timeRemaining)"
          >
            {{ formatTimeRemaining(message.timeRemaining) }}
          </span>
        </div>
      </div>
      
      <div class="timer-description">
        <p>{{ message.description }}</p>
      </div>
    </div>

    <!-- Progress Update Message -->
    <div v-if="message.type === 'progress_update'" class="progress-message">
      <div class="progress-header">
        <n-icon :component="CheckmarkCircleOutline" class="progress-icon" size="18" />
        <span class="progress-title">{{ message.title }}</span>
      </div>
      
      <div class="progress-content">
        <div class="progress-bar-wrapper">
          <n-progress 
            :percentage="message.progressPercentage" 
            :status="message.progressStatus"
            :show-indicator="false"
            style="margin: 8px 0;"
          />
          <span class="progress-text">{{ message.progressText }}</span>
        </div>
      </div>
    </div>

    <!-- Timestamp -->
    <div class="message-timestamp">
      {{ formatTimestamp(message.timestamp) }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  ClockOutline, 
  CheckmarkCircleOutline, 
  ReceiptOutline,
  CardOutline,
  AlertCircleOutline,
  CheckmarkOutline
} from '@vicons/ionicons5';
import { useMessage } from 'naive-ui';

interface SmartMessage {
  id: string;
  type: 'payment_declared' | 'status_update' | 'timer_warning' | 'progress_update';
  timestamp: Date;
  sender?: {
    name: string;
    avatar?: string;
  };
  amount?: number;
  currency?: string;
  recipient?: string;
  trackingNumber?: string;
  status?: string;
  description?: string;
  actionRequired?: boolean;
  primaryAction?: {
    type: string;
    text: string;
    action: string;
  };
  title?: string;
  timeRemaining?: number;
  critical?: boolean;
  progressPercentage?: number;
  progressStatus?: 'success' | 'warning' | 'error' | 'info';
  progressText?: string;
}

interface Props {
  message: SmartMessage;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  action: [action: any];
}>();

const messageApi = useMessage();

const messageTypeClass = computed(() => `message-type-${props.message.type}`);
const urgencyClass = computed(() => {
  if (props.message.critical) return 'urgent';
  if (props.message.actionRequired) return 'action-required';
  return 'info';
});

const formatAmount = (amount: number) => {
  return new Intl.NumberFormat().format(amount);
};

const formatTimestamp = (timestamp: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  }).format(timestamp);
};

const formatTimeRemaining = (milliseconds: number) => {
  const minutes = Math.floor(milliseconds / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  }
  return `${minutes}m`;
};

const getStatusIcon = (status: string) => {
  const iconMap: Record<string, any> = {
    'payment_sent': CardOutline,
    'payment_confirmed': CheckmarkCircleOutline,
    'awaiting_confirmation': ClockOutline,
    'expired': AlertCircleOutline
  };
  return iconMap[status] || AlertCircleOutline;
};

const getStatusIconClass = (status: string) => {
  const classMap: Record<string, string> = {
    'payment_sent': 'status-icon-warning',
    'payment_confirmed': 'status-icon-success',
    'awaiting_confirmation': 'status-icon-info',
    'expired': 'status-icon-error'
  };
  return classMap[status] || '';
};

const getStatusTitle = (status: string) => {
  const titleMap: Record<string, string> = {
    'payment_sent': 'Payment Sent',
    'payment_confirmed': 'Payment Confirmed',
    'awaiting_confirmation': 'Awaiting Confirmation',
    'expired': 'Payment Window Expired'
  };
  return titleMap[status] || 'Status Update';
};

const getActionButtonType = (actionType: string) => {
  const typeMap: Record<string, string> = {
    'confirm': 'success',
    'pay': 'primary',
    'dispute': 'warning',
    'cancel': 'error'
  };
  return typeMap[actionType] || 'default';
};

const getActionIcon = (actionType: string) => {
  const iconMap: Record<string, any> = {
    'confirm': CheckmarkOutline,
    'pay': CardOutline,
    'dispute': AlertCircleOutline,
    'cancel': AlertCircleOutline
  };
  return iconMap[actionType] || CheckmarkOutline;
};

const getTimerClass = (timeRemaining: number) => {
  const minutes = timeRemaining / (1000 * 60);
  if (minutes <= 30) return 'timer-critical';
  if (minutes <= 120) return 'timer-warning';
  return 'timer-normal';
};

const copyTracking = async () => {
  if (props.message.trackingNumber) {
    await navigator.clipboard.writeText(props.message.trackingNumber);
    messageApi.success('Tracking number copied');
  }
};
</script>

<style scoped>
.smart-system-message {
  margin: 16px 12px;
  padding: 16px;
  border-radius: 12px;
  background: var(--bg-elevated);
  border-left: 4px solid var(--border-emphasis);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.smart-system-message.urgent {
  border-left-color: var(--color-error);
  background: linear-gradient(135deg, var(--error-50) 0%, var(--bg-elevated) 100%);
}

.smart-system-message.action-required {
  border-left-color: var(--color-warning);
  background: linear-gradient(135deg, var(--warning-50) 0%, var(--bg-elevated) 100%);
}

.smart-system-message.info {
  border-left-color: var(--color-primary);
  background: linear-gradient(135deg, var(--blue-50) 0%, var(--bg-elevated) 100%);
}

/* Payment Message */
.payment-message .message-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.sender-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sender-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.action-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.payment-content {
  margin-left: 44px; /* Align with content after avatar */
}

.amount-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.amount {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-primary);
}

.currency {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
}

.arrow {
  color: var(--color-accent);
  font-weight: 600;
}

.recipient {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.tracking-section {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--bg-hover);
  border-radius: 8px;
  font-size: 13px;
}

.tracking-label {
  color: var(--text-secondary);
}

.tracking-number {
  flex: 1;
  font-weight: 600;
  color: var(--text-primary);
}

/* Status Message */
.status-message .status-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.status-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.status-description {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.status-icon-success { color: var(--color-success); }
.status-icon-warning { color: var(--color-warning); }
.status-icon-error { color: var(--color-error); }
.status-icon-info { color: var(--color-primary); }

.status-actions {
  margin-left: 32px; /* Align with status content */
}

/* Timer Message */
.timer-message .timer-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.timer-icon {
  color: var(--color-warning);
  transition: color 0.2s ease;
}

.timer-icon.timer-critical {
  color: var(--color-error);
  animation: pulse 1s infinite;
}

.timer-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timer-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.timer-countdown {
  font-weight: 700;
  font-size: 16px;
}

.timer-countdown.timer-normal { color: var(--color-primary); }
.timer-countdown.timer-warning { color: var(--color-warning); }
.timer-countdown.timer-critical { 
  color: var(--color-error);
  animation: pulse 1s infinite;
}

.timer-description {
  margin-left: 30px;
  font-size: 13px;
  color: var(--text-secondary);
}

/* Progress Message */
.progress-message .progress-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.progress-icon {
  color: var(--color-success);
}

.progress-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.progress-content {
  margin-left: 30px;
}

.progress-bar-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Common Elements */
.message-timestamp {
  margin-top: 12px;
  font-size: 11px;
  color: var(--text-disabled);
  text-align: right;
}

.financial-data {
  font-family: var(--font-mono);
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .smart-system-message {
    margin: 12px 8px;
    padding: 12px;
  }
  
  .amount-section {
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .timer-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
```

## 🎯 SmartActionBar Component

```vue
<template>
  <div 
    class="smart-action-bar" 
    :class="contextClass"
    v-if="shouldShow"
  >
    <!-- Context Information -->
    <div class="action-context">
      <div class="context-header">
        <n-icon :component="getContextIcon()" class="context-icon" size="16" />
        <span class="context-title">{{ getContextTitle() }}</span>
        <n-tag :type="getContextTagType()" size="small">{{ getContextTag() }}</n-tag>
      </div>
      
      <div class="context-details">
        <!-- Payment Required Context -->
        <template v-if="context.type === 'payment_required'">
          <div class="payment-info">
            <span class="payment-text">Send</span>
            <span class="amount financial-data">{{ formatAmount(context.amount) }}</span>
            <span class="currency">{{ context.currency }}</span>
            <span class="payment-text">to</span>
            <span class="recipient">{{ context.recipient }}</span>
          </div>
          
          <div class="account-preview">
            <span class="account-label">Account:</span>
            <span class="account-number financial-data">{{ maskedAccountNumber }}</span>
            <n-button size="tiny" text @click="showPaymentDetails">
              View Details
            </n-button>
          </div>
        </template>

        <!-- Confirmation Required Context -->
        <template v-if="context.type === 'confirmation_required'">
          <div class="confirmation-info">
            <span class="sender-name">{{ context.senderName }}</span>
            <span class="confirmation-text">sent</span>
            <span class="amount financial-data">{{ formatAmount(context.amount) }}</span>
            <span class="currency">{{ context.currency }}</span>
          </div>
          
          <div v-if="context.trackingNumber" class="tracking-preview">
            <span class="tracking-label">Tracking:</span>
            <span class="tracking-number financial-data">{{ context.trackingNumber }}</span>
          </div>
        </template>

        <!-- Waiting Context -->
        <template v-if="context.type === 'waiting'">
          <div class="waiting-info">
            <span class="waiting-text">Waiting for {{ context.waitingFor }}</span>
            <span class="action-text">to {{ context.actionNeeded }}</span>
          </div>
          
          <div class="timer-preview">
            <n-icon :component="ClockOutline" size="14" />
            <span class="timer-text" :class="getTimerClass(context.timeRemaining)">
              {{ formatTimeRemaining(context.timeRemaining) }} remaining
            </span>
          </div>
        </template>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <template v-if="context.type === 'payment_required'">
        <n-button 
          type="primary" 
          @click="$emit('action', 'declare_payment')"
          :loading="isLoading"
          class="primary-action"
        >
          <template #icon>
            <n-icon :component="CardOutline" />
          </template>
          I've Sent Payment
        </n-button>
        
        <n-button 
          text 
          @click="$emit('action', 'need_help')"
          class="secondary-action"
        >
          Need Help?
        </n-button>
      </template>

      <template v-if="context.type === 'confirmation_required'">
        <n-button 
          type="success" 
          @click="$emit('action', 'confirm_receipt')"
          :loading="isLoading"
          class="primary-action"
        >
          <template #icon>
            <n-icon :component="CheckmarkOutline" />
          </template>
          Confirm Receipt
        </n-button>
        
        <n-button 
          text 
          @click="$emit('action', 'report_issue')"
          class="secondary-action"
        >
          Report Issue
        </n-button>
      </template>

      <template v-if="context.type === 'waiting'">
        <n-button 
          text 
          @click="$emit('action', 'send_reminder')"
          :loading="isLoading"
          class="primary-action"
        >
          <template #icon>
            <n-icon :component="NotificationsOutline" />
          </template>
          Send Reminder
        </n-button>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { 
  ClockOutline, 
  CardOutline, 
  CheckmarkOutline, 
  NotificationsOutline,
  AlertCircleOutline,
  SendOutline
} from '@vicons/ionicons5';

interface ActionContext {
  type: 'payment_required' | 'confirmation_required' | 'waiting';
  amount?: number;
  currency?: string;
  recipient?: string;
  accountNumber?: string;
  senderName?: string;
  trackingNumber?: string;
  waitingFor?: string;
  actionNeeded?: string;
  timeRemaining?: number;
}

interface Props {
  context: ActionContext;
  isLoading?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  action: [actionType: string];
}>();

const shouldShow = computed(() => {
  return props.context && props.context.type;
});

const contextClass = computed(() => {
  return `context-${props.context?.type}`;
});

const maskedAccountNumber = computed(() => {
  if (!props.context?.accountNumber) return '';
  const account = props.context.accountNumber;
  return account.slice(0, 4) + '****' + account.slice(-4);
});

const formatAmount = (amount: number) => {
  return new Intl.NumberFormat().format(amount);
};

const formatTimeRemaining = (milliseconds: number) => {
  const minutes = Math.floor(milliseconds / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  }
  return `${minutes}m`;
};

const getContextIcon = () => {
  const iconMap = {
    'payment_required': CardOutline,
    'confirmation_required': CheckmarkOutline,
    'waiting': ClockOutline
  };
  return iconMap[props.context?.type] || AlertCircleOutline;
};

const getContextTitle = () => {
  const titleMap = {
    'payment_required': 'Payment Required',
    'confirmation_required': 'Confirmation Required',
    'waiting': 'Waiting'
  };
  return titleMap[props.context?.type] || 'Action Required';
};

const getContextTag = () => {
  const tagMap = {
    'payment_required': 'Your Turn',
    'confirmation_required': 'Your Turn',
    'waiting': 'Pending'
  };
  return tagMap[props.context?.type] || 'Active';
};

const getContextTagType = () => {
  const typeMap = {
    'payment_required': 'warning',
    'confirmation_required': 'success',
    'waiting': 'info'
  };
  return typeMap[props.context?.type] || 'default';
};

const getTimerClass = (timeRemaining: number) => {
  const minutes = timeRemaining / (1000 * 60);
  if (minutes <= 30) return 'timer-critical';
  if (minutes <= 120) return 'timer-warning';
  return 'timer-normal';
};

const showPaymentDetails = () => {
  emit('action', 'show_payment_details');
};
</script>

<style scoped>
.smart-action-bar {
  background: var(--bg-card);
  border-top: 2px solid var(--border-subtle);
  padding: 16px;
  position: sticky;
  bottom: 60px; /* Above chat input */
  z-index: 10;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.smart-action-bar.context-payment_required {
  border-top-color: var(--color-warning);
  background: linear-gradient(135deg, var(--warning-50) 0%, var(--bg-card) 100%);
}

.smart-action-bar.context-confirmation_required {
  border-top-color: var(--color-success);
  background: linear-gradient(135deg, var(--success-50) 0%, var(--bg-card) 100%);
}

.smart-action-bar.context-waiting {
  border-top-color: var(--color-primary);
  background: linear-gradient(135deg, var(--blue-50) 0%, var(--bg-card) 100%);
}

.action-context {
  margin-bottom: 16px;
}

.context-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.context-icon {
  color: var(--color-primary);
}

.context-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  flex: 1;
}

.context-details {
  margin-left: 24px; /* Align with content after icon */
}

.payment-info,
.confirmation-info,
.waiting-info {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.payment-text,
.confirmation-text,
.action-text {
  font-size: 14px;
  color: var(--text-secondary);
}

.amount {
  font-size: 16px;
  font-weight: 700;
  color: var(--color-primary);
}

.currency {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
}

.recipient,
.sender-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.account-preview,
.tracking-preview,
.timer-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-hover);
  border-radius: 6px;
  font-size: 13px;
}

.account-label,
.tracking-label {
  color: var(--text-secondary);
}

.account-number,
.tracking-number {
  flex: 1;
  font-weight: 600;
  color: var(--text-primary);
}

.timer-preview {
  gap: 6px;
}

.timer-text {
  font-weight: 600;
}

.timer-text.timer-normal { color: var(--color-primary); }
.timer-text.timer-warning { color: var(--color-warning); }
.timer-text.timer-critical { color: var(--color-error); }

.action-buttons {
  display: flex;
  gap: 12px;
}

.primary-action {
  flex: 2;
  min-height: 48px;
  font-weight: 600;
}

.secondary-action {
  flex: 1;
  min-height: 48px;
}

.financial-data {
  font-family: var(--font-mono);
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .smart-action-bar {
    padding: 12px;
    bottom: 55px; /* Adjust for mobile chat input */
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }
  
  .primary-action,
  .secondary-action {
    flex: 1;
    min-height: 44px;
  }
  
  .payment-info,
  .confirmation-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
```

## 🔄 Enhanced ChatView Integration

```vue
<template>
  <div class="enhanced-chat-view">
    <!-- Chat Header with Transaction Status -->
    <div class="chat-header">
      <div class="user-section">
        <n-avatar :src="otherUser?.avatar" size="medium" />
        <div class="user-info">
          <h3 class="user-name">{{ otherUser?.name }}</h3>
          <div class="connection-status">
            <div class="status-indicator" :class="{ online: isOnline }"></div>
            <span class="status-text">{{ isOnline ? 'Online' : 'Last seen 5m ago' }}</span>
          </div>
        </div>
      </div>
      
      <div class="transaction-status">
        <n-tag :type="transactionStatusType" size="small">
          {{ currentTransactionStatus }}
        </n-tag>
        <n-button size="small" text @click="showTransactionDetails">
          <template #icon>
            <n-icon :component="InformationCircleOutline" size="16" />
          </template>
        </n-button>
      </div>
    </div>

    <!-- Enhanced Messages Area -->
    <div class="messages-container" ref="messagesContainer">
      <div 
        v-for="message in enhancedMessages" 
        :key="message.id"
        class="message-wrapper"
      >
        <!-- Regular Chat Message -->
        <div 
          v-if="message.type === 'chat'"
          class="chat-message"
          :class="{ 'my-message': message.isMine }"
        >
          <div v-if="!message.isMine" class="message-avatar">
            <n-avatar :src="message.sender.avatar" size="small" />
          </div>
          
          <div class="message-content">
            <div class="message-bubble">
              <p class="message-text">{{ message.content }}</p>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>
          </div>
        </div>
        
        <!-- Smart System Message -->
        <SmartSystemMessage
          v-else
          :message="message"
          @action="handleSystemMessageAction"
        />
      </div>
    </div>

    <!-- Smart Action Bar (contextual) -->
    <SmartActionBar
      v-if="currentActionContext"
      :context="currentActionContext"
      :is-loading="isActionLoading"
      @action="handleActionBarAction"
    />

    <!-- Chat Input -->
    <div class="chat-input">
      <n-input
        v-model:value="messageText"
        :placeholder="$t('chat.typeMessage')"
        @keyup.enter="sendMessage"
        :disabled="isSending"
        class="message-input"
      >
        <template #suffix>
          <n-button 
            type="primary" 
            @click="sendMessage"
            :loading="isSending"
            :disabled="!messageText.trim()"
            size="small"
            class="send-button"
          >
            <template #icon>
              <n-icon :component="SendOutline" />
            </template>
          </n-button>
        </template>
      </n-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { 
  InformationCircleOutline, 
  SendOutline 
} from '@vicons/ionicons5';
import SmartSystemMessage from './SmartSystemMessage.vue';
import SmartActionBar from './SmartActionBar.vue';

// Component logic here...
const messageText = ref('');
const isSending = ref(false);
const isActionLoading = ref(false);

const enhancedMessages = computed(() => {
  // Merge chat messages with smart system messages
  // Sort by timestamp
  // Return unified message stream
});

const currentActionContext = computed(() => {
  // Determine current action context based on transaction state
  // Return appropriate context object for SmartActionBar
});

const handleSystemMessageAction = (action: any) => {
  // Handle actions from smart system messages
  console.log('System message action:', action);
};

const handleActionBarAction = (actionType: string) => {
  // Handle actions from smart action bar
  console.log('Action bar action:', actionType);
};
</script>

<style scoped>
.enhanced-chat-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-page);
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-subtle);
  box-shadow: var(--shadow-sm);
}

.user-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-error);
  transition: background 0.2s ease;
}

.status-indicator.online {
  background: var(--color-success);
}

.status-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.transaction-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.message-wrapper {
  margin-bottom: 12px;
}

.chat-message {
  display: flex;
  gap: 8px;
  padding: 0 16px;
}

.chat-message.my-message {
  flex-direction: row-reverse;
}

.chat-message.my-message .message-content {
  align-items: flex-end;
}

.message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 70%;
}

.message-bubble {
  background: var(--bg-elevated);
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
}

.my-message .message-bubble {
  background: var(--color-primary);
  color: white;
}

.message-text {
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
}

.chat-input {
  padding: 16px;
  background: var(--bg-card);
  border-top: 1px solid var(--border-subtle);
}

.message-input {
  border-radius: 24px;
}

.send-button {
  border-radius: 20px;
  margin: 2px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px 16px;
  }
  
  .user-name {
    font-size: 15px;
  }
  
  .messages-container {
    padding: 12px 0;
  }
  
  .chat-message {
    padding: 0 12px;
  }
  
  .message-content {
    max-width: 85%;
  }
}
</style>
```

This implementation provides a complete, production-ready solution for the innovative transaction-chat integration, with proper TypeScript support, accessibility features, and mobile-first responsive design.
