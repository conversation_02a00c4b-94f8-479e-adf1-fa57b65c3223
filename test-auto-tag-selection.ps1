#!/usr/bin/env pwsh

Write-Host "=== Testing Auto Tag Selection Debug ===" -ForegroundColor Green
Write-Host "This test will help verify the auto-selection debugging for AI suggested tags."
Write-Host ""

Write-Host "🚀 Starting frontend development server..." -ForegroundColor Blue
Set-Location "c:\Code\MUNygo\frontend"

Write-Host ""
Write-Host "📋 Test Instructions:" -ForegroundColor Yellow
Write-Host "1. Open browser and navigate to the app"
Write-Host "2. Open debug report button and start voice recording"
Write-Host "3. Say something like: 'I want to suggest a new feature for the offer management page'"
Write-Host "4. Wait for AI analysis to complete"
Write-Host "5. Click 'Apply to Form' and check browser console for debugging logs"
Write-Host "6. Check if predefined tags like 'enhancement', 'new-feature', 'user-experience' are auto-selected"
Write-Host ""
Write-Host "🔍 Look for these console logs:" -ForegroundColor Cyan
Write-Host "- Debug: AI suggested tags: [...]"
Write-Host "- Debug: All predefined tags: [...]"
Write-Host "- Debug: Adding predefined tag: ..."
Write-Host "- Debug: Form tags after applying: [...]"
Write-Host ""

# Start the development server
npm run dev
