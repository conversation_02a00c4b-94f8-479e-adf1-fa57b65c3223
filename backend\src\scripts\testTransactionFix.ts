import { PrismaClient, TransactionStatus } from '@prisma/client';
import { TransactionService } from '../services/transactionService';
import { NotificationService } from '../services/notificationService';
import { ChatService } from '../services/chatService';

const prisma = new PrismaClient();

async function testTransactionStatusFix() {
  console.log('Testing transaction status fix...');
    try {    // Create a mock transaction service with required dependencies
    const mockIo = {} as any; // Mock Socket.IO server
    const mockNotificationService = new NotificationService(mockIo); // Mock notification service
    const mockChatService = new ChatService(mockIo); // Mock chat service
    const transactionService = new TransactionService(mockIo, mockNotificationService, mockChatService);
    
    // Look for existing transactions to test with
    const existingTransactions = await prisma.transaction.findMany({
      where: {
        status: {
          in: [
            TransactionStatus.AWAITING_FIRST_PAYER_PAYMENT,
            TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION,
            TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION
          ]
        }
      },
      include: {
        currencyAProvider: true,
        currencyBProvider: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    });
    
    console.log(`Found ${existingTransactions.length} test transactions`);
    
    if (existingTransactions.length === 0) {
      console.log('No suitable transactions found for testing');
      return;
    }
    
    // Display the transactions and their current status
    for (const tx of existingTransactions) {
      console.log(`\nTransaction ${tx.id.substring(0, 8)}:`);
      console.log(`  Status: ${tx.status}`);
      console.log(`  First Payer: ${tx.agreedFirstPayerId}`);
      console.log(`  Currency A Provider: ${tx.currencyAProviderId} (${tx.currencyAProvider.username})`);
      console.log(`  Currency B Provider: ${tx.currencyBProviderId} (${tx.currencyBProvider.username})`);
      console.log(`  Payment declared by payer 1: ${tx.paymentDeclaredAtPayer1 ? 'Yes' : 'No'}`);
      console.log(`  Payment declared by payer 2: ${tx.paymentDeclaredAtPayer2 ? 'Yes' : 'No'}`);
      
      // Test the status logic
      if (tx.status === TransactionStatus.AWAITING_FIRST_PAYER_PAYMENT) {
        console.log(`  ✅ This transaction is correctly waiting for first payer payment`);
      } else if (tx.status === TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION) {
        console.log(`  ✅ This transaction is correctly waiting for second payer confirmation (first payment declared)`);
      } else if (tx.status === TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION) {
        console.log(`  ✅ This transaction is correctly waiting for first payer confirmation (second payment declared)`);
      }
    }
    
    console.log('\n✅ Transaction status logic appears correct based on current data');
    
  } catch (error) {
    console.error('Error testing transaction fix:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testTransactionStatusFix().catch(console.error);
