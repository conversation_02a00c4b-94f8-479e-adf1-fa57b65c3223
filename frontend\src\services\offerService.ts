import apiClient from './apiClient';
import { useOfferStore } from '@/stores/offerStore';
import { useClientLogger } from '@/composables/useClientLogger';
import type { Offer, CreateOfferPayload, BrowseOffer, OfferWithUser } from '@/types/offer';

const logger = useClientLogger();

export const offerService = {  async createOffer(payload: CreateOfferPayload): Promise<Offer> {
    logger.logInfo('Creating new offer', { 
      currencyPair: payload.currencyPair,
      amount: payload.amount,
    });
    
    try {
      const response = await apiClient.post<Offer>('/offers', payload);
      logger.logInfo('Offer created successfully', { offerId: response.data.id });
      return response.data;
    } catch (error) {
      logger.logError('Failed to create offer', error, { payload });
      throw error;
    }
  },

  async getMyOffers(): Promise<BrowseOffer[]> {
    logger.logInfo('Fetching user offers');
    
    try {
      // This endpoint should be updated to include interests in the backend for full support
      const response = await apiClient.get<BrowseOffer[]>('/offers/my?includeInterests=1');
      logger.logInfo('User offers fetched successfully', { count: response.data.length });
      return response.data;
    } catch (error) {
      logger.logError('Failed to fetch user offers', error);
      throw error;
    }
  },

  async getBrowseOffers(): Promise<BrowseOffer[]> {
    logger.logInfo('Fetching browse offers');
    
    try {
      const response = await apiClient.get<BrowseOffer[]>('/offers/browse');
      console.log('[offerService] getBrowseOffers raw response:', response.data);
      
      // Check for our target offer
      const targetOffer = response.data.find(offer => offer.id === 'cmb40jjag002dvly8m8r2gwku');
      if (targetOffer) {
        logger.logInfo('Target offer with completed transaction', { targetOffer });
      }
      
      logger.logInfo('Browse offers fetched successfully', { 
        count: response.data.length,
        hasTargetOffer: !!targetOffer,
      });
      
      return response.data;
    } catch (error) {
      logger.logError('Failed to fetch browse offers', error);
      throw error;
    }
  },

  async showInterest(offerId: string): Promise<void> {
    logger.logInfo('Showing interest in offer', { offerId });
    
    const offerStore = useOfferStore(); // Get store instance outside the try block
    try {
      await apiClient.post(`/offers/${offerId}/interest`);
      logger.logInfo('Interest shown successfully for offer', { offerId });
      // Interest successfully recorded for the first time
      offerStore.markInterestShown(offerId);
    } catch (error: any) {
      // Check if it's an Axios error and has a response
      if (error.isAxiosError && error.response) {
        if (error.response.status === 409) {
          // If 409 (Conflict), it means interest was already shown.
          // We should still update the local store to reflect this.
          logger.logWarn('Interest already shown for offer (409 Conflict)', { offerId });
          offerStore.markInterestShown(offerId);
          // Do not re-throw; consider this a "successful" outcome for UI state.
          return; // Exit successfully, interest is effectively "shown"
        }
      }
      // For other errors, log and re-throw to be caught by the component
      logger.logError('Error showing interest for offer', error, { offerId });
      throw error; // Re-throw other errors
    }
  },

  async cancelOffer(offerId: string): Promise<void> {
    logger.logInfo('Cancelling offer', { offerId });
    
    try {
      await apiClient.delete(`/offers/${offerId}`);
      const offerStore = useOfferStore();
      offerStore.removeOffer(offerId);
      logger.logInfo('Offer cancelled successfully', { offerId });
    } catch (error) {
      logger.logError('Failed to cancel offer', error, { offerId });
      throw error;
    }
  },  
  async getOfferById(offerId: string): Promise<OfferWithUser> {
    logger.logInfo('Fetching offer by ID', { offerId });
    
    try {
      const response = await apiClient.get<OfferWithUser>(`/offers/${offerId}`);
      logger.logInfo('Offer fetched successfully', { offerId });
      return response.data;
    } catch (error) {
      logger.logError('Failed to fetch offer by ID', error, { offerId });
      throw error;
    }
  },

  async expressInterest(offerId: string): Promise<void> {
    logger.logInfo('Expressing interest in offer', { offerId });
    
    try {
      await this.showInterest(offerId);
      logger.logInfo('Interest expressed successfully', { offerId });
    } catch (error) {
      logger.logError('Failed to express interest in offer', error, { offerId });
      throw error;
    }
  },

  async updateOfferStatus(offerId: string, status: string): Promise<void> {
    logger.logInfo('Updating offer status', { offerId, status });
    
    try {
      await apiClient.patch(`/offers/${offerId}/status`, { status });
      logger.logInfo('Offer status updated successfully', { offerId, status });
    } catch (error) {
      logger.logError('Failed to update offer status', error, { offerId, status });
      throw error;
    }
  },

  async updateOffer(offerId: string, payload: Partial<CreateOfferPayload>): Promise<Offer> {
    logger.logInfo('Updating offer', { offerId, payload });
    
    try {
      const response = await apiClient.put<Offer>(`/offers/${offerId}`, payload);
      logger.logInfo('Offer updated successfully', { offerId });
      return response.data;
    } catch (error) {
      logger.logError('Failed to update offer', error, { offerId, payload });
      throw error;
    }
  },

  async getBrowsableOfferById(offerId: string): Promise<BrowseOffer> {
    logger.logInfo('Fetching browsable offer by ID', { offerId });
    
    try {
      const response = await apiClient.get<BrowseOffer>(`/offers/browse/${offerId}`);
      logger.logInfo('Browsable offer fetched successfully', { offerId });
      return response.data;
    } catch (error) {
      logger.logError('Failed to fetch browsable offer by ID', error, { offerId });
      throw error;
    }
  },

  async deleteAllOffersForTest(): Promise<{ message: string, count?: number }> {
    logger.logInfo('Deleting all offers for test');
    
    try {
      const response = await apiClient.delete<{ message: string, count?: number }>('/offers/all-for-test');
      logger.logInfo('All test offers deleted', { count: response.data.count });
      return response.data;
    } catch (error) {
      logger.logError('Failed to delete all offers for test', error);
      throw error;
    }
  },
};

// Ensure OfferWithInterest type is defined if it was below, or adjust if it was part of the original snippet
// For example, if OfferWithInterest was defined like this:
// export interface OfferWithInterest extends Offer {
//   interests: any[]; // Define more specifically if possible
// }
// It should remain or be correctly placed.
