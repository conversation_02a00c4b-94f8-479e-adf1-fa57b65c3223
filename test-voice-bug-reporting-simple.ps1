#!/usr/bin/env pwsh

# Comprehensive Test Script for Enhanced Voice Bug Reporting System
Write-Host "=== MUNygo Voice Bug Reporting - Complete System Test ===" -ForegroundColor Cyan
Write-Host "Testing enhanced features:" -ForegroundColor Yellow
Write-Host "  - Report type field support" -ForegroundColor Green
Write-Host "  - Hybrid AI tag system (predefined + AI-suggested)" -ForegroundColor Green
Write-Host "  - MUNygo-specific app context in AI prompts" -ForegroundColor Green
Write-Host "  - Tag origin tracking and display" -ForegroundColor Green
Write-Host ""

# Step 1: Verify backend is running
Write-Host "Step 1: Checking backend server..." -ForegroundColor Blue
try {
    $backendResponse = Invoke-RestMethod -Uri "http://localhost:3000/health" -Method GET -TimeoutSec 5
    Write-Host "✓ Backend server is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Backend server not accessible. Please start with 'npm run dev' in backend/" -ForegroundColor Red
    exit 1
}

# Step 2: Verify frontend is running
Write-Host "Step 2: Checking frontend server..." -ForegroundColor Blue
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:5173" -Method GET -TimeoutSec 5
    Write-Host "✓ Frontend server is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Frontend server not accessible. Please start with 'npm run dev' in frontend/" -ForegroundColor Red
    exit 1
}

# Step 3: Test database schema
Write-Host "Step 3: Verifying database schema and tag origins..." -ForegroundColor Blue
try {
    node backend/verify-tag-origins.js
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Database schema and tag origins verified" -ForegroundColor Green
    } else {
        Write-Host "✗ Database schema verification failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Database verification error" -ForegroundColor Red
    exit 1
}

# Step 4: Test AI service
Write-Host "Step 4: Testing AI service with MUNygo context..." -ForegroundColor Blue

$testPayload = @{
    transcript = "The transaction flow is broken when I try to confirm payment. Timer keeps running even after confirmation."
    reportType = "bug"
    predefinedTags = @("transaction-flow", "payment-confirmation", "real-time-updates", "timer-issues")
} | ConvertTo-Json -Depth 3

try {
    $headers = @{ 'Content-Type' = 'application/json' }
    $aiResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/ai/analyze-report" -Method POST -Body $testPayload -Headers $headers -TimeoutSec 30
    
    Write-Host "✓ AI analysis completed successfully" -ForegroundColor Green
    Write-Host "  Report Type: $($aiResponse.reportType)" -ForegroundColor Cyan
    Write-Host "  Severity: $($aiResponse.severity)" -ForegroundColor Cyan
    Write-Host "  Tags:" -ForegroundColor Cyan
    
    $predefinedCount = 0
    $aiSuggestedCount = 0
    
    foreach ($tag in $aiResponse.tags) {
        $color = if ($tag.origin -eq "PREDEFINED") { "Green" } elseif ($tag.origin -eq "AI_SUGGESTED") { "Yellow" } else { "White" }
        Write-Host "    - $($tag.tag) ($($tag.origin))" -ForegroundColor $color
        
        if ($tag.origin -eq "PREDEFINED") { $predefinedCount++ }
        if ($tag.origin -eq "AI_SUGGESTED") { $aiSuggestedCount++ }
    }
    
    Write-Host "  Tag Summary: $predefinedCount predefined, $aiSuggestedCount AI-suggested" -ForegroundColor Cyan
    
} catch {
    Write-Host "✗ AI analysis failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 5: Test report submission
Write-Host "Step 5: Testing report submission..." -ForegroundColor Blue

$reportPayload = @{
    transcript = "Voice input: Transaction flow broken"
    reportType = "bug"
    severity = "medium"
    tags = @(
        @{ tag = "transaction-flow"; origin = "PREDEFINED" }
        @{ tag = "payment-confirmation"; origin = "PREDEFINED" }
        @{ tag = "ui-issue"; origin = "AI_SUGGESTED" }
        @{ tag = "user-reported"; origin = "USER_DEFINED" }
    )
    aiAnalysis = @{
        summary = "Transaction confirmation issue"
        category = "Frontend Bug"
    }
    userAgent = "Test-Script/1.0"
    url = "/transaction"
} | ConvertTo-Json -Depth 4

try {
    $reportResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/debug-reports" -Method POST -Body $reportPayload -Headers $headers -TimeoutSec 15
    Write-Host "✓ Report submitted successfully" -ForegroundColor Green
    Write-Host "  Report ID: $($reportResponse.id)" -ForegroundColor Cyan
    $reportId = $reportResponse.id
} catch {
    Write-Host "✗ Report submission failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 6: Verify report retrieval
Write-Host "Step 6: Verifying report retrieval..." -ForegroundColor Blue
try {
    Start-Sleep -Seconds 2
    $retrievedReport = Invoke-RestMethod -Uri "http://localhost:3000/api/debug-reports/$reportId" -Method GET -Headers $headers
    
    Write-Host "✓ Report retrieved successfully" -ForegroundColor Green
    Write-Host "  Type: $($retrievedReport.type)" -ForegroundColor Cyan
    Write-Host "  Tags with Origins:" -ForegroundColor Cyan
    
    foreach ($tag in $retrievedReport.tags) {
        $color = if ($tag.origin -eq "PREDEFINED") { "Green" } elseif ($tag.origin -eq "AI_SUGGESTED") { "Yellow" } else { "Blue" }
        Write-Host "    - $($tag.tag) [$($tag.origin)]" -ForegroundColor $color
    }
} catch {
    Write-Host "✗ Report retrieval failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Final summary
Write-Host ""
Write-Host "=== Test Summary ===" -ForegroundColor Cyan
Write-Host "✓ Backend server operational" -ForegroundColor Green
Write-Host "✓ Frontend server operational" -ForegroundColor Green  
Write-Host "✓ Database schema with tag origins verified" -ForegroundColor Green
Write-Host "✓ AI service with MUNygo context working" -ForegroundColor Green
Write-Host "✓ Report submission with tag origins successful" -ForegroundColor Green
Write-Host "✓ Report retrieval working correctly" -ForegroundColor Green
Write-Host ""
Write-Host "Enhanced Voice Bug Reporting System is READY!" -ForegroundColor Green
Write-Host ""
Write-Host "Manual UI verification needed:" -ForegroundColor Yellow
Write-Host "1. Test voice input at http://localhost:5173" -ForegroundColor White
Write-Host "2. Verify AI analysis with predefined tags" -ForegroundColor White
Write-Host "3. Check admin dashboard tag origin display" -ForegroundColor White
Write-Host "4. Submit test reports and verify admin views" -ForegroundColor White
