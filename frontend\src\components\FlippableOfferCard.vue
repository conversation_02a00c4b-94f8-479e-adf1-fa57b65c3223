<template>
  <div class="flippable-offer-card-wrapper">
    <div 
      class="flippable-card"
      :class="{ 'is-flipped': isFlipped }"
      :data-has-interests="offer.interests && offer.interests.length > 0"
    >
      <!-- Front Side: Offer Details -->
      <div class="card-side card-front">
        <div class="card-content">
          <!-- Header -->
          <div class="card-header">
            <div class="offer-identity">
              <n-tag 
                :type="offer.type === 'BUY' ? 'info' : 'success'" 
                size="small"
                round
                class="offer-type-tag"
              >
                {{ offer.type === 'BUY' ? t('app.buy') : t('app.sell') }}
              </n-tag>
              <h3 class="offer-amount">{{ formatAmount(offer.amount, 'CAD') }}</h3>
            </div>
            
            <div class="status-controls">
              <n-switch
                :value="offer.status === 'ACTIVE'"
                :loading="!!offer._statusLoading.value"
                :disabled="!!offer._statusLoading.value"
                @update:value="(checked) => $emit('statusToggle', offer, checked)"
                size="small"
                class="status-switch"
              >
                <template #checked>{{ t('status.active') }}</template>
                <template #unchecked>{{ t('offers.statusInactive') }}</template>
              </n-switch>
            </div>
          </div>

          <!-- Body -->
          <div class="card-body">
            <!-- Rate Information -->
            <div class="rate-section">
              <div class="base-rate-row">
                <span class="rate-label">{{ t('offers.baseRate') }}</span>
                <span class="rate-value">{{ formatAmountForDisplay(offer.baseRate, 'IRR', true) }}</span>
              </div>
              
              <!-- Tier Adjustments - HIDDEN for now as we only use one rate -->
              <!-- 
              <div v-if="userTier !== 1 && offer.adjustmentForLowerRep != null" class="adjustment-row">
                <span class="adjustment-label">{{ getLowerTierLabel() }} {{ t('offers.adjustment') }}</span>
                <span class="adjustment-value positive">+{{ offer.adjustmentForLowerRep.toFixed(2) }}%</span>
              </div>
              
              <div v-if="userTier !== 5 && offer.adjustmentForHigherRep != null" class="adjustment-row">
                <span class="adjustment-label">{{ getHigherTierLabel() }} {{ t('offers.adjustment') }}</span>
                <span class="adjustment-value negative">{{ offer.adjustmentForHigherRep.toFixed(2) }}%</span>
              </div>
              -->
            </div>

            <!-- Metadata -->
            <div class="metadata-section">
              <div class="created-info">
                <span class="meta-label">{{ t('offers.created') }}</span>
                <span class="meta-value">{{ formatDate(offer.createdAt) }}</span>
              </div>
              
              <div class="status-info">
                <n-tag 
                  :type="getOfferOverallStatus(offer, t)?.type || 'default'"
                  size="small"
                  round
                  class="overall-status-tag"
                  :style="getOfferOverallStatus(offer, t)?.text === t('status.negotiating') || getOfferOverallStatus(offer, t)?.text === t('status.inProgress') ? 
                    { animation: 'pulse 2s infinite' } : {}"
                >
                  {{ getOfferOverallStatus(offer, t)?.text || t('status.active') }}
                </n-tag>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="card-footer">
            <n-button 
              size="small" 
              @click="$emit('edit', offer.id)"
              class="edit-button"
            >
              <template #icon>
                <n-icon><EditOutlined /></n-icon>
              </template>
              {{ t('offers.edit') }}
            </n-button>
            
            <!-- Flip Button -->
            <div class="flip-controls">
              <div v-if="offer.interests && offer.interests.length > 0" class="has-interests">
                <n-badge :value="offer.interests.length" type="warning" :max="99">
                  <n-button 
                    size="small" 
                    type="primary"
                    @click="toggleFlip"
                    class="flip-button active"
                  >
                    <template #icon>
                      <n-icon><MessageOutlined /></n-icon>
                    </template>
                    {{ t('offers.viewInterests') }}
                  </n-button>
                </n-badge>
              </div>
              
              <div v-else-if="offer.status === 'ACTIVE'" class="awaiting-interests">
                <n-button 
                  size="small" 
                  quaternary
                  disabled
                  class="flip-button inactive"
                >
                  <template #icon>
                    <n-icon><MessageOutlined /></n-icon>
                  </template>
                  {{ t('offers.awaitingInterests') }}
                </n-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Back Side: Interest Management -->
      <div class="card-side card-back">
        <div class="card-content">
          <!-- Back Header -->
          <div class="back-header">
            <div class="offer-summary">
              <h4 class="back-title">{{ offer.type === 'BUY' ? t('app.buy') : t('app.sell') }} {{ formatAmount(offer.amount, 'CAD') }}</h4>
              <span class="back-rate">{{ t('offers.atRate') }} {{ formatAmountForDisplay(offer.baseRate, 'IRR', true) }}</span>
            </div>
            <n-button 
              size="small" 
              @click="toggleFlip"
              quaternary
              class="back-button"
            >
              <template #icon>
                <n-icon><ArrowLeftOutlined /></n-icon>
              </template>
              {{ t('offers.backToOffer') }}
            </n-button>
          </div>

          <!-- Interest Management with Carousel -->
          <div class="interests-container">
            <InterestCarousel
              v-if="offer.interests && offer.interests.length > 0"
              :interests="offer.interests"
              :processing-interests="processingInterests"
              @accept-interest="$emit('acceptInterest', $event)"
              @decline-interest="$emit('declineInterest', $event)"
              @go-to-chat="$emit('goToChat', $event)"
            />
            <div v-else class="no-interests">
              <p class="no-interests-message">{{ t('offers.noInterestsYet') }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { Ref } from 'vue';
import { useTranslation } from '@/composables/useTranslation';
// import { useAuthStore } from '@/stores/auth';
import type { MyOffer } from '@/types/offer';
import InterestCarousel from './InterestCarousel.vue';
import { 
  NButton, 
  NSwitch,
  NTag,
  NIcon,
  NBadge
} from 'naive-ui';
import { 
  MessageOutlined,
  EditOutlined,
  ArrowLeftOutlined
} from '@vicons/antd';
import { formatAmount, formatAmountForDisplay } from '@/utils/currencyUtils';
import { getOfferOverallStatus } from '@/utils/statusHelpers';

// Props
interface Props {
  offer: MyOffer & { _statusLoading: Ref<boolean> };
  processingInterests: Set<string>;
}

const props = defineProps<Props>();

// Emits - using $emit in template instead
// const emit = defineEmits<{
//   statusToggle: [offer: Props['offer'], checked: boolean];
//   edit: [offerId: string];
//   acceptInterest: [interestId: string];
//   declineInterest: [interestId: string];
//   goToChat: [chatSessionId: string];
// }>();

// Composables
const { t } = useTranslation();
// const authStore = useAuthStore();

// Local state
const isFlipped = ref(false);

// Methods
function toggleFlip() {
  if (props.offer.interests && props.offer.interests.length > 0) {
    isFlipped.value = !isFlipped.value;
  }
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString();
}
</script>

<style scoped>
/* Mobile-First Flippable Card Component */
.flippable-offer-card-wrapper {
  height: 380px; /* Reduced height for better mobile fit */
  perspective: 1000px;
  width: 100%;
}

/* Tablet and Desktop Enhancements */
@media (min-width: 768px) {
  .flippable-offer-card-wrapper {
    height: 400px; /* Slightly taller for tablet */
  }
}

@media (min-width: 1024px) {
  .flippable-offer-card-wrapper {
    height: 420px; /* Optimal height for desktop */
  }
  
  .card-content {
    padding: 2rem; /* More padding on desktop */
  }
  
  .offer-amount {
    font-size: 2rem; /* Larger amount text on desktop */
  }
  
  .rate-value {
    font-size: 1.1rem; /* Larger rate value on desktop */
  }
  
  .interests-list {
    max-height: 240px; /* Better scrollable height on desktop */
  }
  
  .interest-card {
    padding: 1.25rem; /* More padding for desktop */
    margin-bottom: 1rem;
  }
  
  .user-info-section {
    flex-wrap: nowrap; /* Don't wrap on desktop */
  }
  
  .action-buttons {
    flex-direction: row; /* Horizontal layout for desktop */
    gap: 0.75rem;
  }
}

@media (min-width: 1440px) {
  .flippable-offer-card-wrapper {
    height: 440px; /* Even more height for large screens */
  }
  
  .offer-amount {
    font-size: 2.25rem; /* Even larger on very large screens */
  }
  
  .card-content {
    padding: 2.5rem; /* Even more padding on very large screens */
  }
  
  .interests-list {
    max-height: 260px; /* More space for interests on large screens */
  }
}

.flippable-card {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
  cursor: pointer;
}

.flippable-card.is-flipped {
  transform: rotateY(180deg);
}

.card-side {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

[data-theme="dark"] .card-side {
  background: rgba(26, 27, 46, 0.95);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.card-back {
  transform: rotateY(180deg);
  background: linear-gradient(135deg, rgba(100, 108, 255, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
}

[data-theme="dark"] .card-back {
  background: linear-gradient(135deg, rgba(100, 108, 255, 0.08) 0%, rgba(26, 27, 46, 0.95) 100%);
}

.card-content {
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Front Side Styles */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .card-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.offer-identity {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.offer-type-tag {
  align-self: flex-start;
}

.offer-amount {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: #333;
}

[data-theme="dark"] .offer-amount {
  color: rgba(255, 255, 255, 0.9);
}

.status-controls {
  display: flex;
  align-items: center;
}

.card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.rate-section {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .rate-section {
  background: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.05);
}

.base-rate-row, .adjustment-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.base-rate-row {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

[data-theme="dark"] .base-rate-row {
  border-bottom-color: rgba(255, 255, 255, 0.05);
}

.rate-label, .adjustment-label {
  font-size: 0.875rem;
  color: #666;
}

[data-theme="dark"] .rate-label,
[data-theme="dark"] .adjustment-label {
  color: rgba(255, 255, 255, 0.7);
}

.rate-value {
  font-weight: 600;
  color: #333;
}

[data-theme="dark"] .rate-value {
  color: rgba(255, 255, 255, 0.9);
}

.adjustment-value {
  font-weight: 600;
  font-size: 0.875rem;
}

.adjustment-value.positive {
  color: #16a34a;
}

.adjustment-value.negative {
  color: #dc2626;
}

.metadata-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.created-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.meta-label {
  font-size: 0.75rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

[data-theme="dark"] .meta-label {
  color: rgba(255, 255, 255, 0.6);
}

.meta-value {
  font-size: 0.875rem;
  color: #333;
}

[data-theme="dark"] .meta-value {
  color: rgba(255, 255, 255, 0.8);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .card-footer {
  border-top-color: rgba(255, 255, 255, 0.1);
}

.flip-controls {
  display: flex;
  align-items: center;
}

.flip-button.active {
  animation: subtle-pulse 2s infinite;
}

.flip-button.inactive {
  opacity: 0.6;
}

/* Back Side Styles */
.back-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(100, 108, 255, 0.2);
}

.offer-summary {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.back-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: #333;
}

[data-theme="dark"] .back-title {
  color: rgba(255, 255, 255, 0.9);
}

.back-rate {
  font-size: 0.875rem;
  color: #646cff;
  font-weight: 500;
}

.interests-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.no-interests {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.no-interests-message {
  color: #666;
  font-style: italic;
  margin: 0;
}

[data-theme="dark"] .no-interests-message {
  color: rgba(255, 255, 255, 0.6);
}

/* Animations */
@keyframes subtle-pulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(100, 108, 255, 0.4);
  }
  50% { 
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(100, 108, 255, 0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive Design */
@media (min-width: 768px) {
  .flippable-offer-card-wrapper {
    height: 380px;
  }
  
  .card-content {
    padding: 2rem;
  }
  
  .offer-amount {
    font-size: 1.75rem;
  }
  
  .metadata-section {
    flex-direction: row;
    align-items: center;
  }
}

@media (min-width: 1024px) {
  .flippable-offer-card-wrapper {
    height: 360px;
  }
}

/* Visual Indicators for Offers with Interests */
.flippable-card[data-has-interests="true"] {
  border-left: 4px solid #646cff;
}

.flippable-card[data-has-interests="true"]::before {
  content: '●';
  position: absolute;
  top: 12px;
  right: 12px;
  color: #646cff;
  font-size: 10px;
  z-index: 10;
  animation: pulse 2s infinite;
}
</style>
