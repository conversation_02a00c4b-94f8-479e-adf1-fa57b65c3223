<template>
  <div class="tag-selector-v2" :class="{ compact }">
    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <n-spin size="small" />
      <span class="loading-text">{{ t('debug.tags.loading') }}</span>
    </div>    <!-- Error State -->
    <div v-else-if="error" class="error-state">
      <n-alert type="error" :show-icon="true" class="error-alert" :closable="false">
        {{ t('debug.tags.loadError') }}: {{ error }}
      </n-alert>
      <div class="error-actions">
        <n-button size="small" type="primary" @click="retryLoading">
          {{ t('common.retry') }}
        </n-button>
      </div>
    </div>

    <!-- Main Interface -->
    <div v-else class="tag-interface">      <!-- Header -->
      <div class="header">
        <div class="header-left">
          <h4 class="title">{{ t('debug.tags.selectTags') }}</h4>
        </div>
        <div v-if="hasSelectedTags" class="header-actions">
          <n-button 
            text 
            size="small" 
            type="error" 
            @click="clearAllSelections"
            :loading="isClearing"
          >
            {{ t('common.clearAll') }}
          </n-button>
        </div>
      </div>

      <!-- Selected Tags Display -->
      <div v-if="hasSelectedTags" class="selected-tags-section">
        <div class="selected-tags-header">
          <n-icon :component="CheckIcon" size="16" />
          <span class="selected-tags-title">{{ t('debug.tags.selectedTags') }}</span>
          <n-badge :value="totalSelectedCount" type="success" size="small" />
        </div>
        <div class="selected-tags-grid">
          <!-- Selected Predefined Tags -->
          <n-tag
            v-for="tagId in internalSelectedIds"
            :key="`selected-${tagId}`"
            type="success"
            size="medium"
            closable
            round
            @close="handleTagDeselect(tagId)"
            class="selected-tag"
          >
            <template #icon>
              <n-icon :component="CheckIcon" size="12" />
            </template>
            {{ getTagDisplayName(tagId) }}
          </n-tag>
          
          <!-- Selected Custom Tags -->
          <n-tag
            v-for="tag in customTags"
            :key="`selected-custom-${tag.id}`"
            type="success"
            size="medium"
            closable
            round
            @close="removeCustomTag(tag.id)"
            class="selected-tag custom-selected"
          >
            <template #icon>
              <n-icon :component="UserIcon" size="12" />
            </template>
            {{ tag.name }}
          </n-tag>
        </div>
      </div>

      <!-- AI Suggestions Section -->
      <div v-if="showAiSuggestions && aiSuggestions.length > 0" class="suggestions-section">
        <div class="section-header">
          <n-icon :component="LightbulbIcon" size="16" />
          <span class="section-title">{{ t('debug.tags.aiSuggestions') }}</span>
          <n-tag size="small" type="warning">{{ t('debug.tags.autoSuggested') }}</n-tag>
        </div>
        <div class="suggestions-grid">
          <n-tag
            v-for="suggestion in aiSuggestions"
            :key="`ai-${suggestion.id}`"
            :type="isTagSelected(suggestion.id) ? 'success' : 'warning'"
            :checkable="true"
            :checked="isTagSelected(suggestion.id)"
            size="medium"
            round
            @update:checked="(checked) => handleTagToggle(suggestion.id, checked, 'AI_SUGGESTED')"
            class="suggestion-tag"
          >
            <template #icon>
              <n-icon :component="LightbulbIcon" size="12" />
            </template>
            {{ suggestion.name }}
          </n-tag>
        </div>
      </div>

      <!-- Predefined Tags Section -->
      <div v-if="categorizedTags.length > 0" class="predefined-section">
        <div class="section-header">
          <n-icon :component="TagIcon" size="16" />
          <span class="section-title">{{ t('debug.tags.predefinedTags') }}</span>
          <n-badge 
            :value="visibleTagsCount" 
            type="info" 
            size="small"
          />
        </div>        <!-- Category Selection (responsive: dropdown on mobile, tabs on desktop) -->        <div v-if="categorizedTags.length > 1" class="category-selection">
          <!-- Mobile Dropdown -->
          <div class="mobile-category-selector">
            <n-select
              v-model:value="activeCategory"
              :options="categoryOptions"
              size="small"
              @update:value="handleCategoryChange"
            >
              <template #option="{ node, option }">
                <div class="category-option">
                  <span>{{ option.label }}</span>
                  <n-badge :value="option.tags" type="info" size="small" />
                </div>
              </template>
            </n-select>
          </div>
          
          <!-- Desktop Tabs -->
          <div class="desktop-category-tabs">
            <n-tabs
              type="segment"
              size="small"
              v-model:value="activeCategory"
              @update:value="handleCategoryChange"
            >
              <n-tab-pane
                v-for="category in categorizedTags"
                :key="category.name"
                :name="category.name"
                :tab="getCategoryDisplayName(category.name)"
              />
            </n-tabs>
          </div>
        </div>

        <!-- Tags Grid -->
        <div class="tags-grid">
          <n-tag
            v-for="tag in activeTagsToShow"
            :key="`predefined-${tag.id}`"
            :type="isTagSelected(tag.id) ? 'success' : 'default'"
            :checkable="true"
            :checked="isTagSelected(tag.id)"
            size="medium"
            round
            @update:checked="(checked) => handleTagToggle(tag.id, checked, 'PREDEFINED')"
            class="predefined-tag"
          >
            <template #icon v-if="isTagSelected(tag.id)">
              <n-icon :component="CheckIcon" size="12" />
            </template>
            {{ tag.name }}
          </n-tag>
        </div>
      </div>

      <!-- Custom Tags Section -->
      <div class="custom-section">
        <div class="section-header">
          <n-icon :component="PlusIcon" size="16" />
          <span class="section-title">{{ t('debug.tags.customTags') }}</span>
          <n-badge 
            v-if="customTags.length > 0"
            :value="customTags.length" 
            type="success" 
            size="small"
          />
        </div>

        <!-- Custom Tag Input -->
        <div class="custom-input-row">
          <n-input
            v-model:value="newCustomTagInput"
            :placeholder="t('debug.tags.addCustomTag')"
            size="small"
            :maxlength="maxCustomTagLength"
            @keydown.enter="addCustomTag"
            :disabled="!allowCustomTags || customTags.length >= maxCustomTags"
            class="custom-input"
          />
          <n-button
            type="primary"
            size="small"
            @click="addCustomTag"
            :disabled="!canAddCustomTag"
            :loading="isAddingCustomTag"
            class="add-button"
          >
            <template #icon>
              <n-icon :component="PlusIcon" />
            </template>
            {{ t('debug.tags.add') }}
          </n-button>
        </div>

        <!-- Custom Tags Display -->
        <div v-if="customTags.length > 0" class="custom-tags-grid">
          <n-tag
            v-for="tag in customTags"
            :key="`custom-${tag.id}`"
            type="success"
            size="medium"
            closable
            round
            @close="removeCustomTag(tag.id)"
            class="custom-tag"
          >
            <template #icon>
              <n-icon :component="UserIcon" size="12" />
            </template>
            {{ tag.name }}
          </n-tag>
        </div>

        <!-- Custom Tag Limits Info -->
        <div v-if="allowCustomTags && customTags.length > 0" class="limits-info">
          <n-text depth="3" class="limits-text">
            {{ t('debug.tags.customTagsCount', { 
              current: customTags.length, 
              max: maxCustomTags 
            }) }}
          </n-text>
        </div>
      </div>      <!-- Tag Limits Warning -->
      <div v-if="isApproachingLimit" class="limits-warning">
        <n-alert type="warning" :show-icon="true" size="small">
          {{ t('debug.tags.approachingLimit', { 
            current: totalSelectedCount, 
            max: maxTags 
          }) }}
        </n-alert>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { useMessage } from 'naive-ui';
import { useTagStore } from '@/stores/tagStore';
import type { TagWithRelations } from '@/types/api';
import type { ReportTagWithOrigin } from '@/types/logging';
import {
  Bulb as LightbulbIcon,
  Tag as TagIcon,
  Plus as PlusIcon,
  Check as CheckIcon,
  User as UserIcon
} from '@vicons/tabler';

// Props interface
interface Props {
  selectedTagIds?: string[];
  reportType?: string;
  debugReportData?: any;
  aiSuggestedTags?: ReportTagWithOrigin[];
  allowCustomTags?: boolean;
  enableAiSuggestions?: boolean;
  maxTags?: number;
  maxCustomTags?: number;
  maxCustomTagLength?: number;
  compact?: boolean;
}

// Props with defaults  
const props = withDefaults(defineProps<Props>(), {
  selectedTagIds: () => [],
  aiSuggestedTags: () => [],
  allowCustomTags: true,
  enableAiSuggestions: false, // Disabled by default since it's mock data
  maxTags: 10,
  maxCustomTags: 5,
  maxCustomTagLength: 30,
  compact: false
});

// Emits
interface Emits {
  'update:selectedTagIds': [value: string[]];
  'tagsChanged': [tags: { id: string; name: string; isCustom: boolean; origin: 'PREDEFINED' | 'AI_SUGGESTED' | 'USER_DEFINED' }[]];
  'customTagAdded': [tag: { id: string; name: string }];
}

const emit = defineEmits<Emits>();

// Composables
const { t } = useI18n();
const message = useMessage();
const tagStore = useTagStore();

// Local state
const internalSelectedIds = ref<string[]>([...props.selectedTagIds]);
const customTags = ref<{ id: string; name: string }[]>([]);
const newCustomTagInput = ref('');
const activeCategory = ref<string>('');
const isLoading = ref(false);
const error = ref<string | null>(null);
const isAddingCustomTag = ref(false);
const isClearing = ref(false);
const customTagCounter = ref(0); // For more stable custom tag IDs

// Mock AI suggestions for now (will be replaced with real AI integration)
const aiSuggestions = ref<{ id: string; name: string }[]>([]);

// Load predefined tags on mount
onMounted(async () => {
  await loadPredefinedTags();
  setupAiSuggestions();
});

// Load predefined tags from store
async function loadPredefinedTags() {
  isLoading.value = true;
  error.value = null;
  
  try {
    if (tagStore.tags.length === 0) {
      await tagStore.fetchTags();
    }
    
    // Set initial active category
    if (categorizedTags.value.length > 0) {
      activeCategory.value = categorizedTags.value[0].name;
    }
  } catch (err) {
    error.value = t('debug.tags.loadError');
    console.error('Failed to load predefined tags:', err);
  } finally {
    isLoading.value = false;
  }
}

// Setup AI suggestions (use real AI suggestions if provided, otherwise fall back to mock data)
function setupAiSuggestions() {
  if (!props.enableAiSuggestions) {
    aiSuggestions.value = [];
    return;
  }
    // Use real AI-suggested tags if provided
  if (props.aiSuggestedTags && props.aiSuggestedTags.length > 0) {
    // Wait for categorized tags to be loaded before filtering
    if (categorizedTags.value.length === 0) {
      // Tags not loaded yet, will be called again when they are loaded
      return;
    }
    
    // Get list of all predefined tag names for filtering
    const predefinedTagNames = new Set(
      categorizedTags.value.flatMap(category => 
        category.tags.map(tag => tag.name.toLowerCase())
      )
    );
    
    // Filter AI suggestions to only include tags that are NOT in predefined set
    const aiOnlyTags = props.aiSuggestedTags.filter(tag => {
      const tagName = tag.tagId || tag.tagName || '';
      return !predefinedTagNames.has(tagName.toLowerCase());
    });
    
    console.log('Debug: AI suggested tags (all):', props.aiSuggestedTags);
    console.log('Debug: Predefined tag names:', Array.from(predefinedTagNames));
    console.log('Debug: AI-only tags (filtered):', aiOnlyTags);
    
    // Create AI suggestions only for non-predefined tags
    aiSuggestions.value = aiOnlyTags.map((tag, index) => {
      const tagName = tag.tagId || tag.tagName || '';
      
      return {
        id: `ai-real-${index}`,
        name: tagName,
        origin: 'AI_SUGGESTED'
      };
    });
    
    // Auto-select AI-only tags when they are provided
    const aiTagIds = aiSuggestions.value.map(tag => tag.id);
    aiTagIds.forEach(tagId => {
      if (!internalSelectedIds.value.includes(tagId)) {
        internalSelectedIds.value.push(tagId);
      }
    });
    
    console.log('Debug: AI suggestions to show:', aiSuggestions.value);
    console.log('Debug: AI tag IDs selected:', aiTagIds);
    
    // Emit the changes to include the AI-only tags
    nextTick(() => {
      emitTagsChanged();
    });
    return;
  }  
  // No real AI suggestions provided, hide the AI suggestions section
  aiSuggestions.value = [];
}

// Retry loading
async function retryLoading() {
  await loadPredefinedTags();
}

// Computed properties
const categorizedTags = computed(() => {
  const grouped: { name: string; tags: TagWithRelations[] }[] = [];
  const tagsByCategory = tagStore.tagsByCategory;
  
  Object.keys(tagsByCategory).forEach(categoryName => {
    grouped.push({
      name: categoryName,
      tags: tagsByCategory[categoryName]
    });
  });
  
  return grouped;
});

const activeTagsToShow = computed(() => {
  if (categorizedTags.value.length <= 1) {
    // Show all tags if only one category or no categories
    return tagStore.activeTags;
  }
  
  const activeCateg = categorizedTags.value.find(cat => cat.name === activeCategory.value);
  return activeCateg ? activeCateg.tags : [];
});

const totalPredefinedCount = computed(() => tagStore.activeTags.length);

const visibleTagsCount = computed(() => {
  if (categorizedTags.value.length <= 1) {
    return tagStore.activeTags.length;
  }
  
  const activeCateg = categorizedTags.value.find(cat => cat.name === activeCategory.value);
  return activeCateg ? activeCateg.tags.length : 0;
});

const totalSelectedCount = computed(() => {
  return internalSelectedIds.value.length + customTags.value.length;
});

const hasSelectedTags = computed(() => totalSelectedCount.value > 0);

const showAiSuggestions = computed(() => 
  props.enableAiSuggestions && aiSuggestions.value.length > 0
);

const isApproachingLimit = computed(() => 
  props.maxTags && totalSelectedCount.value >= props.maxTags - 2
);

const canAddCustomTag = computed(() => {
  // Check if custom tags are allowed
  if (!props.allowCustomTags) return false;
  
  // Check if input has content
  if (newCustomTagInput.value.trim().length === 0) return false;
  
  // Check custom tag limit
  if (customTags.value.length >= props.maxCustomTags) return false;
  
  // Check total tag limit (including all selected tags: predefined, AI-suggested, and custom)
  if (totalSelectedCount.value >= props.maxTags) return false;
  
  // Check for duplicate custom tag names
  if (customTags.value.some(tag => 
    tag.name.toLowerCase() === newCustomTagInput.value.trim().toLowerCase()
  )) return false;
  
  return true;
});

const categoryOptions = computed(() => {
  return categorizedTags.value.map(category => ({
    label: getCategoryDisplayName(category.name),
    value: category.name,
    tags: category.tags.length
  }));
});

// Tag selection methods
function isTagSelected(tagId: string): boolean {
  if (tagId.startsWith('custom-')) {
    return customTags.value.some(tag => tag.id === tagId);
  }
  return internalSelectedIds.value.includes(tagId);
}

function getTagDisplayName(tagId: string): string {
  if (tagId.startsWith('ai-')) {
    const aiTag = aiSuggestions.value.find(t => t.id === tagId);
    return aiTag ? aiTag.name : tagId;
  } else {
    const tag = tagStore.activeTags.find(t => t.id === tagId);
    return tag ? tag.name : tagId;
  }
}

function handleTagDeselect(tagId: string) {
  const index = internalSelectedIds.value.indexOf(tagId);
  if (index > -1) {
    internalSelectedIds.value.splice(index, 1);
    emitTagsChanged();
  }
}

function handleTagToggle(tagId: string, checked: boolean, origin: 'PREDEFINED' | 'AI_SUGGESTED') {
  if (checked) {
    // Check tag limits
    if (totalSelectedCount.value >= props.maxTags) {
      message.warning(t('debug.tags.maxTagsReached', { max: props.maxTags }));
      return;
    }
    
    if (!internalSelectedIds.value.includes(tagId)) {
      internalSelectedIds.value.push(tagId);
    }
  } else {
    handleTagDeselect(tagId);
  }
  
  emitTagsChanged();
}

// Custom tag methods
async function addCustomTag() {
  if (!canAddCustomTag.value) return;
  
  isAddingCustomTag.value = true;
  
  try {
    const tagName = newCustomTagInput.value.trim();
    customTagCounter.value += 1;
    const customTagId = `custom-${customTagCounter.value}-${tagName.toLowerCase().replace(/\s+/g, '-')}`;
    
    const newTag = {
      id: customTagId,
      name: tagName
    };
    
    customTags.value.push(newTag);
    newCustomTagInput.value = '';
    
    emit('customTagAdded', newTag);
    emitTagsChanged();
    
    message.success(t('debug.tags.customTagAdded', { name: tagName }));
  } catch (err) {
    message.error(t('debug.tags.customTagAddError'));
    console.error('Failed to add custom tag:', err);
  } finally {
    isAddingCustomTag.value = false;
  }
}

function removeCustomTag(tagId: string) {
  const index = customTags.value.findIndex(tag => tag.id === tagId);
  if (index > -1) {
    const removedTag = customTags.value.splice(index, 1)[0];
    message.info(t('debug.tags.customTagRemoved', { name: removedTag.name }));
    emitTagsChanged();
  }
}

// Clear all selections
async function clearAllSelections() {
  isClearing.value = true;
  
  try {
    internalSelectedIds.value = [];
    customTags.value = [];
    
    emitTagsChanged();
    message.success(t('debug.tags.allTagsCleared'));
  } finally {
    isClearing.value = false;
  }
}

// Category handling
function handleCategoryChange(categoryName: string) {
  activeCategory.value = categoryName;
}

function getCategoryDisplayName(categoryName: string): string {
  // Handle internationalization for category names
  const categoryMap: Record<string, string> = {
    'General': t('debug.tags.categories.general'),
    'Technical': t('debug.tags.categories.technical'),
    'UI/UX': t('debug.tags.categories.uiux'),
    'Performance': t('debug.tags.categories.performance'),
    'Uncategorized': t('debug.tags.categories.uncategorized')
  };
  
  return categoryMap[categoryName] || categoryName;
}

// Emit changes to parent
function emitTagsChanged() {
  // Update v-model
  emit('update:selectedTagIds', [...internalSelectedIds.value]);
  
  // Build comprehensive tag data for parent
  const allSelectedTags: { 
    id: string; 
    name: string; 
    isCustom: boolean; 
    origin: 'PREDEFINED' | 'AI_SUGGESTED' | 'USER_DEFINED' 
  }[] = [];
  
  // Add predefined tags
  internalSelectedIds.value.forEach(tagId => {
    let tag: TagWithRelations | undefined;
    let origin: 'PREDEFINED' | 'AI_SUGGESTED' = 'PREDEFINED';
    
    if (tagId.startsWith('ai-')) {
      const aiTag = aiSuggestions.value.find(t => t.id === tagId);
      if (aiTag) {
        allSelectedTags.push({
          id: aiTag.id,
          name: aiTag.name,
          isCustom: false,
          origin: 'AI_SUGGESTED'
        });
      }
    } else {
      tag = tagStore.activeTags.find(t => t.id === tagId);
      if (tag) {
        allSelectedTags.push({
          id: tag.id,
          name: tag.name,
          isCustom: false,
          origin: 'PREDEFINED'
        });
      }
    }
  });
  
  // Add custom tags
  customTags.value.forEach(tag => {
    allSelectedTags.push({
      id: tag.id,
      name: tag.name,
      isCustom: true,
      origin: 'USER_DEFINED'
    });
  });
  
  emit('tagsChanged', allSelectedTags);
}

// Watch for external prop changes
watch(() => props.selectedTagIds, (newIds) => {
  internalSelectedIds.value = [...newIds];
}, { deep: true });

watch(() => props.reportType, () => {
  setupAiSuggestions();
});

watch(() => props.aiSuggestedTags, () => {
  setupAiSuggestions();
}, { deep: true });

// Watch for categorized tags to be loaded, then setup AI suggestions
watch(() => categorizedTags.value, () => {
  if (categorizedTags.value.length > 0) {
    setupAiSuggestions();
  }
}, { deep: true });
</script>

<style scoped>
.tag-selector-v2 {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tag-selector-v2.compact {
  gap: 0.75rem;
}

/* Loading and Error States */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  color: #6b7280;
}

.loading-text {
  font-size: 0.875rem;
}

.error-state {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.error-alert {
  font-size: 0.875rem;
}

.error-actions {
  display: flex;
  justify-content: center;
  margin-top: 0.5rem;
}

/* Main Interface */
.tag-interface {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

[data-theme="dark"] .header {
  border-bottom-color: #374151;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

[data-theme="dark"] .title {
  color: #f9fafb;
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Selected Tags Section */
.selected-tags-section {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

[data-theme="dark"] .selected-tags-section {
  background: #0c4a6e;
  border-color: #0284c7;
}

.selected-tags-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.selected-tags-title {
  font-size: 14px;
  font-weight: 500;
  color: #0369a1;
}

[data-theme="dark"] .selected-tags-title {
  color: #7dd3fc;
}

.selected-tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-tag {
  transition: all 0.2s ease;
}

.selected-tag:hover {
  transform: scale(1.02);
}

.custom-selected {
  background: #dcfce7;
  border-color: #16a34a;
}

[data-theme="dark"] .custom-selected {
  background: #14532d;
  border-color: #22c55e;
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

[data-theme="dark"] .section-title {
  color: #d1d5db;
}

/* AI Suggestions */
.suggestions-section {
  padding: 1rem;
  background-color: #fefce8;
  border-radius: 0.5rem;
  border: 1px solid #fde047;
  margin-bottom: 1rem;
}

[data-theme="dark"] .suggestions-section {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.suggestions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 0.75rem;
}

.suggestion-tag {
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;
}

.suggestion-tag:hover {
  transform: scale(1.05);
}

/* Predefined Tags */
.predefined-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Category Selection - Responsive */
.category-selection {
  margin-bottom: 1rem;
}

.mobile-category-selector {
  display: block;
}

.desktop-category-tabs {
  display: none;
}

@media (min-width: 768px) {
  .mobile-category-selector {
    display: none;
  }
  
  .desktop-category-tabs {
    display: block;
  }
}

.category-tabs {
  margin-bottom: 1rem;
}

.tags-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

@media (min-width: 640px) {
  .tags-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 768px) {
  .tags-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .tags-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

.predefined-tag {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.predefined-tag:hover {
  transform: scale(1.05);
}

/* Custom Tags */
.custom-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

[data-theme="dark"] .custom-section {
  background-color: rgba(31, 41, 55, 0.5);
  border-color: #374151;
}

.custom-input-row {
  display: flex;
  gap: 0.5rem;
}

.custom-input {
  flex: 1;
}

.add-button {
  flex-shrink: 0;
}

.custom-tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 0.75rem;
}

.custom-tag {
  transition: all 0.2s ease-in-out;
}

.custom-tag:hover {
  transform: scale(1.05);
}

.limits-info {
  margin-top: 0.5rem;
}

.limits-text {
  font-size: 0.75rem;
}

/* Limits Warning */
.limits-warning {
  margin-top: 1rem;
}

/* Summary Footer */
.summary-footer {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: #eff6ff;
  border-radius: 0.5rem;
  border: 1px solid #bfdbfe;
}

[data-theme="dark"] .summary-footer {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.summary-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.stat-label {
  color: #6b7280;
}

[data-theme="dark"] .stat-label {
  color: #9ca3af;
}

.stat-value {
  font-weight: 500;
  color: #111827;
}

[data-theme="dark"] .stat-value {
  color: #f9fafb;
}

.stat-value.total {
  color: #2563eb;
  font-weight: 600;
}

[data-theme="dark"] .stat-value.total {
  color: #60a5fa;
}

/* Responsive Design */
@media (max-width: 640px) {
  .tags-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .suggestions-grid {
    gap: 0.5rem;
  }
  
  .custom-tags-grid {
    gap: 0.5rem;
  }
  
  .selected-tags-grid {
    gap: 0.5rem;
  }
  
  .selected-tags-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .custom-input-row {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .add-button {
    width: 100%;
  }
  
  /* Mobile-specific category selection improvements */
  .mobile-category-selector .n-select {
    width: 100%;
  }
  
  /* Better mobile touch targets for tags */
  .predefined-tag,
  .suggestion-tag,
  .custom-tag,
  .selected-tag {
    min-height: 32px;
    font-size: 14px;
  }
  
  /* Improved mobile grid layout */
  .tags-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
}

/* Theme-specific adjustments */
[data-theme="dark"] .tag-selector-v2 {
  color-scheme: dark;
}

/* Category option styling */
.category-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
</style>
