{"reportIssue": "گزارش مشکل", "reportType": "نوع گزارش", "selectReportType": "انتخاب نوع گزارش", "orSelectFromCards": "یا از گزینه‌های زیر انتخاب کنید", "severityText": "شدت", "selectSeverity": "انتخاب سطح شدت", "tagsText": "برچسب‌ها", "tagsDescription": "برچسب‌های سفارشی برای دسته‌بندی این گزارش اضافه کنید", "title": "عنوان", "titlePlaceholder": "خلاصه کوتاه از مشکل", "description": "این سیستم، اطلاعات تشخیصی جامع از جمله لاگ‌ها، اقدامات کاربر و محتوای صفحه را جمع‌آوری کرده و برای تحلیل و بهبود برنامه ارسال می‌کند.", "descriptionPlaceholder": "توضیح تفصیلی از آنچه اتفاق افتاده", "stepsToReproduce": "مرا<PERSON><PERSON> تکرار", "stepsPlaceholder": "1. روی ... کلیک کنید\n2. به ... بروید\n3. نتیجه مورد انتظار...", "expectedBehavior": "رف<PERSON><PERSON><PERSON> مورد انتظار", "expectedPlaceholder": "آنچه انتظار داشتید اتفاق بیفتد", "actualBehavior": "رفتار واقعی", "actualPlaceholder": "آنچه واقعاً اتفاق افتاد", "additionalNotes": "یادداشت‌های اضافی", "additionalNotesPlaceholder": "هر اطلاعات اضافی که ممکن است مفید باشد", "contextInfo": "اطلاعات زمینه", "currentPage": "صفحه فعلی", "logEntries": "ورودی‌های لاگ", "userActions": "اقدامات کاربر", "viewport": "نمای صفحه", "recentActions": "اقدامات اخیر", "sendReport": "ارسال گزارش", "formValidationError": "لطفاً فیلدهای الزامی را پر کنید", "reportSentSuccess": "گزارش با موفقیت ارسال شد! شناسه گزارش: {reportId}", "reportSentError": "ارسال گزارش ناموفق بود. لطفاً دوباره تلاش کنید.", "tags": {"selectTags": "انتخاب برچسب‌ها", "selectTagsDescription": "برچسب‌هایی که بهترین توصیف از مشکل شما را ارائه می‌دهند انتخاب کنید تا به ما کمک کنید تا سریع‌تر آن را دسته‌بندی و حل کنیم", "loading": "بارگذاری برچسب‌ها...", "search": "جستجوی برچسب‌ها...", "searchPlaceholder": "جستجو برای برچسب‌ها...", "selectedTags": "برچسب‌های انتخاب شده", "recommended": "پیشنهادی", "browseAllTags": "مرور همه برچسب‌ها", "customTags": "برچسب‌های سفارشی", "addCustomTag": "برچ<PERSON><PERSON> خود را اضافه کنید...", "customTagHelp": "برچسب‌های سفارشی برای توصیف بهتر مشکل خاص خود اضافه کنید", "aiSuggestions": "پیشنهادات هوش مصنوعی", "autoSuggested": "پیشن<PERSON><PERSON> خودکار", "aiNotAvailable": "پیشنهادات هوش مصنوعی در دسترس نیست", "confidence": "اطمینان", "selected": "انتخاب شده", "manualSelection": "انتخاب دستی برچسب", "manualSelectionDesc": "برچسب‌هایی که بهترین توصیف از مشکل شما را ارائه می‌دهند از دسته‌بندی‌های زیر انتخاب کنید", "customHelp": "برچسب‌های سفارشی برای توصیف بهتر مشکل اضافه کنید", "clickHint": "نکته: روی برچسب‌های رنگی بالا کلیک کنید تا به سرعت آنها را به گزارش خود اضافه کنید", "clickToAdd": "برای افزودن این برچسب به گزارش خود کلیک کنید", "clickToRemove": "برای حذف این برچسب از گزار<PERSON> خود کلیک کنید", "label": "برچسب‌ها", "placeholder": "برچسب‌های سفارشی اضافه کنید", "addCustom": "افزودن برچسب سفارشی", "predefined": "از پیش تعریف شده", "predefinedTags": "برچسب‌های موجود", "selectedCount": "{count} انتخاب شده", "loadError": "خطا در بارگذاری برچسب‌ها", "maxTagsReached": "حداکثر {max} برچسب مجاز است", "customTagAdded": "برچسب سفارشی '{name}' اضافه شد", "customTagAddError": "خطا در افزودن برچسب سفارشی", "customTagRemoved": "برچسب '{name}' حذ<PERSON> شد", "allTagsCleared": "تمام برچسب‌ها پاک شدند", "customTagsCount": "{current} از {max} برچسب سفارشی", "approachingLimit": "نزدیک به حد مجاز برچسب: {current}/{max}", "add": "افزودن", "total": "کل", "custom": "سفار<PERSON>ی", "categories": {"general": "عمومی", "technical": "فنی", "uiux": "راب<PERSON> کاربری", "performance": "کارایی", "uncategorized": "دسته‌بندی نشده"}}, "titleText": "عنوان گزارش اشکال‌زدایی پیشرفته", "subtitle": "ارسال گزارش تشخیصی جامع برای بهبود برنامه", "reportTypes": {"bug": "مشکل (باگ)", "bugDescription": "چیزی کار نمی‌کند یا رفتار غیرعادی دارد", "feature": "ویژگی جدید", "featureDescription": "درخواست ویژگی یا قابلیت جدید", "performance": "مشکل کارایی", "performanceDescription": "برنامه کند است یا پاسخگو نیست", "uiux": "مشکل رابط کاربری", "uiuxDescription": "مشکل طراحی رابط یا قابلیت استفاده", "improvement": "<PERSON><PERSON><PERSON><PERSON><PERSON> عمومی", "improvementDescription": "پیشنهاد برای بهتر کردن چیزی", "question": "سؤال/کمک", "questionDescription": "نیاز به کمک یا توضیح", "other": "سایر", "otherDescription": "چیز دیگری که در بالا پوشش داده نشده", "idea": "ایده/پیشنهاد", "ui": "راب<PERSON> کاربری", "help": "کمک"}, "reportTypeDescriptions": {"bug": "چیزی کار نمی‌کند یا رفتار غیرعادی دارد", "bugDescription": "چیزی کار نمی‌کند یا رفتار غیرعادی دارد", "idea": "پیشنهاد برای بهبود یا ویژگی جدید", "ui": "مشکل با ظاهر، چیدمان یا تجربه کاربری", "uiux": "مشکل با ظاهر، چیدمان یا تجربه کاربری", "uiuxDescription": "مشکل با ظاهر، چیدمان یا تجربه کاربری", "performance": "صفحه کند است یا عملکرد ضعیفی دارد", "performanceDescription": "صفحه کند است یا عملکرد ضعیفی دارد", "feature": "درخواست ویژگی یا قابلیت جدید", "featureDescription": "درخواست ویژگی یا قابلیت جدید", "improvement": "پیشنهاد برای بهبود عملکرد یا تجربه", "improvementDescription": "پیشنهاد برای بهبود عملکرد یا تجربه", "question": "سردرگمی یا نیاز به راهنمایی", "questionDescription": "سردرگمی یا نیاز به راهنمایی", "other": "موارد دیگر که در بالا پوشش داده نشده", "otherDescription": "موارد دیگر که در بالا پوشش داده نشده", "help": "سردرگمی یا نیاز به راهنمایی", "helpDescription": "سردرگمی یا نیاز به راهنمایی"}, "severity": {"low": "کم", "medium": "متوسط", "high": "ز<PERSON>اد", "critical": "حیا<PERSON>ی"}, "form": {"title": "عنوان", "titlePlaceholder": "خلاصه کوتاه از مشکل یا پیشنهاد", "description": "توضیحات", "descriptionPlaceholder": "توضیح کامل از آنچه رخ داده یا پیشنهادتان", "stepsToReproduce": "مرا<PERSON><PERSON> تکرار", "stepsToReproducePlaceholder": "مراحل دقیق برای تکرار این مشکل", "expectedBehavior": "رف<PERSON><PERSON><PERSON> مورد انتظار", "expectedBehaviorPlaceholder": "آنچه انتظار داشتید اتفاق بیفتد", "actualBehavior": "رفتار واقعی", "actualBehaviorPlaceholder": "آنچه واقعاً اتفاق افتاد", "additionalNotes": "یادداشت‌های اضافی", "additionalNotesPlaceholder": "هر اطلاعات اضافی که ممکن است مفید باشد"}, "context": {"title": "اطلاعات محیط", "totalLogs": "کل لاگ‌ها", "userActions": "اقدامات کاربر", "currentPage": "صفحه فعلی", "viewport": "ابع<PERSON> صفحه", "browser": "مرورگر", "timestamp": "زمان", "sessionId": "شناسه جلسه", "route": "مسیر", "recentActions": "اقدامات اخیر", "correlatedData": "داده‌های مرتبط", "showDetails": "نمایش جزئیات", "hideDetails": "مخ<PERSON>ی کردن جزئیات"}, "actions": {"cancel": "لغو", "send": "ارسال گزارش", "sending": "در حال ارسال..."}, "messages": {"success": "گزارش شما با موفقیت ارسال شد! از مشارکت شما در بهبود برنامه سپاسگزاریم.", "error": "خطا در ارسال گزارش. لطفاً دوباره تلاش کنید.", "noLogsWarning": "هیچ لاگی برای ارسال موجود نیست.", "fillRequired": "لطفاً فیلدهای الزامی را پر کنید.", "titleRequired": "عنوان الزامی است", "descriptionRequired": "توضیحات الزامی است"}, "help": {"reportTypeHelp": "نوع گزارش مناسب را انتخاب کنید تا تیم ما بتواند بهتر به شما کمک کند.", "severityHelp": "سطح اهمیت کمک می‌کند ما اولویت‌بندی درستی داشته باشیم.", "tagsHelp": "برچسب‌ها به دسته‌بندی و جستجوی سریع‌تر گزارش‌ها کمک می‌کند.", "contextHelp": "این اطلاعات به طور خودکار جمع‌آوری شده و به ما کمک می‌کند مشکل را بهتر درک کنیم."}, "resetForm": "بازنشانی فرم", "resetFormConfirm": "آیا مطمئن هستید که می‌خواهید تمام داده‌های فرم را پاک کنید؟ این عمل قابل بازگشت نیست.", "resetFormSuccess": "فرم با موفقیت بازنشانی شد.", "offlineReportStored": "شما آفلا<PERSON>ن هستید. گزارش به صورت محلی ذخیره شد و هنگام برقراری اتصال ارسال خواهد شد.", "offlineReportsSubmitted": "{count} گزارش آفلاین با موفقیت ارسال شد.", "offlineReportsFailed": "{count} گزارش آفلاین پس از چندین تلاش ناموفق بود.", "draftSaved": "پیش‌نویس به طور خودکار ذخیره شد.", "draftSavedManually": "پیش‌نویس با موفقیت ذخیره شد.", "draftSaveError": "ذخیره پیش‌نویس ناموفق بود. تغییرات شما ممکن است از دست برود.", "storageQuotaExceeded": "فضای ذخیره‌سازی پر است. امکان ذخیره پیش‌نویس وجود ندارد. لطفاً داده‌های مرورگر را پاک کنید.", "storageSecurityError": "امکان ذخیره پیش‌نویس به دلیل محدودیت‌های امنیتی مرورگر وجود ندارد.", "draftLoaded": "پیش‌نویس قبلی بازیابی شد.", "draftCleared": "پیش‌نویس پاک شد.", "foundSavedDraft": "پیش‌نویس ذخیره شده یافت شد", "foundSavedDrafts": "پیش‌نویس‌های ذخیره شده یافت شدند", "continueOrStartFresh": "شما تغییرات ذخیره نشده از جلسه قبلی دارید. آیا می‌خواهید ادامه ویرایش دهید یا تازه شروع کنید؟", "continueEditing": "ادامه ویرایش", "hasDrafts": "شما {count} پیش‌نویس ذخیره شده دارید. آیا می‌خواهید آخرین مورد را بازیابی کنید؟", "multipleDraftsFound": "شما {count} پیش‌نویس ذخیره شده دارید. آیا می‌خواهید جدیدترین مورد را بازیابی کنید؟", "selectDraft": "انتخاب پیش‌نویس", "restoreDraft": "بازیابی پیش‌نویس", "restoreLatest": "بازیا<PERSON><PERSON> جدیدترین", "discardDraft": "شروع تازه", "startFresh": "شروع تازه", "autoSaveEnabled": "ذخیره خودکار فعال است. پیشرفت شما به طور خودکار ذخیره می‌شود.", "saveNow": "ذ<PERSON><PERSON><PERSON>ه کنید", "connectionRestored": "اتصال برقرار شد. در حال پردازش گزارش‌های آفلاین...", "offlineMode": "شما در حال حاضر آفلاین هستید. گزارش‌ها به صورت محلی ذخیره خواهند شد.", "offlineReportsCount": "{count} گزارش آفلاین در انتظار ارسال", "processingOfflineReports": "در حال پردازش گزارش‌های آفلاین..."}