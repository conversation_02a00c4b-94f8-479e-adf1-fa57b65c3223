const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkExistingMatches() {
  try {
    console.log('=== Checking for existing matches ===');
    
    // Get all offers
    const offers = await prisma.offer.findMany({
      where: { status: 'ACTIVE' },
      include: { user: { select: { username: true } } }
    });
    
    console.log('\n=== Active Offers ===');
    offers.forEach(offer => {
      console.log(`${offer.id}: ${offer.type} ${offer.amount} ${offer.currencyPair} @ ${offer.baseRate} (${offer.user.username})`);
    });
    
    // Get all matches
    const matches = await prisma.offerMatch.findMany({
      include: {
        offerA: { include: { user: { select: { username: true } } } },
        offerB: { include: { user: { select: { username: true } } } }
      },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log(`\n=== All Matches (${matches.length}) ===`);
    matches.forEach(match => {
      console.log(`${match.id}: ${match.offerA.user.username}(${match.offerA.type}@${match.offerA.baseRate}) <-> ${match.offerB.user.username}(${match.offerB.type}@${match.offerB.baseRate}) - Status: ${match.status}`);
    });
    
    // Check for specific offers that should match
    const buyOffers = offers.filter(o => o.type === 'BUY' && o.baseRate === 300);
    const sellOffers = offers.filter(o => o.type === 'SELL' && o.baseRate === 300);
    
    console.log(`\n=== Compatible Offers (rate 300) ===`);
    console.log('BUY offers:', buyOffers.map(o => `${o.user.username}(${o.id.slice(-8)})`));
    console.log('SELL offers:', sellOffers.map(o => `${o.user.username}(${o.id.slice(-8)})`));
    
    if (buyOffers.length > 0 && sellOffers.length > 0) {
      console.log('\n=== Checking for existing matches between compatible offers ===');
      for (const buyOffer of buyOffers) {
        for (const sellOffer of sellOffers) {
          if (buyOffer.userId !== sellOffer.userId) {
            const existingMatch = await prisma.offerMatch.findFirst({
              where: {
                OR: [
                  { offerAId: buyOffer.id, offerBId: sellOffer.id },
                  { offerAId: sellOffer.id, offerBId: buyOffer.id }
                ]
              }
            });
            
            if (existingMatch) {
              console.log(`Match exists: ${buyOffer.user.username} <-> ${sellOffer.user.username} - Status: ${existingMatch.status}`);
            } else {
              console.log(`No match found: ${buyOffer.user.username} <-> ${sellOffer.user.username} - Should create new match!`);
            }
          }
        }
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkExistingMatches();
