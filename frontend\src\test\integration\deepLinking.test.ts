import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, type VueWrapper } from '@vue/test-utils'
import type { ComponentPublicInstance } from 'vue'
import { nextTick } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import NotificationBell from '@/components/NotificationBell.vue'
import OfferDetailsView from '@/views/OfferDetailsView.vue'
import type { FrontendNotification } from '@/stores/notificationStore'
import { FrontendNotificationType } from '@/stores/notificationStore'
import type { OfferWithUser } from '@/types/offer'

// Mock the stores and services
const mockNotificationStore = {
  notifications: [] as FrontendNotification[],
  unreadCount: 0,
  markAsRead: vi.fn(),
  clearNotification: vi.fn()
}

const mockOfferService = {
  getOfferById: vi.fn(),
  expressInterest: vi.fn(),
  updateOfferStatus: vi.fn()
}

const mockAuthStore = {
  user: {
    id: 'user-123',
    username: 'testuser',
    reputationLevel: 3
  }
}

// Mock modules
vi.mock('@/stores/notificationStore', () => ({
  useNotificationStore: vi.fn(() => mockNotificationStore)
}))

vi.mock('@/services/offerService', () => mockOfferService)

vi.mock('@/stores/auth', () => ({
  useAuthStore: vi.fn(() => mockAuthStore)
}))

vi.mock('@/stores/interestStore', () => ({
  useInterestStore: vi.fn(() => ({
    expressInterest: vi.fn(),
    isExpressingInterest: false
  }))
}))

// Mock Vue I18n
const mockT = vi.fn((key: string) => key)
vi.mock('vue-i18n', async (importOriginal) => {
  const actual = await importOriginal() as any
  return {
    ...actual,
    useI18n: () => ({
      t: mockT
    })
  }
})

// Mock naive UI
const mockMessage = vi.fn()
vi.mock('naive-ui', () => ({
  useMessage: () => mockMessage,
  useDialog: () => ({
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  })
}))

// Mock icon components
vi.mock('@vicons/ionicons5', () => ({
  NotificationsOutline: { template: '<div>notification-icon</div>' }
}))

vi.mock('@/components/ReputationIcon.vue', () => ({
  default: { template: '<div data-testid="reputation-icon">Reputation</div>' }
}))

vi.mock('@/components/InterestRequestCard.vue', () => ({
  default: { template: '<div data-testid="interest-request-card">Interest Card</div>' }
}))

describe('Deep Linking Integration Tests', () => {
  let router: any
  let notificationWrapper: VueWrapper<ComponentPublicInstance>
  let offerDetailsWrapper: VueWrapper<ComponentPublicInstance>
  const createMockNotification = (overrides: Partial<FrontendNotification> = {}): FrontendNotification => ({
    id: 'test-notification-1',
    userId: 'user-123',
    type: FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER,
    message: 'Someone is interested in your offer',
    isRead: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    relatedEntityType: 'OFFER',
    relatedEntityId: 'offer-123',
    actorId: 'actor-123',
    actorUsername: 'testuser',
    data: {},
    ...overrides
  })
  const createMockOffer = (overrides: Partial<OfferWithUser> = {}): OfferWithUser => ({
    id: 'offer-123',
    type: 'SELL',
    amount: 1000,
    baseRate: 150000,
    adjustmentForLowerRep: 2,
    adjustmentForHigherRep: 1,
    status: 'ACTIVE',
    createdAt: new Date().toISOString(),
    currencyPair: 'USD-IRR',
    userId: 'owner-456',
    user: {
      username: 'offerowner',
      reputationLevel: 4
    },
    userInterest: null,
    isOwner: false,
    ...overrides
  })

  beforeEach(async () => {
    vi.clearAllMocks()
    mockT.mockImplementation((key: string) => key)
    
    // Create router with offer details route
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/offers/:id', component: OfferDetailsView, name: 'OfferDetails' },
        { path: '/my-offers', component: { template: '<div>My Offers</div>' } },
        { path: '/chat/:id', component: { template: '<div>Chat</div>' } },
        { path: '/transactions/:id', component: { template: '<div>Transaction</div>' } },
        { path: '/home', component: { template: '<div>Home</div>' } }
      ]
    })

    await router.push('/')
    await router.isReady()
  })

  afterEach(() => {
    notificationWrapper?.unmount()
    offerDetailsWrapper?.unmount()
  })

  describe('Notification Click → Offer Details Navigation', () => {
    it('should navigate from notification to offer details and load offer', async () => {
      // Setup notification with offer entity
      const notification = createMockNotification({
        relatedEntityType: 'OFFER',
        relatedEntityId: 'offer-123'
      })
      mockNotificationStore.notifications = [notification]
      
      // Setup offer response
      const mockOffer = createMockOffer()
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)

      // Mount notification bell component
      notificationWrapper = mount(NotificationBell, {
        global: {
          plugins: [router]
        }
      })

      // Click notification to trigger navigation
      const notificationItem = notificationWrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      // Verify navigation occurred
      expect(router.currentRoute.value.path).toBe('/offers/offer-123')
      expect(mockNotificationStore.markAsRead).toHaveBeenCalledWith('test-notification-1')

      // Mount offer details view at the new route
      await router.push('/offers/offer-123')
      offerDetailsWrapper = mount(OfferDetailsView, {
        global: {
          plugins: [router]
        }
      })

      await nextTick()

      // Verify offer was loaded
      expect(mockOfferService.getOfferById).toHaveBeenCalledWith('offer-123')
      
      // Verify offer details are displayed
      expect(offerDetailsWrapper.text()).toContain('offerDetails.title')
      expect(offerDetailsWrapper.text()).toContain('offerowner')
    })

    it('should handle legacy notification with data.offerId', async () => {      // Setup legacy notification
      const notification = createMockNotification({
        type: FrontendNotificationType.OFFER_STATUS_CHANGED,
        relatedEntityType: undefined,
        relatedEntityId: undefined,
        data: { offerId: 'legacy-offer-456' }
      })
      mockNotificationStore.notifications = [notification]
      
      const mockOffer = createMockOffer({ id: 'legacy-offer-456' })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)

      notificationWrapper = mount(NotificationBell, {
        global: {
          plugins: [router]
        }
      })

      const notificationItem = notificationWrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
        expect(router.currentRoute.value.path).toBe('/offers/legacy-offer-456')
      expect(mockNotificationStore.markAsRead).toHaveBeenCalledWith('test-notification-1')
    })

    it('should fallback to type-based navigation when entity info missing', async () => {
      const notification = createMockNotification({
        type: FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER,
        relatedEntityType: undefined,
        relatedEntityId: undefined,
        data: {}
      })
      mockNotificationStore.notifications = [notification]

      notificationWrapper = mount(NotificationBell, {
        global: {
          plugins: [router]
        }
      })

      const notificationItem = notificationWrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      expect(router.currentRoute.value.path).toBe('/my-offers')
    })
  })

  describe('Chat Session Deep Linking', () => {    it('should navigate to chat session from notification', async () => {
      const notification = createMockNotification({
        type: FrontendNotificationType.CHAT_MESSAGE_RECEIVED,
        relatedEntityType: 'CHAT_SESSION',
        relatedEntityId: 'chat-789'
      })
      mockNotificationStore.notifications = [notification]

      notificationWrapper = mount(NotificationBell, {
        global: {
          plugins: [router]
        }
      })

      const notificationItem = notificationWrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
        expect(router.currentRoute.value.path).toBe('/chat/chat-789')
      expect(mockNotificationStore.markAsRead).toHaveBeenCalledWith('test-notification-1')
    })

    it('should handle legacy chat notification with data.chatSessionId', async () => {
      const notification = createMockNotification({
        type: FrontendNotificationType.CHAT_MESSAGE_RECEIVED,
        relatedEntityType: undefined,
        relatedEntityId: undefined,
        data: { chatSessionId: 'legacy-chat-123' }
      })
      mockNotificationStore.notifications = [notification]

      notificationWrapper = mount(NotificationBell, {
        global: {
          plugins: [router]
        }
      })

      const notificationItem = notificationWrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      expect(router.currentRoute.value.path).toBe('/chat/legacy-chat-123')
    })
  })

  describe('Transaction Deep Linking', () => {    it('should navigate to transaction from notification', async () => {
      const notification = createMockNotification({
        type: FrontendNotificationType.TRANSACTION_STATUS_CHANGED,
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: 'txn-456'
      })
      mockNotificationStore.notifications = [notification]

      notificationWrapper = mount(NotificationBell, {
        global: {
          plugins: [router]
        }
      })

      const notificationItem = notificationWrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      expect(router.currentRoute.value.path).toBe('/transactions/txn-456')
      expect(mockNotificationStore.markAsRead).toHaveBeenCalledWith('test-notification-1')
    })
  })

  describe('Error Handling in Deep Linking Flow', () => {
    it('should handle offer not found gracefully', async () => {
      const notification = createMockNotification({
        relatedEntityType: 'OFFER',
        relatedEntityId: 'nonexistent-offer'
      })
      mockNotificationStore.notifications = [notification]
      
      // Mock 404 error
      mockOfferService.getOfferById.mockRejectedValue({
        response: { status: 404 }
      })

      notificationWrapper = mount(NotificationBell, {
        global: {
          plugins: [router]
        }
      })

      // Click notification
      const notificationItem = notificationWrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      // Navigation should still occur
      expect(router.currentRoute.value.path).toBe('/offers/nonexistent-offer')
      expect(mockNotificationStore.markAsRead).toHaveBeenCalledWith('test-notification-1')

      // Mount offer details view
      await router.push('/offers/nonexistent-offer')
      offerDetailsWrapper = mount(OfferDetailsView, {
        global: {
          plugins: [router]
        }
      })

      await nextTick()

      // Should show error message
      expect(offerDetailsWrapper.text()).toContain('errors.offerNotFound')
    })

    it('should handle navigation errors gracefully', async () => {
      const notification = createMockNotification()
      mockNotificationStore.notifications = [notification]

      // Mock router push failure
      const routerSpy = vi.spyOn(router, 'push')
      routerSpy.mockRejectedValueOnce(new Error('Navigation failed'))

      notificationWrapper = mount(NotificationBell, {
        global: {
          plugins: [router]
        }
      })

      const notificationItem = notificationWrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      // Should still mark as read
      expect(mockNotificationStore.markAsRead).toHaveBeenCalledWith('test-notification-1')
      // Should show error message
      expect(mockMessage).toHaveBeenCalledWith('notifications.navigationError', { type: 'error' })
    })
  })

  describe('Complex Notification Types', () => {
    const notificationTypes = [
      {
        type: FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER,
        expectedRoute: '/my-offers',
        description: 'interest received'
      },
      {
        type: FrontendNotificationType.YOUR_INTEREST_ACCEPTED,
        expectedRoute: '/home',
        description: 'interest accepted'
      },
      {
        type: FrontendNotificationType.YOUR_INTEREST_DECLINED,
        expectedRoute: '/home',
        description: 'interest declined'
      },
      {
        type: FrontendNotificationType.OFFER_STATUS_CHANGED,
        expectedRoute: '/home',
        description: 'offer updated'
      },
      {
        type: FrontendNotificationType.CHAT_MESSAGE_RECEIVED,
        expectedRoute: '/home',
        description: 'new message'
      },      {
        type: FrontendNotificationType.TRANSACTION_STATUS_CHANGED,
        expectedRoute: '/home',
        description: 'transaction status changed'
      }
    ]

    notificationTypes.forEach(({ type, expectedRoute, description }) => {
      it(`should handle ${description} notification fallback correctly`, async () => {
        const notification = createMockNotification({
          type: type as any,
          relatedEntityType: undefined,
          relatedEntityId: undefined,
          data: {}
        })
        mockNotificationStore.notifications = [notification]

        notificationWrapper = mount(NotificationBell, {
          global: {
            plugins: [router]
          }
        })

        const notificationItem = notificationWrapper.find('[data-testid="notification-item"]')
        await notificationItem.trigger('click')
        
        expect(router.currentRoute.value.path).toBe(expectedRoute)
        expect(mockNotificationStore.markAsRead).toHaveBeenCalledWith('test-notification-1')
      })
    })
  })

  describe('Multiple Notifications Deep Linking', () => {
    it('should handle clicking different notifications correctly', async () => {
      const notifications = [
        createMockNotification({
          id: 'notif-1',
          relatedEntityType: 'OFFER',
          relatedEntityId: 'offer-123'
        }),
        createMockNotification({
          id: 'notif-2',
          relatedEntityType: 'CHAT_SESSION',
          relatedEntityId: 'chat-456'
        }),
        createMockNotification({
          id: 'notif-3',
          relatedEntityType: 'TRANSACTION',
          relatedEntityId: 'txn-789'
        })
      ]
      mockNotificationStore.notifications = notifications

      notificationWrapper = mount(NotificationBell, {
        global: {
          plugins: [router]
        }
      })

      // Click first notification (offer)
      const firstNotification = notificationWrapper.findAll('[data-testid="notification-item"]')[0]
      await firstNotification.trigger('click')
      expect(router.currentRoute.value.path).toBe('/offers/offer-123')

      // Click second notification (chat)
      const secondNotification = notificationWrapper.findAll('[data-testid="notification-item"]')[1]
      await secondNotification.trigger('click')
      expect(router.currentRoute.value.path).toBe('/chat/chat-456')

      // Click third notification (transaction)
      const thirdNotification = notificationWrapper.findAll('[data-testid="notification-item"]')[2]
      await thirdNotification.trigger('click')
      expect(router.currentRoute.value.path).toBe('/transactions/txn-789')

      // Verify all notifications were marked as read
      expect(mockNotificationStore.markAsRead).toHaveBeenCalledTimes(3)
      expect(mockNotificationStore.markAsRead).toHaveBeenCalledWith('notif-1')
      expect(mockNotificationStore.markAsRead).toHaveBeenCalledWith('notif-2')
      expect(mockNotificationStore.markAsRead).toHaveBeenCalledWith('notif-3')
    })
  })
})
