import { PrismaClient, OfferType, OfferStatus, MatchStatus, MatchResponse, TransactionStatus } from '@prisma/client';
import { Server } from 'socket.io';
import {
  MATCH_FOUND,
  MATCH_ACCEPTED,
  MATCH_DECLINED,
  MATCH_EXPIRED,
  MATCH_CANCELLED,
  MatchFoundPayload,
  MatchAcceptedPayload,
  MatchDeclinedPayload,
  MatchExpiredPayload,
  MatchCancelledPayload
} from '../types/socketEvents';
import { NotificationService } from './notificationService';
import { NotificationType } from '@prisma/client';

export class MatchingService {
  constructor(
    private prisma: PrismaClient,
    private io: Server,
    private notificationService: NotificationService
  ) {}

  /**
   * Find potential matches for a given offer
   * MVP: Exact rate matching for cross-currency pairs
   */
  async findPotentialMatches(offerId: string) {
    console.log(`[MatchingService] Finding potential matches for offer: ${offerId}`);

    try {
      // 1. Get the offer details
      const offer = await this.prisma.offer.findUnique({
        where: { id: offerId },
        include: {
          user: { select: { id: true, username: true, email: true, reputationLevel: true } }
        }
      });

      if (!offer || offer.status !== OfferStatus.ACTIVE) {
        console.log(`[MatchingService] Offer ${offerId} not found or not active`);
        return [];
      }      console.log(`[MatchingService] Processing offer: ${JSON.stringify(offer)}`);

      // 2. Extract currency information from currencyPair (e.g., "CAD-IRR")
      // const [baseCurrency, quoteCurrency] = offer.currencyPair.split('-'); // Not strictly needed for the new logic but good for clarity
      
      // 3. Find matching offers with opposite direction and SAME currency pair
      const oppositeType = offer.type === OfferType.BUY ? OfferType.SELL : OfferType.BUY;
      const targetCurrencyPair = offer.currencyPair; // Match offers with the exact same currency pair

      console.log(`[MatchingService] Looking for ${oppositeType} offers with pair: ${targetCurrencyPair}`);      // 4. Query for potential matches
      const potentialMatches = await this.prisma.offer.findMany({
        where: {
          type: oppositeType,
          currencyPair: targetCurrencyPair, // Ensure the currency pair is the same
          status: OfferStatus.ACTIVE,
          userId: { not: offer.userId }, // Don't match with own offers
          // Only exclude offers in committed transactions (not just AWAITING_FIRST_PAYER_DESIGNATION)
          OR: [
            { transaction: null }, // No transaction at all
            { 
              transaction: { 
                status: TransactionStatus.AWAITING_FIRST_PAYER_DESIGNATION // Transaction exists but not committed yet
              } 
            }
          ],
          // Amount of base currency should match
          amount: offer.amount,
          // BaseRate (QuoteCurrency / BaseCurrency) should match
          baseRate: {
            gte: offer.baseRate * 0.99, // Allow 1% tolerance for floating point precision
            lte: offer.baseRate * 1.01
          }
        },
        include: {
          user: { select: { id: true, username: true, email: true, reputationLevel: true } }
        }
      });

      console.log(`[MatchingService] Found ${potentialMatches.length} potential matches`);      // 5. Create match records for each compatible offer
      const matches = [];
      for (const matchOffer of potentialMatches) {        // Check if match already exists and is still active (not declined/expired)
        const existingMatch = await this.prisma.offerMatch.findFirst({
          where: {
            OR: [
              { offerAId: offer.id, offerBId: matchOffer.id },
              { offerAId: matchOffer.id, offerBId: offer.id }
            ],
            // Only block if match is still active (can lead to transaction)
            status: { in: [MatchStatus.PENDING, MatchStatus.PARTIAL_ACCEPT, MatchStatus.BOTH_ACCEPTED] }
          }
        });

        // Check decline backoff
        const shouldBlock = await this.shouldBlockDueToDeclineHistory(offer.userId, matchOffer.userId);

        if (!existingMatch && !shouldBlock) {
          const match = await this.createMatch(offer, matchOffer);
          matches.push(match);
        } else if (existingMatch) {
          console.log(`[MatchingService] Match already exists for offers ${offer.id} and ${matchOffer.id}`);
        } else if (shouldBlock) {
          console.log(`[MatchingService] Blocking match due to recent decline between users ${offer.userId} and ${matchOffer.userId}`);
        }
      }

      return matches;
    } catch (error) {
      console.error(`[MatchingService] Error finding matches for offer ${offerId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new match between two compatible offers
   */
  async createMatch(offerA: any, offerB: any) {
    console.log(`[MatchingService] Creating match between offers ${offerA.id} and ${offerB.id}`);

    try {
      const matchId = this.generateMatchId();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // 24-hour expiration      // Extract currencies from currency pairs (e.g., "CAD-IRR" -> currencyA="CAD", currencyB="IRR")
      const [baseCurrency, quoteCurrency] = offerA.currencyPair.split('-');
      const currencyA = baseCurrency;
      const currencyB = quoteCurrency;

      // Calculate compatibility score (MVP: simple exact match score)
      const compatibilityScore = this.calculateCompatibilityScore(offerA, offerB);      // Check if match already exists to handle race conditions
      const existingMatch = await this.prisma.offerMatch.findFirst({
        where: {
          OR: [
            { offerAId: offerA.id, offerBId: offerB.id },
            { offerAId: offerB.id, offerBId: offerA.id }
          ]
        }
      });

      if (existingMatch) {
        console.log(`[MatchingService] Match already exists for offers ${offerA.id} and ${offerB.id}`);
        return existingMatch;
      }

      const match = await this.prisma.offerMatch.create({
        data: {
          matchId,
          offerAId: offerA.id,
          offerBId: offerB.id,
          userAId: offerA.userId,
          userBId: offerB.userId,
          compatibilityScore,
          currencyA,
          currencyB,
          amountA: offerA.amount,
          amountB: offerB.amount,
          rateAToB: offerA.baseRate,
          rateBToA: offerB.baseRate,
          expiresAt,
          matchCriteria: {
            type: 'exact_rate_match',
            tolerance: 0.01,
            timestamp: new Date().toISOString()
          }
        },
        include: {
          offerA: {
            include: { user: { select: { id: true, username: true, email: true } } }
          },
          offerB: {
            include: { user: { select: { id: true, username: true, email: true } } }
          },
          userA: { select: { id: true, username: true, email: true } },
          userB: { select: { id: true, username: true, email: true } }
        }
      });

      console.log(`[MatchingService] Match created: ${match.id}`);

      // Emit real-time notifications to both users
      await this.notifyUsersOfMatch(match);

      return match;
    } catch (error) {
      console.error(`[MatchingService] Error creating match:`, error);
      throw error;
    }
  }

  /**
   * Handle user accepting a match
   */
  async acceptMatch(matchId: string, userId: string) {
    console.log(`[MatchingService] User ${userId} accepting match ${matchId}`);

    try {
      const result = await this.prisma.$transaction(async (tx) => {
        // 1. Get the match with a lock to prevent race conditions
        const match = await tx.offerMatch.findUnique({
          where: { id: matchId },
          include: {
            offerA: { include: { user: true } },
            offerB: { include: { user: true } },
            userA: true,
            userB: true
          }
        });

        if (!match) {
          throw new Error('Match not found');
        }

        if (match.status !== MatchStatus.PENDING && match.status !== MatchStatus.PARTIAL_ACCEPT) {
          throw new Error('Match is no longer available');
        }

        if (match.userAId !== userId && match.userBId !== userId) {
          throw new Error('Unauthorized to accept this match');
        }

        if (match.expiresAt < new Date()) {
          throw new Error('Match has expired');
        }        // 2. Update user response
        const isUserA = match.userAId === userId;
        const updateData: any = {
          updatedAt: new Date()
        };

        if (isUserA) {
          if (match.userAResponse) {
            throw new Error('You have already responded to this match');
          }
          updateData.userAResponse = MatchResponse.ACCEPTED;
          updateData.userARespondedAt = new Date();
        } else {
          if (match.userBResponse) {
            throw new Error('You have already responded to this match');
          }
          updateData.userBResponse = MatchResponse.ACCEPTED;
          updateData.userBRespondedAt = new Date();
        }        // 3. Update the user's response first
        const updatedMatch = await tx.offerMatch.update({
          where: { id: matchId },
          data: updateData,
          include: {
            offerA: { include: { user: true } },
            offerB: { include: { user: true } },
            userA: true,
            userB: true
          }
        });

        // 4. Check the current state after the update to handle race conditions
        const currentUserAAccepted = updatedMatch.userAResponse === MatchResponse.ACCEPTED;
        const currentUserBAccepted = updatedMatch.userBResponse === MatchResponse.ACCEPTED;
          if (currentUserAAccepted && currentUserBAccepted) {
          // Both users have now accepted - update to BOTH_ACCEPTED and convert to transaction
          const finalMatch = await tx.offerMatch.update({
            where: { id: matchId },
            data: {
              status: MatchStatus.BOTH_ACCEPTED,
              acceptedByUserId: userId, // Track who completed the acceptance
              updatedAt: new Date()
            },
            include: {
              offerA: { include: { user: true } },
              offerB: { include: { user: true } },
              userA: true,
              userB: true
            }
          });

          // Create chat session and transaction
          const transaction = await this.convertMatchToTransaction(finalMatch, tx);
          
          // Auto-cancel competing matches when one is accepted
          await this.cancelCompetingMatches(finalMatch, tx);
          
          // Fetch the updated match with transaction and chat session IDs
          const updatedMatch = await tx.offerMatch.findUnique({
            where: { id: matchId },
            include: {
              offerA: { include: { user: true } },
              offerB: { include: { user: true } },
              userA: true,
              userB: true
            }
          });
          
          return { 
            match: updatedMatch, 
            status: 'both_accepted', 
            transaction,
            chatSessionId: updatedMatch?.chatSessionId 
          };
        } else {
          // Partial acceptance - waiting for other user
          const partialMatch = await tx.offerMatch.update({
            where: { id: matchId },
            data: {
              status: MatchStatus.PARTIAL_ACCEPT,
              updatedAt: new Date()
            },
            include: {
              offerA: { include: { user: true } },
              offerB: { include: { user: true } },
              userA: true,
              userB: true
            }
          });

          return { match: partialMatch, status: 'partial_accept' };
        }
      });      // Emit real-time events
      if (result.match) {
        console.log(`[MatchingService] About to emit match accepted event with:`, {
          matchId: result.match.id,
          status: result.status,
          chatSessionId: result.match.chatSessionId,
          transactionId: result.match.transactionId
        });
        await this.emitMatchAcceptedEvent(result.match, userId, result.status);
      }

      return result;
    } catch (error) {
      console.error(`[MatchingService] Error accepting match ${matchId}:`, error);
      throw error;
    }
  }

  /**
   * Handle user declining a match
   */
  async declineMatch(matchId: string, userId: string, reason?: string) {
    console.log(`[MatchingService] User ${userId} declining match ${matchId}`);

    try {
      const match = await this.prisma.offerMatch.findUnique({
        where: { id: matchId },
        include: {
          userA: true,
          userB: true
        }
      });

      if (!match) {
        throw new Error('Match not found');
      }

      if (match.userAId !== userId && match.userBId !== userId) {
        throw new Error('Unauthorized to decline this match');
      }

      if (match.status !== MatchStatus.PENDING && match.status !== MatchStatus.PARTIAL_ACCEPT) {
        throw new Error('Match is no longer available');
      }

      // Update match as declined
      const updatedMatch = await this.prisma.offerMatch.update({
        where: { id: matchId },
        data: {
          status: MatchStatus.DECLINED,
          declinedByUserId: userId,
          declineReason: reason,
          updatedAt: new Date()
        },
        include: {
          offerA: { include: { user: true } },
          offerB: { include: { user: true } },
          userA: true,
          userB: true
        }
      });

      // Emit real-time events
      await this.emitMatchDeclinedEvent(updatedMatch, userId, reason);

      return updatedMatch;
    } catch (error) {
      console.error(`[MatchingService] Error declining match ${matchId}:`, error);
      throw error;
    }
  }

  /**
   * Get matches for a user
   */
  async getUserMatches(userId: string, status?: MatchStatus) {
    try {
      const where: any = {
        OR: [
          { userAId: userId },
          { userBId: userId }
        ]
      };

      if (status) {
        where.status = status;
      }

      const matches = await this.prisma.offerMatch.findMany({
        where,
        include: {
          offerA: { include: { user: { select: { id: true, username: true, email: true, reputationLevel: true } } } },
          offerB: { include: { user: { select: { id: true, username: true, email: true, reputationLevel: true } } } },
          userA: { select: { id: true, username: true, email: true, reputationLevel: true } },
          userB: { select: { id: true, username: true, email: true, reputationLevel: true } }
        },
        orderBy: { createdAt: 'desc' }
      });

      return matches;
    } catch (error) {
      console.error(`[MatchingService] Error getting matches for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Clean up expired matches
   */
  async cleanupExpiredMatches() {
    console.log(`[MatchingService] Cleaning up expired matches`);

    try {
      const expiredMatches = await this.prisma.offerMatch.findMany({
        where: {
          status: { in: [MatchStatus.PENDING, MatchStatus.PARTIAL_ACCEPT] },
          expiresAt: { lt: new Date() }
        },
        include: {
          userA: true,
          userB: true
        }
      });

      for (const match of expiredMatches) {
        await this.prisma.offerMatch.update({
          where: { id: match.id },
          data: { status: MatchStatus.EXPIRED }
        });

        // Emit expiration events
        await this.emitMatchExpiredEvent(match);
      }

      console.log(`[MatchingService] Cleaned up ${expiredMatches.length} expired matches`);
      return expiredMatches.length;
    } catch (error) {
      console.error(`[MatchingService] Error cleaning up expired matches:`, error);
      throw error;
    }
  }

  // Private helper methods

  private generateMatchId(): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    
    return `MATCH_${year}${month}${day}_${random}`;
  }

  private calculateCompatibilityScore(offerA: any, offerB: any): number {
    // MVP: Simple exact match scoring
    // Future: More sophisticated scoring based on reputation, rates, timing, etc.
    
    let score = 0.8; // Base score for valid match
    
    // Rate compatibility (exact match gets higher score)
    const rateDiff = Math.abs(offerA.baseRate - offerB.baseRate) / offerA.baseRate;
    if (rateDiff < 0.001) score += 0.15; // Exact match
    else if (rateDiff < 0.005) score += 0.1; // Very close
    else if (rateDiff < 0.01) score += 0.05; // Close
    
    // Amount match (exact gets bonus)
    if (offerA.amount === offerB.amount) score += 0.05;
    
    return Math.min(score, 1.0);
  }
  private async notifyUsersOfMatch(match: any) {
    try {      // Create notifications for both users
      await this.notificationService.createNotification({
        userId: match.userAId,
        type: NotificationType.MATCH_FOUND,
        message: `New match found! ${match.userB.username || match.userB.email} wants to exchange ${match.currencyB} for ${match.currencyA}`,
        relatedEntityType: 'MATCH',
        relatedEntityId: match.id,
        actorId: match.userBId,
        actorUsername: match.userB.username || match.userB.email
      });

      await this.notificationService.createNotification({
        userId: match.userBId,
        type: NotificationType.MATCH_FOUND,
        message: `New match found! ${match.userA.username || match.userA.email} wants to exchange ${match.currencyA} for ${match.currencyB}`,
        relatedEntityType: 'MATCH',
        relatedEntityId: match.id,
        actorId: match.userAId,
        actorUsername: match.userA.username || match.userA.email
      });

      // Emit Socket.IO events
      const matchPayload: MatchFoundPayload = {
        matchId: match.id,
        match: {
          id: match.id,
          matchId: match.matchId,
          offerA: match.offerA,
          offerB: match.offerB,
          userA: match.userA,
          userB: match.userB,
          compatibilityScore: Number(match.compatibilityScore),
          expiresAt: match.expiresAt.toISOString()
        }
      };

      this.io.to(`user_${match.userAId}`).emit(MATCH_FOUND, matchPayload);
      this.io.to(`user_${match.userBId}`).emit(MATCH_FOUND, matchPayload);

      console.log(`[MatchingService] Notified users about new match: ${match.id}`);
    } catch (error) {
      console.error(`[MatchingService] Error notifying users of match:`, error);
    }
  }  private async emitMatchAcceptedEvent(match: any, acceptingUserId: string, status: string) {
    try {
      console.log(`[MatchingService] Emitting match accepted event with match data:`, {
        matchId: match.id,
        status: match.status,
        chatSessionId: match.chatSessionId,
        transactionId: match.transactionId,
        eventStatus: status
      });

      const payload: MatchAcceptedPayload = {
        matchId: match.id,
        acceptingUserId,
        status,
        match: {
          id: match.id,
          matchId: match.matchId,
          status: match.status,
          userAResponse: match.userAResponse,
          userBResponse: match.userBResponse,
          chatSessionId: match.chatSessionId,
          transactionId: match.transactionId
        }
      };

      console.log(`[MatchingService] Socket payload:`, JSON.stringify(payload, null, 2));

      // Get accepting user details
      const acceptingUser = match.userAId === acceptingUserId ? match.userA : match.userB;

      // Only send notification to the OTHER user, not the one who performed the action
      if (status === 'partial_accept') {
        // First acceptance - notify only the other user
        const otherUserId = match.userAId === acceptingUserId ? match.userBId : match.userAId;
        
        // Create notification for the other user
        await this.notificationService.createNotification({
          userId: otherUserId,
          type: NotificationType.MATCH_ACCEPTED_BY_OTHER,
          message: `${acceptingUser.username || acceptingUser.email} accepted your match!`,
          relatedEntityType: 'MATCH',
          relatedEntityId: match.id,
          actorId: acceptingUserId,
          actorUsername: acceptingUser.username || acceptingUser.email,
        });
        
        // Send socket event only to the other user
        this.io.to(`user_${otherUserId}`).emit(MATCH_ACCEPTED, payload);
        
        console.log(`[MatchingService] Emitted match accepted event to other user: ${otherUserId} for match: ${match.id}`);
      } else if (status === 'both_accepted') {
        // Both users accepted - notify both users that the match is complete and chat is ready
        await this.notificationService.createNotification({
          userId: match.userAId,
          type: NotificationType.MATCH_CONVERTED_TO_CHAT,
          message: `Match complete! Chat is ready with ${match.userB.username || match.userB.email}.`,
          relatedEntityType: 'MATCH',
          relatedEntityId: match.id,
          actorId: match.userBId,
          actorUsername: match.userB.username || match.userB.email,
        });

        await this.notificationService.createNotification({
          userId: match.userBId,
          type: NotificationType.MATCH_CONVERTED_TO_CHAT,
          message: `Match complete! Chat is ready with ${match.userA.username || match.userA.email}.`,
          relatedEntityType: 'MATCH',
          relatedEntityId: match.id,
          actorId: match.userAId,
          actorUsername: match.userA.username || match.userA.email,
        });

        // Send socket events to both users for mutual acceptance
        this.io.to(`user_${match.userAId}`).emit(MATCH_ACCEPTED, payload);
        this.io.to(`user_${match.userBId}`).emit(MATCH_ACCEPTED, payload);
        
        console.log(`[MatchingService] Emitted match complete event to both users for match: ${match.id}`);
      }
    } catch (error) {
      console.error(`[MatchingService] Error emitting match accepted event:`, error);
    }
  }
  private async emitMatchDeclinedEvent(match: any, decliningUserId: string, reason?: string) {
    try {
      const payload: MatchDeclinedPayload = {
        matchId: match.id,
        decliningUserId,
        reason,
        match: {
          id: match.id,
          matchId: match.matchId,
          status: match.status
        }
      };

      // Get declining user details
      const decliningUser = match.userAId === decliningUserId ? match.userA : match.userB;
      
      // Only send notification to the OTHER user, not the one who performed the action
      const otherUserId = match.userAId === decliningUserId ? match.userBId : match.userAId;
      
      // Create notification for the other user
      await this.notificationService.createNotification({
        userId: otherUserId,
        type: NotificationType.MATCH_DECLINED_BY_OTHER,
        message: `${decliningUser.username || decliningUser.email} declined your match${reason ? `: ${reason}` : '.'}`,
        relatedEntityType: 'MATCH',
        relatedEntityId: match.id,
        actorId: decliningUserId,
        actorUsername: decliningUser.username || decliningUser.email,
      });

      // Send socket event only to the other user
      this.io.to(`user_${otherUserId}`).emit(MATCH_DECLINED, payload);

      console.log(`[MatchingService] Emitted match declined event to other user: ${otherUserId} for match: ${match.id}`);
    } catch (error) {
      console.error(`[MatchingService] Error emitting match declined event:`, error);
    }
  }

  private async emitMatchExpiredEvent(match: any) {
    try {
      const payload: MatchExpiredPayload = {
        matchId: match.id,
        match: {
          id: match.id,
          matchId: match.matchId,
          status: MatchStatus.EXPIRED
        }
      };

      this.io.to(`user_${match.userAId}`).emit(MATCH_EXPIRED, payload);
      this.io.to(`user_${match.userBId}`).emit(MATCH_EXPIRED, payload);

      console.log(`[MatchingService] Emitted match expired event for match: ${match.id}`);
    } catch (error) {
      console.error(`[MatchingService] Error emitting match expired event:`, error);
    }
  }  private async convertMatchToTransaction(match: any, tx: any) {
    try {
      console.log(`[MatchingService] Converting match ${match.id} to transaction`);

      // Parse currency pair to extract base and quote currencies
      const [baseCurrency, quoteCurrency] = match.offerA.currencyPair.split('-');
      
      // Determine which user provides which currency based on offer types
      let currencyAProviderId: string;
      let currencyBProviderId: string;
      let currencyA: string;
      let currencyB: string;
      let amountA: number;
      let amountB: number;

      if (match.offerA.type === 'BUY') {
        // Offer A wants to buy base currency, so they provide quote currency
        currencyA = quoteCurrency; // IRR (what A provides)
        currencyB = baseCurrency;  // CAD (what B provides)
        currencyAProviderId = match.userAId;
        currencyBProviderId = match.userBId;
        amountA = match.amountA * match.offerA.baseRate; // IRR amount
        amountB = match.amountA; // CAD amount
      } else {
        // Offer A wants to sell base currency, so they provide base currency
        currencyA = baseCurrency;  // CAD (what A provides)
        currencyB = quoteCurrency; // IRR (what B provides)
        currencyAProviderId = match.userAId;
        currencyBProviderId = match.userBId;
        amountA = match.amountA; // CAD amount
        amountB = match.amountA * match.offerA.baseRate; // IRR amount
      }

      // Create a chat session first (required for transaction)
      const chatSession = await tx.chatSession.create({
        data: {
          offerId: match.offerAId, // Use the primary offer
          userOneId: match.userAId,
          userTwoId: match.userBId,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });      // Create a transaction with all required fields
      const transaction = await tx.transaction.create({
        data: {
          offerId: match.offerAId,
          chatSessionId: chatSession.id,
          currencyA,
          amountA,
          currencyAProviderId,          currencyB,
          amountB,
          currencyBProviderId,
          status: TransactionStatus.AWAITING_FIRST_PAYER_DESIGNATION,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });      // Update the match with transaction and chat session IDs and mark as converted
      await tx.offerMatch.update({
        where: { id: match.id },
        data: {          transactionId: transaction.id,
          chatSessionId: chatSession.id,
          status: MatchStatus.CONVERTED,
          updatedAt: new Date()
        }
      });

      // Note: Offers remain ACTIVE and available for matching until both users agree to terms
      // They will be marked as COMPLETED when transaction status changes from AWAITING_FIRST_PAYER_DESIGNATION

      console.log(`[MatchingService] Transaction ${transaction.id} and chat ${chatSession.id} created for match ${match.id}`);
      console.log(`[MatchingService] Offers ${match.offerAId} and ${match.offerBId} remain ACTIVE until both users agree to terms`);
      
      return transaction;
    } catch (error) {
      console.error(`[MatchingService] Error converting match to transaction:`, error);
      throw error;
    }
  }

  /**
   * Check if two users should be prevented from matching due to recent declines
   * Simple MVP version: 1 hour cooldown after decline
   */
  private async shouldBlockDueToDeclineHistory(userAId: string, userBId: string): Promise<boolean> {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const recentDecline = await this.prisma.offerMatch.findFirst({
      where: {
        OR: [
          { userAId: userAId, userBId: userBId },
          { userAId: userBId, userBId: userAId }
        ],
        status: MatchStatus.DECLINED,
        updatedAt: { gt: oneHourAgo }
      }
    });

    return !!recentDecline;
  }

  /**
   * Cancel competing matches when one match is accepted
   * This prevents users from having multiple active matches for the same offer
   */
  private async cancelCompetingMatches(acceptedMatch: any, tx: any) {
    console.log(`[MatchingService] Cancelling competing matches for accepted match ${acceptedMatch.matchId}`);

    try {
      // Find all other pending matches involving the same offers
      const competingMatches = await tx.offerMatch.findMany({
        where: {
          OR: [
            { offerAId: acceptedMatch.offerAId },
            { offerBId: acceptedMatch.offerAId },
            { offerAId: acceptedMatch.offerBId },
            { offerBId: acceptedMatch.offerBId }
          ],
          status: { in: [MatchStatus.PENDING, MatchStatus.PARTIAL_ACCEPT] },
          id: { not: acceptedMatch.id } // Don't cancel the accepted match itself
        }
      });

      if (competingMatches.length > 0) {
        console.log(`[MatchingService] Found ${competingMatches.length} competing matches to cancel`);        // Update all competing matches to CANCELLED
        await tx.offerMatch.updateMany({
          where: {
            id: { in: competingMatches.map((m: any) => m.id) }
          },
          data: {
            status: MatchStatus.CANCELLED,
            updatedAt: new Date()
          }
        });

        // Send notifications to affected users
        for (const competingMatch of competingMatches) {
          await this.notificationService.createNotification({
            userId: competingMatch.userAId,
            type: NotificationType.MATCH_EXPIRED,
            message: `Your match has been cancelled because the other user accepted a different match.`,
            relatedEntityType: 'OFFER_MATCH',
            relatedEntityId: competingMatch.id
          });

          await this.notificationService.createNotification({
            userId: competingMatch.userBId,
            type: NotificationType.MATCH_EXPIRED,
            message: `Your match has been cancelled because the other user accepted a different match.`,
            relatedEntityType: 'OFFER_MATCH',
            relatedEntityId: competingMatch.id
          });          // Emit socket events to update UI immediately
          this.io.to(competingMatch.userAId).emit(MATCH_CANCELLED, {
            matchId: competingMatch.matchId,
            reason: 'competing_match_accepted'
          });

          this.io.to(competingMatch.userBId).emit(MATCH_CANCELLED, {
            matchId: competingMatch.matchId,
            reason: 'competing_match_accepted'
          });
        }

        console.log(`[MatchingService] Cancelled ${competingMatches.length} competing matches and notified users`);
      }
    } catch (error) {
      console.error(`[MatchingService] Error cancelling competing matches:`, error);
      // Don't throw - this is cleanup, shouldn't fail the main transaction
    }
  }

  // Add method to trigger fresh matching (to be called after transaction cancellation)
  async triggerFreshMatchingForUser(userId: string) {
    console.log(`[MatchingService] Triggering fresh matching for user ${userId} after becoming available`);
    
    try {
      // Find all active offers by this user
      const userOffers = await this.prisma.offer.findMany({
        where: {
          userId: userId,
          status: OfferStatus.ACTIVE,
          OR: [
            { transaction: null },
            { 
              transaction: { 
                status: TransactionStatus.AWAITING_FIRST_PAYER_DESIGNATION 
              } 
            }
          ]
        }
      });

      console.log(`[MatchingService] Found ${userOffers.length} active offers for user ${userId}`);

      // Process each offer for fresh matching
      for (const offer of userOffers) {
        await this.processOfferForMatching(offer);
      }

      console.log(`[MatchingService] Completed fresh matching for user ${userId}`);
    } catch (error) {
      console.error(`[MatchingService] Error in fresh matching for user ${userId}:`, error);
    }
  }
}
