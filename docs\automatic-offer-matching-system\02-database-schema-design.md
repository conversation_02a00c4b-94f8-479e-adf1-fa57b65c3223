# Automatic Offer Matching System - Database Schema Design

## Document Overview
**Feature:** Automatic Offer Matching System  
**Version:** MVP (Minimum Viable Product)  
**Date:** December 2024  
**Status:** Foundation & Design Phase

## Table of Contents
1. [Schema Overview](#schema-overview)
2. [Existing Schema Analysis](#existing-schema-analysis)
3. [New Tables Design](#new-tables-design)
4. [Index Strategy](#index-strategy)
5. [Migration Plan](#migration-plan)
6. [Performance Considerations](#performance-considerations)
7. [Data Integrity](#data-integrity)

## Schema Overview

### Design Philosophy
- **Extend, Don't Break:** Build upon existing MUNygo schema without breaking changes
- **Performance First:** Optimize for sub-100ms match queries
- **Mobile Efficiency:** Minimize data transfer for mobile clients
- **Future-Ready:** Support future ML/AI enhancements

### Current Schema Integration
The automatic matching system extends the existing MUNygo database schema by:
- Adding new tables for match management
- Creating optimized indexes for matching queries
- Extending notification system for match events
- Maintaining referential integrity with existing offer system

## Existing Schema Analysis

### Current Core Tables (Relevant to Matching)
```sql
-- Existing tables that the matching system will integrate with

-- Users table (existing)
model User {
  id                 String   @id @default(cuid())
  email              String   @unique
  username           String?
  reputationScore    Int      @default(0)
  reputationLevel    Int      @default(1)
  phoneVerified      Boolean? @default(false)
  emailVerified      Boolean? @default(false)
  -- ... other fields
  offers             Offer[]
  interests          Interest[]
  notifications      Notification[]
}

-- Offers table (existing)
model Offer {
  id                     String      @id @default(cuid())
  userId                 String
  type                   OfferType   -- BUY or SELL
  currencyPair           String      @default("CAD-IRR")
  amount                 Float
  baseRate               Float
  adjustmentForLowerRep  Float
  adjustmentForHigherRep Float
  status                 OfferStatus @default(ACTIVE)
  -- ... other fields
  user                   User        @relation(fields: [userId], references: [id])
  interests              Interest[]
}

-- Notifications table (existing)
model Notification {
  id                 String           @id @default(uuid())
  userId             String
  type               NotificationType
  message            String
  isRead             Boolean          @default(false)
  relatedEntityType  String?
  relatedEntityId    String?
  -- ... other fields
}
```

### Current Offer Types and Currency Logic
```sql
-- Current enums
enum OfferType {
  BUY   -- User wants to buy currency A with currency B
  SELL  -- User wants to sell currency A for currency B
}

enum OfferStatus {
  ACTIVE
  INACTIVE
  DEACTIVATED
  COMPLETED
  CANCELLED
}
```

### Matching Logic Foundation
Based on existing schema, matching works as follows:
- **Currency Pair Format:** "CAD-IRR" (buy CAD, pay IRR)
- **Match Condition:** User A wants "CAD-IRR" and User B wants "IRR-CAD"
- **Cross-Currency Matching:** Buy currency A with B ↔ Buy currency B with A

## New Tables Design

### 1. Offer Matches Table
**Purpose:** Store detected matches between compatible offers

```sql
CREATE TABLE offer_matches (
  -- Primary identification
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  match_id VARCHAR(30) UNIQUE NOT NULL, -- Human-readable ID: MATCH_20241201_001
  
  -- Offer references
  offer_a_id VARCHAR(25) NOT NULL, -- References offers(id)
  offer_b_id VARCHAR(25) NOT NULL, -- References offers(id)
  
  -- User references (denormalized for performance)
  user_a_id VARCHAR(25) NOT NULL, -- References users(id)
  user_b_id VARCHAR(25) NOT NULL, -- References users(id)
  
  -- Match metadata
  compatibility_score DECIMAL(4,3) NOT NULL DEFAULT 0.000, -- 0.000-1.000
  match_criteria JSONB, -- Store matching criteria used
  
  -- Currency and amount information (denormalized)
  currency_a VARCHAR(3) NOT NULL, -- e.g., 'CAD'
  currency_b VARCHAR(3) NOT NULL, -- e.g., 'IRR' 
  amount_a DECIMAL(15,2) NOT NULL,
  amount_b DECIMAL(15,2) NOT NULL,
  rate_a_to_b DECIMAL(10,6) NOT NULL,
  rate_b_to_a DECIMAL(10,6) NOT NULL,
  
  -- Status and lifecycle
  status match_status_enum NOT NULL DEFAULT 'PENDING',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP NOT NULL, -- Auto-expiration
  
  -- User actions
  user_a_response match_response_enum DEFAULT NULL,
  user_a_responded_at TIMESTAMP DEFAULT NULL,
  user_b_response match_response_enum DEFAULT NULL,
  user_b_responded_at TIMESTAMP DEFAULT NULL,
    -- Result tracking
  chat_session_id VARCHAR(25) DEFAULT NULL, -- If accepted and chat created
  transaction_id VARCHAR(25) DEFAULT NULL,  -- If transaction initiated
  declined_by_user_id VARCHAR(25) DEFAULT NULL, -- Who declined first
  decline_reason VARCHAR(100) DEFAULT NULL,
  
  -- Race condition handling
  accepted_by_user_id VARCHAR(25) DEFAULT NULL, -- Track who accepted first in simultaneous cases
  processing_lock BOOLEAN DEFAULT FALSE, -- Prevent duplicate transaction creation
  
  -- Constraints
  CONSTRAINT fk_offer_matches_offer_a FOREIGN KEY (offer_a_id) REFERENCES offers(id) ON DELETE CASCADE,
  CONSTRAINT fk_offer_matches_offer_b FOREIGN KEY (offer_b_id) REFERENCES offers(id) ON DELETE CASCADE,
  CONSTRAINT fk_offer_matches_user_a FOREIGN KEY (user_a_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_offer_matches_user_b FOREIGN KEY (user_b_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_offer_matches_chat FOREIGN KEY (chat_session_id) REFERENCES chat_sessions(id) ON DELETE SET NULL,
  CONSTRAINT fk_offer_matches_transaction FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE SET NULL,
  
  -- Business logic constraints
  CONSTRAINT check_different_offers CHECK (offer_a_id != offer_b_id),
  CONSTRAINT check_different_users CHECK (user_a_id != user_b_id),
  CONSTRAINT check_compatibility_score CHECK (compatibility_score >= 0.000 AND compatibility_score <= 1.000),
  CONSTRAINT check_expire_after_create CHECK (expires_at > created_at)
);

-- Custom enums for match system
CREATE TYPE match_status_enum AS ENUM (
  'PENDING',        -- Initial state, awaiting user responses
  'BOTH_ACCEPTED',  -- Both users accepted the match → Creates ONE transaction
  'PARTIAL_ACCEPT', -- One user accepted, waiting for other
  'DECLINED',       -- At least one user declined
  'EXPIRED',        -- Match timed out
  'CONVERTED',      -- Successfully converted to chat/transaction
  'CANCELLED'       -- System cancelled (e.g., offer deactivated)
);

CREATE TYPE match_response_enum AS ENUM (
  'ACCEPTED',
  'DECLINED',
  'IGNORED'
);
```

### 2. Match Configuration Table
**Purpose:** Store matching algorithm parameters and feature flags

```sql
CREATE TABLE match_configuration (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  config_key VARCHAR(50) UNIQUE NOT NULL,
  config_value JSONB NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  -- Examples of config_key values:
  -- 'amount_tolerance_percent' -> {"value": 10.0}
  -- 'rate_compatibility_threshold' -> {"value": 0.05}
  -- 'max_matches_per_user' -> {"value": 5}
  -- 'match_expiration_hours' -> {"value": 24}
  -- 'enable_ml_scoring' -> {"value": false}
);

-- Insert default configuration
INSERT INTO match_configuration (config_key, config_value, description) VALUES
('amount_tolerance_percent', '{"value": 15.0}', 'Percentage tolerance for amount differences'),
('rate_compatibility_threshold', '{"value": 0.08}', 'Maximum rate difference for compatibility (8%)'),
('max_matches_per_user', '{"value": 10}', 'Maximum active matches per user'),
('match_expiration_hours', '{"value": 48}', 'Hours before match expires'),
('enable_advanced_scoring', '{"value": false}', 'Enable ML-based compatibility scoring'),
('notification_delay_seconds', '{"value": 5}', 'Delay before sending match notification'),
('min_reputation_for_matching', '{"value": 0}', 'Minimum reputation score for matching');
```

### 3. Match Analytics Table
**Purpose:** Track matching performance and user behavior

```sql
CREATE TABLE match_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type VARCHAR(30) NOT NULL,
  event_timestamp TIMESTAMP DEFAULT NOW(),
  
  -- Context
  user_id VARCHAR(25), -- References users(id)
  match_id UUID,       -- References offer_matches(id)
  offer_id VARCHAR(25), -- References offers(id)
  
  -- Event data
  event_data JSONB,
  
  -- Metrics
  response_time_ms INTEGER, -- For performance tracking
  compatibility_score DECIMAL(4,3),
  
  -- Constraints
  CONSTRAINT fk_match_analytics_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  CONSTRAINT fk_match_analytics_match FOREIGN KEY (match_id) REFERENCES offer_matches(id) ON DELETE SET NULL,
  CONSTRAINT fk_match_analytics_offer FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE SET NULL
);

-- Event types include:
-- 'MATCH_DETECTED', 'MATCH_NOTIFIED', 'MATCH_VIEWED', 'MATCH_ACCEPTED', 
-- 'MATCH_DECLINED', 'MATCH_EXPIRED', 'MATCH_CONVERTED'
```

### 4. Extended Notification Types
**Purpose:** Add match-related notification types to existing enum

```sql
-- Extend existing NotificationType enum
ALTER TYPE notification_type ADD VALUE 'MATCH_FOUND';
ALTER TYPE notification_type ADD VALUE 'MATCH_ACCEPTED_BY_OTHER';
ALTER TYPE notification_type ADD VALUE 'MATCH_DECLINED_BY_OTHER';
ALTER TYPE notification_type ADD VALUE 'MATCH_EXPIRED';
ALTER TYPE notification_type ADD VALUE 'MATCH_CONVERTED_TO_CHAT';
```

## Index Strategy

### Primary Performance Indexes
```sql
-- 1. User-based match queries (most common)
CREATE INDEX idx_offer_matches_user_a_status 
ON offer_matches(user_a_id, status) 
WHERE status IN ('PENDING', 'PARTIAL_ACCEPT');

CREATE INDEX idx_offer_matches_user_b_status 
ON offer_matches(user_b_id, status) 
WHERE status IN ('PENDING', 'PARTIAL_ACCEPT');

-- 2. Offer-based lookups
CREATE INDEX idx_offer_matches_offer_a_active 
ON offer_matches(offer_a_id) 
WHERE status NOT IN ('EXPIRED', 'CANCELLED', 'CONVERTED');

CREATE INDEX idx_offer_matches_offer_b_active 
ON offer_matches(offer_b_id) 
WHERE status NOT IN ('EXPIRED', 'CANCELLED', 'CONVERTED');

-- 3. Expiration cleanup
CREATE INDEX idx_offer_matches_expires_at 
ON offer_matches(expires_at) 
WHERE status IN ('PENDING', 'PARTIAL_ACCEPT');

-- 4. Match detection performance
CREATE INDEX idx_offers_matching_criteria 
ON offers(status, type, currency_pair) 
WHERE status = 'ACTIVE';

-- 5. Analytics queries
CREATE INDEX idx_match_analytics_event_timestamp 
ON match_analytics(event_type, event_timestamp DESC);

CREATE INDEX idx_match_analytics_user_events 
ON match_analytics(user_id, event_timestamp DESC);
```

### Composite Indexes for Complex Queries
```sql
-- User dashboard queries (get all user matches with details)
CREATE INDEX idx_offer_matches_user_composite 
ON offer_matches(user_a_id, status, created_at DESC, compatibility_score DESC);

CREATE INDEX idx_offer_matches_user_b_composite 
ON offer_matches(user_b_id, status, created_at DESC, compatibility_score DESC);

-- Currency-specific matching
CREATE INDEX idx_offer_matches_currency_pair 
ON offer_matches(currency_a, currency_b, status, created_at DESC);

-- Mutual acceptance tracking
CREATE INDEX idx_offer_matches_responses 
ON offer_matches(user_a_response, user_b_response, status);
```

## Migration Plan

### Phase 1: Core Tables Creation
```sql
-- 01_create_match_enums.sql
CREATE TYPE match_status_enum AS ENUM (...);
CREATE TYPE match_response_enum AS ENUM (...);

-- 02_create_offer_matches.sql
CREATE TABLE offer_matches (...);

-- 03_create_match_configuration.sql
CREATE TABLE match_configuration (...);
INSERT INTO match_configuration (...);

-- 04_create_match_analytics.sql
CREATE TABLE match_analytics (...);
```

### Phase 2: Indexes and Constraints
```sql
-- 05_create_primary_indexes.sql
CREATE INDEX idx_offer_matches_user_a_status ...;
CREATE INDEX idx_offer_matches_user_b_status ...;
-- ... other indexes

-- 06_add_foreign_keys.sql
ALTER TABLE offer_matches ADD CONSTRAINT fk_offer_matches_offer_a ...;
-- ... other foreign keys
```

### Phase 3: Notification System Extension
```sql
-- 07_extend_notification_types.sql
ALTER TYPE notification_type ADD VALUE 'MATCH_FOUND';
-- ... other notification types
```

### Phase 4: Data Migration and Validation
```sql
-- 08_validate_schema.sql
-- Validation queries to ensure schema integrity

-- 09_create_initial_config.sql
-- Insert production-ready configuration values
```

## Performance Considerations

### Query Performance Targets
- **User Match Lookup:** < 50ms for getting user's active matches
- **Match Detection:** < 100ms for finding matches for a new offer
- **Dashboard Load:** < 200ms for complete match dashboard
- **Bulk Operations:** < 5s for processing 1000+ offers

### Database Optimization Strategies

#### 1. Denormalization for Performance
```sql
-- Store frequently accessed data in offer_matches table
-- Avoid JOIN queries for common operations
SELECT 
  match_id,
  currency_a, currency_b,  -- Denormalized from offers
  amount_a, amount_b,      -- Denormalized from offers
  compatibility_score,
  status,
  expires_at
FROM offer_matches 
WHERE user_a_id = ? OR user_b_id = ?
  AND status IN ('PENDING', 'PARTIAL_ACCEPT');
```

#### 2. Partial Indexes for Active Data
```sql
-- Only index active/relevant matches
CREATE INDEX idx_active_matches ON offer_matches(user_a_id, created_at DESC) 
WHERE status IN ('PENDING', 'PARTIAL_ACCEPT');
```

#### 3. Materialized Views for Analytics
```sql
-- Pre-computed match statistics
CREATE MATERIALIZED VIEW match_stats_daily AS
SELECT 
  DATE(created_at) as match_date,
  COUNT(*) as total_matches,
  AVG(compatibility_score) as avg_compatibility,
  COUNT(*) FILTER (WHERE status = 'CONVERTED') as successful_matches
FROM offer_matches 
GROUP BY DATE(created_at);

-- Refresh daily via cron job
```

### Mobile-Specific Optimizations

#### 1. Paginated Results
```sql
-- Limit data transfer for mobile clients
SELECT * FROM offer_matches 
WHERE user_a_id = ? 
ORDER BY created_at DESC 
LIMIT 20 OFFSET ?;
```

#### 2. Essential Data Only
```sql
-- Mobile-optimized query returning minimal data
SELECT 
  match_id,
  currency_a || '-' || currency_b as currency_pair,
  amount_a,
  compatibility_score,
  expires_at,
  status
FROM offer_matches 
WHERE (user_a_id = ? OR user_b_id = ?)
  AND status = 'PENDING'
ORDER BY compatibility_score DESC
LIMIT 10;
```

## Data Integrity

### Referential Integrity Rules
1. **Cascade Deletes:** When offer is deleted, remove related matches
2. **Null Constraints:** Prevent orphaned matches when optional references are deleted
3. **Business Logic Constraints:** Enforce matching rules at database level

### Data Validation Constraints
```sql
-- Ensure match logic consistency
ALTER TABLE offer_matches ADD CONSTRAINT check_currency_logic 
CHECK (
  (currency_a != currency_b) AND
  (LENGTH(currency_a) = 3) AND
  (LENGTH(currency_b) = 3)
);

-- Ensure amounts are positive
ALTER TABLE offer_matches ADD CONSTRAINT check_positive_amounts 
CHECK (amount_a > 0 AND amount_b > 0);

-- Ensure rates are reasonable (prevent obvious errors)
ALTER TABLE offer_matches ADD CONSTRAINT check_reasonable_rates 
CHECK (rate_a_to_b > 0 AND rate_b_to_a > 0);
```

### Data Consistency Maintenance
```sql
-- Stored procedure for cleaning expired matches
CREATE OR REPLACE FUNCTION cleanup_expired_matches()
RETURNS INTEGER AS $$
DECLARE
  cleaned_count INTEGER;
BEGIN
  UPDATE offer_matches 
  SET status = 'EXPIRED', updated_at = NOW()
  WHERE status IN ('PENDING', 'PARTIAL_ACCEPT')
    AND expires_at < NOW();
  
  GET DIAGNOSTICS cleaned_count = ROW_COUNT;
  
  -- Log cleanup activity
  INSERT INTO match_analytics (event_type, event_data)
  VALUES ('CLEANUP_EXPIRED', json_build_object('cleaned_count', cleaned_count));
  
  RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql;

-- Schedule via cron: run every hour
-- 0 * * * * psql -d munygo -c "SELECT cleanup_expired_matches();"
```

### Backup and Recovery Strategy
- **Point-in-time Recovery:** Maintain WAL logs for match data recovery
- **Incremental Backups:** Daily backups of match tables
- **Cross-Region Replication:** For production high availability
- **Data Retention:** Archive old matches after 90 days

---

## Implementation Notes

### Development Best Practices
1. **Migration Testing:** Test all migrations on copy of production data
2. **Performance Monitoring:** Monitor query performance in development
3. **Data Validation:** Validate data integrity after each migration step
4. **Rollback Strategy:** Prepare rollback scripts for each migration

### Production Deployment
1. **Staged Rollout:** Deploy schema changes during low-traffic periods
2. **Index Creation:** Create indexes CONCURRENTLY to avoid table locks
3. **Performance Baseline:** Establish performance metrics before deployment
4. **Monitoring Setup:** Configure alerts for query performance degradation

This database schema design provides a robust foundation for the automatic offer matching system while maintaining optimal performance for mobile users and preparing for future enhancements.
