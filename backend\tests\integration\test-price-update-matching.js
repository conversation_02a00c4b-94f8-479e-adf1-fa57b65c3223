const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testPriceUpdateMatching() {
  console.log('=== TESTING PRICE UPDATE MATCHING ===\n');
  
  try {
    // 1. Check current state - look for offers that could potentially match if prices were adjusted
    const offers = await prisma.offer.findMany({
      where: { status: 'ACTIVE' },
      include: { user: { select: { username: true } } },
      orderBy: { type: 'asc' }
    });
    
    console.log(`Current active offers (${offers.length}):`);
    offers.forEach(offer => {
      console.log(`- ${offer.user.username}: ${offer.type} ${offer.amount} ${offer.currencyPair} @ ${offer.baseRate}`);
    });
    
    // 2. Look for potential matches if prices were aligned
    console.log('\n=== POTENTIAL MATCHES IF PRICES ALIGNED ===');
    
    const buyOffers = offers.filter(o => o.type === 'BUY');
    const sellOffers = offers.filter(o => o.type === 'SELL');
    
    for (const buyOffer of buyOffers) {
      for (const sellOffer of sellOffers) {
        // Check if they could match (same currency pair, amount, different users)
        const couldMatch = 
          buyOffer.currencyPair === sellOffer.currencyPair &&
          buyOffer.amount === sellOffer.amount &&
          buyOffer.userId !== sellOffer.userId;
          
        if (couldMatch) {
          const priceDiff = Math.abs(buyOffer.baseRate - sellOffer.baseRate);
          const isCurrentlyCompatible = priceDiff < 1; // Within 1% tolerance
          
          console.log(`${buyOffer.user.username} BUY ${buyOffer.amount}@${buyOffer.baseRate} <-> ${sellOffer.user.username} SELL ${sellOffer.amount}@${sellOffer.baseRate}`);
          console.log(`  Price difference: ${priceDiff.toFixed(2)} - ${isCurrentlyCompatible ? '✅ COMPATIBLE' : '❌ NOT COMPATIBLE'}`);
          
          if (!isCurrentlyCompatible && priceDiff < 100) {
            console.log(`  💡 POTENTIAL: If prices aligned, this could be a match!`);
          }
        }
      }
    }
    
    // 3. Simulate what happens when someone updates their price
    console.log('\n=== SIMULATING PRICE UPDATE ===');
    
    // Find a specific scenario - let's say someone updates BUY 300@300 to BUY 300@350
    // This might make it compatible with a SELL 300@350 if one exists
    
    const testBuyOffer = offers.find(o => o.type === 'BUY' && o.amount === 300);
    if (testBuyOffer) {
      console.log(`\nSimulating: ${testBuyOffer.user.username} updates BUY ${testBuyOffer.amount}@${testBuyOffer.baseRate} to rate 350`);
      
      // Check what would match with BUY 300@350
      const potentialMatches = await prisma.offer.findMany({
        where: {
          type: 'SELL',
          currencyPair: testBuyOffer.currencyPair,
          status: 'ACTIVE',
          userId: { not: testBuyOffer.userId },
          amount: testBuyOffer.amount,
          baseRate: {
            gte: 350 * 0.99,
            lte: 350 * 1.01
          }
        },
        include: {
          user: { select: { username: true } }
        }
      });
      
      console.log(`  Found ${potentialMatches.length} potential matches at rate 350:`);
      potentialMatches.forEach(match => {
        console.log(`  - ${match.user.username}: SELL ${match.amount}@${match.baseRate}`);
      });
      
      // Check if any existing matches would block this
      for (const match of potentialMatches) {
        const existingMatch = await prisma.offerMatch.findFirst({
          where: {
            OR: [
              { offerAId: testBuyOffer.id, offerBId: match.id },
              { offerAId: match.id, offerBId: testBuyOffer.id }
            ]
          }
        });
        
        if (existingMatch) {
          console.log(`    ⚠️ Existing match ${existingMatch.matchId} [${existingMatch.status}] would affect this`);
        }
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPriceUpdateMatching();
