import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import apiClient from '@/services/apiClient';
import type {
  PaymentMethodWithValidation,
  CreatePaymentMethodPayload,
  UpdatePaymentMethodPayload,
  PaymentMethodsApiResponse,
  PaymentMethodApiResponse,
  PaymentMethodStats,
  PaymentMethodType
} from '@/types/paymentMethods';

export const usePaymentMethodsStore = defineStore('paymentMethods', () => {
  // State
  const paymentMethods = ref<PaymentMethodWithValidation[]>([]);
  const isLoading = ref(false);
  const isSubmitting = ref(false);
  const error = ref<string | null>(null);
  const lastFetchTime = ref<Date | null>(null);

  // Computed getters
  const allMethods = computed(() => paymentMethods.value);
  
  const getMethodsByCurrency = computed(() => (currency: string) => {
    const allMethods = paymentMethods.value;
    const filteredMethods = allMethods.filter(method => 
      method.currency === currency && method.isActive
    );
    
    const DEBUG = import.meta.env.DEV; // Only debug in development

    // Debug logging for payment method filtering
    if (DEBUG) {
      console.log('🔍 [PaymentMethodsStore] getMethodsByCurrency called for:', currency);
      console.log('🔍 [PaymentMethodsStore] Filtered methods:', filteredMethods.length);
    }
    
    return filteredMethods;
  });

  const getDefaultMethodForCurrency = computed(() => (currency: string) => {
    return paymentMethods.value.find(method => 
      method.currency === currency && 
      method.isDefaultForUser && 
      method.isActive
    );
  });

  const getMethodById = computed(() => (id: string) => {
    return paymentMethods.value.find(method => method.id === id);
  });

  const configuredCurrencies = computed(() => {
    const currencies = new Set(
      paymentMethods.value
        .filter(method => method.isActive)
        .map(method => method.currency)
    );
    return Array.from(currencies).sort();
  });

  const methodStats = computed((): PaymentMethodStats => {
    const activeMethods = paymentMethods.value.filter(m => m.isActive);
    
    const methodsByCurrency: Record<string, number> = {};
    const methodsByType: Partial<Record<PaymentMethodType, number>> = {};
    const defaultMethods: Record<string, PaymentMethodWithValidation> = {};
    
    let completeMethods = 0;
    let incompleteMethods = 0;

    activeMethods.forEach(method => {
      // Count by currency
      methodsByCurrency[method.currency] = (methodsByCurrency[method.currency] || 0) + 1;
      
      // Count by type
      methodsByType[method.paymentMethodType] = (methodsByType[method.paymentMethodType] || 0) + 1;
      
      // Track default methods
      if (method.isDefaultForUser) {
        defaultMethods[method.currency] = method;
      }
      
      // Count validation status
      if (method.validationStatus === 'complete') {
        completeMethods++;
      } else {
        incompleteMethods++;
      }
    });

    return {
      totalMethods: activeMethods.length,
      methodsByCurrency,
      methodsByType,
      completeMethods,
      incompleteMethods,
      defaultMethods
    };
  });

  // Actions
  const fetchAllMethods = async (): Promise<void> => {
    isLoading.value = true;
    error.value = null;
    
    try {
      const response = await apiClient.get<PaymentMethodsApiResponse>('/payment-methods');
      paymentMethods.value = response.data.data || [];
      lastFetchTime.value = new Date();
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || 'Failed to fetch payment methods';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchMethodsByCurrency = async (currency: string): Promise<void> => {
    isLoading.value = true;
    error.value = null;
    
    try {
      const response = await apiClient.get<PaymentMethodsApiResponse>(`/payment-methods/currency/${currency}`);
      
      // FIXED: Instead of replacing all methods, merge with existing ones
      // Remove existing methods for this currency first, then add the new ones
      const existingMethodsOtherCurrencies = paymentMethods.value.filter(method => method.currency !== currency);
      const newMethodsForCurrency = response.data.data || [];
      
      // Combine existing methods (other currencies) with new methods for this currency
      paymentMethods.value = [...existingMethodsOtherCurrencies, ...newMethodsForCurrency];
      
      lastFetchTime.value = new Date();
      
      const DEBUG = import.meta.env.DEV;
      if (DEBUG) {
        console.log('🔍 [PaymentMethodsStore] fetchMethodsByCurrency completed for:', currency);
        console.log('🔍 [PaymentMethodsStore] Added/updated', newMethodsForCurrency.length, 'methods for', currency);
        console.log('🔍 [PaymentMethodsStore] Store now contains:', paymentMethods.value.length, 'total methods');
      }
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || 'Failed to fetch payment methods';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const getDefaultMethod = async (currency: string): Promise<PaymentMethodWithValidation | null> => {
    try {
      const response = await apiClient.get<{ success: boolean; data: PaymentMethodWithValidation | null }>(`/payment-methods/currency/${currency}/default`);
      return response.data.data;
    } catch (err: any) {
      console.error('Failed to get default payment method:', err);
      return null;
    }
  };

  const createMethod = async (data: CreatePaymentMethodPayload): Promise<PaymentMethodWithValidation> => {
    isSubmitting.value = true;
    error.value = null;
    
    try {
      const response = await apiClient.post<PaymentMethodApiResponse>('/payment-methods', data);
      
      // Add the new method to the store
      if (response.data.data) {
        paymentMethods.value.push(response.data.data);
      }
      
      return response.data.data;
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || 'Failed to create payment method';
      throw err;
    } finally {
      isSubmitting.value = false;
    }
  };

  const updateMethod = async (id: string, data: UpdatePaymentMethodPayload): Promise<PaymentMethodWithValidation> => {
    isSubmitting.value = true;
    error.value = null;
    
    try {
      const response = await apiClient.put<PaymentMethodApiResponse>(`/payment-methods/${id}`, data);
      
      // Update the method in the store
      if (response.data.data) {
        const index = paymentMethods.value.findIndex(m => m.id === id);
        if (index >= 0) {
          paymentMethods.value[index] = response.data.data;
        }
      }
      
      return response.data.data;
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || 'Failed to update payment method';
      throw err;
    } finally {
      isSubmitting.value = false;
    }
  };

  const setAsDefault = async (id: string): Promise<PaymentMethodWithValidation> => {
    isSubmitting.value = true;
    error.value = null;
    
    try {
      const response = await apiClient.patch<PaymentMethodApiResponse>(`/payment-methods/${id}/set-default`);
      
      // Update the default status in the store
      if (response.data.data) {
        const currency = response.data.data.currency;
        
        // Remove default status from other methods of the same currency
        paymentMethods.value.forEach(method => {
          if (method.currency === currency && method.id !== id) {
            method.isDefaultForUser = false;
          } else if (method.id === id) {
            method.isDefaultForUser = true;
          }
        });
      }
      
      return response.data.data;
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || 'Failed to set as default';
      throw err;
    } finally {
      isSubmitting.value = false;
    }
  };

  const deactivateMethod = async (id: string): Promise<void> => {
    isSubmitting.value = true;
    error.value = null;
    
    try {
      await apiClient.delete<{ success: boolean }>(`/payment-methods/${id}`);
      
      // Remove the method from the store (or mark as inactive)
      const index = paymentMethods.value.findIndex(m => m.id === id);
      if (index >= 0) {
        paymentMethods.value[index].isActive = false;
        paymentMethods.value[index].isDefaultForUser = false;
      }
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || 'Failed to deactivate payment method';
      throw err;
    } finally {
      isSubmitting.value = false;
    }
  };

  // Utility actions
  const clearError = () => {
    error.value = null;
  };

  const refreshMethods = async (): Promise<void> => {
    await fetchAllMethods();
  };

  const getMethodsForTransaction = (currency: string) => {
    console.log('🔍 [PaymentMethodsStore] getMethodsForTransaction called for currency:', currency);
    const methods = getMethodsByCurrency.value(currency);
    console.log('🔍 [PaymentMethodsStore] Returning', methods.length, 'methods for transaction currency:', currency);
    return methods;
  };
  
  // Helper method to clear payment methods for a specific currency (useful for debugging)
  const clearMethodsForCurrency = (currency: string) => {
    console.log('🔍 [PaymentMethodsStore] Clearing methods for currency:', currency);
    paymentMethods.value = paymentMethods.value.filter(method => method.currency !== currency);
  };

  // Clear store
  const $reset = () => {
    paymentMethods.value = [];
    isLoading.value = false;
    isSubmitting.value = false;
    error.value = null;
    lastFetchTime.value = null;
  };

  return {
    // State
    paymentMethods: allMethods,
    isLoading,
    isSubmitting,
    error,
    lastFetchTime,
    
    // Getters
    getMethodsByCurrency,
    getDefaultMethodForCurrency,
    getMethodById,
    configuredCurrencies,
    methodStats,
    
    // Actions
    fetchAllMethods,
    fetchMethodsByCurrency,
    getDefaultMethod,
    createMethod,
    updateMethod,
    setAsDefault,
    deactivateMethod,
    clearError,
    refreshMethods,
    getMethodsForTransaction,
    clearMethodsForCurrency,
    $reset
  };
});

export type PaymentMethodsStore = ReturnType<typeof usePaymentMethodsStore>;
