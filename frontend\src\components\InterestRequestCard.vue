<template>
  <n-card :title="`Interest from ${interest.username}`" size="small" class="interest-request-card">
    <div class="interest-info">
      <div>
        <strong>User:</strong> {{ interest.username }}
      </div>
      <div>
        <strong>Reputation:</strong> Level {{ interest.reputationLevel }}
      </div>
      <div v-if="interest.reasonCode">
        <strong>Decline Reason:</strong> {{ interest.reasonCode }}
      </div>
    </div>
    <div class="actions" v-if="interest.status === 'PENDING'">
      <n-button type="primary" size="small" :loading="loadingAccept" @click="onAccept">Accept</n-button>
      <n-button type="error" size="small" :loading="loadingDecline" @click="onDecline">Decline</n-button>
    </div>
    <div v-else-if="interest.status === 'ACCEPTED'">
      <n-tag type="success">Accepted</n-tag>
      <n-button v-if="interest.chatSessionId" type="info" size="small" @click="goToChat">Go to Chat</n-button>
    </div>    <div v-else-if="interest.status === 'DECLINED'">
      <n-tag type="error">Declined</n-tag>
    </div>
    <div v-else-if="interest.status === 'CANCELLED'">
      <n-tag type="warning">Cancelled</n-tag>
    </div>
  </n-card>
</template>


<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { navigateToChat } from '@/utils/chatNavigation';
import { NCard, NButton, NTag, useNotification } from 'naive-ui';
import { useInterestStore } from '../stores/interestStore';

const props = defineProps<{
  interest: {
    id: string;
    username: string;
    reputationLevel: number;
    status: 'PENDING' | 'ACCEPTED' | 'DECLINED' | 'CANCELLED';
    chatSessionId?: string | null;
    transactionId?: string | null;
    reasonCode?: string | null;
  };
}>();


const interestStore = useInterestStore();
const loadingAccept = ref(false);
const loadingDecline = ref(false);
const router = useRouter();
const notification = useNotification();


async function onAccept() {
  loadingAccept.value = true;
  await interestStore.handleAcceptInterest(props.interest.id, notification);
  loadingAccept.value = false;
}

async function onDecline() {
  loadingDecline.value = true;
  await interestStore.handleDeclineInterest(props.interest.id, undefined, notification);
  loadingDecline.value = false;
}

async function goToChat() {
  if (props.interest.transactionId) {
    await navigateToChat(router, { transactionId: props.interest.transactionId });
  } else if (props.interest.chatSessionId) {
    await navigateToChat(router, { chatSessionId: props.interest.chatSessionId });
  }
}
</script>

<style scoped>
.interest-request-card {
  margin-bottom: 1rem;
}
.actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}
</style>
