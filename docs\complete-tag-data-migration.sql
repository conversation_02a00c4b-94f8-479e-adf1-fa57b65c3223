-- Complete Production Tag System Data Migration
-- This script adds all the missing tag data to match local dev exactly

-- Add the missing tag categories
INSERT INTO tag_categories (id, name, description, color, "order", "isActive", created_at, updated_at) VALUES
('f8d50d4b-26a5-472b-813a-6948aa729806', 'UI/UX', 'User interface and experience tags', '#8b5cf6', 6, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('e32588e5-1e4b-4583-a226-6e02c55f8649', 'Support', 'Support and help related tags', '#6b7280', 7, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('f8766881-da88-486a-a256-2a8bd513c005', 'General', 'General purpose tags', '#6b7280', 8, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (id) DO NOTHING;

-- Add all the missing tags
INSERT INTO tags (id, name, "displayName", description, category_id, color, icon, weight, is_active, is_system, usage_count, last_used_at, ai_relevance, created_at, updated_at) VALUES
('4bd3a407-376b-4aa0-9b7a-197d19a9ffc1', 'slow', '{"en": "Slow Performance", "fa": "کند"}', '{"en": "Performance issues related to speed", "fa": "مشکلات عملکرد مربوط به سرعت"}', '894962be-3074-4e3b-ba28-e4690c5c0a3c', '#f59e0b', 'clock', 80, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('fb7acb95-ee0a-469e-86f1-9a9d9cfd49ac', 'speed', '{"en": "Speed Issue", "fa": "مشکل سرعت"}', '{"en": "Speed-related performance problems", "fa": "مشکلات عملکرد مربوط به سرعت"}', '894962be-3074-4e3b-ba28-e4690c5c0a3c', '#f59e0b', 'zap', 80, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('089eb7a0-537a-46ca-b845-3215e9e933da', 'optimization', '{"en": "Optimization", "fa": "بهینه‌سازی"}', '{"en": "Areas that can be optimized", "fa": "مناطقی که می‌توانند بهینه شوند"}', '894962be-3074-4e3b-ba28-e4690c5c0a3c', '#f59e0b', 'zap', 60, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('dc243266-b7d5-450b-9729-c26c79f8bfc0', 'better-way', '{"en": "Better Way", "fa": "راه بهتر"}', '{"en": "Suggestions for better approaches", "fa": "پیشنهادات برای روش‌های بهتر"}', '936d5844-ec6b-43bf-88bf-7a41ec765201', '#10b981', 'arrow-up', 40, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('8e878c6f-2f0a-4bd6-aaf6-f52f8be1e10a', 'suggestion', '{"en": "Suggestion", "fa": "پیشنهاد"}', '{"en": "General suggestions for improvement", "fa": "پیشنهادات عمومی برای بهبود"}', '936d5844-ec6b-43bf-88bf-7a41ec765201', '#10b981', 'message-circle', 40, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('e0ed9712-9794-4b4c-bc66-5e082e9be391', 'idea', '{"en": "Idea", "fa": "ایده"}', '{"en": "Creative suggestions and ideas", "fa": "پیشنهادات و ایده‌های خلاقانه"}', 'c1369ac0-d249-471c-9067-76351c2fd3fb', '#8b5cf6', 'lightbulb', 50, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('3f59ab70-c871-465c-b250-fbee37c24c7e', 'unclear', '{"en": "Unclear", "fa": "نامشخص"}', '{"en": "Unclear or confusing aspects", "fa": "جنبه‌های نامشخص یا گیج‌کننده"}', 'e32588e5-1e4b-4583-a226-6e02c55f8649', '#6b7280', 'question-mark', 30, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('1f3e8618-a077-4fa7-b9c4-63c3dc3653b0', 'help', '{"en": "Help Needed", "fa": "نیاز به کمک"}', '{"en": "Requests for help or clarification", "fa": "درخواست‌های کمک یا توضیح"}', 'e32588e5-1e4b-4583-a226-6e02c55f8649', '#6b7280', 'help-circle', 30, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('36bf7e15-96e9-45b5-9674-614b2527c21e', 'documentation', '{"en": "Documentation", "fa": "مستندات"}', '{"en": "Documentation-related issues", "fa": "مسائل مربوط به مستندات"}', 'e32588e5-1e4b-4583-a226-6e02c55f8649', '#6b7280', 'book', 20, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('89dc01b3-b87a-4eed-9641-132e789ea1ec', 'general', '{"en": "General", "fa": "عمومی"}', '{"en": "General feedback and comments", "fa": "بازخورد و نظرات عمومی"}', 'f8766881-da88-486a-a256-2a8bd513c005', '#6b7280', 'circle', 10, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('555b1ca4-85d7-47a1-ad11-6a63ee683ba5', 'miscellaneous', '{"en": "Miscellaneous", "fa": "متفرقه"}', '{"en": "Various other issues", "fa": "مسائل مختلف دیگر"}', 'f8766881-da88-486a-a256-2a8bd513c005', '#6b7280', 'more-horizontal', 10, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('3868dde6-4577-4b29-a7de-61f14beb12cd', 'user-experience', '{"en": "User Experience", "fa": "تجربه کاربری"}', '{"en": "User experience improvements", "fa": "بهبودهای تجربه کاربری"}', 'f8d50d4b-26a5-472b-813a-6948aa729806', '#8b5cf6', 'user', 60, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('ba6c1ab6-0508-4d3a-ae8d-dfb04c22db84', 'design', '{"en": "Design", "fa": "طراحی"}', '{"en": "Visual design and layout issues", "fa": "مشکلات طراحی و چیدمان بصری"}', 'f8d50d4b-26a5-472b-813a-6948aa729806', '#8b5cf6', 'palette', 50, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('5ef3ed8c-4bfa-4d85-8e11-82da5b3ed70a', 'interface', '{"en": "Interface", "fa": "رابط کاربری"}', '{"en": "User interface problems", "fa": "مشکلات رابط کاربری"}', 'f8d50d4b-26a5-472b-813a-6948aa729806', '#8b5cf6', 'monitor', 50, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (id) DO NOTHING;

-- Update existing tags with complete data (description field was missing in some records)
UPDATE tags SET 
    description = '{"en": "Issues that require immediate attention", "fa": "مسائلی که نیاز به توجه فوری دارند"}'
WHERE id = '4c9c3f1a-b605-4037-b060-a037efa18d75' AND description IS NULL;

UPDATE tags SET 
    description = '{"en": "Problems that need to be fixed", "fa": "مشکلاتی که نیاز به رفع دارند"}'
WHERE id = 'd7ef7039-8ce0-43f5-87be-5ffa5d1b8313' AND description IS NULL;

UPDATE tags SET 
    description = '{"en": "System errors and exceptions", "fa": "خطاهای سیستم و استثناها"}'
WHERE id = '3c42a34b-87c7-410c-8e16-c617d03a3aa4' AND description IS NULL;

UPDATE tags SET 
    description = '{"en": "Improvements to existing features", "fa": "بهبودهای ویژگی‌های موجود"}'
WHERE id = '35b80061-2d8d-41bd-a722-483e5dbbe48f' AND description IS NULL;

UPDATE tags SET 
    description = '{"en": "Requests for new functionality", "fa": "درخواست‌های عملکرد جدید"}'
WHERE id = '2919ad83-dd8f-488a-ad28-1debcaf08ff9' AND description IS NULL;

-- Add all the missing tag-report type associations
INSERT INTO tag_report_type_associations (id, tag_id, report_type, weight, created_at) VALUES
('ccab888f-90de-412d-9ac2-187456d418d0', '089eb7a0-537a-46ca-b845-3215e9e933da', 'PERFORMANCE', 100, CURRENT_TIMESTAMP),
('c6e294b0-64c8-4ddc-a47f-c6ce4e7b765f', '1f3e8618-a077-4fa7-b9c4-63c3dc3653b0', 'QUESTION', 100, CURRENT_TIMESTAMP),
('aea1e21c-c95d-49c4-bfc7-7dbd62b499fc', '35b80061-2d8d-41bd-a722-483e5dbbe48f', 'IMPROVEMENT', 100, CURRENT_TIMESTAMP),
('b0153e3e-d44e-4b7e-89aa-2efe32ed63c0', '36bf7e15-96e9-45b5-9674-614b2527c21e', 'QUESTION', 100, CURRENT_TIMESTAMP),
('748b4adf-b60e-44b0-973b-f495d4c636f1', '3868dde6-4577-4b29-a7de-61f14beb12cd', 'UI_UX', 100, CURRENT_TIMESTAMP),
('096bcd79-f284-4bf8-94a9-1d7f007619bd', '3f59ab70-c871-465c-b250-fbee37c24c7e', 'QUESTION', 100, CURRENT_TIMESTAMP),
('c47d9c7f-afd1-4dc7-9420-16ff35ff9fef', '4bd3a407-376b-4aa0-9b7a-197d19a9ffc1', 'PERFORMANCE', 100, CURRENT_TIMESTAMP),
('750898e5-8b97-4f09-a9c5-69c85211d11a', '555b1ca4-85d7-47a1-ad11-6a63ee683ba5', 'OTHER', 100, CURRENT_TIMESTAMP),
('43c0a757-3763-4b54-a569-be098b925a8b', '5ef3ed8c-4bfa-4d85-8e11-82da5b3ed70a', 'UI_UX', 100, CURRENT_TIMESTAMP),
('f58061c6-49e0-4640-a656-e979d7118fc7', '89dc01b3-b87a-4eed-9641-132e789ea1ec', 'OTHER', 100, CURRENT_TIMESTAMP),
('40c9e69e-3943-4808-9b75-c74ff6dea85f', '8e878c6f-2f0a-4bd6-aaf6-f52f8be1e10a', 'IMPROVEMENT', 100, CURRENT_TIMESTAMP),
('a4491a0e-c397-4463-afc6-c877d73f26f8', 'ba6c1ab6-0508-4d3a-ae8d-dfb04c22db84', 'UI_UX', 100, CURRENT_TIMESTAMP),
('206381c9-e057-49d0-acc5-dbc7dc2b555e', 'dc243266-b7d5-450b-9729-c26c79f8bfc0', 'IMPROVEMENT', 100, CURRENT_TIMESTAMP),
('e9ae7476-13b1-43bc-927a-1a95c916162b', 'e0ed9712-9794-4b4c-bc66-5e082e9be391', 'FEATURE_REQUEST', 100, CURRENT_TIMESTAMP),
('c173601e-5222-406e-8fb6-6525f449cb7e', 'fb7acb95-ee0a-469e-86f1-9a9d9cfd49ac', 'PERFORMANCE', 100, CURRENT_TIMESTAMP)
ON CONFLICT (id) DO NOTHING;

-- Verify the complete migration
SELECT 'Final Tag Categories Count:' as info, COUNT(*) as count FROM tag_categories;
SELECT 'Final Tags Count:' as info, COUNT(*) as count FROM tags;
SELECT 'Final Tag Associations Count:' as info, COUNT(*) as count FROM tag_report_type_associations;
