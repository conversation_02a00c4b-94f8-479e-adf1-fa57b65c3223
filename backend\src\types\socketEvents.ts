// Centralized Socket.IO event names for offer-related real-time updates
// Use as string literal types for type safety

export const OFFER_CREATED = 'OFFER_CREATED' as const;
export const OFFER_UPDATED = 'OFFER_UPDATED' as const;
export const OFFER_STATUS_CHANGED = 'OFFER_STATUS_CHANGED' as const;
export const OFFER_DELETED = 'OFFER_DELETED' as const; // Optional, for future use

// Socket event payload types
export interface OfferCreatedPayload {
  offerId: string;
  userId: string;
  fullOfferData?: {
    id: string;
    userId: string;
    type: string;
    currencyPair: string;
    amount: number;
    baseRate: number;
    adjustmentForLowerRep: number;
    adjustmentForHigherRep: number;
    status: string;
    user?: {
      username: string | null;
      reputationLevel: number;
    };
    interests: any[];
    [key: string]: any; // For other properties
  };
}

export const INTEREST_RECEIVED = 'INTEREST_RECEIVED';


export const INTEREST_ACCEPTED = 'INTEREST_ACCEPTED';
export const INTEREST_DECLINED = 'INTEREST_DECLINED';
export const INTEREST_REQUEST_DECLINED = 'INTEREST_REQUEST_DECLINED';
export const INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY = 'INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY';

// Chat-specific events - NEW
export const CHAT_MESSAGE_SEND = 'CHAT_MESSAGE_SEND' as const;
export const CHAT_MESSAGE_RECEIVE = 'CHAT_MESSAGE_RECEIVE' as const;
export const TRANSACTION_STATUS_UPDATED = 'TRANSACTION_STATUS_UPDATED' as const; // New event
export const SYSTEM_MESSAGE_RECEIVE = 'SYSTEM_MESSAGE_RECEIVE';

export const NEGOTIATION_STATE_UPDATED = 'NEGOTIATION_STATE_UPDATED' as const;
export const PAYMENT_INFO_SUBMITTED = 'PAYMENT_INFO_SUBMITTED' as const;
export const PROPOSAL_MADE = 'PROPOSAL_MADE' as const;
export const NEGOTIATION_FINALIZED = 'NEGOTIATION_FINALIZED' as const;

// Model for storing Notifications
export interface InterestRequestAcceptedAndChatReadyPayload {
  interestId: string;
  offerId: string;
  chatSessionId: string;
  offer: {
    type: string; 
    amount: number;
    currencyPair: string;
  };
  offerCreator: {
    userId: string;
    username: string;
    reputationLevel: number;
  };
  interestedUser: {
    userId: string;
    username: string;
    reputationLevel: number;
  };
}

export interface ChatMessageSendPayload {
  chatSessionId: string;
  messageText: string;
  // senderId will be derived from the authenticated socket connection on the backend
  // timestamp will be generated on the backend
}

export interface ChatMessageReceivePayload {
  messageId: string;
  chatSessionId: string;
  sender: { // Changed from senderId: string and senderUsername: string
    id: string;
    username: string;
    reputationLevel: number;
  };
  content: string; // Renamed from messageText
  createdAt: string; // Renamed from timestamp, ensure ISO string format
  isSystemMessage?: boolean; // Added to identify system messages
}

// New interface for system messages
// For system-generated messages (e.g., transaction updates in chat)
export interface SystemMessagePayload {
  messageId: string; // Message ID
  chatSessionId: string;
  content: string;
  createdAt: string; // ISO string
  isSystemMessage: true; // Always true for this payload
  transactionId?: string | null; // Optional: if system message is related to a transaction
}

// Updated to align with the new Transaction model and provide more comprehensive details
export interface TransactionStatusUpdatePayload {
  offerId: string | null; // Offer ID if applicable, can be null if not related to an offer
  chatSessionId: string;
  transactionId: string;
  status: string; // Corresponds to TransactionStatus enum
  currencyA: string;
  amountA: number;
  currencyAProviderId: string;
  currencyAProviderUsername: string | null; // Username of the provider for currency A, can be null if not applicable
  currencyBProviderUsername: string | null; // Username of the provider for currency B, can be null if not applicable
  currencyB: string;
  amountB: number;
  currencyBProviderId: string;
  agreedFirstPayerId?: string | null;
  termsAgreementTimestampPayer1?: string | null; // ISO Date string
  termsAgreementTimestampPayer2?: string | null; // ISO Date string
  paymentExpectedByPayer1?: string | null;     // ISO Date string
  paymentDeclaredAtPayer1?: string | null;       // ISO Date string
  paymentTrackingNumberPayer1?: string | null;
  firstPaymentConfirmedByPayer2At?: string | null; // ISO Date string
  paymentExpectedByPayer2?: string | null;     // ISO Date string
  paymentDeclaredAtPayer2?: string | null;       // ISO Date string
  paymentTrackingNumberPayer2?: string | null;
  secondPaymentConfirmedByPayer1At?: string | null; // ISO Date string
  cancellationReason?: string | null;
  disputeReason?: string | null;
  updatedAt: string; // ISO Date string, to know when the last update happened
  createdAt: string; // ISO Date string
  firstPayerDesignationTimestamp?: string | null;
  cancelledByUserId?: string | null; // User ID of the user who cancelled the transaction
  disputedByUserId?: string | null; // User ID of the user who raised the dispute
  disputeResolvedAt?: string | null; // ISO Date string, when the dispute was resolved
  disputeResolutionNotes?: string | null; // Notes on how the dispute was resolved
}

export type OfferSocketEvent =
  | typeof OFFER_CREATED
  | typeof OFFER_UPDATED
  | typeof OFFER_STATUS_CHANGED
  | typeof OFFER_DELETED;

export type SocketEvents = {
  [INTEREST_RECEIVED]: {
    interestId: string;
    interestedUserId: string;
    offerId: string;
    offer: {
      type: string;
      amount: number;
      currencyPair: string;
    };
    interestedUser: {
      username: string;
      reputationLevel: number;
    };
  };
  [INTEREST_ACCEPTED]: {
    interestId: string;
    offerId: string;
    chatSessionId: string;
    offerDetails: OfferDetailsForSocket;
    actorDetails: UserActorDetailsForSocket;
  };
  [INTEREST_DECLINED]: {
    interestId: string;
    offerId: string;
    reasonCode?: string;
    offer: OfferDetailsForSocket; 
    offerCreator: UserActorDetailsForSocket;
  };
  [INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY]: InterestRequestAcceptedAndChatReadyPayload;

  // Chat events - NEW
  [CHAT_MESSAGE_SEND]: ChatMessageSendPayload; // Technically, backend listens for this, frontend emits
  [CHAT_MESSAGE_RECEIVE]: ChatMessageReceivePayload; // Backend emits this, frontend listens
  [TRANSACTION_STATUS_UPDATED]: TransactionStatusUpdatePayload; // Backend emits this, frontend listens
  [SYSTEM_MESSAGE_RECEIVE]: SystemMessagePayload; // Backend emits this, frontend listens for system messages

  // Payer Negotiation Events - NEW
  [NEGOTIATION_STATE_UPDATED]: PayerNegotiationStatePayload;
  [PAYMENT_INFO_SUBMITTED]: PaymentInfoSubmittedPayload;
  [PROPOSAL_MADE]: ProposalMadePayload;
  [NEGOTIATION_FINALIZED]: NegotiationFinalizedPayload;
  // Automatic Offer Matching System Events
  [MATCH_FOUND]: MatchFoundPayload;
  [MATCH_ACCEPTED]: MatchAcceptedPayload;
  [MATCH_DECLINED]: MatchDeclinedPayload;
  [MATCH_EXPIRED]: MatchExpiredPayload;
  [MATCH_CONVERTED]: MatchConvertedPayload;
  [MATCH_CANCELLED]: MatchCancelledPayload;
};

export interface OfferDetailsForSocket {
  id: string;
  type: string;
  amount: number;
  currencyPair?: string;
}

export interface UserActorDetailsForSocket {
  userId: string;
  username: string;
  reputationLevel?: number;
}

export interface YourInterestAcceptedPayload {
  interestId: string;
  offerId: string;
  chatSessionId: string;
  offerDetails: OfferDetailsForSocket;
  actorDetails: UserActorDetailsForSocket;
}

export interface YourInterestDeclinedPayload {
  interestId: string;
  offerId: string;
  reasonCode?: string;
  offer: OfferDetailsForSocket; 
  offerCreator: UserActorDetailsForSocket;
}

// Payer Negotiation Payloads - NEW
import type { ReceivingInfoStatus, NegotiationStatus } from '@prisma/client';
// Remove the local PayerDesignationRule and SystemRecommendation if they are not uniquely defined here
// and rely on the imported version from payerNegotiationService or a shared types file.
import type { PayerDesignationRule as PayerDesignationRuleEnum, SystemRecommendation as SystemRecommendationInterface } from '../services/payerNegotiationService';
import type { MappedPaymentReceivingInfo } from './payerNegotiation';

export interface PayerNegotiationStatePayload {
  negotiationId: string;
  transactionId: string;
  partyA_Id: string;
  partyB_Id: string;
  partyA_receivingInfoStatus: ReceivingInfoStatus;
  partyB_receivingInfoStatus: ReceivingInfoStatus;
  partyA_PaymentReceivingInfo: MappedPaymentReceivingInfo | null;
  partyB_PaymentReceivingInfo: MappedPaymentReceivingInfo | null;
  systemRecommendedPayerId: string | null;
  systemRecommendationReason: string | null;
  systemRecommendationRule: PayerDesignationRuleEnum | null; // Use imported enum
  systemRecommendationDetails: any; // Or use SystemRecommendationInterface if it matches the structure
  currentProposal_PayerId: string | null;
  currentProposal_ById: string | null;
  currentProposal_Message: string | null;
  partyA_agreedToCurrentProposal: boolean;
  partyB_agreedToCurrentProposal: boolean;
  negotiationStatus: NegotiationStatus;
  finalizedPayerId: string | null;
  paymentTimerDueDate: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentInfoSubmittedPayload {
  negotiationId: string;
  userId: string; // User who submitted the info
  receivingInfoStatus: string; // ReceivingInfoStatus enum as string
  negotiationState: PayerNegotiationStatePayload; // The full updated negotiation state
}

export interface ProposalMadePayload {
  negotiationId: string;
  proposerId: string;
  proposedPayerId: string;
  message?: string; // <-- CONFIRM THIS IS PRESENT AND OPTIONAL
}

export interface NegotiationFinalizedPayload {
  negotiationId: string;
  finalizedPayerId: string;
  paymentTimerDueDate?: string | null; // ISO Date string
  // Remove negotiationState from here if it's not part of this specific event's direct payload
  // negotiationState: PayerNegotiationStatePayload; 
}

// Remove the local enum and interface if they are fully covered by imports
/*
export enum PayerDesignationRule {
    REPUTATION = 'REPUTATION',      // Lower reputation user pays first
    CURRENCY = 'CURRENCY',         // RIAL provider pays first if reputations equal
    OFFER_CREATOR = 'OFFER_CREATOR' // Fallback: offer creator pays first
}

export interface SystemRecommendation {
    recommendedPayerId: string;
    appliedRule: PayerDesignationRule;
    reasoning: string;
    ruleDetails?: {
        reputationA?: number;
        reputationB?: number;
        currencyA?: string;
        currencyB?: string;
        isOfferCreator?: boolean;
    };
}
*/

// Automatic Offer Matching System Events
export const MATCH_FOUND = 'MATCH_FOUND' as const;
export const MATCH_ACCEPTED = 'MATCH_ACCEPTED' as const;
export const MATCH_DECLINED = 'MATCH_DECLINED' as const;
export const MATCH_EXPIRED = 'MATCH_EXPIRED' as const;
export const MATCH_CONVERTED = 'MATCH_CONVERTED' as const;
export const MATCH_CANCELLED = 'MATCH_CANCELLED' as const;

// Matching system payload interfaces
export interface MatchFoundPayload {
  matchId: string;
  match: {
    id: string;
    matchId: string; // Human-readable ID
    offerA: {
      id: string;
      type: string;
      currencyPair: string;
      amount: number;
      baseRate: number;
      user: {
        id: string;
        username: string | null;
        email: string;
        reputationLevel: number;
      };
    };
    offerB: {
      id: string;
      type: string;
      currencyPair: string;
      amount: number;
      baseRate: number;
      user: {
        id: string;
        username: string | null;
        email: string;
        reputationLevel: number;
      };
    };
    userA: {
      id: string;
      username: string | null;
      email: string;
      reputationLevel: number;
    };
    userB: {
      id: string;
      username: string | null;
      email: string;
      reputationLevel: number;
    };
    compatibilityScore: number;
    expiresAt: string; // ISO date string
  };
}

export interface MatchAcceptedPayload {
  matchId: string;
  acceptingUserId: string;
  status: string; // 'partial_accept' or 'both_accepted'
  match: {
    id: string;
    matchId: string;
    status: string; // MatchStatus
    userAResponse: string | null;
    userBResponse: string | null;
    chatSessionId: string | null;
    transactionId: string | null;
  };
}

export interface MatchDeclinedPayload {
  matchId: string;
  decliningUserId: string;
  reason?: string;
  match: {
    id: string;
    matchId: string;
    status: string; // MatchStatus.DECLINED
  };
}

export interface MatchExpiredPayload {
  matchId: string;
  match: {
    id: string;
    matchId: string;
    status: string; // MatchStatus.EXPIRED
  };
}

export interface MatchCancelledPayload {
  matchId: string;
  reason: string; // 'competing_match_accepted', 'offer_deactivated', etc.
}

export interface MatchConvertedPayload {
  matchId: string;
  transactionId: string;
  chatSessionId: string;
}
