/* Transactional Chat CSS Variables - Light and Dark Mode Support */

:root {
  /* Light Mode Colors */
  --tc-bg-primary: #ffffff;
  --tc-bg-secondary: #f8fafc;
  --tc-bg-card: #ffffff;
  --tc-bg-header: #ffffff;
  --tc-bg-status: #f1f5f9;
  --tc-bg-action-bar: #ffffff;
  --tc-bg-feed: #f8fafc;
  
  /* Text Colors */
  --tc-text-primary: #1e293b;
  --tc-text-secondary: #64748b;
  --tc-text-muted: #94a3b8;
  --tc-text-light: #cbd5e1;
  --tc-text-white: #ffffff;
  
  /* Border Colors */
  --tc-border-light: #e2e8f0;
  --tc-border-medium: #cbd5e1;
  --tc-border-dark: #94a3b8;
  
  /* Action Colors */
  --tc-primary: #3b82f6;
  --tc-primary-light: #dbeafe;
  --tc-primary-dark: #1d4ed8;
  --tc-primary-hover: #2563eb;
  --tc-primary-active: #1d4ed8;
  --tc-success: #10b981;
  --tc-success-light: #d1fae5;
  --tc-success-hover: #059669;
  --tc-warning: #f59e0b;
  --tc-warning-hover: #d97706;
  --tc-danger: #ef4444;
  --tc-danger-hover: #dc2626;
  
  /* Background Variants */
  --tc-bg-subtle: #f8fafc;
  --tc-bg-hover: #f1f5f9;
  
  /* Step Progress Colors */
  --tc-step-complete: #10b981;
  --tc-step-active: #3b82f6;
  --tc-step-pending: #cbd5e1;
  --tc-step-line: #e2e8f0;
  
  /* Chat Colors */
  --tc-chat-user: #3b82f6;
  --tc-chat-other: #64748b;
  --tc-chat-system: #6b7280;
  --tc-chat-bubble-user: #3b82f6;
  --tc-chat-bubble-other: #f1f5f9;
  --tc-chat-bubble-system: #f9fafb;
  
  /* Timer Colors */
  --tc-timer-normal: #64748b;
  --tc-timer-warning: #f59e0b;
  --tc-timer-danger: #ef4444;
  
  /* Shadows */
  --tc-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tc-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tc-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tc-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* Component Specific */
  --tc-action-card-bg: #ffffff;
  --tc-action-card-border: #e2e8f0;
  --tc-action-card-shadow: var(--tc-shadow);
  
  /* Input Colors */
  --tc-input-bg: #ffffff;
  --tc-input-border: #cbd5e1;
  --tc-input-border-focus: #3b82f6;
  --tc-input-placeholder: #94a3b8;
  --tc-input-text: #1e293b;
  
  /* Reputation Colors */
  --tc-reputation-1: #ef4444;
  --tc-reputation-2: #f59e0b;
  --tc-reputation-3: #eab308;
  --tc-reputation-4: #84cc16;
  --tc-reputation-5: #10b981;
}

/* Dark Mode Overrides */
html.dark {
  /* Dark Mode Colors */
  --tc-bg-primary: #0f172a;
  --tc-bg-secondary: #1e293b;
  --tc-bg-card: #1e293b;
  --tc-bg-header: #1e293b;
  --tc-bg-status: #334155;
  --tc-bg-action-bar: #1e293b;
  --tc-bg-feed: #0f172a;
  
  /* Text Colors */
  --tc-text-primary: #f8fafc;
  --tc-text-secondary: #cbd5e1;
  --tc-text-muted: #94a3b8;
  --tc-text-light: #64748b;
  --tc-text-white: #ffffff;
  
  /* Border Colors */
  --tc-border-light: #334155;
  --tc-border-medium: #475569;
  --tc-border-dark: #64748b;
  
  /* Action Colors - Slightly brighter for dark mode */
  --tc-primary: #60a5fa;
  --tc-primary-light: #1e3a8a;
  --tc-primary-dark: #1d4ed8;
  --tc-primary-hover: #3b82f6;
  --tc-primary-active: #2563eb;
  --tc-success: #34d399;
  --tc-success-light: #064e3b;
  --tc-success-hover: #10b981;
  --tc-warning: #fbbf24;
  --tc-warning-hover: #f59e0b;
  --tc-danger: #f87171;
  --tc-danger-hover: #ef4444;
  
  /* Background Variants */
  --tc-bg-subtle: #334155;
  --tc-bg-hover: #475569;
  
  /* Step Progress Colors */
  --tc-step-complete: #34d399;
  --tc-step-active: #60a5fa;
  --tc-step-pending: #475569;
  --tc-step-line: #334155;
  
  /* Chat Colors */
  --tc-chat-user: #60a5fa;
  --tc-chat-other: #cbd5e1;
  --tc-chat-system: #94a3b8;
  --tc-chat-bubble-user: #1e40af;
  --tc-chat-bubble-other: #334155;
  --tc-chat-bubble-system: #374151;
  
  /* Timer Colors */
  --tc-timer-normal: #cbd5e1;
  --tc-timer-warning: #fbbf24;
  --tc-timer-danger: #f87171;
  
  /* Shadows - Darker for dark mode */
  --tc-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --tc-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --tc-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --tc-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  
  /* Component Specific */
  --tc-action-card-bg: #1e293b;
  --tc-action-card-border: #334155;
  --tc-action-card-shadow: var(--tc-shadow);
  
  /* Input Colors */
  --tc-input-bg: #334155;
  --tc-input-border: #475569;
  --tc-input-border-focus: #60a5fa;
  --tc-input-placeholder: #64748b;
  --tc-input-text: #f8fafc;
  
  /* Reputation Colors - Slightly brighter for dark mode */
  --tc-reputation-1: #f87171;
  --tc-reputation-2: #fbbf24;
  --tc-reputation-3: #fde047;
  --tc-reputation-4: #a3e635;
  --tc-reputation-5: #34d399;
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  :root {
    --tc-mobile-padding: 16px;
    --tc-mobile-margin: 8px;
    --tc-mobile-border-radius: 12px;
    --tc-mobile-font-base: 16px;
    --tc-mobile-font-small: 14px;
    --tc-mobile-font-large: 18px;
  }
}

/* Tablet and up */
@media (min-width: 769px) {
  :root {
    --tc-desktop-padding: 24px;
    --tc-desktop-margin: 16px;
    --tc-desktop-border-radius: 16px;
    --tc-desktop-font-base: 16px;
    --tc-desktop-font-small: 14px;
    --tc-desktop-font-large: 20px;
  }
}

/* Common utility classes for the transactional chat */
.tc-fade-in {
  animation: tcFadeIn 0.3s ease-out;
}

.tc-slide-up {
  animation: tcSlideUp 0.3s ease-out;
}

.tc-pulse {
  animation: tcPulse 2s infinite;
}

@keyframes tcFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes tcSlideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes tcPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
