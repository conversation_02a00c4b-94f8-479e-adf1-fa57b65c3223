// Debug script for match filtering issues
// Run this in browser console on the matches page

console.log('=== Match Store Debug ===');

// Check localStorage for debugging
const authToken = localStorage.getItem('authToken');
const userInfo = localStorage.getItem('userInfo');

console.log('Auth token exists:', !!authToken);
if (userInfo) {
  const user = JSON.parse(userInfo);
  console.log('Current user ID:', user.id);
}

// Make API call to get matches
fetch('http://localhost:3000/api/matches', {
  headers: {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('Raw matches from API:', data);
  
  // Handle both response formats
  const matches = data.matches || data.data || [];
  const currentUserId = JSON.parse(userInfo).id;
  
  console.log('Total matches:', matches.length);
  
  matches.forEach((match, index) => {
    const isCurrentUserA = match.userAId === currentUserId;
    const isCurrentUserB = match.userBId === currentUserId;
    const currentUserResponse = isCurrentUserA ? match.userAResponse : match.userBResponse;
    const otherUserResponse = isCurrentUserA ? match.userBResponse : match.userAResponse;
    const isExpired = new Date(match.expiresAt) < new Date();
    
    console.log(`Match ${index + 1}:`, {
      id: match.id,
      status: match.status,
      userAId: match.userAId,
      userBId: match.userBId,
      userAResponse: match.userAResponse,
      userBResponse: match.userBResponse,
      isCurrentUserA,
      isCurrentUserB,
      currentUserResponse,
      otherUserResponse,
      expiresAt: match.expiresAt,
      isExpired,
      needsResponse: !isExpired && 
        (match.status === 'PENDING' || match.status === 'PARTIAL_ACCEPT') && 
        !currentUserResponse
    });
  });
  
  // Filter for matches needing response
  const needingResponse = matches.filter(match => {
    const isCurrentUserA = match.userAId === currentUserId;
    const isCurrentUserB = match.userBId === currentUserId;
    const isCurrentUserInvolved = isCurrentUserA || isCurrentUserB;
    const currentUserResponse = isCurrentUserA ? match.userAResponse : match.userBResponse;
    const isExpired = new Date(match.expiresAt) < new Date();
    
    const needsResponse = isCurrentUserInvolved && 
      !isExpired && 
      (match.status === 'PENDING' || match.status === 'PARTIAL_ACCEPT') && 
      !currentUserResponse;
      
    return needsResponse;
  });
  
  console.log('Matches needing response:', needingResponse.length);
  console.log('Matches needing response details:', needingResponse);
  
  // Also check what the frontend match store shows
  console.log('=== Frontend Store Check ===');
  console.log('Try accessing store through Vue dev tools or check the UI state');
})
.catch(error => {
  console.error('Error fetching matches:', error);
});
