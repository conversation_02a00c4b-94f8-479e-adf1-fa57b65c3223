#!/usr/bin/env pwsh

Write-Host "🚀 Updating production database schema from schema.prisma..." -ForegroundColor Green

# Change to the project root directory
Set-Location "C:\Code\MUNygo"

# Check if production containers are running
Write-Host "🔍 Checking production containers..." -ForegroundColor Yellow
$containers = docker-compose ps --services --filter "status=running"
if ($containers -notcontains "backend") {
    Write-Host "❌ Production backend container is not running" -ForegroundColor Red
    Write-Host "💡 Start production environment first with: docker-compose up -d" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Production backend container is running" -ForegroundColor Green

# Push schema changes directly to production database
Write-Host "🔄 Pushing schema changes to production database..." -ForegroundColor Yellow
Write-Host "⏳ This will apply all changes from schema.prisma directly..." -ForegroundColor Yellow

$pushResult = docker-compose exec backend npx prisma db push --accept-data-loss

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Schema push failed" -ForegroundColor Red
    Write-Host "💡 Check the output above for error details" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Schema pushed successfully!" -ForegroundColor Green

# Generate Prisma client to ensure it's up to date
Write-Host "🔄 Regenerating Prisma client..." -ForegroundColor Yellow
docker-compose exec backend npx prisma generate

# Test the debug reports API
Write-Host "🧪 Testing debug reports API..." -ForegroundColor Yellow
$testResult = docker-compose exec backend node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testDebugReports() {
    try {
        // Check if debug_reports table exists and has correct schema
        const tableInfo = await prisma.\$queryRaw\`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'debug_reports'
            ORDER BY ordinal_position;
        \`;
        
        console.log('✅ debug_reports table schema:');
        console.table(tableInfo);
        
        // Try to fetch debug reports with the new schema
        const reports = await prisma.debugReport.findMany({
            take: 5,
            include: { user: { select: { email: true, firstName: true, lastName: true } } }
        });
        
        console.log(\`✅ Successfully fetched \${reports.length} debug reports\`);
        
        await prisma.\$disconnect();
        console.log('✅ Database connection test passed');
    } catch (error) {
        console.error('❌ Database test failed:', error.message);
        process.exit(1);
    }
}

testDebugReports();
"

Write-Host "🎉 Production database schema updated successfully!" -ForegroundColor Green
Write-Host "💡 Debug Dashboard should now work correctly in production" -ForegroundColor Yellow

# Show final status
Write-Host "`n📊 Production Environment Status:" -ForegroundColor Cyan
Write-Host "✅ Database schema updated from schema.prisma" -ForegroundColor Green
Write-Host "✅ Prisma client regenerated" -ForegroundColor Green
Write-Host "✅ Debug reports API tested" -ForegroundColor Green

Write-Host "`n🔗 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Access Debug Dashboard at: http://your-domain/admin/debug-dashboard" -ForegroundColor White
Write-Host "2. Verify admin user can view debug reports" -ForegroundColor White
Write-Host "3. Test debug report submission and viewing" -ForegroundColor White

Write-Host "`n⚠️  Note: If you encounter any issues, check container logs with:" -ForegroundColor Yellow
Write-Host "   docker-compose logs backend" -ForegroundColor Gray
