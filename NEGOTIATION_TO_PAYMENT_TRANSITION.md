# UI Flow: After Both Parties Agree on Who Should Pay First

## Current Implementation

After both parties agree on who should pay first in the `SmartNegotiationSection`, the following happens:

### 1. Backend Processing
When both parties agree to a proposal:
- The negotiation status changes from `PENDING_RESPONSE` to `FINALIZED`
- The `finalizedPayerId` is set to identify who will pay first
- The backend calls `transactionService.designateFirstPayer()` which:
  - Updates transaction status from `AWAITING_FIRST_PAYER_DESIGNATION` to `AWAITING_FIRST_PAYER_PAYMENT`
  - Sets a payment timer/deadline for the first payer
  - Creates notifications for both parties
  - Emits socket events for real-time updates

### 2. Frontend UI Updates

#### SmartNegotiationSection Component
- Detects negotiation status change to `FINALIZED`
- Shows success message indicating who will pay first
- Displays transition message: "The transaction will now proceed to the payment phase"
- Emits `negotiationFinalized` event to parent component

#### ActionCard Component
- Receives the `negotiationFinalized` event
- Shows success notification to user
- Triggers a fresh fetch of transaction data (`fetchTransaction(transactionId, true)`)

#### TransactionalChatStore
- Receives socket events for transaction status updates
- Updates transaction details with new status `AWAITING_FIRST_PAYER_PAYMENT`
- Regenerates feed items to show appropriate action cards for the payment phase

### 3. Next UI State
After negotiation finalization, the UI transitions to show:

1. **For the First Payer:**
   - Payment action card with recipient's payment details
   - Timer showing how long they have to make payment
   - "Declare Payment" button to mark payment as sent

2. **For the Second Payer (Receiver):**
   - Waiting state showing they're waiting for first payment
   - Information about what the first payer needs to do

### 4. Key UI Components Involved

- `SmartNegotiationSection.vue` - Handles the negotiation phase
- `ActionCard.vue` - Manages different action cards in the feed
- `TransactionFlowCardV3.vue` - Shows overall transaction progress
- `transactionalChatStore.ts` - Manages transaction state and feed items

### 5. Socket Events
The system uses real-time socket events to keep all parties synchronized:
- `NEGOTIATION_FINALIZED` - When agreement is reached
- `TRANSACTION_STATUS_UPDATED` - When transaction moves to payment phase

## Implementation Status ✅

The core functionality is implemented and working:
- ✅ Negotiation finalization detection
- ✅ Event emission to parent components
- ✅ Automatic data refresh after agreement
- ✅ UI transition to payment phase
- ✅ Real-time updates via sockets
- ✅ Proper success messages and feedback

## Files Modified
- `SmartNegotiationSection.vue` - Added negotiation finalized event emission
- `ActionCard.vue` - Added negotiation finalized handler
- `transactionalChat.json` (en/fa) - Added transition message translations

The system now provides a smooth transition from the negotiation phase to the payment phase with clear user feedback.
