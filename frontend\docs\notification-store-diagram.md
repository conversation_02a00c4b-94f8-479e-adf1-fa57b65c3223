# Notification Store & Centralized Socket Manager Interaction

This diagram shows how the notificationStore works with the centralizedSocketManager to provide real-time notifications in the MUNygo frontend.

```mermaid
flowchart TD
    subgraph UI
        A[NotificationBell.vue / Notification List]
    end

    subgraph Pinia Stores
        B[notificationStore]
    end

    subgraph Services
        C[centralizedSocketManager]
    end

    subgraph Backend
        D[Socket.IO Server]
        E[Notification API]
    end

    A -- uses --> B
    B -- subscribes to events --> C
    C -- receives events --> D
    B -- fetches notifications --> E
    D -- emits notification events --> C
    B -- updates state --> A
    A -- user marks as read --> B
    B -- updates backend --> E
```

**Legend:**
- **UI**: Vue components displaying notifications
- **Pinia Stores**: State management (notificationStore)
- **Services**: Centralized socket connection
- **Backend**: Real-time and REST API endpoints

**How it works:**
- UI components use the notificationStore for state/actions
- notificationStore subscribes to real-time events via centralizedSocketManager
- centralizedSocketManager maintains a single Socket.IO connection
- notificationStore fetches initial notifications from the backend API
- Real-time events from the backend are dispatched to the store, updating the UI
- User actions (e.g., mark as read) update both local state and backend
