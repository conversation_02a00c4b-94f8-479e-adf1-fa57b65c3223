#!/usr/bin/env pwsh

Write-Host "Testing AI tag display fix..." -ForegroundColor Green

# Start the development servers if not already running
Write-Host "Checking if development servers are running..." -ForegroundColor Cyan

# Test if frontend server is running
try {
    $frontendTest = Invoke-WebRequest -Uri "http://localhost:5173" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
    Write-Host "✓ Frontend server is running on port 5173" -ForegroundColor Green
} catch {
    Write-Host "✗ Frontend server not running on port 5173" -ForegroundColor Red
    Write-Host "Please run 'npm run dev' in the frontend directory" -ForegroundColor Yellow
    exit 1
}

# Test if backend server is running
try {
    $backendTest = Invoke-WebRequest -Uri "http://localhost:3000/health" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
    Write-Host "✓ Backend server is running on port 3000" -ForegroundColor Green
} catch {
    Write-Host "✗ Backend server not running on port 3000" -ForegroundColor Red
    Write-Host "Please run 'npm run dev' in the backend directory" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "=== AI Tag Display Fix Test Instructions ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Open the application at http://localhost:5173" -ForegroundColor White
Write-Host "2. Click the 'Debug Report' button (usually in the bottom-right corner)" -ForegroundColor White
Write-Host "3. Click 'Voice Recording' tab" -ForegroundColor White
Write-Host "4. Record a voice message mentioning authentication or login issues" -ForegroundColor White
Write-Host "5. Wait for AI analysis to complete" -ForegroundColor White
Write-Host "6. Check that tags are displayed with proper text (not just placeholder icons)" -ForegroundColor White
Write-Host ""
Write-Host "Expected behavior:" -ForegroundColor Green
Write-Host "- Predefined tags (error, fix-needed, urgent) should show their display names" -ForegroundColor Green
Write-Host "- AI-suggested tags (authentication, login-flow) should show their names" -ForegroundColor Green
Write-Host "- NO console errors about 'Tag with no display name'" -ForegroundColor Green
Write-Host ""
Write-Host "If issues persist, check the browser console for any remaining errors." -ForegroundColor Cyan

# Additional check - look for TypeScript compilation errors
Write-Host ""
Write-Host "Checking for TypeScript compilation errors..." -ForegroundColor Cyan
cd c:\Code\MUNygo\frontend
$tscResult = npm run build 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ TypeScript compilation successful" -ForegroundColor Green
} else {
    Write-Host "✗ TypeScript compilation errors detected:" -ForegroundColor Red
    Write-Host $tscResult -ForegroundColor Red
}

Write-Host ""
Write-Host "Test script completed. Please verify the fix manually in the browser." -ForegroundColor Yellow
