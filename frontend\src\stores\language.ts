import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Locale } from 'vue-i18n'
import { i18n } from '@/i18n'

export type Language = Locale

export const useLanguageStore = defineStore('language', () => {
  const currentLanguage = ref<Language>('fa') // Default to Persian
  
  const isRTL = computed(() => currentLanguage.value === 'fa')
  
  const setLanguage = (lang: Language) => {
    currentLanguage.value = lang
    localStorage.setItem('munygo-language', lang)
    
    // Update document direction and language
    document.documentElement.dir = lang === 'fa' ? 'rtl' : 'ltr'
    document.documentElement.lang = lang    // Force body direction as well
    document.body.dir = lang === 'fa' ? 'rtl' : 'ltr'
      // Update i18n locale
    ;(i18n.global as any).locale.value = lang
  }
  
  const initializeLanguage = () => {
    const savedLang = localStorage.getItem('munygo-language') as Language
    if (savedLang && ['fa', 'en'].includes(savedLang)) {
      setLanguage(savedLang)
    } else {
      setLanguage('fa') // Default to Persian
    }
  }
  
  return {
    currentLanguage,
    isRTL,
    setLanguage,
    initializeLanguage
  }
})


