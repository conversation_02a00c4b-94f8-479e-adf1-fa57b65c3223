<template>
  <div class="profile-view-enhanced">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="profile-header">
          <div class="avatar-section">
            <div class="avatar-container">
              <n-avatar 
                :size="avatarSize" 
                :src="user?.profile?.avatarUrl || undefined"
                class="profile-avatar"
                round
              >
                <n-icon :size="avatarIconSize">
                  <UserOutlined />
                </n-icon>
              </n-avatar>
              <n-button 
                text 
                class="change-avatar-btn" 
                size="small"
                @click="handleAvatarChange"
                :aria-label="t('profile.changePhoto')"
              >
                <template #icon>
                  <n-icon><CameraOutlined /></n-icon>
                </template>
              </n-button>
            </div>
          </div>
          
          <div class="profile-info">
            <h1 class="hero-title">{{ username }}</h1>
            <p class="hero-subtitle">{{ maskEmail(user?.email) }}</p>
            
            <div class="profile-badges">
              <n-tag 
                :type="reputationTagType" 
                :size="reputationTagSize"
                data-testid="reputation-level"
                class="reputation-tag"
              >
                <template #icon>
                  <n-icon><StarOutlined /></n-icon>
                </template>
                {{ reputationDisplayText }}
              </n-tag>
            </div>
            
            <div class="member-info">
              <n-icon class="member-icon"><CalendarOutlined /></n-icon>
              <span>{{ t('profile.memberSince', { date: joinDateDisplay }) }}</span>
            </div>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="quick-stats">
          <div class="stat-card" tabindex="0" role="button" @click="goToMyOffers">
            <div class="stat-icon reputation">
              <n-icon size="20"><TrophyOutlined /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ user?.reputationScore || 0 }}</div>
              <div class="stat-label">{{ t('profile.reputationScore') }}</div>
            </div>
          </div>
          
          <div class="stat-card" tabindex="0" role="button" @click="goToMyOffers">
            <div class="stat-icon offers">
              <n-icon size="20"><ShopOutlined /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ activeOffersCount }}</div>
              <div class="stat-label">{{ t('profile.activeOffers') }}</div>
            </div>
          </div>
          
          <div class="stat-card" tabindex="0" role="button" @click="goToTransactionHistory">
            <div class="stat-icon transactions">
              <n-icon size="20"><CheckCircleOutlined /></n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ completedTransactionsCount }}</div>
              <div class="stat-label">{{ t('profile.completedTransactions') }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Content -->
    <div class="profile-content">
      <!-- Account Status Section -->
      <section class="content-section">
        <div class="section-header">
          <h2 class="section-title">{{ t('profile.accountStatus') }}</h2>
        </div>
        
        <div class="status-grid">
          <!-- Email Status -->
          <div class="status-card">
            <div class="status-header">
              <div class="status-icon email">
                <n-icon size="24"><MailOutlined /></n-icon>
              </div>
              <div class="status-info">
                <h4 class="status-title">{{ t('profile.emailVerification') }}</h4>
                <!-- Custom RTL-safe verification badge -->
                <div 
                  class="custom-verification-badge" 
                  :class="{ 'verified': user?.emailVerified, 'unverified': !user?.emailVerified }"
                >
                  <div class="badge-icon">
                    <n-icon size="14">
                      <CheckCircleOutlined v-if="user?.emailVerified" />
                      <ClockCircleOutlined v-else />
                    </n-icon>
                  </div>
                  <span class="badge-text">
                    {{ user?.emailVerified ? t('profile.verified') : t('profile.pendingVerification') }}
                  </span>
                </div>
              </div>
            </div>
            <n-button 
              v-if="!user?.emailVerified" 
              text 
              type="primary" 
              size="small"
              class="status-action"
              @click="resendVerificationEmail"
              :loading="loadingResendEmail"
            >
              {{ t('profile.resendEmail') }}
            </n-button>
          </div>

          <!-- Phone Status -->
          <div class="status-card">
            <div class="status-header">
              <div class="status-icon phone">
                <n-icon size="24"><PhoneOutlined /></n-icon>
              </div>
              <div class="status-info">
                <h4 class="status-title">{{ t('profile.phoneVerification') }}</h4>
                <!-- Custom RTL-safe verification badge -->
                <div 
                  class="custom-verification-badge" 
                  :class="{ 'verified': isPhoneVerified, 'unverified': !isPhoneVerified }"
                  :data-testid="isPhoneVerified ? 'phone-status-verified' : 'phone-status-unverified'"
                >
                  <div class="badge-icon">
                    <n-icon size="14">
                      <CheckCircleOutlined v-if="isPhoneVerified" />
                      <ClockCircleOutlined v-else />
                    </n-icon>
                  </div>
                  <span class="badge-text">
                    {{ isPhoneVerified ? t('profile.verified') : t('profile.notVerified') }}
                  </span>
                </div>
              </div>
            </div>
            <span v-if="isPhoneVerified && user?.phoneNumber" class="phone-number">
              {{ user.phoneNumber }}
            </span>
          </div>

          <!-- Security Score -->
          <div class="status-card security-card">
            <div class="status-header">
              <div class="status-icon security">
                <n-icon size="20"><SafetyOutlined /></n-icon>
              </div>
              <div class="status-info">
                <h4 class="status-title">{{ t('profile.securityScore') }}</h4>
                <div class="security-score">
                  <!-- Custom circular progress for better RTL support -->
                  <div class="custom-circular-progress">
                    <svg width="60" height="60" viewBox="0 0 60 60" class="progress-svg">
                      <circle
                        cx="30"
                        cy="30"
                        r="24"
                        fill="none"
                        stroke="rgba(255, 255, 255, 0.1)"
                        stroke-width="4"
                        class="progress-bg"
                      />
                      <circle
                        cx="30"
                        cy="30"
                        r="24"
                        fill="none"
                        :stroke="securityScoreColor"
                        stroke-width="4"
                        stroke-linecap="round"
                        :stroke-dasharray="150.8"
                        :stroke-dashoffset="150.8 * (1 - securityScore / 100)"
                        class="progress-bar"
                        transform="rotate(-90 30 30)"
                      />
                    </svg>
                    <div class="progress-text">
                      <span class="progress-percentage">{{ securityScore }}%</span>
                    </div>
                  </div>
                  <div class="security-details">
                    <span class="security-text">{{ securityScoreText }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="security-tips" v-if="securityScore < 100">
              <n-text depth="3" class="security-tip">
                {{ getSecurityTip() }}
              </n-text>
            </div>
          </div>
        </div>
      </section>

      <!-- Phone Verification Section -->
      <section v-if="!isPhoneVerified" class="content-section">
        <div class="section-header">
          <h2 class="section-title">{{ t('profile.completePhoneVerification') }}</h2>
        </div>
        
        <div class="verification-card">
          <div class="verification-intro">
            <n-alert
              type="info"
              :title="t('profile.whyVerifyPhone')"
              class="verification-info"
              :show-icon="false"
            >
              <div class="verification-benefits">
                <div class="benefit-item">
                  <n-icon class="benefit-icon"><CheckCircleOutlined /></n-icon>
                  <span>{{ t('profile.phoneVerificationBenefit') }}</span>
                </div>
                <div class="benefit-item">
                  <n-icon class="benefit-icon"><SafetyOutlined /></n-icon>
                  <span>{{ t('profile.increasedSecurityLevel') }}</span>
                </div>
                <div class="benefit-item">
                  <n-icon class="benefit-icon"><StarOutlined /></n-icon>
                  <span>{{ t('profile.higherTrustRating') }}</span>
                </div>
              </div>
            </n-alert>
          </div>

          <!-- Rate Limit Alerts -->
          <n-alert
            v-if="isRateLimitError()"
            type="error"
            :title="t('profile.rateLimitExceeded')"
            :closable="true"
            @close="clearMessages"
            class="rate-limit-alert"
            data-testid="rate-limit-alert"
          >
            <div v-if="blockTimeRemaining > 0">
              {{ t('profile.tooManyAttempts', { seconds: blockTimeRemaining }) }}
            </div>
            <div v-else-if="remainingAttempts !== null && remainingAttempts > 0">
              {{ t('profile.remainingAttempts', { count: remainingAttempts }) }}
            </div>
            <div v-else>
              {{ error }}
            </div>
          
          </n-alert>

          <n-alert
            v-else-if="blockedUntil && !error"
            type="info"
            class="blocked-alert"
          >
            {{ t('profile.waitBeforeRetrying', { seconds: blockTimeRemaining }) }}
            <div v-if="remainingAttempts !== null" style="margin-top: 4px;">
              {{ t('profile.remainingAttempts', { count: remainingAttempts }) }}
            </div>
          </n-alert>

          <n-spin :show="loadingRequest || loadingVerify">
            <!-- Phone Number Input Form -->
            <n-form
              v-show="!showOtpForm"
              ref="phoneFormRef"
              :model="phoneFormValue"
              :rules="phoneValidationRules"
              data-testid="phone-form"
              size="large"
              class="verification-form"
            >
              <n-form-item path="phoneNumber">
                <template #label>
                  <div class="form-label">
                    {{ t('profile.phoneNumberE164') }}
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="16" class="help-icon">
                          <QuestionCircleOutlined />
                        </n-icon>
                      </template>
                      {{ t('profile.phoneNumberHelpText') }}
                    </n-tooltip>
                  </div>
                </template>
                <n-input
                  ref="phoneInputRef"
                  v-model:value="phoneFormValue.phoneNumber"
                  :placeholder="t('profile.phoneNumberPlaceholder')"
                  data-testid="phone-input"
                  :disabled="loadingRequest || !!blockedUntil || remainingAttempts === 0"
                  size="large"
                  autocomplete="tel"
                  class="modern-input"
                >
                  <template #prefix>
                    <n-icon><PhoneOutlined /></n-icon>
                  </template>
                </n-input>
              </n-form-item>
              <n-form-item>
                <n-button
                  block
                  type="primary"
                  size="large"
                  data-testid="send-otp-button"
                  @click="requestOtp"
                  :disabled="loadingRequest || !!blockedUntil || remainingAttempts === 0"
                  :loading="loadingRequest"
                  class="modern-button"
                >
                  <template #icon>
                    <n-icon><SendOutlined /></n-icon>
                  </template>
                  {{ t('profile.sendVerificationCode') }}
                </n-button>
              </n-form-item>
            </n-form>

            <!-- OTP Input Form -->
            <n-form
              v-show="showOtpForm"
              ref="otpFormRef"
              :model="otpFormData"
              :rules="otpValidationRules"
              data-testid="otp-form"
              size="large"
              class="verification-form"
            >
              <div class="otp-info">
                <n-text depth="3">{{ t('profile.verifyingNumber') }}</n-text>
                <n-text strong>{{ lastSubmittedPhone }}</n-text>
              </div>
              
              <n-form-item path="otpCode">
                <template #label>
                  <div class="form-label">
                    {{ t('profile.verificationCode') }}
                  </div>
                </template>
                <n-input
                  ref="otpInputRef"
                  v-model:value="otpFormData.otpCode"
                  maxlength="6"
                  data-testid="otp-input"
                  :disabled="loadingVerify || !!blockedUntil || remainingAttempts === 0"
                  size="large"
                  :placeholder="t('profile.enterSixDigitCode')"
                  autocomplete="one-time-code"
                  class="modern-input otp-input"
                >
                  <template #prefix>
                    <n-icon><LockOutlined /></n-icon>
                  </template>
                </n-input>
              </n-form-item>
              
              <n-form-item>
                <n-space vertical size="medium" style="width: 100%;">
                  <n-button
                    block
                    type="primary"
                    size="large"
                    data-testid="verify-otp-button"
                    @click="verifyOtp"
                    :disabled="loadingVerify || !!blockedUntil || remainingAttempts === 0"
                    :loading="loadingVerify"
                    class="modern-button"
                  >
                    <template #icon>
                      <n-icon><CheckOutlined /></n-icon>
                    </template>
                    {{ t('profile.verifyCode') }}
                  </n-button>

                  <n-space size="small">
                    <n-button
                      style="flex: 1;"
                      @click="requestOtp"
                      :disabled="loadingRequest || !!blockedUntil || (remainingAttempts !== null && remainingAttempts === 0) || resendDisabledTime > 0"
                      :loading="loadingRequest"
                      data-testid="resend-otp-button"
                      class="secondary-button"
                    >
                      <template #icon>
                        <n-icon><ReloadOutlined /></n-icon>
                      </template>
                      {{ resendDisabledTime > 0 ? t('profile.resendCodeWait', { seconds: resendDisabledTime }) : t('profile.resendCode') }}
                    </n-button>
                    <n-button
                      style="flex: 1;"
                      @click="goBackToPhoneInput"
                      :disabled="loadingRequest || loadingVerify || !!blockedUntil || remainingAttempts === 0"
                      data-testid="change-number-button"
                      class="secondary-button"
                    >
                      <template #icon>
                        <n-icon><EditOutlined /></n-icon>
                      </template>
                      {{ t('profile.changeNumber') }}
                    </n-button>
                  </n-space>
                </n-space>
              </n-form-item>
            </n-form>
          </n-spin>
        </div>
      </section>

      <!-- Verified Success Message -->
      <section v-else-if="isPhoneVerified" class="content-section">
        <div class="success-card">
          <n-result
            status="success"
            :title="t('profile.phoneVerified')"
            :description="t('profile.phoneVerifiedSuccess')"
          >
            <template #icon>
              <n-icon color="#18a058" size="48">
                <CheckCircleOutlined />
              </n-icon>
            </template>
            <template #footer>
              <n-text depth="3">
                {{ t('profile.verifiedNumber', { number: user?.phoneNumber }) }}
              </n-text>
            </template>
          </n-result>
        </div>
      </section>

      <!-- Quick Actions Section -->
      <section class="content-section quick-actions-section">
        <div class="section-header">
          <h2 class="section-title">{{ t('profile.quickActions') }}</h2>
          <p class="section-description">{{ t('profile.quickActionsDescription') }}</p>
        </div>
        
        <div class="actions-grid">
          <n-card 
            class="action-card primary-action" 
            :hoverable="true"
            @click="goToCreateOffer"
            tabindex="0"
            role="button"
            :aria-label="t('profile.createNewOffer')"
          >
            <div class="action-content">
              <div class="action-icon primary">
                <n-icon size="24"><PlusOutlined /></n-icon>
              </div>
              <div class="action-text">
                <h3 class="action-title">{{ t('profile.createNewOffer') }}</h3>
                <p class="action-description">{{ t('profile.createOfferDescription') }}</p>
              </div>
              <n-icon class="action-arrow" size="16"><ArrowRightOutlined /></n-icon>
            </div>
          </n-card>
          
          <n-card 
            class="action-card" 
            :hoverable="true"
            @click="goToMyOffers"
            tabindex="0"
            role="button"
            :aria-label="t('profile.viewMyOffers')"
          >
            <div class="action-content">
              <div class="action-icon">
                <n-icon size="20"><FileTextOutlined /></n-icon>
              </div>
              <div class="action-text">
                <h3 class="action-title">{{ t('profile.viewMyOffers') }}</h3>
                <p class="action-description">{{ t('profile.manageActiveOffers') }}</p>
              </div>
              <n-icon class="action-arrow" size="16"><ArrowRightOutlined /></n-icon>
            </div>
          </n-card>
          
          <n-card 
            class="action-card" 
            :hoverable="true"
            @click="goToBrowseOffers"
            tabindex="0"
            role="button"
            :aria-label="t('profile.browseOffers')"
          >
            <div class="action-content">
              <div class="action-icon">
                <n-icon size="20"><SearchOutlined /></n-icon>
              </div>
              <div class="action-text">
                <h3 class="action-title">{{ t('profile.browseOffers') }}</h3>
                <p class="action-description">{{ t('profile.findExchangeOffers') }}</p>
              </div>
              <n-icon class="action-arrow" size="16"><ArrowRightOutlined /></n-icon>
            </div>
          </n-card>
        </div>
      </section>
    </div>

    <!-- Loading Overlay -->
    <div v-if="isLoadingProfile" class="profile-loading-overlay" @click="clearLoadingState">
      <div class="loading-content" @click.stop>
        <n-icon size="48"><LoadingOutlined /></n-icon>
        <p>{{ t('profile.loadingProfile') }}</p>
        <n-button text @click="clearLoadingState" style="margin-top: 16px; color: #666;">
          {{ t('profile.clickIfStuck') }}
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import {
  NSpin, NTag, NAlert, NForm, NFormItem, NInput, NButton,
  NIcon, NTooltip, NText, NAvatar, NSpace, NCard,
  NResult,
  useMessage, type FormInst, type InputInst
} from 'naive-ui';
import {
  UserOutlined, CameraOutlined, StarOutlined, CalendarOutlined,
  MailOutlined, PhoneOutlined, SafetyOutlined,
  CheckCircleOutlined, ClockCircleOutlined, QuestionCircleOutlined,
  SendOutlined, LockOutlined, CheckOutlined, ReloadOutlined, EditOutlined,
  TrophyOutlined, ShopOutlined,
  PlusOutlined, FileTextOutlined, SearchOutlined, LoadingOutlined,
  ArrowRightOutlined
} from '@vicons/antd';
import { useAuthStore } from '@/stores/auth';
import { useMyOffersStore } from '@/stores/myOffersStore';
import apiClient from '@/services/apiClient';
import { storeToRefs } from 'pinia';
import type { OtpSuccessResponse, VerifySuccessResponse, RateLimitInfo } from '@/types/api';
import { handleError as handleApiError } from '@/utils/errorHandler';
import { useTranslation } from '@/composables/useTranslation';

const router = useRouter();
const authStore = useAuthStore();
const myOffersStore = useMyOffersStore();
const { user } = storeToRefs(authStore);
const { myOffers } = storeToRefs(myOffersStore);
const message = useMessage();
const { t } = useTranslation();

// --- Refs for form instances ---
const phoneFormRef = ref<FormInst | null>(null);
const otpFormRef = ref<FormInst | null>(null);
const phoneInputRef = ref<InputInst | null>(null);
const otpInputRef = ref<InputInst | null>(null);
const lastSubmittedPhone = ref<string | null>(null);

// --- Reactive form data ---
const phoneFormValue = reactive({ phoneNumber: '' });
const otpFormData = reactive({ otpCode: '' });
const otpFormValue = otpFormData;

// --- Loading and state refs ---
const loadingProfile = ref(true);
const loadingRequest = ref(false);
const loadingVerify = ref(false);
const loadingResendEmail = ref(false);
const showOtpForm = ref(false);
const error = ref<string | null>(null);
const lastStatus = ref<string | null>(null);

// --- Rate Limiting Refs ---
const remainingAttempts = ref<number | null>(null);
const blockedUntil = ref<number | null>(null);
const resendDisabledTime = ref(0);
const blockTimerInterval = ref<number | null>(null);
const resendTimerInterval = ref<number | null>(null);
// Removed unused blockTimer ref to avoid confusion and potential resource leaks.
const currentTime = ref(Date.now());

// --- Navigation functions ---
function goToCreateOffer() {
  router.push({ name: 'CreateOffer' });
}

function goToMyOffers() {
  router.push({ name: 'MyOffers' });
}

function goToBrowseOffers() {
  router.push({ name: 'BrowseOffers' });
}

function goToTransactionHistory() {
  // Navigate to transaction history if available, otherwise to my offers
  router.push({ name: 'MyOffers' });
}

// --- Utility functions for UI improvements ---
function handleAvatarChange() {
  // Placeholder for avatar change functionality
  message.info(t('profile.avatarChangeComingSoon'));
}

function maskEmail(email: string | undefined): string {
  if (!email || typeof email !== 'string') return '';
  
  // Handle emails without '@' symbol
  const atIndex = email.indexOf('@');
  if (atIndex === -1) return '*'.repeat(Math.max(1, email.length - 2)) + email.slice(-1);
  
  const username = email.slice(0, atIndex);
  const domain = email.slice(atIndex + 1);
  
  // Handle empty username or domain
  if (!username || !domain) return '*'.repeat(Math.max(1, email.length - 2)) + email.slice(-1);
  
  // Always mask at least one character, regardless of username length
  if (username.length === 1) {
    return '*@' + domain;
  } else if (username.length === 2) {
    return username.slice(0, 1) + '*@' + domain;
  } else {
    // For usernames 3+ characters: show first 2, mask middle, show last 1
    const maskedUsername = username.slice(0, 2) + '*'.repeat(username.length - 3) + username.slice(-1);
    return `${maskedUsername}@${domain}`;
  }
}

function getSecurityTip(): string {
  if (!user.value?.emailVerified) {
    return t('profile.securityTips.verifyEmail');
  }
  if (!user.value?.phoneVerified) {
    return t('profile.securityTips.verifyPhone');
  }
  return t('profile.securityTips.enableTwoFactor');
}

// --- Computed properties ---
const isPhoneVerified = computed(() => !!user.value?.phoneVerified);

// Responsive avatar sizing
const avatarSize = computed(() => {
  // Mobile-first sizing - will be enhanced with CSS for larger screens
  return 100; // Smaller on mobile for better fit
});

const avatarIconSize = computed(() => {
  return 36; // Proportional to avatar size
});

const reputationTagSize = computed(() => {
  return 'medium' as const; // Better for mobile touch targets
});

// Force reactive computation for loading state
const isLoadingProfile = computed(() => {
  const loading = loadingProfile.value;
  return loading;
});

const blockTimeRemaining = computed(() => {
  if (!blockedUntil.value || blockedUntil.value <= currentTime.value) return 0;
  return Math.ceil((blockedUntil.value - currentTime.value) / 1000);
});

const securityScore = computed(() => {
  let score = 0;
  if (user.value?.emailVerified) score += 50;
  if (user.value?.phoneVerified) score += 50;
  return score;
});

const securityScoreColor = computed(() => {
  const score = securityScore.value;
  if (score >= 100) return '#18a058'; // Green
  if (score >= 50) return '#f0a020'; // Orange
  return '#d03050'; // Red
});

const securityScoreText = computed(() => {
  const score = securityScore.value;
  if (score >= 100) return t('profile.excellentSecurity');
  if (score >= 50) return t('profile.goodSecurity');
  return t('profile.basicSecurity');
});

const computedReputationLevel = computed(() => {
  let level = user.value?.reputationLevel;
  
  if (!level) {
    if (user.value?.emailVerified && user.value?.phoneVerified) {
      level = 3;
    } else if (user.value?.emailVerified || user.value?.phoneVerified) {
      level = 2;
    } else {
      level = 1;
    }
  }
  
  return level;
});

const reputationDisplayText = computed(() => {
  switch (computedReputationLevel.value) {
    case 1: return t('profile.bronze');
    case 2: return t('profile.silver');
    case 3: return t('profile.gold');
    case 4: return t('profile.platinum');
    case 5: return t('profile.diamond');
    default: return t('profile.bronze');
  }
});

const reputationTagType = computed(() => {
  switch (computedReputationLevel.value) {
    case 1: return 'default';
    case 2: return 'info';
    case 3: return 'success';
    case 4: return 'warning';
    case 5: return 'error';
    default: return 'default';
  }
});

// Add computed for rate limit error check if needed by template
const isRateLimitError = () => {
    return !!error.value && (
        error.value.includes('Rate limit exceeded') ||
        error.value.includes('Too many attempts') ||
        error.value.includes('Too Many Requests')
    );
};

// --- Profile related computed properties ---
const username = computed(() => {
  return user.value?.username || user.value?.email?.split('@')[0] || t('profile.defaultUsername');
});

const joinDateDisplay = computed(() => {
  if (!user.value?.createdAt) {
    return t('profile.defaultJoinDate');
  }
  try {
    return new Date(user.value.createdAt).toLocaleDateString('en-CA', {
      year: 'numeric', month: 'long', day: 'numeric'
    });
  } catch (e) {
    console.error('Error formatting date:', e);
    return t('profile.defaultJoinDate');
  }
});

// --- Statistics computed properties ---
const activeOffersCount = computed(() => {
  if (!myOffers.value || !Array.isArray(myOffers.value)) {
    return 0;
  }
  return myOffers.value.filter(offer => offer.status === 'ACTIVE').length;
});

const completedTransactionsCount = computed(() => {
  if (!myOffers.value || !Array.isArray(myOffers.value)) {
    return 0;
  }
  
  // Count completed transactions from interests on user's offers
  let completedCount = 0;
  myOffers.value.forEach(offer => {
    if (offer.interests && Array.isArray(offer.interests)) {
      offer.interests.forEach(interest => {
        if (interest.transactionStatus === 'COMPLETED') {
          completedCount++;
        }
      });
    }
  });
  
  return completedCount;
});

// --- Validation Rules ---
const phoneValidationRules = {
    phoneNumber: [
        { required: true, message: t('profile.validation.phoneNumberRequired'), trigger: ['input', 'blur'] },
        // Add E.164 format validation if possible with regex or a custom validator
        {
            pattern: /^\+[1-9]\d{1,14}$/, // Basic E.164 check
            message: t('profile.validation.phoneNumberFormat'),
            trigger: ['input', 'blur']
        }
    ]
};

const otpValidationRules = {
  otpCode: [
    { required: true, message: t('profile.validation.otpRequired'), trigger: ['input', 'blur'] },
    { len: 6, message: t('profile.validation.otpLength'), trigger: ['input', 'blur'] },
    { pattern: /^\d{6}$/, message: t('profile.validation.otpDigitsOnly'), trigger: ['input', 'blur'] }
    // Removed the Zod validator for simplicity, relying on Naive rules
  ]
};

// --- Helper Methods ---
const forceHideLoading = async () => {
  loadingProfile.value = false;
  await nextTick();
  // Double check and force hide if still showing
  setTimeout(() => {
    if (loadingProfile.value) {
      loadingProfile.value = false;
    }
  }, 100);
};

// --- Lifecycle Hooks ---
onMounted(async () => {
  // If user data is already available, don't show loading
  if (user.value && user.value.id) {
    loadingProfile.value = false;
    // Fetch user's offers for statistics
    try {
      await myOffersStore.fetchMyOffers();
    } catch (err) {
      console.error('Failed to fetch offers for statistics:', err);
    }
    return;
  }

  loadingProfile.value = true;
  error.value = null;

  // Immediate check - if user becomes available quickly, hide loading
  const immediateCheck = setTimeout(() => {
    if (user.value && user.value.id && loadingProfile.value) {
      forceHideLoading();
    }
  }, 100);

  // Safety timeout to ensure loading doesn't get stuck
  const loadingTimeout = setTimeout(() => {
    loadingProfile.value = false;
  }, 10000); // 10 second timeout

  try {
    const authStore = useAuthStore();
    await authStore.fetchUserProfile();
    
    // Fetch user's offers for statistics once profile is loaded
    if (user.value && user.value.id) {
      await myOffersStore.fetchMyOffers();
    }  } catch (err) {
    try {
      // Properly handle the error and assign the returned message string
      error.value = handleApiError(err, message, t('profile.messages.loadProfileError'));
    } catch (handlerError) {
      error.value = t('profile.messages.loadProfileError');
      message.error(t('profile.messages.loadProfileError'));
    }
  } finally {
    clearTimeout(loadingTimeout); // Clear the safety timeout
    clearTimeout(immediateCheck); // Clear the immediate check timeout
    await forceHideLoading();
  }
});

// --- Watchers ---
watch(user, (newUser, oldUser) => {
  // Reset only if the user actually changes (e.g., logs out and logs in as someone else, or logs out)
  // or if the phone verification status changes externally
  if (newUser?.id !== oldUser?.id || newUser?.phoneVerified !== oldUser?.phoneVerified) {
      resetVerification();
      // If the user becomes verified, ensure the phone number is displayed if available
      if (newUser?.phoneVerified && !phoneFormValue.phoneNumber && newUser.phoneNumber) {
          phoneFormValue.phoneNumber = newUser.phoneNumber; // Pre-fill if needed, though usually not necessary as form hides
      }
  }
}, { deep: true }); // Use deep watch if user object structure is complex

// Debug watcher for loading state
watch(loadingProfile, () => {
}, { immediate: true });

// Additional safety check - if user data exists but loading is still true
watch(user, (newUser) => {
  if (newUser && loadingProfile.value) {
    setTimeout(() => {
      if (loadingProfile.value) {
        forceHideLoading();
      }
    }, 2000);
  }
}, { immediate: true });

// --- Utility Functions ---
function clearMessages() {
  error.value = null;
}

// Force clear loading state (for debugging)
function clearLoadingState() {
  forceHideLoading();
}

// Make clearLoadingState available on window for debugging
if (typeof window !== 'undefined') {
  // Removed window debug method for production cleanliness
}

// --- Timer Functions ---
function startResendTimer() {
  // Clear any existing timer
  if (resendTimerInterval.value) {
    clearInterval(resendTimerInterval.value);
    resendTimerInterval.value = null;
  }
  resendDisabledTime.value = 30; // 30 seconds for example

  // Start a new timer
  resendTimerInterval.value = window.setInterval(() => {
    resendDisabledTime.value--;

    // Clear the timer when reaching 0
    if (resendDisabledTime.value <= 0) {
      clearInterval(resendTimerInterval.value ?? undefined);
      resendTimerInterval.value = null;
    }
  }, 1000);
}


function clearTimers() {
  if (blockTimerInterval.value) {
    clearInterval(blockTimerInterval.value);
    blockTimerInterval.value = null;
  }
  if (resendTimerInterval.value) {
    clearInterval(resendTimerInterval.value);
    resendTimerInterval.value = null;
  }
}

function startBlockTimer() {
  if (blockTimerInterval.value) {
    clearInterval(blockTimerInterval.value);
    blockTimerInterval.value = null;
  }

  if (blockedUntil.value && blockedUntil.value > Date.now()) {
    currentTime.value = Date.now(); // Initialize currentTime

    blockTimerInterval.value = window.setInterval(() => {
      // ---> FIX: Update currentTime ref <---
      currentTime.value = Date.now();
      // ---------------------------------

      // Use currentTime for calculation
      const remaining = Math.max(0, blockedUntil.value! - currentTime.value);

      if (remaining <= 0) {
        clearInterval(blockTimerInterval.value!);
        blockTimerInterval.value = null;
        blockedUntil.value = null;
        remainingAttempts.value = null;
        error.value = null;
      }
    }, 1000);
  } else {
    blockedUntil.value = null;
    remainingAttempts.value = null;
    error.value = null;
  }
}

// --- API Call Functions ---
async function requestOtp() {
  error.value = null; // Clear previous errors

  try {
    await phoneFormRef.value?.validate();
  } catch (validationError: any) {
    return; // Validation failed, Naive UI shows messages
  }

  // Store the submitted phone number
  lastSubmittedPhone.value = phoneFormValue.phoneNumber;

  loadingRequest.value = true;
  try {
    const response = await apiClient.post<OtpSuccessResponse | RateLimitInfo>('/auth/phone/send-otp', {
      phoneNumber: phoneFormValue.phoneNumber,
    });

    // THIS BLOCK MOVED: Only process response.data on success
    if ('remainingAttempts' in response.data) {
        remainingAttempts.value = response.data.remainingAttempts ?? null; // Use nullish coalescing
        if (response.data.blockedUntil) { // Check if blockedUntil is present
          blockedUntil.value = new Date(response.data.blockedUntil).getTime(); // Convert to timestamp
          startBlockTimer();
        } else {
          blockedUntil.value = null;
        }
    } else if ('message' in response.data) { // Assuming OtpSuccessResponse has a message
        // Handle non-rate-limit success response if necessary, though message.success is primary
    }


    message.success(response.data.message || t('profile.messages.otpSentSuccess'));
    showOtpForm.value = true;
    startResendTimer();
    otpFormData.otpCode = '';
    nextTick(() => {
      otpInputRef.value?.focus();
    });
    // END MOVED BLOCK
  } catch (err: any) {
    console.error('[TEST_DEBUG] apiClient.post for send-otp FAILED:', err);
    try {
      // Properly handle the error and assign the returned message string
      error.value = handleApiError(err, message, t('profile.messages.sendOtpError'));
    } catch (handlerError) {
      console.error('[ProfileView] Error in handleApiError:', handlerError);      
      error.value = t('profile.messages.sendOtpError');
      message.error(t('profile.messages.sendOtpError'));
    }
    // Extract rate limit info directly from err if it's a 429 error
    if (err?.response?.status === 429 && err.response.data) {
      remainingAttempts.value = err.response.data.remainingAttempts ?? null;
      if (err.response.data.blockedUntil) {
        blockedUntil.value = new Date(err.response.data.blockedUntil).getTime();
        startBlockTimer();
      } else {
        blockedUntil.value = null;
      }
      
      // ADD THIS LINE: Show the OTP form even when rate limited, if we have a number
      if (lastSubmittedPhone.value) {
        showOtpForm.value = true;
      }
    }
  } finally {
    console.log('[TEST_DEBUG] requestOtp finally block');
    loadingRequest.value = false;
  }
}

async function verifyOtp() {
  error.value = null;
  lastStatus.value = null; 

  try {
    await otpFormRef.value?.validate();
  } catch (validationError: any) {
    console.log('Naive UI OTP validation failed:', validationError);
    return; 
  }

  loadingVerify.value = true; 
  try {
    const response = await apiClient.post<VerifySuccessResponse | RateLimitInfo>('/auth/phone/verify-otp', {
        otpCode: otpFormData.otpCode 
    });

    // THIS BLOCK MOVED: Only process response.data on success
    if ('remainingAttempts' in response.data) {
        remainingAttempts.value = response.data.remainingAttempts ?? null; // Use nullish coalescing
        // Correctly handle blockedUntil if it's a string date
        if (response.data.blockedUntil && typeof response.data.blockedUntil === 'string') {
            blockedUntil.value = new Date(response.data.blockedUntil).getTime();
        } else {
            blockedUntil.value = response.data.blockedUntil ?? null; // Assuming it might already be a number or null
        }
        if (blockedUntil.value) startBlockTimer();
    }

    let verifiedPhoneNumber: string = phoneFormValue.phoneNumber;
    if ('phoneNumber' in response.data && typeof response.data.phoneNumber === 'string') {
      verifiedPhoneNumber = response.data.phoneNumber;
    }

    message.success(response.data.message || t('profile.messages.phoneVerifiedSuccess'));
    authStore.updatePhoneVerificationStatus(true, verifiedPhoneNumber); 
    showOtpForm.value = false; 
    clearTimers(); 
    // END MOVED BLOCK
  }   catch (err: any) {
    try {      // Properly handle the error and assign the returned message string
      error.value = handleApiError(err, message, t('profile.messages.verifyOtpError'));
    } catch (handlerError) {
      console.error('[ProfileView] Error in handleApiError:', handlerError);
      error.value = t('profile.messages.verifyOtpError');
      message.error(t('profile.messages.verifyOtpError'));
    }
  
    // Optionally extract rate limit info directly from err if present
    if (err?.response?.data?.remainingAttempts !== undefined) {
      remainingAttempts.value = err.response.data.remainingAttempts ?? null;
      // Correctly parse the blockedUntil ISO string to a timestamp
      if (err.response.data.blockedUntil && typeof err.response.data.blockedUntil === 'string') {
        blockedUntil.value = new Date(err.response.data.blockedUntil).getTime();
      } else {
        blockedUntil.value = err.response.data.blockedUntil ?? null; // Fallback if not a string
      }
      if (blockedUntil.value) {
        startBlockTimer();
      }
    }
  } finally {
    loadingVerify.value = false; 
  }
}

// --- Go Back to Phone Input ---
// --- Go Back to Phone Input ---
function goBackToPhoneInput() {
    // We need to remove the condition that keeps the user in the OTP form
    // when they click "Change Number". The user explicitly wants to go back.
    
    showOtpForm.value = false;
    otpFormValue.otpCode = ''; 
    error.value = null;
    lastStatus.value = null;
    nextTick(() => {
        phoneInputRef.value?.focus();
    });
}


// --- Reset Verification (called on user change/logout) ---
function resetVerification() {
  // console.log('[RESET_DEBUG] resetVerification called'); // Keep logs if needed for debugging
  showOtpForm.value = false; // Start with phone input
  phoneFormValue.phoneNumber = '';
  otpFormValue.otpCode = ''; // Use otpFormValue here
  error.value = null;
  lastStatus.value = null;
  remainingAttempts.value = null;
  blockedUntil.value = null;
  clearTimers(); // Clear interval timers first
  // Clear blockTimerInterval specifically if it exists
  if (blockTimerInterval.value) {
    clearInterval(blockTimerInterval.value);
    blockTimerInterval.value = null;
  }

  // Use nextTick to allow DOM updates from showOtpForm change
  nextTick(() => {
    // console.log('[RESET_DEBUG] nextTick in resetVerification');
    // ---> Explicitly check if refs exist before calling methods <---
    if (phoneFormRef.value) {
      // console.log('[RESET_DEBUG] Restoring phoneFormRef validation');
      phoneFormRef.value.restoreValidation();
    } else {
      // console.log('[RESET_DEBUG] phoneFormRef is null in resetVerification nextTick');
    }

    // Check otpFormRef as well, although it should be null if showOtpForm is false
    if (otpFormRef.value) {
      // console.log('[RESET_DEBUG] Restoring otpFormRef validation');
      otpFormRef.value.restoreValidation();
    } else {
      // console.log('[RESET_DEBUG] otpFormRef is null in resetVerification nextTick');
    }  });
}

// --- Resend Verification Email ---
async function resendVerificationEmail() {
  loadingResendEmail.value = true;
  
  try {
    const response = await apiClient.post('/auth/resend-verification-email');
    
    message.success(response.data.message || t('profile.messages.emailSentSuccess'));
  } catch (error: any) {
    const errorMessage = handleApiError(error, message, t('profile.messages.resendEmailError'));
    console.error('Resend email error:', errorMessage);
  } finally {
    loadingResendEmail.value = false;
  }
}

</script>

<style scoped>
/* Main Container */
.profile-view-enhanced {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Light theme */
[data-theme="light"] .profile-view-enhanced {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
}

/* Dark theme */
[data-theme="dark"] .profile-view-enhanced {
  background: linear-gradient(135deg, #1a1b2e 0%, #16213e 50%, #0f1419 100%);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  color: white;
  padding: 2rem 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.avatar-section {
  position: relative;
}

/* Avatar Section Improvements */
.avatar-section {
  position: relative;
  margin-bottom: 1rem;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.profile-avatar {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 3px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.profile-avatar:hover {
  transform: scale(1.02);
}

.change-avatar-btn {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 44px; /* Touch target */
  min-width: 44px;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.change-avatar-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.profile-info {
  text-align: center;
  flex: 1;
}

.hero-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  opacity: 0.85;
  font-weight: 400;
}

.profile-badges {
  margin-bottom: 1rem;
}

.reputation-tag {
  font-weight: 600;
  letter-spacing: 0.5px;
}

.member-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 0.85rem;
  opacity: 0.8;
  margin: 0;
}

.member-icon {
  flex-shrink: 0;
}

/* Quick Stats - Mobile First */
.quick-stats {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
  min-height: 60px;
}

.stat-card:hover,
.stat-card:focus {
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.stat-card:active {
  transform: translateY(0);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: white;
}

.stat-icon.reputation {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.offers {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-icon.transactions {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-content {
  color: white;
  flex: 1;
  text-align: left;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 0.125rem;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.8;
  line-height: 1.2;
}

/* Main Content */
.profile-content {
  padding: 0 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.content-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .content-section {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.section-header {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
}

[data-theme="dark"] .section-title {
  color: rgba(255, 255, 255, 0.9);
}

/* Status Section - Mobile Optimized */
.status-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.25rem;
}

.status-card {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.25rem;
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  position: relative;
}

[data-theme="dark"] .status-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.status-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: white;
}

.status-icon.email {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.status-icon.phone {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.status-icon.security {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.status-info {
  flex: 1;
  min-width: 0;
}

.status-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  line-height: 1.3;
}

[data-theme="dark"] .status-title {
  color: rgba(255, 255, 255, 0.9);
}

/* Custom RTL-safe verification badge */
.custom-verification-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.custom-verification-badge.verified {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.2);
}

.custom-verification-badge.unverified {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border-color: rgba(245, 158, 11, 0.2);
}

[data-theme="dark"] .custom-verification-badge.verified {
  background: rgba(16, 185, 129, 0.15);
  color: #34d399;
}

[data-theme="dark"] .custom-verification-badge.unverified {
  background: rgba(245, 158, 11, 0.15);
  color: #fbbf24;
}

.badge-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.badge-text {
  white-space: nowrap;
  line-height: 1;
}

/* RTL support for verification badge */
[dir="rtl"] .custom-verification-badge {
  flex-direction: row-reverse;
}

.phone-number {
  font-size: 0.9rem;
  opacity: 0.7;
  font-family: monospace;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.5rem;
  border-radius: 8px;
  letter-spacing: 0.5px;
  margin-top: 0.5rem;
  align-self: flex-start;
}

[data-theme="dark"] .phone-number {
  background: rgba(255, 255, 255, 0.05);
}

/* Custom circular progress for security score */
.security-score {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.75rem;
}

.custom-circular-progress {
  position: relative;
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.progress-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-bg {
  opacity: 0.2;
}

[data-theme="dark"] .progress-bg {
  stroke: rgba(255, 255, 255, 0.1);
}

.progress-bar {
  transition: stroke-dashoffset 0.6s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-percentage {
  font-size: 0.9rem;
  font-weight: 700;
  line-height: 1;
}

.security-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.security-text {
  font-size: 0.85rem;
  opacity: 0.8;
  font-weight: 500;
}

.security-tips {
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(245, 158, 11, 0.08);
  border-radius: 8px;
  border-left: 3px solid #f59e0b;
}

[data-theme="dark"] .security-tips {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: #fbbf24;
}

.security-tip {
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Improved status action button */
.status-action {
  margin-top: 0.5rem;
  align-self: flex-start;
  font-size: 0.85rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  height: auto;
  min-height: 36px;
}



/* Verification Section - Enhanced UX */
.verification-card {
  padding: 0;
}

.verification-intro {
  margin-bottom: 1.5rem;
}

.verification-info {
  border-radius: 12px;
  border: none;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

[data-theme="dark"] .verification-info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.1) 100%);
}

.verification-benefits {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.benefit-icon {
  color: #10b981;
  flex-shrink: 0;
}

.rate-limit-alert,
.blocked-alert {
  margin-bottom: 1.5rem;
  border-radius: 12px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.help-icon {
  opacity: 0.6;
  cursor: help;
}

.modern-input {
  border-radius: 8px;
  font-size: 1rem;
}

.modern-button {
  border-radius: 8px;
  font-weight: 600;
  height: 48px; /* Better touch target */
}

.secondary-button {
  border-radius: 8px;
  height: 44px; /* Touch target */
}

.otp-info {
  text-align: center;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

[data-theme="light"] .otp-info {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

[data-theme="dark"] .otp-info {
  background: rgba(30, 32, 48, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.otp-input {
  text-align: center;
  letter-spacing: 2px;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Success Section */
.success-card {
  padding: 0;
}

/* Actions Section - Card-based Design */
.quick-actions-section {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
}

[data-theme="dark"] .quick-actions-section {
  background: linear-gradient(135deg, rgba(26, 27, 46, 0.8) 0%, rgba(16, 20, 25, 0.8) 100%);
}

.section-description {
  font-size: 0.9rem;
  opacity: 0.7;
  margin: 0.5rem 0 0 0;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.action-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

[data-theme="dark"] .action-card {
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.action-card:active {
  transform: translateY(0);
}

.action-card.primary-action {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
}

.action-card.primary-action:hover {
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.action-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  min-height: 80px;
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.action-card:not(.primary-action) .action-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.action-icon.primary {
  background: rgba(255, 255, 255, 0.2);
}

.action-text {
  flex: 1;
}

.action-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.action-description {
  font-size: 0.85rem;
  opacity: 0.8;
  margin: 0;
  line-height: 1.4;
}

.action-card.primary-action .action-title,
.action-card.primary-action .action-description {
  color: white;
}

.action-arrow {
  opacity: 0.6;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.action-card:hover .action-arrow {
  opacity: 1;
  transform: translateX(4px);
}

.action-card.primary-action .action-arrow {
  color: white;
}

/* Loading */
.profile-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
}

[data-theme="dark"] .profile-loading-overlay {
  background: rgba(0, 0, 0, 0.8);
}

.loading-content {
  text-align: center;
}

/* Responsive Design - Mobile First */
@media (min-width: 480px) {
  .quick-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    padding: 1.25rem 1rem;
  }
  
  .stat-content {
    text-align: center;
  }
  
  .avatar-section {
    margin-bottom: 0;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
}

@media (min-width: 768px) {
  .profile-header {
    flex-direction: row;
    text-align: left;
    align-items: center;
    gap: 2rem;
  }

  .profile-info {
    text-align: left;
  }

  .member-info {
    justify-content: flex-start;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }
  
  .status-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
  
  .avatar-size {
    width: 120px;
    height: 120px;
  }

  .status-card {
    padding: 1.5rem;
  }

  .status-header {
    gap: 1.25rem;
  }

  .status-icon {
    width: 52px;
    height: 52px;
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 3rem;
  }
  
  .profile-content {
    padding: 0 2rem 2rem;
  }
  
  .content-section {
    padding: 2rem;
  }
  
  .actions-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .status-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .status-card {
    padding: 2rem;
    gap: 1.25rem;
  }

  .status-header {
    gap: 1.5rem;
  }

  .status-icon {
    width: 56px;
    height: 56px;
  }

  .custom-circular-progress {
    width: 70px;
    height: 70px;
  }

  .progress-percentage {
    font-size: 1rem;
  }
}

/* Focus and Accessibility Improvements */
.stat-card:focus,
.action-card:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.change-avatar-btn:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

/* Dark theme avatar improvements */
[data-theme="dark"] .profile-avatar {
  border-color: rgba(255, 255, 255, 0.2);
}

/* Improved loading state */
.profile-loading-overlay {
  backdrop-filter: blur(4px);
}

/* Animation improvements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-section {
  animation: fadeInUp 0.6s ease-out;
}

.content-section:nth-child(2) {
  animation-delay: 0.1s;
}

.content-section:nth-child(3) {
  animation-delay: 0.2s;
}

.content-section:nth-child(4) {
  animation-delay: 0.3s;
}
</style>
