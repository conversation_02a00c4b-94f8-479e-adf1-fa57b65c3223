import { config } from '@vue/test-utils'
import { beforeEach, vi } from 'vitest'
import { createRouter, createWebHistory } from 'vue-router'

// Mock vue-i18n module BEFORE any imports that might use it
vi.mock('vue-i18n', () => ({
  createI18n: vi.fn(() => ({
    global: {
      t: (key: string) => key,
      locale: { value: 'en' }
    },
    install: vi.fn()
  })),
  useI18n: vi.fn(() => ({
    t: (key: string) => key,
    locale: { value: 'en' }
  }))
}))

import { createI18n } from 'vue-i18n'

// Mock import.meta.env for all tests
Object.defineProperty(globalThis, 'import', {
  value: {
    meta: {
      env: {
        DEV: true,
        VITE_ENABLE_DEBUG_REPORT: 'true',
        NODE_ENV: 'test'
      }
    }
  },
  writable: true
})

// Mock Vue i18n
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en: {},
    fa: {}
  },
  globalInjection: true
})

// Create mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/home', component: { template: '<div>Home</div>' } },
    { path: '/landing', component: { template: '<div>Landing</div>' } }
  ]
})

// Configure global test setup
config.global.plugins = [i18n, router]
config.global.mocks = {
  $t: (key: string) => key,
  $router: router,
  $route: {
    path: '/',
    params: {},
    query: {},
    meta: {}
  }
}

vi.mock('vue-confetti-explosion', () => ({
  default: {
    template: '<div data-testid="confetti-explosion-mock"></div>',
  },
}));

// Mock Naive UI with inline component definitions to avoid hoisting issues
vi.mock('naive-ui', async (importOriginal) => {
  const actual = await importOriginal()
  
  // Define components inline to avoid hoisting issues
  const naiveComponents = [
    'NButton', 'NInput', 'NCard', 'NForm', 'NFormItem', 'NSpace', 
    'NText', 'NTag', 'NModal', 'NSpin', 'NAlert', 'NTooltip',
    'NSelect', 'NOption', 'NCheckbox', 'NRadio', 'NRadioGroup',
    'NSwitch', 'NSlider', 'NProgress', 'NTable', 'NAvatar',
    'NIcon', 'NBadge', 'NDropdown', 'NMenu', 'NMenuItem',
    'NTabPane', 'NTabs', 'NCollapse', 'NCollapseItem',
    'NDrawer', 'NPopover', 'NMessageProvider', 'NDialogProvider',
    'NNotificationProvider', 'NLoadingBarProvider', 'NConfigProvider',
    'NDivider', 'NLayout', 'NLayoutHeader', 'NLayoutContent',
    'NLayoutFooter', 'NLayoutSider', 'NGrid', 'NGridItem',
    'NRow', 'NCol', 'NInputNumber', 'NDatePicker', 'NTimePicker',
    'NCascader', 'NTransfer', 'NTreeSelect', 'NAutoComplete',
    'NColorPicker', 'NRate', 'NUpload', 'NBackTop', 'NAnchor',
    'NAnchorLink', 'NBreadcrumb', 'NBreadcrumbItem', 'NPageHeader',
    'NSteps', 'NStep', 'NTree', 'NDataTable', 'NList', 'NListItem',
    'NThing', 'NSkeleton', 'NEmpty', 'NStatistic', 'NNumberAnimation',
    'NPagination', 'NResult', 'NWatermark', 'NAffix', 'NScrollbar',
    'NCode', 'NDescriptions', 'NDescriptionsItem', 'NCalendar',
    'NTimeline', 'NTimelineItem', 'NLog', 'NGlobalStyle'
  ]    // Create specific mocks for components that need special handling
  const specialMocks = {
    NInput: {
      template: `<div data-testid="ninput" class="n-input ninput">
        <input 
          :value="modelValue || value" 
          :placeholder="placeholder"
          :disabled="disabled"
          @input="$emit('update:modelValue', $event.target.value)"
          @change="$emit('change', $event)"
        />
        <slot />
      </div>`,
      props: ['loading', 'disabled', 'size', 'type', 'value', 'modelValue', 'placeholder', 'error', 'status'],
      emits: ['update:modelValue', 'click', 'change', 'input']
    },
    NCheckbox: {
      template: `<div data-testid="ncheckbox" class="n-checkbox ncheckbox" @click="toggle">
        <input 
          type="checkbox" 
          :checked="checked || modelValue"
          :disabled="disabled"
          @change="handleChange"
          style="display: none;"
        />
        <div class="n-checkbox__indicator"></div>
        <div class="n-checkbox__label"><slot /></div>
      </div>`,
      props: ['checked', 'modelValue', 'disabled', 'label'],
      emits: ['update:modelValue', 'update:checked', 'change'],
      methods: {
        toggle() {
          if (!this.disabled) {
            const newValue = !(this.checked || this.modelValue)
            this.$emit('update:modelValue', newValue)
            this.$emit('update:checked', newValue)
            this.$emit('change', newValue)
          }
        },
        handleChange(event: Event) {
          const target = event.target as HTMLInputElement
          this.$emit('update:modelValue', target.checked)
          this.$emit('update:checked', target.checked)
          this.$emit('change', target.checked)
        }
      }
    },    NButton: {
      template: `<button 
        data-testid="n-button" 
        class="n-button nbutton"
        :disabled="disabled || loading"
        :type="htmlType || 'button'"
        :title="title"
        @click="$emit('click', $event)"
      >
        <slot name="icon" />
        <slot />
      </button>`,
      props: ['loading', 'disabled', 'size', 'type', 'htmlType', 'title'],
      emits: ['click']
    },
    NIcon: {
      template: `<i data-testid="nicon" class="n-icon nicon"><slot /></i>`,
      props: ['size', 'color', 'depth'],
      emits: []
    }
  }
  // Create default mocks for other components
  const defaultComponents = naiveComponents.filter(name => !specialMocks[name])
  const mockComponents = defaultComponents.reduce((acc, componentName) => {
    const testId = componentName.toLowerCase()
    acc[componentName] = {
      template: `<div data-testid="${testId}" class="${testId}"><slot /></div>`,
      props: ['loading', 'disabled', 'size', 'type', 'value', 'modelValue', 'placeholder', 'error', 'status', 'label', 'show', 'title', 'closable'],
      emits: ['update:modelValue', 'click', 'change', 'input', 'close', 'update:show']
    }
    return acc
  }, {} as Record<string, any>)

  // Combine special mocks with default mocks
  Object.assign(mockComponents, specialMocks)
  
  return {
    ...actual,
    ...mockComponents,
    // Mock the message API
    useMessage: () => ({
      error: vi.fn(),
      success: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
      loading: vi.fn()
    }),
    useDialog: () => ({
      error: vi.fn(),
      success: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
      create: vi.fn()
    }),
    useNotification: () => ({
      error: vi.fn(),
      success: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
      create: vi.fn()
    }),
    useLoadingBar: () => ({
      start: vi.fn(),
      finish: vi.fn(),
      error: vi.fn()
    })
  }
})

// Mock socket.io-client globally
vi.mock('socket.io-client', () => ({
  io: vi.fn(() => ({
    connected: false,
    disconnected: true,
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    connect: vi.fn(),
    disconnect: vi.fn(),
    removeAllListeners: vi.fn()
  }))
}))

// Mock centralized socket manager
vi.mock('@/services/centralizedSocketManager', () => ({
  default: {
    initialize: vi.fn(),
    isConnected: vi.fn(() => false),
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    forceReconnect: vi.fn(),
    cleanup: vi.fn()
  }
}))

// Mock icon libraries globally
vi.mock('@vicons/ionicons5', () => {
  // Create a mock component factory for any icon
  const createIconMock = (name: string) => ({
    name,
    template: `<i data-testid="icon-${name.toLowerCase()}" class="icon icon-${name.toLowerCase()}"></i>`,
    props: ['size', 'color']
  })
  
  return new Proxy({}, {
    get(target, prop) {
      if (typeof prop === 'string') {
        return createIconMock(prop)
      }
      return undefined
    }
  })
})

vi.mock('@vicons/tabler', () => {
  const createIconMock = (name: string) => ({
    name,
    template: `<i data-testid="icon-${name.toLowerCase()}" class="icon icon-${name.toLowerCase()}"></i>`,
    props: ['size', 'color']
  })
  
  return new Proxy({}, {
    get(target, prop) {
      if (typeof prop === 'string') {
        return createIconMock(prop)
      }
      return undefined
    }
  })
})

vi.mock('@vicons/material', () => {
  const createIconMock = (name: string) => ({
    name,
    template: `<i data-testid="icon-${name.toLowerCase()}" class="icon icon-${name.toLowerCase()}"></i>`,
    props: ['size', 'color']
  })
  
  return new Proxy({}, {
    get(target, prop) {
      if (typeof prop === 'string') {
        return createIconMock(prop)      }
      return undefined
    }
  })
})

// Note: Global test utilities like createMockStore would be helpful but 
// cause hoisting issues with Vitest. Use manual mocking patterns instead.
// See unit.md for recommended patterns.

// Global test setup
beforeEach(() => {
  // Reset all mocks before each test
  vi.clearAllMocks()
  
  // Mock console methods to reduce noise in tests
  vi.spyOn(console, 'log').mockImplementation(() => {})
  vi.spyOn(console, 'warn').mockImplementation(() => {})
  vi.spyOn(console, 'error').mockImplementation(() => {})
  
  // Mock window methods
  Object.defineProperty(window, 'location', {
    value: {
      href: 'http://localhost:3000',
      assign: vi.fn(),
      reload: vi.fn()
    },
    writable: true
  })
  
  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn()
  }
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
  })
  
  // Mock navigator.onLine
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: true
  })
  
  // Mock matchMedia (required for theme components and responsive design)
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })
  
  // Mock ResizeObserver (required for many UI components)
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))
  
  // Mock document.documentElement (needed for theme switching)
  Object.defineProperty(document, 'documentElement', {
    value: {
      setAttribute: vi.fn(),
      getAttribute: vi.fn(),
      removeAttribute: vi.fn(),
      classList: {
        add: vi.fn(),
        remove: vi.fn(),
        contains: vi.fn(),
        toggle: vi.fn()
      }
    },
    writable: true
  })
})

// Configure Vue Test Utils globally
config.global.config.warnHandler = () => {}
config.global.config.errorHandler = () => {}
