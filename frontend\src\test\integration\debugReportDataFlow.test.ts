import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createTesting<PERSON>inia } from '@pinia/testing'
import DebugReportButtonEnhanced from '../../components/DebugReportButtonEnhanced.vue'
import { createI18n } from 'vue-i18n'
import axios from 'axios'
import type { ComponentPublicInstance } from 'vue'
import type { ClientReportPayload } from '../../types/logging'

// Mock axios
vi.mock('axios')
const mockedAxios = vi.mocked(axios)

// Create i18n instance with test translations
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en: {
      debug: {
        reportIssue: 'Report Issue',
        reportType: 'Report Type',
        title: 'Title',
        titlePlaceholder: 'Brief summary of the issue',
        description: 'Description',
        descriptionPlaceholder: 'Detailed description',
        stepsToReproduce: 'Steps to Reproduce',
        stepsPlaceholder: 'Step-by-step instructions',
        expectedBehavior: 'Expected Behavior',
        expectedPlaceholder: 'What should happen',
        actualBehavior: 'Actual Behavior',
        actualPlaceholder: 'What actually happens',
        severity: 'Severity',
        additionalNotes: 'Additional Notes',
        notesPlaceholder: 'Any other relevant information',
        submitReport: 'Submit Report',
        submitting: 'Submitting...',
        reportSubmitted: 'Report submitted successfully',
        reportFailed: 'Failed to submit report',
        reportTypes: {
          bug: 'Bug Report',
          'feature-request': 'Feature Request',
          performance: 'Performance Issue',
          'ui-ux': 'UI/UX Issue',
          improvement: 'Improvement Suggestion',
          question: 'Question',
          other: 'Other'
        },
        severityLevels: {
          low: 'Low',
          medium: 'Medium',
          high: 'High',
          critical: 'Critical'
        }
      }
    }
  }
})

describe('Debug Report Data Flow Integration Tests', () => {
  let wrapper: VueWrapper<ComponentPublicInstance>
  let mockApiPost: ReturnType<typeof vi.fn>

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()

    // Mock API post method
    mockApiPost = vi.fn()
    mockedAxios.post = mockApiPost

    wrapper = mount(DebugReportButtonEnhanced, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false
          }),
          i18n
        ]
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  describe('Complete Bug Report Data Flow', () => {
    it('should capture all form fields and send complete payload to backend', async () => {
      // Mock successful backend response
      const mockReportId = 'report_1705312200000_xyz789'
      mockApiPost.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Debug report received successfully. Thank you for helping us improve!',
          reportId: mockReportId
        }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill out comprehensive bug report
      const vm = wrapper.vm as any
      const testBugReport = {
        type: 'bug',
        title: 'Form submission fails silently',
        description: 'When users fill out the offer creation form and click submit, the form appears to submit but nothing happens. No error messages are shown and the offer is not created.',
        stepsToReproduce: '1. Navigate to the Create Offer page\n2. Fill out all required fields (title, description, amount, currency)\n3. Click the "Submit Offer" button\n4. Observe that the button shows loading briefly but then returns to normal state\n5. Check that no offer was created and no error message is displayed',
        expectedBehavior: 'The form should submit successfully, create the offer in the database, show a success message, and redirect the user to their offers page.',
        actualBehavior: 'The submit button shows loading for about 1 second, then returns to normal state. No success or error message is displayed. The user remains on the form page and no offer is created.',
        severity: 'high',
        additionalNotes: 'This issue has been reported by multiple users. It seems to affect both Chrome and Firefox browsers. The network tab shows a 200 response from the API, but the frontend does not handle the response correctly.',
        reportTags: ['critical', 'form-submission', 'frontend', 'needs-investigation']
      }

      vm.reportForm = { ...testBugReport }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')

      // Wait for API call to complete
      await wrapper.vm.$nextTick()

      // Verify API was called with correct endpoint
      expect(mockApiPost).toHaveBeenCalledTimes(1)
      expect(mockApiPost).toHaveBeenCalledWith('/debug/report-issue', expect.any(Object))

      // Get the payload that was sent to the backend
      const sentPayload = mockApiPost.mock.calls[0][1] as ClientReportPayload

      // Verify the payload structure
      expect(sentPayload).toMatchObject({
        logs: expect.any(Array),
        reportDetails: expect.objectContaining({
          type: 'bug',
          title: 'Form submission fails silently',
          description: 'When users fill out the offer creation form and click submit, the form appears to submit but nothing happens. No error messages are shown and the offer is not created.',
          stepsToReproduce: '1. Navigate to the Create Offer page\n2. Fill out all required fields (title, description, amount, currency)\n3. Click the "Submit Offer" button\n4. Observe that the button shows loading briefly but then returns to normal state\n5. Check that no offer was created and no error message is displayed',
          expectedBehavior: 'The form should submit successfully, create the offer in the database, show a success message, and redirect the user to their offers page.',
          actualBehavior: 'The submit button shows loading for about 1 second, then returns to normal state. No success or error message is displayed. The user remains on the form page and no offer is created.',
          severity: 'high',
          additionalNotes: 'This issue has been reported by multiple users. It seems to affect both Chrome and Firefox browsers. The network tab shows a 200 response from the API, but the frontend does not handle the response correctly.',
          reportTags: ['critical', 'form-submission', 'frontend', 'needs-investigation']
        }),
        timestamp: expect.any(String),
        sessionId: expect.any(String)
      })

      // Verify that reportDetails includes userContext
      expect(sentPayload.reportDetails.userContext).toMatchObject({
        currentPage: expect.any(String),
        userAgent: expect.any(String),
        viewport: expect.objectContaining({
          width: expect.any(Number),
          height: expect.any(Number)
        }),
        timestamp: expect.any(String),
        userActions: expect.any(Array),
        routeHistory: expect.any(Array)
      })

      // Verify that ALL form fields are present in reportDetails
      const { reportDetails } = sentPayload
      expect(reportDetails.type).toBe(testBugReport.type)
      expect(reportDetails.title).toBe(testBugReport.title)
      expect(reportDetails.description).toBe(testBugReport.description)
      expect(reportDetails.stepsToReproduce).toBe(testBugReport.stepsToReproduce)
      expect(reportDetails.expectedBehavior).toBe(testBugReport.expectedBehavior)
      expect(reportDetails.actualBehavior).toBe(testBugReport.actualBehavior)
      expect(reportDetails.severity).toBe(testBugReport.severity)
      expect(reportDetails.additionalNotes).toBe(testBugReport.additionalNotes)
      expect(reportDetails.reportTags).toEqual(testBugReport.reportTags)
    })

    it('should handle feature request with minimal data correctly', async () => {
      // Mock successful backend response
      mockApiPost.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Feature request received successfully',
          reportId: 'report_feature_123'
        }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill out minimal feature request
      const vm = wrapper.vm as any
      const testFeatureRequest = {
        type: 'feature-request',
        title: 'Add dark mode support',
        description: 'Please add a dark mode toggle to the application for better user experience in low-light environments.',
        severity: 'low'
        // Note: only required fields filled
      }

      vm.reportForm = { ...testFeatureRequest }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Verify API call
      expect(mockApiPost).toHaveBeenCalledTimes(1)
      
      const sentPayload = mockApiPost.mock.calls[0][1] as ClientReportPayload

      // Verify minimal data is correctly sent
      expect(sentPayload.reportDetails).toMatchObject({
        type: 'feature-request',
        title: 'Add dark mode support',
        description: 'Please add a dark mode toggle to the application for better user experience in low-light environments.',
        severity: 'low'
      })

      // Verify optional fields are undefined (not empty strings)
      expect(sentPayload.reportDetails.stepsToReproduce).toBeUndefined()
      expect(sentPayload.reportDetails.expectedBehavior).toBeUndefined()
      expect(sentPayload.reportDetails.actualBehavior).toBeUndefined()
      expect(sentPayload.reportDetails.additionalNotes).toBeUndefined()
      expect(sentPayload.reportDetails.reportTags).toBeUndefined()
    })

    it('should include performance context for performance reports', async () => {
      // Mock successful backend response
      mockApiPost.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Performance report received',
          reportId: 'report_performance_456'
        }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill out performance report
      const vm = wrapper.vm as any
      const testPerformanceReport = {
        type: 'performance',
        title: 'Browse offers page loads very slowly',
        description: 'The browse offers page takes more than 10 seconds to load, making the app unusable.',
        stepsToReproduce: '1. Navigate to Browse Offers page\n2. Wait for page to load\n3. Observe long loading times',
        expectedBehavior: 'Page should load within 2-3 seconds',
        actualBehavior: 'Page takes 10+ seconds to load consistently',        severity: 'critical',
        additionalNotes: 'Issue started after the recent deployment last week',
        reportTags: [
          { tagId: 'performance-id', origin: 'PREDEFINED' },
          { tagId: 'critical-id', origin: 'PREDEFINED' },
          { tagId: 'api-slow-id', origin: 'PREDEFINED' }
        ]
      }

      vm.reportForm = { ...testPerformanceReport }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Verify API call
      expect(mockApiPost).toHaveBeenCalledTimes(1)
      
      const sentPayload = mockApiPost.mock.calls[0][1] as ClientReportPayload

      // Verify performance report data
      expect(sentPayload.reportDetails).toMatchObject({
        type: 'performance',        title: 'Browse offers page loads very slowly',
        severity: 'critical',
        reportTags: [
          { tagId: 'performance-id', origin: 'PREDEFINED' },
          { tagId: 'critical-id', origin: 'PREDEFINED' },
          { tagId: 'api-slow-id', origin: 'PREDEFINED' }
        ]
      })

      // Verify logs array is included (may contain performance-related logs)
      expect(sentPayload.logs).toBeDefined()
      expect(Array.isArray(sentPayload.logs)).toBe(true)
    })
  })

  describe('Error Scenarios', () => {
    it('should handle backend API errors gracefully', async () => {
      // Mock backend error response
      mockApiPost.mockRejectedValueOnce({
        response: {
          status: 500,
          data: {
            success: false,
            message: 'Internal server error while saving report'
          }
        }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill out form
      const vm = wrapper.vm as any
      vm.reportForm = {
        type: 'bug',
        title: 'Error test',
        description: 'Testing error handling',
        severity: 'medium'
      }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Verify API call was made
      expect(mockApiPost).toHaveBeenCalledTimes(1)

      // Verify error handling (component should handle the error gracefully)
      expect(vm.isSubmitting).toBe(false)
    })

    it('should handle network timeout errors', async () => {
      // Mock network timeout
      mockApiPost.mockRejectedValueOnce(new Error('Network timeout'))

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill out form
      const vm = wrapper.vm as any
      vm.reportForm = {
        type: 'other',
        title: 'Network test',
        description: 'Testing network error handling',
        severity: 'low'
      }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Verify error handling
      expect(mockApiPost).toHaveBeenCalledTimes(1)
      expect(vm.isSubmitting).toBe(false)
    })
  })

  describe('Data Validation', () => {
    it('should validate that title and description are not missing critical fields', async () => {
      // Mock successful response
      mockApiPost.mockResolvedValueOnce({
        data: { success: true, reportId: 'test-validation' }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill form with comprehensive data
      const vm = wrapper.vm as any
      const comprehensiveReport = {
        type: 'ui-ux',
        title: 'Mobile responsiveness issues on iPhone',
        description: 'The offer cards are not displaying correctly on iPhone devices, with text being cut off and buttons being too small to tap.',
        stepsToReproduce: '1. Open the app on iPhone (tested on iPhone 12, iOS 15)\n2. Navigate to Browse Offers page\n3. Observe the layout issues with offer cards\n4. Try to tap the "Express Interest" buttons',
        expectedBehavior: 'Offer cards should display properly with readable text and tap-friendly buttons on all mobile devices',
        actualBehavior: 'Text is cut off, buttons are too small (less than 44px height), and some content overflows the card boundaries',
        severity: 'medium',
        additionalNotes: 'This affects user conversion as users cannot easily interact with offers on mobile. Tested on multiple iPhone models with consistent results.',
        reportTags: ['mobile', 'ui-ux', 'responsive-design', 'ios']
      }

      vm.reportForm = { ...comprehensiveReport }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Verify all fields are sent correctly
      const sentPayload = mockApiPost.mock.calls[0][1] as ClientReportPayload
      
      // Check that none of the important fields are lost or corrupted
      const { reportDetails } = sentPayload
      expect(reportDetails.title).toContain('Mobile responsiveness')
      expect(reportDetails.description).toContain('offer cards are not displaying correctly')
      expect(reportDetails.stepsToReproduce).toContain('Open the app on iPhone')
      expect(reportDetails.expectedBehavior).toContain('should display properly')
      expect(reportDetails.actualBehavior).toContain('Text is cut off')
      expect(reportDetails.additionalNotes).toContain('affects user conversion')
      expect(reportDetails.reportTags).toEqual(['mobile', 'ui-ux', 'responsive-design', 'ios'])
    })
  })

  describe('Backend Data Format Verification', () => {
    it('should send data in the exact format expected by ClientLogService', async () => {
      // Mock successful response
      mockApiPost.mockResolvedValueOnce({
        data: { success: true, reportId: 'format-test-123' }
      })

      // Open modal and submit report
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      const vm = wrapper.vm as any
      vm.reportForm = {
        type: 'bug',
        title: 'Format verification test',
        description: 'Testing data format compatibility',
        severity: 'low',
        additionalNotes: 'Backend format test'
      }
      await wrapper.vm.$nextTick()

      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')
      await wrapper.vm.$nextTick()

      const sentPayload = mockApiPost.mock.calls[0][1] as ClientReportPayload

      // Verify the payload matches exactly what ClientLogService expects
      expect(sentPayload).toMatchObject({
        logs: expect.any(Array),
        reportDetails: expect.objectContaining({
          type: expect.any(String),
          severity: expect.any(String),
          title: expect.any(String),
          description: expect.any(String),
          additionalNotes: expect.any(String),
          userContext: expect.objectContaining({
            currentPage: expect.any(String),
            userAgent: expect.any(String),
            viewport: expect.any(Object),
            timestamp: expect.any(String),
            userActions: expect.any(Array),
            routeHistory: expect.any(Array)
          }),
          correlatedLogEntries: expect.any(Array)
        }),
        timestamp: expect.any(String),
        sessionId: expect.any(String)
      })

      // Verify timestamp format (ISO string)
      expect(sentPayload.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/)
      
      // Verify sessionId format
      expect(sentPayload.sessionId).toMatch(/^session_\d+_[a-z0-9]+$/)
    })
  })
})
