<template>
  <div 
    class="enhanced-offer-card"
    :class="{ 
      'is-own-offer': isOwnOffer,
      'has-interest': offer.currentUserHasShownInterest,
      'bonus-rate': isBonus,
      'penalty-rate': isPenalty
    }"
    @click="handleCardClick"
  >
    <!-- Card Header with Offer Type -->
    <div class="card-header">
      <div class="offer-type-badge" :class="offer.type.toLowerCase()">
        <n-icon size="20">
          <ExportOutlined v-if="offer.type === 'SELL'" />
          <ImportOutlined v-else />
        </n-icon>
        <span>{{ offer.type === 'BUY' ? $t('offers.buying') : $t('offers.selling') }}</span>
      </div>
      
      <!-- Status Badge -->
      <div v-if="dynamicStatus" class="status-badge" :class="dynamicStatus.type">
        <span class="status-icon">{{ getStatusIcon(dynamicStatus.text) }}</span>
        <span class="status-text">{{ dynamicStatus.text }}</span>
      </div>
    </div>

    <!-- Main Content -->
    <div class="card-content">
      <!-- Amount Display -->
      <div class="amount-section">
        <div class="amount-display">
          <span class="currency">CAD</span>
          <span class="amount">${{ formatAmount(offer.amount) }}</span>
        </div>
        <div class="amount-subtitle">{{ $t('offers.exchangeAmount') }}</div>
      </div>

      <!-- Rate Information -->
      <div class="rate-section">
        <div class="rate-row">
          <span class="rate-label">{{ $t('offers.rate') }}:</span>
          <div class="rate-value">
            <span class="rate-number">{{ formatRate(offer.calculatedApplicableRate) }}</span>
            <span class="rate-currency">IRR/CAD</span>
            <n-tag 
              v-if="!isOwnOffer && (isBonus || isPenalty)" 
              :type="rateIndicatorType" 
              size="tiny" 
              round 
              class="rate-tag"
            >
              {{ isBonus ? $t('offers.bonus') : $t('offers.penalty') }}
            </n-tag>
          </div>
        </div>
        
        <div v-if="showBaseRate" class="rate-row base-rate">
          <span class="rate-label">{{ $t('offers.baseRate') }}:</span>
          <div class="rate-value">
            <span class="rate-number">{{ formatRate(offer.baseRate) }}</span>
            <span class="rate-currency">IRR/CAD</span>
          </div>
        </div>
      </div>

      <!-- Creator Information -->
      <div class="creator-section">
        <div class="creator-info">
          <div class="creator-avatar">
            <n-icon size="16" color="#666">
              <UserOutlined />
            </n-icon>
          </div>
          <div class="creator-details">
            <span class="creator-name">{{ offer.offerCreatorUsername || $t('offers.unknownUser') }}</span>
            <div class="creator-reputation">
              <ReputationIcon :level="offer.offerCreatorReputationLevel" size="small" />
              <span class="reputation-text">{{ $t('offers.level') }} {{ offer.offerCreatorReputationLevel ?? 'N/A' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Card Footer Actions -->
    <div class="card-footer">
      <div class="action-buttons">
        <!-- Show Interest Button -->
        <n-button
          v-if="canShowInterest"
          type="primary"
          size="medium"
          round
          @click.stop="showInterest"
          class="primary-action-btn"
        >
          <template #icon>
            <n-icon><HeartOutlined /></n-icon>
          </template>
          {{ $t('offers.showInterest') }}
        </n-button>
        
        <!-- Go to Chat Button -->
        <n-button
          v-if="canGoToChat"
          type="success"
          size="medium"
          round
          @click.stop="goToChat"
          class="primary-action-btn"
        >
          <template #icon>
            <n-icon><MessageOutlined /></n-icon>
          </template>
          {{ $t('offers.goToChat') }}
        </n-button>
        
        <!-- View Details Button -->
        <n-button
          quaternary
          size="medium"
          round
          @click.stop="$emit('click', offer)"
          class="secondary-action-btn"
        >
          {{ $t('offers.viewDetails') }}
        </n-button>
      </div>
    </div>

    <!-- Hover Effect Overlay -->
    <div class="hover-overlay"></div>
  </div>

  <!-- Confirmation Modal for Show Interest -->
  <n-modal v-model:show="showConfirmModal" preset="dialog" style="max-width: 400px;">
    <template #header>
      <div class="modal-header">
        <n-icon size="24" color="#2080f0">
          <HeartOutlined />
        </n-icon>
        <span>{{ $t('offers.showInterest') }}</span>
      </div>
    </template>
    
    <div class="modal-content">
      <div class="offer-summary">
        <div class="summary-item">
          <span class="summary-label">{{ $t('offers.type') }}:</span>
          <span class="summary-value">{{ offer.type === 'BUY' ? $t('offers.buying') : $t('offers.selling') }} CAD</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">{{ $t('offers.amount') }}:</span>
          <span class="summary-value">${{ formatAmount(offer.amount) }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">{{ $t('offers.rate') }}:</span>
          <span class="summary-value">{{ formatRate(offer.calculatedApplicableRate) }} IRR/CAD</span>
        </div>
      </div>
      <n-text class="confirmation-text">{{ $t('offers.confirmShowInterest') }}</n-text>
    </div>
    
    <template #action>
      <n-space>
        <n-button @click="cancelShowInterest">{{ $t('app.cancel') }}</n-button>
        <n-button type="primary" @click="confirmShowInterest">
          <template #icon>
            <n-icon><HeartOutlined /></n-icon>
          </template>
          {{ $t('app.confirm') }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { 
  NIcon, 
  NTag, 
  NButton, 
  NModal, 
  NText, 
  NSpace 
} from 'naive-ui';
import {
  ExportOutlined,
  ImportOutlined,
  UserOutlined,
  HeartOutlined,
  MessageOutlined
} from '@vicons/antd';
import ReputationIcon from '@/components/ReputationIcon.vue';
import type { BrowseOffer } from '@/types/offer';
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'vue-router';
import { useTranslation } from '@/composables/useTranslation';
import { navigateToChat } from '@/utils/chatNavigation';
import { getInterestDynamicStatus } from '@/utils/statusHelpers';
import { formatAmountForDisplay } from '@/utils/currencyUtils';

const props = defineProps<{
  offer: BrowseOffer
}>();

const emit = defineEmits<{
  showInterest: [offerId: string]
  click: [offer: BrowseOffer]
}>();

const authStore = useAuthStore();
const router = useRouter();
const { t } = useTranslation();

const showConfirmModal = ref(false);

// Computed properties
const isOwnOffer = computed(() => {
  return authStore.user?.id === props.offer.offerCreatorId;
});

const rateDifference = computed(() => {
  if (props.offer.calculatedApplicableRate != null && props.offer.baseRate != null) {
    const applicableRate = Number(props.offer.calculatedApplicableRate);
    const baseRate = Number(props.offer.baseRate);
    if (!isNaN(applicableRate) && !isNaN(baseRate)) {
      return applicableRate - baseRate;
    }
  }
  return 0;
});

const isBonus = computed(() => {
  if (isOwnOffer.value || Math.abs(rateDifference.value) < 0.00001) return false;
  if (props.offer.type === 'BUY') {
    return rateDifference.value > 0;
  } else {
    return rateDifference.value < 0;
  }
});

const isPenalty = computed(() => {
  if (isOwnOffer.value || Math.abs(rateDifference.value) < 0.00001) return false;
  if (props.offer.type === 'BUY') {
    return rateDifference.value < 0;
  } else {
    return rateDifference.value > 0;
  }
});

const rateIndicatorType = computed<'success' | 'error' | 'default'>(() => {
  if (isOwnOffer.value) return 'default';
  if (isBonus.value) return 'success';
  if (isPenalty.value) return 'error';
  return 'default';
});

const dynamicStatus = computed(() => {
  if (!props.offer.currentUserHasShownInterest) {
    return null;
  }
  return getInterestDynamicStatus(
    props.offer.currentUserInterestStatus || null,
    props.offer.transactionStatus,
    props.offer.negotiationStatus,
    t
  );
});

const canShowInterest = computed(() => {
  return !isOwnOffer.value && !props.offer.currentUserHasShownInterest;
});

const canGoToChat = computed(() => {
  return props.offer.currentUserInterestStatus === 'ACCEPTED' && !!props.offer.chatSessionId;
});

const showBaseRate = computed(() => {
  return !isOwnOffer.value && Math.abs(rateDifference.value) > 0.00001;
});

// Methods
function formatAmount(amount: number): string {
  return Number(amount).toLocaleString();
}

function formatRate(rate: number): string {
  return formatAmountForDisplay(Number(rate), 'IRR', true);
}

function getStatusIcon(status: string): string {
  switch (status) {
    case 'Complete': return '✅';
    case 'In Progress': return '🔄';
    case 'Negotiating': return '💬';
    default: return '📋';
  }
}

function handleCardClick() {
  emit('click', props.offer);
}

function showInterest() {
  showConfirmModal.value = true;
}

function confirmShowInterest() {
  showConfirmModal.value = false;
  emit('showInterest', props.offer.id);
}

function cancelShowInterest() {
  showConfirmModal.value = false;
}

async function goToChat() {
  if (props.offer.chatSessionId) {
    await navigateToChat(router, { chatSessionId: props.offer.chatSessionId });
  } else {
    console.error('Attempted to go to chat without a chatSessionId on offer:', props.offer.id);
  }
}
</script>

<style scoped>
/* Enhanced Offer Card Styles */
.enhanced-offer-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

.enhanced-offer-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: rgba(32, 128, 240, 0.3);
}

.enhanced-offer-card.bonus-rate {
  border-color: rgba(24, 160, 88, 0.3);
}

.enhanced-offer-card.bonus-rate:hover {
  border-color: rgba(24, 160, 88, 0.6);
  box-shadow: 0 8px 25px rgba(24, 160, 88, 0.2);
}

.enhanced-offer-card.penalty-rate {
  border-color: rgba(231, 76, 60, 0.3);
}

.enhanced-offer-card.penalty-rate:hover {
  border-color: rgba(231, 76, 60, 0.6);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.2);
}

.enhanced-offer-card.has-interest {
  border-color: rgba(32, 128, 240, 0.5);
}

.enhanced-offer-card.is-own-offer {
  border-color: rgba(102, 102, 102, 0.3);
  opacity: 0.8;
  cursor: default;
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1rem 0.5rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  margin-bottom: 0.5rem;
}

.offer-type-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.offer-type-badge.buy {
  background: rgba(24, 160, 88, 0.1);
  color: #18a058;
  border: 1px solid rgba(24, 160, 88, 0.2);
}

.offer-type-badge.sell {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.success {
  background: rgba(24, 160, 88, 0.1);
  color: #18a058;
}

.status-badge.warning {
  background: rgba(255, 193, 7, 0.1);
  color: #f0a020;
}

.status-badge.info {
  background: rgba(32, 128, 240, 0.1);
  color: #2080f0;
}

.status-icon {
  font-size: 0.875rem;
}

/* Card Content */
.card-content {
  flex: 1;
  padding: 0.5rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Amount Section */
.amount-section {
  text-align: center;
  padding: 0.75rem 0;
}

.amount-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.currency {
  font-size: 0.875rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
}

.amount {
  font-size: 1.75rem;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.amount-subtitle {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Rate Section */
.rate-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.rate-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.rate-row.base-rate {
  opacity: 0.7;
  font-size: 0.8rem;
}

.rate-label {
  color: #666;
  font-weight: 500;
}

.rate-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.rate-number {
  color: #333;
}

.rate-currency {
  font-size: 0.75rem;
  color: #666;
  text-transform: uppercase;
}

.rate-tag {
  font-size: 0.7rem;
}

/* Creator Section */
.creator-section {
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  padding-top: 0.75rem;
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.creator-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(32, 128, 240, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.creator-details {
  flex: 1;
  min-width: 0;
}

.creator-name {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.creator-reputation {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-top: 0.125rem;
}

.reputation-text {
  font-size: 0.75rem;
  color: #666;
}

/* Card Footer */
.card-footer {
  padding: 0.75rem 1rem 1rem 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  margin-top: auto;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.primary-action-btn {
  width: 100%;
  height: 40px;
  font-weight: 600;
}

.secondary-action-btn {
  width: 100%;
  height: 36px;
  font-size: 0.875rem;
}

/* Hover Overlay */
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(32, 128, 240, 0.05), rgba(102, 126, 234, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: 16px;
}

.enhanced-offer-card:hover .hover-overlay {
  opacity: 1;
}

/* Modal Styles */
.modal-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.modal-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.offer-summary {
  background: rgba(32, 128, 240, 0.05);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(32, 128, 240, 0.1);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-weight: 500;
  color: #666;
}

.summary-value {
  font-weight: 600;
  color: #333;
}

.confirmation-text {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #666;
}

/* Dark theme support */
[data-theme="dark"] .enhanced-offer-card {
  background: rgba(26, 27, 46, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .amount,
[data-theme="dark"] .rate-number,
[data-theme="dark"] .creator-name,
[data-theme="dark"] .summary-value {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .card-header,
[data-theme="dark"] .creator-section,
[data-theme="dark"] .card-footer {
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .offer-summary {
  background: rgba(32, 128, 240, 0.1);
  border-color: rgba(32, 128, 240, 0.2);
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .action-buttons {
    flex-direction: row;
  }
  
  .primary-action-btn {
    flex: 1;
  }
  
  .secondary-action-btn {
    flex: 0 0 auto;
    width: auto;
    min-width: 120px;
  }
}

/* Animations */
@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
  }
  50% { 
    opacity: 0.7; 
  }
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .enhanced-offer-card,
  .hover-overlay {
    transition: none !important;
  }
  
  .enhanced-offer-card:hover {
    transform: none !important;
  }
}
</style>
