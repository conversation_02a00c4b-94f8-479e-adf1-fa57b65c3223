{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue"],  "exclude": [
    "src/**/__tests__/*",
    "src/components/TransactionFlowCard.vue",
    "src/components/TransactionFlowCardV2.vue",
    "src/components/TransactionFlowCardV3.vue",
    "src/components/TransactionInfoCard.vue",
    "src/components/TransactionTimelineLog.vue",
    "src/stores/transaction.ts",
    "src/stores/payerNegotiation.ts",
    "src/stores/chatStore.ts",
    "src/stores/transactionStore.ts",
    "src/types/transaction.ts",
    "src/types/socketEvents.ts",
    "src/services/transactionApiService.ts",
    "src/composables/useTransactionFlowLogic.ts",
    "src/i18n.ts",
    "src/main.ts",
    "src/stores/language.ts",
    "src/services/offerService.ts",
    "src/views/CreateOfferView.vue",
    "src/views/BrowseOffersView.vue"
  ],
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",

    /* Path Aliases */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },

    /* Linting - More lenient for Docker build */
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    "noUncheckedSideEffectImports": false,
    "skipLibCheck": true,

    /* Module Resolution and Compilation */
    "target": "ES2020",
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "moduleResolution": "bundler",

    "composite": true,
    "types": ["naive-ui/volar"]
  }
}
