// Test script to verify the API endpoint works
async function testPaymentMethodAPI() {
  console.log('=== Testing Payment Method API ===');
  
  try {
    // First login to get JWT token
    const loginResponse = await fetch('http://localhost:3000/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    if (!loginResponse.ok) {
      console.log('❌ Login failed, creating test API call...');
      
      // Create a test payment method via API (this will test our fixed service)
      const response = await fetch('http://localhost:3000/api/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token-will-fail' // This will fail auth but test our service
        },
        body: JSON.stringify({
          currency: 'USD',
          paymentMethodType: 'BANK_TRANSFER',
          bankName: 'API Test Bank',
          accountNumber: '*********',
          accountHolderName: 'API Test User',
          isDefaultForUser: true
        })
      });
      
      console.log(`API Response Status: ${response.status}`);
      const responseText = await response.text();
      console.log('Response:', responseText);
      
      if (response.status === 401) {
        console.log('✅ Expected auth failure - this means our service is running and accessible');
      } else if (response.status === 500) {
        console.log('❌ 500 error - there might still be an issue');
      }
      
      return;
    }
    
    const loginData = await loginResponse.json();
    const token = loginData.token;
    
    console.log('✅ Login successful, testing payment method creation...');
    
    // Test creating payment method with real auth
    const response = await fetch('http://localhost:3000/api/payment-methods', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        currency: 'USD',
        paymentMethodType: 'BANK_TRANSFER',
        bankName: 'API Test Bank',
        accountNumber: '*********',
        accountHolderName: 'API Test User',
        isDefaultForUser: true
      })
    });
    
    console.log(`API Response Status: ${response.status}`);
    const responseData = await response.json();
    console.log('Response:', responseData);
    
    if (response.ok) {
      console.log('✅ Payment method created successfully via API!');
    } else {
      console.log('❌ API call failed');
    }
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

testPaymentMethodAPI();
