import { PrismaClient, Tag, TagCategory, DebugReportType, Prisma } from '@prisma/client';

// Define payload types for methods that include relations
export type TagWithCategoryAndReportTypes = Prisma.TagGetPayload<{
  include: {
    category: true;
    reportTypes: true;
  };
}>;

export type TagWithCategoryReportTypesAndCount = Prisma.TagGetPayload<{
  include: {
    category: true;
    reportTypes: true;
    _count: {
      select: { reportTags: true };
    };
  };
}>;

export type TagWithCategoryAndFilteredReportTypes = Prisma.TagGetPayload<{
  include: {
    category: true;
    reportTypes: {
      where: {
        reportType: string;
      };
    };
  };
}>;

export type TagCategoryWithCount = Prisma.TagCategoryGetPayload<{
  include: {
    _count: {
      select: { tags: true };
    };
  };
}>;

export type TagWithCategory = Prisma.TagGetPayload<{
  include: {
    category: true;
  };
}>;

export interface CreateTagRequest {
  name: string;
  displayName: Record<string, string>; // { "en": "Bug Fix", "fa": "رفع اشکال" }
  description?: Record<string, string>;
  categoryId?: string;
  color?: string;
  icon?: string;
  weight?: number;
  isSystem?: boolean;
  reportTypes?: { reportType: string; weight: number }[];
}

export interface UpdateTagRequest {
  name?: string;
  displayName?: Record<string, string>;
  description?: Record<string, string>;
  categoryId?: string;
  color?: string;
  icon?: string;
  weight?: number;
  isActive?: boolean;
  reportTypes?: { reportType: string; weight: number }[];
}

export interface TagUsageStats {
  tagId: string;
  tagName: string;
  usageCount: number;
  lastUsedAt: Date | null;
  aiRelevance: number;
}

export interface TagSuggestion {
  tag: Tag;
  confidence: number;
  reason: string;
}

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface TagCategoriesResponse {
  categories: TagCategoryWithCount[];
  total: number;
}

export interface PredefinedTagsResponse {
  tags: TagWithCategoryAndReportTypes[];
  total: number;
  categories: TagCategory[];
}

export interface TagsByReportTypeResponse {
  reportType: string;
  tags: TagWithCategoryAndReportTypes[];
  total: number;
}

/**
 * Service for managing the enhanced tag system
 */
export class TagService {
  constructor(private prisma: PrismaClient) {}

  // ==================== CORE CRUD OPERATIONS ====================
  /**
   * Get all tags with optional filtering
   */
  async getAllTags(includeInactive = false): Promise<TagWithCategoryReportTypesAndCount[]> {
    try {
      const tags = await this.prisma.tag.findMany({
        where: includeInactive ? {} : { isActive: true },
        include: {
          category: true,
          reportTypes: true,
          _count: {
            select: { reportTags: true }
          }
        },
        orderBy: [
          { weight: 'desc' },
          { usageCount: 'desc' },
          { name: 'asc' }
        ]
      });

      return tags;
    } catch (error) {
      console.error('❌ [TagService] Failed to get all tags:', error);
      throw error;
    }
  }
  /**
   * Get tags by category
   */
  async getTagsByCategory(categoryId: string): Promise<TagWithCategoryAndReportTypes[]> {
    try {
      const tags = await this.prisma.tag.findMany({
        where: {
          categoryId,
          isActive: true
        },
        include: {
          category: true,
          reportTypes: true
        },
        orderBy: [
          { weight: 'desc' },
          { usageCount: 'desc' },
          { name: 'asc' }
        ]
      });

      return tags;
    } catch (error) {
      console.error('❌ [TagService] Failed to get tags by category:', error);
      throw error;
    }
  }
  /**
   * Get tags relevant to a specific report type
   */
  async getTagsByReportType(reportType: string): Promise<TagWithCategoryAndFilteredReportTypes[]> {
    try {
      const tags = await this.prisma.tag.findMany({
        where: {
          isActive: true,
          OR: [
            // Tags explicitly associated with this report type
            {
              reportTypes: {
                some: {
                  reportType: reportType.toUpperCase()
                }
              }
            },
            // System tags that are generally applicable
            {
              isSystem: true,
              weight: { gte: 0 }
            }
          ]
        },
        include: {
          category: true,
          reportTypes: {
            where: {
              reportType: reportType.toUpperCase()
            }
          }
        },
        orderBy: [
          { weight: 'desc' },
          { usageCount: 'desc' },
          { name: 'asc' }
        ]
      });

      return tags;
    } catch (error) {
      console.error('❌ [TagService] Failed to get tags by report type:', error);
      throw error;
    }
  }
  /**
   * Create a new tag
   */
  async createTag(data: CreateTagRequest): Promise<TagWithCategoryAndReportTypes> {
    try {
      const tag = await this.prisma.tag.create({
        data: {
          name: data.name,
          displayName: data.displayName,
          description: data.description,
          categoryId: data.categoryId,
          color: data.color,
          icon: data.icon,
          weight: data.weight ?? 0,
          isSystem: data.isSystem ?? false,
          reportTypes: data.reportTypes ? {
            create: data.reportTypes.map(rt => ({
              reportType: rt.reportType.toUpperCase(),
              weight: rt.weight
            }))
          } : undefined
        },
        include: {
          category: true,
          reportTypes: true
        }
      });

      console.log(`✅ [TagService] Created tag: ${tag.name}`);
      return tag;
    } catch (error) {
      console.error('❌ [TagService] Failed to create tag:', error);
      throw error;
    }
  }
  /**
   * Update an existing tag
   */
  async updateTag(id: string, data: UpdateTagRequest): Promise<TagWithCategoryAndReportTypes> {
    try {
      // If updating report types, we need to handle them separately
      const updateData: any = {
        name: data.name,
        displayName: data.displayName,
        description: data.description,
        categoryId: data.categoryId,
        color: data.color,
        icon: data.icon,
        weight: data.weight,
        isActive: data.isActive
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      const tag = await this.prisma.tag.update({
        where: { id },
        data: updateData,
        include: {
          category: true,
          reportTypes: true
        }
      });

      // Handle report type associations separately if provided
      if (data.reportTypes) {
        // Delete existing associations
        await this.prisma.tagReportTypeAssociation.deleteMany({
          where: { tagId: id }
        });

        // Create new associations
        if (data.reportTypes.length > 0) {
          await this.prisma.tagReportTypeAssociation.createMany({
            data: data.reportTypes.map(rt => ({
              tagId: id,
              reportType: rt.reportType.toUpperCase(),
              weight: rt.weight
            }))
          });
        }
      }

      console.log(`✅ [TagService] Updated tag: ${tag.name}`);
      return tag;
    } catch (error) {
      console.error('❌ [TagService] Failed to update tag:', error);
      throw error;
    }
  }

  /**
   * Delete a tag (soft delete by marking inactive)
   */
  async deleteTag(id: string): Promise<void> {
    try {
      await this.prisma.tag.update({
        where: { id },
        data: { isActive: false }
      });

      console.log(`✅ [TagService] Soft deleted tag: ${id}`);
    } catch (error) {
      console.error('❌ [TagService] Failed to delete tag:', error);
      throw error;
    }
  }

  // ==================== LEGACY COMPATIBILITY METHODS ====================

  /**
   * Get all tag categories (legacy compatibility)
   */
  async getCategories(): Promise<TagCategoriesResponse> {
    try {
      const categories = await this.prisma.tagCategory.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: { tags: true }
          }
        },
        orderBy: { order: 'asc' }
      });

      return {
        categories,
        total: categories.length
      };
    } catch (error) {
      console.error('❌ [TagService] Failed to get categories:', error);
      throw error;
    }
  }

  /**
   * Get predefined tags (legacy compatibility)
   */
  async getPredefinedTags(filters: {
    reportType?: string;
    categoryId?: string;
    isActive?: boolean;
    language?: 'en' | 'fa';
  } = {}): Promise<PredefinedTagsResponse> {
    try {
      const {
        reportType,
        categoryId,
        isActive = true,
        language = 'en'
      } = filters;

      const whereClause: any = {};
      
      if (isActive !== undefined) {
        whereClause.isActive = isActive;
      }

      if (categoryId) {
        whereClause.categoryId = categoryId;
      }

      if (reportType) {
        whereClause.reportTypes = {
          some: {
            reportType: reportType.toUpperCase()
          }
        };
      }

      const tags = await this.prisma.tag.findMany({
        where: whereClause,
        include: {
          category: true,
          reportTypes: true
        },
        orderBy: [
          { weight: 'desc' },
          { usageCount: 'desc' },
          { name: 'asc' }
        ]
      });

      const categories = await this.getCategories();

      return {
        tags,
        total: tags.length,
        categories: categories.categories
      };
    } catch (error) {
      console.error('❌ [TagService] Failed to get predefined tags:', error);
      throw error;
    }
  }

  /**
   * Get tags for a specific report type (legacy compatibility)
   */
  async getTagsForReportType(reportType: string, language: 'en' | 'fa' = 'en'): Promise<TagsByReportTypeResponse> {
    const result = await this.getPredefinedTags({ reportType, language });
    
    return {
      reportType,
      tags: result.tags,
      total: result.total
    };
  }
  /**
   * Get tags formatted for AI context
   */
  async getTagsForAiContext(language: 'en' | 'fa' = 'en'): Promise<Record<string, string[]>> {
    try {
      const tags = await this.getAllTags();
      
      const tagsByType: Record<string, string[]> = {};
      
      for (const tag of tags) {
        // Handle the reportTypes relation properly
        const reportTypeAssociations = (tag as any).reportTypes || [];
        for (const association of reportTypeAssociations) {
          if (!tagsByType[association.reportType]) {
            tagsByType[association.reportType] = [];
          }
          tagsByType[association.reportType].push(tag.name);
        }
      }
      
      return tagsByType;
    } catch (error) {
      console.error('❌ [TagService] Failed to get tags for AI context:', error);
      throw error;
    }
  }

  // ==================== CATEGORY MANAGEMENT ====================

  /**
   * Create a new tag category
   */
  async createCategory(data: {
    name: string;
    description?: string;
    color?: string;
    order?: number;
  }): Promise<TagCategory> {
    try {
      const category = await this.prisma.tagCategory.create({
        data: {
          name: data.name,
          description: data.description,
          color: data.color,
          order: data.order ?? 0
        }
      });

      console.log(`✅ [TagService] Created category: ${category.name}`);
      return category;
    } catch (error) {
      console.error('❌ [TagService] Failed to create category:', error);
      throw error;
    }
  }

  // ==================== ANALYTICS & INTELLIGENCE ====================

  /**
   * Get tag usage statistics
   */
  async getTagUsageStats(timeRange?: DateRange): Promise<TagUsageStats[]> {
    try {
      const whereClause: any = {};
      
      if (timeRange) {
        whereClause.createdAt = {
          gte: timeRange.startDate,
          lte: timeRange.endDate
        };
      }

      const tagStats = await this.prisma.tag.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          usageCount: true,
          lastUsedAt: true,
          aiRelevance: true,
          _count: {
            select: {
              reportTags: timeRange ? {
                where: {
                  createdAt: {
                    gte: timeRange.startDate,
                    lte: timeRange.endDate
                  }
                }
              } : true
            }
          }
        },
        orderBy: { usageCount: 'desc' }
      });

      return tagStats.map(tag => ({
        tagId: tag.id,
        tagName: tag.name,
        usageCount: timeRange ? tag._count.reportTags : tag.usageCount,
        lastUsedAt: tag.lastUsedAt,
        aiRelevance: tag.aiRelevance ?? 0
      }));
    } catch (error) {
      console.error('❌ [TagService] Failed to get tag usage stats:', error);
      throw error;
    }
  }
  /**
   * Get popular tags
   */
  async getPopularTags(limit = 10): Promise<TagWithCategory[]> {
    try {
      const tags = await this.prisma.tag.findMany({
        where: { isActive: true },
        include: {
          category: true
        },
        orderBy: [
          { usageCount: 'desc' },
          { name: 'asc' }
        ],
        take: limit
      });

      return tags;
    } catch (error) {
      console.error('❌ [TagService] Failed to get popular tags:', error);
      throw error;
    }
  }

  /**
   * Update tag usage when used in a report
   */
  async incrementTagUsage(tagId: string): Promise<void> {
    try {
      await this.prisma.tag.update({
        where: { id: tagId },
        data: {
          usageCount: { increment: 1 },
          lastUsedAt: new Date()
        }
      });
    } catch (error) {
      console.error('❌ [TagService] Failed to increment tag usage:', error);
      throw error;
    }
  }

  /**
   * Get suggested tags based on content analysis
   */
  async getSuggestedTags(description: string, reportType: string): Promise<TagSuggestion[]> {
    try {
      // Get relevant tags for this report type
      const relevantTags = await this.getTagsByReportType(reportType);
      
      // Simple keyword matching for now - can be enhanced with AI
      const suggestions: TagSuggestion[] = [];
      const descriptionLower = description.toLowerCase();

      relevantTags.forEach(tag => {
        const tagName = tag.name.toLowerCase();
        let confidence = 0;

        // Check if tag name appears in description
        if (descriptionLower.includes(tagName)) {
          confidence = 0.8;
        }
        // Check for related keywords based on tag name
        else if (this.checkKeywordRelevance(descriptionLower, tagName)) {
          confidence = 0.6;
        }
        // Check AI relevance score
        else if (tag.aiRelevance && tag.aiRelevance > 0.5) {
          confidence = tag.aiRelevance * 0.5;
        }

        if (confidence > 0.3) {
          suggestions.push({
            tag,
            confidence,
            reason: `Content analysis suggests this tag is relevant (${Math.round(confidence * 100)}% confidence)`
          });
        }
      });

      // Sort by confidence and return top suggestions
      return suggestions
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, 5);
    } catch (error) {
      console.error('❌ [TagService] Failed to get suggested tags:', error);
      throw error;
    }
  }

  /**
   * Simple keyword relevance checking
   */
  private checkKeywordRelevance(description: string, tagName: string): boolean {
    const keywordMappings: Record<string, string[]> = {
      'urgent': ['critical', 'important', 'immediate', 'asap'],
      'slow': ['performance', 'loading', 'speed', 'lag'],
      'error': ['exception', 'crash', 'fail', 'bug'],
      'design': ['ui', 'interface', 'layout', 'visual'],
      'enhancement': ['improve', 'better', 'feature', 'add']
    };

    const keywords = keywordMappings[tagName] || [];
    return keywords.some(keyword => description.includes(keyword));
  }

  // ==================== MIGRATION HELPERS ====================

  /**
   * Ensure default categories exist
   */
  async ensureDefaultCategories(): Promise<Record<string, TagCategory>> {
    return await this.createDefaultCategories();
  }
  /**
   * Create a predefined tag with category lookup by name
   */
  async createPredefinedTag(data: {
    name: string;
    displayName: Record<string, string>;
    description?: Record<string, string>;
    categoryName?: string;
    color?: string;
    icon?: string;
    weight?: number;
    isActive?: boolean;
    isSystem?: boolean;
  }): Promise<TagWithCategoryAndReportTypes> {
    try {
      let categoryId: string | undefined;
      
      if (data.categoryName) {
        const category = await this.prisma.tagCategory.findUnique({
          where: { name: data.categoryName }
        });
        categoryId = category?.id;
      }

      const tag = await this.prisma.tag.create({
        data: {
          name: data.name,
          displayName: data.displayName,
          description: data.description,
          categoryId,
          color: data.color,
          icon: data.icon,
          weight: data.weight ?? 0,
          isActive: data.isActive ?? true,
          isSystem: data.isSystem ?? false
        },
        include: {
          category: true,
          reportTypes: true
        }
      });

      console.log(`✅ [TagService] Created predefined tag: ${data.name}`);
      return tag;
    } catch (error) {
      console.error(`❌ [TagService] Failed to create predefined tag ${data.name}:`, error);
      throw error;
    }
  }
  /**
   * Update a predefined tag
   */
  async updatePredefinedTag(data: UpdateTagRequest & { id: string }): Promise<TagWithCategoryAndReportTypes> {
    return await this.updateTag(data.id, data);
  }

  /**
   * Delete (deactivate) a predefined tag
   */
  async deletePredefinedTag(id: string): Promise<void> {
    await this.deleteTag(id);
  }

  /**
   * Migrate existing hardcoded tags to the new system
   */
  async migrateExistingTags(): Promise<void> {
    try {
      console.log('🔄 [TagService] Starting tag migration...');

      // Create default categories
      const categories = await this.createDefaultCategories();
      
      // Create default tags
      await this.createDefaultTags(categories);

      console.log('✅ [TagService] Tag migration completed');
    } catch (error) {
      console.error('❌ [TagService] Tag migration failed:', error);
      throw error;
    }
  }
  private async createDefaultCategories(): Promise<Record<string, TagCategory>> {
    const categoriesData = [
      { name: 'Priority', description: 'Tags indicating priority and urgency', color: '#ef4444', order: 1 },
      { name: 'Technical', description: 'Technical issue related tags', color: '#dc2626', order: 2 },
      { name: 'Feature', description: 'Feature requests and ideas', color: '#3b82f6', order: 3 },
      { name: 'Improvement', description: 'Enhancement and improvement tags', color: '#10b981', order: 4 },
      { name: 'Performance', description: 'Performance related tags', color: '#f59e0b', order: 5 },
      { name: 'UI/UX', description: 'User interface and experience tags', color: '#8b5cf6', order: 6 },
      { name: 'Support', description: 'Support and help related tags', color: '#6b7280', order: 7 },
      { name: 'General', description: 'General purpose tags', color: '#6b7280', order: 8 }
    ];

    const categories: Record<string, TagCategory> = {};
    
    for (const categoryData of categoriesData) {
      try {
        const category = await this.prisma.tagCategory.create({
          data: categoryData
        });
        categories[category.name] = category;
      } catch (error) {
        // Category might already exist, try to find it
        const existingCategory = await this.prisma.tagCategory.findUnique({
          where: { name: categoryData.name }
        });
        if (existingCategory) {
          categories[existingCategory.name] = existingCategory;
        }
      }
    }

    return categories;
  }
  private async createDefaultTags(categories: Record<string, TagCategory>): Promise<void> {
    const tagsData = [
      // Priority tags
      {
        name: 'urgent',
        displayName: { en: 'Urgent', fa: 'فوری' },
        description: { en: 'Requires immediate attention', fa: 'نیاز به توجه فوری' },
        categoryId: categories['Priority']?.id,
        color: '#dc2626',
        icon: 'alert-triangle',
        weight: 10,
        isSystem: true,
        reportTypes: [
          { reportType: 'BUG', weight: 10 },
          { reportType: 'PERFORMANCE', weight: 8 }
        ]
      },
      {
        name: 'fix-needed',
        displayName: { en: 'Fix Needed', fa: 'نیاز به رفع' },
        description: { en: 'Requires a fix', fa: 'نیاز به رفع اشکال' },
        categoryId: categories['Priority']?.id,
        color: '#ef4444',
        icon: 'wrench',
        weight: 8,
        isSystem: true,
        reportTypes: [{ reportType: 'BUG', weight: 10 }]
      },
      {
        name: 'error',
        displayName: { en: 'Error', fa: 'خطا' },
        description: { en: 'Application error', fa: 'خطای برنامه' },
        categoryId: categories['Priority']?.id,
        color: '#dc2626',
        icon: 'x-circle',
        weight: 9,
        isSystem: true,
        reportTypes: [{ reportType: 'BUG', weight: 10 }]
      },
      // Feature tags
      {
        name: 'enhancement',
        displayName: { en: 'Enhancement', fa: 'بهبود' },
        description: { en: 'Feature enhancement', fa: 'بهبود قابلیت' },
        categoryId: categories['Feature']?.id,
        color: '#10b981',
        icon: 'trending-up',
        weight: 7,
        isSystem: true,
        reportTypes: [
          { reportType: 'FEATURE_REQUEST', weight: 10 },
          { reportType: 'IMPROVEMENT', weight: 8 }
        ]
      },
      {
        name: 'new-feature',
        displayName: { en: 'New Feature', fa: 'قابلیت جدید' },
        description: { en: 'Request for new feature', fa: 'درخواست قابلیت جدید' },
        categoryId: categories['Feature']?.id,
        color: '#3b82f6',
        icon: 'plus-circle',
        weight: 8,
        isSystem: true,
        reportTypes: [{ reportType: 'FEATURE_REQUEST', weight: 10 }]
      },
      // Performance tags
      {
        name: 'slow',
        displayName: { en: 'Slow', fa: 'آهسته' },
        description: { en: 'Performance issue', fa: 'مشکل عملکرد' },
        categoryId: categories['Performance']?.id,
        color: '#f59e0b',
        icon: 'clock',
        weight: 7,
        isSystem: true,
        reportTypes: [{ reportType: 'PERFORMANCE', weight: 10 }]
      },
      {
        name: 'optimization',
        displayName: { en: 'Optimization', fa: 'بهینه‌سازی' },
        description: { en: 'Needs optimization', fa: 'نیاز به بهینه‌سازی' },
        categoryId: categories['Performance']?.id,
        color: '#f59e0b',
        icon: 'zap',
        weight: 6,
        isSystem: true,
        reportTypes: [{ reportType: 'PERFORMANCE', weight: 10 }]
      },
      // UI/UX tags
      {
        name: 'design',
        displayName: { en: 'Design', fa: 'طراحی' },
        description: { en: 'Design related issue', fa: 'مسئله مربوط به طراحی' },
        categoryId: categories['UI/UX']?.id,
        color: '#8b5cf6',
        icon: 'palette',
        weight: 6,
        isSystem: true,
        reportTypes: [{ reportType: 'UI_UX', weight: 10 }]
      },
      {
        name: 'user-experience',
        displayName: { en: 'User Experience', fa: 'تجربه کاربری' },
        description: { en: 'User experience issue', fa: 'مسئله تجربه کاربری' },
        categoryId: categories['UI/UX']?.id,
        color: '#8b5cf6',
        icon: 'user',
        weight: 7,
        isSystem: true,
        reportTypes: [{ reportType: 'UI_UX', weight: 10 }]
      },
      // General tags
      {
        name: 'help',
        displayName: { en: 'Help', fa: 'کمک' },
        description: { en: 'User needs help', fa: 'کاربر نیاز به کمک دارد' },
        categoryId: categories['General']?.id,
        color: '#6b7280',
        icon: 'help-circle',
        weight: 5,
        isSystem: true,
        reportTypes: [{ reportType: 'QUESTION', weight: 10 }]
      }
    ];

    for (const tagData of tagsData) {
      try {
        await this.createTag(tagData);
      } catch (error) {
        console.log(`Tag ${tagData.name} may already exist, skipping...`);
      }
    }
  }
}
