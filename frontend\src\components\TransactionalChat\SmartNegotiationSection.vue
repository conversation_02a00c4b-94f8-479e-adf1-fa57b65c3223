<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useMessage } from 'naive-ui'
import { usePayerNegotiationStore } from '@/stores/payerNegotiation'
import { useAuthStore } from '@/stores/auth'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'
import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic'
import { useTimerDisplay } from '@/composables/useTimerDisplay'
import { NegotiationStatus } from '@/types/payerNegotiation'

interface Props {
  transactionId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  negotiationUpdate: [negotiationData: any]
  negotiationFinalized: [finalizedPayerId: string]
}>()

const { t } = useI18n()
const naiveMessage = useMessage()
const payerNegotiationStore = usePayerNegotiationStore()
const authStore = useAuthStore()
const transactionalChatStore = useTransactionalChatStore()

// Get timer functionality from the transaction flow logic
const {
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
} = useTransactionFlowLogic(computed(() => transactionalChatStore.currentChatSessionId), naiveMessage)

// Use shared timer display logic
const {
  timerDisplayValue,
  timerLabel
} = useTimerDisplay({
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
});

// Use the t function from useI18n composable for translations
const translate = t

// State management
const customMessage = ref<string>('')
const showMessageInput = ref<boolean>(false)
const isSubmitting = ref<boolean>(false)
const showFinalWarning = ref<boolean>(false)
const showFirstCounterWarning = ref<boolean>(false)
const isTransitioning = ref<boolean>(false)

// Computed properties from real backend data
const negotiation = computed(() => payerNegotiationStore.currentNegotiation)
const currentUserId = computed(() => authStore.user?.id)
const isUserRecommended = computed(() => {
  if (!negotiation.value || !currentUserId.value) return false

  return negotiation.value.systemRecommendedPayerId === currentUserId.value
})

const otherUserId = computed(() => {
  if (!negotiation.value || !currentUserId.value) return null
  return negotiation.value.partyA_Id === currentUserId.value
    ? negotiation.value.partyB_Id
    : negotiation.value.partyA_Id
})

const otherUserInfo = computed(() => {
  // Get real user information from transactional chat store
  const otherUser = transactionalChatStore.otherUser
  if (otherUser) {
    return {
      name: otherUser.name,
      reputation: otherUser.reputation || 5
    }
  }

  // Fallback if transactional chat store doesn't have user info yet
  return {
    name: otherUserId.value ? `User ${otherUserId.value.substring(0, 6)}` : 'Other User',
    reputation: 5 // Default value
  }
})

const hasCurrentUserAgreed = computed(() => {
  if (!negotiation.value || !currentUserId.value) return false
  const isPartyA = negotiation.value.partyA_Id === currentUserId.value
  return isPartyA
    ? negotiation.value.partyA_agreedToCurrentProposal
    : negotiation.value.partyB_agreedToCurrentProposal
})

// New computed properties for enhanced design patterns
const negotiationState = computed(() => {
  if (!negotiation.value) return 'loading'
  
  const status = negotiation.value.negotiationStatus
  const hasCurrentProposal = !!negotiation.value.currentProposal_PayerId
  const isCurrentUserProposer = negotiation.value.currentProposal_ById === currentUserId.value
  const hasSystemRecommendation = !!negotiation.value.systemRecommendedPayerId
  
  // Determine the current UI state based on the design document
  if (status === NegotiationStatus.PENDING_RECEIVING_INFO || 
      status === NegotiationStatus.AWAITING_PARTY_A_RECEIVING_INFO || 
      status === NegotiationStatus.AWAITING_PARTY_B_RECEIVING_INFO) {
    return 'loading' // Still waiting for payment information
  } else if (status === NegotiationStatus.READY_TO_NEGOTIATE) {
    // When ready to negotiate, show system recommendation if available
    if (hasSystemRecommendation) {
      // If the current user has already agreed to the system recommendation,
      // they should be in waiting state
      if (hasCurrentUserAgreed.value) {
        return 'waiting-for-response'
      }
      return 'system-recommendation'
    }
    // If no system recommendation but has current proposal, treat as pending response
    if (hasCurrentProposal) {
      if (isCurrentUserProposer) {
        return 'waiting-for-response'
      } else {
        return 'user-proposal'
      }
    }
  } else if (status === NegotiationStatus.PENDING_RESPONSE && hasCurrentProposal) {
    if (isCurrentUserProposer) {
      return 'waiting-for-response'
    } else {
      // If it's a system proposal/recommendation and the current user has already agreed,
      // they should be in a waiting state.
      if ((negotiation.value.currentProposal_ById === 'system' || hasSystemRecommendation) && hasCurrentUserAgreed.value) {
        return 'waiting-for-response'
      }
      return 'user-proposal'
    }
  } else if (status === NegotiationStatus.FINALIZED) {
    return 'finalized'
  } else if (status === NegotiationStatus.CANCELLED || status === NegotiationStatus.EXPIRED) {
    return 'cancelled'
  }
  
  return 'loading'
})

const currentProposalInfo = computed(() => {
  if (!negotiation.value?.currentProposal_PayerId) return null
  
  const proposedPayerId = negotiation.value.currentProposal_PayerId
  const proposerUserId = negotiation.value.currentProposal_ById
  const message = negotiation.value.currentProposal_Message
  
  const isUserProposed = proposedPayerId === currentUserId.value
  const proposerName = proposerUserId === currentUserId.value ? 'You' : otherUserInfo.value.name
  
  return {
    proposedPayerId,
    proposerUserId,
    message,
    isUserProposed,
    proposerName,
    payerName: isUserProposed ? 'You' : otherUserInfo.value.name
  }
})

// New computed property to check if the user is receiving a final proposal
const isReceivingFinalProposal = computed(() => {
  if (!negotiation.value || !currentProposalInfo.value) return false;

  const isFinal = negotiation.value.isFinalOffer && currentProposalInfo.value.proposerUserId !== currentUserId.value;
  

  return isFinal;
});

// Check if this would be a second counter-offer (final proposal)
const wouldBeFinalProposal = computed(() => {
  return negotiationState.value === 'user-proposal' && !currentProposalInfo.value?.isUserProposed
})

const isCurrentUserFirstPayer = computed(() => {
  // In states driven by a proposal (either receiving one or waiting for a response), the proposal is the source of truth.
  if ((negotiationState.value === 'user-proposal' || negotiationState.value === 'waiting-for-response') && currentProposalInfo.value) {
    return currentProposalInfo.value.proposedPayerId === currentUserId.value;
  }
  
  // In the system recommendation state, the recommendation is the source of truth.
  // This also serves as a good fallback for initial states.
  return isUserRecommended.value;
});

const firstPayerName = computed(() => isCurrentUserFirstPayer.value ? translate('common.you') : otherUserInfo.value.name);
const secondPayerName = computed(() => isCurrentUserFirstPayer.value ? otherUserInfo.value.name : translate('common.you'));

const currentUserPayment = computed(() => {
  const td = transactionalChatStore.transactionDetails;
  if (!td) return { amount: 0, currency: '' };
  // Based on the store, amountToSend/currencyFrom is what the current user sends
  return { amount: td.amountToSend, currency: td.currencyFrom };
});

const otherUserPayment = computed(() => {
  const td = transactionalChatStore.transactionDetails;
  if (!td) return { amount: 0, currency: '' };
  // amountToReceive/currencyTo is what the current user receives (i.e., what the other user sends)
  return { amount: td.amountToReceive, currency: td.currencyTo };
});

const firstPaymentAmount = computed(() => {
  const transactionData = transactionalChatStore.transactionDetails;
  if (!transactionData) return '';

  // If the current user is the first payer, the amount is what they are designated to send.
  // Otherwise, it's the amount the other user is designated to send.
  return isCurrentUserFirstPayer.value
    ? formatAmount(currentUserPayment.value.amount, currentUserPayment.value.currency)
    : formatAmount(otherUserPayment.value.amount, otherUserPayment.value.currency);
});

const secondPaymentAmount = computed(() => {
  const transactionData = transactionalChatStore.transactionDetails;
  if (!transactionData) return '';

  // If the current user is the first payer, the second payment is from the other user.
  // Otherwise, the second payment is from the current user.
  return isCurrentUserFirstPayer.value
    ? formatAmount(otherUserPayment.value.amount, otherUserPayment.value.currency)
    : formatAmount(currentUserPayment.value.amount, currentUserPayment.value.currency);
});

const formatAmount = (amount: number, currency: string) => {
  // Handle empty or invalid currency codes
  if (!currency || currency.trim() === '') {
    return `${amount.toLocaleString()}`
  }
  
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  } catch (error) {
    // Fallback if currency is invalid
    console.warn(`Invalid currency code: ${currency}`, error)
    return `${currency} ${amount.toLocaleString()}`
  }
}

// Computed property to get a properly formatted system recommendation reason
const systemRecommendationReason = computed(() => {
  if (!negotiation.value?.systemRecommendationReason) return ''
  
  const reason = negotiation.value.systemRecommendationReason
  
  // Check if the reason contains reputation-related information
  const reputationMatch = reason.match(/(\w+) has a lower reputation level \((\d+)\)/)
  if (reputationMatch) {
    const [, , reputationLevel] = reputationMatch
    
    if (isUserRecommended.value) {
      // The current user is the one being recommended to pay first
      return translate('transactionalChat.actionCards.negotiation.reasons.lowerReputation', { 
        level: reputationLevel 
      })
    } else {
      // The other user is being recommended
      return translate('transactionalChat.actionCards.negotiation.reasons.lowerReputationOther', {
        name: otherUserInfo.value.name,
        level: reputationLevel
      })
    }
  }
  
  // Check for other known reason patterns and localize them
  if (reason.includes('lower amount')) {
    return translate('transactionalChat.actionCards.negotiation.reasons.lowerAmount')
  }
  
  if (reason.includes('trust score') || reason.includes('reputation')) {
    return translate('transactionalChat.actionCards.negotiation.reasons.trustScore')
  }
  
  // For any other reasons, return as fallback
  return translate('transactionalChat.actionCards.negotiation.reasons.trustScore')
})

// Enhanced methods following the design document patterns
const handleAcceptSystemRecommendation = async () => {
  if (isSubmitting.value || !negotiation.value) return
  
  isSubmitting.value = true
  
  try {
    await payerNegotiationStore.acceptCurrentProposal(props.transactionId)
    naiveMessage.success(translate('transactionalChat.actionCards.negotiation.recommendationAccepted'))
    emit('negotiationUpdate', negotiation.value)
  } catch (error) {
    console.error('Failed to accept system recommendation:', error)
    naiveMessage.error(translate('transactionalChat.actionCards.negotiation.actionFailed'))
  } finally {
    isSubmitting.value = false
  }
}

const handleAcceptUserProposal = async () => {
  if (isSubmitting.value || !negotiation.value) return
  
  isSubmitting.value = true
  
  try {
    await payerNegotiationStore.acceptCurrentProposal(props.transactionId)
    naiveMessage.success(translate('transactionalChat.actionCards.negotiation.proposalAccepted'))
    emit('negotiationUpdate', negotiation.value)
  } catch (error) {
    console.error('Failed to accept user proposal:', error)
    naiveMessage.error(translate('transactionalChat.actionCards.negotiation.actionFailed'))
  } finally {
    isSubmitting.value = false
  }
}

const handleRequestCounterOffer = () => {
  // For a second counter-offer, show the final warning modal
  if (wouldBeFinalProposal.value) {
    showFinalWarning.value = true;
  } else {
    // For the first counter-offer, show a less severe warning
    showFirstCounterWarning.value = true;
  }
  
  // Scroll to bottom to ensure action buttons are visible
  setTimeout(() => {
    const cardElement = document.querySelector('[data-testid="smart-negotiation-section"]');
    if (cardElement) {
      cardElement.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  }, 100);
};

const handleConfirmFinalProposal = () => {
  showFinalWarning.value = false
  showMessageInput.value = true
}

const handleCancelFinalProposal = () => {
  showFinalWarning.value = false
}

const handleConfirmFirstCounter = () => {
  showFirstCounterWarning.value = false;
  showMessageInput.value = true;
};

const handleCancelFirstCounter = () => {
  showFirstCounterWarning.value = false;
};

const handleProposeCounter = async () => {
  if (isSubmitting.value || !negotiation.value || !currentUserId.value) return
  
  
  isSubmitting.value = true
  
  try {
    // Propose that the OTHER user pays first (counter to current proposal/recommendation)
    const proposedPayerId = otherUserId.value
    if (!proposedPayerId) {
      naiveMessage.error('Unable to determine other user ID')
      return
    }
    const counterMessage = customMessage.value.trim() || undefined
    
    
    await payerNegotiationStore.proposeFirstPayer(props.transactionId, proposedPayerId, counterMessage)
    
    // Reset UI state
    showMessageInput.value = false
    showFinalWarning.value = false
    customMessage.value = ''
    
    naiveMessage.success(translate('transactionalChat.actionCards.negotiation.counterProposed'))
    emit('negotiationUpdate', negotiation.value)
  } catch (error) {
    console.error('Failed to propose counter offer:', error)
    naiveMessage.error(translate('transactionalChat.actionCards.negotiation.actionFailed'))
  } finally {
    isSubmitting.value = false
  }
}

const handleDeclineProposal = async () => {
  if (isSubmitting.value || !negotiation.value) return
  isSubmitting.value = true
  try {
    // Decline the proposal by canceling the negotiation
    await payerNegotiationStore.cancelNegotiation(props.transactionId, 'Proposal declined by user.')
    naiveMessage.info(translate('transactionalChat.actionCards.negotiation.transactionCancelled'))
    emit('negotiationUpdate', negotiation.value)
  } catch (error) {
    console.error('Failed to decline proposal:', error)
    naiveMessage.error(translate('transactionalChat.actionCards.negotiation.actionFailed'))
  } finally {
    isSubmitting.value = false
  }
}

// Watch for negotiation changes and fetch initial data
watch(() => props.transactionId, async (newTransactionId) => {
  if (newTransactionId) {
    try {
      await payerNegotiationStore.fetchNegotiation(newTransactionId)
    } catch (error) {
      console.error('Failed to fetch negotiation:', error)
      naiveMessage.error('Failed to load negotiation data')
    }
  }
}, { immediate: true })

// Watch for negotiation finalization to emit event to parent
watch(() => negotiation.value?.negotiationStatus, (newStatus, oldStatus) => {
  if (newStatus === NegotiationStatus.FINALIZED && oldStatus !== NegotiationStatus.FINALIZED) {
    isTransitioning.value = true
    if (negotiation.value?.finalizedPayerId) {
      emit('negotiationFinalized', negotiation.value.finalizedPayerId)
    }
    
    // Show transitioning state for a few seconds
    setTimeout(() => {
      isTransitioning.value = false
    }, 3000)
  }
})

// Initialize socket listeners
payerNegotiationStore.initializeSocketListeners()
</script>

<template>
  <div class="negotiation-card" data-testid="smart-negotiation-section">
    
    <!-- Card Header & Status (Consistent across all states) -->
    <div class="card-header">
      <h3 class="card-title" data-testid="negotiation-title">
        {{ translate('transactionalChat.actionCards.negotiation.whoShouldPayFirst') }}
      </h3>
      <div class="card-status" data-testid="negotiation-status">
        <template v-if="negotiationState === 'loading'">
          <span class="status-text loading">{{ translate('transactionalChat.actionCards.negotiation.loadingNegotiation') }}</span>
        </template>
        <template v-else-if="negotiationState === 'system-recommendation'">
          <span class="status-text">{{ translate('transactionalChat.actionCards.negotiation.systemRecommendation') }}</span>
        </template>
        <template v-else-if="negotiationState === 'user-proposal'">
          <span class="status-text">{{ translate('transactionalChat.actionCards.negotiation.newProposal', { name: currentProposalInfo?.proposerName }) }}</span>
        </template>
        <template v-else-if="negotiationState === 'waiting-for-response'">
          <span class="status-text">{{ translate('transactionalChat.actionCards.negotiation.waitingForResponse') }}</span>
        </template>
        <template v-else-if="negotiationState === 'finalized'">
          <span class="status-text success">{{ translate('transactionalChat.actionCards.negotiation.agreementReached') }}</span>
        </template>
        <template v-else-if="negotiationState === 'cancelled'">
          <span class="status-text error">{{ translate('transactionalChat.actionCards.negotiation.transactionCancelled') }}</span>
        </template>
      </div>
    </div>

    <!-- Content Body (Dynamic based on state) -->
    <div class="card-content">
      
      <!-- Timer Section -->
      <div v-if="timerDisplayValue" class="timer-area" :class="{ 
        'critical': isTimerCritical, 
        'info': !isTimerCritical && !isTimerExpired && !isElapsedTimer,
        'expired': isTimerExpired,
        'elapsed': isElapsedTimer
      }" data-testid="negotiation-timer">
        <div class="timer-content">
          <span class="timer-label">{{ timerLabel }}</span>
          <span class="timer-value" :class="{ 
            'timer-critical': isTimerCritical, 
            'timer-expired': isTimerExpired,
            'timer-elapsed': isElapsedTimer
          }">
            {{ timerDisplayValue }}
          </span>
        </div>
      </div>
      
      <!-- Loading State -->
      <div v-if="negotiationState === 'loading'" class="content-loading">
        <div class="loading-spinner" data-testid="loading-spinner">⏳</div>
        <span class="loading-message">{{ translate('transactionalChat.actionCards.negotiation.loadingDetails') }}</span>
      </div>

      <!-- System Recommendation View -->
      <template v-else-if="negotiationState === 'system-recommendation'">
        <!-- Central Recommendation Statement -->
        <div class="recommendation-statement" data-testid="recommendation-statement">
          <div class="recommendation-main">
            {{ isUserRecommended 
              ? translate('transactionalChat.actionCards.negotiation.youShouldPayFirst') 
              : translate('transactionalChat.actionCards.negotiation.otherShouldPayFirst', { name: otherUserInfo.name })
            }}
          </div>
          <div class="recommendation-reason">
            <span class="info-icon" data-testid="info-icon">ⓘ</span>
            {{ translate('transactionalChat.actionCards.negotiation.why') }} 
            {{ systemRecommendationReason || translate('transactionalChat.actionCards.negotiation.reasons.trustScore') }}
          </div>
        </div>

        <!-- Smart Visual Payment Flow -->
        <div 
          class="payment-flow-smart" 
          :class="{ 'updated': negotiationState === 'user-proposal' }"
          data-testid="payment-flow-smart"
        >
          <!-- Step 1 -->
          <div class="flow-step-smart">
            <div class="step-badge">1</div>
            <div class="step-content">
              <span class="participant payer">{{ firstPayerName }}</span>
              <div class="payment-arrow">
                <span class="payment-direction">→</span>
                <span class="amount">{{ firstPaymentAmount }}</span>
                <span class="payment-direction">→</span>
              </div>
              <span class="participant receiver">{{ secondPayerName }}</span>
            </div>
          </div>
          <!-- Step 2 -->
          <div class="flow-step-smart">
            <div class="step-badge">2</div>
            <div class="step-content">
              <span class="participant payer">{{ secondPayerName }}</span>
              <div class="payment-arrow">
                <span class="payment-direction">→</span>
                <span class="amount">{{ secondPaymentAmount }}</span>
                <span class="payment-direction">→</span>
              </div>
              <span class="participant receiver">{{ firstPayerName }}</span>
            </div>
          </div>
        </div>

        <!-- Message Input (Expanded when needed) -->
        <div v-if="showMessageInput" class="message-input-section" data-testid="message-input-section">
          <div class="message-header">
            <span>{{ translate('transactionalChat.actionCards.negotiation.addMessage') }}</span>
            <span class="optional-label">{{ translate('common.optional') }}</span>
          </div>
          <textarea 
            v-model="customMessage"
            class="message-textarea"
            :placeholder="translate('transactionalChat.actionCards.negotiation.messagePlaceholder')"
            data-testid="custom-message-input"
            maxlength="300"
          ></textarea>
          <div class="message-footer">
            <span class="char-count">{{ customMessage.length }}/300</span>
          </div>
        </div>
      </template>

      <!-- User Proposal View -->
      <template v-else-if="negotiationState === 'user-proposal'">
        <!-- Proposal Message (Chat-like) -->
        <div v-if="currentProposalInfo?.message" class="proposal-message-display" data-testid="proposal-message">
          <div class="message-header">
            <strong>{{ currentProposalInfo.proposerName }}</strong> {{ translate('transactionalChat.actionCards.negotiation.says') }}:
          </div>
          <div class="message-content">
            "{{ currentProposalInfo.message }}"
          </div>
        </div>

        <!-- New Proposal Statement -->
        <div class="proposal-statement" data-testid="proposal-statement">
          {{ translate('transactionalChat.actionCards.negotiation.theyRequestingToPaySecond') }}
        </div>

        <!-- Smart Visual Payment Flow -->
        <div 
          class="payment-flow-smart" 
          :class="{ 'updated': negotiationState === 'user-proposal' }"
          data-testid="payment-flow-smart"
        >


          <!-- Step 1 -->
          <div class="flow-step-smart">
            <div class="step-badge">1</div>
            <div class="step-content">
              <span class="participant payer">{{ firstPayerName }}</span>
              <div class="payment-arrow">
                <span class="payment-direction">→</span>
                <span class="amount">{{ firstPaymentAmount }}</span>
                <span class="payment-direction">→</span>
              </div>
              <span class="participant receiver">{{ secondPayerName }}</span>
            </div>
          </div>
          <!-- Step 2 -->
          <div class="flow-step-smart">
            <div class="step-badge">2</div>
            <div class="step-content">
              <span class="participant payer">{{ secondPayerName }}</span>
              <div class="payment-arrow">
                <span class="payment-direction">→</span>
                <span class="amount">{{ secondPaymentAmount }}</span>
                <span class="payment-direction">→</span>
              </div>
              <span class="participant receiver">{{ firstPayerName }}</span>
            </div>
          </div>
        </div>

        <!-- Final Proposal Warning (Inline) -->
        <div v-if="wouldBeFinalProposal" class="final-proposal-warning" data-testid="final-proposal-warning">
          <div class="warning-content">
            <span class="warning-icon">⚠️</span>
            <div class="warning-text">
              <div class="warning-title">{{ translate('transactionalChat.actionCards.negotiation.finalOfferNotice') }}</div>
              <div class="warning-message">{{ translate('transactionalChat.actionCards.negotiation.discussFirstRecommendation') }}</div>
            </div>
          </div>
        </div>

        <!-- Message Input for Counter-Offer -->
        <div v-if="showMessageInput" class="message-input-section" data-testid="counter-message-input">
          <div class="message-header">
            <span>{{ translate('transactionalChat.actionCards.negotiation.explainYourRequest') }}</span>
            <span class="optional-label">{{ translate('common.optional') }}</span>
          </div>
          <textarea 
            v-model="customMessage"
            class="message-textarea"
            :placeholder="translate('transactionalChat.actionCards.negotiation.counterMessagePlaceholder')"
            data-testid="counter-custom-message-input"
            maxlength="300"
          ></textarea>
          <div class="message-footer">
            <span class="char-count">{{ customMessage.length }}/300</span>
          </div>
        </div>
      </template>

      <!-- Waiting State -->
      <template v-else-if="negotiationState === 'waiting-for-response'">
        <div class="content-waiting" data-testid="waiting-content">
          <div class="waiting-animation">
            <div class="waiting-dots">
              <span>.</span><span>.</span><span>.</span>
            </div>
          </div>
          <div class="waiting-message">
            {{ translate('transactionalChat.actionCards.negotiation.waitingMessage', { name: otherUserInfo.name }) }}
          </div>

          <!-- Visual representation of the proposal -->
          <div 
            class="payment-flow-smart" 
            data-testid="payment-flow-smart-waiting"
          >
            <!-- Step 1 -->
            <div class="flow-step-smart">
              <div class="step-badge">1</div>
              <div class="step-content">
                <span class="participant payer">{{ firstPayerName }}</span>
                <div class="payment-arrow">
                  <span class="payment-direction">→</span>
                  <span class="amount">{{ firstPaymentAmount }}</span>
                  <span class="payment-direction">→</span>
                </div>
                <span class="participant receiver">{{ secondPayerName }}</span>
              </div>
            </div>
            <!-- Step 2 -->
            <div class="flow-step-smart">
              <div class="step-badge">2</div>
              <div class="step-content">
                <span class="participant payer">{{ secondPayerName }}</span>
                <div class="payment-arrow">
                  <span class="payment-direction">→</span>
                  <span class="amount">{{ secondPaymentAmount }}</span>
                  <span class="payment-direction">→</span>
                </div>
                <span class="participant receiver">{{ firstPayerName }}</span>
              </div>
            </div>
          </div>

          <div v-if="currentProposalInfo?.message" class="sent-message-summary">
            <div class="summary-label">{{ translate('transactionalChat.actionCards.negotiation.yourMessage') }}:</div>
            <div class="summary-content">"{{ currentProposalInfo.message }}"</div>
          </div>
        </div>
      </template>

      <!-- Finalized State -->
      <template v-else-if="negotiationState === 'finalized'">
        <div class="content-success" data-testid="success-content">
          <div class="success-animation">
            <div class="success-checkmark">✔</div>
          </div>
          <div class="success-message">
            {{ negotiation?.finalizedPayerId === currentUserId 
              ? translate('transactionalChat.actionCards.negotiation.youWillPayFirst')
              : translate('transactionalChat.actionCards.negotiation.otherWillPayFirst', { name: otherUserInfo.name })
            }}
          </div>
          <div class="transition-message">
            {{ translate('transactionalChat.actionCards.negotiation.proceedingToPayment') }}
          </div>
          <div v-if="isTransitioning" class="transition-loader">
            <div class="transition-dots">
              <span>.</span><span>.</span><span>.</span>
            </div>
            <div class="transition-text">
              {{ translate('transactionalChat.actionCards.negotiation.preparingPaymentPhase') }}
            </div>
          </div>
        </div>
      </template>

      <!-- Cancelled State -->
      <template v-else-if="negotiationState === 'cancelled'">
        <div class="content-cancelled" data-testid="cancelled-content">
          <div class="cancelled-icon">✗</div>
          <div class="cancelled-message">
            {{ translate('transactionalChat.actionCards.negotiation.cancelledDescription') }}
          </div>
        </div>
      </template>
    </div>
    <!-- Action Footer (Dynamic buttons based on state) -->
    <div class="card-actions" data-testid="card-actions">
      <!-- Message Input Actions -->
      <template v-if="showMessageInput">
        <div class="message-actions">
          <button 
            class="action-btn secondary"
            @click="showMessageInput = false; customMessage = ''"
            data-testid="cancel-message-btn"
          >
            {{ translate('common.cancel') }}
          </button>
          <button 
            class="action-btn primary"
            :disabled="isSubmitting"
            data-testid="send-proposal-btn"
            @click="handleProposeCounter"
          >
            <span v-if="isSubmitting" class="btn-spinner">⟳</span>
            <span v-else class="btn-icon">→</span>
            {{ translate('transactionalChat.actionCards.negotiation.sendProposal') }}
          </button>
        </div>
      </template>

      <!-- System Recommendation Actions -->
      <template v-else-if="negotiationState === 'system-recommendation'">
        <div v-if="isUserRecommended" class="actions-grid">
          <button 
            class="action-btn primary"
            :disabled="isSubmitting"
            data-testid="accept-recommendation-btn"
            @click="handleAcceptSystemRecommendation"
          >
            <span v-if="isSubmitting" class="btn-spinner">⟳</span>
            <span v-else class="btn-icon">✓</span>
            {{ translate('transactionalChat.actionCards.negotiation.iAgree') }}
          </button>
          <button 
            class="action-btn secondary"
            :disabled="isSubmitting"
            data-testid="request-counter-btn"
            @click="handleRequestCounterOffer"
          >
            {{ translate('transactionalChat.actionCards.negotiation.proposeTheyPayFirst') }}
          </button>
        </div>
        <div v-else class="actions-single">
          <button 
            class="action-btn primary large"
            :disabled="isSubmitting"
            data-testid="agree-to-recommendation-btn"
            @click="handleAcceptSystemRecommendation"
          >
            <span v-if="isSubmitting" class="btn-spinner">⟳</span>
            <span v-else class="btn-icon">✓</span>
            {{ translate('transactionalChat.actionCards.negotiation.iAgree') }}
          </button>
          <div class="waiting-status">
            {{ translate('transactionalChat.actionCards.negotiation.waitingForOtherResponse', { name: otherUserInfo.name }) }}
          </div>
        </div>
      </template>

      <!-- User Proposal Actions -->
      <template v-else-if="negotiationState === 'user-proposal'">
        <div class="actions-grid">
          <button 
            class="action-btn primary"
            :disabled="isSubmitting"
            data-testid="accept-proposal-btn"
            @click="handleAcceptUserProposal"
          >
            <span v-if="isSubmitting" class="btn-spinner">⟳</span>
            <span v-else class="btn-icon">✓</span>
            {{ translate('transactionalChat.actionCards.negotiation.agreeToProposal') }}
          </button>
          
          <!-- In final offer, show Decline button in the grid -->
          <button 
            v-if="isReceivingFinalProposal"
            class="action-btn tertiary"
            :disabled="isSubmitting"
            data-testid="decline-proposal-btn"
            @click="handleDeclineProposal"
          >
            {{ translate('transactionalChat.actionCards.negotiation.decline') }}
          </button>

          <!-- Otherwise, show the Propose Alternative button -->
          <button 
            v-else
            class="action-btn secondary"
            :disabled="isSubmitting"
            data-testid="counter-proposal-btn"
            @click="handleRequestCounterOffer"
          >
            {{ translate('transactionalChat.actionCards.negotiation.proposeAlternative') }}
            <span v-if="wouldBeFinalProposal" class="final-badge">{{ translate('transactionalChat.actionCards.negotiation.final') }}</span>
          </button>
        </div>
        <!-- Full-width decline button is only shown when it's NOT a final offer -->
        <button 
          v-if="!isReceivingFinalProposal"
          class="action-btn tertiary full-width"
          :disabled="isSubmitting"
          data-testid="decline-proposal-btn-full"
          @click="handleDeclineProposal"
        >
          {{ translate('transactionalChat.actionCards.negotiation.decline') }} & {{ translate('transactionalChat.actionCards.negotiation.cancel') }}
        </button>
      </template>

      <!-- No actions for waiting, finalized, or cancelled states -->
    </div>

    <!-- First Counter-Offer Warning Modal -->
    <teleport to="body">
      <div v-if="showFirstCounterWarning" class="modal-overlay" data-testid="first-counter-warning-modal">
        <div class="modal-card">
          <div class="modal-header">
            <span class="modal-icon">💬</span>
            <h4 class="modal-title">{{ translate('transactionalChat.actionCards.negotiation.proposeAlternativeTitle') }}</h4>
          </div>
          <div class="modal-content">
            <p class="modal-message">{{ translate('transactionalChat.actionCards.negotiation.proposeAlternativeDescription') }}</p>
          </div>
          <div class="modal-actions">
            <button class="modal-btn secondary" @click="handleCancelFirstCounter">
              {{ translate('common.cancel') }}
            </button>
            <button class="modal-btn primary" @click="handleConfirmFirstCounter">
              {{ translate('transactionalChat.actionCards.negotiation.proceed') }}
            </button>
          </div>
        </div>
      </div>
    </teleport>

    <!-- Final Warning Confirmation Modal -->
    <teleport to="body">
      <div v-if="showFinalWarning" class="modal-overlay" data-testid="final-warning-modal">
        <div class="modal-card">
          <div class="modal-header">
            <span class="modal-icon">⚠️</span>
            <h4 class="modal-title">{{ translate('transactionalChat.actionCards.negotiation.finalOfferWarning') }}</h4>
          </div>
          <div class="modal-content">
            <p class="modal-message">{{ translate('transactionalChat.actionCards.negotiation.finalOfferDescription') }}</p>
          </div>
          <div class="modal-actions">
            <button class="modal-btn secondary" @click="handleCancelFinalProposal">
              {{ translate('transactionalChat.actionCards.negotiation.discussInChatFirst') }}
            </button>
            <button class="modal-btn primary" @click="handleConfirmFinalProposal">
              {{ translate('transactionalChat.actionCards.negotiation.proceed') }}
            </button>
          </div>
        </div>
      </div>
    </teleport>

  </div>
</template>

<style scoped>
@import './SmartNegotiationSection.module.scss';
</style>