<template>
  <header class="dashboard-header">
    <div class="header-content">
      <div class="title-section">
        <h1>Debug Reports Dashboard</h1>
        <p>Manage and review client-side debug reports</p>
      </div>      <div class="header-actions">
        <button @click="$emit('refresh')" :disabled="loading" class="btn btn-primary">
          {{ loading ? 'Refreshing...' : 'Refresh' }}
        </button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
defineOptions({
  name: 'DebugDashboardHeader'
});

defineProps<{
  loading: boolean;
}>();

defineEmits(['refresh']);
</script>

<style scoped>
.dashboard-header {
  background-color: var(--bg-surface, #fff);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-base, #e0e0e0);
  box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0,0,0,0.1));
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1440px;
  margin: 0 auto;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet (769px - 968px) */
@media (max-width: 968px) and (min-width: 769px) {
  .dashboard-header {
    padding: 0.875rem 1.25rem;
  }

  .title-section h1 {
    font-size: 1.375rem;
  }

  .title-section p {
    font-size: 0.8rem;
  }

  .header-actions {
    gap: 0.5rem;
  }

  .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

/* Mobile (481px - 768px) */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 0.75rem 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    text-align: center;
  }

  .title-section h1 {
    font-size: 1.25rem;
    margin-bottom: 0.125rem;
  }

  .title-section p {
    font-size: 0.75rem;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .btn {
    padding: 0.5rem 0.875rem;
    font-size: 0.8rem;
    min-width: 100px;
  }
}

/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .dashboard-header {
    padding: 0.625rem 0.75rem;
  }

  .title-section h1 {
    font-size: 1.125rem;
  }

  .header-actions {
    gap: 0.375rem;
  }

  .btn {
    padding: 0.4rem 0.75rem;
    font-size: 0.75rem;
    min-width: 90px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px; /* Minimum touch target size */
  }

  .btn:hover {
    /* Disable hover effects on touch devices */
    background-color: initial;
    border-color: initial;
  }

  .btn:active {
    transform: scale(0.98);
  }
}

.title-section h1 {
  margin: 0 0 0.25rem 0;
  font-size: var(--font-size-2xl, 1.5rem);
  color: var(--text-primary, #333);
}

.title-section p {
  margin: 0;
  font-size: var(--font-size-sm, 0.875rem);
  color: var(--text-secondary, #666);
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md, 6px);
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-semibold, 600);
  font-size: var(--font-size-sm, 0.875rem);
  transition: all 0.2s ease-in-out;
}

.btn:focus {
  outline: 2px solid var(--primary-500, #3b82f6);
  outline-offset: 2px;
}

.btn-primary {
  background-color: var(--primary-500, #3b82f6);
  color: white;
  border-color: var(--primary-500, #3b82f6);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-600, #2563eb);
  border-color: var(--primary-600, #2563eb);
}

.btn-primary:disabled {
  background-color: var(--primary-300, #93c5fd);
  border-color: var(--primary-300, #93c5fd);
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--gray-200, #e2e8f0);
  color: var(--text-primary, #333);
  border-color: var(--gray-300, #cbd5e1);
}

.btn-secondary:hover {
  background-color: var(--gray-300, #cbd5e1);
  border-color: var(--gray-400, #94a3b8);
}

/* ===== DARK MODE SUPPORT ===== */

[data-theme="dark"] .dashboard-header {
  background-color: var(--bg-surface-dark, #1f2937);
  border-bottom-color: var(--border-base-dark, #374151);
}

[data-theme="dark"] .title-section h1 {
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .title-section p {
  color: var(--text-secondary-dark, #d1d5db);
}

[data-theme="dark"] .btn-primary {
  background-color: var(--primary-600, #2563eb);
  border-color: var(--primary-600, #2563eb);
}

[data-theme="dark"] .btn-primary:hover:not(:disabled) {
  background-color: var(--primary-700, #1d4ed8);
  border-color: var(--primary-700, #1d4ed8);
}

[data-theme="dark"] .btn-secondary {
  background-color: var(--gray-700, #374151);
  color: var(--text-primary-dark, #f9fafb);
  border-color: var(--gray-600, #4b5563);
}

[data-theme="dark"] .btn-secondary:hover {
  background-color: var(--gray-600, #4b5563);
  border-color: var(--gray-500, #6b7280);
}
</style>