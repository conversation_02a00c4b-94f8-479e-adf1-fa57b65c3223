# CentOS 9 Production Deployment - Quick Start Guide

## 🚀 **Automated Deployment Scripts Ready**

I've created CentOS 9-compatible deployment scripts with your actual database credentials:

### **📁 Files Created/Updated:**
- ✅ `deploy-production.sh` - Automated deployment script for CentOS 9
- ✅ `verify-deployment.sh` - Post-deployment verification script  
- ✅ `PRODUCTION_DEPLOYMENT_GUIDE.md` - Complete deployment guide

### **🔐 Database Credentials Configured:**
- **Database Name**: `munygo_db`
- **Database User**: `munygo_user`  
- **Database Password**: `U6^#A7sBp&tE%qgRt5Ra`

## 🎯 **Quick Deployment Process**

### **On your CentOS 9 production machine:**

```bash
# 1. Navigate to your MUNygo project directory
cd /path/to/your/munygo/project

# 2. Make scripts executable
chmod +x deploy-production.sh verify-deployment.sh

# 3. Run automated deployment
./deploy-production.sh

# 4. Verify deployment
./verify-deployment.sh
```

## 🔧 **What the Scripts Do**

### **deploy-production.sh:**
- ✅ **Creates database backup** using your actual credentials
- ✅ **Pulls latest code** from git
- ✅ **Validates environment** variables
- ✅ **Zero-downtime deployment** (stops only backend during migration)
- ✅ **Runs Prisma migrations** for debug report tables
- ✅ **Rebuilds containers** with latest code
- ✅ **Verifies deployment** success

### **verify-deployment.sh:**
- ✅ **Checks container health** (munygo-postgres, munygo-backend, munygo-frontend)
- ✅ **Tests API endpoints** (health checks)
- ✅ **Verifies database schema** (debug report tables)
- ✅ **Analyzes logs** for errors
- ✅ **Validates environment** variables
- ✅ **Checks volumes and storage**

## 🛡️ **Safety Features**

### **Automatic Backup:**
```bash
# Backup created before deployment
./backups/backup_YYYYMMDD_HHMMSS.sql
```

### **Rollback Commands:**
```bash
# If deployment fails, rollback:
git log --oneline -10  # Find previous commit
git checkout previous_commit_hash
docker compose up -d --build

# Restore database if needed:
docker exec -i munygo-postgres psql -U munygo_user -d munygo_db < backup_file.sql
```

## 📊 **New Features After Deployment**

### **For Users:**
- Enhanced debug report submission UI
- Better error reporting and feedback
- Mobile-optimized debug reporting

### **For Admins:**
- Complete admin dashboard for debug reports
- Report status management (NOT_REVIEWED, IN_PROGRESS, COMPLETED, etc.)
- Internal comment system for reports
- Report assignment and filtering
- Comprehensive analytics and statistics

## 🔍 **Manual Verification Checklist**

After running the scripts, manually verify:

- [ ] Visit your frontend URL
- [ ] Test debug report submission
- [ ] Log in as admin user
- [ ] Check admin dashboard shows debug reports
- [ ] Test report status changes
- [ ] Verify internal comments work
- [ ] Check report assignment functionality

## 🌐 **Expected Ports & Services**

### **Your Production Setup:**
- **Frontend**: `http://localhost:8081` 
- **Backend API**: `http://localhost:3004`
- **Database**: `localhost:5432` (internal to Docker)

### **Container Names:**
- `munygo-postgres` - PostgreSQL database
- `munygo-backend` - Hono API server  
- `munygo-frontend` - Vue.js application

## 🚨 **Troubleshooting**

### **If Containers Won't Start:**
```bash
# Check logs
docker compose logs backend --tail=50
docker compose logs frontend --tail=50

# Check container status
docker compose ps

# Restart specific service
docker compose restart backend
```

### **If Migration Fails:**
```bash
# Check migration status
docker compose run --rm backend npx prisma migrate status

# Manual migration (if needed)
docker compose run --rm backend npx prisma migrate deploy
```

### **If Database Connection Fails:**
```bash
# Test database connection
docker exec munygo-postgres pg_isready -U munygo_user -d munygo_db

# Connect to database manually
docker exec -it munygo-postgres psql -U munygo_user -d munygo_db
```

## 📞 **Support Commands**

### **Real-time Monitoring:**
```bash
# Monitor all logs
docker compose logs -f

# Monitor specific service
docker compose logs -f backend

# Check resource usage
docker stats
```

### **Health Checks:**
```bash
# Test backend API
curl http://localhost:3004/health

# Test frontend
curl http://localhost:8081

# Check database tables
docker exec munygo-postgres psql -U munygo_user -d munygo_db -c "\dt DebugReport*"
```

---

## 🎉 **Ready for Deployment!**

Your CentOS 9 production deployment scripts are configured with the correct database credentials and ready to deploy the debug report system update safely.

**Estimated Deployment Time**: 5-10 minutes  
**Expected Downtime**: < 2 minutes (with zero-downtime option)

Run `./deploy-production.sh` when ready to deploy!
