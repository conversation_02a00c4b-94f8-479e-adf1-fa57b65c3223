# Production Debug Diagnosis Instructions

## Step 1: Run Database Diagnosis

Connect to your production server and run these commands:

```bash
# Navigate to the backend directory in your production environment
cd /path/to/your/production/backend

# Run the database diagnosis
node diagnose-production-debug.js
```

## Step 2: Run Service Test

```bash
# Test the debug report service specifically
node test-debug-service-production.js
```

## Step 3: Check Production Logs

```bash
# Check the backend application logs for any errors
docker logs your-backend-container-name

# Or if using docker-compose:
docker-compose logs backend
```

## Step 4: Check Database Migration Status

```bash
# Check if all migrations have been applied
npx prisma migrate status

# If migrations are missing, run:
npx prisma migrate deploy
```

## Step 5: Verify Environment Variables

```bash
# Check if all required environment variables are set
echo "DATABASE_URL: $DATABASE_URL" | sed 's/:[^:@]*@/:****@/g'
echo "VITE_ADMIN_EMAILS: $VITE_ADMIN_EMAILS"
```

## Common Issues and Solutions

### Issue 1: DebugReport table doesn't exist
**Solution:** Run Prisma migrations
```bash
npx prisma migrate deploy
```

### Issue 2: Environment variables missing
**Solution:** Check your production .env file or Docker environment variables

### Issue 3: Database permissions
**Solution:** Verify the database user has proper read/write permissions

### Issue 4: Recent code changes not deployed
**Solution:** Ensure the latest backend code with the updated formatReportForAPI is deployed

## Manual API Test

You can also test the API endpoint directly:

```bash
# Test the API endpoint (replace with your admin JWT token)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     https://arzani.husotech.com/api/debug/admin/reports?page=1&limit=10&sortBy=serverReceivedAt&sortOrder=desc
```

## If All Else Fails

If the diagnosis scripts don't reveal the issue, we may need to:

1. Enable more detailed logging in production
2. Check the exact error from the backend logs
3. Temporarily add try-catch blocks with detailed error logging to the debug routes
4. Verify the production database schema matches the local schema
