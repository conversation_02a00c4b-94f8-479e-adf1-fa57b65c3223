import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { createTestingP<PERSON> } from '@pinia/testing';
import { useAuthStore } from '@/stores/auth';
import ProfileView from '../ProfileView.vue';
import {
  NConfigProvider, NMessageProvider, NCard, NSpin, NDescriptions, NDescriptionsItem,
  NTag, NDivider, NH3, NAlert, NForm, NFormItem, NInput, NButton, NIcon, NTooltip
} from 'naive-ui';
import type { UserInfo } from '@/types/auth';
import { ref, computed, nextTick } from 'vue';
import apiClient from '@/services/apiClient';
import type { Mock } from 'vitest';
import { AxiosError } from 'axios'; // Import AxiosError for realistic error mocking

// --- Mock apiClient ---
vi.mock('@/services/apiClient', () => ({
  default: { post: vi.fn() }
}));
const apiClientPostMock = apiClient.post as Mock;

// --- Mock naive-ui useMessage ---
const messageSuccessSpy = vi.fn();
const messageErrorSpy = vi.fn();
vi.mock('naive-ui', async (importOriginal) => {
  const originalNaive = await importOriginal<typeof import('naive-ui')>();
  return {
    ...originalNaive,
    useMessage: () => ({ success: messageSuccessSpy, error: messageErrorSpy }),
  };
});

// --- Mock the auth store module ---
const mockUserRef = ref<UserInfo | null>(null);
const fetchUserProfileMock = vi.fn();
const updatePhoneVerificationStatusMock = vi.fn((status: boolean, phoneNumber: string | null) => {
  if (mockUserRef.value) {
    mockUserRef.value.phoneVerified = status;
    mockUserRef.value.phoneNumber = phoneNumber;
  }
});

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    user: mockUserRef,
    fetchUserProfile: fetchUserProfileMock,
    isLoggedIn: computed(() => !!mockUserRef.value),
    updatePhoneVerificationStatus: updatePhoneVerificationStatusMock,
  }),
}));
// -----------------------------------------

// Mock the error handler utility
vi.mock('@/utils/errorHandler', () => ({
  handleError: vi.fn((err: any, messageMock: any, defaultMessage?: string) => {
    let displayMessage = defaultMessage || 'An unknown error occurred in test';
    if (err?.response?.data?.message) {
      displayMessage = err.response.data.message;
    } else if (err?.message) {
      displayMessage = err.message;
    }

    if (messageMock && typeof messageMock.error === 'function') {
      messageMock.error(displayMessage);
    }
    return displayMessage; // Crucial: mock returns the string
  })
}));


// Required wrapper to provide Naive UI context
const TestWrapper = {
  template: `
    <n-config-provider>
      <n-message-provider>
        <profile-view />
      </n-message-provider>
    </n-config-provider>
  `,
  components: {
    ProfileView, NConfigProvider, NMessageProvider, NCard, NSpin, NDescriptions,
    NDescriptionsItem, NTag, NDivider, NH3, NAlert, NInput, NButton, NIcon, NTooltip
    // Note: NForm and NFormItem are stubbed below
  },
};

describe('ProfileView.vue - Error Handling (Non-Rate Limit)', () => {
  let wrapper: ReturnType<typeof mount>;

  // Function to setup mocks and mount component for each test
  const setupTest = (initialUser: UserInfo) => {
    vi.resetAllMocks();
    mockUserRef.value = initialUser;
    fetchUserProfileMock.mockResolvedValue(undefined);
    // Default API mock - can be overridden in tests
    apiClientPostMock.mockResolvedValue({ data: {} });

    wrapper = mount(TestWrapper, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
        stubs: {
          NForm: {
            name: 'StubbedNForm',
            props: ['style'],
            template: '<form :style="style" @submit.prevent="$emit(\'submit\')"><slot></slot></form>',
            methods: {
              validate(): Promise<void> {
                return Promise.resolve();
              },
              restoreValidation() {
                // no-op
              }
            }
          },
          NFormItem: {
            template: '<div><slot></slot><div class="validation-message"><slot name="feedback"></slot></div></div>',
          }
        }
      },
    });
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  // --- Error Handling Tests ---

  it('shows error message and keeps phone form if requestOtp fails (non-429)', async () => {
    // Arrange
    const initialUser: UserInfo = { id: 'err-user-1', email: '<EMAIL>', emailVerified: true, phoneVerified: false, phoneNumber: null };
    setupTest(initialUser);
    const testPhoneNumber = '+15559876543';
    const apiError = new AxiosError('Request failed');
    apiError.response = { data: { message: 'Server unavailable' }, status: 500, statusText: 'Internal Server Error', headers: {}, config: {} };
    apiClientPostMock.mockRejectedValueOnce(apiError); // Mock failure for send-otp

    await flushPromises();
    await nextTick();

    const phoneInput = wrapper.find('[data-testid="phone-input"] input');
    // ---> FIX: Use specific selector for the phone form
    const phoneForm = wrapper.find('[data-testid="phone-form"]');
    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');

    // Act
    await phoneInput.setValue(testPhoneNumber);
    await sendOtpButton.trigger('click');
    await flushPromises();
    await nextTick();

    // Assert
    expect(apiClientPostMock).toHaveBeenCalledWith('/auth/phone/send-otp', { phoneNumber: testPhoneNumber });
    expect(messageErrorSpy).toHaveBeenCalledWith('Server unavailable');
    // ---> FIX: Use .attributes() and check for undefined (meaning not disabled) <---
    expect(sendOtpButton.attributes('disabled')).toBeUndefined();
    // --------------------------------------------------------------------------
    // Check that the OTP form is not visible, as it's controlled by v-show
    // ---> FIX: Use style check if isVisible() is unreliable for v-show="false"
    expect(wrapper.find('[data-testid="otp-form"]').attributes('style')).toContain('display: none;');
    expect(wrapper.findComponent(ProfileView).vm.showOtpForm).toBe(false);
  });

  it('shows error message and keeps OTP form if verifyOtp fails (non-429)', async () => {
    // Arrange: user not verified and mock send/verify
    setupTest({ id: 'err-user-2', email: '<EMAIL>', emailVerified: true, phoneVerified: false, phoneNumber: null });
    apiClientPostMock
      .mockResolvedValueOnce({ data: { message: 'OTP sent' } })
      .mockRejectedValueOnce(Object.assign(new AxiosError(''), { response: { data: { message: 'Invalid OTP code' } } }));

    // Act 1: Send OTP
    const phoneInput = wrapper.find('[data-testid="phone-input"] input');
    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
    await phoneInput.setValue('+15559876543');
    await sendOtpButton.trigger('click');
    await flushPromises();
    await nextTick();

    // Act 2: Attempt verify OTP and fail
    const otpInput = wrapper.find('[data-testid="otp-input"] input');
    await otpInput.setValue('987654');
    const verifyButton = wrapper.find('[data-testid="verify-otp-button"]');
    await verifyButton.trigger('click');
    await flushPromises();
    await nextTick();

    // Assert: error message called and form stays on OTP step
    expect(messageErrorSpy).toHaveBeenCalledWith('Invalid OTP code');
    expect(wrapper.find('[data-testid="otp-form"]').attributes('style') ?? '').not.toContain('display: none;');
    expect(wrapper.find('[data-testid="phone-form"]').attributes('style')).toContain('display: none;');
    expect(updatePhoneVerificationStatusMock).not.toHaveBeenCalled();
  });

});