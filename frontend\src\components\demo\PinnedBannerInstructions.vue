<template>
  <div class="demo-instructions-container">
    <h2>📌 Testing the Pinned Action Banner</h2>
    
    <div class="instructions-content">
      <div class="instruction-section">
        <h3>What to Look For:</h3>
        <div class="feature-list">
          <div class="feature-item">
            <span class="icon">🎯</span>
            <span>A sticky banner appears at the top when action is required</span>
          </div>
          <div class="feature-item">
            <span class="icon">📱</span>
            <span>Banner stays visible while scrolling through chat messages</span>
          </div>
          <div class="feature-item">
            <span class="icon">🔗</span>
            <span>"View Details" button smoothly scrolls to the full action</span>
          </div>
          <div class="feature-item">
            <span class="icon">✨</span>
            <span>Banner disappears when action is completed</span>
          </div>
        </div>
      </div>
      
      <div class="instruction-section">
        <h3>Best Steps to Test:</h3>
        <div class="test-steps">
          <div class="test-step recommended">
            <span class="step-badge">Step 1</span>
            <span>Payment Info - Shows pinned banner with payment icon</span>
          </div>
          <div class="test-step recommended">
            <span class="step-badge">Step 2</span>
            <span>Negotiation - Shows pinned banner with decision needed</span>
          </div>
          <div class="test-step special">
            <span class="step-badge">Step 4</span>
            <span>Confirm Receipt - Smart Action Bar + Pinned Banner</span>
          </div>
          <div class="test-step recommended">
            <span class="step-badge">Step 7</span>
            <span>Rate Experience - Final action with rating</span>
          </div>
        </div>
      </div>
      
      <div class="instruction-section">
        <h3>How to Test:</h3>
        <div class="test-instructions">
          <div class="test-instruction">
            <span class="number">1</span>
            <span>Click on any step that shows "📌 PINNED" badge</span>
          </div>
          <div class="test-instruction">
            <span class="number">2</span>
            <span>Notice the sticky banner between status bar and chat feed</span>
          </div>
          <div class="test-instruction">
            <span class="number">3</span>
            <span>Scroll through the chat messages - banner stays visible</span>
          </div>
          <div class="test-instruction">
            <span class="number">4</span>
            <span>Click "View Details" to scroll to the full action</span>
          </div>
          <div class="test-instruction">
            <span class="number">5</span>
            <span>Complete the action and watch banner disappear</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="tip-section">
      <div class="tip">
        <span class="tip-icon">💡</span>
        <span><strong>Pro Tip:</strong> Try scrolling through long conversations in Step 4 - the banner ensures you never lose track of what you need to do!</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// This component can be used as a modal or inline instructions
</script>

<style scoped>
.demo-instructions-container {
  background: linear-gradient(135deg, #e6fffa 0%, #f0fff4 100%);
  border: 2px solid #38b2ac;
  border-radius: 16px;
  padding: 24px;
  margin: 20px;
  max-width: 800px;
  margin: 20px auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.demo-instructions-container h2 {
  color: #234e52;
  margin: 0 0 20px 0;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
}

.instructions-content {
  display: grid;
  gap: 24px;
}

.instruction-section h3 {
  color: #2d3748;
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.feature-list {
  display: grid;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  border: 1px solid #bee3f8;
}

.feature-item .icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.test-steps {
  display: grid;
  gap: 8px;
}

.test-step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid #bee3f8;
}

.test-step.recommended {
  border-left: 4px solid #38b2ac;
}

.test-step.special {
  border-left: 4px solid #ed8936;
  background: linear-gradient(135deg, #fff5f0 0%, rgba(255, 255, 255, 0.8) 100%);
}

.step-badge {
  background: #3182ce;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.test-instructions {
  display: grid;
  gap: 8px;
}

.test-instruction {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  border: 1px solid #bee3f8;
}

.test-instruction .number {
  background: #3182ce;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.85rem;
  flex-shrink: 0;
}

.tip-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 2px solid #bee3f8;
}

.tip {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #fff5f0 0%, rgba(255, 255, 255, 0.9) 100%);
  border: 1px solid #fbb6ce;
  border-radius: 8px;
}

.tip-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
  margin-top: 2px;
}

.tip strong {
  color: #744210;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .demo-instructions-container {
    margin: 10px;
    padding: 16px;
  }
  
  .demo-instructions-container h2 {
    font-size: 1.3rem;
  }
  
  .instructions-content {
    gap: 16px;
  }
  
  .instruction-section h3 {
    font-size: 1rem;
  }
  
  .feature-item,
  .test-step,
  .test-instruction {
    padding: 6px;
    gap: 8px;
  }
  
  .tip {
    padding: 12px;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .demo-instructions-container {
    margin: 5px;
    padding: 12px;
  }
  
  .demo-instructions-container h2 {
    font-size: 1.2rem;
  }
  
  .step-badge {
    font-size: 0.7rem;
    padding: 3px 6px;
  }
  
  .test-instruction .number {
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
  }
}
</style>
