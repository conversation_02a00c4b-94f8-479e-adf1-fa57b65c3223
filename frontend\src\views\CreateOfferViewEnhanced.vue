<template>
  <div class="create-offer-view" :class="{ 'enhanced': useSimpleOfferForm }">
    
    <!-- Enhanced Simple Offer Form -->
    <div v-if="useSimpleOfferForm" class="enhanced-offer-container">
      <!-- Hero Section -->
      <div class="offer-hero-section animate-fade-up" :style="{ animationDelay: '0.1s' }">
        <div class="hero-icon-container">
          <n-icon size="48" color="#2080f0">
            <PlusCircleOutlined />
          </n-icon>
        </div>
        <h1 class="hero-title">{{ $t('createOffer.title') }}</h1>
        <p class="hero-subtitle">{{ $t('createOffer.subtitle') }}</p>
      </div>

      <!-- Loading Skeleton -->
      <div v-if="!contentLoaded" class="form-skeleton">
        <div class="skeleton-card">
          <div class="skeleton-header"></div>
          <div class="skeleton-field"></div>
          <div class="skeleton-field"></div>
          <div class="skeleton-field"></div>
          <div class="skeleton-button"></div>
        </div>
      </div>

      <!-- Enhanced Simple Form -->
      <div v-else class="simple-offer-form animate-fade-up" :style="{ animationDelay: '0.3s' }">
        <n-form 
          ref="formRef" 
          :model="simpleForm" 
          :rules="simpleRules" 
          label-placement="top"
          class="offer-form-enhanced"
        >
          <!-- Step 1: Offer Type Selection -->
          <n-card class="form-card offer-type-card" hoverable>
            <template #header>
              <div class="card-header">
                <div class="step-indicator">
                  <span class="step-number">1</span>
                </div>
                <div class="step-content">
                  <h3 class="step-title">{{ $t('createOffer.offerType') }}</h3>
                  <p class="step-subtitle">{{ $t('createOffer.chooseOfferType') }}</p>
                </div>
              </div>
            </template>

            <div class="form-section">
              <n-form-item path="type">
                <n-radio-group 
                  v-model:value="simpleForm.type" 
                  class="offer-type-group enhanced-radio-group"
                  @update:value="handleOfferTypeChange"
                >
                  <div class="radio-option-card" :class="{ active: simpleForm.type === 'SELL' }">
                    <n-radio value="SELL" class="radio-hidden">
                      <div class="radio-content">
                        <div class="radio-icon">
                          <n-icon size="40" color="#e74c3c">
                            <ExportOutlined />
                          </n-icon>
                        </div>
                        <div class="radio-text">
                          <h3>{{ $t('createOffer.sellCAD') }}</h3>
                          <p>{{ $t('createOffer.sellDescription') }}</p>
                        </div>
                      </div>
                    </n-radio>
                  </div>
                  <div class="radio-option-card" :class="{ active: simpleForm.type === 'BUY' }">
                    <n-radio value="BUY" class="radio-hidden">
                      <div class="radio-content">
                        <div class="radio-icon">
                          <n-icon size="40" color="#27ae60">
                            <ImportOutlined />
                          </n-icon>
                        </div>
                        <div class="radio-text">
                          <h3>{{ $t('createOffer.buyCAD') }}</h3>
                          <p>{{ $t('createOffer.buyDescription') }}</p>
                        </div>
                      </div>
                    </n-radio>
                  </div>
                </n-radio-group>
              </n-form-item>
            </div>
          </n-card>

          <!-- Step 2: Amount Configuration -->
          <n-card class="form-card amount-card" hoverable>
            <template #header>
              <div class="card-header">
                <div class="step-indicator">
                  <span class="step-number">2</span>
                </div>
                <div class="step-content">
                  <h3 class="step-title">{{ $t('createOffer.amount') }}</h3>
                  <p class="step-subtitle">{{ $t('createOffer.setAmount') }}</p>
                </div>
              </div>
            </template>

            <div class="form-section">
              <n-form-item path="amount">
                <div class="amount-input-container">
                  <n-input-number
                    v-model:value="simpleForm.amount"
                    :min="0.01"
                    :max="10000"
                    :precision="2"
                    :step="50"
                    size="large"
                    class="amount-input"
                    :placeholder="$t('createOffer.amountPlaceholder')"
                    @update:value="handleAmountChange"
                  >
                    <template #prefix>
                      <span class="currency-prefix">CAD $</span>
                    </template>
                  </n-input-number>
                </div>
              </n-form-item>
            </div>

            <!-- Quick Amount Selection -->
            <div class="quick-amounts-section">
              <h4 class="quick-amounts-title">{{ $t('createOffer.quickAmounts') }}</h4>
              <div class="quick-amounts-grid">
                <n-button 
                  v-for="amount in quickAmounts" 
                  :key="amount"
                  @click="simpleForm.amount = amount"
                  type="tertiary"
                  class="quick-amount-btn"
                  :class="{ 'active': simpleForm.amount === amount }"
                >
                  ${{ amount }}
                </n-button>
              </div>
            </div>
          </n-card>

          <!-- Step 3: Exchange Rate -->
          <n-card class="form-card rate-card" hoverable>
            <template #header>
              <div class="card-header">
                <div class="step-indicator">
                  <span class="step-number">3</span>
                </div>
                <div class="step-content">
                  <h3 class="step-title">{{ $t('createOffer.exchangeRate') }}</h3>
                  <p class="step-subtitle">{{ $t('createOffer.setRate') }}</p>
                </div>
              </div>
            </template>

            <div class="form-section">
              <n-form-item path="rate">
                <div class="rate-input-container">
                  <n-input-number
                    v-model:value="simpleForm.rate"
                    :min="1"
                    :max="100000"
                    :precision="0"
                    size="large"
                    class="rate-input"
                    :placeholder="$t('createOffer.ratePlaceholder')"
                    @update:value="handleRateChange"
                  >
                    <template #prefix>
                      <span class="currency-prefix">IRR</span>
                    </template>
                    <template #suffix>
                      <span class="rate-suffix">per CAD</span>
                    </template>
                  </n-input-number>
                </div>
              </n-form-item>
            </div>

            <!-- Rate Calculation -->
            <div v-if="showRateHelper" class="rate-calculation-section">
              <div class="calculation-card">
                <div class="calculation-header">
                  <n-icon size="20" color="#2080f0">
                    <CalculatorOutlined />
                  </n-icon>
                  <span class="calculation-title">{{ $t('createOffer.calculation') }}</span>
                </div>
                <div class="calculation-content">
                  <div class="calculation-line">
                    <span class="calc-label">{{ $t('createOffer.totalValue') }}:</span>
                    <span class="calc-value">{{ formatCurrency(totalIRR) }} IRR</span>
                  </div>
                  <div class="calculation-line main-calculation">
                    <span class="calc-label">{{ $t('createOffer.youWill') }} {{ simpleForm.type === 'SELL' ? $t('createOffer.receive') : $t('createOffer.pay') }}:</span>
                    <span class="calc-value total-amount">{{ formatCurrency(totalIRR) }} IRR</span>
                  </div>
                </div>
              </div>
            </div>
          </n-card>

          <!-- Submit Section -->
          <div class="submit-section">
            <n-button 
              type="primary" 
              size="large" 
              block
              :loading="isLoading"
              :disabled="!isFormValid"
              @click="handleSubmit"
              class="submit-button enhanced-submit"
            >
              <template #icon>
                <n-icon><CheckOutlined /></n-icon>
              </template>
              {{ $t('createOffer.createOffer') }}
            </n-button>
            
            <div class="submit-helper">
              <n-text depth="3" class="helper-text">
                {{ $t('createOffer.submitHelper') }}
              </n-text>
            </div>
          </div>
        </n-form>
      </div>
    </div>

    <!-- Original Complex Form (Fallback) -->
    <div v-else class="original-offer-container">
      <OfferForm
        mode="create"
        @submit="handleSubmit"
        :loading="isLoading"
        :user-tier="authStore.user?.reputationLevel ?? 3"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useUiPreferencesStore } from '@/stores/uiPreferences'
import { useMessage } from 'naive-ui'
import { 
  NForm, NFormItem, NCard, NButton, NButtonGroup, NIcon, NInputNumber, 
  NRadio, NRadioGroup, NAlert, NText,
  type FormRules
} from 'naive-ui'
import { 
  PlusCircleOutlined, 
  MoneyCollectOutlined,
  ExportOutlined,
  ImportOutlined,
  CheckOutlined,
  CalculatorOutlined
} from '@vicons/antd'
import OfferForm from '@/components/OfferForm.vue'
import { offerService } from '@/services/offerService'
import type { CreateOfferPayload } from '@/types/offer'

const router = useRouter()
const authStore = useAuthStore()
const uiPreferencesStore = useUiPreferencesStore()
const message = useMessage()

// Feature flag for simple offer form
const useSimpleOfferForm = computed(() => uiPreferencesStore.useSimpleOfferForm)

// Loading states
const isLoading = ref(false)
const contentLoaded = ref(false)
const formRef = ref()

// Simple form data
const simpleForm = ref({
  type: 'SELL' as 'SELL' | 'BUY',
  amount: null as number | null,
  rate: null as number | null,
  fromCurrency: 'CAD',
  toCurrency: 'IRR',
  description: ''
})

// Quick amount shortcuts
const quickAmounts = [100, 500, 1000, 2000, 5000]

// Form validation rules
const simpleRules: FormRules = {
  type: [
    {
      required: true,
      message: 'Please select offer type',
      trigger: 'change'
    }
  ],
  amount: [
    {
      required: true,
      validator: (_rule, value) => {
        if (!value || value <= 0) {
          return new Error('Please enter a valid amount greater than 0')
        }
        return true
      },
      trigger: 'blur'
    }
  ],
  rate: [
    {
      required: true,
      validator: (_rule, value) => {
        if (!value || value <= 0) {
          return new Error('Please enter a valid exchange rate greater than 0')
        }
        return true
      },
      trigger: 'blur'
    }
  ]
}

// Computed properties
const totalIRR = computed(() => {
  if (!simpleForm.value.amount || !simpleForm.value.rate) return 0
  return simpleForm.value.amount * simpleForm.value.rate
})

const showRateHelper = computed(() => {
  return simpleForm.value.amount && simpleForm.value.rate && totalIRR.value > 0
})

const isFormValid = computed(() => {
  return simpleForm.value.type && 
         simpleForm.value.amount && 
         simpleForm.value.amount > 0 &&
         simpleForm.value.rate && 
         simpleForm.value.rate > 0
})

// Utility functions
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US').format(amount)
}

// Event handlers
function handleOfferTypeChange(_value: 'SELL' | 'BUY') {
  // Add haptic feedback
  setTimeout(() => {
    const activeCard = document.querySelector('.radio-option-card.active')
    if (activeCard) {
      ;(activeCard as HTMLElement).style.transform = 'scale(0.98)'
      setTimeout(() => {
        ;(activeCard as HTMLElement).style.transform = ''
      }, 150)
    }
  }, 50)
}

function handleAmountChange(value: number | null) {
  // Provide visual feedback for amount changes
  if (value && value > 0) {
    console.log(`💰 Amount changed to: $${value} CAD`)
  }
}

function handleRateChange(value: number | null) {
  // Provide visual feedback for rate changes
  if (value && value > 0) {
    console.log(`📈 Exchange rate changed to: ${value} IRR per CAD`)
  }
}

async function handleSubmit() {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (!authStore.user?.id) {
      message.error('Please log in to create an offer')
      return
    }

    isLoading.value = true

    const payload: CreateOfferPayload = {
      type: simpleForm.value.type,
      amount: simpleForm.value.amount!,
      baseRate: simpleForm.value.rate!,
      // Simplified: no tiered pricing adjustments
      adjustmentForLowerRep: 0,
      adjustmentForHigherRep: 0,
      currencyPair: `${simpleForm.value.fromCurrency}-${simpleForm.value.toCurrency}`,
      description: simpleForm.value.description || undefined,
    }

    await offerService.createOffer(payload)
    message.success('🎉 Offer created successfully!')
    
    // Navigate to offers page
    router.push({ name: 'MyOffers' })
    
  } catch (error: any) {
    console.error('Error creating offer:', error)
    const errorMessage = error?.response?.data?.message || error?.message || 'Failed to create offer'
    message.error(errorMessage)
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Simulate loading for skeleton
  setTimeout(() => {
    contentLoaded.value = true
  }, 600)
})

// Watch for feature flag changes
watch(useSimpleOfferForm, (newValue) => {
  console.log(`🔄 Simple offer form ${newValue ? 'enabled' : 'disabled'}`)
}, { immediate: true })
</script>

<style scoped>
/* Mobile-first enhanced create offer styles */
.create-offer-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0.5rem;
  position: relative;
  overflow-x: hidden;
}

/* Enhanced container */
.enhanced-offer-container {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  padding: 0.5rem 0;
}

/* Hero section */
.offer-hero-section {
  text-align: center;
  margin-bottom: 1rem;
  color: white;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease forwards;
  padding: 0 0.5rem;
}

.hero-icon-container {
  margin-bottom: 0.75rem;
  transform: scale(0);
  animation: scaleIn 0.6s ease 0.3s forwards;
}

.hero-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.3;
}

.hero-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
  line-height: 1.5;
  padding: 0 1rem;
}

/* Loading skeleton */
.form-skeleton {
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.4s forwards;
  padding: 0 0.5rem;
}

.skeleton-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1rem;
  backdrop-filter: blur(20px);
  margin: 0;
}

.skeleton-header {
  height: 24px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 1.5rem;
}

.skeleton-field {
  height: 56px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.skeleton-button {
  height: 48px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 8px;
  margin-top: 1.5rem;
}

/* Enhanced form */
.simple-offer-form {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
  padding: 0 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.offer-form-enhanced {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-card {
  border-radius: 12px;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  margin: 0;
  padding: 0;
}

.form-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Step-based card headers */
.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  margin: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #2080f0, #1668dc);
  border-radius: 50%;
  flex-shrink: 0;
}

.step-number {
  color: white;
  font-weight: 700;
  font-size: 1.25rem;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #1e293b;
}

.step-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
}

.form-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Dark theme support */
[data-theme="dark"] .form-card {
  background: rgba(26, 27, 46, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.12);
}

[data-theme="dark"] .skeleton-header,
[data-theme="dark"] .skeleton-field,
[data-theme="dark"] .skeleton-button {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
}

/* Card header */
.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* Form sections */
.form-section {
  margin-bottom: 1rem;
}

.form-section:last-child {
  margin-bottom: 0;
}

/* Enhanced radio group for offer type */
.enhanced-radio-group {
  width: 100%;
}

.enhanced-radio-group .n-radio-group {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.radio-option-card {
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  touch-action: manipulation;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-option-card:hover {
  border-color: #2080f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(32, 128, 240, 0.2);
}

.radio-option-card.active {
  border-color: #2080f0;
  background: rgba(32, 128, 240, 0.1);
  transform: scale(1.02);
}

.radio-hidden {
  width: 100%;
}

.radio-content {
  text-align: center;
  pointer-events: none;
}

.radio-content h3 {
  margin: 0.25rem 0 0.125rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.radio-content p {
  margin: 0;
  font-size: 0.8rem;
  color: #666;
  line-height: 1.3;
}

/* Dark theme radio cards */
[data-theme="dark"] .radio-option-card {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .radio-content h3 {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .radio-content p {
  color: rgba(255, 255, 255, 0.75);
}

/* Amount input styling */
.amount-input-container {
  width: 100%;
}

.amount-input {
  width: 100%;
}

.currency-prefix {
  font-weight: 600;
  color: #18a058;
  font-size: 1rem;
}

.rate-suffix {
  font-size: 0.875rem;
  color: #666;
}

.amount-helpers {
  margin-top: 0.5rem;
}

/* Quick amounts section */
.quick-amounts-section {
  padding: 1rem;
  background: rgba(248, 250, 252, 0.8);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: 0;
}

.quick-amounts-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #475569;
  margin: 0 0 0.75rem 0;
}

.quick-amounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.5rem;
}

.quick-amount-btn {
  min-height: 44px;
  font-size: 0.9rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.quick-amount-btn:hover {
  border-color: #2080f0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(32, 128, 240, 0.2);
}

.quick-amount-btn.active {
  background: #2080f0;
  color: white;
  border-color: #2080f0;
  transform: scale(1.02);
}

/* Rate input styling */
.rate-input-container {
  width: 100%;
}

.rate-input {
  width: 100%;
}

/* Rate helper */
.rate-helper {
  margin-top: 0.75rem;
}

/* Rate calculation section */
.rate-calculation-section {
  padding: 1rem;
  background: rgba(248, 250, 252, 0.8);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: 0;
}

.calculation-card {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(32, 128, 240, 0.2);
  box-shadow: 0 2px 8px rgba(32, 128, 240, 0.1);
}

.calculation-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.calculation-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
}

.calculation-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.calculation-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
  padding: 0.25rem 0;
}

.calculation-line.main-calculation {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 0.5rem;
  padding-top: 0.75rem;
  font-size: 0.9rem;
}

.calc-label {
  color: #64748b;
  font-weight: 500;
}

.calc-value {
  font-weight: 600;
  color: #1e293b;
}

.total-amount {
  color: #18a058 !important;
  font-size: 1rem;
  font-weight: 700;
}

/* Dark theme for new sections */
[data-theme="dark"] .quick-amounts-section,
[data-theme="dark"] .rate-calculation-section {
  background: rgba(30, 41, 59, 0.8);
  border-top-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .quick-amounts-title {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .calculation-card {
  background: rgba(26, 27, 46, 0.95);
  border-color: rgba(32, 128, 240, 0.3);
}

[data-theme="dark"] .calculation-title {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .calc-label {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .calc-value {
  color: rgba(255, 255, 255, 0.95);
}

/* Submit section */
.submit-section {
  margin-top: 1.5rem;
  padding: 0 0.5rem;
}

.enhanced-submit {
  height: 48px;
  border-radius: 8px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  width: 100%;
  font-size: 1rem;
}

.enhanced-submit:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.enhanced-submit:hover:before {
  left: 100%;
}

.enhanced-submit:active {
  transform: scale(0.98);
}

.submit-helper {
  text-align: center;
  margin-top: 0.75rem;
  padding: 0 0.5rem;
}

.helper-text {
  font-size: 0.8rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Tablet enhancements */
@media (min-width: 768px) {
  .create-offer-view {
    padding: 1rem;
  }
  
  .enhanced-offer-container {
    max-width: 600px;
    padding: 1rem 0;
  }
  
  .offer-hero-section {
    margin-bottom: 1.5rem;
    padding: 0;
  }
  
  .hero-title {
    font-size: 1.75rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
    padding: 0;
  }
  
  .simple-offer-form {
    gap: 1.5rem;
    padding: 0;
  }
  
  .enhanced-radio-group .n-radio-group {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .radio-option-card {
    padding: 1.5rem;
  }
  
  .form-card {
    border-radius: 16px;
  }
  
  .card-header {
    padding: 1.5rem;
  }
  
  .form-section {
    padding: 1.5rem;
  }
  
  .quick-amounts-section,
  .rate-calculation-section {
    padding: 1.5rem;
  }
  
  .quick-amounts-grid {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .submit-section {
    margin-top: 2rem;
    padding: 0;
  }
  
  .submit-helper {
    padding: 0;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  .enhanced-offer-container {
    padding: 2rem 0;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
  
  .simple-offer-form {
    gap: 2rem;
  }
  
  .form-card {
    border-radius: 20px;
  }
  
  .card-header {
    padding: 2rem;
  }
  
  .form-section {
    padding: 2rem;
  }
  
  .quick-amounts-section,
  .rate-calculation-section {
    padding: 2rem;
  }
  
  .radio-option-card {
    padding: 2rem;
  }
  
  .form-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .offer-hero-section,
  .form-skeleton,
  .simple-offer-form,
  .form-card,
  .radio-option-card,
  .enhanced-submit {
    animation: none !important;
    transition: none !important;
  }
  
  .form-card:hover,
  .radio-option-card:hover {
    transform: none !important;
  }
}

/* Original form fallback styling */
.original-offer-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Input field enhancements for mobile */
.n-input-number {
  border-radius: 8px;
}

.n-input-number .n-input__input {
  font-size: 1rem;
  padding: 0.75rem;
}

.n-form-item-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

/* Better touch targets */
.n-button {
  min-height: 44px;
  touch-action: manipulation;
}

.n-input {
  min-height: 44px;
}

.n-input-number {
  min-height: 44px;
}

/* Mobile keyboard optimization */
.amount-input input[type="number"] {
  -webkit-appearance: none;
  -moz-appearance: textfield;
  appearance: none;
}

.rate-input input[type="number"] {
  -webkit-appearance: none;
  -moz-appearance: textfield;
  appearance: none;
}
</style>
