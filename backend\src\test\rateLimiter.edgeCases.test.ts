import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import OtpRateLimiter from '../utils/rateLimiter_new';

// Helper function to wait for a specified time
const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

describe('OtpRateLimiter Edge Cases', () => {
  let rateLimiter: OtpRateLimiter;

  beforeEach(() => {
    // Get a fresh instance before each test
    rateLimiter = OtpRateLimiter.getInstance();
    rateLimiter.clearLimits(); // Clear existing limits
    vi.useFakeTimers(); // Use fake timers for time-related tests
  });

  afterEach(() => {
    // Clean up after each test
    vi.restoreAllMocks();
    vi.useRealTimers(); // Restore real timers
  });

  describe('Race Condition Handling', () => {
    it('should handle concurrent operations safely', async () => {
      const phoneNumber = '+15551234567';
      
      // Set up multiple concurrent operations
      const concurrentPromises = [];
      for (let i = 0; i < 5; i++) {
        concurrentPromises.push(Promise.resolve().then(() => {
          rateLimiter.recordSendAttempt(phoneNumber);
        }));
      }
      
      // Wait for all operations to complete
      await Promise.all(concurrentPromises);
      
      // Should have recorded 5 attempts
      const result = rateLimiter.checkSendLimit(phoneNumber);
      
      // If MAX_SEND_ATTEMPTS is 3, this should be blocked
      expect(result.allowed).toBe(false);
      expect(result.remainingAttempts).toBe(0);
      expect(result.blockedUntil).toBeDefined();
    });
  });

  describe('Memory Management', () => {
    it('should properly clean up stale entries', () => {
      // Mock a large number of entries
      const phoneNumbers = Array.from({ length: 100 }, (_, i) => `+1555000${i.toString().padStart(4, '0')}`);
      
      // Record send attempts for all numbers
      phoneNumbers.forEach(num => {
        rateLimiter.recordSendAttempt(num);
      });
      
      // Mock a private method to check entries size
      const getPrivateEntriesSize = () => {
        // @ts-ignore - Accessing private property for testing
        return rateLimiter['limits']?.size || 0;
      };
      
      // Verify entries were created
      expect(getPrivateEntriesSize()).toBe(100);
      
      // Fast forward time past the entry TTL
      const ttl = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      vi.advanceTimersByTime(ttl + 1000); // A bit more than TTL
      
      // Manually trigger cleanup (normally done by internal timer)
      // @ts-ignore - Accessing private method for testing
      rateLimiter['cleanupStaleEntries']();
      
      // Verify entries were cleaned up
      expect(getPrivateEntriesSize()).toBe(0);
    });
      it('should not clean up blocked entries even if they are old', () => {
      // Mock Date.now to ensure consistent time handling with vi.useFakeTimers
      const now = 1000000;
      vi.setSystemTime(now);
      
      const phoneNumber = '+15551234567';
      
      // Record max attempts to trigger a block
      for (let i = 0; i < 3; i++) { // Assuming MAX_SEND_ATTEMPTS is 3
        rateLimiter.recordSendAttempt(phoneNumber);
      }
      
      // Verify the entry is blocked
      const initialCheck = rateLimiter.checkSendLimit(phoneNumber);
      expect(initialCheck.allowed).toBe(false);
      
      // Fast forward time past the entry TTL but NOT past the block duration
      const ttl = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      const blockDuration = 60 * 60 * 1000; // 1 hour in milliseconds
      
      // Only advance by 30 minutes (less than block duration but still "old" for testing)
      vi.advanceTimersByTime(30 * 60 * 1000);
      
      // Manually trigger cleanup
      // @ts-ignore - Accessing private method for testing
      rateLimiter['cleanupStaleEntries']();
      
      // Entry should still exist because it's blocked
      const afterCleanupCheck = rateLimiter.checkSendLimit(phoneNumber);
      expect(afterCleanupCheck.allowed).toBe(false);
      expect(afterCleanupCheck.blockedUntil).toBeDefined();
      expect(afterCleanupCheck.blockedUntil).toBeGreaterThan(now + 30 * 60 * 1000);
    });
  });

  describe('Phone Number Normalization', () => {
    it('should handle various phone number formats consistently', () => {
      // Different formats of the same number
      const phoneFormats = [
        '+15551234567',
        ' +15551234567 ',
        '****** 123 4567',
        '+****************',
        '******-123-4567'
      ];
      
      // Record an attempt for the first format
      rateLimiter.recordSendAttempt(phoneFormats[0]);
      
      // All formats should register the same attempt count
      for (const format of phoneFormats) {
        const result = rateLimiter.checkSendLimit(format);
        expect(result.remainingAttempts).toBe(2); // Assuming MAX_SEND_ATTEMPTS is 3
      }
    });
    
    it('should handle invalid phone numbers gracefully without crashing', () => {
      const invalidFormats = [
        'not-a-phone',
        '12345',
        '',
        null,
        undefined
      ];
      
      for (const invalid of invalidFormats) {
        // Should not throw for any input
        // @ts-ignore - Deliberately testing invalid input
        expect(() => rateLimiter.checkSendLimit(invalid)).not.toThrow();
        // @ts-ignore - Deliberately testing invalid input
        expect(() => rateLimiter.recordSendAttempt(invalid)).not.toThrow();
      }
    });
  });

  describe('Error Recovery', () => {
    it('should recover after an internal error', () => {
      const phoneNumber = '+15551234567';
      
      // Force an error in the internal getEntry method
      const getEntrySpy = vi.spyOn(rateLimiter as any, 'getEntry').mockImplementationOnce(() => {
        throw new Error('Simulated internal error');
      });
      
      // The check should return a default "allowed" response
      const errorResult = rateLimiter.checkSendLimit(phoneNumber);
      expect(errorResult.allowed).toBe(true);
      expect(errorResult.remainingAttempts).toBe(3); // Default max value
      
      // Next call should work normally after error recovery
      getEntrySpy.mockRestore();
      const normalResult = rateLimiter.checkSendLimit(phoneNumber);
      expect(normalResult.allowed).toBe(true);
      expect(normalResult.remainingAttempts).toBe(3);
    });
  });

  describe('Reset Functionality', () => {
    it('should properly reset verification attempts when requesting a new OTP', () => {
      const phoneNumber = '+15551234567';
      
      // First make some verify attempts
      rateLimiter.recordVerifyAttempt(phoneNumber);
      rateLimiter.recordVerifyAttempt(phoneNumber);
      
      // Check verify attempts count
      let verifyResult = rateLimiter.checkVerifyLimit(phoneNumber);
      expect(verifyResult.remainingAttempts).toBe(3); // Assuming MAX_VERIFY_ATTEMPTS is 5
      
      // Now request a new OTP
      rateLimiter.recordSendAttempt(phoneNumber);
      
      // Verify attempts should be reset
      verifyResult = rateLimiter.checkVerifyLimit(phoneNumber);
      expect(verifyResult.remainingAttempts).toBe(5); // Reset to max
      
      // But send attempts should be incremented
      const sendResult = rateLimiter.checkSendLimit(phoneNumber);
      expect(sendResult.remainingAttempts).toBe(2); // Decremented from 3
    });
  });

  describe('Time-based Tests', () => {
    it('should unblock after the block duration expires', () => {
      const phoneNumber = '+15551234567';
      
      // Record max attempts to trigger a block
      for (let i = 0; i < 3; i++) { // Assuming MAX_SEND_ATTEMPTS is 3
        rateLimiter.recordSendAttempt(phoneNumber);
      }
      
      // Verify the entry is blocked
      const blockedCheck = rateLimiter.checkSendLimit(phoneNumber);
      expect(blockedCheck.allowed).toBe(false);
      expect(blockedCheck.blockedUntil).toBeGreaterThan(Date.now());
      
      // Fast forward past the block duration
      vi.advanceTimersByTime(60 * 60 * 1000 + 1000); // 1 hour + 1 second
      
      // Check again - should be unblocked
      const unblockedCheck = rateLimiter.checkSendLimit(phoneNumber);
      expect(unblockedCheck.allowed).toBe(true);
    });
    
    it('should enforce cooldown between sends without resetting attempts', () => {
      const phoneNumber = '+15551234567';
      
      // Record first attempt
      rateLimiter.recordSendAttempt(phoneNumber);
      
      // Check immediate retry - should be in cooldown
      let cooldownCheck = rateLimiter.checkSendLimit(phoneNumber);
      expect(cooldownCheck.allowed).toBe(false);
      expect(cooldownCheck.remainingAttempts).toBe(2); // Attempts should still be counted
      
      // Fast forward just past the cooldown
      vi.advanceTimersByTime(60 * 1000 + 100); // 60 seconds + a bit
      
      // Should allow next attempt but remember previous attempts
      const afterCooldownCheck = rateLimiter.checkSendLimit(phoneNumber);
      expect(afterCooldownCheck.allowed).toBe(true);
      expect(afterCooldownCheck.remainingAttempts).toBe(2); // Still 2 remaining
    });
    
    it('should reset attempts after the window duration expires', () => {
      const phoneNumber = '+15551234567';
      
      // Record two attempts
      rateLimiter.recordSendAttempt(phoneNumber);
      rateLimiter.recordSendAttempt(phoneNumber);
      
      // Fast forward past the cooldown but still within window
      vi.advanceTimersByTime(2 * 60 * 1000); // 2 minutes
      
      // Should still remember previous attempts
      let check = rateLimiter.checkSendLimit(phoneNumber);
      expect(check.remainingAttempts).toBe(1); // 1 attempt remaining
      
      // Fast forward past the entire window
      vi.advanceTimersByTime(60 * 60 * 1000); // 1 hour total (window duration)
      
      // Attempts should be reset
      check = rateLimiter.checkSendLimit(phoneNumber);
      expect(check.allowed).toBe(true);
      expect(check.remainingAttempts).toBe(3); // Reset to max
    });
  });
});
