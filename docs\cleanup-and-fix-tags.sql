-- Cleanup script to remove conflicting tag tables and recreate them correctly
-- This will match the working local development schema

-- Drop all tag-related tables (in correct order due to foreign keys)
DROP TABLE IF EXISTS tag_report_type_associations CASCADE;
DROP TABLE IF EXISTS tags CASCADE;
DROP TABLE IF EXISTS predefined_tags CASCADE;
DROP TABLE IF EXISTS tag_metadata CASCADE;
DROP TABLE IF EXISTS tag_categories CASCADE;

-- Recreate tag_categories table to match local schema
CREATE TABLE tag_categories (
    id TEXT NOT NULL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7),
    "order" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL
);

-- Create indexes for tag_categories
CREATE INDEX "tag_categories_name_idx" ON tag_categories(name);
CREATE INDEX "tag_categories_order_idx" ON tag_categories("order");

-- Recreate tags table to match local schema exactly (with camelCase columns)
CREATE TABLE tags (
    id TEXT NOT NULL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    "displayName" JSONB NOT NULL,
    description JSONB,
    category_id TEXT,
    color VARCHAR(7),
    icon VARCHAR(50),
    weight INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_system BOOLEAN NOT NULL DEFAULT false,
    usage_count INTEGER NOT NULL DEFAULT 0,
    last_used_at TIMESTAMP(3),
    ai_relevance DOUBLE PRECISION DEFAULT 0.0,
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) NOT NULL
);

-- Create indexes for tags
CREATE UNIQUE INDEX "tags_name_key" ON tags(name);
CREATE INDEX "tags_name_idx" ON tags(name);
CREATE INDEX "tags_category_id_idx" ON tags(category_id);
CREATE INDEX "tags_is_active_idx" ON tags(is_active);
CREATE INDEX "tags_usage_count_idx" ON tags(usage_count);
CREATE INDEX "tags_ai_relevance_idx" ON tags(ai_relevance);

-- Add foreign key constraint
ALTER TABLE tags ADD CONSTRAINT "tags_category_id_fkey" 
    FOREIGN KEY (category_id) REFERENCES tag_categories(id) 
    ON UPDATE CASCADE ON DELETE SET NULL;

-- Create tag_report_type_associations table
CREATE TABLE tag_report_type_associations (
    id TEXT NOT NULL PRIMARY KEY,
    tag_id TEXT NOT NULL,
    report_type VARCHAR(50) NOT NULL,
    weight INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for tag_report_type_associations
CREATE UNIQUE INDEX "tag_report_type_associations_tag_id_report_type_key" 
    ON tag_report_type_associations(tag_id, report_type);
CREATE INDEX "tag_report_type_associations_report_type_idx" 
    ON tag_report_type_associations(report_type);
CREATE INDEX "tag_report_type_associations_weight_idx" 
    ON tag_report_type_associations(weight);

-- Add foreign key constraint
ALTER TABLE tag_report_type_associations ADD CONSTRAINT "tag_report_type_associations_tag_id_fkey"
    FOREIGN KEY (tag_id) REFERENCES tags(id) 
    ON UPDATE CASCADE ON DELETE CASCADE;

-- Insert predefined categories (using TEXT IDs like Prisma generates)
INSERT INTO tag_categories (id, name, description, color, "order") VALUES
('cat_severity_001', 'severity', 'Tags related to issue severity', '#ef4444', 1),
('cat_type_002', 'type', 'Tags related to issue classification', '#3b82f6', 2),
('cat_area_003', 'area', 'Tags related to application areas', '#8b5cf6', 3),
('cat_priority_004', 'priority', 'Tags related to priority levels', '#f59e0b', 4),
('cat_status_005', 'status', 'Tags related to resolution status', '#10b981', 5);

-- Insert predefined tags with proper structure (using displayName with camelCase)
INSERT INTO tags (id, name, "displayName", category_id, is_system, weight, is_active, created_at, updated_at) VALUES
-- Severity tags
('tag_urgent_001', 'urgent', '{"en": "Urgent", "fa": "فوری"}', 'cat_severity_001', true, 10, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_critical_002', 'critical', '{"en": "Critical", "fa": "بحرانی"}', 'cat_severity_001', true, 9, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_high_003', 'high', '{"en": "High Priority", "fa": "اولویت بالا"}', 'cat_severity_001', true, 8, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Type tags
('tag_bug_fix_004', 'bug-fix', '{"en": "Bug Fix", "fa": "رفع اشکال"}', 'cat_type_002', true, 7, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_enhancement_005', 'enhancement', '{"en": "Enhancement", "fa": "بهبود"}', 'cat_type_002', true, 6, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_new_feature_006', 'new-feature', '{"en": "New Feature", "fa": "ویژگی جدید"}', 'cat_type_002', true, 6, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_performance_007', 'performance', '{"en": "Performance Issue", "fa": "مشکل عملکرد"}', 'cat_type_002', true, 7, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Area tags
('tag_ui_008', 'user-interface', '{"en": "User Interface", "fa": "رابط کاربری"}', 'cat_area_003', true, 5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_ux_009', 'user-experience', '{"en": "User Experience", "fa": "تجربه کاربری"}', 'cat_area_003', true, 5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_backend_010', 'backend', '{"en": "Backend", "fa": "بک‌اند"}', 'cat_area_003', true, 5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_frontend_011', 'frontend', '{"en": "Frontend", "fa": "فرانت‌اند"}', 'cat_area_003', true, 5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_database_012', 'database', '{"en": "Database", "fa": "پایگاه داده"}', 'cat_area_003', true, 5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_security_013', 'security', '{"en": "Security", "fa": "امنیت"}', 'cat_area_003', true, 8, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Priority tags
('tag_high_priority_014', 'high-priority', '{"en": "High Priority", "fa": "اولویت بالا"}', 'cat_priority_004', true, 8, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_medium_priority_015', 'medium-priority', '{"en": "Medium Priority", "fa": "اولویت متوسط"}', 'cat_priority_004', true, 5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_low_priority_016', 'low-priority', '{"en": "Low Priority", "fa": "اولویت پایین"}', 'cat_priority_004', true, 3, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Status tags
('tag_needs_review_017', 'needs-review', '{"en": "Needs Review", "fa": "نیاز به بررسی"}', 'cat_status_005', true, 6, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_in_progress_018', 'in-progress', '{"en": "In Progress", "fa": "در حال انجام"}', 'cat_status_005', true, 5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_ready_test_019', 'ready-to-test', '{"en": "Ready to Test", "fa": "آماده تست"}', 'cat_status_005', true, 4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert tag-report type associations
INSERT INTO tag_report_type_associations (id, tag_id, report_type, weight, created_at) VALUES
-- Bug-related associations
('assoc_001', 'tag_bug_fix_004', 'BUG', 10, CURRENT_TIMESTAMP),
('assoc_002', 'tag_critical_002', 'BUG', 9, CURRENT_TIMESTAMP),
('assoc_003', 'tag_urgent_001', 'BUG', 8, CURRENT_TIMESTAMP),

-- Performance associations
('assoc_004', 'tag_performance_007', 'PERFORMANCE', 10, CURRENT_TIMESTAMP),
('assoc_005', 'tag_backend_010', 'PERFORMANCE', 7, CURRENT_TIMESTAMP),

-- UI/UX associations
('assoc_006', 'tag_ui_008', 'UI_UX', 10, CURRENT_TIMESTAMP),
('assoc_007', 'tag_ux_009', 'UI_UX', 10, CURRENT_TIMESTAMP),
('assoc_008', 'tag_frontend_011', 'UI_UX', 8, CURRENT_TIMESTAMP),

-- Feature request associations
('assoc_009', 'tag_new_feature_006', 'FEATURE_REQUEST', 10, CURRENT_TIMESTAMP),
('assoc_010', 'tag_enhancement_005', 'FEATURE_REQUEST', 8, CURRENT_TIMESTAMP),
('assoc_011', 'tag_enhancement_005', 'IMPROVEMENT', 9, CURRENT_TIMESTAMP);
