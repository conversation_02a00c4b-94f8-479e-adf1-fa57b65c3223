# Debug Report System Migration Guide

## Overview

This guide covers the migration from file-based debug report storage to PostgreSQL database storage, implementing Phase 1 of the debug report system improvements.

## What's New

### ✅ **Database Schema**
- **DebugReport** - Main reports table with status tracking
- **DebugReportTag** - Flexible tagging system
- **DebugReportStatusHistory** - Complete audit trail
- **DebugReportComment** - Internal developer communication

### ✅ **Status Management**
- `NOT_REVIEWED` (default) - New reports awaiting triage
- `IN_PROGRESS` - Reports being actively worked on
- `COMPLETED` - Reports that have been resolved
- `ARCHIVED` - Old reports kept for reference
- `DUPLICATE` - Reports that duplicate existing issues
- `WONT_FIX` - Reports that won't be addressed

### ✅ **New API Endpoints**
- `PUT /api/debug/admin/reports/:id/status` - Update report status
- `PUT /api/debug/admin/reports/:id/assign` - Assign report to user
- `POST /api/debug/admin/reports/:id/comments` - Add internal comments
- `GET /api/debug/admin/stats` - Enhanced dashboard statistics

### ✅ **Dual-Write Capability**
- Reports are written to both database and file system during migration
- Automatic fallback to file system if database fails
- Comprehensive logging of dual-write status

## Migration Steps

### Step 1: Database Migration

```bash
# Generate Prisma client with new schema
npm run prisma:generate

# Create and apply database migration
npm run migrate:postgres  # or migrate:sqlite for development
```

### Step 2: Migrate Existing Reports

```bash
# Run the migration script to transfer existing file-based reports to database
npm run migrate:debug-reports
```

The migration script will:
- ✅ Create backup of existing log files
- ✅ Parse all reports from JSON log files
- ✅ Convert legacy format to new database format
- ✅ Skip reports that already exist in database
- ✅ Provide detailed migration summary
- ✅ Validate migration results

### Step 3: Verify Migration

1. **Check Migration Output**:
   ```
   📈 Migration Summary:
   ✅ Successfully migrated: X reports
   ⏭️ Skipped (already exists): Y reports
   ❌ Failed: Z reports
   📊 Total processed: N reports
   ```

2. **Verify Database Content**:
   ```bash
   npm run studio:postgres  # Open Prisma Studio to inspect data
   ```

3. **Test New Endpoints**:
   - Try updating report status via API
   - Test assignment functionality
   - Add comments to reports

### Step 4: Monitor Dual-Write

During the transition period, monitor logs for dual-write status:
```
📊 [DebugRoutes] Dual-write status - DB: ✅, File: ✅
```

## API Usage Examples

### Update Report Status
```bash
curl -X PUT http://localhost:3000/api/debug/admin/reports/DBG-123/status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"status": "in_progress", "comment": "Started investigating"}'
```

### Assign Report
```bash
curl -X PUT http://localhost:3000/api/debug/admin/reports/DBG-123/assign \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"assignedTo": "user-id-123", "comment": "Assigned to John"}'
```

### Add Comment
```bash
curl -X POST http://localhost:3000/api/debug/admin/reports/DBG-123/comments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"comment": "Found the root cause", "isInternal": true}'
```

### Get Enhanced Stats
```bash
curl -X GET http://localhost:3000/api/debug/admin/stats \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Database Schema Details

### DebugReport Table
```sql
- id (UUID, Primary Key)
- reportId (String, Unique) - External facing ID
- userId (String, Optional) - Report submitter
- type (Enum) - bug, feature-request, performance, etc.
- severity (Enum) - low, medium, high, critical
- status (Enum) - not_reviewed, in_progress, completed, etc.
- priority (Integer) - 1-5 scale
- title, description, stepsToReproduce, etc.
- assignedToId (String, Optional) - Assigned developer
- diagnosticData, logs, userActions (JSON)
- timestamps (clientTimestamp, serverReceivedAt, createdAt, updatedAt)
```

### Status History Table
```sql
- id (UUID, Primary Key)
- reportId (UUID, Foreign Key)
- oldStatus, newStatus (Enum)
- changedBy (String, Foreign Key to User)
- comment (Text, Optional)
- createdAt (Timestamp)
```

## Troubleshooting

### Migration Issues

**Problem**: Migration script fails with "Report not found"
**Solution**: Ensure the database schema is up to date and Prisma client is generated

**Problem**: Dual-write shows database failures
**Solution**: Check database connection and ensure migrations are applied

**Problem**: File system backup fails
**Solution**: Ensure write permissions to log directory

### API Issues

**Problem**: Status update returns 503 "Database service not available"
**Solution**: Ensure Prisma client is passed to debug routes in server setup

**Problem**: Authentication errors on new endpoints
**Solution**: Ensure valid JWT token is provided in Authorization header

## Rollback Plan

If issues occur during migration:

1. **Stop the application**
2. **Restore from backup**:
   ```bash
   # Restore log files from backup
   cp logs/client-reports.log.backup.TIMESTAMP logs/client-reports.log
   ```
3. **Revert database changes** (if needed):
   ```bash
   npm run reset:postgres
   ```
4. **Restart with file-only mode** by commenting out Prisma client in debug routes

## Performance Considerations

- **Database Indexes**: Optimized for common query patterns (status, type, severity, assignedTo, createdAt)
- **JSON Storage**: Diagnostic data, logs, and user actions stored as JSON for flexibility
- **Pagination**: All list endpoints support pagination to handle large datasets
- **Connection Pooling**: Prisma handles database connection pooling automatically

## Security Notes

- All new endpoints require authentication
- User ID is automatically captured from auth context
- Internal comments are marked as such and not exposed to end users
- Audit trail maintains complete history of status changes

## Next Steps

After successful migration:

1. **Monitor system performance** for 1-2 weeks
2. **Gather feedback** from developers using new status management
3. **Plan Phase 2** implementation (bulk operations, enhanced UI)
4. **Consider removing file-based fallback** once database is proven stable

## Support

For issues during migration:
1. Check application logs for detailed error messages
2. Verify database connectivity and schema state
3. Ensure all environment variables are properly configured
4. Contact development team if persistent issues occur
