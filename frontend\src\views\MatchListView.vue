<template>
  <div class="matches-view-enhanced" :class="{ 'rtl': isRTL, 'ltr': !isRTL }" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">{{ $t('matches.title') }}</h1>
        <p class="hero-subtitle">{{ $t('matches.heroSubtitle') }}</p>
        
        <!-- Quick Stats -->
        <div class="quick-stats">
          <div class="stat-card">
            <div class="stat-value">{{ matchesNeedingResponse.length }}</div>
            <div class="stat-label">{{ $t('matches.stats.needingResponse') }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ activeMatches.length }}</div>
            <div class="stat-label">{{ $t('matches.stats.active') }}</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ completedMatches.length }}</div>
            <div class="stat-label">{{ $t('matches.stats.completed') }}</div>
          </div>
        </div>

        <!-- Hero Actions -->
        <div class="hero-actions">
          <n-button 
            type="primary" 
            size="large"
            @click="router.push('/browse-offers')"
            class="primary-cta"
          >
            <template #icon>
              <n-icon><Search /></n-icon>
            </template>
            {{ $t('matches.findNewMatches') }}
          </n-button>
          
          <n-button
            secondary
            size="large"
            :loading="isRefreshing"
            @click="refreshMatches"
            class="refresh-cta"
          >
            <template #icon>
              <n-icon><Refresh /></n-icon>
            </template>
            {{ $t('matches.refreshMatches') }}
          </n-button>
        </div>
      </div>
    </section>

    <!-- Matches Section -->
    <section class="matches-section">
      <div class="section-header">
        <h2 class="section-title">{{ $t('matches.yourMatches') }}</h2>
        <p class="section-subtitle">{{ $t('matches.sectionSubtitle') }}</p>
      </div>

      <!-- Custom RTL-Compatible Tabs -->
      <div class="custom-tabs-container">
        <div class="custom-tabs" :class="{ 'rtl': isRTL }">
          <!-- Tab Headers -->
          <div class="tab-headers">
            <button 
              v-for="tab in tabsData" 
              :key="tab.name"
              :class="['tab-header', { 'active': activeTab === tab.name }]"
              @click="setActiveTab(tab.name)"
              type="button"
            >
              {{ tab.label }}
            </button>
            <!-- Active Tab Indicator -->
            <div 
              class="tab-indicator" 
              :style="getTabIndicatorStyle()"
            ></div>
          </div>
        </div>
      </div>

      <!-- Loading Skeleton -->
      <div v-if="isLoading && !matches.length" class="matches-skeleton animate-fade-up">
        <div v-for="i in 4" :key="i" class="skeleton-card">
          <div class="skeleton-header"></div>
          <div class="skeleton-content">
            <div class="skeleton-line"></div>
            <div class="skeleton-line short"></div>
            <div class="skeleton-line medium"></div>
          </div>
          <div class="skeleton-footer"></div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="enhanced-error-state animate-fade-up">
        <div class="error-icon">⚠️</div>
        <h3 class="error-title">{{ $t('matches.errorTitle') }}</h3>
        <p class="error-description">{{ error }}</p>
        <n-button
          type="primary"
          @click="() => loadMatches()"
          :loading="isLoading"
          class="retry-button"
        >
          {{ $t('common.retry') }}
        </n-button>
      </div>

      <!-- Enhanced Empty State -->
      <div v-else-if="!filteredMatches.length" class="enhanced-empty-state animate-fade-up">
        <div class="empty-icon">🔍</div>
        <h3 class="empty-title">{{ getEmptyStateTitle() }}</h3>
        <p class="empty-description">{{ getEmptyStateDescription() }}</p>
        <n-button 
          v-if="activeTab === TabNames.NEEDING_RESPONSE"
          type="primary" 
          size="large"
          @click="router.push('/browse-offers')"
          class="empty-cta"
        >
          {{ $t('matches.browseOffers') }}
        </n-button>
      </div>

      <!-- Enhanced Matches Grid -->
      <div v-else class="matches-grid animate-fade-up">
        <!-- Pull to Refresh Indicator -->
        <div v-if="isRefreshing" class="refresh-indicator">
          <n-spin size="small" />
          <span>{{ $t('matches.refreshing') }}</span>
        </div>

        <!-- Match Cards -->
        <div class="match-cards-container">
          <div 
            v-for="(match, index) in filteredMatches" 
            :key="match.id"
            class="match-card-wrapper"
            :style="{ animationDelay: `${index * 0.1}s` }"
          >
            <MatchNotificationCard
              :match="match"
              class="enhanced-match-card"
            />
          </div>
        </div>

        <!-- Load More Button -->
        <div v-if="hasMoreMatches" class="load-more-section">
          <n-button
            type="primary"
            size="large"
            block
            :loading="isLoadingMore"
            @click="loadMoreMatches"
            class="load-more-btn"
          >
            {{ $t('matches.loadMore') }}
          </n-button>
        </div>
      </div>

      <!-- Stats Summary -->
      <div v-if="!isLoading && filteredMatches.length > 0" class="stats-summary">
        <div class="stats-item">
          <span class="stats-number">{{ filteredMatches.length }}</span>
          <span class="stats-label">{{ $t('matches.stats.showing') }}</span>
        </div>
        <div class="stats-item">
          <span class="stats-number">{{ matches.length }}</span>
          <span class="stats-label">{{ $t('matches.stats.total') }}</span>
        </div>
      </div>
    </section>

    <!-- Floating Action Button (Mobile) -->
    <div class="fab-container">
      <n-button
        type="primary"
        circle
        size="large"
        class="refresh-fab"
        :class="{ 'fab-hidden': isRefreshing }"
        @click="refreshMatches"
      >
        <template #icon>
          <n-icon><Refresh /></n-icon>
        </template>
      </n-button>
    </div>

    <!-- Auto-refresh Timer -->
    <div v-if="autoRefreshEnabled" class="auto-refresh-timer">
      <span class="timer-text">
        {{ $t('matches.nextRefresh') }}: {{ formatAutoRefreshTime(autoRefreshCountdown) }}
      </span>
      <n-button
        text
        size="small"
        @click="toggleAutoRefresh"
        class="toggle-auto-refresh"
      >
        {{ $t('matches.disableAutoRefresh') }}
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { 
  NButton, 
  NIcon, 
  NSpin, 
  useMessage
} from 'naive-ui';
import { Refresh, Search } from '@vicons/ionicons5';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useTranslation } from '@/composables/useTranslation';
import { useMatchStore } from '@/stores/matchStore';
import { storeToRefs } from 'pinia';
import MatchNotificationCard from '@/components/MatchNotificationCard.vue';

// Tab names constant for type safety
const TabNames = {
  NEEDING_RESPONSE: 'needingResponse',
  ACTIVE: 'active',
  COMPLETED: 'completed',
  ALL: 'all'
} as const;

type TabName = typeof TabNames[keyof typeof TabNames];

const { t } = useI18n();
const { isRTL } = useTranslation();
const router = useRouter();
const message = useMessage();
const matchStore = useMatchStore();

// Store refs
const {
  matches,
  isLoading,
  error,
  matchesNeedingResponse,
  activeMatches,
  completedMatches
} = storeToRefs(matchStore);

// Local state
const activeTab = ref<TabName>(TabNames.NEEDING_RESPONSE);
const isRefreshing = ref(false);
const isLoadingMore = ref(false);
const hasMoreMatches = ref(false);
const autoRefreshEnabled = ref(true);
const autoRefreshCountdown = ref(30);
const countdownTimer = ref<number | null>(null);

// Computed
const filteredMatches = computed(() => {
  switch (activeTab.value) {
    case TabNames.NEEDING_RESPONSE:
      return matchesNeedingResponse.value;
    case TabNames.ACTIVE:
      return activeMatches.value;
    case TabNames.COMPLETED:
      return completedMatches.value;
    case TabNames.ALL:
    default:
      return matches.value;
  }
});

const needingResponseTabLabel = computed(() => {
  const count = matchesNeedingResponse.value.length;
  const baseLabel = t('matches.tabs.needingResponse');
  return count > 0 ? `${baseLabel} (${count})` : baseLabel;
});

// Custom tabs data for RTL support
const tabsData = computed(() => [
  {
    name: TabNames.NEEDING_RESPONSE,
    label: needingResponseTabLabel.value
  },
  {
    name: TabNames.ACTIVE,
    label: t('matches.tabs.active')
  },
  {
    name: TabNames.COMPLETED,
    label: t('matches.tabs.completed')
  },
  {
    name: TabNames.ALL,
    label: t('matches.tabs.all')
  }
]);

// Get tab indicator style for RTL support
function getTabIndicatorStyle() {
  const currentIndex = tabsData.value.findIndex(tab => tab.name === activeTab.value);
  const tabWidth = 100 / tabsData.value.length;
  
  if (isRTL.value) {
    // For RTL, the indicator should follow the visual position
    // Since tabs are laid out RTL, we use the same index but position from right
    const rightPosition = currentIndex * tabWidth;
    return {
      width: `${tabWidth}%`,
      right: `${rightPosition}%`,
      left: 'auto'
    };
  } else {
    // For LTR, position from left
    const leftPosition = currentIndex * tabWidth;
    return {
      width: `${tabWidth}%`,
      left: `${leftPosition}%`,
      right: 'auto'
    };
  }
}

// Set active tab and handle changes
function setActiveTab(tabName: string) {
  activeTab.value = tabName as TabName;
  handleTabChange(tabName);
}

// Methods
async function loadMatches() {
  try {
    // Always load all matches to maintain consistent hero stats
    // Filtering is now done client-side via computed properties
    await matchStore.loadMatches(undefined);
  } catch (error) {
    console.error('Failed to load matches:', error);
    message.error(t('matches.loadError'));
  }
}

async function refreshMatches() {
  if (isRefreshing.value) return;
  
  isRefreshing.value = true;
  
  try {
    // Always load all matches to maintain consistent hero stats
    await loadMatches();
    message.success(t('matches.refreshSuccess'));
    resetAutoRefresh();
  } catch (error) {
    console.error('Failed to refresh matches:', error);
    message.error(t('matches.refreshError'));
  } finally {
    isRefreshing.value = false;
  }
}

async function loadMoreMatches() {
  if (isLoadingMore.value) return;
  
  isLoadingMore.value = true;
  
  try {
    // TODO: Implement pagination in the backend and store
    message.info(t('matches.noMoreMatches'));
    hasMoreMatches.value = false;
  } catch (error) {
    console.error('Failed to load more matches:', error);
    message.error(t('matches.loadMoreError'));
  } finally {
    isLoadingMore.value = false;
  }
}

function handleTabChange(tabName: string) {
  activeTab.value = tabName as TabName;
  
  // Reset pagination
  hasMoreMatches.value = matches.value.length >= 50;
  
  // No need to reload data - filtering is done client-side via computed properties
  // This maintains consistent hero stats while showing filtered tab content
}

function getEmptyStateTitle(): string {
  switch (activeTab.value) {
    case TabNames.NEEDING_RESPONSE:
      return t('matches.empty.needingResponseTitle');
    case TabNames.ACTIVE:
      return t('matches.empty.activeTitle');
    case TabNames.COMPLETED:
      return t('matches.empty.completedTitle');
    case TabNames.ALL:
    default:
      return t('matches.empty.allTitle');
  }
}

function getEmptyStateDescription(): string {
  switch (activeTab.value) {
    case TabNames.NEEDING_RESPONSE:
      return t('matches.empty.needingResponse');
    case TabNames.ACTIVE:
      return t('matches.empty.active');
    case TabNames.COMPLETED:
      return t('matches.empty.completed');
    case TabNames.ALL:
    default:
      return t('matches.empty.all');
  }
}

function toggleAutoRefresh() {
  autoRefreshEnabled.value = !autoRefreshEnabled.value;
  
  if (autoRefreshEnabled.value) {
    startAutoRefresh();
    message.success(t('matches.autoRefreshEnabled'));
  } else {
    stopAutoRefresh();
    message.info(t('matches.autoRefreshDisabled'));
  }
}

function startAutoRefresh() {
  if (!autoRefreshEnabled.value) return;
  
  stopAutoRefresh(); // Clear existing timers
  
  // Start countdown timer - handles both countdown display and refresh trigger
  autoRefreshCountdown.value = 30;
  countdownTimer.value = window.setInterval(() => {
    autoRefreshCountdown.value--;
    if (autoRefreshCountdown.value <= 0) {
      if (autoRefreshEnabled.value && !isRefreshing.value) {
        refreshMatches();
      }
      // Reset countdown for next cycle
      autoRefreshCountdown.value = 30;
    }
  }, 1000);
}

function stopAutoRefresh() {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
  autoRefreshCountdown.value = 0;
}

function resetAutoRefresh() {
  if (autoRefreshEnabled.value) {
    startAutoRefresh();
  }
}

function formatAutoRefreshTime(seconds: number): string {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// Pull-to-refresh functionality (mobile)
function setupPullToRefresh() {
  let startY = 0;
  let pulling = false;
  const threshold = 100; // pixels
  
  function handleTouchStart(e: TouchEvent) {
    startY = e.touches[0].clientY;
    pulling = false;
  }
  
  function handleTouchMove(e: TouchEvent) {
    const currentY = e.touches[0].clientY;
    const diffY = currentY - startY;
    
    if (diffY > threshold && window.scrollY === 0 && !pulling) {
      pulling = true;
      // Add visual feedback here if needed
    }
  }
  
  function handleTouchEnd() {
    if (pulling && !isRefreshing.value) {
      refreshMatches();
    }
    pulling = false;
  }
  
  // Add touch listeners for mobile devices
  if ('ontouchstart' in window) {
    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });
    
    // Cleanup function
    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }
  
  return () => {}; // No-op cleanup for non-touch devices
}

// Store cleanup reference for pull-to-refresh
let cleanupPullToRefresh: (() => void) | null = null;

// Lifecycle
onMounted(async () => {
  // Attach real-time socket listeners for match events (found, accepted, declined, expired, converted)
  matchStore.attachSocketListeners();
  
  // Load all initial data to maintain consistent stats across all tabs
  await loadMatches();
  
  // Set up auto-refresh
  startAutoRefresh();
  
  // Set up pull-to-refresh
  cleanupPullToRefresh = setupPullToRefresh();
});

onUnmounted(() => {
  stopAutoRefresh();
  cleanupPullToRefresh?.();
  matchStore.detachSocketListeners();
});
</script>

<style scoped>
/* Mobile-first Enhanced Matches View */
.matches-view-enhanced {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* Light theme */
[data-theme="light"] .matches-view-enhanced {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
}

/* Dark theme */
[data-theme="dark"] .matches-view-enhanced {
  background: linear-gradient(135deg, #1a1b2e 0%, #16213e 50%, #0f1419 100%);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  color: white;
  padding: 2rem 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1" fill-rule="nonzero"><circle cx="30" cy="30" r="4"/></g></g></svg>');
  opacity: 0.1;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: 1rem;
  margin: 0 0 1.5rem 0;
  opacity: 0.9;
  line-height: 1.6;
}

/* Quick Stats */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin: 1.5rem 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.8;
}

.hero-actions {
  margin-top: 1.5rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primary-cta,
.refresh-cta {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(0);
  transition: all 0.3s ease;
}

.primary-cta:hover,
.refresh-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Matches Section */
.matches-section {
  padding: 0 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
}

[data-theme="dark"] .section-title {
  color: rgba(255, 255, 255, 0.9);
}

.section-subtitle {
  font-size: 0.875rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

[data-theme="dark"] .section-subtitle {
  color: rgba(255, 255, 255, 0.7);
}

/* Custom Tabs Styles - Mobile-First with RTL Support */
.custom-tabs-container {
  padding: 0 1rem;
  margin-bottom: 2rem;
}

.custom-tabs {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
}

[data-theme="dark"] .custom-tabs {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Tab Headers Container */
.tab-headers {
  position: relative;
  display: flex;
  background: transparent;
}

/* Individual Tab Header */
.tab-header {
  flex: 1;
  padding: 1rem 0.75rem;
  border: none;
  background: transparent;
  color: var(--n-text-color-3);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  min-height: 44px; /* Touch-friendly minimum */
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.tab-header:hover {
  color: var(--n-text-color-1);
  background: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .tab-header:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tab-header.active {
  color: var(--n-primary-color);
  font-weight: 700;
  background: rgba(24, 160, 88, 0.1);
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(24, 160, 88, 0.2);
  border: 1px solid rgba(24, 160, 88, 0.3);
}

[data-theme="dark"] .tab-header.active {
  background: rgba(24, 160, 88, 0.15);
  box-shadow: 0 2px 8px rgba(24, 160, 88, 0.3);
  border: 1px solid rgba(24, 160, 88, 0.4);
}

/* Active Tab Indicator */
.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--n-primary-color), rgba(24, 160, 88, 0.8));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  border-radius: 4px 4px 0 0;
  box-shadow: 0 -2px 8px rgba(24, 160, 88, 0.4);
}

/* RTL Support for Custom Tabs */
.custom-tabs.rtl .tab-headers {
  direction: rtl;
}

.custom-tabs.rtl .tab-header {
  text-align: center;
}

/* RTL indicator positioning fix */
.custom-tabs.rtl .tab-indicator {
  transform-origin: right center;
}

/* Loading Skeleton */
.matches-skeleton {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin: 0;
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.2s forwards;
}

.skeleton-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] .skeleton-card {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.skeleton-header {
  height: 24px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-line.short { width: 60%; }
.skeleton-line.medium { width: 80%; }

.skeleton-footer {
  height: 32px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 16px;
  width: 120px;
}

/* Enhanced Error State */
.enhanced-error-state {
  text-align: center;
  padding: 3rem 1rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.4s forwards;
}

[data-theme="dark"] .enhanced-error-state {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #e53e3e;
}

[data-theme="dark"] .error-title {
  color: #fc8181;
}

.error-description {
  color: #666;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

[data-theme="dark"] .error-description {
  color: rgba(255, 255, 255, 0.7);
}

.retry-button {
  margin-top: 1rem;
}

/* Enhanced Empty State */
.enhanced-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.4s forwards;
}

/* RTL Support for Empty State - Set direction while maintaining center alignment */
.rtl .enhanced-empty-state {
  direction: rtl;
}

.ltr .enhanced-empty-state {
  direction: ltr;
}

[data-theme="dark"] .enhanced-empty-state {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
}

[data-theme="dark"] .empty-title {
  color: rgba(255, 255, 255, 0.9);
}

.empty-description {
  color: #666;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

[data-theme="dark"] .empty-description {
  color: rgba(255, 255, 255, 0.7);
}

.empty-cta {
  margin-top: 1rem;
}

/* Enhanced Matches Grid */
.matches-grid {
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.3s forwards;
}

.refresh-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  font-size: 0.875rem;
  color: var(--text-color-secondary);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .refresh-indicator {
  background: rgba(26, 27, 46, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.match-cards-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.match-card-wrapper {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

.enhanced-match-card {
  transition: all 0.3s ease;
}

.enhanced-match-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Load More Section */
.load-more-section {
  margin-top: 2rem;
  text-align: center;
}

.load-more-btn {
  min-width: 200px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Stats Summary */
.stats-summary {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] .stats-summary {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2080f0;
}

.stats-label {
  font-size: 0.875rem;
  color: #666;
}

[data-theme="dark"] .stats-number {
  color: #63a4ff;
}

[data-theme="dark"] .stats-label {
  color: rgba(255, 255, 255, 0.7);
}

/* Floating Action Button */
.fab-container {
  position: fixed;
  bottom: 2rem;
  right: 1rem;
  z-index: 20;
}

.refresh-fab {
  width: 56px;
  height: 56px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.fab-hidden {
  transform: scale(0);
  opacity: 0;
}

/* Auto-refresh Timer */
.auto-refresh-timer {
  position: fixed;
  bottom: 5rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.75rem;
  z-index: 15;
  backdrop-filter: blur(10px);
}

[data-theme="dark"] .auto-refresh-timer {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.timer-text {
  color: var(--text-color-secondary);
  white-space: nowrap;
}

.toggle-auto-refresh {
  font-size: 0.75rem;
  padding: 0;
  height: auto;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Mobile RTL Enhancements */
@media (max-width: 767px) {
  .rtl .enhanced-empty-state {
    text-align: center;
    padding: 2rem 1rem;
  }
  
  .rtl .tab-header {
    font-size: 0.9rem;
  }
  
  .rtl .stat-label {
    text-align: right;
  }
  
  .custom-tabs.rtl .tab-indicator {
    border-radius: 0 0 4px 4px;
  }
}

/* Tablet enhancements */
@media (min-width: 768px) {
  .matches-view-enhanced {
    padding-bottom: 2rem;
  }
  
  .hero-section {
    padding: 3rem 2rem;
  }
  
  .hero-title {
    font-size: 3rem;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
  
  .quick-stats {
    gap: 1.5rem;
    max-width: 700px;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-value {
    font-size: 2rem;
  }
  
  .stat-label {
    font-size: 0.875rem;
  }
  
  .matches-section {
    padding: 0 2rem 2rem;
  }
  
  /* Custom tabs tablet styles */
  .custom-tabs-container {
    padding: 0 2rem;
  }
  
  .tab-header {
    padding: 1.25rem 1rem;
    font-size: 1rem;
  }
  
  .tab-indicator {
    height: 4px;
  }
  
  .match-cards-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
  
  .matches-skeleton {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
  
  .fab-container {
    display: none; /* Hide FAB on tablet+ */
  }
  
  .auto-refresh-timer {
    position: relative;
    bottom: auto;
    left: auto;
    transform: none;
    margin: 2rem auto 0;
    justify-content: center;
    max-width: 300px;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  .hero-section {
    padding: 4rem 2rem;
  }
  
  .hero-title {
    font-size: 3.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .quick-stats {
    max-width: 800px;
    gap: 2rem;
  }
  
  .matches-section {
    padding: 0 3rem 3rem;
    max-width: 1400px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  /* Custom tabs desktop styles */
  .custom-tabs-container {
    padding: 0 3rem;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .tab-header {
    padding: 1.5rem 1.25rem;
    font-size: 1.125rem;
  }
  
  .match-cards-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
  
  .matches-skeleton {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
  
  .stats-summary {
    margin: 2rem 0 1rem;
  }
}

/* Large desktop enhancements */
@media (min-width: 1440px) {
  .match-cards-container {
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
  }
  
  .matches-skeleton {
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
  }
  
  .matches-section {
    max-width: 1600px;
    padding: 0 4rem 4rem;
  }
  
  .hero-section {
    padding: 5rem 4rem;
  }
  
  .hero-title {
    font-size: 4rem;
  }
  
  .quick-stats {
    max-width: 900px;
    gap: 2.5rem;
  }
  
  .stat-card {
    padding: 2rem;
  }
  
  .stat-value {
    font-size: 2.5rem;
  }
}

/* RTL Support */
.rtl .hero-content {
  direction: rtl;
  text-align: center; /* Keep center alignment for visual consistency */
}

.rtl .hero-title {
  text-align: center; /* Keep center alignment for visual consistency */
}

.rtl .hero-subtitle {
  text-align: center; /* Keep center alignment for visual consistency */
}

.rtl .hero-actions {
  flex-direction: row-reverse;
}

.rtl .section-header {
  text-align: center; /* Keep center alignment for visual consistency */
}

.rtl .stats-summary {
  flex-direction: row-reverse;
}

.rtl .refresh-indicator {
  flex-direction: row-reverse;
}

.rtl .auto-refresh-timer {
  flex-direction: row-reverse;
}

.rtl .fab-container {
  left: 1rem;
  right: auto;
}

/* RTL text alignment for Persian content - Keep centered for empty states */
.rtl .empty-title,
.rtl .error-title {
  text-align: center;
}

.rtl .empty-description,
.rtl .error-description {
  text-align: center;
}

/* RTL spacing adjustments */
.rtl .quick-stats {
  direction: rtl;
}

.rtl .stat-card {
  text-align: center; /* Keep stats centered for better readability */
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .matches-skeleton,
  .matches-grid,
  .match-card-wrapper,
  .enhanced-empty-state,
  .enhanced-error-state {
    animation: none !important;
  }
  
  .enhanced-match-card:hover {
    transform: none;
  }
  
  .primary-cta:hover,
  .refresh-cta:hover,
  .load-more-btn:hover {
    transform: none;
  }
}
</style>
