import { describe, it, expect, beforeEach, afterEach, vi, type MockedFunction } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import { ref, readonly } from 'vue'
import DebugReportButtonEnhanced from '../../components/DebugReportButtonEnhanced.vue'
import { createI18n } from 'vue-i18n'
import type { ComponentPublicInstance } from 'vue'
import type { ReportDetails, ClientReportResponse } from '../../types/logging'

// Mock @vicons/tabler with all required icons
vi.mock('@vicons/tabler', () => ({
  Bug: { name: 'Bug', render: () => 'bug-icon' },
  InfoCircle: { name: 'InfoCircle', render: () => 'info-icon' },
  Bulb: { name: 'Bulb', render: () => 'lightbulb-icon' },
  Bolt: { name: 'Bolt', render: () => 'zap-icon' },
  Palette: { name: 'Palette', render: () => 'palette-icon' },
  TrendingUp: { name: 'TrendingUp', render: () => 'trending-up-icon' },
  QuestionMark: { name: 'QuestionMark', render: () => 'help-circle-icon' },
  Check: { name: 'Check', render: () => 'check-icon' }
}))

// Import after mocking
import { useClientLogger } from '../../composables/useClientLogger'
const mockUseClientLogger = vi.mocked(useClientLogger)

// Create i18n instance with test translations
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en: {        debug: {
          reportIssue: 'Report Issue',
          reportType: 'Report Type',
          title: 'Title',
          titlePlaceholder: 'Brief summary of the issue',
          description: 'Description',
          descriptionPlaceholder: 'Detailed description',
          stepsToReproduce: 'Steps to Reproduce',
          stepsPlaceholder: 'Step-by-step instructions',
          expectedBehavior: 'Expected Behavior',
          expectedPlaceholder: 'What should happen',
          actualBehavior: 'Actual Behavior',
          actualPlaceholder: 'What actually happens',
          severityText: 'Severity',
          severity: {
            low: 'Low',
            medium: 'Medium',
            high: 'High',
            critical: 'Critical'
          },
          selectSeverity: 'Select severity',
          additionalNotes: 'Additional Notes',
          additionalNotesPlaceholder: 'Any other relevant information',
          sendReport: 'Send Report',
          contextInfo: 'Context Information',
          currentPage: 'Current Page',
          logEntries: 'Log Entries',
          userActions: 'User Actions',
          viewport: 'Viewport',
          recentActions: 'Recent Actions',
          reportTypes: {
            bug: 'Bug Report',
            bugDescription: 'Report a bug or error',
            'feature-request': 'Feature Request',
            featureDescription: 'Request a new feature',
            performance: 'Performance Issue',
            performanceDescription: 'Report performance problems',
            'ui-ux': 'UI/UX Issue',
            uiuxDescription: 'Report user interface issues',
            improvement: 'Improvement Suggestion',
            improvementDescription: 'Suggest improvements',
            question: 'Question',
            questionDescription: 'Ask a question',
            other: 'Other',
            otherDescription: 'Other type of feedback'
          },
          tags: {
            addCustom: 'Add Custom Tags',
            clickHint: 'Click predefined tags to add them',
            clickToAdd: 'Click to add',
            clickToRemove: 'Click to remove',
            predefined: {
              urgent: 'urgent',
              'fix-needed': 'fix-needed',
              error: 'error',
              enhancement: 'enhancement',
              'new-feature': 'new-feature',
              idea: 'idea',
              slow: 'slow',
              optimization: 'optimization',
              speed: 'speed',
              design: 'design',
              'user-experience': 'user-experience',
              interface: 'interface',
              help: 'help',
              unclear: 'unclear',
              documentation: 'documentation',
              'better-way': 'better-way',
              suggestion: 'suggestion',
              miscellaneous: 'miscellaneous',
              general: 'general'
            }
          },
          tagsDescription: 'Add tags to categorize your report',
          formValidationError: 'Please fill in all required fields',
          reportSentSuccess: 'Report sent successfully! Report ID: {reportId}',
          reportSentError: 'Failed to send report. Please try again.'
        },
        common: {
          cancel: 'Cancel'
        }
    }
  }
})

describe('DebugReportButtonEnhanced', () => {
  let wrapper: VueWrapper<ComponentPublicInstance>
  let mockSendLogsToServer: MockedFunction<any>
  let mockLogUserAction: MockedFunction<any>
  beforeEach(() => {
    // Mock environment for development mode
    vi.stubGlobal('import.meta', {
      env: {
        DEV: true,
        VITE_ENABLE_DEBUG_REPORT: 'true'
      }
    })
    
    // Reset all mocks
    vi.clearAllMocks()

    // Mock the logger functions
    mockSendLogsToServer = vi.fn()
    mockLogUserAction = vi.fn()

    mockUseClientLogger.mockReturnValue({
      sendLogsToServer: mockSendLogsToServer,
      logUserAction: mockLogUserAction,
      logInfo: vi.fn(),
      logWarn: vi.fn(),
      logError: vi.fn(),
      logDebug: vi.fn(),
      logNavigation: vi.fn(),
      getLogs: vi.fn(() => []),
      clearLogs: vi.fn(),
      getLogCount: vi.fn(() => 0),
      getCorrelatedLogs: vi.fn(() => []),
      generateUserContext: vi.fn(),
      getUserActions: vi.fn(() => []),
      getRouteHistory: vi.fn(() => []),
      handleGlobalError: vi.fn(),
      handleUnhandledRejection: vi.fn(),      handleVueError: vi.fn(),      logs: readonly(ref([])),
      userActions: readonly(ref([])),
      routeHistory: readonly(ref([]))
    })

    wrapper = mount(DebugReportButtonEnhanced, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false
          }),
          i18n
        ]
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  // Basic rendering test
  it('should render component and show structure', async () => {
    const wrapper = mount(DebugReportButtonEnhanced, {
      global: {
        plugins: [i18n, createTestingPinia()],
        stubs: {
          // Use our mocked components
        }
      }
    })

    // Debug what's actually rendered
    console.log('Component HTML:', wrapper.html())
    console.log('Find all buttons:', wrapper.findAll('button'))
    console.log('Find all n-button:', wrapper.findAll('[data-testid="n-button"]'))
    console.log('Find all with n-button class:', wrapper.findAll('.n-button'))
    
    // Check if component mounted
    expect(wrapper.exists()).toBe(true)
  })

  describe('Form Data Collection', () => {
    it('should collect all form fields and send them to useClientLogger', async () => {
      // Mock successful response
      const mockResponse: ClientReportResponse = {
        success: true,
        message: 'Report submitted successfully',
        reportId: 'test-report-123'
      }
      mockSendLogsToServer.mockResolvedValue(mockResponse)

      // Open the modal
      const debugButton = wrapper.find('[data-testid="n-button"]')
      await debugButton.trigger('click')

      // Wait for modal to be visible
      await wrapper.vm.$nextTick()

      // Fill out ALL form fields
      const testData = {
        type: 'bug',
        title: 'Test Bug Title',
        description: 'This is a detailed description of the bug',
        stepsToReproduce: '1. First step\n2. Second step\n3. Third step',
        expectedBehavior: 'Expected behavior description',
        actualBehavior: 'Actual behavior description',
        severity: 'medium',
        additionalNotes: 'Additional notes for context',
        reportTags: [
          { tagId: 'urgent-id', origin: 'PREDEFINED' },
          { tagId: 'frontend-id', origin: 'PREDEFINED' }
        ]
      }

      // Access the component's data and update it
      const vm = wrapper.vm as any
      vm.reportForm = { ...testData }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')

      // Verify that sendLogsToServer was called with complete form data
      expect(mockSendLogsToServer).toHaveBeenCalledTimes(1)
      
      const callArgs = mockSendLogsToServer.mock.calls[0][0] as ReportDetails
      
      // Verify ALL fields are included
      expect(callArgs.type).toBe(testData.type)
      expect(callArgs.title).toBe(testData.title)
      expect(callArgs.description).toBe(testData.description)
      expect(callArgs.stepsToReproduce).toBe(testData.stepsToReproduce)
      expect(callArgs.expectedBehavior).toBe(testData.expectedBehavior)
      expect(callArgs.actualBehavior).toBe(testData.actualBehavior)
      expect(callArgs.severity).toBe(testData.severity)
      expect(callArgs.additionalNotes).toBe(testData.additionalNotes)
      expect(callArgs.reportTags).toEqual(testData.reportTags)
    })

    it('should handle partial form data and still send all available fields', async () => {
      // Mock successful response
      const mockResponse: ClientReportResponse = {
        success: true,
        message: 'Report submitted successfully',
        reportId: 'test-report-456'
      }
      mockSendLogsToServer.mockResolvedValue(mockResponse)

      // Open the modal
      const debugButton = wrapper.find('[data-testid="n-button"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill out only required fields
      const testData = {
        type: 'feature-request',
        title: 'Feature Request Title',
        description: 'Feature description',
        severity: 'low'
      }

      const vm = wrapper.vm as any
      vm.reportForm = { ...testData }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')

      // Verify that sendLogsToServer was called
      expect(mockSendLogsToServer).toHaveBeenCalledTimes(1)
      
      const callArgs = mockSendLogsToServer.mock.calls[0][0] as ReportDetails
      
      // Verify required fields are included
      expect(callArgs.type).toBe(testData.type)
      expect(callArgs.title).toBe(testData.title)
      expect(callArgs.description).toBe(testData.description)
      expect(callArgs.severity).toBe(testData.severity)
      
      // Verify optional fields are undefined or empty
      expect(callArgs.stepsToReproduce).toBeUndefined()
      expect(callArgs.expectedBehavior).toBeUndefined()
      expect(callArgs.actualBehavior).toBeUndefined()
      expect(callArgs.additionalNotes).toBeUndefined()
    })

    it('should include report tags when provided', async () => {
      // Mock successful response
      const mockResponse: ClientReportResponse = {
        success: true,
        message: 'Report submitted successfully',
        reportId: 'test-report-789'
      }
      mockSendLogsToServer.mockResolvedValue(mockResponse)

      // Open the modal
      const debugButton = wrapper.find('[data-testid="n-button"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill out form with tags
      const testData = {
        type: 'ui-ux',
        title: 'UI Issue',
        description: 'UI problem description',
        severity: 'high',
        reportTags: [
          { tagId: 'ui-id', origin: 'PREDEFINED' },
          { tagId: 'mobile-id', origin: 'PREDEFINED' },
          { tagId: 'critical-id', origin: 'PREDEFINED' }
        ]
      }

      const vm = wrapper.vm as any
      vm.reportForm = { ...testData }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')

      // Verify tags are included
      const callArgs = mockSendLogsToServer.mock.calls[0][0] as ReportDetails
      expect(callArgs.reportTags).toEqual(testData.reportTags)
    })
  })

  describe('Error Handling', () => {
    it('should handle failed submission and display error message', async () => {
      // Mock failed response
      const mockError = new Error('Network error')
      mockSendLogsToServer.mockRejectedValue(mockError)

      // Open the modal
      const debugButton = wrapper.find('[data-testid="n-button"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill out minimal form
      const vm = wrapper.vm as any
      vm.reportForm = {
        type: 'bug',
        title: 'Test',
        description: 'Test description',
        severity: 'medium'
      }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')

      // Wait for error handling
      await wrapper.vm.$nextTick()

      // Verify that submission was attempted
      expect(mockSendLogsToServer).toHaveBeenCalledTimes(1)
      
      // Check that isSubmitting is reset to false
      expect(vm.isSubmitting).toBe(false)
    })

    it('should handle empty required fields gracefully', async () => {
      // Open the modal
      const debugButton = wrapper.find('[data-testid="n-button"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Try to submit without filling required fields
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')

      // Form validation should prevent submission
      // (exact behavior depends on Naive UI form validation)
      const vm = wrapper.vm as any
      expect(vm.reportForm.title).toBe('')
      expect(vm.reportForm.description).toBe('')
    })
  })

  describe('User Actions Logging', () => {
    it('should log user actions when opening and closing modal', async () => {
      // Open the modal
      const debugButton = wrapper.find('[data-testid="n-button"]')
      await debugButton.trigger('click')

      // Verify action was logged
      expect(mockLogUserAction).toHaveBeenCalledWith(
        'opened_debug_report_modal',
        expect.objectContaining({
          timestamp: expect.any(String)
        })
      )

      // Close the modal
      const vm = wrapper.vm as any
      vm.showModal = false
      await wrapper.vm.$nextTick()

      // Additional user actions might be logged during form interaction
      expect(mockLogUserAction).toHaveBeenCalled()
    })

    it('should log successful report submission', async () => {
      // Mock successful response
      const mockResponse: ClientReportResponse = {
        success: true,
        message: 'Report submitted successfully',
        reportId: 'test-report-success'
      }
      mockSendLogsToServer.mockResolvedValue(mockResponse)

      // Open modal and fill form
      const debugButton = wrapper.find('[data-testid="n-button"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      const vm = wrapper.vm as any
      vm.reportForm = {
        type: 'bug',
        title: 'Success Test',
        description: 'Success test description',
        severity: 'medium'
      }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')

      // Verify successful submission was logged
      expect(mockLogUserAction).toHaveBeenCalledWith(
        expect.stringContaining('debug_report'),
        expect.any(Object)
      )
    })
  })

  describe('Component State Management', () => {
    it('should reset form data when modal is closed', async () => {
      // Open the modal
      const debugButton = wrapper.find('[data-testid="n-button"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill out form
      const vm = wrapper.vm as any
      vm.reportForm = {
        type: 'bug',
        title: 'Test Title',
        description: 'Test Description',
        severity: 'high'
      }
      await wrapper.vm.$nextTick()

      // Close modal
      vm.showModal = false
      await wrapper.vm.$nextTick()

      // Reopen modal
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Verify form was reset (or preserved based on intended behavior)
      // This test verifies the actual behavior of the component
      expect(vm.reportForm).toBeDefined()
    })

    it('should disable submit button while submitting', async () => {
      // Mock a delayed response
      mockSendLogsToServer.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          success: true,
          message: 'Success',
          reportId: 'delayed-test'
        }), 100))
      )

      // Open the modal
      const debugButton = wrapper.find('[data-testid="n-button"]')
      await debugButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Fill out form
      const vm = wrapper.vm as any
      vm.reportForm = {
        type: 'bug',
        title: 'Delayed Test',
        description: 'Test description',
        severity: 'medium'
      }
      await wrapper.vm.$nextTick()

      // Submit the form
      const submitButton = wrapper.find('[data-testid="submit-debug-report"]')
      await submitButton.trigger('click')

      // Check that isSubmitting is true
      expect(vm.isSubmitting).toBe(true)
    })
  })
})
