&lt;template&gt;
  &lt;div class="tag-selector"&gt;
    &lt;!-- Predefined Tags Section --&gt;
    &lt;div class="predefined-tags-section"&gt;
      &lt;h4 class="section-title"&gt;{{ $t('debugReport.tagSelector.predefinedTags') }}&lt;/h4&gt;
      
      &lt;div v-if="isLoadingTags" class="loading-state"&gt;
        &lt;n-spin size="small" /&gt;
        &lt;span&gt;{{ $t('debugReport.tagSelector.loadingTags') }}&lt;/span&gt;
      &lt;/div&gt;
      
      &lt;div v-else-if="predefinedTags.length === 0" class="empty-state"&gt;
        {{ $t('debugReport.tagSelector.noTagsAvailable') }}
      &lt;/div&gt;
      
      &lt;div v-else class="tags-grid"&gt;
        &lt;n-tag
          v-for="tag in predefinedTags"
          :key="tag.id"
          :type="isTagSelected(tag.id) ? 'primary' : 'default'"
          :bordered="!isTagSelected(tag.id)"
          checkable
          :checked="isTagSelected(tag.id)"
          size="small"
          class="predefined-tag"
          :class="{
            'tag-ai-suggested': isTagAiSuggested(tag.id),
            'tag-selected': isTagSelected(tag.id)
          }"
          @update:checked="(checked) =&gt; toggleTag(tag.id, checked)"
        &gt;
          &lt;template #icon v-if="tag.icon"&gt;
            &lt;n-icon :component="getTagIcon(tag.icon)" /&gt;
          &lt;/template&gt;
          {{ getTagDisplayName(tag) }}
          &lt;span v-if="isTagAiSuggested(tag.id)" class="ai-indicator"&gt;✨&lt;/span&gt;
        &lt;/n-tag&gt;
      &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- AI Suggested Tags Info --&gt;
    &lt;div v-if="aiSuggestedTagIds.length &gt; 0" class="ai-suggestions-info"&gt;
      &lt;n-alert type="info" :show-icon="false" size="small"&gt;
        {{ $t('debugReport.tagSelector.aiSuggestionsHint', { count: aiSuggestedTagIds.length }) }}
      &lt;/n-alert&gt;
    &lt;/div&gt;

    &lt;!-- Custom Tags Section --&gt;
    &lt;div class="custom-tags-section"&gt;
      &lt;h4 class="section-title"&gt;{{ $t('debugReport.tagSelector.customTags') }}&lt;/h4&gt;
      
      &lt;div class="custom-tags-input"&gt;
        &lt;n-input
          v-model:value="customTagInput"
          type="text"
          :placeholder="$t('debugReport.tagSelector.addCustomTag')"
          size="small"
          :maxlength="50"
          @keydown.enter="addCustomTag"
          clearable
        &gt;
          &lt;template #suffix&gt;
            &lt;n-button
              quaternary
              circle
              size="tiny"
              :disabled="!customTagInput.trim()"
              @click="addCustomTag"
            &gt;
              &lt;n-icon :component="Add" /&gt;
            &lt;/n-button&gt;
          &lt;/template&gt;
        &lt;/n-input&gt;
      &lt;/div&gt;

      &lt;!-- Display selected custom tags --&gt;
      &lt;div v-if="customTags.length &gt; 0" class="custom-tags-list"&gt;
        &lt;n-tag
          v-for="(tag, index) in customTags"
          :key="`custom-${index}`"
          type="warning"
          size="small"
          closable
          class="custom-tag"
          @close="removeCustomTag(index)"
        &gt;
          {{ tag }}
        &lt;/n-tag&gt;
      &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Summary --&gt;
    &lt;div v-if="totalSelectedTags &gt; 0" class="selection-summary"&gt;
      {{ $t('debugReport.tagSelector.selectedCount', { count: totalSelectedTags }) }}
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup lang="ts"&gt;
import { ref, computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { 
  NTag, 
  NInput, 
  NButton, 
  NIcon, 
  NSpin, 
  NAlert 
} from 'naive-ui';
import { Add } from '@vicons/ionicons5';
import { useTagStore } from '@/stores/tagStore';
import type { TagWithRelations } from '@/types/api';

// Props
interface Props {
  selectedTagIds?: string[];
  aiSuggestedTagIds?: string[];
  customTags?: string[];
  reportType?: string;
}

const props = withDefaults(defineProps&lt;Props&gt;(), {
  selectedTagIds: () =&gt; [],
  aiSuggestedTagIds: () =&gt; [],
  customTags: () =&gt; [],
  reportType: 'bug'
});

// Emits
interface Emits {
  (e: 'update:selectedTagIds', value: string[]): void;
  (e: 'update:customTags', value: string[]): void;
}

const emit = defineEmits&lt;Emits&gt;();

// Composables
const { t } = useI18n();
const tagStore = useTagStore();

// Local state
const customTagInput = ref('');
const selectedTagIds = ref&lt;string[]&gt;([...props.selectedTagIds]);
const customTags = ref&lt;string[]&gt;([...props.customTags]);

// Computed
const isLoadingTags = computed(() =&gt; tagStore.isLoadingTags);

const predefinedTags = computed(() =&gt; {
  // Filter tags by report type if available
  let filteredTags = tagStore.activeTags;
  
  if (props.reportType) {
    filteredTags = filteredTags.filter(tag =&gt; 
      tag.reportTypes?.includes(props.reportType) || 
      tag.reportTypes?.length === 0 // Include tags that apply to all report types
    );
  }
  
  return filteredTags.sort((a, b) =&gt; (a.order || 0) - (b.order || 0));
});

const totalSelectedTags = computed(() =&gt; 
  selectedTagIds.value.length + customTags.value.length
);

// Methods
const isTagSelected = (tagId: string): boolean =&gt; {
  return selectedTagIds.value.includes(tagId);
};

const isTagAiSuggested = (tagId: string): boolean =&gt; {
  return props.aiSuggestedTagIds.includes(tagId);
};

const toggleTag = (tagId: string, checked: boolean) =&gt; {
  if (checked) {
    if (!selectedTagIds.value.includes(tagId)) {
      selectedTagIds.value.push(tagId);
    }
  } else {
    const index = selectedTagIds.value.indexOf(tagId);
    if (index &gt; -1) {
      selectedTagIds.value.splice(index, 1);
    }
  }
  emit('update:selectedTagIds', [...selectedTagIds.value]);
};

const getTagDisplayName = (tag: TagWithRelations): string =&gt; {
  // Use display name if available, fallback to name
  return tag.displayName?.[t('locale')] || tag.displayName?.en || tag.name;
};

const getTagIcon = (iconName: string) =&gt; {
  // For now, return null. Can be expanded to map icon names to components
  return null;
};

const addCustomTag = () =&gt; {
  const tag = customTagInput.value.trim();
  if (!tag) return;
  
  // Check if tag already exists
  if (customTags.value.includes(tag)) {
    customTagInput.value = '';
    return;
  }
  
  // Check if it matches any predefined tag name
  const existingPredefinedTag = predefinedTags.value.find(t =&gt; 
    getTagDisplayName(t).toLowerCase() === tag.toLowerCase()
  );
  
  if (existingPredefinedTag) {
    // If it matches a predefined tag, select that instead
    toggleTag(existingPredefinedTag.id, true);
    customTagInput.value = '';
    return;
  }
  
  customTags.value.push(tag);
  customTagInput.value = '';
  emit('update:customTags', [...customTags.value]);
};

const removeCustomTag = (index: number) =&gt; {
  customTags.value.splice(index, 1);
  emit('update:customTags', [...customTags.value]);
};

// Watchers
watch(() =&gt; props.selectedTagIds, (newIds) =&gt; {
  selectedTagIds.value = [...newIds];
});

watch(() =&gt; props.customTags, (newTags) =&gt; {
  customTags.value = [...newTags];
});

// Lifecycle
onMounted(async () =&gt; {
  try {
    // Fetch predefined tags if not already loaded
    if (tagStore.activeTags.length === 0) {
      await tagStore.fetchTags({ reportType: props.reportType });
    }
  } catch (error) {
    console.error('Failed to load predefined tags:', error);
  }
});
&lt;/script&gt;

&lt;style scoped&gt;
.tag-selector {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color-1);
}

.predefined-tags-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.loading-state {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color-3);
  font-size: 12px;
}

.empty-state {
  color: var(--text-color-3);
  font-size: 12px;
  font-style: italic;
}

.tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.predefined-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.predefined-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-ai-suggested {
  border-color: var(--warning-color) !important;
  background-color: var(--warning-color-suppl) !important;
}

.tag-selected.tag-ai-suggested {
  background-color: var(--warning-color) !important;
  color: white !important;
}

.ai-indicator {
  margin-left: 4px;
  font-size: 10px;
}

.ai-suggestions-info {
  margin-top: -8px;
}

.custom-tags-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.custom-tags-input {
  max-width: 300px;
}

.custom-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.custom-tag {
  cursor: default;
}

.selection-summary {
  font-size: 12px;
  color: var(--success-color);
  font-weight: 500;
  text-align: center;
  padding: 4px 8px;
  background-color: var(--success-color-suppl);
  border-radius: 4px;
}

/* Dark theme adjustments */
[data-theme="dark"] .predefined-tag:hover {
  box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
}
&lt;/style&gt;
