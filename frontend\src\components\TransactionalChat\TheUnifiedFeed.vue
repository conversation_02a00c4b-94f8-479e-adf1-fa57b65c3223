<script setup lang="ts">
import { computed, nextTick, onMounted, onUpdated, ref, defineExpose } from 'vue'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'
import ChatMessage from './ChatMessage.vue'
import SystemLog from './SystemLog.vue'
import ActionCard from './ActionCard.vue'

const transactionalChatStore = useTransactionalChatStore()
const feedContainer = ref<HTMLElement | null>(null)
// Expose the scrollable container to parent
defineExpose({ feedContainer })
const firstActionCardRef = ref<HTMLElement | null>(null)

// Computed properties
const feedItems = computed(() => {
  const items = transactionalChatStore.feedItems
  console.log('🔍 TheUnifiedFeed - feedItems:', items);
  return items
})

// Auto-scroll to bottom when new items are added
const scrollToBottom = async () => {
  await nextTick()
  if (feedContainer.value) {
    feedContainer.value.scrollTop = feedContainer.value.scrollHeight
  }
}

// Component mapping for dynamic components
const componentMap = {
  chat: ChatMessage,
  systemLog: SystemLog,
  actionCard: ActionCard
} as const


onMounted(async () => {
  scrollToBottom()
  // Scroll first ActionCard into view if present
  await nextTick()
  if (firstActionCardRef.value) {
    firstActionCardRef.value.scrollIntoView({ block: 'start', behavior: 'smooth' })
  }
})

onUpdated(() => {
  scrollToBottom()
})
</script>

<template>
  <div 
    ref="feedContainer"
    class="unified-feed"
    data-testid="unified-feed"
  >
    <div class="feed-content">
      <!-- Empty State -->
      <div 
        v-if="feedItems.length === 0"
        class="empty-state"
        data-testid="empty-state"
      >
        <div class="empty-icon">💬</div>
        <p class="empty-text">{{ $t('transactionalChat.chat.placeholder') }}</p>
      </div>
      
      <!-- Feed Items -->
      <div 
        v-else
        class="feed-items"
        data-testid="feed-items"
      >
        <component
          v-for="(item, idx) in feedItems"
          :is="componentMap[item.type]"
          :key="item.id"
          :item="item"
          class="feed-item"
          :data-testid="`feed-item-${item.type}-${item.id}`"
          v-bind="item.type === 'actionCard' && idx === 0 ? { autoScroll: true } : {}"
        />
      </div>
      
      <!-- Bottom Spacer for better scrolling -->
      <div class="feed-spacer"></div>
    </div>
  </div>
</template>

<style scoped>
.unified-feed {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: var(--tc-bg-feed);
  position: relative;
  /* Custom scrollbar */
  scrollbar-width: thin;
  scrollbar-color: var(--tc-border-medium) transparent;
}

.unified-feed::-webkit-scrollbar {
  width: 6px;
}

.unified-feed::-webkit-scrollbar-track {
  background: transparent;
}

.unified-feed::-webkit-scrollbar-thumb {
  background-color: var(--tc-border-medium);
  border-radius: 3px;
}

.unified-feed::-webkit-scrollbar-thumb:hover {
  background-color: var(--tc-border-dark);
}

.feed-content {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  padding-bottom: 160px; /* Increased bottom padding to account for action bar and forms */
}

/* Empty State */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  color: var(--tc-text-muted);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}

/* Feed Items */
.feed-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.feed-item {
  animation: fadeInSlide 0.3s ease-out;
}

@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Bottom spacer for better UX */
.feed-spacer {
  height: 40px; /* Increased spacer height */
  flex-shrink: 0;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .feed-content {
    padding: 12px;
    padding-bottom: 180px; /* Extra bottom padding on mobile */
  }
  
  .feed-items {
    gap: 10px;
  }
  
  .empty-state {
    padding: 32px 16px;
  }
  
  .empty-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }
  
  .empty-text {
    font-size: 15px;
  }
  
  .feed-spacer {
    height: 60px; /* Larger spacer on mobile */
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .feed-content {
    padding: 8px;
  }
  
  .feed-items {
    gap: 8px;
  }
  
  .empty-state {
    padding: 24px 12px;
  }
  
  .empty-icon {
    font-size: 36px;
    margin-bottom: 10px;
  }
  
  .empty-text {
    font-size: 14px;
  }
  
  .feed-spacer {
    height: 8px;
  }
}

/* RTL Support */
[dir="rtl"] .feed-content {
  direction: rtl;
}

[dir="rtl"] .empty-state {
  direction: rtl;
}

/* Improve performance on mobile */
@media (max-width: 768px) {
  .unified-feed {
    /* Use hardware acceleration for better scrolling */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    -webkit-overflow-scrolling: touch;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .unified-feed::-webkit-scrollbar-thumb {
    background-color: var(--tc-text-primary);
  }
  
  .empty-state {
    color: var(--tc-text-primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .feed-item {
    animation: none;
  }
  
  @keyframes fadeInSlide {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}
</style>
