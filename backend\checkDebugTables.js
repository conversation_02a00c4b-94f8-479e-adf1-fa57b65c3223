const { PrismaClient } = require('@prisma/client');

async function checkDebugTables() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Checking if DebugReport tables exist...');
    
    // Try to query the DebugReport table
    const debugReports = await prisma.debugReport.findMany({
      take: 1
    });
    
    console.log('✅ DebugReport table exists!');
    console.log(`Found ${debugReports.length} debug reports`);
    
    // Try to query the DebugReportContext table
    const debugContexts = await prisma.debugReportContext.findMany({
      take: 1
    });
    
    console.log('✅ DebugReportContext table exists!');
    console.log(`Found ${debugContexts.length} debug contexts`);
    
    console.log('\n🎉 All debug report tables are successfully created!');
    
  } catch (error) {
    console.error('❌ Error checking debug tables:', error.message);
    
    if (error.code === 'P2021') {
      console.log('Tables do not exist yet. Need to run migration or db push.');
    }
  } finally {
    await prisma.$disconnect();
  }
}

checkDebugTables();
