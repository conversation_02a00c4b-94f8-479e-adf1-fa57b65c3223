<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import { usePayerNegotiationStore } from '@/stores/payerNegotiation'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'

interface NegotiationProps {
  transactionId: string
}

interface NegotiationEvent {
  action: 'agree' | 'propose' | 'decline'
  proposedPayerId?: string
  message?: string
}

const props = defineProps<NegotiationProps>()
const emit = defineEmits<{
  negotiationUpdate: [event: NegotiationEvent]
}>()

// Stores and utilities
const authStore = useAuthStore()
const payerNegotiationStore = usePayerNegotiationStore()
const transactionalChatStore = useTransactionalChatStore()
const { t: translate } = useI18n()

// Refs
const toastContainer = ref<HTMLElement>()

// Reactive state
const isSubmitting = ref(false)
const showMessageInput = ref(false)
const showFinalWarning = ref(false)
const proposalMessage = ref('')

// Computed properties
const currentUserId = computed(() => authStore.user?.id)
const negotiation = computed(() => payerNegotiationStore.currentNegotiation)
const otherUserInfo = computed(() => transactionalChatStore.otherUserProfile)

const negotiationState = computed(() => {
  if (!negotiation.value) return 'loading'
  
  switch (negotiation.value.status) {
    case 'AWAITING_FIRST_PAYER_DESIGNATION':
      return negotiation.value.proposedPayerId ? 'user-proposal' : 'system-recommendation'
    case 'AWAITING_COUNTER_PROPOSAL_RESPONSE':
      return 'waiting-for-response'
    case 'FINALIZED':
      return 'finalized'
    case 'CANCELLED':
      return 'cancelled'
    default:
      return 'loading'
  }
})

const systemRecommendation = computed(() => {
  if (!negotiation.value?.systemRecommendation) return null
  
  const rec = negotiation.value.systemRecommendation
  const isCurrentUserRecommended = rec.recommendedPayerId === currentUserId.value
  
  return {
    payerId: rec.recommendedPayerId,
    isCurrentUser: isCurrentUserRecommended,
    reason: rec.reason || 'trustScore',
    userDisplayName: isCurrentUserRecommended ? translate('common.you') : otherUserInfo.value?.name || 'Other User'
  }
})

const currentProposalInfo = computed(() => {
  if (!negotiation.value?.proposedPayerId) return null
  
  const isUserProposed = negotiation.value.proposedPayerId === currentUserId.value
  
  return {
    payerId: negotiation.value.proposedPayerId,
    isUserProposed,
    message: negotiation.value.proposalMessage,
    proposerName: isUserProposed ? translate('common.you') : otherUserInfo.value?.name || 'Other User'
  }
})

const negotiationContext = computed(() => {
  // Get real transaction data from transactional chat store
  const transactionData = transactionalChatStore.transactionDetails
  if (transactionData) {
    return {
      userSends: { 
        amount: transactionData.amountToSend, 
        currency: transactionData.currencyFrom 
      },
      userReceives: { 
        amount: transactionData.amountToReceive, 
        currency: transactionData.currencyTo 
      }
    }
  }
  
  // Fallback values - replace with real transaction data
  return {
    userSends: { amount: 28500000, currency: 'IRR' },
    userReceives: { amount: 500, currency: 'CAD' }
  }
})

// Check if this would be a second counter-offer (final proposal)
const wouldBeFinalProposal = computed(() => {
  return negotiationState.value === 'user-proposal' && !currentProposalInfo.value?.isUserProposed
})

const systemRecommendationReason = computed(() => {
  if (!systemRecommendation.value) return ''
  
  const reasonKey = systemRecommendation.value.reason
  const isCurrentUser = systemRecommendation.value.isCurrentUser
  
  if (reasonKey === 'lowerReputation') {
    return isCurrentUser 
      ? translate('transactionalChat.actionCards.negotiation.reasons.lowerReputation')
      : translate('transactionalChat.actionCards.negotiation.reasons.lowerReputationOther', { 
          name: systemRecommendation.value.userDisplayName 
        })
  }
  
  return translate('transactionalChat.actionCards.negotiation.reasons.trustScore')
})

// Utility functions
const formatAmount = (amount: number, currency: string) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount)
}

// Toast notification system to replace Naive UI
const showToast = (message: string, type: 'success' | 'error' | 'warning' = 'success') => {
  if (!toastContainer.value) return
  
  const toast = document.createElement('div')
  toast.className = `toast toast-${type}`
  toast.textContent = message
  
  toastContainer.value.appendChild(toast)
  
  // Trigger animation
  nextTick(() => {
    toast.classList.add('toast-show')
  })
  
  // Remove after 3 seconds
  setTimeout(() => {
    toast.classList.add('toast-hide')
    setTimeout(() => {
      if (toastContainer.value && toastContainer.value.contains(toast)) {
        toastContainer.value.removeChild(toast)
      }
    }, 300)
  }, 3000)
}

// Event handlers
const handleAcceptSystemRecommendation = async () => {
  if (isSubmitting.value || !negotiation.value) return
  
  isSubmitting.value = true
  
  try {
    await payerNegotiationStore.acceptCurrentProposal(props.transactionId)
    const event: NegotiationEvent = { action: 'agree' }
    emit('negotiationUpdate', event)
    showToast(translate('transactionalChat.actionCards.negotiation.recommendationAccepted'), 'success')
  } catch (error) {
    console.error('Failed to accept system recommendation:', error)
    showToast(translate('transactionalChat.actionCards.negotiation.actionFailed'), 'error')
  } finally {
    isSubmitting.value = false
  }
}

const handleAcceptUserProposal = async () => {
  if (isSubmitting.value || !negotiation.value) return
  
  isSubmitting.value = true
  
  try {
    await payerNegotiationStore.acceptCurrentProposal(props.transactionId)
    const event: NegotiationEvent = { action: 'agree' }
    emit('negotiationUpdate', event)
    showToast(translate('transactionalChat.actionCards.negotiation.proposalAccepted'), 'success')
  } catch (error) {
    console.error('Failed to accept user proposal:', error)
    showToast(translate('transactionalChat.actionCards.negotiation.actionFailed'), 'error')
  } finally {
    isSubmitting.value = false
  }
}

const handleRequestCounterOffer = () => {
  if (wouldBeFinalProposal.value) {
    showFinalWarning.value = true
  } else {
    showMessageInput.value = true
  }
}

const handleConfirmFinalProposal = () => {
  showFinalWarning.value = false
  showMessageInput.value = true
}

const handleCancelFinalProposal = () => {
  showFinalWarning.value = false
}

const handleProposeCounter = async () => {
  if (isSubmitting.value || !negotiation.value || !currentUserId.value) return
  
  isSubmitting.value = true
  
  try {
    // Propose that the current user pays first (counter to current proposal)
    const proposedPayerId = currentUserId.value
    const counterMessage = proposalMessage.value.trim() || undefined
    
    await payerNegotiationStore.proposeFirstPayer(props.transactionId, proposedPayerId, counterMessage)
    
    const event: NegotiationEvent = {
      action: 'propose',
      proposedPayerId: currentUserId.value,
      message: proposalMessage.value.trim() || undefined
    }
    emit('negotiationUpdate', event)
    
    // Reset form
    showMessageInput.value = false
    proposalMessage.value = ''
    showToast(translate('transactionalChat.actionCards.negotiation.counterProposed'), 'success')
  } catch (error) {
    console.error('Failed to propose counter offer:', error)
    showToast(translate('transactionalChat.actionCards.negotiation.actionFailed'), 'error')
  } finally {
    isSubmitting.value = false
  }
}

const handleDeclineProposal = async () => {
  try {
    const event: NegotiationEvent = { action: 'decline' }
    emit('negotiationUpdate', event)
    showToast(translate('transactionalChat.actionCards.negotiation.discussInChat'), 'warning')
  } catch (error) {
    console.error('Failed to decline proposal:', error)
    showToast(translate('transactionalChat.actionCards.negotiation.actionFailed'), 'error')
  }
}

const cancelMessageInput = () => {
  showMessageInput.value = false
  proposalMessage.value = ''
}

// Watch for negotiation changes
watch(() => props.transactionId, async (newTransactionId) => {
  if (newTransactionId) {
    try {
      await payerNegotiationStore.fetchNegotiation(newTransactionId)
    } catch (error) {
      console.error('Failed to fetch negotiation:', error)
      showToast('Failed to load negotiation data', 'error')
    }
  }
}, { immediate: true })

// Initialize socket listeners
payerNegotiationStore.initializeSocketListeners()
</script>

<template>
  <div class="negotiation-card" data-testid="smart-negotiation-section">
    
    <!-- Loading State -->
    <div v-if="negotiationState === 'loading'" class="state-loading">
      <div class="loading-spinner">⏳</div>
      <span class="loading-text">{{ translate('transactionalChat.actionCards.negotiation.loadingNegotiation') }}</span>
    </div>

    <!-- System Recommendation View -->
    <template v-else-if="negotiationState === 'system-recommendation'">
      <!-- Recommendation Header -->
      <div class="recommendation-header">
        <div class="recommendation-icon">🤖</div>
        <div class="recommendation-text">
          <div class="recommendation-main">
            {{ isUserRecommended 
              ? translate('transactionalChat.actionCards.negotiation.youPayFirst') 
              : translate('transactionalChat.actionCards.negotiation.otherPaysFirst', { name: otherUserInfo.name })
            }}
          </div>
          <div class="recommendation-reason">
            {{ systemRecommendationReason || translate('transactionalChat.actionCards.negotiation.reasons.trustScore') }}
          </div>
        </div>
      </div>

      <!-- Simple Payment Flow -->
      <div class="payment-flow-simple">
        <div class="payment-step">
          <span class="step-number">1</span>
          <div class="step-content">
            <div class="payer">
              {{ isUserRecommended ? translate('transactionalChat.actionCards.negotiation.you') : otherUserInfo.name }}
            </div>
            <div class="arrow">→</div>
            <div class="amount">
              {{ isUserRecommended 
                ? formatAmount(negotiationContext.userSends.amount, negotiationContext.userSends.currency)
                : formatAmount(negotiationContext.userReceives.amount, negotiationContext.userReceives.currency)
              }}
            </div>
            <div class="arrow">→</div>
            <div class="receiver">
              {{ isUserRecommended ? otherUserInfo.name : translate('transactionalChat.actionCards.negotiation.you') }}
            </div>
          </div>
        </div>
        <div class="payment-step">
          <span class="step-number">2</span>
          <div class="step-content">
            <div class="payer">
              {{ !isUserRecommended ? translate('transactionalChat.actionCards.negotiation.you') : otherUserInfo.name }}
            </div>
            <div class="arrow">→</div>
            <div class="amount">
              {{ !isUserRecommended 
                ? formatAmount(negotiationContext.userSends.amount, negotiationContext.userSends.currency)
                : formatAmount(negotiationContext.userReceives.amount, negotiationContext.userReceives.currency)
              }}
            </div>
            <div class="arrow">→</div>
            <div class="receiver">
              {{ !isUserRecommended ? otherUserInfo.name : translate('transactionalChat.actionCards.negotiation.you') }}
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-section">
        <div v-if="isUserRecommended" class="action-grid">
          <button 
            class="btn-primary"
            :disabled="isSubmitting"
            data-testid="accept-recommendation-btn"
            @click="handleAcceptSystemRecommendation"
          >
            <span v-if="isSubmitting">⟳</span>
            <span v-else>✓</span>
            {{ translate('transactionalChat.actionCards.negotiation.agreeButton') }}
          </button>
          <button 
            class="btn-secondary"
            :disabled="isSubmitting"
            data-testid="request-counter-btn"
            @click="handleRequestCounterOffer"
          >
            🔄 {{ translate('transactionalChat.actionCards.negotiation.requestButton') }}
          </button>
        </div>
        <div v-else class="single-action">
          <button 
            class="btn-primary btn-large"
            :disabled="isSubmitting"
            data-testid="agree-to-recommendation-btn"
            @click="handleAcceptSystemRecommendation"
          >
            <span v-if="isSubmitting">⟳</span>
            <span v-else>🤝</span>
            {{ translate('transactionalChat.actionCards.negotiation.agreeButton') }}
          </button>
        </div>
      </div>

      <!-- Message Input (Compact) -->
      <div v-if="showMessageInput" class="message-section">
        <textarea 
          v-model="customMessage"
          class="message-input"
          :placeholder="translate('transactionalChat.actionCards.negotiation.messagePlaceholder')"
          data-testid="custom-message-input"
          maxlength="300"
        ></textarea>
        <div class="message-actions">
          <span class="char-count">{{ customMessage.length }}/300</span>
          <button 
            class="btn-send"
            :disabled="isSubmitting"
            @click="handleProposeCounter"
          >
            <span v-if="isSubmitting">⟳</span>
            <span v-else>→</span>
          </button>
        </div>
      </div>
    </template>

    <!-- User Proposal View -->
    <template v-else-if="negotiationState === 'user-proposal'">
      <div class="proposal-header">
        <div class="proposal-icon">💬</div>
        <div class="proposal-text">
          <strong>{{ currentProposalInfo?.proposerName }}</strong> {{ translate('transactionalChat.actionCards.negotiation.proposalText') }}
        </div>
      </div>

      <div v-if="currentProposalInfo?.message" class="proposal-message">
        "{{ currentProposalInfo.message }}"
      </div>

      <!-- Simple Payment Flow for Proposal -->
      <div class="payment-flow-simple">
        <div class="payment-step">
          <span class="step-number">1</span>
          <div class="step-content">
            <div class="payer">
              {{ currentProposalInfo?.isUserProposed ? translate('transactionalChat.actionCards.negotiation.you') : otherUserInfo.name }}
            </div>
            <div class="arrow">→</div>
            <div class="amount">
              {{ currentProposalInfo?.isUserProposed 
                ? formatAmount(negotiationContext.userSends.amount, negotiationContext.userSends.currency)
                : formatAmount(negotiationContext.userReceives.amount, negotiationContext.userReceives.currency)
              }}
            </div>
            <div class="arrow">→</div>
            <div class="receiver">
              {{ currentProposalInfo?.isUserProposed ? otherUserInfo.name : translate('transactionalChat.actionCards.negotiation.you') }}
            </div>
          </div>
        </div>
        <div class="payment-step">
          <span class="step-number">2</span>
          <div class="step-content">
            <div class="payer">
              {{ !currentProposalInfo?.isUserProposed ? translate('transactionalChat.actionCards.negotiation.you') : otherUserInfo.name }}
            </div>
            <div class="arrow">→</div>
            <div class="amount">
              {{ !currentProposalInfo?.isUserProposed 
                ? formatAmount(negotiationContext.userSends.amount, negotiationContext.userSends.currency)
                : formatAmount(negotiationContext.userReceives.amount, negotiationContext.userReceives.currency)
              }}
            </div>
            <div class="arrow">→</div>
            <div class="receiver">
              {{ !currentProposalInfo?.isUserProposed ? otherUserInfo.name : translate('transactionalChat.actionCards.negotiation.you') }}
            </div>
          </div>
        </div>
      </div>

      <!-- Proposal Actions -->
      <div class="action-section">
        <div class="action-grid">
          <button 
            class="btn-primary"
            :disabled="isSubmitting"
            data-testid="accept-proposal-btn"
            @click="handleAcceptUserProposal"
          >
            <span v-if="isSubmitting">⟳</span>
            <span v-else>✓</span>
            {{ translate('transactionalChat.actionCards.negotiation.acceptProposal') }}
          </button>
          <button 
            class="btn-secondary"
            :disabled="isSubmitting"
            data-testid="counter-proposal-btn"
            @click="handleRequestCounterOffer"
          >
            🔄 {{ translate('transactionalChat.actionCards.negotiation.proposeAlternative') }}
            <span v-if="wouldBeFinalProposal" class="warning-badge">⚠️</span>
          </button>
        </div>
        <button 
          class="btn-danger"
          :disabled="isSubmitting"
          data-testid="decline-proposal-btn"
          @click="handleDeclineProposal"
        >
          ✕ {{ translate('transactionalChat.actionCards.negotiation.decline') }}
        </button>
      </div>

      <!-- Message Input (if needed) -->
      <div v-if="showMessageInput" class="message-section">
        <textarea 
          v-model="customMessage"
          class="message-input"
          :placeholder="translate('transactionalChat.actionCards.negotiation.messagePlaceholder')"
          data-testid="custom-message-input"
          maxlength="300"
        ></textarea>
        <div class="message-actions">
          <span class="char-count">{{ customMessage.length }}/300</span>
          <button 
            class="btn-send"
            :disabled="isSubmitting"
            @click="handleProposeCounter"
          >
            <span v-if="isSubmitting">⟳</span>
            <span v-else>→</span>
          </button>
        </div>
      </div>
    </template>

    <!-- Waiting State -->
    <template v-else-if="negotiationState === 'waiting-for-response'">
      <div class="state-waiting">
        <div class="waiting-icon">⏳</div>
        <div class="waiting-text">
          {{ translate('transactionalChat.actionCards.negotiation.waitingMessage', { name: otherUserInfo.name }) }}
        </div>
        <div v-if="currentProposalInfo?.message" class="proposal-summary">
          "{{ currentProposalInfo.message }}"
        </div>
      </div>
    </template>

    <!-- Finalized State -->
    <template v-else-if="negotiationState === 'finalized'">
      <div class="state-success">
        <div class="success-icon">🎉</div>
        <div class="success-text">
          {{ negotiation?.finalizedPayerId === currentUserId 
            ? translate('transactionalChat.actionCards.negotiation.youWillPayFirst')
            : translate('transactionalChat.actionCards.negotiation.otherWillPayFirst', { name: otherUserInfo.name })
          }}
        </div>
      </div>
    </template>

    <!-- Cancelled State -->
    <template v-else-if="negotiationState === 'cancelled'">
      <div class="state-error">
        <div class="error-icon">❌</div>
        <div class="error-text">
          {{ translate('transactionalChat.actionCards.negotiation.cancelledDescription') }}
        </div>
      </div>
    </template>

    <!-- Final Warning Modal (Minimalist) -->
    <div v-if="showFinalWarning" class="modal-overlay" data-testid="final-warning-modal">
      <div class="modal-content">
        <div class="modal-header">
          <span class="warning-icon">⚠️</span>
          {{ translate('transactionalChat.actionCards.negotiation.finalOfferWarning') }}
        </div>
        <div class="modal-text">
          {{ translate('transactionalChat.actionCards.negotiation.finalOfferDescription') }}
        </div>
        <div class="modal-actions">
          <button class="btn-secondary" @click="handleCancelFinalProposal">
            {{ translate('transactionalChat.actionCards.negotiation.discussInChatFirst') }}
          </button>
          <button class="btn-primary" @click="handleConfirmFinalProposal">
            {{ translate('transactionalChat.actionCards.negotiation.proceedWithFinal') }}
          </button>
        </div>
      </div>
    </div>

  </div>
</template>

<style scoped>
/* Minimalist Mobile-First Design */
.negotiation-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 20px;
  margin: 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
  max-width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Loading State */
.state-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 32px 0;
}

.loading-spinner {
  font-size: 32px;
  animation: spin 2s linear infinite;
}

.loading-text {
  color: #64748b;
  font-size: 16px;
}

/* Recommendation Header */
.recommendation-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border-left: 4px solid #3b82f6;
}

.recommendation-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.recommendation-text {
  flex: 1;
}

.recommendation-main {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
  line-height: 1.4;
}

.recommendation-reason {
  font-size: 14px;
  color: #64748b;
  font-style: italic;
}

/* Simple Payment Flow */
.payment-flow-simple {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  border: 1px solid #e2e8f0;
}

.payment-step {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.payment-step:last-child {
  margin-bottom: 0;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #1e293b;
  color: white;
  border-radius: 50%;
  font-size: 14px;
  font-weight: 700;
  flex-shrink: 0;
}

.step-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  font-size: 14px;
}

.payer, .receiver {
  font-weight: 600;
  color: #1e293b;
  min-width: 0;
  flex-shrink: 1;
}

.arrow {
  color: #64748b;
  font-weight: bold;
  flex-shrink: 0;
}

.amount {
  background: #3b82f6;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 700;
  white-space: nowrap;
  flex-shrink: 0;
}

/* Action Section */
.action-section {
  margin-bottom: 16px;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.single-action {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-danger {
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  min-height: 44px;
}

.btn-primary {
  background: #16a34a;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #15803d;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #f8fafc;
  color: #3b82f6;
  border: 1px solid #cbd5e1;
}

.btn-secondary:hover:not(:disabled) {
  background: #e2e8f0;
}

.btn-danger {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
  grid-column: 1 / -1;
  margin-top: 8px;
}

.btn-danger:hover:not(:disabled) {
  background: #fee2e2;
}

.btn-large {
  padding: 16px 24px;
  font-size: 16px;
  min-height: 52px;
  width: 100%;
}

.btn-primary:disabled,
.btn-secondary:disabled,
.btn-danger:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.warning-badge {
  background: #fbbf24;
  color: #92400e;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  margin-left: 4px;
}

/* Message Section */
.message-section {
  background: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
}

.message-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  margin-bottom: 8px;
  box-sizing: border-box;
}

.message-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.message-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.char-count {
  font-size: 12px;
  color: #64748b;
  font-family: monospace;
}

.btn-send {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 32px;
}

.btn-send:hover:not(:disabled) {
  background: #2563eb;
}

/* Proposal States */
.proposal-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 16px;
  background: #fef3c7;
  border-radius: 12px;
  border-left: 4px solid #f59e0b;
}

.proposal-icon {
  font-size: 20px;
}

.proposal-text {
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
  line-height: 1.4;
}

.proposal-message {
  background: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  font-style: italic;
  color: #64748b;
  margin-bottom: 16px;
  font-size: 14px;
}

/* State Components */
.state-waiting,
.state-success,
.state-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 32px 16px;
}

.waiting-icon,
.success-icon,
.error-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.waiting-text,
.success-text,
.error-text {
  font-size: 16px;
  line-height: 1.4;
  color: #1e293b;
}

.state-success {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-radius: 12px;
  color: #166534;
}

.state-error {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border-radius: 12px;
  color: #991b1b;
}

.proposal-summary {
  background: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  margin-top: 16px;
  font-style: italic;
  color: #64748b;
  font-size: 14px;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 24px;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
}

.warning-icon {
  font-size: 20px;
}

.modal-text {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 20px;
}

.modal-actions {
  display: flex;
  gap: 12px;
}

.modal-actions .btn-primary,
.modal-actions .btn-secondary {
  flex: 1;
  padding: 12px 16px;
  font-size: 14px;
}

/* Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 480px) {
  .negotiation-card {
    padding: 16px;
  }

  .action-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .step-content {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }

  .step-content .arrow {
    transform: rotate(90deg);
  }

  .modal-overlay {
    padding: 16px;
  }

  .modal-content {
    padding: 20px;
  }

  .modal-actions {
    flex-direction: column;
  }
}

/* RTL Support */
[dir="rtl"] .recommendation-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .step-content {
  flex-direction: row-reverse;
}

[dir="rtl"] .message-actions {
  flex-direction: row-reverse;
}
</style>
