import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises, VueWrapper } from '@vue/test-utils';
import { nextTick, defineComponent, h } from 'vue';
import ProfileView from '../ProfileView.vue';
import { createTesting<PERSON>inia } from '@pinia/testing';
import { NButton } from 'naive-ui';

// Mock external dependencies
const apiClientPostMock = vi.fn();
const apiClientGetMock = vi.fn(); // Added mock for GET requests
vi.mock('@/services/apiClient', () => ({
  default: {
    get post() { return apiClientPostMock; },
    get get() { return apiClientGetMock; } // Added getter for GET mock
  },
}));

const handleErrorMock = vi.fn();
vi.mock('@/utils/errorHandler', () => ({
  get handleError() { return handleErrorMock; }
}));

const messageSuccessMock = vi.fn();
const messageErrorMock = vi.fn();
const messageInfoMock = vi.fn();
const messageWarningMock = vi.fn();

// Simplified synchronous mock for Naive UI
vi.mock('naive-ui', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useMessage: () => ({
      success: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
      warning: vi.fn(),
    }),
    NTooltip: defineComponent({
      name: 'NTooltip',
      setup(_, { slots }) {
        return () =>
          h('div', [
            slots.trigger ? slots.trigger() : null,
            h('div', { class: 'stub-tooltip-content' }, slots.default ? slots.default() : null),
          ]);
      },
    }),
    // Optionally stub other components if you want full control:
    NFormItem: defineComponent({
      name: 'NFormItem',
      setup(_, { slots }) {
        return () =>
          h('div', [
            slots.label ? slots.label() : null,
            slots.default ? slots.default() : [],
          ]);
      },
    }),
    NIcon: defineComponent({
      name: 'NIcon',
      setup(_, { slots, attrs }) {
        return () => h('span', { ...attrs }, slots.default ? slots.default() : null);
      }
    }),
    NForm: defineComponent({
      name: 'NForm',
      inheritAttrs: false,
      setup(_, { slots, expose, attrs }) {
        const validate = vi.fn();
        const restoreValidation = vi.fn();
        expose({ validate, restoreValidation });
        // Forward all attributes (including v-show/style directives) to the form
        return () => h('form', { ...attrs }, slots.default ? slots.default() : []);
      }
    }),
       NInput: defineComponent({
      name: 'NInput',
      props: ['value', 'onUpdate:value', 'disabled'],
      setup(props, { slots, expose, emit }) {
        const focus = vi.fn();
        const blur = vi.fn();
        expose({ focus, blur });
        return () => h('div', [
          h('input', {
            value: props.value,
            disabled: props.disabled,
            onInput: (e: Event) => emit('update:value', (e.target as HTMLInputElement).value),
            ref: (el: HTMLInputElement) => {
              // Optionally, you can call focus here if needed for the test
            }
          }),
          slots.default ? slots.default() : []
        ]);
      }
    }),
    NCard: defineComponent({
      name: 'NCard',
      setup(_, { slots }) {
        return () => h('div', { class: 'n-card-stub' }, slots.default ? slots.default() : []);
      }
    }),
    NButton: defineComponent({
      name: 'NButton',
      props: ['disabled', 'loading'],
      setup(props, { slots }) {
        return () => h('button', { 
          disabled: props.disabled || props.loading 
        }, slots.default ? slots.default() : []);
      }
    }),
    // Simplified stub for spinner: always render children to avoid DOM fragment issues
    NSpin: defineComponent({
      name: 'NSpin',
      setup(_, { slots }) {
        return () => h('div', slots.default ? slots.default() : []);
      }
    }),
    NDescriptions: defineComponent({
      name: 'NDescriptions',
      setup(_, { slots }) {
        return () => h('div', { class: 'n-descriptions-stub' }, slots.default ? slots.default() : []);
      }
    }),
    NDescriptionsItem: defineComponent({
      name: 'NDescriptionsItem',
      props: ['label'],
      setup(props, { slots }) {
        return () => h('div', { class: 'n-descriptions-item-stub' }, [
          h('span', { class: 'n-descriptions-item-label-stub'}, props.label),
          slots.default ? slots.default() : []
        ]);
      }
    }),
    NTag: defineComponent({
      name: 'NTag',
      props: ['type'],
      setup(props, { slots }) {
        return () => h('span', { class: `n-tag-stub ${props.type}` }, slots.default ? slots.default() : []);
      }
    }),
    NDivider: defineComponent({
      name: 'NDivider',
      setup() {
        return () => h('hr', { class: 'n-divider-stub' });
      }
    }),
    NH3: defineComponent({
      name: 'NH3',
      setup(_, { slots }) {
        return () => h('h3', { class: 'n-h3-stub' }, slots.default ? slots.default() : []);
      }
    }),
    NAlert: defineComponent({
      name: 'NAlert',
      props: ['type', 'title', 'closable'],
      setup(props, { slots }) {
        return () => h('div', { class: `n-alert-stub ${props.type}` }, [
          props.title ? h('div', { class: 'n-alert-title-stub' }, props.title) : null,
          slots.default ? slots.default() : []
        ]);
      }
    }),   
    // NTooltip is stubbed in mount options
  }});

// Mock child components / icons
vi.mock('@vicons/ionicons5', () => ({
  InformationCircleOutline: defineComponent({
    name: 'InformationCircleOutline',
    setup() {
      return () => h('svg', { 'data-testid': 'info-icon-svg' });
    }
  }),
}));


describe('ProfileView.vue - Additional Tests', () => {
  let wrapper: VueWrapper<any>;
  let authStoreMock: any;

  // Spies are declared here
  let phoneFormValidateSpy: ReturnType<typeof vi.spyOn> | undefined;
  let otpFormValidateSpy: ReturnType<typeof vi.spyOn> | undefined;
  let phoneInputFocusSpy: ReturnType<typeof vi.spyOn> | undefined;
  let otpInputFocusSpy: ReturnType<typeof vi.spyOn> | undefined;

  beforeEach(async () => {
    vi.useFakeTimers();

    apiClientPostMock.mockReset();
    apiClientGetMock.mockReset();
    handleErrorMock.mockReset();
    messageSuccessMock.mockReset();
    messageErrorMock.mockReset();
    messageInfoMock.mockReset();
    messageWarningMock.mockReset();

    const initialUser = { 
      id: 'user1', 
      email: '<EMAIL>', 
      emailVerified: true, 
      phoneVerified: false,
      phoneNumber: null, 
      reputation: 0, 
      offersMade: 0, 
      offersTaken: 0, 
      chatPrivacy: 'PUBLIC' 
    };

    // This mock is for apiClient.get IF the original fetchUserProfile were to call it.
    // However, we will now mock fetchUserProfile itself.
    apiClientGetMock.mockResolvedValue({ data: initialUser }); 
    handleErrorMock.mockReturnValue(undefined);

    const pinia = createTestingPinia({
      createSpy: vi.fn,
      initialState: {
        auth: {
          user: initialUser, 
        },
      },
      stubActions: false, 
    });

    const stores = await import('@/stores/auth');
    authStoreMock = stores.useAuthStore(pinia);
    
    // Mock the implementation of fetchUserProfile to prevent it from clearing user state
    // The user state is already set by createTestingPinia's initialState.
    // If the component relies on loading states set by this action, they should be managed
    // by the component's own loadingProfile ref or explicitly in this mock if necessary.
    vi.spyOn(authStoreMock, 'fetchUserProfile').mockImplementation(async () => {
      // console.log('[TEST_DEBUG_MOCK] Mocked authStore.fetchUserProfile called');
      // Ensure the store reflects the initial state if the action normally sets it.
      // authStoreMock.user = initialUser; // Should be redundant due to createTestingPinia
      return Promise.resolve();
    });

    authStoreMock.updatePhoneVerificationStatus = vi.fn();

    wrapper = mount(ProfileView, {
      global: {
        plugins: [pinia],
      },
    });

    // Allow onMounted and subsequent reactivity to settle
    // The component's onMounted will call our mocked fetchUserProfile
    await flushPromises(); 
    await nextTick();      
    await nextTick();      
    await nextTick(); // Third nextTick for good measure

    // --- Debugging Logs ---
    console.log('[TEST_DEBUG_BEFORE_EACH] wrapper.vm.user:', JSON.stringify(wrapper.vm.user));
    console.log('[TEST_DEBUG_BEFORE_EACH] wrapper.vm.isPhoneVerified:', wrapper.vm.isPhoneVerified);
    console.log('[TEST_DEBUG_BEFORE_EACH] wrapper.vm.loadingProfile:', wrapper.vm.loadingProfile);
    console.log('[TEST_DEBUG_BEFORE_EACH] wrapper.html():', wrapper.html()); // Crucial log

    if (wrapper.vm.phoneFormRef?.validate) {
      phoneFormValidateSpy = vi.spyOn(wrapper.vm.phoneFormRef, 'validate');
    } else {
      console.warn('[TEST_DEBUG_BEFORE_EACH] phoneFormRef not found or validate method missing on initial mount. Check v-if="user && !isPhoneVerified".');
    }

    const phoneNInputComponent = wrapper.findComponent({ ref: 'phoneInputRef' });
    if (phoneNInputComponent.exists() && typeof phoneNInputComponent.vm.focus === 'function') {
      phoneInputFocusSpy = vi.spyOn(phoneNInputComponent.vm, 'focus');
    } else {
      console.warn('[TEST_DEBUG_BEFORE_EACH] phoneInputRef component not found or focus method missing on initial mount. Check v-if="user && !isPhoneVerified".');
    }
  });

  afterEach(() => {
    vi.restoreAllMocks();
    phoneFormValidateSpy = undefined;
    otpFormValidateSpy = undefined;
    phoneInputFocusSpy = undefined;
    otpInputFocusSpy = undefined;
    vi.useRealTimers();
    if (wrapper) {
      try {
        wrapper.unmount();
      } catch (e) {
        console.error('Error during wrapper.unmount():', e);
      }
    }
  });

  // Helper function to navigate to OTP form
  async function navigateToOtpForm(phoneNumber = '+15551234567') {
    // Check if phone form is even rendered
    const phoneFormElement = wrapper.find('[data-testid="phone-form"]');
    if (!phoneFormElement.exists()) {
        console.error('[TEST_DEBUG_NAV_OTP] Phone form (data-testid="phone-form") not found. Current user:', JSON.stringify(wrapper.vm.user), 'isPhoneVerified:', wrapper.vm.isPhoneVerified);
        console.error('[TEST_DEBUG_NAV_OTP] Current HTML:', wrapper.html()); // Crucial log
        throw new Error('Phone form (data-testid="phone-form") not found in navigateToOtpForm. Check v-if conditions.');
    }

    if (wrapper.vm.phoneFormRef?.validate && !phoneFormValidateSpy) {
        phoneFormValidateSpy = vi.spyOn(wrapper.vm.phoneFormRef, 'validate');
    } else if (!wrapper.vm.phoneFormRef?.validate) {
        console.warn('[TEST_DEBUG_NAV_OTP] phoneFormRef not found or validate method missing before sending OTP.');
    }
    phoneFormValidateSpy?.mockResolvedValue(undefined);

    apiClientPostMock.mockResolvedValueOnce({
      data: { message: 'OTP sent successfully!' },
    });

    const phoneInputWrapper = wrapper.find('[data-testid="phone-input"]');
    if (!phoneInputWrapper.exists()) {
        // This is the original error point, add more context
        console.error('[TEST_DEBUG_NAV_OTP] Phone input (data-testid="phone-input") not found. Current user:', JSON.stringify(wrapper.vm.user), 'isPhoneVerified:', wrapper.vm.isPhoneVerified);
        console.error('[TEST_DEBUG_NAV_OTP] Current HTML (phone input search):', wrapper.html()); // Uncomment for deep dive
        throw new Error('Phone input (data-testid="phone-input") not found in navigateToOtpForm');
    }
    await phoneInputWrapper.find('input').setValue(phoneNumber);

    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
    if (!sendOtpButton.exists()) throw new Error('Send OTP button not found in navigateToOtpForm');
    await sendOtpButton.trigger('click');

    await flushPromises(); // API call resolves. requestOtp in component continues (sets showOtpForm=true)
    await nextTick();      // Allows requestOtp's internal nextTick to complete (DOM updates, otpInputRef available),
                           // and focusOtpInput() is called, scheduling the actual .focus() for the *next* tick.

    // Now, otpInputRef should exist. Set up the spy on its focus method.
    const otpNInputComponent = wrapper.findComponent({ ref: 'otpInputRef' });
    if (otpNInputComponent.exists() && typeof otpNInputComponent.vm.focus === 'function') {
      if (otpInputFocusSpy && typeof otpInputFocusSpy.mockRestore === 'function') {
        otpInputFocusSpy.mockRestore();
      }
      otpInputFocusSpy = vi.spyOn(otpNInputComponent.vm, 'focus');
    } else {
      console.warn('[TEST_DEBUG_NAV_OTP] otpInputRef component not found or focus method missing when attempting to spy. HTML:', wrapper.html());
      otpInputFocusSpy = undefined;
    }

    // This nextTick allows the focus() call scheduled by focusOtpInput() to execute.
    await nextTick();
    // Optional: another nextTick for full settlement if needed.
    await nextTick();


    // Initialize otpFormValidateSpy (or re-initialize if already set)
    if (wrapper.vm.otpFormRef?.validate) {
      if (otpFormValidateSpy && typeof otpFormValidateSpy.mockRestore === 'function') {
        otpFormValidateSpy.mockRestore();
      }
      otpFormValidateSpy = vi.spyOn(wrapper.vm.otpFormRef, 'validate');
    } else {
      console.warn('[TEST_DEBUG_NAV_OTP] otpFormRef not found or validate method missing after navigating to OTP form.');
      otpFormValidateSpy = undefined;
    }
  }

  // --- Test Cases ---

  describe('III. Form Validation', () => {
    it('III.9 - OTP Input Validation: does not submit if OTP is not 6 digits', async () => {
      await navigateToOtpForm(); 
      
      // otpFormValidateSpy should be initialized by navigateToOtpForm
      if (!otpFormValidateSpy) {
        // If still not defined, try one more time, or fail test explicitly
        if (wrapper.vm.otpFormRef?.validate) {
          otpFormValidateSpy = vi.spyOn(wrapper.vm.otpFormRef, 'validate');
        } else {
          throw new Error('otpFormValidateSpy could not be initialized. otpFormRef or its validate method is missing.');
        }
      }
      otpFormValidateSpy.mockRejectedValueOnce(new Error('OTP must be 6 digits'));

      const otpInputWrapper = wrapper.find('[data-testid="otp-input"]');
      if (!otpInputWrapper.exists()) throw new Error('OTP input not found in test III.9');
      await otpInputWrapper.find('input').setValue('12345'); 

      const verifyOtpButton = wrapper.find('[data-testid="verify-otp-button"]');
      if (!verifyOtpButton.exists()) throw new Error('Verify OTP button not found in test III.9');
      await verifyOtpButton.trigger('click');

      await flushPromises();
      await nextTick();

      expect(otpFormValidateSpy).toHaveBeenCalled();
      // Check that the second call to apiClientPostMock (verify-otp) was NOT made
      // The first call is send-otp from navigateToOtpForm
      const verifyOtpCalls = apiClientPostMock.mock.calls.filter(call => call[0].includes('/auth/phone/verify-otp'));
      expect(verifyOtpCalls.length).toBe(0);
    });
  });

    describe('VI. OTP Form Actions', () => {
    it('focuses OTP input after successful OTP request', async () => {
        // Ensure phone form validation spy (if it exists) is mocked to pass
        if (phoneFormValidateSpy) {
          phoneFormValidateSpy.mockResolvedValue(undefined);
        } else if (wrapper.vm.phoneFormRef?.validate) {
          phoneFormValidateSpy = vi.spyOn(wrapper.vm.phoneFormRef, 'validate').mockResolvedValue(undefined);
        }

        // 1. Mock the API call with a manually controlled promise
        let resolveApiCall: (value: any) => void = () => {};
        apiClientPostMock.mockImplementationOnce(() => 
          new Promise((resolve) => {
            resolveApiCall = resolve;
          })
        );

        // Simulate user entering phone and requesting OTP
        const phoneInputWrapper = wrapper.find('[data-testid="phone-input"]');
        await phoneInputWrapper.find('input').setValue('+15551234567');
        const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
        await sendOtpButton.trigger('click'); // requestOtp starts, awaits apiClient.post

        // Allow validation promise (if any) and initial part of requestOtp to run,
        // so requestOtp is paused at `await apiClient.post(...)`
        await nextTick(); 

        // 2. Manually resolve the API call.
        // This allows requestOtp to continue: it sets showOtpForm = true,
        // and then schedules a nextTick to call focus on the OTP input.
        resolveApiCall({ data: { message: 'OTP sent successfully!' } });
        
        // 3. Allow Vue to process the state change (showOtpForm=true) and update the DOM.
        // After this tick, the otpInputRef component should exist in the DOM.
        // The component's internal nextTick for focusing is also scheduled but likely not yet run.
        await nextTick(); 
      
        // 4. OTP form and input should now be visible.
        const otpNInputComponent = wrapper.findComponent({ ref: 'otpInputRef' });
        expect(otpNInputComponent.exists()).toBe(true);
      
        // 5. Attach spy BEFORE the component's internal nextTick (which calls focus()) executes.
        const focusSpy = vi.spyOn(otpNInputComponent.vm, 'focus');
      
        // 6. Now allow the component's scheduled nextTick (which calls focus()) to execute.
        await nextTick(); 
      
        expect(focusSpy).toHaveBeenCalled();
        focusSpy.mockRestore();
      });
  });

  describe('VIII. UI Elements & Edge Cases', () => {
    it('VIII.22 - Tooltips: phone input tooltip trigger and content exist', async () => {
      // Ensure the form and icon are part of the initial render
      expect(wrapper.find('[data-testid="phone-form"]').exists()).toBe(true);
      // The icon mock should render the data-testid
      expect(wrapper.find('[data-testid="info-icon-svg"]').exists()).toBe(true); 
      
      await flushPromises(); 
      await nextTick(); 

      // The NTooltip stub renders its slot content directly for testing
      const tooltipContent = wrapper.find('.stub-tooltip-content'); 
      expect(tooltipContent.exists()).toBe(true); 
      if (tooltipContent.exists()) { 
          expect(tooltipContent.text()).toContain("Start with '+' and your country code");
      }
    });

    describe('VIII.24 - Button Disablement during Loading', () => {
      it('disables "Send OTP" button and phone input when loadingRequest is true', async () => {
        if (wrapper.vm.phoneFormRef?.validate && !phoneFormValidateSpy) {
            phoneFormValidateSpy = vi.spyOn(wrapper.vm.phoneFormRef, 'validate');
        }
        if (!phoneFormValidateSpy) throw new Error('phoneFormValidateSpy not initialized for VIII.24 Send OTP');
        phoneFormValidateSpy.mockResolvedValue(undefined);

        let resolveApiCall: (value: any) => void = () => {};
        apiClientPostMock.mockImplementationOnce(() => new Promise(resolve => {
          resolveApiCall = resolve;
        }));

        const phoneInputWrapper = wrapper.find('[data-testid="phone-input"]');
        if (!phoneInputWrapper.exists()) throw new Error('Phone input not found in VIII.24 Send OTP');
        await phoneInputWrapper.find('input').setValue('+1234567890');
        
        const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
        if (!sendOtpButton.exists()) throw new Error('Send OTP button not found in VIII.24 Send OTP');
        await sendOtpButton.trigger('click');

        await nextTick(); 
        expect(wrapper.vm.loadingRequest).toBe(true);
        expect(sendOtpButton.attributes('disabled')).toBeDefined();
        expect(phoneInputWrapper.find('input').attributes('disabled')).toBeDefined();

        if (resolveApiCall) resolveApiCall({ data: { message: 'OTP sent!' } }); // Resolve the promise
        await flushPromises();
        await nextTick();
        expect(wrapper.vm.loadingRequest).toBe(false);
      });

      it('disables "Verify OTP" button and OTP input when loadingVerify is true', async () => {
        await navigateToOtpForm();
        if (wrapper.vm.otpFormRef?.validate && !otpFormValidateSpy) {
            otpFormValidateSpy = vi.spyOn(wrapper.vm.otpFormRef, 'validate');
        }
        if (!otpFormValidateSpy) throw new Error('otpFormValidateSpy not initialized for VIII.24 Verify OTP');
        otpFormValidateSpy.mockResolvedValue(undefined);


        let resolveApiCall: (value: any) => void = () => {};
        apiClientPostMock.mockReset(); // Reset from navigateToOtpForm's call
        apiClientPostMock.mockImplementationOnce(() => new Promise(resolve => {
          resolveApiCall = resolve;
        }));

        const otpInputWrapper = wrapper.find('[data-testid="otp-input"]');
        if (!otpInputWrapper.exists()) throw new Error('OTP input not found in VIII.24 Verify OTP');
        await otpInputWrapper.find('input').setValue('123456');
        
        const verifyOtpButton = wrapper.find('[data-testid="verify-otp-button"]');
        if (!verifyOtpButton.exists()) throw new Error('Verify OTP button not found in VIII.24 Verify OTP');
        await verifyOtpButton.trigger('click');

        await nextTick();
        expect(wrapper.vm.loadingVerify).toBe(true);
        expect(verifyOtpButton.attributes('disabled')).toBeDefined();
        expect(otpInputWrapper.find('input').attributes('disabled')).toBeDefined();

        if (resolveApiCall) resolveApiCall({ data: { message: 'Verified!', phoneNumber: '+15551234567' } });
        await flushPromises();
        await nextTick();
        expect(wrapper.vm.loadingVerify).toBe(false);
      });

       it('disables "Resend Code" button when loadingRequest is true (during resend), then re-enables it after cooldown', async () => {
        await navigateToOtpForm(); // This sends an OTP and might start a cooldown timer.

        // Advance time to ensure any initial OTP request cooldown (e.g., 60 seconds) has passed.
        vi.advanceTimersByTime(60000); 
        await vi.runAllTimersAsync(); 

        // Ensure phoneFormValidateSpy is set up if phoneFormRef exists
        if (wrapper.vm.phoneFormRef?.validate && !phoneFormValidateSpy) {
             phoneFormValidateSpy = vi.spyOn(wrapper.vm.phoneFormRef, 'validate');
        }
        // If phoneFormRef is expected for resend logic, ensure spy is present
        // For resend, phoneForm might not be validated, but if it is, it should pass.
        phoneFormValidateSpy?.mockResolvedValue(undefined); 

        apiClientPostMock.mockReset(); 
        let resolveApiCall: (value: any) => void = () => {};
        apiClientPostMock.mockImplementationOnce(() => new Promise(resolve => {
          resolveApiCall = resolve;
        }));

        const resendButton = wrapper.find('[data-testid="resend-otp-button"]');
        if (!resendButton.exists()) throw new Error('Resend OTP button not found in VIII.24 Resend Code');

        await resendButton.trigger('click'); // Calls requestOtp
        // requestOtp sets loadingRequest = true before awaiting apiClient.post

        // Allow Vue to process loadingRequest state change and update DOM/VM.
        await nextTick(); 
        // Additional ticks if needed for complex reactivity, but one should be enough for loadingRequest to be set.
        // await nextTick(); 
        
        expect(wrapper.vm.loadingRequest).toBe(true); // Verify loadingRequest is true
        
        const resendButtonComponent = wrapper.findAllComponents({ name: 'NButton' }).find(
          btn => btn.attributes('data-testid') === 'resend-otp-button'
        );
        expect(resendButtonComponent?.props('loading')).toBe(true); // NButton's loading prop
        expect(resendButton.attributes('disabled')).toBeDefined(); // DOM disabled attribute

        // Resolve the API call for resend
        if (resolveApiCall) resolveApiCall({ data: { message: 'OTP resent successfully!' } });
        await flushPromises(); // API call resolves, requestOtp's finally block runs (loadingRequest=false)
        await nextTick();      // Vue processes loadingRequest = false state change

        expect(wrapper.vm.loadingRequest).toBe(false); // loadingRequest should now be false

        // Check NButton's loading prop is false
        const updatedResendButtonComponent = wrapper.findAllComponents(NButton).find(
            (comp) => comp.attributes('data-testid') === 'resend-otp-button'
        );
        expect(updatedResendButtonComponent?.props('loading')).toBe(false);

        // At this point, loadingRequest is false, but resendDisabledTime (e.g., 30s) is active.
        // The button should still be disabled due to resendDisabledTime.
        const resendButtonWrapperAfterLoad = wrapper.find('[data-testid="resend-otp-button"]');
        expect(resendButtonWrapperAfterLoad.attributes('disabled')).toBeDefined();
        expect(wrapper.vm.resendDisabledTime).toBeGreaterThan(0);


        // Advance timers to let resendDisabledTime count down to 0
        const currentResendDisabledTimeMs = wrapper.vm.resendDisabledTime * 1000;
        vi.advanceTimersByTime(currentResendDisabledTimeMs + 500); // Advance by remaining time + buffer
        await vi.runAllTimersAsync(); // Ensure interval timer completes
        await nextTick(); // Allow Vue to react to resendDisabledTime becoming 0

        expect(wrapper.vm.resendDisabledTime).toBe(0); // resendDisabledTime should now be 0

        // Re-find the button and check its final state (should be enabled)
        const finalResendButtonWrapper = wrapper.find('[data-testid="resend-otp-button"]');
        // Assuming no other conditions like blockedUntil are active
        expect(finalResendButtonWrapper.attributes('disabled')).toBeUndefined();
      }); // End of "disables Resend Code" test; // End of "disables Resend Code" test
    }); // End of "VIII.24 - Button Disablement during Loading" describe block

    describe('VIII.25 - Focus Management', () => {
      it('focuses OTP input after successful OTP request', async () => {
        // Ensure phone form validation spy (if it exists) is mocked to pass
        if (phoneFormValidateSpy) {
          phoneFormValidateSpy.mockResolvedValue(undefined);
        } else if (wrapper.vm.phoneFormRef?.validate) {
          phoneFormValidateSpy = vi.spyOn(wrapper.vm.phoneFormRef, 'validate').mockResolvedValue(undefined);
        }

        // 1. Mock the API call with a manually controlled promise
        let resolveApiCall: (value: any) => void = () => {};
        apiClientPostMock.mockImplementationOnce(() => 
          new Promise((resolve) => {
            resolveApiCall = resolve;
          })
        );

        // Simulate user entering phone and requesting OTP
        const phoneInputWrapper = wrapper.find('[data-testid="phone-input"]');
        await phoneInputWrapper.find('input').setValue('+15551234567');
        const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
        await sendOtpButton.trigger('click'); // requestOtp starts, awaits apiClient.post

        // Allow validation promise (if any) and initial part of requestOtp to run,
        // so requestOtp is paused at `await apiClient.post(...)`
        await nextTick(); 

        // 2. Manually resolve the API call.
        // This allows requestOtp to continue: it sets showOtpForm = true,
        // and then schedules a nextTick to call focus on the OTP input.
        resolveApiCall({ data: { message: 'OTP sent successfully!' } });
        
        // 3. Allow Vue to process the state change (showOtpForm=true) and update the DOM.
        // After this tick, the otpInputRef component should exist in the DOM.
        // The component's internal nextTick for focusing is also scheduled but likely not yet run.
        await nextTick(); 
      
        // 4. OTP form and input should now be visible.
        const otpNInputComponent = wrapper.findComponent({ ref: 'otpInputRef' });
        expect(otpNInputComponent.exists()).toBe(true);
      
        // 5. Attach spy BEFORE the component's internal nextTick (which calls focus()) executes.
        const focusSpy = vi.spyOn(otpNInputComponent.vm, 'focus');
      
        // 6. Now allow the component's scheduled nextTick (which calls focus()) to execute.
        await nextTick(); 
      
        expect(focusSpy).toHaveBeenCalled();
        focusSpy.mockRestore();
      });

      it('focuses phone input after clicking "Change Number"', async () => {
        await navigateToOtpForm(); // User is now on OTP form. phoneInput is hidden.

        // The phoneInputFocusSpy might have been created in beforeEach, targeting the initially visible phone input.
        // We need to ensure it's restored so we can get the raw mock from the component instance later.
        if (phoneInputFocusSpy && typeof phoneInputFocusSpy.mockRestore === 'function') {
          phoneInputFocusSpy.mockRestore();
        }
        phoneInputFocusSpy = undefined; // Clear the variable for this test

        const changeNumberButton = wrapper.find('[data-testid="change-number-button"]');
        if (!changeNumberButton.exists()) throw new Error('Change number button not found in VIII.25 Focus Phone');
        
        console.log('[TEST_DEBUG_FOCUS] Before clicking "Change Number". HTML:', wrapper.html());
        await changeNumberButton.trigger('click'); // Calls goBackToPhoneInput in component
                                                  // goBackToPhoneInput sets showOtpForm = false and schedules focus in nextTick
        
        // Allow component to react to click (showOtpForm = false) and update DOM (phone form becomes visible)
        await nextTick(); 
        console.log('[TEST_DEBUG_FOCUS] After first nextTick (DOM updated). HTML:', wrapper.html());

        // Re-find the phone input component now that it's visible
        const phoneNInputComponentVisible = wrapper.findComponent({ ref: 'phoneInputRef' });
        if (!phoneNInputComponentVisible.exists() || typeof phoneNInputComponentVisible.vm.focus !== 'function') {
          console.error('[TEST_DEBUG_FOCUS] phoneInputRef component NOT found or focus method missing after clicking Change Number. HTML:', wrapper.html());
          throw new Error('phoneInputRef component not found or focus method missing after clicking Change Number.');
        }
        
        // Get the direct mock function from the stub instance
        const directMockFocusFn = phoneNInputComponentVisible.vm.focus as ReturnType<typeof vi.fn>;
        expect(vi.isMockFunction(directMockFocusFn)).toBe(true); // Verify it's the mock from the stub

        // Log calls BEFORE the nextTick that should trigger the component's focus call
        console.log('[TEST_DEBUG_FOCUS] directMockFocusFn calls BEFORE final nextTick:', JSON.stringify(directMockFocusFn.mock.calls));
        // It should not have been called yet by this specific interaction.
        // If it was called by beforeEach, mockClear() would be needed if we didn't restore/reset the spy.
        // Since we restored the spy from beforeEach, this directMockFocusFn should be "clean" for this interaction.

        // Wait for the focus to be triggered by the component's logic (its internal nextTick)
        await nextTick();
        console.log('[TEST_DEBUG_FOCUS] After second nextTick (component should have called focus).');
        
        console.log('[TEST_DEBUG_FOCUS] directMockFocusFn calls AFTER final nextTick:', JSON.stringify(directMockFocusFn.mock.calls));
        expect(directMockFocusFn).toHaveBeenCalled();
      });    
    }); 
});

describe('IX. ProfileView - Additional Edge Cases & UI States', () => {
  it('IX.1 - Shows unverified phone status tag and verification section when phone is not verified', async () => {
    // User is not phone verified by default in beforeEach
    const phoneStatusTag = wrapper.find('[data-testid="phone-status-unverified"]');
    expect(phoneStatusTag.exists()).toBe(true);
    expect(phoneStatusTag.text()).toContain('Not Verified');

    const verificationTitle = wrapper.find('[data-testid="phone-verification-title"]');
    expect(verificationTitle.exists()).toBe(true);
    expect(verificationTitle.text()).toContain('Phone Number Verification');

    const phoneForm = wrapper.find('[data-testid="phone-form"]');
    expect(phoneForm.exists()).toBe(true);
  });

  it('IX.2 - Does not show OTP form by default (before requesting OTP)', async () => {
    const otpForm = wrapper.find('[data-testid="otp-form"]');
    expect(wrapper.find('[data-testid="phone-form"]').exists()).toBe(true);
    // The icon mock should render the data-testid
    expect(wrapper.find('[data-testid="info-icon-svg"]').exists()).toBe(true); 
    
    await flushPromises(); 
    await nextTick(); 

    // The NTooltip stub renders its slot content directly for testing
    const tooltipContent = wrapper.find('.stub-tooltip-content'); 
    expect(tooltipContent.exists()).toBe(true); 
    if (tooltipContent.exists()) { 
      expect(tooltipContent.text()).toContain("Start with '+' and your country code");
    }
  });

})})