<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DreamNavBar - Stunning Custom Navigation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Light theme */
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #4ecdc4;
            --warning-color: #ffe066;
            --error-color: #ff6b6b;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --bg-primary: rgba(255, 255, 255, 0.95);
            --bg-secondary: rgba(247, 250, 252, 0.8);
            --border-color: rgba(226, 232, 240, 0.8);
            --shadow-light: rgba(0, 0, 0, 0.1);
            --shadow-medium: rgba(0, 0, 0, 0.15);
            --blur-strength: 20px;
        }

        [data-theme="dark"] {
            --primary-color: #9f7aea;
            --secondary-color: #667eea;
            --accent-color: #ed64a6;
            --success-color: #38b2ac;
            --warning-color: #ecc94b;
            --error-color: #f56565;
            --text-primary: #f7fafc;
            --text-secondary: #e2e8f0;
            --bg-primary: rgba(26, 32, 44, 0.95);
            --bg-secondary: rgba(45, 55, 72, 0.8);
            --border-color: rgba(74, 85, 104, 0.8);
            --shadow-light: rgba(0, 0, 0, 0.3);
            --shadow-medium: rgba(0, 0, 0, 0.4);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
            min-height: 100vh;
            color: var(--text-primary);
            transition: all 0.3s ease;
            overflow-x: hidden;
        }

        /* Animated background particles */
        .bg-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .particle:nth-child(1) { width: 80px; height: 80px; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { width: 120px; height: 120px; left: 20%; animation-delay: 1s; }
        .particle:nth-child(3) { width: 60px; height: 60px; left: 60%; animation-delay: 2s; }
        .particle:nth-child(4) { width: 100px; height: 100px; left: 80%; animation-delay: 3s; }
        .particle:nth-child(5) { width: 40px; height: 40px; left: 40%; animation-delay: 4s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.5; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        /* Main navbar container */
        .dream-navbar {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 1000;
            background: var(--bg-primary);
            backdrop-filter: blur(var(--blur-strength));
            -webkit-backdrop-filter: blur(var(--blur-strength));
            border: 1px solid var(--border-color);
            border-radius: 20px;
            box-shadow: 
                0 8px 32px var(--shadow-light),
                0 4px 16px var(--shadow-medium),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            padding: 0 24px;            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateY(0);
        }

        .dream-navbar.scrolled {
            top: 10px;
            left: 50%;
            right: auto;
            transform: translateX(-50%) translateY(0);
            width: 95%;
            max-width: 1200px;
            height: 60px;
            border-radius: 15px;
        }

        /* Logo section */
        .navbar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .navbar-logo:hover .logo-icon {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .navbar-logo:hover .logo-icon::before {
            opacity: 1;
            animation: shine 0.6s ease-in-out;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        /* Navigation links */
        .navbar-nav {
            display: flex;
            list-style: none;
            gap: 8px;
            margin: 0;
            padding: 0;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 12px;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .nav-link:hover {
            color: var(--text-primary);
            background: var(--bg-secondary);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--shadow-light);
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link span {
            position: relative;
            z-index: 1;
        }

        .nav-link.active {
            color: var(--primary-color);
            background: var(--bg-secondary);
            box-shadow: 0 2px 8px var(--shadow-light);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--primary-color);
            border-radius: 1px;
        }

        /* Right section */
        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* Connection status */
        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: var(--bg-secondary);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .connection-status:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--shadow-light);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
            position: relative;
        }

        .status-dot.disconnected {
            background: var(--error-color);
            animation: none;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(78, 205, 196, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(78, 205, 196, 0); }
            100% { box-shadow: 0 0 0 0 rgba(78, 205, 196, 0); }
        }

        /* Notification bell */
        .notification-bell {
            position: relative;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--text-secondary);
        }

        .notification-bell:hover {
            color: var(--primary-color);
            background: var(--bg-primary);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-light);
        }

        .notification-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            background: var(--error-color);
            color: white;
            border-radius: 10px;
            min-width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            animation: bounce 1s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Theme toggle */
        .theme-toggle {
            width: 50px;
            height: 26px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 13px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            outline: none;
        }

        .theme-toggle:hover {
            box-shadow: 0 4px 12px var(--shadow-light);
        }

        .theme-toggle-thumb {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
        }

        [data-theme="dark"] .theme-toggle-thumb {
            transform: translateX(24px);
        }

        /* Language selector */
        .language-selector {
            position: relative;
        }

        .language-toggle {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--text-secondary);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .language-toggle:hover {
            color: var(--primary-color);
            background: var(--bg-primary);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--shadow-light);
        }

        /* User menu */
        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .user-avatar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .user-avatar:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .user-avatar:hover::before {
            transform: translateX(100%);
        }

        /* Dropdown menus */
        .dropdown-menu {
            position: absolute;
            top: calc(100% + 12px);
            right: 0;
            background: var(--bg-primary);
            backdrop-filter: blur(var(--blur-strength));
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 8px 0;
            min-width: 200px;
            box-shadow: 0 12px 40px var(--shadow-medium);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .dropdown-item:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        /* Mobile menu */
        .mobile-menu-toggle {
            display: none;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            width: 44px;
            height: 44px;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background: var(--bg-primary);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--shadow-light);
        }

        .hamburger {
            width: 20px;
            height: 2px;
            background: var(--text-secondary);
            position: relative;
            transition: all 0.3s ease;
        }

        .hamburger::before,
        .hamburger::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 2px;
            background: var(--text-secondary);
            transition: all 0.3s ease;
        }

        .hamburger::before { top: -6px; }
        .hamburger::after { top: 6px; }

        .mobile-menu-toggle.active .hamburger {
            background: transparent;
        }

        .mobile-menu-toggle.active .hamburger::before {
            transform: rotate(45deg);
            top: 0;
        }

        .mobile-menu-toggle.active .hamburger::after {
            transform: rotate(-45deg);
            top: 0;
        }

        .mobile-nav {
            position: fixed;
            top: 110px;
            left: 20px;
            right: 20px;
            background: var(--bg-primary);
            backdrop-filter: blur(var(--blur-strength));
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 20px;
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-20px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 12px 40px var(--shadow-medium);
        }

        .mobile-nav.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .mobile-nav-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.2s ease;
            margin-bottom: 4px;
        }

        .mobile-nav-link:hover,
        .mobile-nav-link.active {
            background: var(--bg-secondary);
            color: var(--primary-color);
            transform: translateX(8px);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .navbar-nav { display: none; }
            .mobile-menu-toggle { display: flex; }
            
            .navbar-actions {
                gap: 8px;
            }
            
            .connection-status span {
                display: none;
            }
            
            .language-toggle {
                padding: 8px;
            }
        }

        @media (max-width: 480px) {
            .dream-navbar {
                left: 10px;
                right: 10px;
                padding: 0 16px;
                height: 60px;
            }
            
            .navbar-logo span {
                display: none;
            }
        }

        /* Demo content styling */
        .demo-content {
            margin-top: 120px;
            padding: 40px 20px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .demo-section {
            background: var(--bg-primary);
            backdrop-filter: blur(var(--blur-strength));
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px var(--shadow-light);
        }

        .demo-section h2 {
            color: var(--text-primary);
            margin-bottom: 20px;
            font-size: 2rem;
            font-weight: 700;
        }

        .demo-section p {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px var(--shadow-medium);
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="bg-particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <nav class="dream-navbar" id="dreamNavbar">
        <!-- Logo -->
        <a href="#" class="navbar-logo">
            <div class="logo-icon">M</div>
            <span>MUNygo</span>
        </a>

        <!-- Desktop Navigation -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a href="#" class="nav-link active">
                    <i class="fas fa-search"></i>
                    <span>Browse</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-list"></i>
                    <span>My Offers</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-plus"></i>
                    <span>Create</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-comments"></i>
                    <span>Chat</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
            </li>
        </ul>

        <!-- Actions -->
        <div class="navbar-actions">
            <!-- Connection Status -->
            <div class="connection-status" onclick="toggleConnection()">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Connected</span>
            </div>

            <!-- Notifications -->
            <div class="notification-bell" onclick="toggleNotifications()">
                <i class="fas fa-bell"></i>
                <div class="notification-badge" id="notificationBadge">3</div>
            </div>

            <!-- Theme Toggle -->
            <button class="theme-toggle" onclick="toggleTheme()">
                <div class="theme-toggle-thumb">
                    <i class="fas fa-sun" id="themeIcon"></i>
                </div>
            </button>

            <!-- Language -->
            <div class="language-selector">
                <button class="language-toggle" onclick="toggleLanguage()">
                    <span id="languageText">EN</span>
                    <i class="fas fa-globe"></i>
                </button>
            </div>

            <!-- User Menu -->
            <div class="user-menu">
                <div class="user-avatar" onclick="toggleUserMenu()">
                    JD
                </div>
                <div class="dropdown-menu" id="userDropdown">
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-user"></i>
                        <span>Profile</span>
                    </a>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobileToggle" onclick="toggleMobileMenu()">
                <div class="hamburger"></div>
            </button>
        </div>
    </nav>

    <!-- Mobile Navigation -->
    <div class="mobile-nav" id="mobileNav">
        <a href="#" class="mobile-nav-link active">
            <i class="fas fa-search"></i>
            <span>Browse Offers</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-list"></i>
            <span>My Offers</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-plus"></i>
            <span>Create Offer</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-comments"></i>
            <span>Chat</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </div>

    <!-- Demo Content -->
    <div class="demo-content">
        <div class="demo-section">
            <h2>🎨 DreamNavBar Features</h2>
            <p>This is a completely custom navigation bar with stunning visual effects and smooth animations. It showcases what's possible when you have complete creative freedom without UI library constraints.</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3>Glassmorphism</h3>
                    <p>Beautiful backdrop blur effects with translucent glass-like appearance</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Dynamic Gradients</h3>
                    <p>Smooth color transitions and animated gradient backgrounds</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3>Smooth Animations</h3>
                    <p>Buttery-smooth micro-interactions and hover effects</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile Perfection</h3>
                    <p>Responsive design with beautiful mobile experience</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🛠️ Interactive Elements</h2>
            <p>Try interacting with the navbar elements above:</p>
            <ul style="padding-left: 20px; color: var(--text-secondary);">
                <li>Click the theme toggle to switch between light and dark modes</li>
                <li>Hover over navigation links to see smooth animations</li>
                <li>Click the connection status to simulate reconnection</li>
                <li>Try the notification bell and user menu dropdowns</li>
                <li>Resize the window to see responsive mobile menu</li>
                <li>Scroll down to see the navbar transform and center</li>
            </ul>
        </div>
    </div>

    <script>
        // Theme management
        let currentTheme = 'light';
        
        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);
            
            const themeIcon = document.getElementById('themeIcon');
            themeIcon.className = currentTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';
            
            // Add some visual feedback
            const toggle = document.querySelector('.theme-toggle');
            toggle.style.transform = 'scale(0.9)';
            setTimeout(() => {
                toggle.style.transform = 'scale(1)';
            }, 150);
        }

        // Language management
        let currentLanguage = 'en';
        
        function toggleLanguage() {
            currentLanguage = currentLanguage === 'en' ? 'fa' : 'en';
            document.getElementById('languageText').textContent = currentLanguage.toUpperCase();
            
            // Add visual feedback
            const langBtn = document.querySelector('.language-toggle');
            langBtn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                langBtn.style.transform = 'scale(1)';
            }, 150);
        }

        // Connection status
        let isConnected = true;
        
        function toggleConnection() {
            isConnected = !isConnected;
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (isConnected) {
                statusDot.className = 'status-dot';
                statusText.textContent = 'Connected';
            } else {
                statusDot.className = 'status-dot disconnected';
                statusText.textContent = 'Disconnected';
            }
            
            // Simulate reconnection after 2 seconds if disconnected
            if (!isConnected) {
                setTimeout(() => {
                    isConnected = true;
                    statusDot.className = 'status-dot';
                    statusText.textContent = 'Connected';
                }, 2000);
            }
        }

        // User menu
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }

        // Mobile menu
        function toggleMobileMenu() {
            const mobileNav = document.getElementById('mobileNav');
            const mobileToggle = document.getElementById('mobileToggle');
            
            mobileNav.classList.toggle('show');
            mobileToggle.classList.toggle('active');
        }

        // Notifications
        function toggleNotifications() {
            const badge = document.getElementById('notificationBadge');
            const bell = document.querySelector('.notification-bell');
            
            // Animate the bell
            bell.style.transform = 'scale(0.9)';
            setTimeout(() => {
                bell.style.transform = 'scale(1)';
            }, 150);
            
            // Update notification count
            let count = parseInt(badge.textContent);
            count = count > 0 ? 0 : 3;
            badge.textContent = count;
            badge.style.display = count > 0 ? 'flex' : 'none';
        }

        // Scroll behavior
        function handleScroll() {
            const navbar = document.getElementById('dreamNavbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }

        // Close dropdowns when clicking outside
        function closeDropdowns(e) {
            if (!e.target.closest('.user-menu')) {
                document.getElementById('userDropdown').classList.remove('show');
            }
            if (!e.target.closest('.mobile-menu-toggle') && !e.target.closest('.mobile-nav')) {
                document.getElementById('mobileNav').classList.remove('show');
                document.getElementById('mobileToggle').classList.remove('active');
            }
        }

        // Navigation link interaction
        function setupNavigation() {
            const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));
                    
                    // Add active class to clicked link
                    this.classList.add('active');
                    
                    // Close mobile menu if open
                    document.getElementById('mobileNav').classList.remove('show');
                    document.getElementById('mobileToggle').classList.remove('active');
                });
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Setup event listeners
            window.addEventListener('scroll', handleScroll);
            document.addEventListener('click', closeDropdowns);
            setupNavigation();
            
            // Simulate dynamic connection status changes
            setTimeout(() => {
                console.log('🎉 DreamNavBar loaded with stunning visual effects!');
            }, 1000);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + D to toggle theme
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                toggleTheme();
            }
            
            // Ctrl/Cmd + L to toggle language
            if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
                e.preventDefault();
                toggleLanguage();
            }
        });

        // Add some extra sparkle effects
        function createSparkle() {
            const sparkle = document.createElement('div');
            sparkle.style.position = 'fixed';
            sparkle.style.width = '4px';
            sparkle.style.height = '4px';
            sparkle.style.background = 'var(--accent-color)';
            sparkle.style.borderRadius = '50%';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.zIndex = '999';
            sparkle.style.opacity = '0.8';
            
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * window.innerHeight;
            
            sparkle.style.left = x + 'px';
            sparkle.style.top = y + 'px';
            
            document.body.appendChild(sparkle);
            
            // Animate sparkle
            sparkle.animate([
                { transform: 'scale(0) rotate(0deg)', opacity: 0 },
                { transform: 'scale(1) rotate(180deg)', opacity: 1 },
                { transform: 'scale(0) rotate(360deg)', opacity: 0 }
            ], {
                duration: 1000,
                easing: 'ease-out'
            }).onfinish = () => sparkle.remove();
        }

        // Create occasional sparkles
        setInterval(createSparkle, 3000);
    </script>
</body>
</html>
