<template>
  <nav class="dream-navbar" :class="{ scrolled: isScrolled, rtl: languageStore.currentLanguage === 'fa' }" ref="navbar">
    <!-- Logo -->
    <router-link to="/" class="navbar-logo">
      <div class="logo-icon">M</div>
      <span>{{ $t('navigation.appName') }}</span>
    </router-link>    <!-- Desktop Navigation -->
    <ul class="navbar-nav">
      <li class="nav-item">
        <router-link to="/browse-offers" class="nav-link" active-class="active">
          <i class="fas fa-search"></i>
          <span>{{ $t('navigation.browse') }}</span>
        </router-link>
      </li>      <li class="nav-item">
        <router-link to="/my-offers" class="nav-link" active-class="active">
          <i class="fas fa-list"></i>
          <span>{{ $t('navigation.myOffers') }}</span>
        </router-link>
      </li>
      <li class="nav-item">
        <router-link to="/matches" class="nav-link" active-class="active">
          <i class="fas fa-heart"></i>
          <span>{{ $t('navigation.matches') }}</span>
          <div 
            v-if="matchStore.matchesNeedingResponse.length > 0" 
            class="nav-badge"
          >
            {{ matchStore.matchesNeedingResponse.length }}
          </div>
        </router-link>
      </li>
      <li class="nav-item">
        <router-link to="/create-offer" class="nav-link" active-class="active">
          <i class="fas fa-plus"></i>
          <span>{{ $t('navigation.createOffer') }}</span>
        </router-link>
      </li>
      <li class="nav-item">
        <router-link to="/profile" class="nav-link" active-class="active">
          <i class="fas fa-user"></i>
          <span>{{ $t('navigation.profile') }}</span>
        </router-link>
      </li>
    </ul>    <!-- Actions -->
    <div class="navbar-actions">
      <!-- Connection Status -->
      <n-tooltip 
        trigger="hover" 
        :placement="languageStore.isRTL ? 'bottom-end' : 'bottom-start'"
        :delay="200"
      >
        <template #trigger>
          <div 
            class="connection-status" 
            :class="connectionStatusClass"
            @click="handleConnectionClick"
          >
            <i :class="connectionIconClass"></i>
          </div>
        </template>
        <div class="connection-tooltip">
          <div class="tooltip-title">{{ connectionTooltipTitle }}</div>
          <div class="tooltip-details">
            <div>{{ $t('connection.status') }}: {{ connectionStatusText }}</div>
            <div v-if="connectionStore.transportType">
              {{ $t('connection.transport') }}: {{ connectionStore.transportType }}
            </div>
            <div v-if="connectionStore.lastDisconnectReason && !connectionStore.isConnected">
              {{ $t('connection.reason') }}: {{ connectionStore.lastDisconnectReason }}
            </div>
          </div>
          <n-button
            v-if="!connectionStore.isConnected"
            @click="attemptReconnect"
            text
            type="primary"
            size="tiny"
            style="margin-top: 8px;"
          >
            {{ $t('connection.retryConnection') }}
          </n-button>
        </div>
      </n-tooltip><!-- Notifications -->
      <div class="notification-container">
        <div 
          ref="notificationBellRef"
          class="notification-bell" 
          :class="{ animating: isNotificationBellAnimating }"
          @click="toggleNotifications"
        >
          <i class="fas fa-bell"></i>
          <div 
            class="notification-badge" 
            v-if="notificationStore.unreadNotificationsCount > 0"
            :key="notificationStore.unreadNotificationsCount"
          >
            {{ notificationStore.unreadNotificationsCount }}
          </div>
        </div>
        
        <!-- Notification Dropdown -->
        <div class="notification-dropdown" :class="{ show: showNotifications }" v-if="showNotifications">
          <div class="notification-header">
            <h3>{{ $t('notifications.title') }}</h3>
            <button 
              v-if="notificationStore.unreadNotificationsCount > 0" 
              @click="markAllAsRead" 
              class="mark-all-read-btn"
            >
              {{ $t('notifications.markAllAsRead') }}
            </button>
          </div>
          
          <div class="notification-list">
            <div 
              v-if="notificationStore.notifications.length === 0" 
              class="no-notifications"
            >
              <i class="fas fa-bell-slash"></i>
              <span>{{ $t('notifications.noNotifications') }}</span>
            </div>
              <div 
              v-for="notification in notificationStore.notifications.slice(0, 5)" 
              :key="notification.id"
              class="notification-item"
              :class="{ unread: !notification.isRead }"
              @click="handleNotificationClick(notification)"
            >
              <div class="notification-icon">
                <i :class="getNotificationIcon(notification.type)"></i>
              </div>              <div class="notification-content">
                <div class="notification-title">{{ getNotificationTitle(notification) }}</div>
                <div class="notification-message">{{ notification.message }}</div>
                <div class="notification-time">{{ formatNotificationTime(notification.createdAt) }}</div>
              </div>
            </div>
            
            <div v-if="notificationStore.notifications.length > 5" class="view-all-notifications">
              <router-link to="/notifications" @click="closeNotifications">
                {{ $t('notifications.viewAll') }}
              </router-link>
            </div>
          </div>
        </div>      </div>

      <!-- Debug Report Button (Development/Testing) -->
      <DebugReportButtonEnhanced />      <!-- Theme Toggle -->
      <button 
        ref="themeToggleRef"
        class="theme-toggle" 
        :class="{ animating: isThemeToggleAnimating }"
        @click="toggleTheme" 
        :aria-label="$t('navigation.toggleTheme')"
      >
        <div class="theme-toggle-thumb">
          <i :class="themeStore.isDark ? 'fas fa-moon' : 'fas fa-sun'"></i>
        </div>
      </button>      <!-- Language -->
      <div class="language-selector">
        <button 
          ref="languageToggleRef"
          class="language-toggle" 
          :class="{ animating: isLanguageToggleAnimating }"
          @click="toggleLanguage" 
          :aria-label="$t('navigation.toggleLanguage')"
        >
          <span>{{ languageStore.currentLanguage === 'fa' ? 'EN' : 'فا' }}</span>
          <i class="fas fa-globe"></i>
        </button>
      </div>

      <!-- User Menu -->
      <div class="user-menu" v-if="authStore.isAuthenticated">
        <div class="user-avatar" @click="toggleUserMenu">
          {{ userInitials }}
        </div>        <div class="dropdown-menu" :class="{ show: showUserMenu }">
          <router-link to="/profile" class="dropdown-item" @click="closeUserMenu">
            <i class="fas fa-user"></i>
            <span>{{ $t('navigation.profile') }}</span>
          </router-link>
          <router-link 
            v-if="isUserAdmin" 
            to="/admin/debug-dashboard" 
            class="dropdown-item admin-item" 
            @click="closeUserMenu"
          >
            <i class="fas fa-chart-line"></i>
            <span>{{ $t('navigation.adminDashboard') }}</span>
          </router-link>
          <a href="#" class="dropdown-item" @click="closeUserMenu">
            <i class="fas fa-cog"></i>
            <span>{{ $t('navigation.settings') }}</span>
          </a>
          <a href="#" class="dropdown-item" @click="handleLogout">
            <i class="fas fa-sign-out-alt"></i>
            <span>{{ $t('navigation.logout') }}</span>
          </a>
        </div>
      </div>

      <!-- Login/Register for unauthenticated users -->
      <div v-else class="auth-actions">
        <router-link to="/login" class="auth-link">
          {{ $t('navigation.login') }}
        </router-link>
        <router-link to="/register" class="auth-link primary">
          {{ $t('navigation.register') }}
        </router-link>
      </div>

      <!-- Mobile Menu Toggle -->
      <button 
        class="mobile-menu-toggle" 
        :class="{ active: showMobileMenu }"
        @click="toggleMobileMenu"
        :aria-label="$t('navigation.toggleMobileMenu')"
      >
        <div class="hamburger"></div>
      </button>
    </div>
  </nav>
  <!-- Mobile Navigation -->
  <div class="mobile-nav" :class="{ show: showMobileMenu }">
    <router-link to="/browse-offers" class="mobile-nav-link" @click="closeMobileMenu">
      <i class="fas fa-search"></i>
      <span>{{ $t('navigation.browse') }}</span>
<router-link to="/my-offers" class="mobile-nav-link" @click="closeMobileMenu">
       <i class="fas fa-list"></i>
       <span>{{ $t('navigation.myOffers') }}</span>
     </router-link>
    <router-link to="/matches" class="mobile-nav-link" @click="closeMobileMenu">
      <i class="fas fa-heart"></i>
      <span>{{ $t('navigation.matches') }}</span>
      <div 
        v-if="matchStore.matchesNeedingResponse.length > 0" 
        class="nav-badge"
      >
        {{ matchStore.matchesNeedingResponse.length }}
      </div>
    </router-link>
     <router-link to="/create-offer" class="mobile-nav-link" @click="closeMobileMenu">
       <i class="fas fa-plus"></i>
       <span>{{ $t('navigation.createOffer') }}</span>
     </router-link>
    </router-link>    <router-link to="/profile" class="mobile-nav-link" @click="closeMobileMenu">
      <i class="fas fa-user"></i>
      <span>{{ $t('navigation.profile') }}</span>
    </router-link>
    <router-link 
      v-if="isUserAdmin" 
      to="/admin/debug-dashboard" 
      class="mobile-nav-link admin-item" 
      @click="closeMobileMenu"
    >
      <i class="fas fa-chart-line"></i>
      <span>{{ $t('navigation.adminDashboard') }}</span>
    </router-link>
    
    <!-- Debug Report Button (Mobile) -->
    <div class="mobile-debug-section">
      <DebugReportButtonEnhanced />
    </div>
    
    <!-- Mobile auth actions -->
    <div v-if="!authStore.isAuthenticated" class="mobile-auth-section">
      <router-link to="/login" class="mobile-nav-link" @click="closeMobileMenu">
        <i class="fas fa-sign-in-alt"></i>
        <span>{{ $t('navigation.login') }}</span>
      </router-link>
      <router-link to="/register" class="mobile-nav-link" @click="closeMobileMenu">
        <i class="fas fa-user-plus"></i>
        <span>{{ $t('navigation.register') }}</span>
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { NTooltip, NButton } from 'naive-ui'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'
import { useLanguageStore } from '@/stores/language'
import { useConnectionStore } from '@/stores/connection'
import { useNotificationStore, FrontendNotificationType, type FrontendNotification } from '@/stores/notificationStore'
import { useMatchStore } from '@/stores/matchStore'
import DebugReportButtonEnhanced from '@/components/DebugReportButtonEnhanced.vue'
import { isAdmin } from '@/utils/adminUtils'
import centralizedSocketManager from '@/services/centralizedSocketManager'
import { useTranslation } from '@/composables/useTranslation'

// Stores
const router = useRouter()
const authStore = useAuthStore()
const themeStore = useThemeStore()
const languageStore = useLanguageStore()
const connectionStore = useConnectionStore()
const notificationStore = useNotificationStore()
const matchStore = useMatchStore()
const { t } = useTranslation()

// Reactive state
const isScrolled = ref(false)
const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const showNotifications = ref(false)
const navbar = ref<HTMLElement>()

// Animation refs and states
const themeToggleRef = ref<HTMLElement>()
const languageToggleRef = ref<HTMLElement>()
const notificationBellRef = ref<HTMLElement>()
const isThemeToggleAnimating = ref(false)
const isLanguageToggleAnimating = ref(false)
const isNotificationBellAnimating = ref(false)

// Computed properties
const userInitials = computed(() => {
  // Use username if available, otherwise use email
  if (authStore.user?.username) {
    return authStore.user.username.substring(0, 2).toUpperCase()
  }
  return authStore.user?.email?.substring(0, 2).toUpperCase() || 'U'
})

const isUserAdmin = computed(() => {
  return isAdmin(authStore.user?.email)
})

// Connection status computed properties
const connectionStatusClass = computed(() => {
  if (!connectionStore.isConnected) return 'connection-error'
  
  switch (connectionStore.connectionQuality) {
    case 'excellent':
      return 'connection-excellent'
    case 'good':
      return 'connection-good'
    case 'poor':
      return 'connection-poor'
    default:
      return 'connection-good'
  }
})

const connectionIconClass = computed(() => {
  const baseClass = 'fas'
  const pulseClass = (!connectionStore.isConnected || connectionStore.connectionQuality === 'poor') ? 'connection-pulse' : ''
  
  if (!connectionStore.isConnected) {
    return `${baseClass} fa-wifi connection-disconnected ${pulseClass}`
  }
  
  switch (connectionStore.connectionQuality) {
    case 'excellent':
      return `${baseClass} fa-wifi`
    case 'good':
      return `${baseClass} fa-wifi`
    case 'poor':
      return `${baseClass} fa-exclamation-triangle ${pulseClass}`
    default:
      return `${baseClass} fa-wifi`
  }
})

const connectionTooltipTitle = computed(() => {
  return connectionStore.isConnected ? 'Connected' : 'Disconnected'
})

const connectionStatusText = computed(() => {
  if (!connectionStore.isConnected) return 'Disconnected'
  
  switch (connectionStore.connectionQuality) {
    case 'excellent':
      return 'Excellent'
    case 'good':
      return 'Good'
    case 'poor':
      return 'Poor'
    default:
      return 'Connected'
  }
})



// Methods
const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

const toggleTheme = () => {
  themeStore.toggleTheme()
  
  // Add visual feedback using reactive state
  isThemeToggleAnimating.value = true
  setTimeout(() => {
    isThemeToggleAnimating.value = false
  }, 150)
}

const toggleLanguage = () => {
  languageStore.setLanguage(languageStore.currentLanguage === 'en' ? 'fa' : 'en')
  
  // Add visual feedback using reactive state
  isLanguageToggleAnimating.value = true
  setTimeout(() => {    isLanguageToggleAnimating.value = false
  }, 150)
}

const toggleNotifications = () => {
  // Close user menu if open
  if (showUserMenu.value) {
    showUserMenu.value = false
  }
  
  // Toggle dropdown visibility
  showNotifications.value = !showNotifications.value
  
  // Add visual feedback using reactive state
  isNotificationBellAnimating.value = true
  setTimeout(() => {
    isNotificationBellAnimating.value = false
  }, 150)
}

const closeNotifications = () => {
  showNotifications.value = false
}

const markAllAsRead = async () => {
  try {
    await notificationStore.markAllNotificationsAsRead()
  } catch (error) {
    console.error('Failed to mark all notifications as read:', error)
  }
}

const handleNotificationClick = async (notification: FrontendNotification) => {
  try {
    console.log('[DreamNavBar] Notification clicked:', notification.type, notification.id);
    
    // Close the notification dropdown first
    closeNotifications()
    
    // Mark as read if not already read
    if (!notification.isRead) {
      await notificationStore.markNotificationAsRead(notification.id)
    }    let targetRoute: string | null = null;
    
    // Special case: Handle NEW_INTEREST_ON_YOUR_OFFER notifications
    if (notification.type === FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER) {
      // Always navigate to /my-offers for interest notifications, where users can accept/decline interests
      console.log('[DreamNavBar] Interest notification - navigating to my-offers for interest management');
      targetRoute = '/my-offers';
    }
    
    // Primary routing: Use relatedEntityType and relatedEntityId (only if not handled above)
    if (!targetRoute && notification.relatedEntityType && notification.relatedEntityId) {
      switch (notification.relatedEntityType) {
        case 'OFFER':
          targetRoute = `/offers/${notification.relatedEntityId}`;
          break;
        case 'CHAT_SESSION':
        case 'CHAT':
          targetRoute = `/chat/${notification.relatedEntityId}`;
          break;
        case 'TRANSACTION':
          targetRoute = `/transactions/${notification.relatedEntityId}`;
          break;
        case 'MATCH':
          targetRoute = `/matches`;
          break;
        default:
          console.warn('[DreamNavBar] Unknown entity type:', notification.relatedEntityType);
      }
    }
      // Fallback routing: Use data fields for legacy notifications
    if (!targetRoute && notification.data) {
      if (notification.data.offerId) {
        targetRoute = `/offers/${notification.data.offerId}`;
      } else if (notification.data.chatSessionId) {
        targetRoute = `/chat/${notification.data.chatSessionId}`;
      } else if (notification.data.transactionId) {
        targetRoute = `/transactions/${notification.data.transactionId}`;
      } else if (notification.data.matchId) {
        targetRoute = `/matches`;
      }
    }
      // Type-based fallback routing
    if (!targetRoute) {
      switch (notification.type) {
        case FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER:
          targetRoute = '/my-offers';
          break;
        case FrontendNotificationType.YOUR_INTEREST_ACCEPTED:
        case FrontendNotificationType.YOUR_INTEREST_DECLINED:
        case FrontendNotificationType.CHAT_MESSAGE_RECEIVED:
        case FrontendNotificationType.OFFER_STATUS_UPDATED_BY_OWNER:
        case FrontendNotificationType.OFFER_STATUS_CHANGED:
        case FrontendNotificationType.TRANSACTION_STATUS_CHANGED:
          targetRoute = '/home';
          break;
        case FrontendNotificationType.MATCH_FOUND:
        case FrontendNotificationType.MATCH_ACCEPTED:
        case FrontendNotificationType.MATCH_DECLINED:
        case FrontendNotificationType.MATCH_EXPIRED:
        case FrontendNotificationType.MATCH_CONVERTED:
        case FrontendNotificationType.MATCH_ACCEPTED_BY_OTHER:
        case FrontendNotificationType.MATCH_DECLINED_BY_OTHER:
        case FrontendNotificationType.MATCH_CONVERTED_TO_CHAT:
          targetRoute = '/matches';
          break;
        default:
          console.warn('[DreamNavBar] Unknown notification type for fallback:', notification.type);
          targetRoute = '/home';
      }
    }
    
    // Navigate to the target route
    if (targetRoute) {
      console.log('[DreamNavBar] Navigating to:', targetRoute);
      await router.push(targetRoute);
    } else {
      console.error('[DreamNavBar] Could not determine target route for notification:', notification);
      // You might want to show a toast notification here
    }
    
  } catch (error) {
    console.error('[DreamNavBar] Error handling notification click:', error);
    // You might want to show an error toast here
  }
}

const getNotificationIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'INTEREST_RECEIVED': 'fas fa-heart',
    'INTEREST_ACCEPTED': 'fas fa-check-circle',
    'INTEREST_DECLINED': 'fas fa-times-circle',
    'TRANSACTION_CREATED': 'fas fa-handshake',
    'TRANSACTION_UPDATED': 'fas fa-sync',
    'PAYMENT_DECLARED': 'fas fa-credit-card',
    'PAYMENT_CONFIRMED': 'fas fa-check-double',
    'SYSTEM': 'fas fa-info-circle',
    default: 'fas fa-bell'
  }
  return iconMap[type] || iconMap.default
}

const formatNotificationTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
  return `${Math.floor(diffInMinutes / 1440)}d ago`
}

const getNotificationTitle = (notification: FrontendNotification): string => {
  switch (notification.type) {
    case FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER:
      return t('notifications.newInterestOnOffer');
    case FrontendNotificationType.YOUR_INTEREST_ACCEPTED:
      return t('notifications.yourInterestAccepted');
    case FrontendNotificationType.YOUR_INTEREST_DECLINED:
      return t('notifications.yourInterestDeclined');
    case FrontendNotificationType.OFFER_STATUS_CHANGED:
      return t('notifications.offerStatusUpdated');
    case FrontendNotificationType.OFFER_STATUS_UPDATED_BY_OWNER:
      return t('notifications.yourOfferStatusChanged');
    default:
      return t('notifications.newNotification');
  }
}

// Connection handling methods
const handleConnectionClick = () => {
  // Optional: You can add connection debugging info here
  console.log('Connection status clicked:', {
    connected: connectionStore.isConnected,
    quality: connectionStore.connectionQuality,
    transport: connectionStore.transportType
  })
}

const attemptReconnect = async () => {
  try {
    console.log('[DreamNavBar] Manual reconnect attempt...')
    await centralizedSocketManager.forceReconnect()
    console.log('[DreamNavBar] Reconnection successful')
  } catch (error) {
    console.error('[DreamNavBar] Failed to reconnect:', error)
  }
}

const toggleUserMenu = () => {
  // Close notifications if open
  if (showNotifications.value) {
    showNotifications.value = false
  }
  
  showUserMenu.value = !showUserMenu.value
}

const closeUserMenu = () => {
  showUserMenu.value = false
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

const handleLogout = async () => {
  closeUserMenu()
  await authStore.logout()
  router.push('/')
}

const closeDropdowns = (e: Event) => {
  const target = e.target
  
  // Guard clause: ensure target is an Element
  if (!target || !(target instanceof Element)) {
    return
  }
  
  if (!target.closest('.user-menu')) {
    showUserMenu.value = false
  }
  if (!target.closest('.mobile-menu-toggle') && !target.closest('.mobile-nav')) {
    showMobileMenu.value = false
  }
  // Close notifications dropdown if click is outside notification area
  if (!target.closest('.notification-bell') && !target.closest('.notification-dropdown')) {
    showNotifications.value = false
  }
}

// Lifecycle
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  document.addEventListener('click', closeDropdowns)
  
  // Apply initial theme
  if (themeStore.isDark) {
    document.documentElement.setAttribute('data-theme', 'dark')
  }
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  document.removeEventListener('click', closeDropdowns)
})

// Watch theme changes
import { watch } from 'vue'
watch(() => themeStore.isDark, (isDark) => {
  if (isDark) {
    document.documentElement.setAttribute('data-theme', 'dark')
  } else {
    document.documentElement.removeAttribute('data-theme')
  }
})
</script>

<style scoped>
/* CSS Variables */
:root {
  /* Light theme */
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #4ecdc4;
  --warning-color: #ffe066;
  --error-color: #ff6b6b;
  --text-primary: #2d3748;
  --text-secondary: #718096;
  --bg-primary: rgba(255, 255, 255, 0.95);
  --bg-secondary: rgba(247, 250, 252, 0.8);
  --border-color: rgba(226, 232, 240, 0.8);
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --blur-strength: 20px;
}

[data-theme="dark"] {
  --primary-color: #9f7aea;
  --secondary-color: #667eea;
  --accent-color: #ed64a6;
  --success-color: #38b2ac;
  --warning-color: #ecc94b;
  --error-color: #f56565;
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --bg-primary: rgba(26, 32, 44, 0.95);
  --bg-secondary: rgba(45, 55, 72, 0.8);
  --border-color: rgba(74, 85, 104, 0.8);
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-medium: rgba(0, 0, 0, 0.4);
}

/* Main navbar container */
.dream-navbar {
  position: fixed;
  top: 20px;
  left: 20px;
  right: 20px;
  z-index: 1000;
  background: var(--bg-primary);
  backdrop-filter: blur(var(--blur-strength));
  -webkit-backdrop-filter: blur(var(--blur-strength));
  border: 1px solid var(--border-color);
  border-radius: 20px;
  box-shadow: 
    0 8px 32px var(--shadow-light),
    0 4px 16px var(--shadow-medium),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  padding: 0 24px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.dream-navbar.scrolled {
  top: 10px;
  left: 50%;
  right: auto;
  transform: translateX(-50%) translateY(0);
  width: 95%;
  max-width: 1200px;
  height: 60px;
  border-radius: 15px;
}

/* Logo section */
.navbar-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.3s ease;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.navbar-logo:hover .logo-icon {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.navbar-logo:hover .logo-icon::before {
  opacity: 1;
  animation: shine 0.6s ease-in-out;
}

@keyframes shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Navigation links */
.navbar-nav {
  display: flex;
  list-style: none;
  gap: 8px;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.nav-link:hover {
  color: var(--text-primary);
  background: var(--bg-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-light);
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link span {
  position: relative;
  z-index: 1;
}

.nav-link.active {
  color: var(--primary-color);
  background: var(--bg-secondary);
  box-shadow: 0 2px 8px var(--shadow-light);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

/* Nav badge for notifications */
.nav-badge {
  position: absolute;
  top: -2px;
  right: -6px;
  background: var(--warning-color, #f0a020);
  color: white;
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

/* Right section */
.navbar-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Connection status */
.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: var(--bg-secondary);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  position: relative;
}

.connection-status:hover {
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px var(--shadow-light);
}

/* Connection status colors based on quality */
.connection-excellent {
  color: var(--success-color);
  border-color: rgba(78, 205, 196, 0.3);
}

.connection-excellent:hover {
  background: rgba(78, 205, 196, 0.1);
}

.connection-good {
  color: var(--primary-color);
  border-color: rgba(255, 193, 7, 0.3);
}

.connection-good:hover {
  background: rgba(255, 193, 7, 0.1);
}

.connection-poor {
  color: var(--warning-color);
  border-color: rgba(255, 193, 7, 0.5);
}

.connection-poor:hover {
  background: rgba(255, 193, 7, 0.15);
}

.connection-error {
  color: var(--error-color);
  border-color: rgba(220, 53, 69, 0.5);
}

.connection-error:hover {
  background: rgba(220, 53, 69, 0.1);
}

/* Connection icon animations */
.connection-pulse {
  animation: connectionPulse 2s infinite;
}

.connection-disconnected {
  opacity: 0.6;
}

@keyframes connectionPulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Connection tooltip styling */
.connection-tooltip {
  padding: 12px;
  max-width: 250px;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
}

.tooltip-details {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.tooltip-details > div {
  margin-bottom: 4px;
}

.tooltip-details > div:last-child {
  margin-bottom: 0;
}

/* Notification container */
.notification-container {
  position: relative;
}

/* Notification bell */
.notification-bell {
  position: relative;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.notification-bell:hover {
  color: var(--primary-color);
  background: var(--bg-primary);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px var(--shadow-light);
}

.notification-bell.animating {
  transform: scale(0.9);
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--error-color);
  color: white;
  border-radius: 10px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  animation: bounce 1s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Notification dropdown */
.notification-dropdown {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  min-width: 380px;
  max-width: 420px;
  background: var(--bg-primary);
  backdrop-filter: blur(var(--blur-strength));
  -webkit-backdrop-filter: blur(var(--blur-strength));
  border: 1px solid var(--border-color);
  border-radius: 16px;
  box-shadow: 
    0 12px 40px var(--shadow-light),
    0 6px 20px var(--shadow-medium),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 500px;
  overflow: hidden;
}

.notification-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* RTL positioning for notification dropdown */
.dream-navbar.rtl .notification-dropdown {
  right: auto;
  left: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 16px;
  border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.mark-all-read-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-family: inherit;
}

/* Persian font optimization for notification elements */
.dream-navbar.rtl .mark-all-read-btn,
.dream-navbar.rtl .notification-header h3,
.dream-navbar.rtl .notification-item,
.dream-navbar.rtl .notification-content,
.dream-navbar.rtl .notification-title,
.dream-navbar.rtl .notification-message,
.dream-navbar.rtl .notification-time,
.dream-navbar.rtl .no-notifications {
  font-family: 'Vazirmatn', 'Vazir', 'Tahoma', 'Iran Sans', -apple-system, BlinkMacSystemFont, sans-serif;
}

.mark-all-read-btn:hover {
  background: var(--bg-secondary);
}

.notification-list {
  max-height: 350px;
  overflow-y: auto;
}

.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.no-notifications i {
  font-size: 2rem;
  margin-bottom: 12px;
  opacity: 0.5;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background: var(--bg-secondary);
}

.notification-item.unread {
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.05) 0%, transparent 100%);
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-color);
}

.notification-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  color: var(--primary-color);
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
  margin-bottom: 4px;
  line-height: 1.3;
}

.notification-message {
  color: var(--text-secondary);
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 6px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.notification-time {
  color: var(--text-secondary);
  font-size: 0.75rem;
  opacity: 0.8;
}

.view-all-notifications {
  padding: 16px 20px;
  text-align: center;
  border-top: 1px solid var(--border-color);
}

.view-all-notifications a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.view-all-notifications a:hover {
  text-decoration: underline;
}

/* Theme toggle */
.theme-toggle {
  width: 50px;
  height: 26px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 13px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  outline: none;
}

.theme-toggle:hover {
  box-shadow: 0 4px 12px var(--shadow-light);
}

.theme-toggle.animating {
  transform: scale(0.9);
}

.theme-toggle-thumb {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
}

[data-theme="dark"] .theme-toggle-thumb {
  transform: translateX(24px);
}

/* Language selector */
.language-selector {
  position: relative;
}

.language-toggle {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  font-family: 'Vazirmatn', 'Vazir', 'Tahoma', 'Iran Sans', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.language-toggle:hover {
  color: var(--primary-color);
  background: var(--bg-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-light);
}

.language-toggle.animating {
  transform: scale(0.9);
}

/* User menu */
.user-menu {
  position: relative;
}

.user-avatar {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.user-avatar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.user-avatar:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.user-avatar:hover::before {
  transform: translateX(100%);
}

/* Auth actions for unauthenticated users */
.auth-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.auth-link {
  padding: 8px 16px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.auth-link:hover {
  color: var(--text-primary);
  background: var(--bg-secondary);
  transform: translateY(-1px);
}

.auth-link.primary {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
}

.auth-link.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Dropdown menus */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 12px);
  right: 0;
  background: var(--bg-primary);
  backdrop-filter: blur(var(--blur-strength));
  border: 1px solid var(--border-color);
  border-radius: 15px;
  padding: 8px 0;
  min-width: 200px;
  box-shadow: 0 12px 40px var(--shadow-medium);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1001;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* RTL positioning for user dropdown */
.dream-navbar.rtl .dropdown-menu {
  right: auto;
  left: 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.dropdown-item:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* Admin item styling */
.dropdown-item.admin-item {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 185, 107, 0.1));
  border-left: 3px solid var(--accent-color);
  color: var(--accent-color);
  font-weight: 600;
}

.dropdown-item.admin-item:hover {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(255, 185, 107, 0.15));
  color: var(--accent-color);
  transform: translateX(2px);
}

.mobile-nav-link.admin-item {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 185, 107, 0.1));
  border-left: 3px solid var(--accent-color);
  color: var(--accent-color);
  font-weight: 600;
}

.mobile-nav-link.admin-item:hover {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(255, 185, 107, 0.15));
  color: var(--accent-color);
  transform: translateX(2px);
}

/* Mobile menu */
.mobile-menu-toggle {
  display: none;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  width: 44px;
  height: 44px;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
  background: var(--bg-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-light);
}

.hamburger {
  width: 20px;
  height: 2px;
  background: var(--text-secondary);
  position: relative;
  transition: all 0.3s ease;
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background: var(--text-secondary);
  transition: all 0.3s ease;
}

.hamburger::before { top: -6px; }
.hamburger::after { top: 6px; }

.mobile-menu-toggle.active .hamburger {
  background: transparent;
}

.mobile-menu-toggle.active .hamburger::before {
  transform: rotate(45deg);
  top: 0;
}

.mobile-menu-toggle.active .hamburger::after {
  transform: rotate(-45deg);
  top: 0;
}

.mobile-nav {
  position: fixed;
  top: 110px;
  left: 20px;
  right: 20px;
  background: var(--bg-primary);
  backdrop-filter: blur(var(--blur-strength));
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 20px;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 12px 40px var(--shadow-medium);
}

.mobile-nav.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.2s ease;
  margin-bottom: 4px;
}

.mobile-nav-link:hover,
.mobile-nav-link.router-link-active {
  background: var(--bg-secondary);
  color: var(--primary-color);
  transform: translateX(8px);
}

.mobile-auth-section {
  border-top: 1px solid var(--border-color);
  margin-top: 16px;
  padding-top: 16px;
}

.mobile-debug-section {
  padding: 12px 16px;
  margin: 8px 0;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .navbar-nav { display: none; }
  .mobile-menu-toggle { display: flex; }
  
  .navbar-actions {
    gap: 8px;
  }
  
  .connection-status {
    width: 32px;
    height: 32px;
  }
  
  .language-toggle {
    padding: 8px;
  }
}

@media (max-width: 480px) {
  .dream-navbar {
    left: 10px;
    right: 10px;
    padding: 0 16px;
    height: 60px;
  }
  
  .navbar-logo span {
    display: none;
  }
}
</style>
