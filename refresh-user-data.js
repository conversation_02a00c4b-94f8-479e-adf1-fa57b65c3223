// <PERSON>ript to refresh user data from backend without logging out
// Run this in your browser console

const authToken = localStorage.getItem('authToken');
if (!authToken) {
  console.log('No auth token found');
} else {
  fetch('http://localhost:3000/api/auth/me', {
    headers: {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('Fresh user data from backend:', data);
    
    // Update localStorage with fresh user data
    localStorage.setItem('userInfo', JSON.stringify(data));
    
    console.log('✅ User data refreshed in localStorage');
    console.log('Email verified:', data.isEmailVerified);
    console.log('Phone verified:', data.isPhoneVerified);
    console.log('Reputation:', data.reputationScore, 'Level:', data.reputationLevel);
    
    // Reload the page to update the frontend state
    console.log('Reloading page to update frontend state...');
    window.location.reload();
  })
  .catch(error => {
    console.error('Failed to refresh user data:', error);
  });
}
