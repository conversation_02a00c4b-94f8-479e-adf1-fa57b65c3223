import axios from 'axios';
import { useAuthStore } from '@/stores/auth';
import router from '@/router';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

// Create axios instance with auth interceptor
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const authStore = useAuthStore();
  const token = authStore.token;
  
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  
  return config;
});

// Handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore();
      authStore.logout();
      router.push('/login');
    }
    return Promise.reject(error);
  }
);

export interface TransactionChatDetails {
  id: string;
  chatSessionId: string;
  offerId: string | null;
  status: string;
  currencyA: string;
  amountA: number;
  currencyAProviderId: string;
  currencyB: string;
  amountB: number;
  currencyBProviderId: string;
  agreedFirstPayerId: string | null;
  otherUser: {
    id: string;
    name: string;
    profilePic?: string | null;
    reputation: number;
  };
  transactionDetails: {
    amountToSend: number;
    amountToReceive: number;
    currencyFrom: string;
    currencyTo: string;
    isUserFirstPayer: boolean;
    otherUserPaymentDetails?: any;
    userPaymentDetails?: any;
  };
  currentStepIndex: number;
  feedItems: TransactionFeedItem[];
  timer?: {
    isActive: boolean;
    remainingSeconds: number;
    dueDate?: string;
  };
}

export interface TransactionFeedItem {
  id: string;
  type: 'chat' | 'systemLog' | 'actionCard';
  timestamp: string;
  content?: string; // For chat messages
  message?: string; // For system logs
  sender?: {
    id: string;
    name: string;
    isCurrentUser: boolean;
  };
  actionType?: string; // For action cards
  data?: any; // Additional data for action cards
}

export interface ChatMessageResponse {
  messageId: string;
  chatSessionId: string;
  sender: {
    id: string;
    username: string;
    reputationLevel: number;
  };
  content: string;
  createdAt: string;
  isSystemMessage?: boolean;
}

class TransactionalChatApiService {
  /**
   * Resolve chatSessionId to transactionId
   */
  async resolveChatSessionToTransaction(chatSessionId: string): Promise<{ transactionId: string; chatSessionId: string }> {
    try {
      console.log('🔄 API: Resolving chat session to transaction:', chatSessionId);
      
      const response = await apiClient.get(`/transactional-chat/by-chat-session/${chatSessionId}`);
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to resolve chat session to transaction');
      }
      
      console.log('✅ API: Successfully resolved chat session to transaction');
      return response.data.data;
    } catch (error: any) {
      console.error('❌ API: Error resolving chat session to transaction:', error);
      
      if (error.response?.status === 404) {
        throw new Error('Transaction not found for this chat session or access denied');
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required');
      } else {
        throw new Error(error.response?.data?.error || 'Failed to resolve chat session to transaction');
      }
    }
  }

  /**
   * Get complete transaction chat details
   */
  async fetchTransactionChatDetails(transactionId: string): Promise<TransactionChatDetails> {
    try {
      console.log('🔄 API: Fetching transaction chat details for:', transactionId);
      
      const response = await apiClient.get(`/transactional-chat/${transactionId}`);
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to fetch transaction details');
      }
      
      console.log('✅ API: Successfully fetched transaction chat details');
      return response.data.data;
    } catch (error: any) {
      console.error('❌ API: Error fetching transaction chat details:', error);
      
      if (error.response?.status === 404) {
        throw new Error('Transaction not found or access denied');
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required');
      } else {
        throw new Error(error.response?.data?.error || 'Failed to fetch transaction details');
      }
    }
  }

  /**
   * Send a chat message in the transaction context
   */
  async sendTransactionMessage(transactionId: string, messageText: string): Promise<ChatMessageResponse> {
    try {
      console.log('🔄 API: Sending message for transaction:', transactionId);
      
      const response = await apiClient.post(`/transactional-chat/${transactionId}/messages`, {
        messageText
      });
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to send message');
      }
      
      console.log('✅ API: Successfully sent message');
      return response.data.data;
    } catch (error: any) {
      console.error('❌ API: Error sending message:', error);
      
      if (error.response?.status === 404) {
        throw new Error('Transaction not found or access denied');
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required');
      } else {
        throw new Error(error.response?.data?.error || 'Failed to send message');
      }
    }
  }

  /**
   * Perform a transaction action
   */
  async performTransactionAction(
    transactionId: string, 
    actionType: string, 
    data?: any
  ): Promise<void> {
    try {
      console.log('🔄 API: Performing action:', actionType, 'for transaction:', transactionId);
      
      const response = await apiClient.post(`/transactional-chat/${transactionId}/actions`, {
        actionType,
        data
      });
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to perform action');
      }
      
      console.log('✅ API: Successfully performed action');
    } catch (error: any) {
      console.error('❌ API: Error performing action:', error);
      
      if (error.response?.status === 404) {
        throw new Error('Transaction not found or access denied');
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required');
      } else if (error.response?.status === 400) {
        throw new Error(error.response?.data?.error || 'Invalid action or transaction state');
      } else {
        throw new Error(error.response?.data?.error || 'Failed to perform action');
      }
    }
  }

  /**
   * Get current timer status for time-sensitive actions
   */
  async getTimerStatus(transactionId: string): Promise<{ isActive: boolean; remainingSeconds: number; dueDate?: string }> {
    try {
      console.log('🔄 API: Fetching timer status for transaction:', transactionId);
      
      const response = await apiClient.get(`/transactional-chat/${transactionId}/timer`);
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to fetch timer status');
      }
      
      console.log('✅ API: Successfully fetched timer status');
      return response.data.data.timer;
    } catch (error: any) {
      console.error('❌ API: Error fetching timer status:', error);
      
      if (error.response?.status === 404) {
        throw new Error('Transaction not found or access denied');
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required');
      } else {
        throw new Error(error.response?.data?.error || 'Failed to fetch timer status');
      }
    }
  }
}

export const transactionalChatApiService = new TransactionalChatApiService();
