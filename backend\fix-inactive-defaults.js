const { PrismaClient } = require('@prisma/client');

async function fixInactiveDefaults() {
  const prisma = new PrismaClient();
  
  try {
    console.log('=== Fixing Inactive Default Payment Methods ===');
    
    // Find all inactive methods that are still marked as default
    const inactiveDefaults = await prisma.paymentReceivingInfo.findMany({
      where: {
        isActive: false,
        isDefaultForUser: true
      }
    });
    
    console.log(`Found ${inactiveDefaults.length} inactive methods marked as default:`);
    inactiveDefaults.forEach(method => {
      console.log(`- ID: ${method.id}, User: ${method.userId}, Currency: ${method.currency}`);
    });
    
    if (inactiveDefaults.length > 0) {
      // Clear default flag from all inactive methods
      const result = await prisma.paymentReceivingInfo.updateMany({
        where: {
          isActive: false,
          isDefaultForUser: true
        },
        data: {
          isDefaultForUser: false
        }
      });
      
      console.log(`\n✅ Updated ${result.count} inactive methods to remove default flag`);
    } else {
      console.log('\n✅ No inactive default methods found - database is clean');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixInactiveDefaults();
