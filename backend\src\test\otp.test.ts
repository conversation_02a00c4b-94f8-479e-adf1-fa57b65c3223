import { describe, it, expect, vi } from 'vitest';
import {
  generateOtpSecret,
  generateOtpToken,
  verifyOtpToken,
} from '../utils/otp';
import { authenticator } from '@otplib/preset-default';

describe('OTP Utilities', () => {
  describe('generateOtpSecret', () => {
    it('should generate a non-empty string', () => {
      const secret = generateOtpSecret();
      expect(secret).toBeTypeOf('string');
      expect(secret.length).toBeGreaterThan(0);
    });

    it('should generate a Base32 encoded string', () => {
      const secret = generateOtpSecret();
      // Basic check for Base32 characters (A-Z, 2-7) and padding (=)
      expect(/^[A-Z2-7]+=*$/.test(secret)).toBe(true);
      // otplib default secret length (10 bytes / 80 bits) is 16 chars Base32
      expect(secret.length).toBe(16); // <-- Changed from 32 to 16
    });
  });

  describe('generateOtpToken', () => {
    it('should generate a 6-digit token string', () => {
      const secret = generateOtpSecret();
      const token = generateOtpToken(secret);
      expect(token).toBeTypeOf('string');
      expect(token).toMatch(/^\d{6}$/);
    });
  });

  describe('verifyOtpToken', () => {
    it('should return true for a valid token and secret', () => {
      const secret = generateOtpSecret();
      // Use authenticator directly to get a token guaranteed to be valid *now*
      authenticator.options = { step: 300, window: 1 }; // Ensure options match verifyOtpToken
      const token = authenticator.generate(secret);
      expect(verifyOtpToken(token, secret)).toBe(true);
    });

    it('should return false for an invalid token', () => {
      const secret = generateOtpSecret();
      const invalidToken = '123456'; // Highly unlikely to be valid
      expect(verifyOtpToken(invalidToken, secret)).toBe(false);
    });

    it('should return false for an invalid secret', () => {
      const secret1 = generateOtpSecret();
      const secret2 = generateOtpSecret(); // Different secret
      const token = generateOtpToken(secret1);
      expect(verifyOtpToken(token, secret2)).toBe(false);
    });

    it('should return false if authenticator.verify throws an error', () => {
        const secret = generateOtpSecret();
        const token = generateOtpToken(secret);

        // Mock authenticator.verify to throw an error
        const verifySpy = vi.spyOn(authenticator, 'verify').mockImplementationOnce(() => {
          throw new Error('Test verification error');
        });
        const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {}); // Suppress console error

        expect(verifyOtpToken(token, secret)).toBe(false);
        expect(verifySpy).toHaveBeenCalled();
        expect(consoleErrorSpy).toHaveBeenCalledWith('OTP verification error:', expect.any(Error));

        // Restore mocks
        verifySpy.mockRestore();
        consoleErrorSpy.mockRestore();
      });

      it('should return false for malformed token input', () => {
        const secret = generateOtpSecret();
        expect(verifyOtpToken('invalid-token', secret)).toBe(false); // otplib should handle this gracefully
      });

      it('should return false for malformed secret input', () => {
        const secret = 'not-base-32';
        const token = '123456';
        // otplib verify might throw or return false depending on internal handling
        expect(verifyOtpToken(token, secret)).toBe(false);
      });
  });
});
