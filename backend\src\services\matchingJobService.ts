import { PrismaClient, OfferStatus } from '@prisma/client';
import { MatchingService } from './matchingService';
import { ConsoleLogger, ILogger } from '../utils/logger';

/**
 * Background service for periodic matching operations
 * Handles error recovery and performance monitoring
 */
export class MatchingJobService {
  private isRunning = false;
  private intervalId: NodeJS.Timeout | null = null;
  private readonly MATCH_INTERVAL = 30000; // 30 seconds
  private readonly MAX_CONSECUTIVE_ERRORS = 5;
  private consecutiveErrors = 0;
  private lastProcessedTime = new Date();

  constructor(
    private prisma: PrismaClient,
    private matchingService: MatchingService,
    private logger: ILogger = new ConsoleLogger()
  ) {}

  /**
   * Start the background matching process
   */
  start(): void {
    if (this.isRunning) {
      this.logger.warn('[MatchingJobService] Already running');
      return;
    }

    this.isRunning = true;
    this.logger.info('[MatchingJobService] Starting background matching service');

    // Run immediately on start
    this.processMatches();

    // Set up periodic execution
    this.intervalId = setInterval(() => {
      this.processMatches();
    }, this.MATCH_INTERVAL);
  }

  /**
   * Stop the background matching process
   */
  stop(): void {
    if (!this.isRunning) {
      this.logger.warn('[MatchingJobService] Not running');
      return;
    }

    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    this.logger.info('[MatchingJobService] Stopped background matching service');
  }

  /**
   * Process matches for all active offers
   */
  private async processMatches(): Promise<void> {
    try {
      const startTime = Date.now();
      this.logger.info('[MatchingJobService] Starting match processing cycle');

      // Get all active offers that haven't been processed recently
      const activeOffers = await this.prisma.offer.findMany({
        where: {
          status: OfferStatus.ACTIVE,
          // Only process offers that haven't been checked in the last 5 minutes
          OR: [
            { lastMatchedAt: null },
            { lastMatchedAt: { lt: new Date(Date.now() - 5 * 60 * 1000) } }
          ]
        },        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
              reputationScore: true,
              reputationLevel: true
            }
          }
        },
        take: 50 // Limit batch size for performance
      });

      this.logger.info(`[MatchingJobService] Processing ${activeOffers.length} offers`);

      let processedCount = 0;
      let matchesFoundCount = 0;

      // Process each offer for potential matches
      for (const offer of activeOffers) {
        try {
          const matches = await this.matchingService.findPotentialMatches(offer.id);
          
          if (matches.length > 0) {
            matchesFoundCount += matches.length;
            this.logger.info(`[MatchingJobService] Found ${matches.length} matches for offer ${offer.id}`);
          }

          // Update lastMatchedAt timestamp
          await this.prisma.offer.update({
            where: { id: offer.id },
            data: { lastMatchedAt: new Date() }
          });

          processedCount++;
        } catch (error) {
          this.logger.error(`[MatchingJobService] Error processing offer ${offer.id}:`, error);
          // Continue processing other offers even if one fails
        }
      }

      const duration = Date.now() - startTime;
      this.lastProcessedTime = new Date();
      this.consecutiveErrors = 0; // Reset error count on successful cycle

      this.logger.info(`[MatchingJobService] Completed cycle: ${processedCount} offers processed, ${matchesFoundCount} matches found in ${duration}ms`);

    } catch (error) {
      this.consecutiveErrors++;
      this.logger.error(`[MatchingJobService] Error in match processing cycle (${this.consecutiveErrors}/${this.MAX_CONSECUTIVE_ERRORS}):`, error);

      // Stop service if too many consecutive errors
      if (this.consecutiveErrors >= this.MAX_CONSECUTIVE_ERRORS) {
        this.logger.error('[MatchingJobService] Too many consecutive errors, stopping service');
        this.stop();
      }
    }
  }

  /**
   * Manually trigger a match processing cycle
   */
  async triggerMatching(): Promise<void> {
    if (!this.isRunning) {
      throw new Error('MatchingJobService is not running');
    }

    this.logger.info('[MatchingJobService] Manual matching trigger');
    await this.processMatches();
  }

  /**
   * Get service status and metrics
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastProcessedTime: this.lastProcessedTime,
      consecutiveErrors: this.consecutiveErrors,
      maxConsecutiveErrors: this.MAX_CONSECUTIVE_ERRORS,
      intervalMs: this.MATCH_INTERVAL
    };
  }

  /**
   * Clean up expired matches (called daily)
   */
  async cleanupExpiredMatches(): Promise<void> {
    try {
      this.logger.info('[MatchingJobService] Starting expired match cleanup');

      const expirationTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

      const result = await this.prisma.offerMatch.updateMany({
        where: {
          status: 'PENDING',
          createdAt: { lt: expirationTime }
        },
        data: {
          status: 'EXPIRED',
          updatedAt: new Date()
        }
      });

      this.logger.info(`[MatchingJobService] Expired ${result.count} matches`);

      // TODO: Send expiration notifications to users
      // This could be implemented as a separate method that queries
      // the newly expired matches and sends notifications

    } catch (error) {
      this.logger.error('[MatchingJobService] Error cleaning up expired matches:', error);
    }
  }
}
