<template>
  <div class="home-container">
    <!-- Feature Flag: New Component-Based Design -->
    <template v-if="uiPreferences.useNewHomeDesign">
      <HeroSection />
      <StatsSection 
        :stats="stats"
        :user-reputation="userReputation"
        :loading="isLoadingStats"
      />
      <QuickActionsSection 
        :pending-matches-count="pendingMatchesCount"
      />
      <ActivitySection />
    </template>

    <!-- Original Design (Fallback) -->
    <template v-else>
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">{{ $t('homeView.heroTitle') }}</h1>
          <p class="hero-subtitle">
            {{ $t('homeView.heroSubtitle') }}
          </p>
          <div class="hero-actions">
            <n-button 
              type="primary" 
              size="large" 
              class="primary-cta"
              @click="goToCreateOffer"
            >
              <template #icon>
                <n-icon><PlusOutlined /></n-icon>
              </template>
              {{ $t('homeView.createOffer') }}
            </n-button>
            <n-button 
              size="large" 
              class="secondary-cta"
              @click="goToBrowseOffers"
            >
              <template #icon>
                <n-icon><SearchOutlined /></n-icon>
              </template>
              {{ $t('homeView.browseOffers') }}
            </n-button>
          </div>
        </div>
      </section>      <!-- Stats Section -->
      <section class="stats-section">
        <n-card class="stats-card">
          <n-grid :cols="3" :x-gap="24" responsive="screen">
            <n-grid-item>
              <div class="stat-item">
                <n-icon size="32" color="#18a058">
                  <ShopOutlined />
                </n-icon>
                <n-statistic 
                  :label="$t('homeView.activeOffersLabel')"
                  :loading="isLoadingStats"
                >
                  <n-number-animation :from="prevActiveOffers" :to="stats.activeOffers" :duration="1000" />
                </n-statistic>
              </div>
            </n-grid-item>
            <n-grid-item>
              <div class="stat-item">
                <n-icon size="32" color="#2080f0">
                  <UserOutlined />
                </n-icon>
                <n-statistic 
                  :label="$t('homeView.myOffers')"
                  :loading="isLoadingStats"
                >
                  <n-number-animation :from="prevMyOffers" :to="stats.myOffers" :duration="1000" />
                </n-statistic>
              </div>
            </n-grid-item>
            <n-grid-item>
              <div class="stat-item">
                <n-icon size="32" color="#f0a020">
                  <StarOutlined />
                </n-icon>
                <n-statistic 
                  :label="$t('homeView.reputationLevel')"
                  :loading="isLoadingStats"
                >
                  <n-number-animation :from="prevReputation" :to="userReputation" :duration="1000" />
                </n-statistic>
              </div>
            </n-grid-item>
          </n-grid>
        </n-card>
      </section>

      <!-- Quick Actions Section -->
      <section class="actions-section">
        <h2 class="section-title">{{ $t('homeView.quickActions') }}</h2>
        <n-grid :cols="3" :x-gap="24" :y-gap="24" responsive="screen">
          <n-grid-item>
            <n-card 
              hoverable 
              class="action-card" 
              @click="goToMyOffers"
            >
              <div class="action-content">
                <div class="action-header">
                  <n-icon size="40" color="#2080f0">
                    <FileTextOutlined />
                  </n-icon>
                  <h3>{{ $t('homeView.myOffersAction') }}</h3>
                </div>
                <p>{{ $t('homeView.myOffersDescription') }}</p>
                <div class="action-footer">
                  <n-button text type="primary">
                    {{ $t('homeView.viewMyOffers') }}
                    <template #icon>
                      <n-icon><ArrowRightOutlined /></n-icon>
                    </template>
                  </n-button>
                </div>
              </div>
            </n-card>
          </n-grid-item>
          <n-grid-item>
            <n-card 
              hoverable 
              class="action-card" 
              @click="goToMatches"
            >
              <div class="action-content">
                <div class="action-header">
                  <n-icon size="40" color="#f0a020">
                    <HeartOutlined />
                  </n-icon>
                  <h3>{{ $t('homeView.myMatchesAction') }}</h3>
                </div>
                <p>{{ $t('homeView.myMatchesDescription') }}</p>
                <div class="action-footer">
                  <n-button text type="primary">
                    {{ $t('homeView.viewMyMatches') }}
                    <template #icon>
                      <n-icon><ArrowRightOutlined /></n-icon>
                    </template>
                  </n-button>
                  <n-badge 
                    v-if="pendingMatchesCount > 0" 
                    :value="pendingMatchesCount"
                    class="match-badge"
                    type="warning"
                  />
                </div>
              </div>
            </n-card>
          </n-grid-item>
          <n-grid-item>
            <n-card 
              hoverable 
              class="action-card" 
              @click="goToProfile"
            >
              <div class="action-content">
                <div class="action-header">
                  <n-icon size="40" color="#18a058">
                    <UserOutlined />
                  </n-icon>
                  <h3>{{ $t('homeView.profile') }}</h3>
                </div>
                <p>{{ $t('homeView.profileDescription') }}</p>
                <div class="action-footer">
                  <n-button text type="primary">
                    {{ $t('homeView.viewProfile') }}
                    <template #icon>
                      <n-icon><ArrowRightOutlined /></n-icon>
                    </template>
                  </n-button>
                </div>
              </div>
            </n-card>
          </n-grid-item>
        </n-grid>
      </section>

      <!-- Recent Activity Section -->
      <section class="activity-section" v-if="recentOffers.length > 0">
        <h2 class="section-title">{{ $t('homeView.recentActivity') }}</h2>
        <n-grid :cols="1" :x-gap="24" :y-gap="16">
          <n-grid-item v-for="offer in recentOffers" :key="offer.id">
            <n-card class="offer-preview">
              <div class="offer-preview-content">
                <div class="offer-info">
                  <h4>{{ offer.type === 'BUY' ? $t('homeView.buying') : $t('homeView.selling') }} {{ (offer.currencyPair?.split('-')[0]) || 'Currency' }}</h4>
                  <p class="offer-description">
                    {{ formatOfferDescription(offer) }}
                  </p>
                  <n-tag 
                    :type="getOfferStatusType(offer.status)" 
                    size="small"
                  >
                    {{ offer.status }}
                  </n-tag>
                </div>
                <div class="offer-amount">
                  <div class="amount-display">
                    $<span class="amount-value">{{ formatAmount(offer.amount || 0, 'CAD') }}</span>
                  </div>
                </div>
              </div>
            </n-card>
          </n-grid-item>
        </n-grid>
      </section>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { computed, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { 
  NButton, 
  NCard, 
  NGrid, 
  NGridItem, 
  NIcon, 
  NStatistic, 
  NTag,
  NBadge,
  NNumberAnimation, // Import NNumberAnimation
  useMessage // Import for error messages
} from 'naive-ui';
import {
  PlusOutlined,
  SearchOutlined,
  ShopOutlined,
  UserOutlined,
  StarOutlined,
  FileTextOutlined,
  ArrowRightOutlined,
  HeartOutlined
} from '@vicons/antd';
import { formatAmount } from '@/utils/currencyUtils';

import { useAuthStore } from '@/stores/auth';
import { useOfferStore } from '@/stores/offerStore';
import { useMyOffersStore } from '@/stores/myOffersStore';
import { useInterestStore } from '@/stores/interestStore';
import { useMatchStore } from '@/stores/matchStore';
import { useUiPreferencesStore } from '@/stores/uiPreferences';

// Import new components
import HeroSection from '@/components/home/<USER>';
import StatsSection from '@/components/home/<USER>';
import QuickActionsSection from '@/components/home/<USER>';
import ActivitySection from '@/components/home/<USER>';

const router = useRouter();
const { t } = useI18n();
const authStore = useAuthStore();
const offerStore = useOfferStore();
const myOffersStore = useMyOffersStore();
const interestStore = useInterestStore();
const matchStore = useMatchStore();
const uiPreferences = useUiPreferencesStore();
const message = useMessage(); // Initialize message for notifications

// Debug: Check feature flag state
console.log('🏠 HomeView: Feature flag state:', {
  storeValue: uiPreferences.useNewHomeDesign,
  localStorageValue: localStorage.getItem('useNewHomeDesign'),
  timestamp: new Date().toISOString()
});

// Ensure store is synced with localStorage on mount
const syncStoreWithLocalStorage = () => {
  const savedValue = localStorage.getItem('useNewHomeDesign');
  if (savedValue !== null) {
    const shouldEnable = savedValue === 'true';
    if (uiPreferences.useNewHomeDesign !== shouldEnable) {
      console.log('🔄 Syncing store with localStorage:', savedValue);
      uiPreferences.syncFromLocalStorage();
    }
  }
};

// Call sync immediately
syncStoreWithLocalStorage();

// Loading states
const isLoadingStats = ref(false);

// Previous values for smooth transitions
const prevActiveOffers = ref(0);
const prevMyOffers = ref(0);
const prevReputation = ref(0);

// Computed properties
const userReputation = computed(() => {
  return authStore.user?.reputationLevel || 0;
});

const stats = computed(() => ({
  activeOffers: offerStore.activeBrowsableOffers.length, // Use activeBrowsableOffers for the count
  myOffers: myOffersStore.myOffers.length
}));

// Watch for changes to update previous values
watch(() => stats.value.activeOffers, (_, oldVal) => {
  if (oldVal !== undefined) {
    prevActiveOffers.value = oldVal;
  }
});

watch(() => stats.value.myOffers, (_, oldVal) => {
  if (oldVal !== undefined) {
    prevMyOffers.value = oldVal;
  }
});

watch(() => userReputation.value, (_, oldVal) => {
  if (oldVal !== undefined) {
    prevReputation.value = oldVal;
  }
});

const recentOffers = computed(() => {
  // Show recent offers from both browse offers and user's own offers
  const allOffers = [
    ...offerStore.offers.slice(0, 3), // Use all offers for recent activity preview
    ...myOffersStore.myOffers.slice(0, 2)
  ];
  
  // Remove duplicates and limit to 5
  const uniqueOffers = allOffers.filter((offer, index, self) => 
    offer?.id && index === self.findIndex(o => o?.id === offer.id)
  ).slice(0, 5);
  
  return uniqueOffers;
});

const pendingMatchesCount = computed(() => {
  return matchStore.matches.filter(match => match.status === 'PENDING').length;
});

// Methods
function goToCreateOffer() {
  router.push({ name: 'CreateOffer' });
}

function goToMyOffers() {
  router.push({ name: 'MyOffers' });
}

function goToBrowseOffers() {
  router.push({ name: 'BrowseOffers' });
}

function goToMatches() {
  router.push({ name: 'MatchList' });
}

function goToProfile() {
  router.push({ name: 'profile' });
}

function getOfferStatusType(status: string) {
  switch (status) {
    case 'ACTIVE':
      return 'success';
    case 'PENDING': // Assuming PENDING is a possible status for offers in general
      return 'warning';
    case 'COMPLETED':
      return 'info';
    case 'CANCELLED':
      return 'error';
    default:
      return 'default';
  }
}


interface OfferSummary {
  id?: string; // Added id for key in v-for and uniqueness
  type: 'BUY' | 'SELL';
  currencyPair?: string;
  baseRate?: number;
  status?: string; // Added status for tag
  amount?: number; // Added amount for statistic
}

function formatOfferDescription(offer: OfferSummary) {  
  if (!offer.currencyPair || !offer.baseRate) {
    return '';
  }
  
  // Extract currencies from the currency pair (e.g., "IRR-CAD" -> ["IRR", "CAD"])
  const currencies = offer.currencyPair.split('-');
  if (currencies.length !== 2) {
    return '';
  }
  
  const [fromCurrency, toCurrency] = currencies;
  
  // Swap the order to show "IRR/CAD" format for proper exchange rate display
  // This shows how many units of the first currency per one unit of the second currency
  return t('homeView.atRate', { rate: `${offer.baseRate} ${toCurrency}/${fromCurrency}` });
}

// Load data on mount
onMounted(async () => {
  // Ensure feature flag is properly synced on mount
  syncStoreWithLocalStorage();
  
  // Listen for localStorage changes (e.g., from console commands)
  const handleStorageChange = (e: StorageEvent) => {
    if (e.key === 'useNewHomeDesign') {
      console.log('🔄 Storage change detected for useNewHomeDesign:', e.newValue);
      syncStoreWithLocalStorage();
    }
  };
  
  window.addEventListener('storage', handleStorageChange);
  
  // Also check periodically in case of manual console changes in same tab
  const checkInterval = setInterval(() => {
    syncStoreWithLocalStorage();
  }, 1000);
  
  // Cleanup on unmount
  const cleanup = () => {
    window.removeEventListener('storage', handleStorageChange);
    clearInterval(checkInterval);
  };
  
  // Store cleanup function for component unmount
  (window as any).__homeViewCleanup = cleanup;
  
  isLoadingStats.value = true;
  try {
    // Initialize previous values to current values to prevent initial animation
    prevActiveOffers.value = stats.value.activeOffers;
    prevMyOffers.value = stats.value.myOffers;
    prevReputation.value = userReputation.value;

    // Load offers and user's offers in parallel
    await Promise.all([
      offerStore.loadOffers(),
      myOffersStore.fetchMyOffers(),
      matchStore.loadMatches()
    ]);
    // Initialize socket listeners for the offer store to receive real-time updates
    // The offerStore's initializeSocketListeners is idempotent.
    offerStore.initializeSocketListeners();
    // Initialize socket listeners for the interest store to receive real-time updates
    // The interestStore's initializeSocketListeners is idempotent.
    interestStore.initializeSocketListeners();
    // Initialize socket listeners for the match store to receive real-time updates
    // The matchStore's attachSocketListeners is idempotent.
    matchStore.attachSocketListeners();
    } catch (error) {
    console.error('Error loading homepage data:', error);
    message.error(t('homeView.failedToLoadData'));
  } finally {
    isLoadingStats.value = false;
  }
});
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

/* Light theme home container */
[data-theme="light"] .home-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
}

/* Dark theme home container */
[data-theme="dark"] .home-container {
  background: linear-gradient(135deg, #1a1b2e 0%, #16213e 50%, #0f1419 100%);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 24px 60px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Light theme hero */
[data-theme="light"] .hero-section {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  color: white;
}

/* Dark theme hero */
[data-theme="dark"] .hero-section {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  color: white;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1" fill-rule="nonzero"><circle cx="30" cy="30" r="4"/></g></g></svg>');
  opacity: 0.1;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 24px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.primary-cta {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(0);
  transition: all 0.3s ease;
}

.primary-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.secondary-cta {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.secondary-cta:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Stats Section */
.stats-section {
  margin: -30px 24px 40px;
  position: relative;
  z-index: 2;
}

.stats-card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

/* Light theme stats card */
[data-theme="light"] .stats-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

/* Dark theme stats card */
[data-theme="dark"] .stats-card {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.stat-item {
  text-align: center;
  padding: 24px 16px;
  transition: all 0.3s ease;
}

.stat-item .n-icon {
  margin-bottom: 12px;
  transition: transform 0.3s ease;
}

.stat-item:hover .n-icon {
  transform: scale(1.1);
}

.stat-item .n-statistic .n-statistic__label { /* Target Naive UI's generated class for label */
  margin-bottom: 4px; /* Adjust spacing for label */
  font-size: 0.9em;
  opacity: 0.8;
}

.stat-item .n-statistic .n-statistic__value { /* Target Naive UI's generated class for value */
  font-size: 1.75rem; /* Adjust font size for the number animation */
  font-weight: 600;
}

/* Sections */
.actions-section,
.activity-section {
  padding: 40px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  font-size: 2rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 32px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Light theme section title */
[data-theme="light"] .section-title {
  color: #1e293b;
  text-shadow: none;
}

/* Dark theme section title */
[data-theme="dark"] .section-title {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Action Cards */
.action-card {
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Light theme action cards */
[data-theme="light"] .action-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* Dark theme action cards */
[data-theme="dark"] .action-card {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

/* Light theme action card hover */
[data-theme="light"] .action-card:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

/* Dark theme action card hover */
[data-theme="dark"] .action-card:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.5);
}

.action-content {
  text-align: center;
  padding: 32px 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.action-content .n-icon {
  margin-bottom: 16px;
}

.action-header {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-header .n-icon {
  margin-bottom: 16px;
}

.action-content h3 {
  margin: 16px 0 12px;
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  transition: color 0.3s ease;
}

.action-footer {
  margin-top: auto;
}

/* Light theme action content */
[data-theme="light"] .action-content h3 {
  color: #1e293b;
}

/* Dark theme action content */
[data-theme="dark"] .action-content h3 {
  color: rgba(255, 255, 255, 0.9);
}

.action-content p {
  color: #666;
  margin-bottom: 24px;
  line-height: 1.5;
  transition: color 0.3s ease;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 3em;
}

/* Light theme action content paragraph */
[data-theme="light"] .action-content p {
  color: #64748b;
}

/* Dark theme action content paragraph */
[data-theme="dark"] .action-content p {
  color: rgba(255, 255, 255, 0.7);
}

/* Offer Preview Cards - Enhanced Glassmorphism */
.offer-preview {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 1px 4px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* Light theme offer preview */
[data-theme="light"] .offer-preview {
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

/* Dark theme offer preview */
[data-theme="dark"] .offer-preview {
  background: rgba(16, 16, 20, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.4),
    0 1px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.offer-preview:hover {
  transform: translateY(-4px) scale(1.02);
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 
    0 12px 32px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Dark theme hover */
[data-theme="dark"] .offer-preview:hover {
  background: rgba(16, 16, 20, 0.9);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 
    0 12px 32px rgba(0, 0, 0, 0.6),
    0 4px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.offer-preview-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  position: relative;
  z-index: 2;
}

.offer-info {
  flex: 1;
}

.offer-info h4 {
  margin: 0 0 8px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  transition: color 0.3s ease;
}

/* Light theme offer info */
[data-theme="light"] .offer-info h4 {
  color: #1e293b;
}

/* Dark theme offer info */
[data-theme="dark"] .offer-info h4 {
  color: rgba(255, 255, 255, 0.9);
}

.offer-description {
  margin: 8px 0 12px;
  color: #666;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

/* Light theme offer description */
[data-theme="light"] .offer-description {
  color: #64748b;
}

/* Dark theme offer description */
[data-theme="dark"] .offer-description {
  color: rgba(255, 255, 255, 0.7);
}

.offer-amount {
  text-align: right;
  margin-left: 16px;
}

.amount-display {
  font-size: 1.5rem;
  font-weight: 600;
  color: #18a058;
}

.amount-value {
  font-family: tabular-nums;
}

/* Glassmorphism Enhancement - Subtle shimmer effect */
.offer-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  border-radius: 16px;
  transition: left 0.6s ease;
  pointer-events: none;
  z-index: 1;
}

.offer-preview:hover::before {
  left: 100%;
}

/* Dark theme shimmer */
[data-theme="dark"] .offer-preview::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    padding: 60px 20px 50px;
  }
  
  .hero-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
    margin-bottom: 32px;
    padding: 0 10px;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
  
  .hero-actions .n-button {
    width: 100%;
    max-width: 280px;
    height: 48px;
    font-size: 16px;
  }
  
  .stats-section {
    margin: -20px 16px 32px;
  }
  
  .stats-card .n-grid {
    /* Force single column on mobile for better readability */
    grid-template-columns: 1fr !important;
    gap: 20px;
  }
    .stat-item {
    padding: 20px 16px;
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  [data-theme="dark"] .stat-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .stat-item:last-child {
    border-bottom: none;
  }
  
  .actions-section,
  .activity-section {
    padding: 32px 16px;
  }
  
  .actions-section .n-grid {
    /* Single column on mobile for action cards */
    grid-template-columns: 1fr !important;
    gap: 16px;
  }
  
  .section-title {
    font-size: 1.75rem;
    margin-bottom: 24px;
  }
  
  .action-card {
    /* Better touch targets on mobile */
    min-height: 160px;
  }
  
  .action-content {
    padding: 24px 20px;
  }
  
  .action-content .n-icon {
    margin-bottom: 12px;
  }
  
  .action-content h3 {
    font-size: 1.1rem;
    margin: 12px 0 8px;
  }
  
  .action-content p {
    font-size: 0.9rem;
    margin-bottom: 16px;
  }
  
  .offer-preview-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
  }
  
  .offer-amount {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .amount-display {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 50px 16px 40px;
  }
  
  .hero-title {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  .hero-subtitle {
    font-size: 1rem;
    line-height: 1.5;
  }
  
  .hero-actions .n-button {
    height: 44px;
    font-size: 15px;
  }
  
  .stats-section {
    margin: -15px 12px 24px;
  }
  
  .stat-item {
    padding: 16px 12px;
  }
  
  .stat-item .n-icon {
    margin-bottom: 8px;
  }
  
  .actions-section,
  .activity-section {
    padding: 24px 12px;
  }
  
  .section-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
  }
    .action-content {
    padding: 20px 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .action-header .n-icon {
    margin-bottom: 10px;
  }
  
  .offer-preview-content {
    padding: 14px;
  }
  
  .offer-info h4 {
    font-size: 1rem;
  }
  
  .offer-description {
    font-size: 0.85rem;
  }
}

/* Improve touch interactions on mobile */
@media (hover: none) and (pointer: coarse) {
  .action-card:hover {
    transform: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  }
  
  .action-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
  
  /* Enhanced mobile glassmorphism for offer cards */
  .offer-preview:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.85);
  }
  
  .offer-preview:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 
      0 8px 24px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }
  
  /* Dark theme mobile active state */
  [data-theme="dark"] .offer-preview:active {
    background: rgba(16, 16, 20, 0.95);
    box-shadow: 
      0 8px 24px rgba(0, 0, 0, 0.5),
      0 2px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }
  
  .primary-cta:hover {
    transform: none;
  }
  
  .primary-cta:active {
    transform: scale(0.97);
  }
  
  .secondary-cta:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .secondary-cta:active {
    transform: scale(0.97);
    background: rgba(255, 255, 255, 0.2);
  }
}

/* Additional mobile optimizations */
@media (max-width: 360px) {
  .hero-title {
    font-size: 1.75rem;
  }
  
  .hero-subtitle {
    font-size: 0.95rem;
  }
  
  .stats-section {
    margin: -10px 8px 20px;
  }
  
  .actions-section,
  .activity-section {
    padding: 20px 8px;
  }
  
  .section-title {
    font-size: 1.4rem;
  }
}

/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur()) {
  .offer-preview {
    background: rgba(255, 255, 255, 0.95);
  }
  
  [data-theme="light"] .offer-preview {
    background: rgba(255, 255, 255, 0.98);
  }
  
  [data-theme="dark"] .offer-preview {
    background: rgba(16, 16, 20, 0.95);
  }
}
</style>
