/**
 * Test script to validate AI service tag origin validation
 */

// Simulate the fix behavior
function validateTags(tags, predefinedTags) {
  if (!Array.isArray(tags)) return undefined;
  
  // Create a flat list of all predefined tags for validation
  const allPredefinedTags = [];
  if (predefinedTags) {
    Object.values(predefinedTags).forEach(tagList => {
      allPredefinedTags.push(...tagList);
    });
  }
  
  return tags
    .filter(tagItem => 
      tagItem && 
      typeof tagItem.tag === 'string' && 
      tagItem.tag.length <= 50 &&
      ['PREDEFINED', 'AI_SUGGESTED'].includes(tagItem.origin)
    )
    .map(tagItem => {
      // Validate that tags marked as PREDEFINED actually exist in the predefined list
      if (tagItem.origin === 'PREDEFINED') {
        const isActuallyPredefined = allPredefinedTags.includes(tagItem.tag);
        if (!isActuallyPredefined) {
          console.warn(`Tag "${tagItem.tag}" marked as PREDEFINED but not found in predefined tags list. Converting to AI_SUGGESTED.`);
          return {
            tag: tagItem.tag,
            origin: 'AI_SUGGESTED'
          };
        }
      }
      
      return {
        tag: tagItem.tag,
        origin: tagItem.origin
      };
    })
    .slice(0, 10);
}

// Test with the problematic tags from the debug log
const aiSuggestedTags = [
  { tag: 'bug', origin: 'PREDEFINED' },           // Should be converted to AI_SUGGESTED
  { tag: 'ui-ux', origin: 'PREDEFINED' },        // Should be converted to AI_SUGGESTED  
  { tag: 'error', origin: 'PREDEFINED' },        // Should stay PREDEFINED if in list
  { tag: 'fix-needed', origin: 'PREDEFINED' },   // Should stay PREDEFINED if in list
  { tag: 'login-issue', origin: 'AI_SUGGESTED' }, // Should stay AI_SUGGESTED
  { tag: 'input-fields', origin: 'AI_SUGGESTED' } // Should stay AI_SUGGESTED
];

const predefinedTags = {
  bug: ['urgent', 'error', 'fix-needed', 'slow'],
  'feature-request': ['new-feature', 'enhancement', 'improvement'],
  'ui-ux': ['user-experience', 'design', 'interface']
};

console.log('Testing AI service tag validation fix...');
console.log('Input tags:', aiSuggestedTags);
console.log('Predefined tags:', predefinedTags);

const result = validateTags(aiSuggestedTags, predefinedTags);
console.log('Result after validation:', result);

console.log('\nExpected behavior:');
console.log('- "bug" should be converted to AI_SUGGESTED (not in predefined list)');
console.log('- "ui-ux" should be converted to AI_SUGGESTED (not in predefined list)');
console.log('- "error" should stay PREDEFINED (in predefined list)'); 
console.log('- "fix-needed" should stay PREDEFINED (in predefined list)');
console.log('- "login-issue" should stay AI_SUGGESTED');
console.log('- "input-fields" should stay AI_SUGGESTED');
