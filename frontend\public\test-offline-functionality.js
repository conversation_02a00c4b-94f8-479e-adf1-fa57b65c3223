// Test script to verify offline bug report functionality
// Run this in the browser console to test the offline functionality

console.log('🧪 Testing Offline Bug Report Functionality');

// Function to test the offline functionality
async function testOfflineFunctionality() {
  console.log('📋 Starting offline bug report test...');
  
  try {
    // Get the useClientLogger composable
    const { useClientLogger } = await import('/src/composables/useClientLogger.ts');
    const { useOfflineReports } = await import('/src/composables/useOfflineReports.ts');
    
    const logger = useClientLogger();
    const offlineReports = useOfflineReports();
    
    console.log('✅ Composables loaded successfully');
    
    // Clear any existing offline reports
    offlineReports.clearOfflineReports();
    console.log('🧹 Cleared existing offline reports');
    
    // Test report details
    const testReport = {
      type: 'bug',
      title: 'Test Offline Functionality',
      description: 'Testing if reports are stored offline when backend is unavailable',
      severity: 'medium',
      stepsToReproduce: '1. Backend server is down\n2. Submit bug report\n3. Should store offline',
      expectedBehavior: 'Report should be stored offline',
      actualBehavior: 'Testing to see if it works',
      additionalNotes: 'This is a test report for offline functionality'
    };
    
    console.log('📝 Test report prepared:', testReport);
    
    // Attempt to send the report (should fail and store offline)
    console.log('🚀 Attempting to send report to server...');
    const response = await logger.sendLogsToServer(testReport);
    
    console.log('📨 Server response:', response);
    
    if (response.isNetworkError) {
      console.log('✅ Network error correctly detected!');
      console.log('💾 Report should be stored offline automatically by the component');
    } else if (response.success) {
      console.log('✅ Report sent successfully (backend is running)');
    } else {
      console.log('❌ Server error (not network error):', response.message);
    }
    
    // Check offline reports count
    setTimeout(() => {
      console.log('📊 Current offline reports count:', offlineReports.offlineReportCount.value);
      console.log('📋 Has offline reports:', offlineReports.hasOfflineReports.value);
      
      if (offlineReports.hasOfflineReports.value) {
        console.log('✅ SUCCESS: Offline reports are being stored!');
        console.log('📄 Offline reports:', offlineReports.offlineReports.value);
      } else {
        console.log('⚠️  No offline reports found. This could mean:');
        console.log('   - Backend is running and report was sent successfully');
        console.log('   - There was an issue with offline storage');
        console.log('   - The component handles the offline storage, not this direct test');
      }
    }, 1000);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Function to simulate network conditions
function simulateOffline() {
  console.log('🔌 Simulating offline mode...');
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: false
  });
  console.log('📱 navigator.onLine set to:', navigator.onLine);
}

function simulateOnline() {
  console.log('🌐 Simulating online mode...');
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: true
  });
  console.log('📱 navigator.onLine set to:', navigator.onLine);
}

// Function to check current status
function checkStatus() {
  console.log('📊 Current Status:');
  console.log('  🌐 navigator.onLine:', navigator.onLine);
  console.log('  🔌 Environment:', import.meta.env.DEV ? 'Development' : 'Production');
  
  // Try to access offline reports if available
  try {
    const offlineReportsData = localStorage.getItem('offline-reports');
    if (offlineReportsData) {
      const reports = JSON.parse(offlineReportsData);
      console.log('  📄 Offline reports in localStorage:', reports.length);
    } else {
      console.log('  📄 No offline reports in localStorage');
    }
  } catch (error) {
    console.log('  📄 Could not access offline reports:', error.message);
  }
}

// Export functions for manual testing
window.testOfflineFunctionality = testOfflineFunctionality;
window.simulateOffline = simulateOffline;
window.simulateOnline = simulateOnline;
window.checkStatus = checkStatus;

console.log('🎯 Test functions available:');
console.log('  - testOfflineFunctionality() - Test the offline functionality');
console.log('  - simulateOffline() - Set navigator.onLine to false');
console.log('  - simulateOnline() - Set navigator.onLine to true');
console.log('  - checkStatus() - Check current status');
console.log('');
console.log('💡 To test offline functionality:');
console.log('  1. Run: checkStatus()');
console.log('  2. Run: testOfflineFunctionality()');
console.log('  3. Or use the test page at /test/offline-reports');
