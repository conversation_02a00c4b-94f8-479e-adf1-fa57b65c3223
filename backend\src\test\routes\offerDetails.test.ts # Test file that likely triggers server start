const { serve } = require('@hono/node-server');
const { describe, it, beforeAll, afterAll } = require('vitest');

let server;

describe('Offer Details API', () => {
	beforeAll(async () => {
		const port = parseInt(process.env.PORT || '3001', 10);
		server = serve({
			fetch: () => new Response('Hello World'),
			port
		});
		await server.start();
	});

	afterAll(async () => {
		await server.stop();
	});

	it('should return offer details', async () => {
		const response = await fetch(`http://localhost:3001/offer-details`);
		expect(response.status).toBe(200);
		const data = await response.text();
		expect(data).toBe('Hello World');
	});
});