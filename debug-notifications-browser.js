// Notification System Test Script
// Paste this into the browser console to test notifications

console.log("🔍 Starting notification system debug...");

// Test 1: Check if socket manager is connected
function checkSocketConnection() {
  console.log("📡 Checking socket connection...");
  
  try {
    // Get socket manager from window (if exposed) or Vue app
    var app = document.querySelector('#app').__vue_app__;
    if (!app) {
      console.error("❌ Cannot find Vue app instance");
      return false;
    }
    
    // Check if we can access the stores
    console.log("✅ Vue app found, checking stores...");
    return true;
  } catch (error) {
    console.error("❌ Error checking socket connection:", error);
    return false;
  }
}

// Test 2: Check notification store state
const checkNotificationStore = () => {
  console.log("🔔 Checking notification store...");
  
  try {
    // Try to access notification store state via Vue DevTools or global
    console.log("Check your Vue DevTools > Pinia tab for notification store state");
    console.log("Look for:");
    console.log("- notifications array");
    console.log("- unreadNotificationsCount");
    console.log("- isLoading state");
    
    return true;
  } catch (error) {
    console.error("❌ Error checking notification store:", error);
    return false;
  }
};

// Test 3: Check authentication state
const checkAuthState = () => {
  console.log("🔐 Checking authentication state...");
  
  const token = localStorage.getItem('authToken');
  const userInfo = localStorage.getItem('userInfo');
  
  if (token && userInfo) {
    console.log("✅ User is authenticated");
    console.log("Token exists:", !!token);
    console.log("User info:", JSON.parse(userInfo));
    return true;
  } else {
    console.log("❌ User is not authenticated");
    console.log("Please log in to test notifications");
    return false;
  }
};

// Test 4: Monitor socket events
const monitorSocketEvents = () => {
  console.log("👂 Setting up socket event monitoring...");
  console.log("Watch the console for these messages when someone expresses interest:");
  console.log("- '🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION:'");
  console.log("- '[NotificationStore] Received NEW_NOTIFICATION via centralized manager:'");
  console.log("- '[NotificationStore] Added new notification'");
  
  // Add a console listener for NEW_NOTIFICATION
  const originalLog = console.log;
  console.log = function(...args) {
    if (args[0] && args[0].includes && args[0].includes('NEW_NOTIFICATION')) {
      console.warn("🔔🔔🔔 NOTIFICATION EVENT DETECTED:", ...args);
    }
    originalLog.apply(console, args);
  };
  
  return true;
};

// Test 5: Manual notification test guide
const showManualTestGuide = () => {
  console.log("");
  console.log("🎯 MANUAL TEST PROCEDURE:");
  console.log("========================");
  console.log("1. Keep this console open");
  console.log("2. Open a second incognito browser window");
  console.log("3. Go to http://localhost:5173 in both windows");
  console.log("4. Log in as different users in each window");
  console.log("5. In window 1: Create an offer");
  console.log("6. In window 2: Browse offers and express interest");
  console.log("7. Check window 1 for notification bell badge");
  console.log("8. Watch this console for socket events");
  console.log("");
};

// Run all tests
console.log("🚀 Running notification system tests...");
console.log("========================================");

const tests = [
  { name: "Socket Connection", test: checkSocketConnection },
  { name: "Notification Store", test: checkNotificationStore },
  { name: "Authentication State", test: checkAuthState },
  { name: "Socket Event Monitoring", test: monitorSocketEvents }
];

tests.forEach(({ name, test }) => {
  console.log(`\n📋 Test: ${name}`);
  try {
    const result = test();
    console.log(result ? "✅ PASSED" : "❌ FAILED");
  } catch (error) {
    console.error("❌ ERROR:", error);
  }
});

showManualTestGuide();

console.log("🏁 Notification system debug complete!");
console.log("Keep this console open and follow the manual test procedure above.");
