// Manual Test Script for Interest Acceptance
// Paste this into the browser console on the browse offers page

async function testInterestAcceptance() {
  console.log('🧪 [TEST] Starting manual interest acceptance test...');
  
  try {
    // Import stores
    const { useInterestStore } = await import('/src/stores/interestStore.js');
    const { useOfferStore } = await import('/src/stores/offerStore.js');
    const { useAuthStore } = await import('/src/stores/auth.js');
    
    const interestStore = useInterestStore();
    const offerStore = useOfferStore();
    const authStore = useAuthStore();
    
    console.log('✅ [TEST] Stores loaded');
    console.log('🔍 [TEST] Current user:', authStore.user?.id);
    console.log('🔍 [TEST] Available offers:', offerStore.offers.length);
    
    // Make sure socket listeners are initialized
    console.log('🔧 [TEST] Initializing socket listeners...');
    interestStore.initializeSocketListeners();
    offerStore.initializeSocketListeners();
    
    // Find the first offer where the user might have shown interest
    const testOffer = offerStore.offers[0];
    if (!testOffer) {
      console.warn('⚠️ [TEST] No offers found to test with');
      return;
    }
    
    console.log('🎯 [TEST] Using test offer:', testOffer.id, testOffer.title);
    
    // Create a mock acceptance payload
    const mockAcceptancePayload = {
      interestId: 'test-interest-' + Date.now(),
      offerId: testOffer.id,
      chatSessionId: 'test-chat-' + Date.now(),
      interestedUser: {
        userId: authStore.user?.id,
        username: authStore.user?.username,
        email: authStore.user?.email,
      },
      offerCreator: {
        userId: testOffer.creator.id,
        username: testOffer.creator.username,
        email: testOffer.creator.email,
      },
      offer: {
        id: testOffer.id,
        title: testOffer.title,
        description: testOffer.description,
        amount: testOffer.amount,
      },
    };
    
    console.log('📦 [TEST] Created mock payload:', mockAcceptancePayload);
    
    // Check the current state of the offer
    console.log('📊 [TEST] Offer state BEFORE:', {
      currentUserInterestStatus: testOffer.currentUserInterestStatus,
      chatSessionId: testOffer.chatSessionId,
    });
    
    // Get the centralized socket manager and manually trigger the event
    const centralizedManager = (await import('/src/services/centralizedSocketManager.js')).default;
    const socket = centralizedManager.getSocket();
    
    if (!socket) {
      console.error('❌ [TEST] No socket connection found');
      return;
    }
    
    console.log('🔌 [TEST] Socket found:', socket.id, 'Connected:', socket.connected);
    
    // Manually trigger the event as if it came from the server
    socket.emit('INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY', mockAcceptancePayload);
    
    // Also trigger it directly on the socket as if received from server
    socket.onevent({ 
      data: ['INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY', mockAcceptancePayload] 
    });
    
    console.log('🚀 [TEST] Event triggered manually');
    
    // Wait a moment for the event to be processed
    setTimeout(() => {
      const updatedOffer = offerStore.offers.find(o => o.id === testOffer.id);
      console.log('📊 [TEST] Offer state AFTER:', {
        currentUserInterestStatus: updatedOffer?.currentUserInterestStatus,
        chatSessionId: updatedOffer?.chatSessionId,
      });
      
      if (updatedOffer?.currentUserInterestStatus === 'ACCEPTED' && updatedOffer?.chatSessionId) {
        console.log('✅ [TEST] SUCCESS! Offer was updated correctly');
      } else {
        console.log('❌ [TEST] FAILED! Offer was not updated');
        console.log('🔍 [TEST] Expected: ACCEPTED status and chatSessionId');
        console.log('🔍 [TEST] Got:', updatedOffer?.currentUserInterestStatus, updatedOffer?.chatSessionId);
      }
    }, 100);
    
  } catch (error) {
    console.error('❌ [TEST] Error during test:', error);
  }
}

// Make it available globally
window.testInterestAcceptance = testInterestAcceptance;

console.log('🧪 [TEST] Manual test function loaded. Run testInterestAcceptance() to test.');

export { testInterestAcceptance };
