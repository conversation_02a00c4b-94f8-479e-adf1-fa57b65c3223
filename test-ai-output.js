const fs = require('fs');

// Check recent AI analysis outputs
const debugReportsPath = 'C:\\Code\\MUNygo\\backend\\src\\test\\fixtures';

console.log('Checking for recent AI analysis outputs...');

// Look for any test files that might show AI output
const testFiles = [
  'C:\\Code\\MUNygo\\backend\\src\\test\\routes\\debugReportRoutes.test.ts',
  'C:\\Code\\MUNygo\\backend\\src\\services\\aiService.ts'
];

testFiles.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      console.log(`\n=== ${file} ===`);
      
      // Look for mentions of AI analysis fields
      const lines = content.split('\n');
      lines.forEach((line, index) => {
        if (line.includes('stepsToReproduce') || 
            line.includes('expectedBehavior') || 
            line.includes('steps_to_reproduce') || 
            line.includes('expected_behavior')) {
          console.log(`Line ${index + 1}: ${line.trim()}`);
        }
      });
    }
  } catch (error) {
    console.log(`Could not read ${file}: ${error.message}`);
  }
});
