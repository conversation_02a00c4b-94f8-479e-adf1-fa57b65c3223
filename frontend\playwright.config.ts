import { defineConfig, devices } from '@playwright/test'

/**
 * Playwright Configuration for MUNygo - Mobile-First P2P Currency Exchange Platform
 * 
 * This configuration is optimized for:
 * - Mobile-first testing (320px-768px primary targets)
 * - Vue 3 + TypeScript + Vite application
 * - Real-time features via Socket.IO
 * - Multi-language support (Persian/English)
 * - Touch-friendly UI components
 */
export default defineConfig({
  // Test directory structure
  testDir: './tests/e2e',
  
  // Global test configuration
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  // Reporting
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['json', { outputFile: 'playwright-results.json' }],
    ['junit', { outputFile: 'playwright-results.xml' }],
  ],
  
  // Global test settings
  use: {
    // Base URL for your Vite dev server
    baseURL: 'http://localhost:5173',
    
    // Trace on first retry for debugging
    trace: 'on-first-retry',
    
    // Screenshots on failure
    screenshot: 'only-on-failure',
    
    // Video recording
    video: 'retain-on-failure',
    
    // Ignore HTTPS errors (useful for local development)
    ignoreHTTPSErrors: true,
    
    // Locale settings for your i18n testing
    locale: 'en-US',
    
    // Timezone (adjust for your target markets)
    timezoneId: 'America/Toronto', // For CAD market
  },
  
  // Test projects for different devices and scenarios
  projects: [
    // Mobile-first testing (primary focus)
    {
      name: 'Mobile Chrome',
      use: { 
        ...devices['Pixel 5'],
        // Override viewport for your mobile-first design
        viewport: { width: 375, height: 667 }
      },
    },
    
    {
      name: 'Mobile Safari',
      use: { 
        ...devices['iPhone 12'],
        viewport: { width: 390, height: 844 }
      },
    },
    
    // Small mobile devices (320px - your minimum target)
    {
      name: 'Small Mobile',
      use: {
        ...devices['Galaxy S9+'],
        viewport: { width: 320, height: 568 }
      },
    },
    
    // Tablet testing (768px+ progressive enhancement)
    {
      name: 'Tablet',
      use: {
        ...devices['iPad'],
        viewport: { width: 768, height: 1024 }
      },
    },
    
    // Desktop (1024px+ enhancement)
    {
      name: 'Desktop Chrome',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 }
      },
    },
    
    // Persian language testing
    {
      name: 'Mobile Chrome Persian',
      use: {
        ...devices['Pixel 5'],
        locale: 'fa-IR',
        viewport: { width: 375, height: 667 }
      },
    },
    
    // Accessibility testing
    {
      name: 'Mobile Accessibility',
      use: {
        ...devices['Pixel 5'],
        // Simulate reduced motion for users with vestibular disorders
        reducedMotion: 'reduce',
        // Simulate forced colors for users with vision impairments
        forcedColors: 'active',
      },
    },
    
    // Network conditions testing
    {
      name: 'Mobile Slow Network',
      use: {
        ...devices['Pixel 5'],
        // Simulate slow 3G for mobile users
        launchOptions: {
          args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        }
      },
    }
  ],
  
  // Web server configuration for your Vite dev server
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000, // 2 minutes timeout for server start
  },
  
  // Output directory
  outputDir: 'test-results/',
  
  // Global timeout settings
  timeout: 30 * 1000, // 30 seconds per test
  expect: {
    timeout: 5 * 1000, // 5 seconds for assertions
  },
  
  // Test file patterns
  testMatch: [
    '**/*.spec.ts',
    '**/*.test.ts',
    '**/*.e2e.ts'
  ],
  
  // Test ignore patterns
  testIgnore: [
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**'
  ]
})
