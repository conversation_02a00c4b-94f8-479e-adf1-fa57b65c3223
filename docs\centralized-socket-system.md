# Centralized Socket Management System

## Overview

This document explains the centralized Socket.IO management system implemented in the MUNygo frontend application. This system was designed to replace multiple competing socket instances with a single, coordinated approach to real-time communication.

## Background

### Previous Issue

```mermaid
flowchart TB
    subgraph OldSystem["OLD SYSTEM - Multiple Socket Instances"]
        subgraph Store1["Store 1 offerStore"]
            S1["socketService.getSocket()"]
            H1["Event Handlers"]
            DUP1["Duplicate listeners"]
        end
        
        subgraph Store2["Store 2 chatStore"]
            S2["socketService.getSocket()"]
            H2["Event Handlers"]
            DUP2["Duplicate listeners"]
        end
        
        subgraph Store3["Store 3 transactionStore"]
            S3["socketService.getSocket()"]
            H3["Event Handlers"]
            DUP3["Duplicate listeners"]
        end
        
        subgraph ConnMonitor["Connection Monitor"]
            CM["ConnectionStatus.vue"]
            STUCK["STUCK IN RECONNECTING<br/>Conflicting states"]
        end
    end
    
    subgraph Backend["Backend Server"]
        SERVER["Socket.IO Server<br/>Receiving mixed signals"]
    end
    
    %% Multiple competing connections
    S1 <-.->|Connection 1| SERVER
    S2 <-.->|Connection 2| SERVER
    S3 <-.->|Connection 3| SERVER
    
    %% Conflicting status reports
    S1 --> CM
    S2 --> CM
    S3 --> CM
    CM --> STUCK
    
    %% Styling for problems
    style STUCK fill:#ffebee,stroke:#f44336,stroke-width:3px
    style S1 fill:#ffcdd2,stroke:#f44336,stroke-width:2px
    style S2 fill:#ffcdd2,stroke:#f44336,stroke-width:2px
    style S3 fill:#ffcdd2,stroke:#f44336,stroke-width:2px
    style DUP1 fill:#ffcdd2,stroke:#f44336
    style DUP2 fill:#ffcdd2,stroke:#f44336
    style DUP3 fill:#ffcdd2,stroke:#f44336
    style SERVER fill:#ffcdd2,stroke:#f44336
```

### Solution

```mermaid
flowchart TB
    subgraph NewSystem["NEW SYSTEM - Centralized Management"]
        subgraph Stores["Multiple Stores"]
            ST1["offerStore"]
            ST2["chatStore"]
            ST3["transactionStore"]
            ST4["notificationStore"]
            ST5["myOffersStore"]
        end
        
        CSM["CentralizedSocketManager<br/>SINGLETON<br/>Single source of truth"]
        
        subgraph ConnHealth["Connection Health"]
            HEALTH["Connection Monitor"]
            STATUS["ACCURATE STATUS<br/>Real-time updates"]
        end
        
        subgraph Features["Key Features"]
            TYPEAFE["Type-safe events"]
            CLEANUP["Automatic cleanup"]
            ERRHAND["Error handling"]
        end
    end
    
    subgraph Backend2["Backend Server"]
        SERVER2["Socket.IO Server<br/>Single clean connection"]
    end
    
    %% All stores connect to single manager
    ST1 --> CSM
    ST2 --> CSM
    ST3 --> CSM
    ST4 --> CSM
    ST5 --> CSM
    
    %% Single connection to backend
    CSM <-->|Single Connection| SERVER2
    
    %% Accurate status monitoring
    CSM --> HEALTH
    HEALTH --> STATUS
    
    %% Feature connections
    CSM -.-> TYPEAFE
    CSM -.-> CLEANUP
    CSM -.-> ERRHAND
    
    %% Styling for success
    style CSM fill:#e8f5e8,stroke:#4caf50,stroke-width:3px
    style STATUS fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    style SERVER2 fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    style TYPEAFE fill:#f3e5f5,stroke:#9c27b0
    style CLEANUP fill:#f3e5f5,stroke:#9c27b0
    style ERRHAND fill:#f3e5f5,stroke:#9c27b0
```

## Architecture

### System Overview Diagram

```mermaid
flowchart TB
    subgraph Frontend["🖥️ Frontend Application"]
        subgraph UI["User Interface Layer"]
            Views[Views & Components]
            ConnStatus[ConnectionStatus.vue]
        end
        
        subgraph StoreLayer["📦 Pinia Store Layer"]
            AuthStore[authStore]
            OfferStore[offerStore]
            ChatStore[chatStore]
            TransStore[transactionStore]
            NotifStore[notificationStore]
            MyOffersStore[myOffersStore]
            InterestStore[interestStore]
            PayerNegStore[payerNegotiation]
        end
        
        subgraph SocketLayer["🔌 Socket Management Layer"]
            CSM[CentralizedSocketManager<br/>⭐ SINGLETON ⭐]
            EventTypes[Socket Event Types]
            EventHandlers[Event Handler Registry]
        end
    end
    
    subgraph Backend["🏗️ Backend Server"]
        API[Hono REST API]
        SocketServer[Socket.IO Server]
    end
    
    %% User interactions flow down
    Views --> StoreLayer
    
    %% All stores connect to centralized manager
    AuthStore --> CSM
    OfferStore --> CSM
    ChatStore --> CSM
    TransStore --> CSM
    NotifStore --> CSM
    MyOffersStore --> CSM
    InterestStore --> CSM
    PayerNegStore --> CSM
    
    %% Socket management
    CSM --> EventHandlers
    CSM <-.->|Real-time Events| SocketServer
    EventTypes -.-> CSM
    
    %% Connection status monitoring
    CSM -->|Connection State| ConnStatus
    ConnStatus --> Views
    
    %% HTTP API calls (separate from socket)
    StoreLayer -.->|HTTP Requests| API
      %% Styling
    style CSM fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style EventHandlers fill:#f3e5f5,stroke:#7b1fa2
    style SocketServer fill:#e8f5e8,stroke:#388e3c
    style API fill:#fff3e0,stroke:#f57c00
    ChatStore --> API
    TransStore --> API
    
    %% Event types
    EventTypes -.-> CSM
    
    style CSM fill:#ff9999
    style SOCKET fill:#99ccff
    style STATUS fill:#99ff99
```

### Core Components

#### 1. CentralizedSocketManager (`src/services/centralizedSocketManager.ts`)
The main singleton class that manages all socket communication.

```typescript
class CentralizedSocketManager {
  private socket: Socket | null = null;
  private handlers: EventHandlers = {};
  private initializationPromise: Promise<Socket> | null = null;

  // Key methods:
  on(event, handler): () => void      // Register event handler, returns unsubscribe function
  getSocket(): Socket | null          // Get current socket instance
  initializeSocket(): Promise<Socket> // Initialize socket connection
  disconnect(): void                  // Clean disconnect
}
```

#### 2. Event Type System (`src/types/socketEvents.ts`)
Centralized type definitions for all socket events and their payloads:

```typescript
// Event name constants
export const OFFER_CREATED = 'OFFER_CREATED';
export const INTEREST_RECEIVED = 'INTEREST_RECEIVED';
export const TRANSACTION_STATUS_UPDATED = 'TRANSACTION_STATUS_UPDATED';
// ... more events

// Type definitions for event payloads
export interface InterestReceivedPayload {
  interestId: string;
  offerId: string;
  // ... more properties
}
```

## Usage Patterns

### Event Registration Pattern

```mermaid
flowchart TD
    START(["Store Initialization"]) --> CHECK{"Are listeners<br/>already registered?"}
    
    CHECK -->|No| REGISTER["Register event listeners<br/>centralizedSocketManager.on()"]
    CHECK -->|Yes| SKIP["Skip registration<br/>Avoid duplicates"]
    
    REGISTER --> STORE["Store unsubscribe functions<br/>for cleanup"]
    STORE --> READY(["Listeners Active"])
    SKIP --> READY
    
    READY --> EVENT{"Event Received?"}
    EVENT -->|Yes| HANDLE["Execute event handler<br/>Type-safe processing"]
    EVENT -->|No| WAIT["Wait for events"]
    
    HANDLE --> VALIDATE{"Validate payload?"}
    VALIDATE -->|Valid| UPDATE["Update store state<br/>Trigger reactivity"]
    VALIDATE -->|Invalid| ERROR["Log error<br/>Skip update"]
    
    UPDATE --> UI["UI automatically updates<br/>Vue reactivity"]
    ERROR --> EVENT
    UI --> EVENT
    WAIT --> EVENT
    
    READY --> CLEANUP{"Cleanup triggered?<br/>Logout/unmount"}
    CLEANUP -->|Yes| UNSUB["Call unsubscribe functions<br/>Remove all handlers"]
    CLEANUP -->|No| EVENT
    
    UNSUB --> CLEAR["Clear unsubscribe references<br/>Null out variables"]
    CLEAR --> END(["Cleanup Complete"])
    
    %% Styling
    style REGISTER fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    style UNSUB fill:#ffcdd2,stroke:#f44336,stroke-width:2px
    style READY fill:#bbdefb,stroke:#2196f3,stroke-width:2px
    style VALIDATE fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    style ERROR fill:#ffcdd2,stroke:#f44336,stroke-width:1px
    style UPDATE fill:#c8e6c9,stroke:#4caf50,stroke-width:1px
```

### Store Integration Architecture

```mermaid
classDiagram
    class CentralizedSocketManager {
        -socket: Socket | null
        -handlers: Map~string, Function[]~
        -initializationPromise: Promise~Socket~ | null
        +on(event: string, handler: Function) UnsubscribeFunction
        +getSocket() Socket | null
        +initializeSocket() Promise~Socket~
        +disconnect() void
        -setupEventListeners() void
        -routeEvent(event: string, payload: any) void
    }
    
    class OfferStore {
        -offerStatusChangedUnsub: Function | null
        -offerUpdatedUnsub: Function | null
        -offerCreatedUnsub: Function | null
        +initializeSocketListeners() void
        +cleanupSocketListeners() void
        +handleOfferStatusChanged(payload) void
        +handleOfferUpdated(payload) void
        +handleOfferCreated(payload) void
    }
    
    class ChatStore {
        -messageUnsub: Function | null
        -systemMessageUnsub: Function | null
        +initializeSocketListeners() void
        +cleanupSocketListeners() void
        +handleChatMessage(payload) void
        +handleSystemMessage(payload) void
    }
    
    class EventTypes {
        <<interface>>
        +OFFER_CREATED: string
        +OFFER_UPDATED: string
        +INTEREST_RECEIVED: string
        +CHAT_MESSAGE_RECEIVE: string
        +TRANSACTION_STATUS_UPDATED: string
    }
    
    CentralizedSocketManager --|> EventTypes : implements routing
    OfferStore --> CentralizedSocketManager : registers events
    ChatStore --> CentralizedSocketManager : registers events
    OfferStore --|> EventTypes : uses constants
    ChatStore --|> EventTypes : uses constants
    
    note for CentralizedSocketManager "🎯 Singleton Pattern\n- Single socket instance\n- Type-safe event routing\n- Automatic cleanup\n- Error handling"
    note for OfferStore "📦 Standard Pattern\n- Unsubscribe functions\n- Cleanup on destroy\n- Type-safe handlers"
```

### 1. Registering Event Listeners

**✅ Correct Pattern:**
```typescript
// In a store or component
import centralizedSocketManager from '@/services/centralizedSocketManager';

export const useMyStore = defineStore('myStore', () => {
  let unsubscribeFunction: (() => void) | null = null;

  function initializeListeners() {
    // Register listener and store unsubscribe function
    unsubscribeFunction = centralizedSocketManager.on(
      SOME_EVENT, 
      handleSomeEvent
    );
  }

  function cleanup() {
    // Always cleanup when done
    if (unsubscribeFunction) {
      unsubscribeFunction();
      unsubscribeFunction = null;
    }
  }

  return { initializeListeners, cleanup };
});
```

**❌ Avoid This Pattern:**
```typescript
// Don't access socket directly for event registration
const socket = centralizedSocketManager.getSocket();
socket?.on(EVENT_NAME, handler); // No cleanup mechanism
```

### 2. Accessing Socket Instance

For cases where you need direct socket access (rare):

```typescript
// Only when you need raw socket access
const socket = centralizedSocketManager.getSocket();
if (socket) {
  // Use socket directly
  socket.emit('custom_event', data);
}
```

### 3. Store Integration

All stores follow this pattern:

```typescript
export const useExampleStore = defineStore('example', () => {
  // Unsubscribe functions for cleanup
  let unsubscribeEvent1: (() => void) | null = null;
  let unsubscribeEvent2: (() => void) | null = null;

  function initializeSocketListeners() {
    unsubscribeEvent1 = centralizedSocketManager.on(EVENT_1, handleEvent1);
    unsubscribeEvent2 = centralizedSocketManager.on(EVENT_2, handleEvent2);
  }

  function cleanup() {
    if (unsubscribeEvent1) {
      unsubscribeEvent1();
      unsubscribeEvent1 = null;
    }
    if (unsubscribeEvent2) {
      unsubscribeEvent2();
      unsubscribeEvent2 = null;
    }
  }

  return {
    initializeSocketListeners,
    cleanup
  };
});
```

## Event Flow

### 1. Connection Lifecycle

```mermaid
sequenceDiagram
    participant User as 👤 User
    participant AuthStore as 🔐 AuthStore
    participant CSM as 🎯 CentralizedSocketManager
    participant Backend as 🏗️ Backend Server
    participant Stores as 📦 Various Stores
    
    Note over User, Stores: Initial Connection Setup
    User->>+AuthStore: Login with credentials
    AuthStore->>+CSM: initializeSocket()
    
    alt Socket already exists
        CSM-->>AuthStore: Return existing socket
    else Create new connection
        CSM->>+Backend: Connect with JWT token
        Backend-->>-CSM: ✅ Connection established
        CSM->>CSM: Set up core event listeners
    end
    
    CSM->>+Stores: Notify connection ready
    Stores->>CSM: Register event handlers
    CSM-->>-Stores: Return unsubscribe functions
    CSM-->>-AuthStore: ✅ Socket ready
    AuthStore-->>-User: ✅ Login complete
    
    Note over CSM: Single socket instance manages<br/>all real-time communication
```

### 2. Event Handling Flow

```mermaid
sequenceDiagram
    participant Backend as 🏗️ Backend
    participant CSM as 🎯 CentralizedSocketManager
    participant Store as 📦 Store (e.g., offerStore)
    participant UI as 🖥️ UI Components
    
    Note over Backend, UI: Real-time Event Processing
    
    Backend->>+CSM: 📡 Emit OFFER_CREATED event
    Note right of Backend: Event includes typed payload
    
    CSM->>CSM: 🔍 Route to registered handlers
    CSM->>+Store: 📞 Call handleOfferCreated(payload)
    
    Note over Store: Type-safe payload processing
    Store->>Store: 💾 Update local state
    Store->>Store: 🔄 Trigger reactivity
    
    Store-->>-CSM: ✅ Handler complete
    Store->>UI: 🔄 Reactive state update
    UI->>UI: 🎨 Re-render with new data
    
    CSM-->>-Backend: ✅ Event processed
    
    Note over CSM: Centralized error handling<br/>and type safety
```

### 3. Cleanup Lifecycle

```mermaid
sequenceDiagram
    participant User as 👤 User
    participant Component as 🖥️ Component/Page
    participant Store as 📦 Store
    participant CSM as 🎯 CentralizedSocketManager
    
    Note over User, CSM: Cleanup & Resource Management
    
    User->>+Component: 🚪 Logout / Navigate away
    Component->>+Store: 🧹 cleanup()
    
    Note over Store: Execute cleanup functions
    Store->>Store: 📞 Call all unsubscribe functions
    Store->>CSM: 🗑️ Remove event listeners
    
    CSM->>CSM: 🧹 Clean up handlers array
    CSM->>CSM: 📊 Update handler count
    
    alt No more handlers for this event
        CSM->>CSM: 🔇 Remove socket listener
    else Other handlers still exist
        CSM->>CSM: ⏸️ Keep socket listener active
    end
    
    Store-->>-Component: ✅ Cleanup complete
    Component-->>-User: 🎯 Navigation complete
    
    Note over Store: Prevents memory leaks<br/>and orphaned listeners
```

## Supported Events

### Core Application Events
| Event | Store | Purpose |
|-------|-------|---------|
| `OFFER_CREATED` | offerStore | New offer added |
| `OFFER_UPDATED` | offerStore | Offer modified |
| `OFFER_STATUS_CHANGED` | offerStore | Offer status changed |
| `INTEREST_RECEIVED` | myOffersStore | User showed interest in offer |
| `INTEREST_PROCESSED` | interestStore | Interest accepted/declined |

### Chat Events
| Event | Store | Purpose |
|-------|-------|---------|
| `CHAT_MESSAGE_RECEIVE` | chatStore | New chat message |
| `SYSTEM_MESSAGE_RECEIVE` | chatStore | System message |

### Transaction Events
| Event | Store | Purpose |
|-------|-------|---------|
| `TRANSACTION_STATUS_UPDATED` | transactionStore | Transaction status changed |
| `NEGOTIATION_STATE_UPDATED` | payerNegotiation | Negotiation updated |
| `NEGOTIATION_FINALIZED` | payerNegotiation | Negotiation completed |

### Notification Events
| Event | Store | Purpose |
|-------|-------|---------|
| `NEW_NOTIFICATION` | notificationStore | General notifications |

## Migration Guide

### From Old SocketService

If you encounter legacy code using the old `socketService.ts`:

**❌ Old Pattern:**
```typescript
import { getSocket } from '@/services/socketService';

const socket = getSocket();
socket.on(EVENT, handler);
```

**✅ New Pattern:**
```typescript
import centralizedSocketManager from '@/services/centralizedSocketManager';

const unsubscribe = centralizedSocketManager.on(EVENT, handler);
// Remember to call unsubscribe() during cleanup
```

### Migration Benefits Comparison

```mermaid
flowchart LR
    subgraph Old["OLD SYSTEM Issues"]
        direction TB
        O1["Multiple socket instances"]
        O2["Connection conflicts"]
        O3["Memory leaks"]
        O4["Duplicate event listeners"]
        O5["Inconsistent state"]
        O6["Hard to debug"]
        O7["No type safety"]
        O8["Manual cleanup"]
    end
    
    subgraph Migration["MIGRATION"]
        direction TB
        M1["Update imports"]
        M2["Replace event registration"]
        M3["Add cleanup functions"]
        M4["Test connections"]
    end
    
    subgraph New["NEW SYSTEM Benefits"]
        direction TB
        N1["Single socket instance"]
        N2["Reliable connections"]
        N3["Automatic cleanup"]
        N4["Efficient event handling"]
        N5["Consistent state"]
        N6["Easy debugging"]
        N7["Full type safety"]
        N8["Automatic cleanup"]
    end
    
    Old -->|Migrate| Migration
    Migration -->|Results in| New
    
    %% Problem-solution connections
    O1 -.->|Solved by| N1
    O2 -.->|Solved by| N2
    O3 -.->|Solved by| N3
    O4 -.->|Solved by| N4
    O5 -.->|Solved by| N5
    O6 -.->|Solved by| N6
    O7 -.->|Solved by| N7
    O8 -.->|Solved by| N8
    
    %% Styling
    style Old fill:#ffebee,stroke:#f44336,stroke-width:2px
    style Migration fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    style New fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
```

## Best Practices

### 1. Always Use Unsubscribe Functions
```typescript
// ✅ Good
const unsubscribe = centralizedSocketManager.on(EVENT, handler);
// Later...
unsubscribe();

// ❌ Bad - memory leak
centralizedSocketManager.on(EVENT, handler); // No cleanup
```

### 2. Proper Error Handling
```typescript
function handleSocketEvent(payload: EventPayload) {
  try {
    // Handle event
    processEvent(payload);
  } catch (error) {
    console.error('[StoreName] Error handling socket event:', error);
    // Don't let errors break the socket connection
  }
}
```

### 3. Type Safety
```typescript
// Always use typed event handlers
import type { InterestReceivedPayload } from '@/types/socketEvents';

function handleInterestReceived(payload: InterestReceivedPayload) {
  // TypeScript will enforce payload structure
}
```

### 4. Conditional Listening
```typescript
// Only register listeners when needed
function conditionalSetup() {
  if (shouldListenToEvents && !unsubscribeFunction) {
    unsubscribeFunction = centralizedSocketManager.on(EVENT, handler);
  }
}
```

## Debugging

### 1. Enable Debug Logging
The centralized socket manager includes comprehensive logging:

```typescript
// Look for these console messages:
// 🚀 [CentralizedSocketManager] initializeSocket called
// 🔌 [CentralizedSocketManager] Registering handler for event: EVENT_NAME
// ✅ [CentralizedSocketManager] Connected to server
// 🔌 [CentralizedSocketManager] Disconnecting socket
```

### 2. Check Event Registration
```typescript
// In browser console, inspect the manager
console.log(centralizedSocketManager);
// Check handlers object to see registered events
```

### 3. Connection Testing
Use the built-in connection testing utilities:

```typescript
// In browser console
window.testConnection.testDisconnect();     // Test disconnect
window.testConnection.testReconnect();      // Test reconnect
window.testConnection.getState();           // Get current state
```

## Common Issues & Solutions

### Issue: Event Handlers Not Firing
**Solution:** Check that the event name constants match between frontend and backend.

### Issue: Memory Leaks
**Solution:** Ensure all unsubscribe functions are called during cleanup.

### Issue: Duplicate Events
**Solution:** Check that listeners aren't registered multiple times without cleanup.

### Issue: Connection Status Stuck
**Solution:** This should be resolved by the centralized system, but check that only one socket manager is active.

## Testing

### Unit Tests
Mock the centralized socket manager in tests:

```typescript
const mockCentralizedSocketManager = {
  on: vi.fn(() => vi.fn()), // Return unsubscribe function
  getSocket: vi.fn(() => mockSocket),
  initializeSocket: vi.fn(),
  disconnect: vi.fn(),
};

vi.mock('@/services/centralizedSocketManager', () => ({
  default: mockCentralizedSocketManager,
}));
```

### Integration Tests
Test the complete event flow from backend emission to frontend handling.

## Future Considerations

### 1. Adding New Events
1. Add event constant to `src/types/socketEvents.ts`
2. Add payload interface if needed
3. Add to EventHandlers type in centralizedSocketManager
4. Add event listener setup in the manager
5. Handle in appropriate store

### 2. Performance Optimization
- Consider event batching for high-frequency events
- Implement event priority systems if needed
- Add event queueing for offline scenarios

### 3. Error Recovery
- Implement automatic retry logic for failed connections
- Add fallback mechanisms for critical events
- Consider event replay for missed messages

## Related Files

- `src/services/centralizedSocketManager.ts` - Main socket manager
- `src/types/socketEvents.ts` - Event type definitions
- `src/stores/connection.ts` - Connection status management
- `src/components/ConnectionStatus.vue` - Connection indicator UI
- `src/utils/connectionTester.ts` - Testing utilities
- `src/services/socketService.ts` - ⚠️ DEPRECATED - Do not use

## Support

For questions about the socket system:
1. Check this documentation first
2. Review the centralized socket manager implementation
3. Check existing store implementations for patterns
4. Use the connection testing utilities for debugging

---

*Last updated: May 25, 2025*
*Migration completed: All stores migrated from legacy socketService*
