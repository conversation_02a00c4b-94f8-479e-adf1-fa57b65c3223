import apiClient from './apiClient';
import { useClientLogger } from '@/composables/useClientLogger';

const logger = useClientLogger();

export const authService = {
  /**
   * Resend verification email for unauthenticated users
   * @param email - User's email address
   */
  async resendVerificationEmail(email: string): Promise<{ message: string }> {
    logger.logInfo('Resending verification email for unauthenticated user', { email });
    
    try {
      const response = await apiClient.post('/auth/resend-verification-email-public', {
        email
      });
      logger.logInfo('Verification email resent successfully');
      return response.data;
    } catch (error) {
      logger.logError('Failed to resend verification email', error, { email });
      throw error;
    }
  },

  /**
   * Resend verification email for authenticated users (existing route)
   */
  async resendVerificationEmailAuthenticated(): Promise<{ message: string }> {
    logger.logInfo('Resending verification email for authenticated user');
    
    try {
      const response = await apiClient.post('/auth/resend-verification-email');
      logger.logInfo('Verification email resent successfully for authenticated user');
      return response.data;
    } catch (error) {
      logger.logError('Failed to resend verification email for authenticated user', error);
      throw error;
    }
  }
};
