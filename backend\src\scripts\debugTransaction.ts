import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugTransaction() {
  const transactionId = 'cmckn520l000jvlv4cvit2ch0';
  
  console.log('Checking if transaction exists...');
  
  try {
    // Check if transaction exists
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        payerNegotiation: true,
        chatSession: true
      }
    });
    
    if (!transaction) {
      console.log('❌ Transaction not found in database');
      return;
    }
    
    console.log('✅ Transaction found:');
    console.log('  - ID:', transaction.id);
    console.log('  - Status:', transaction.status);
    console.log('  - Currency A Provider:', transaction.currencyAProviderId);
    console.log('  - Currency B Provider:', transaction.currencyBProviderId);
    console.log('  - Chat Session ID:', transaction.chatSessionId);
    console.log('  - Has Payer Negotiation:', !!transaction.payerNegotiation);
    
    if (transaction.payerNegotiation) {
      console.log('  - Negotiation Status:', transaction.payerNegotiation.negotiationStatus);
      console.log('  - Party A Status:', transaction.payerNegotiation.partyA_receivingInfoStatus);
      console.log('  - Party B Status:', transaction.payerNegotiation.partyB_receivingInfoStatus);
    }
    
    // Check test users
    console.log('\nChecking test users...');
    const testUsers = await prisma.user.findMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
      },
      select: {
        id: true,
        email: true,
        username: true
      }
    });
    
    console.log('Test users found:', testUsers.length);
    testUsers.forEach(user => {
      console.log(`  - ${user.email} (${user.username}) - ID: ${user.id}`);
    });
    
    // Check if transaction involves test users
    const isTestUserTransaction = testUsers.some(user => 
      user.id === transaction.currencyAProviderId || user.id === transaction.currencyBProviderId
    );
    
    console.log('Is test user transaction:', isTestUserTransaction);
    
  } catch (error) {
    console.error('Error checking transaction:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugTransaction();
