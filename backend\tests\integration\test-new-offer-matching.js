const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testNewOfferMatching() {
  console.log('=== TESTING NEW OFFER MATCHING ===\n');
  
  try {
    // 1. Check current state
    const beforeOffers = await prisma.offer.findMany({
      where: { status: 'ACTIVE' },
      select: { id: true, type: true, amount: true, baseRate: true, lastMatchedAt: true, user: { select: { username: true } } }
    });
    
    console.log(`Current active offers (${beforeOffers.length}):`);
    beforeOffers.forEach(offer => {
      const lastMatched = offer.lastMatchedAt ? new Date(offer.lastMatchedAt).toLocaleTimeString() : 'Never';
      console.log(`- ${offer.user.username}: ${offer.type} ${offer.amount}@${offer.baseRate} (last matched: ${lastMatched})`);
    });
    
    // 2. Simulate what immediate matching would find for a new BUY 100@100 offer
    console.log('\n=== SIMULATING NEW BUY 100@100 OFFER ===');
    
    const potentialMatches = await prisma.offer.findMany({
      where: {
        type: 'SELL',
        currencyPair: 'CAD-IRR',
        status: 'ACTIVE',
        amount: 100,
        baseRate: {
          gte: 100 * 0.99,
          lte: 100 * 1.01
        }
      },
      include: {
        user: { select: { username: true } }
      }
    });
    
    console.log(`Found ${potentialMatches.length} potential matches:`);
    potentialMatches.forEach(offer => {
      const lastMatched = offer.lastMatchedAt ? new Date(offer.lastMatchedAt).toLocaleTimeString() : 'Never';
      console.log(`- ${offer.user.username}: ${offer.type} ${offer.amount}@${offer.baseRate} (last matched: ${lastMatched})`);
    });
    
    // 3. Check what periodic job would process right now
    console.log('\n=== WHAT PERIODIC JOB WOULD PROCESS ===');
    
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const offersForPeriodicJob = await prisma.offer.findMany({
      where: {
        status: 'ACTIVE',
        OR: [
          { lastMatchedAt: null },
          { lastMatchedAt: { lt: fiveMinutesAgo } }
        ]
      },
      include: {
        user: { select: { username: true } }
      }
    });
    
    console.log(`Offers that periodic job would process: ${offersForPeriodicJob.length}`);
    offersForPeriodicJob.forEach(offer => {
      const lastMatched = offer.lastMatchedAt ? new Date(offer.lastMatchedAt).toLocaleTimeString() : 'Never';
      console.log(`- ${offer.user.username}: ${offer.type} ${offer.amount}@${offer.baseRate} (last matched: ${lastMatched})`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testNewOfferMatching();
