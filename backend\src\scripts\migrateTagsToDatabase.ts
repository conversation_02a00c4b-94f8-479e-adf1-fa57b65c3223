import { PrismaClient } from '@prisma/client';
import { TagService } from '../services/tagService';

const prisma = new PrismaClient();
const tagService = new TagService(prisma);

interface HardcodedReportType {
  value: string;
  icon: string;
  color: string;
  tags: string[];
}

// Hardcoded report types and tags from the frontend
const reportTypesData: HardcodedReportType[] = [
  {
    value: 'BUG', // Updated to match enum
    icon: 'bug',
    color: '#ef4444',
    tags: ['urgent', 'fix-needed', 'error']
  },
  {
    value: 'FEATURE_REQUEST', // Updated to match enum
    icon: 'lightbulb',
    color: '#3b82f6',
    tags: ['enhancement', 'new-feature', 'idea']
  },
  {
    value: 'PERFORMANCE', // Updated to match enum
    icon: 'zap',
    color: '#f59e0b',
    tags: ['slow', 'optimization', 'speed']
  },
  {
    value: 'UI_UX', // Updated to match enum
    icon: 'palette',
    color: '#8b5cf6',
    tags: ['design', 'user-experience', 'interface']
  },
  {
    value: 'IMPROVEMENT', // Updated to match enum
    icon: 'trending-up',
    color: '#10b981',
    tags: ['enhancement', 'better-way', 'suggestion']
  },
  {
    value: 'QUESTION', // Updated to match enum
    icon: 'help-circle',
    color: '#6b7280',
    tags: ['help', 'unclear', 'documentation']
  },  {
    value: 'OTHER', // Updated to match enum
    icon: 'more',
    color: '#6b7280',
    tags: ['miscellaneous', 'general']
  }
];

async function migrateTagsToDatabase(): Promise<void> {
  console.log('🚀 Starting tag migration to database...');

  try {
    // Step 1: Create default categories
    console.log('📂 Creating tag categories...');
    await tagService.ensureDefaultCategories();

    // Step 2: Migrate existing hardcoded tags with proper translations
    console.log('🏷️  Migrating hardcoded tags...');
    
    // Get all unique tags from all report types
    const allUniqueTags = new Set<string>();
    reportTypesData.forEach(reportType => {
      reportType.tags.forEach(tag => allUniqueTags.add(tag));
    });

    console.log(`Found ${allUniqueTags.size} unique tags to migrate:`, Array.from(allUniqueTags));    // Create tags with translations and associations
    for (const tag of allUniqueTags) {
      const displayName = {
        en: getEnglishDisplayName(tag),
        fa: getFarsiDisplayName(tag)
      };

      const description = {
        en: getEnglishDescription(tag),
        fa: getFarsiDescription(tag)
      };

      // Determine category based on tag content
      const categoryName = getCategoryForTag(tag);

      try {
        // Check if tag already exists
        const existingTag = await prisma.tag.findUnique({
          where: { name: tag }
        });

        let createdTag;
        if (existingTag) {
          console.log(`⚠️  Tag already exists: ${tag} (${existingTag.id})`);
          createdTag = existingTag;
        } else {
          createdTag = await tagService.createPredefinedTag({
            name: tag,
            displayName,
            description,
            categoryName,
            color: getColorForTag(tag),
            icon: getIconForTag(tag),
            weight: getWeightForTag(tag),
            isActive: true,
            isSystem: true // Mark as system tags since they're migrated from hardcoded values
          });
          console.log(`✅ Created tag: ${tag} (${createdTag.id})`);
        }

        // Create associations with report types
        for (const reportType of reportTypesData) {
          if (reportType.tags.includes(tag)) {
            await prisma.tagReportTypeAssociation.upsert({
              where: {
                tagId_reportType: {
                  tagId: createdTag.id,
                  reportType: reportType.value
                }
              },
              update: {},
              create: {
                tagId: createdTag.id,
                reportType: reportType.value,
                weight: getReportTypeWeight(tag, reportType.value)
              }
            });
            console.log(`   🔗 Associated with report type: ${reportType.value}`);
          }
        }

      } catch (error) {
        console.error(`❌ Error processing tag ${tag}:`, error);
      }
    }// Step 3: Migrate existing debug report tags to use the new system
    console.log('📊 Migrating existing debug report tags...');
    
    const existingReportTags = await prisma.debugReportTag.findMany({
      where: {
        tagId: null, // Tags that don't have a tagId (old system)
      },      include: {
        report: {
          select: {
            id: true,
            type: true,
            title: true
          }
        }
      }
    });

    console.log(`Found ${existingReportTags.length} existing report tags to migrate`);
    
    for (const reportTag of existingReportTags) {
      try {
        // If tagName exists, try to match it to a new tag
        if (reportTag.tagName && reportTag.tagName.trim() !== '') {
          const matchingTag = await prisma.tag.findFirst({
            where: {
              name: reportTag.tagName
            }
          });

          if (matchingTag) {
            await prisma.debugReportTag.update({
              where: { id: reportTag.id },
              data: {
                tagId: matchingTag.id,
              }
            });
            console.log(`✅ Migrated report tag: ${reportTag.tagName} -> ${matchingTag.id}`);
          } else {
            console.log(`⚠️  No matching tag found for: ${reportTag.tagName}`);
          }
        } else {
          // For records with null/empty tagName, assign default tags based on report type and origin
          console.log(`🔧 Handling orphaned tag record ${reportTag.id} (origin: ${reportTag.origin})`);
          
          let suggestedTagName: string;
            // Assign default tags based on origin and report type
          if (reportTag.origin === 'AI_SUGGESTED') {
            suggestedTagName = 'miscellaneous'; // Default for AI-suggested tags without name
          } else if (reportTag.origin === 'PREDEFINED') {
            // Use report type to determine appropriate tag
            const reportType = reportTag.report?.type;
            switch (reportType) {
              case 'BUG':
                suggestedTagName = 'error';
                break;
              case 'FEATURE_REQUEST':
                suggestedTagName = 'enhancement';
                break;
              case 'PERFORMANCE':
                suggestedTagName = 'optimization';
                break;
              case 'UI_UX':
                suggestedTagName = 'design';
                break;
              case 'IMPROVEMENT':
                suggestedTagName = 'enhancement';
                break;
              case 'QUESTION':
                suggestedTagName = 'help';
                break;
              default:
                suggestedTagName = 'general';
            }
          } else {
            suggestedTagName = 'general'; // Default fallback
          }
          
          // Find the matching tag
          const defaultTag = await prisma.tag.findFirst({
            where: {
              name: suggestedTagName
            }
          });
          
          if (defaultTag) {
            await prisma.debugReportTag.update({
              where: { id: reportTag.id },
              data: {
                tagId: defaultTag.id,
                tagName: suggestedTagName, // Set tagName for backward compatibility
              }
            });
            console.log(`✅ Assigned default tag to orphaned record: ${suggestedTagName} -> ${defaultTag.id}`);
          } else {
            console.log(`❌ Could not find default tag: ${suggestedTagName}`);
          }
        }
      } catch (error) {
        console.error(`❌ Error migrating report tag ${reportTag.id}:`, error);
      }
    }

    console.log('🎉 Tag migration completed successfully!');

  } catch (error) {
    console.error('💥 Migration failed:', error);
    throw error;
  }
}

// Helper functions for translations and categorization
function getEnglishDisplayName(tag: string): string {
  const displayNames: Record<string, string> = {
    'urgent': 'Urgent',
    'fix-needed': 'Fix Needed',
    'error': 'Error',
    'enhancement': 'Enhancement',
    'new-feature': 'New Feature',
    'idea': 'Idea',
    'slow': 'Slow Performance',
    'optimization': 'Optimization',
    'speed': 'Speed Issue',
    'design': 'Design',
    'user-experience': 'User Experience',
    'interface': 'Interface',
    'better-way': 'Better Way',
    'suggestion': 'Suggestion',
    'help': 'Help Needed',
    'unclear': 'Unclear',
    'documentation': 'Documentation',
    'miscellaneous': 'Miscellaneous',
    'general': 'General'
  };
  return displayNames[tag] || tag.charAt(0).toUpperCase() + tag.slice(1).replace('-', ' ');
}

function getFarsiDisplayName(tag: string): string {
  const displayNames: Record<string, string> = {
    'urgent': 'فوری',
    'fix-needed': 'نیاز به رفع',
    'error': 'خطا',
    'enhancement': 'بهبود',
    'new-feature': 'ویژگی جدید',
    'idea': 'ایده',
    'slow': 'کند',
    'optimization': 'بهینه‌سازی',
    'speed': 'مشکل سرعت',
    'design': 'طراحی',
    'user-experience': 'تجربه کاربری',
    'interface': 'رابط کاربری',
    'better-way': 'راه بهتر',
    'suggestion': 'پیشنهاد',
    'help': 'نیاز به کمک',
    'unclear': 'نامشخص',
    'documentation': 'مستندات',
    'miscellaneous': 'متفرقه',
    'general': 'عمومی'
  };
  return displayNames[tag] || tag;
}

function getEnglishDescription(tag: string): string {
  const descriptions: Record<string, string> = {
    'urgent': 'Issues that require immediate attention',
    'fix-needed': 'Problems that need to be fixed',
    'error': 'System errors and exceptions',
    'enhancement': 'Improvements to existing features',
    'new-feature': 'Requests for new functionality',
    'idea': 'Creative suggestions and ideas',
    'slow': 'Performance issues related to speed',
    'optimization': 'Areas that can be optimized',
    'speed': 'Speed-related performance problems',
    'design': 'Visual design and layout issues',
    'user-experience': 'User experience improvements',
    'interface': 'User interface problems',
    'better-way': 'Suggestions for better approaches',
    'suggestion': 'General suggestions for improvement',
    'help': 'Requests for help or clarification',
    'unclear': 'Unclear or confusing aspects',
    'documentation': 'Documentation-related issues',
    'miscellaneous': 'Various other issues',
    'general': 'General feedback and comments'
  };
  return descriptions[tag] || `Issues related to ${tag.replace('-', ' ')}`;
}

function getFarsiDescription(tag: string): string {
  const descriptions: Record<string, string> = {
    'urgent': 'مسائلی که نیاز به توجه فوری دارند',
    'fix-needed': 'مشکلاتی که نیاز به رفع دارند',
    'error': 'خطاهای سیستم و استثناها',
    'enhancement': 'بهبودهای ویژگی‌های موجود',
    'new-feature': 'درخواست‌های عملکرد جدید',
    'idea': 'پیشنهادات و ایده‌های خلاقانه',
    'slow': 'مشکلات عملکرد مربوط به سرعت',
    'optimization': 'مناطقی که می‌توانند بهینه شوند',
    'speed': 'مشکلات عملکرد مربوط به سرعت',
    'design': 'مشکلات طراحی و چیدمان بصری',
    'user-experience': 'بهبودهای تجربه کاربری',
    'interface': 'مشکلات رابط کاربری',
    'better-way': 'پیشنهادات برای روش‌های بهتر',
    'suggestion': 'پیشنهادات عمومی برای بهبود',
    'help': 'درخواست‌های کمک یا توضیح',
    'unclear': 'جنبه‌های نامشخص یا گیج‌کننده',
    'documentation': 'مسائل مربوط به مستندات',
    'miscellaneous': 'مسائل مختلف دیگر',
    'general': 'بازخورد و نظرات عمومی'
  };
  return descriptions[tag] || `مسائل مربوط به ${tag}`;
}

function getCategoryForTag(tag: string): string {
  const categoryMap: Record<string, string> = {
    'urgent': 'Priority',
    'fix-needed': 'Priority',
    'error': 'Technical',
    'enhancement': 'Improvement',
    'new-feature': 'Feature',
    'idea': 'Feature',
    'slow': 'Performance',
    'optimization': 'Performance',
    'speed': 'Performance',
    'design': 'UI/UX',
    'user-experience': 'UI/UX',
    'interface': 'UI/UX',
    'better-way': 'Improvement',
    'suggestion': 'Improvement',
    'help': 'Support',
    'unclear': 'Support',
    'documentation': 'Support',
    'miscellaneous': 'General',
    'general': 'General'
  };
  return categoryMap[tag] || 'General';
}

function getColorForTag(tag: string): string {
  const colorMap: Record<string, string> = {
    'urgent': '#ef4444',
    'fix-needed': '#ef4444',
    'error': '#dc2626',
    'enhancement': '#10b981',
    'new-feature': '#3b82f6',
    'idea': '#8b5cf6',
    'slow': '#f59e0b',
    'optimization': '#f59e0b',
    'speed': '#f59e0b',
    'design': '#8b5cf6',
    'user-experience': '#8b5cf6',
    'interface': '#8b5cf6',
    'better-way': '#10b981',
    'suggestion': '#10b981',
    'help': '#6b7280',
    'unclear': '#6b7280',
    'documentation': '#6b7280',
    'miscellaneous': '#6b7280',
    'general': '#6b7280'
  };
  return colorMap[tag] || '#6b7280';
}

function getIconForTag(tag: string): string {
  const iconMap: Record<string, string> = {
    'urgent': 'alert-triangle',
    'fix-needed': 'tool',
    'error': 'x-circle',
    'enhancement': 'trending-up',
    'new-feature': 'plus-circle',
    'idea': 'lightbulb',
    'slow': 'clock',
    'optimization': 'zap',
    'speed': 'zap',
    'design': 'palette',
    'user-experience': 'user',
    'interface': 'monitor',
    'better-way': 'arrow-up',
    'suggestion': 'message-circle',
    'help': 'help-circle',
    'unclear': 'question-mark',
    'documentation': 'book',
    'miscellaneous': 'more-horizontal',
    'general': 'circle'
  };
  return iconMap[tag] || 'tag';
}

function getWeightForTag(tag: string): number {
  const weightMap: Record<string, number> = {
    'urgent': 100,
    'fix-needed': 90,
    'error': 95,
    'enhancement': 60,
    'new-feature': 70,
    'idea': 50,
    'slow': 80,
    'optimization': 60,
    'speed': 80,
    'design': 50,
    'user-experience': 60,
    'interface': 50,
    'better-way': 40,
    'suggestion': 40,
    'help': 30,
    'unclear': 30,
    'documentation': 20,
    'miscellaneous': 10,
    'general': 10
  };
  return weightMap[tag] || 50;
}

function getReportTypeWeight(tag: string, reportType: string): number {
  // Higher weight means more relevant for this report type
  const weights: Record<string, Record<string, number>> = {
    'bug': {
      'urgent': 100,
      'fix-needed': 100,
      'error': 100
    },
    'feature-request': {
      'enhancement': 100,
      'new-feature': 100,
      'idea': 100
    },
    'performance': {
      'slow': 100,
      'optimization': 100,
      'speed': 100
    },
    'ui-ux': {
      'design': 100,
      'user-experience': 100,
      'interface': 100
    },
    'improvement': {
      'enhancement': 100,
      'better-way': 100,
      'suggestion': 100
    },
    'question': {
      'help': 100,
      'unclear': 100,
      'documentation': 100
    },
    'other': {
      'miscellaneous': 100,
      'general': 100
    }
  };

  return weights[reportType]?.[tag] || 50;
}

// Run the migration
if (require.main === module) {
  migrateTagsToDatabase()
    .then(() => {
      console.log('✅ Tag migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Tag migration failed:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

export { migrateTagsToDatabase };
