# Technical Documentation Template Prompt

## Purpose
This prompt template is designed to help generate comprehensive technical documentation for system changes, migrations, or new implementations. It follows the pattern used successfully for the Centralized Socket Management System documentation.

## Prompt Template

```
Please create comprehensive technical documentation for [SYSTEM/FEATURE NAME]. Follow this structure and approach:

## Documentation Requirements

### 1. Executive Summary
- Brief overview of what was implemented/changed
- Key problem it solves
- Main benefits achieved

### 2. Background & Problem Analysis
- **Previous Issue**: What was wrong with the old system?
- **Impact**: How did the problem affect users/developers?
- **Root Cause**: Why did the problem exist?

### 3. Visual Problem-Solution Comparison
Create Mermaid diagrams showing:
- **Before**: Current/old system with clear problem indicators
- **After**: New system with improvements highlighted
- Use flowcharts with color coding (red for problems, green for solutions)

### 4. Architecture Documentation
Include these diagram types as appropriate:
- **System Overview**: Flowchart showing overall architecture
- **Component Relationships**: Class diagram or component diagram
- **Process Flows**: Sequence diagrams for key interactions
- **State Management**: Flowcharts showing state transitions

### 5. Implementation Details
- **Core Components**: Key classes, services, or modules
- **API/Interface Design**: How components interact
- **Data Flow**: How information moves through the system
- **Configuration**: Setup and configuration requirements

### 6. Usage Patterns & Best Practices
- **Correct Usage Examples**: Code samples showing proper implementation
- **Anti-patterns**: What to avoid and why
- **Common Mistakes**: Typical errors and how to prevent them
- **Performance Considerations**: Optimization tips

### 7. Migration Guide (if applicable)
- **Step-by-step process**: How to migrate from old to new system
- **Breaking Changes**: What needs to be updated
- **Compatibility**: Backward compatibility considerations
- **Migration Benefits**: Visual comparison of before/after

### 8. Event/Message/API Reference
- **Complete Event List**: All events, messages, or API endpoints
- **Type Definitions**: TypeScript interfaces or schemas
- **Payload Examples**: Sample data structures
- **Error Handling**: How errors are managed

### 9. Troubleshooting & Debugging
- **Common Issues**: Known problems and solutions
- **Debugging Tools**: How to investigate problems
- **Monitoring**: What to watch for in production
- **Performance Metrics**: How to measure success

### 10. Testing Strategy
- **Unit Testing**: How to test individual components
- **Integration Testing**: How to test component interactions
- **Manual Testing**: Steps for manual verification
- **Test Coverage**: What should be tested

## Mermaid Diagram Guidelines

### Diagram Types to Use:
1. **Flowcharts** (`flowchart TB/LR`) for:
   - System architecture overviews
   - Process flows
   - Decision trees
   - Problem-solution comparisons

2. **Sequence Diagrams** (`sequenceDiagram`) for:
   - API call flows
   - Event handling sequences
   - User interaction flows
   - Lifecycle processes

3. **Class Diagrams** (`classDiagram`) for:
   - Component relationships
   - Interface definitions
   - Inheritance hierarchies
   - Dependency structures

### Visual Design Rules:
- **Use emojis sparingly**: Only in titles and comments, avoid in node definitions
- **Color coding**: Red for problems, green for solutions, blue for processes, orange for warnings
- **Clear labeling**: Use descriptive names for nodes and connections
- **Consistent styling**: Apply consistent colors and styles throughout
- **Avoid parser issues**: Use quoted strings for complex text, avoid special characters in node IDs

### Example Mermaid Structure:
```mermaid
flowchart TB
    subgraph Problem["OLD SYSTEM - Issues"]
        P1["Problem 1"]
        P2["Problem 2"]
    end
    
    subgraph Solution["NEW SYSTEM - Benefits"]
        S1["Solution 1"]
        S2["Solution 2"]
    end
    
    Problem -->|Migration| Solution
    
    style Problem fill:#ffebee,stroke:#f44336
    style Solution fill:#e8f5e8,stroke:#4caf50
```

## Content Guidelines

### Writing Style:
- **Clear and concise**: Avoid jargon, explain technical terms
- **Developer-focused**: Write for the technical audience who will use/maintain the system
- **Problem-solution oriented**: Always explain why changes were made
- **Example-driven**: Include code samples and real-world scenarios

### Code Examples:
- **Before/After comparisons**: Show old vs new patterns
- **Complete examples**: Don't just show fragments, provide working code
- **Type safety**: Include TypeScript types where applicable
- **Error handling**: Show proper error handling patterns

### Structure Requirements:
- **Logical flow**: Information should build from basic to advanced
- **Cross-references**: Link related sections
- **Table of contents**: For long documents
- **Summary sections**: Key takeaways at the end of major sections

## Deliverables Expected

1. **Main documentation file** (`[system-name]-documentation.md`)
2. **Multiple Mermaid diagrams** (minimum 4-6 diagrams of different types)
3. **Code examples** (before/after, usage patterns, best practices)
4. **Migration guide** (if applicable)
5. **Troubleshooting section**
6. **Reference materials** (API docs, type definitions)

## Quality Checklist

Before considering the documentation complete, verify:

- [ ] All Mermaid diagrams render without parse errors
- [ ] Code examples are syntactically correct and runnable
- [ ] Migration steps are clear and actionable
- [ ] Problem-solution mapping is visually clear
- [ ] Troubleshooting section covers common issues
- [ ] Best practices are clearly explained with examples
- [ ] Document follows logical information hierarchy
- [ ] Cross-references and links work correctly
- [ ] TypeScript types are accurate and complete
- [ ] Visual design is consistent throughout

## Context for [SPECIFIC PROJECT]

[When using this template, add specific context here:]
- Project technology stack
- Key stakeholders and audience
- Specific constraints or requirements
- Integration points with other systems
- Performance or scalability requirements
- Security considerations
- Timeline and rollout plan

---

**Example Usage:**
"Please create comprehensive technical documentation for the new Authentication System using this template. Focus on the migration from JWT-only to OAuth2 + JWT hybrid approach, include security implications, and emphasize the developer experience improvements."
```

## Notes for Future Use

- This template was successfully used for the Centralized Socket Management System documentation
- Adjust the sections based on the complexity and scope of your specific system
- The Mermaid diagram guidelines are crucial for avoiding parsing errors
- Always include real code examples - they're often the most valuable part of technical documentation
- Consider your audience: this template is optimized for developer-focused documentation
