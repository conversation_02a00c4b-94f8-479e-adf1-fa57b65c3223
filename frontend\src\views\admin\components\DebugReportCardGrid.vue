<template>
  <div class="card-grid">
    <div
      v-for="report in reports"
      :key="report.reportId"
      class="report-card"
      @click="$emit('view-report', report)"
    >
      <div class="card-header">
        <span class="type-badge">{{ formatType(report.reportType) }}</span>
        <span 
          class="severity-badge" 
          :style="{ backgroundColor: getSeverityColor(report.reportSeverity) }"
        >
          {{ formatSeverity(report.reportSeverity) }}
        </span>
      </div>
      <div class="card-content">
        <p class="report-id">ID: {{ report.reportId ? report.reportId.substring(0, 12) + '...' : 'N/A' }}</p>
        <p class="user-info">User: {{ formatUser(report) }}</p>
        <p class="description">{{ getDescription(report) }}</p>
        
        <!-- Tags section -->        <div v-if="report.tags && report.tags.length > 0" class="tags-section">
          <div class="tags-list">
            <span 
              v-for="tagItem in report.tags.slice(0, 3)" 
              :key="getTagDisplayName(tagItem)"
              class="tag-badge"
              :class="getTagOriginClass(tagItem.origin)"
              :title="getTagOriginTitle(tagItem.origin)"
            >
              {{ getTagDisplayName(tagItem) }}
            </span>
            <span 
              v-if="report.tags.length > 3" 
              class="tag-badge tag-more"
              :title="`${report.tags.length - 3} more tags`"
            >
              +{{ report.tags.length - 3 }}
            </span>
          </div>
        </div>
        
        <p class="timestamp">Received: {{ formatDate(report.serverReceivedAt) }}</p>
      </div>
      <div class="card-actions">
        <button @click.stop="$emit('view-report', report)" class="btn btn-sm btn-outline">
          View Details
        </button>
        <button 
          @click.stop="report.reportId && $emit('download-report', report.reportId)" 
          class="btn btn-sm btn-outline" 
          :disabled="!report.reportId"
        >
          Download
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns';
import { getTagOriginClass, getTagOriginTitle, getTagDisplayName } from '@/utils/tagOriginUtils';
import type { ParsedReport } from '@/types/admin';

const SEVERITY_COLORS = {
  low: '#10b981',
  medium: '#f59e0b', 
  high: '#ef4444',
  critical: '#dc2626'
};

defineOptions({
  name: 'DebugReportCardGrid'
});

defineProps<{
  reports: ParsedReport[];
}>();

defineEmits<{
  'view-report': [report: ParsedReport];
  'download-report': [id: string];
}>();

function formatType(type?: string): string {
  if (!type) return 'N/A';
  return type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
}

function formatSeverity(severity?: string): string {
  if (!severity) return 'N/A';
  return severity.charAt(0).toUpperCase() + severity.slice(1);
}

function formatDate(dateString?: string): string {
  return dateString ? format(new Date(dateString), 'MMM dd, yyyy HH:mm') : 'N/A';
}

function getSeverityColor(severity?: string): string {
  return severity ? (SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666') : '#666';
}

function formatUser(report: ParsedReport): string {
  if (report.username) {
    return report.username;
  }
  if (report.userEmail) {
    return report.userEmail;
  }
  if (report.userId) {
    return `User ${report.userId.substring(0, 8)}...`;
  }
  return 'Anonymous';
}

function getDescription(report: ParsedReport): string {
  const desc = report.reportDescription;
  if (!desc) return 'No description available';
  return desc.length > 100 ? desc.substring(0, 100) + '...' : desc;
}
</script>

<style scoped>
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}

.report-card {
  background-color: var(--bg-surface, #fff);
  border: 1px solid var(--border-base, #e0e0e0);
  border-radius: var(--radius-lg, 8px);
  padding: 1rem;
  box-shadow: var(--shadow-sm, 0 1px 3px rgba(0,0,0,0.1));
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.report-card:hover {
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0,0,0,0.1));
  transform: translateY(-2px);
  border-color: var(--primary-200, #bfdbfe);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.type-badge, 
.severity-badge {
  font-size: var(--font-size-xs, 0.75rem);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-full, 9999px);
  font-weight: var(--font-weight-semibold, 600);
  white-space: nowrap;
}

.type-badge {
  background-color: var(--primary-100, #dbeafe);
  color: var(--primary-700, #1d4ed8);
}

.severity-badge {
  color: white;
}

.card-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-content p {
  margin: 0.5rem 0;
  font-size: var(--font-size-sm, 0.875rem);
}

.report-id {
  font-family: var(--font-family-mono, monospace);
  color: var(--text-tertiary, #6c757d);
  font-size: var(--font-size-xs, 0.75rem);
}

.user-info {
  color: var(--text-secondary, #666);
  font-size: var(--font-size-xs, 0.75rem);
  font-weight: var(--font-weight-medium, 500);
}

.description {
  color: var(--text-secondary, #666);
  min-height: 40px;
  flex-grow: 1;
  line-height: 1.4;
}

.timestamp {
  color: var(--text-tertiary, #6c757d);
  font-size: var(--font-size-xs, 0.75rem);
  margin-top: auto;
}

.card-actions {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-base, #e0e0e0);
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md, 6px);
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-semibold, 600);
  transition: all 0.2s ease-in-out;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-xs, 0.75rem);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-500, #3b82f6);
  border-color: var(--primary-500, #3b82f6);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-50, #eff6ff);
}

.btn-outline:disabled {
  color: var(--text-quaternary, #94a3b8);
  border-color: var(--text-quaternary, #94a3b8);
  cursor: not-allowed;
}

/* Tags section styling */
.tags-section {
  margin: 0.5rem 0;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.tag-badge {
  font-size: var(--font-size-xs, 0.75rem);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm, 4px);
  font-weight: var(--font-weight-medium, 500);
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  border: 1px solid transparent;
}

.tag-predefined {
  background-color: var(--primary-100, #dbeafe);
  color: var(--primary-700, #1d4ed8);
  border-color: var(--primary-200, #bfdbfe);
}

.tag-ai-suggested {
  background-color: var(--purple-100, #e9d5ff);
  color: var(--purple-700, #7c3aed);
  border-color: var(--purple-200, #c4b5fd);
}

.tag-user-defined {
  background-color: var(--emerald-100, #d1fae5);
  color: var(--emerald-700, #047857);
  border-color: var(--emerald-200, #a7f3d0);
}

.tag-unknown {
  background-color: var(--gray-100, #f3f4f6);
  color: var(--gray-700, #374151);
  border-color: var(--gray-200, #e5e7eb);
}

.tag-more {
  background-color: var(--gray-100, #f3f4f6);
  color: var(--gray-600, #4b5563);
  border-color: var(--gray-200, #e5e7eb);
  font-style: italic;
}

/* Dark mode support for tags */
[data-theme="dark"] .tag-predefined {
  background-color: var(--primary-900, #1e3a8a);
  color: var(--primary-200, #bfdbfe);
  border-color: var(--primary-700, #1d4ed8);
}

[data-theme="dark"] .tag-ai-suggested {
  background-color: var(--purple-900, #581c87);
  color: var(--purple-200, #c4b5fd);
  border-color: var(--purple-700, #7c3aed);
}

[data-theme="dark"] .tag-user-defined {
  background-color: var(--emerald-900, #064e3b);
  color: var(--emerald-200, #a7f3d0);
  border-color: var(--emerald-700, #047857);
}

[data-theme="dark"] .tag-unknown,
[data-theme="dark"] .tag-more {
  background-color: var(--gray-800, #1f2937);
  color: var(--gray-200, #e5e7eb);
  border-color: var(--gray-600, #4b5563);
}
</style>