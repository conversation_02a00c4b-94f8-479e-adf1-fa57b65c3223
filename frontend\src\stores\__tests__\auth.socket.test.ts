import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useAuthStore } from '../auth';

// Mock the centralized socket manager
vi.mock('@/services/centralizedSocketManager', () => ({
  default: {
    initializeSocket: vi.fn().mockResolvedValue(undefined),
    disconnect: vi.fn(),
    cleanup: vi.fn(),
    isConnected: vi.fn(() => false)
  }
}));

// Mock API client
vi.mock('@/services/apiClient', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn()
  }
}));

describe('AuthStore - Socket Connection Management', () => {
  let store: ReturnType<typeof useAuthStore>;
  let mockSocketManager: any;
  let mockApiClient: any;
  let mockLocalStorage: any;

  beforeEach(async () => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
    
    // Get the mocked modules
    mockSocketManager = (await import('@/services/centralizedSocketManager')).default;
    mockApiClient = (await import('@/services/apiClient')).default;
    
    // Setup localStorage mock
    mockLocalStorage = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn()
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true
    });
    
    store = useAuthStore();
  });

  describe('initializeSocketConnection', () => {
    it('should initialize socket when user is authenticated', () => {
      // Set up authenticated state
      store.token = 'mock-jwt-token';
      store.user = {
        id: 'user-1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true,
        isPhoneVerified: true,
        phone: '+1234567890',
        reputation: 5.0,
        joinedAt: '2024-01-01T00:00:00Z'
      };

      store.initializeSocketConnection();

      expect(mockSocketManager.initializeSocket).toHaveBeenCalledTimes(1);
    });

    it('should not initialize socket when user is not authenticated', () => {
      // Ensure store is not authenticated
      store.token = null;
      store.user = null;

      store.initializeSocketConnection();

      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });

    it('should not initialize socket when token exists but user is null', () => {
      store.token = 'mock-jwt-token';
      store.user = null;

      store.initializeSocketConnection();

      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });

    it('should not initialize socket when user exists but token is null', () => {
      store.token = null;
      store.user = {
        id: 'user-1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true,
        isPhoneVerified: true,
        phone: '+1234567890',
        reputation: 5.0,
        joinedAt: '2024-01-01T00:00:00Z'
      };

      store.initializeSocketConnection();

      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });
  });

  describe('login method integration', () => {
    it('should initialize socket connection after successful login', () => {
      const mockUserData = {
        id: 'user-1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true,
        isPhoneVerified: true,
        phone: '+1234567890',
        reputation: 5.0,
        joinedAt: '2024-01-01T00:00:00Z'
      };

      store.login('mock-jwt-token', mockUserData);

      expect(mockSocketManager.initializeSocket).toHaveBeenCalledTimes(1);
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('authToken', 'mock-jwt-token');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('userInfo', JSON.stringify(mockUserData));
    });
  });  describe('logout method integration', () => {
    it('should cleanup socket connection during logout', () => {
      // Set up authenticated state first
      store.token = 'mock-jwt-token';
      store.user = {
        id: 'user-1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true,
        isPhoneVerified: true,
        phone: '+1234567890',
        reputation: 5.0,
        joinedAt: '2024-01-01T00:00:00Z'
      };

      store.logout();
      
      expect(mockSocketManager.disconnect).toHaveBeenCalledTimes(1);
      expect(store.token).toBeNull();
      expect(store.user).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('authToken');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('userInfo');
    });
  });

  describe('initializeAuth method integration', () => {    it('should load existing auth from localStorage without initializing socket', () => {
      const mockUserData = {
        id: 'user-1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true,
        isPhoneVerified: true,
        phone: '+1234567890',
        reputation: 5.0,
        joinedAt: '2024-01-01T00:00:00Z'
      };

      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'authToken') return 'mock-jwt-token';
        if (key === 'userInfo') return JSON.stringify(mockUserData);
        return null;
      });

      store.initializeAuth();

      expect(store.token).toBe('mock-jwt-token');
      expect(store.user).toEqual(mockUserData);
      // initializeAuth does not automatically call initializeSocketConnection
      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });    it('should not initialize socket connection when no auth data in localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      store.initializeAuth();

      expect(store.token).toBeNull();
      expect(store.user).toBeNull();
      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });    it('should clear auth state when only token exists in localStorage', () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'authToken') return 'mock-jwt-token';
        return null;
      });

      store.initializeAuth();

      expect(store.token).toBeNull();
      expect(store.user).toBeNull();
      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });    it('should handle corrupted user data in localStorage gracefully', () => {
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'authToken') return 'mock-jwt-token';
        if (key === 'userInfo') return 'invalid-json';
        return null;
      });

      store.initializeAuth();

      // When corrupted data is found, logout() is called which clears everything
      expect(store.token).toBeNull();
      expect(store.user).toBeNull();
      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });
  });

  describe('fetchUserProfile method integration', () => {    it('should fetch and update user profile without initializing socket', async () => {
      const mockUserData = {
        id: 'user-1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true,
        isPhoneVerified: true,
        phone: '+1234567890',
        reputation: 5.0,
        joinedAt: '2024-01-01T00:00:00Z'
      };

      // Set up token first
      store.token = 'mock-jwt-token';
      mockApiClient.get.mockResolvedValue({
        data: mockUserData
      });

      await store.fetchUserProfile();

      expect(store.user).toEqual(mockUserData);
      // fetchUserProfile doesn't automatically initialize socket
      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });    it('should not update user state after failed profile fetch', async () => {
      store.token = 'mock-jwt-token';
      
      mockApiClient.get.mockRejectedValue(new Error('API Error'));

      try {
        await store.fetchUserProfile();
      } catch (error) {
        // Expected to throw
      }

      expect(store.user).toBeNull();
      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });    it('should not fetch profile when no token exists', async () => {
      store.token = null;

      try {
        await store.fetchUserProfile();
      } catch (error) {
        // Expected to throw when no token
      }

      expect(mockApiClient.get).toHaveBeenCalled(); // Will still try to call API
      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });
  });

  describe('updatePhoneVerificationStatus method integration', () => {    it('should update phone verification status without initializing socket', () => {
      // Set up authenticated state first
      store.token = 'mock-jwt-token';      store.user = {
        id: 'user-1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        emailVerified: true,
        phoneVerified: false,
        phoneNumber: '+1234567890',
        reputation: 5.0,
        joinedAt: '2024-01-01T00:00:00Z'
      };

      // Clear previous calls
      vi.clearAllMocks();

      store.updatePhoneVerificationStatus(true, '+1234567890');      expect(store.user?.phoneVerified).toBe(true);
      expect(store.user?.phoneNumber).toBe('+1234567890');
      // updatePhoneVerificationStatus doesn't automatically initialize socket
      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });

    it('should not initialize socket connection when user is not authenticated', () => {
      store.token = null;
      store.user = null;

      store.updatePhoneVerificationStatus(true, '+1234567890');

      expect(mockSocketManager.initializeSocket).not.toHaveBeenCalled();
    });
  });

  describe('isAuthenticated computed property', () => {
    it('should return true when both token and user exist', () => {
      store.token = 'mock-jwt-token';
      store.user = {
        id: 'user-1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true,
        isPhoneVerified: true,
        phone: '+1234567890',
        reputation: 5.0,
        joinedAt: '2024-01-01T00:00:00Z'
      };

      expect(store.isAuthenticated).toBe(true);
    });

    it('should return false when token is missing', () => {
      store.token = null;
      store.user = {
        id: 'user-1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true,
        isPhoneVerified: true,
        phone: '+1234567890',
        reputation: 5.0,
        joinedAt: '2024-01-01T00:00:00Z'
      };

      expect(store.isAuthenticated).toBe(false);
    });

    it('should return false when user is missing', () => {
      store.token = 'mock-jwt-token';
      store.user = null;

      expect(store.isAuthenticated).toBe(false);
    });

    it('should return false when both token and user are missing', () => {
      store.token = null;
      store.user = null;

      expect(store.isAuthenticated).toBe(false);
    });
  });
});
