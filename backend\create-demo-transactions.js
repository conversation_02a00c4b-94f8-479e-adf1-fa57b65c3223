/**
 * Demo Transaction Data Creation Script
 * 
 * This script creates demo transaction data for testing the Transactional Chat Cockpit
 * Run this script to set up test transactions in different states
 * 
 * Usage: node create-demo-transactions.js
 */

import { PrismaClient, TransactionStatus } from '@prisma/client';

const prisma = new PrismaClient();

async function createDemoTransactions() {
  console.log('🎬 Creating demo transactions for Transactional Chat Cockpit...');

  try {
    // First, ensure we have demo users
    const demoUser1 = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        username: 'DemoUser1',
        passwordHash: 'demo-hash', // Not used for demo
        emailVerified: true,
        reputationLevel: 4,
        reputationScore: 4.2
      }
    });

    const demoUser2 = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        username: '<PERSON>',
        passwordHash: 'demo-hash', // Not used for demo
        emailVerified: true,
        reputationLevel: 4,
        reputationScore: 4.5
      }
    });

    console.log('✅ Demo users created:', { 
      user1: demoUser1.username, 
      user2: demoUser2.username 
    });

    // Create demo transactions for each of the 7 steps
    const demoTransactions = [
      {
        id: 'tx-001',
        status: TransactionStatus.PENDING_AGREEMENT,
        stepName: 'Step 1: Payment Info Setup'
      },
      {
        id: 'tx-002',
        status: TransactionStatus.AWAITING_FIRST_PAYER_DESIGNATION,
        stepName: 'Step 2: Negotiation'
      },
      {
        id: 'tx-003',
        status: TransactionStatus.AWAITING_FIRST_PAYER_PAYMENT,
        stepName: 'Step 3: Waiting for First Payer'
      },
      {
        id: 'tx-004',
        status: TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION,
        stepName: 'Step 4: Confirm Receipt (KEY STEP)'
      },
      {
        id: 'tx-005',
        status: TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT,
        stepName: 'Step 5: Your Turn to Pay'
      },
      {
        id: 'tx-006',
        status: TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION,
        stepName: 'Step 6: Waiting for Second Payer Confirmation'
      },
      {
        id: 'tx-007',
        status: TransactionStatus.COMPLETED,
        stepName: 'Step 7: Transaction Complete'
      }
    ];

    for (const demoTx of demoTransactions) {
      // Create chat session for each transaction
      const chatSession = await prisma.chatSession.create({
        data: {
          userOneId: demoUser1.id,
          userTwoId: demoUser2.id
        }
      });

      // Create the transaction
      const transaction = await prisma.transaction.create({
        data: {
          id: demoTx.id,
          chatSessionId: chatSession.id,
          offerId: null, // No specific offer for demo
          status: demoTx.status,
          currencyA: 'CAD',
          amountA: 1000,
          currencyAProviderId: demoUser1.id,
          currencyB: 'IRR',
          amountB: 850000, // 850,000 IRR
          currencyBProviderId: demoUser2.id,
          // Set additional fields based on step
          ...(demoTx.status !== TransactionStatus.PENDING_AGREEMENT && {
            termsAgreementTimestampPayer1: new Date(),
            termsAgreementTimestampPayer2: new Date()
          }),
          ...(demoTx.status !== TransactionStatus.PENDING_AGREEMENT && 
              demoTx.status !== TransactionStatus.AWAITING_FIRST_PAYER_DESIGNATION && {
            agreedFirstPayerId: demoUser2.id, // Alex pays first
            firstPayerDesignationTimestamp: new Date(),
            paymentExpectedByPayer1: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now
          }),
          ...(demoTx.status === TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION ||
              demoTx.status === TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT ||
              demoTx.status === TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION ||
              demoTx.status === TransactionStatus.COMPLETED) && {
            paymentDeclaredAtPayer1: new Date(),
            paymentTrackingNumberPayer1: 'DEMO-123456'
          },
          ...(demoTx.status === TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT ||
              demoTx.status === TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION ||
              demoTx.status === TransactionStatus.COMPLETED) && {
            firstPaymentConfirmedByPayer2At: new Date(),
            paymentExpectedByPayer2: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now
          },
          ...(demoTx.status === TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION ||
              demoTx.status === TransactionStatus.COMPLETED) && {
            paymentDeclaredAtPayer2: new Date(),
            paymentTrackingNumberPayer2: 'DEMO-789012'
          },
          ...(demoTx.status === TransactionStatus.COMPLETED && {
            secondPaymentConfirmedByPayer1At: new Date()
          })
        }
      });

      // Add some demo chat messages
      await createDemoChatMessages(chatSession.id, transaction.id, demoUser1.id, demoUser2.id, demoTx.status);

      console.log(`✅ Created demo transaction: ${demoTx.id} - ${demoTx.stepName}`);
    }

    console.log('🎉 All demo transactions created successfully!');
    console.log('\n📋 Demo Transaction IDs:');
    demoTransactions.forEach(tx => {
      console.log(`  ${tx.id} - ${tx.stepName}`);
    });
    
    console.log('\n🚀 You can now test the Transactional Chat Cockpit with these transactions!');
    console.log('   Navigate to: /transaction/{transactionId} in your frontend');

  } catch (error) {
    console.error('❌ Error creating demo transactions:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function createDemoChatMessages(chatSessionId: string, transactionId: string, user1Id: string, user2Id: string, status: TransactionStatus) {
  const messages = [];

  // Add system message for transaction start
  messages.push({
    chatSessionId,
    content: 'transactionalChat.systemLogs.tradeStarted',
    isSystemMessage: true,
    senderId: null,
    transactionId
  });

  // Add some chat messages based on status
  if (status !== TransactionStatus.PENDING_AGREEMENT) {
    messages.push({
      chatSessionId,
      content: 'Hi! Ready to start our CAD/IRR exchange?',
      isSystemMessage: false,
      senderId: user2Id, // Alex
      transactionId
    });

    messages.push({
      chatSessionId,
      content: 'Yes, let me add my payment details first.',
      isSystemMessage: false,
      senderId: user1Id, // Demo User
      transactionId
    });
  }

  if (status !== TransactionStatus.PENDING_AGREEMENT && status !== TransactionStatus.AWAITING_FIRST_PAYER_DESIGNATION) {
    messages.push({
      chatSessionId,
      content: 'transactionalChat.systemLogs.paymentDetailsProvided',
      isSystemMessage: true,
      senderId: null,
      transactionId
    });

    messages.push({
      chatSessionId,
      content: 'Great! I see your payment details. Who should pay first?',
      isSystemMessage: false,
      senderId: user2Id,
      transactionId
    });

    messages.push({
      chatSessionId,
      content: 'I agree with the system recommendation. You can pay first.',
      isSystemMessage: false,
      senderId: user1Id,
      transactionId
    });

    messages.push({
      chatSessionId,
      content: 'transactionalChat.systemLogs.agreementReached',
      isSystemMessage: true,
      senderId: null,
      transactionId
    });
  }

  // Add more messages for advanced steps
  if (status === TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION ||
      status === TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT ||
      status === TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION ||
      status === TransactionStatus.COMPLETED) {
    
    messages.push({
      chatSessionId,
      content: 'transactionalChat.systemLogs.paymentMarkedSent',
      isSystemMessage: true,
      senderId: null,
      transactionId
    });

    messages.push({
      chatSessionId,
      content: 'Done! I\'ve sent the IRR to your account. Please check and confirm!',
      isSystemMessage: false,
      senderId: user2Id,
      transactionId
    });
  }

  if (status === TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT ||
      status === TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION ||
      status === TransactionStatus.COMPLETED) {
    
    messages.push({
      chatSessionId,
      content: 'transactionalChat.systemLogs.youConfirmedReceipt',
      isSystemMessage: true,
      senderId: null,
      transactionId
    });

    messages.push({
      chatSessionId,
      content: 'Received! Thank you. Now sending CAD to you...',
      isSystemMessage: false,
      senderId: user1Id,
      transactionId
    });
  }

  if (status === TransactionStatus.COMPLETED) {
    messages.push({
      chatSessionId,
      content: 'transactionalChat.systemLogs.transactionComplete',
      isSystemMessage: true,
      senderId: null,
      transactionId
    });

    messages.push({
      chatSessionId,
      content: 'Perfect! Transaction complete! 🎉',
      isSystemMessage: false,
      senderId: user2Id,
      transactionId
    });
  }

  // Create all messages
  for (const messageData of messages) {
    await prisma.chatMessage.create({
      data: {
        ...messageData,
        createdAt: new Date(Date.now() - Math.random() * 60 * 60 * 1000) // Random time in last hour
      }
    });
  }
}

// Run the script
createDemoTransactions();
