/**
 * Comprehensive test for the full tag origin flow
 * This tests the complete pipeline from AI processing to admin display
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testFullTagFlow() {
  console.log('🧪 Testing complete tag origin flow...\n');

  try {    // Clean up any existing test data
    await prisma.debugReportTag.deleteMany({
      where: {
        report: {
          reportId: { startsWith: 'test-tag-flow-' }
        }
      }
    });
    
    await prisma.debugReport.deleteMany({
      where: {
        reportId: { startsWith: 'test-tag-flow-' }
      }
    });

    // 1. Create a test report with all tag origin types
    const testReportId = `test-tag-flow-${Date.now()}`;
      const testReport = await prisma.debugReport.create({
      data: {
        reportId: testReportId,
        sessionId: `session-${Date.now()}`,
        type: 'BUG',
        severity: 'HIGH',
        title: 'Test Report for Tag Origins',
        description: 'Test report for complete tag origin flow',
        clientTimestamp: new Date(),
        serverReceivedAt: new Date(),
        diagnosticData: JSON.stringify({
          testData: 'Complete tag flow test'
        }),
        tags: {
          create: [
            {
              tag: 'authentication',
              origin: 'PREDEFINED'
            },
            {
              tag: 'socket-connection',
              origin: 'AI_SUGGESTED'
            },
            {
              tag: 'custom-issue',
              origin: 'USER_DEFINED'
            }
          ]
        }
      },
      include: {
        tags: true
      }
    });

    console.log('✅ Created test report with mixed tag origins:');
    console.log(`   Report ID: ${testReport.reportId}`);
    console.log(`   Tags created: ${testReport.tags.length}`);
    
    testReport.tags.forEach(tag => {
      console.log(`   - "${tag.tag}" (${tag.origin})`);
    });

    // 2. Test querying with tag origins
    console.log('\n🔍 Testing query with tag origins...');
    
    const retrievedReport = await prisma.debugReport.findUnique({
      where: { reportId: testReportId },
      include: {
        tags: {
          orderBy: { tag: 'asc' }
        }
      }
    });

    if (!retrievedReport) {
      throw new Error('Failed to retrieve test report');
    }

    console.log('✅ Successfully retrieved report with tag origins:');
    retrievedReport.tags.forEach(tag => {
      console.log(`   - "${tag.tag}" (${tag.origin})`);
    });

    // 3. Test tag origin statistics
    console.log('\n📊 Testing tag origin statistics...');
      const tagStats = await prisma.debugReportTag.groupBy({
      by: ['origin'],
      where: {
        report: {
          reportId: testReportId
        }
      },
      _count: {
        origin: true
      }
    });

    console.log('✅ Tag origin statistics:');
    tagStats.forEach(stat => {
      console.log(`   - ${stat.origin}: ${stat._count.origin} tags`);
    });

    // 4. Verify data structure matches frontend expectations
    console.log('\n🎯 Testing frontend data structure compatibility...');
      // This mimics how the backend API would format data for the frontend
    const frontendFormattedData = {
      reportId: retrievedReport.reportId,
      type: retrievedReport.type,
      severity: retrievedReport.severity,
      tags: retrievedReport.tags.map(tag => ({
        tag: tag.tag,
        origin: tag.origin
      })),
      serverReceivedAt: retrievedReport.serverReceivedAt
    };

    console.log('✅ Frontend-compatible data structure:');
    console.log(JSON.stringify(frontendFormattedData, null, 2));

    // 5. Test filtering by tag origin
    console.log('\n🔍 Testing filtering by tag origin...');
      const predefinedTags = await prisma.debugReportTag.findMany({
      where: {
        report: {
          reportId: testReportId
        },
        origin: 'PREDEFINED'
      }
    });

    const aiSuggestedTags = await prisma.debugReportTag.findMany({
      where: {
        report: {
          reportId: testReportId
        },
        origin: 'AI_SUGGESTED'
      }
    });

    const userDefinedTags = await prisma.debugReportTag.findMany({
      where: {
        report: {
          reportId: testReportId
        },
        origin: 'USER_DEFINED'
      }
    });

    console.log(`✅ Predefined tags: ${predefinedTags.length}`);
    console.log(`✅ AI-suggested tags: ${aiSuggestedTags.length}`);
    console.log(`✅ User-defined tags: ${userDefinedTags.length}`);    // Clean up test data
    await prisma.debugReportTag.deleteMany({
      where: {
        report: {
          reportId: testReportId
        }
      }
    });
    
    await prisma.debugReport.delete({
      where: { reportId: testReportId }
    });

    console.log('\n🧹 Cleaned up test data');
    console.log('\n🎉 Complete tag origin flow test PASSED! ✅');

  } catch (error) {
    console.error('\n❌ Test FAILED:');
    console.error(error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

testFullTagFlow();
