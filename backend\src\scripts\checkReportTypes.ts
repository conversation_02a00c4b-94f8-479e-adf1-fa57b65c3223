import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkReportTypes() {
  console.log('🔍 Checking debug report types...');

  try {
    // Check all unique report types
    const reports = await prisma.debugReport.findMany({
      select: {
        id: true,
        type: true
      }
    });

    console.log(`📊 Total debug reports: ${reports.length}`);

    const uniqueTypes = [...new Set(reports.map(r => r.type))];
    console.log(`🏷️  Unique report types: ${uniqueTypes.length}`);
    console.log('Report types:', uniqueTypes);

    // Check TagReportTypeAssociation report types
    const associations = await prisma.tagReportTypeAssociation.findMany({
      select: {
        reportType: true,
        tag: {
          select: {
            name: true
          }
        }
      }
    });

    const uniqueAssocTypes = [...new Set(associations.map(a => a.reportType))];
    console.log(`🔗 Association report types: ${uniqueAssocTypes.length}`);
    console.log('Association types:', uniqueAssocTypes);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkReportTypes().catch(console.error);
