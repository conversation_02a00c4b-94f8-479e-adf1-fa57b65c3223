# Stagewise Dev-Tool Integration

This document explains how the stagewise browser toolbar has been integrated into the MUNygo project to provide AI-powered editing capabilities.

## What is Stagewise?

Stagewise is a browser toolbar that connects frontend UI to code AI agents in your code editor. It allows developers to:
- Select elements in a web app
- Leave comments and feedback
- Let AI agents make changes based on that context

## Integration Details

### 1. Package Installation

The stagewise package has been installed as a development dependency:
```bash
npm install @stagewise/toolbar-vue --save-dev
```

### 2. File Structure

The integration consists of the following files:

- **`src/utils/stagewise.ts`**: Configuration file with basic stagewise settings
- **`src/components/StagewiseDev.vue`**: Vue component wrapper that conditionally renders the toolbar
- **`src/App.vue`**: Updated to include the StagewiseDev component

### 3. Development-Only Integration

The stagewise toolbar **only appears in development mode** and is automatically excluded from production builds:

- Uses `import.meta.env.DEV` to check development mode
- Uses `import.meta.env.MODE === 'development'` for additional safety
- Package is installed in `devDependencies`, so it won't be included in production builds

### 4. How It Works

1. When you run `npm run dev`, the development server starts with stagewise enabled
2. The toolbar appears in your browser window (only in development)
3. You can interact with page elements through the stagewise interface
4. Any changes or comments made through stagewise can be processed by AI agents in your code editor

## Usage

### Starting Development Server

```bash
cd frontend
npm run dev
```

The stagewise toolbar will automatically appear in your browser at `http://localhost:5173`.

### Production Builds

When you build for production:
```bash
npm run build
# or
npm run build:docker
```

The stagewise toolbar will **not** be included in the production bundle, ensuring your production app remains clean and lightweight.

### Customizing Stagewise Configuration

You can modify the stagewise configuration in `src/utils/stagewise.ts`:

```typescript
export const stagewiseConfig = {
  plugins: [
    // Add stagewise plugins here
  ],
  // Additional configuration options:
  // theme: 'dark' | 'light' | 'auto',
  // position: 'top' | 'bottom' | 'left' | 'right',
  // etc.
};
```

## Benefits

1. **AI-Powered Development**: Get AI assistance for UI changes and improvements
2. **Context-Aware**: Select specific elements and provide targeted feedback
3. **Non-Intrusive**: Only runs in development, zero impact on production
4. **Vue Integration**: Seamlessly integrated with the existing Vue 3 + TypeScript setup
5. **Automatic Exclusion**: No risk of accidentally including dev tools in production

## Troubleshooting

### Toolbar Not Appearing

1. Make sure you're running in development mode: `npm run dev`
2. Check that you're accessing `http://localhost:5173` (development server)
3. Open browser developer tools and check for any console errors

### Production Builds

The toolbar should never appear in production builds. If it does, check:
1. Build mode: `npm run build` should use production mode
2. Environment variables: Make sure `NODE_ENV=production` in production

## Technical Notes

- **Framework**: Vue 3 with TypeScript
- **Package**: `@stagewise/toolbar-vue` v0.4.1
- **Integration Pattern**: Component wrapper with conditional rendering
- **Build Tool**: Vite (automatically handles development vs production modes)

The integration follows Vue.js best practices and maintains the existing project architecture while adding the powerful stagewise development capabilities.
