import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { authMiddleware, JwtPayload } from '../middleware/auth';
import { getNotificationService } from '../index';

const notificationRoutes = new Hono();

// --- Zod Schemas for Validation ---
const getNotificationsQuerySchema = z.object({
  limit: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined).refine(val => val === undefined || (!isNaN(val) && val > 0), { message: 'Limit must be a positive number' }),
  offset: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined).refine(val => val === undefined || (!isNaN(val) && val >= 0), { message: 'Offset must be a non-negative number' }),
  unreadOnly: z.string().optional().transform(val => val === 'true' || val === '1').pipe(z.boolean().optional()),
});

const notificationIdParamSchema = z.object({
  notificationId: z.string().uuid({ message: 'Invalid notification ID format' }),
});

// --- GET /notifications route ---
notificationRoutes.get('/', authMiddleware, zValidator('query', getNotificationsQuerySchema), async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const { limit, offset, unreadOnly } = c.req.valid('query');

  const notificationService = getNotificationService();
  if (!notificationService) {
    console.error('[NotificationRoutes] NotificationService not available');
    return c.json({ message: 'Notification service not available' }, 503); // Service Unavailable
  }

  try {
    const notifications = await notificationService.getNotificationsForUser(jwtPayload.userId, {
      limit,
      offset,
      unreadOnly,
    });
    return c.json(notifications);
  } catch (error) {
    console.error('[API Error - /notifications] Exception during fetching notifications:', error);
    return c.json({ message: 'Failed to fetch notifications', error: (error as Error).message }, 500);
  }
});

// --- POST /notifications/mark-all-read route ---
notificationRoutes.post('/mark-all-read', authMiddleware, async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const notificationService = getNotificationService();

  if (!notificationService) {
    console.error('[NotificationRoutes] NotificationService not available for mark-all-read');
    return c.json({ message: 'Notification service not available' }, 503);
  }

  try {
    const result = await notificationService.markAllNotificationsAsRead(jwtPayload.userId);
    return c.json({ message: `Successfully marked ${result.count} notifications as read.`, count: result.count });
  } catch (error) {
    console.error('[API Error - /notifications/mark-all-read] Exception:', error);
    return c.json({ message: 'Failed to mark all notifications as read', error: (error as Error).message }, 500);
  }
});

// --- POST /notifications/:notificationId/mark-read route ---
notificationRoutes.post('/:notificationId/mark-read', authMiddleware, zValidator('param', notificationIdParamSchema), async (c) => {
  const jwtPayload = c.get('jwtPayload') as JwtPayload;
  const { notificationId } = c.req.valid('param');
  const notificationService = getNotificationService();

  if (!notificationService) {
    console.error('[NotificationRoutes] NotificationService not available for mark-read');
    return c.json({ message: 'Notification service not available' }, 503);
  }

  try {
    const updatedNotification = await notificationService.markNotificationAsRead(notificationId, jwtPayload.userId);
    if (!updatedNotification) {
      // The service returns null if not found, not owned, or already read.
      // Frontend might prefer a 404 in all these cases for simplicity unless specific error handling is needed.
      return c.json({ message: 'Notification not found, not owned by user, or already marked as read.' }, 404);
    }
    return c.json(updatedNotification);
  } catch (error) {
    console.error(`[API Error - /notifications/${notificationId}/mark-read] Exception:`, error);
    // Generic error for any other failure in the service layer
    return c.json({ message: 'Failed to mark notification as read', error: (error as Error).message }, 500);
  }
});

export default notificationRoutes;
