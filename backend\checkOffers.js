const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkOffers() {
  try {
    console.log('=== Checking Offers in Database ===');
    
    // Get all offers
    const allOffers = await prisma.offer.findMany({
      include: {
        user: {
          select: {
            id: true,
            username: true,
            reputationLevel: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log(`\nTotal offers in database: ${allOffers.length}`);
    
    if (allOffers.length === 0) {
      console.log('❌ No offers found in the database!');
      console.log('This explains why /offers/browse returns an empty array.');
      console.log('\nTo fix this, you need to create some offers first.');
      return;
    }
    
    // Group by status
    const offersByStatus = allOffers.reduce((acc, offer) => {
      acc[offer.status] = (acc[offer.status] || 0) + 1;
      return acc;
    }, {});
    
    console.log('\nOffers by status:');
    Object.entries(offersByStatus).forEach(([status, count]) => {
      console.log(`  ${status}: ${count}`);
    });
    
    // Show active offers
    const activeOffers = allOffers.filter(offer => offer.status === 'ACTIVE');
    console.log(`\n=== ACTIVE Offers (${activeOffers.length}) ===`);
    
    if (activeOffers.length === 0) {
      console.log('❌ No ACTIVE offers found!');
      console.log('This explains why /offers/browse returns an empty array.');
      console.log('Offers must have status "ACTIVE" to appear in browse.');
    } else {
      activeOffers.forEach((offer, index) => {
        console.log(`\n${index + 1}. Offer ID: ${offer.id}`);
        console.log(`   Type: ${offer.type}`);
        console.log(`   Amount: ${offer.amount}`);
        console.log(`   Base Rate: ${offer.baseRate}`);
        console.log(`   Status: ${offer.status}`);
        console.log(`   Creator: ${offer.user.username} (ID: ${offer.user.id})`);
        console.log(`   Creator Rep Level: ${offer.user.reputationLevel}`);
        console.log(`   Created: ${offer.createdAt}`);
      });
    }
    
    // Get all users to check if there are multiple users
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        reputationLevel: true
      }
    });
    
    console.log(`\n=== Users in Database (${allUsers.length}) ===`);
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username} (ID: ${user.id}, Rep: ${user.reputationLevel})`);
    });
    
    // If there are multiple users, check what each user would see in browse
    if (allUsers.length > 1 && activeOffers.length > 0) {
      console.log('\n=== Browse Simulation for Each User ===');
      for (const user of allUsers) {
        const offersForUser = activeOffers.filter(offer => offer.userId !== user.id);
        console.log(`\n${user.username} would see ${offersForUser.length} offers in browse:`);
        offersForUser.forEach(offer => {
          console.log(`  - ${offer.type} ${offer.amount} by ${offer.user.username}`);
        });
      }
    }
    
  } catch (error) {
    console.error('Error checking offers:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkOffers();
