# Debug Notification System Script
# This script helps debug why notifications are not being received

Write-Host "🔍 MUNygo Notification System Debug Script" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if services are running
function Test-Services {
    Write-Host "📋 Checking Services Status..." -ForegroundColor Yellow
    
    # Check if backend is running
    try {
        $backendResponse = Invoke-RestMethod -Uri "http://localhost:3000/health" -Method GET -TimeoutSec 5
        Write-Host "✅ Backend: Running" -ForegroundColor Green
        Write-Host "   - Status: $($backendResponse.status)" -ForegroundColor Gray
        Write-Host "   - Environment: $($backendResponse.environment)" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ Backend: Not running or unreachable" -ForegroundColor Red
        Write-Host "   Please start the backend with: npm run dev" -ForegroundColor Gray
        return $false
    }
    
    # Check if frontend is accessible
    try {
        $frontendResponse = Invoke-WebRequest -Uri "http://localhost:5173" -Method GET -TimeoutSec 5
        if ($frontendResponse.StatusCode -eq 200) {
            Write-Host "✅ Frontend: Running" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "❌ Frontend: Not running or unreachable" -ForegroundColor Red
        Write-Host "   Please start the frontend with: npm run dev" -ForegroundColor Gray
        return $false
    }
    
    Write-Host ""
    return $true
}

# Function to show debugging tips
function Show-DebuggingTips {
    Write-Host "🔧 Browser Debugging Tips..." -ForegroundColor Yellow
    
    Write-Host "To debug notifications in the browser:" -ForegroundColor Gray
    Write-Host "1. Open Developer Tools (F12)" -ForegroundColor White
    Write-Host "2. Go to Console tab" -ForegroundColor White
    Write-Host "3. Look for these log messages:" -ForegroundColor White
    Write-Host "   - '🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION:'" -ForegroundColor Green
    Write-Host "   - '[NotificationStore] Received NEW_NOTIFICATION via centralized manager:'" -ForegroundColor Green
    Write-Host "   - '[NotificationStore] Added new notification'" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Backend Console Messages to Look For:" -ForegroundColor Gray
    Write-Host "   - '🔔🔔🔔 [BACKEND] Emitting INTEREST_RECEIVED to user'" -ForegroundColor Green
    Write-Host "   - '[NotificationService] Notification created:'" -ForegroundColor Green
    Write-Host "   - '[NotificationService] Emitted NEW_NOTIFICATION to userId:'" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Common Issues:" -ForegroundColor Red
    Write-Host "❌ Socket not connected: Check connection status in navbar" -ForegroundColor White
    Write-Host "❌ Notifications not reaching frontend: Check JWT token validity" -ForegroundColor White
    Write-Host "❌ Notifications received but not displaying: Check Vue DevTools" -ForegroundColor White
    Write-Host ""
}

# Function to run manual test
function Test-OfferInterestFlow {
    Write-Host "🎯 Manual Notification Test..." -ForegroundColor Yellow
    
    Write-Host "Step-by-step test procedure:" -ForegroundColor Gray
    Write-Host "1. Open two incognito browser windows:" -ForegroundColor White
    Write-Host "   - Browser 1: http://localhost:5173" -ForegroundColor Cyan
    Write-Host "   - Browser 2: http://localhost:5173" -ForegroundColor Cyan
    Write-Host "2. Register/login as different users in each browser" -ForegroundColor White
    Write-Host "3. In Browser 1: Create an offer" -ForegroundColor White
    Write-Host "4. In Browser 2: Browse offers and express interest" -ForegroundColor White
    Write-Host "5. In Browser 1: Check the notification bell (should show red badge)" -ForegroundColor White
    Write-Host ""
    
    $continue = Read-Host "Press Enter to continue or 'q' to quit"
    if ($continue -eq 'q') { return }
}

# Function to check database
function Test-DatabaseQuery {
    Write-Host "🔍 Database Check..." -ForegroundColor Yellow
    
    Write-Host "Manual database query to check notifications:" -ForegroundColor Gray
    Write-Host "Connect to your database and run:" -ForegroundColor White
    Write-Host ""
    Write-Host "SELECT * FROM ""Notification"" ORDER BY ""createdAt"" DESC LIMIT 10;" -ForegroundColor Green
    Write-Host ""
    Write-Host "Look for:" -ForegroundColor White
    Write-Host "- Recent notifications with type 'NEW_INTEREST_ON_YOUR_OFFER'" -ForegroundColor Cyan
    Write-Host "- Correct userId (should match the offer creator)" -ForegroundColor Cyan
    Write-Host "- isRead status (should be false for new notifications)" -ForegroundColor Cyan
    Write-Host ""
}

# Main execution
Write-Host "Starting notification system debug..." -ForegroundColor Cyan

$servicesRunning = Test-Services

if ($servicesRunning) {
    Write-Host "Choose what to debug:" -ForegroundColor Yellow
    Write-Host "1. Show debugging tips" -ForegroundColor White
    Write-Host "2. Manual test procedure" -ForegroundColor White
    Write-Host "3. Database query guide" -ForegroundColor White
    Write-Host "4. All of the above" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Enter your choice (1-4)"
    
    switch ($choice) {
        "1" { Show-DebuggingTips }
        "2" { Test-OfferInterestFlow }
        "3" { Test-DatabaseQuery }
        "4" { 
            Show-DebuggingTips
            Test-OfferInterestFlow
            Test-DatabaseQuery
        }
        default { 
            Write-Host "Invalid choice. Showing all debugging information..." -ForegroundColor Yellow
            Show-DebuggingTips
            Test-OfferInterestFlow
            Test-DatabaseQuery
        }
    }
} else {
    Write-Host "❌ Cannot proceed: Required services are not running" -ForegroundColor Red
    Write-Host "Please start both backend and frontend services first" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🏁 Debug script completed!" -ForegroundColor Green
Write-Host "If issues persist, check browser console and backend logs for specific errors." -ForegroundColor Gray
