/**
 * Test script for Transactional Chat API endpoints
 * 
 * This script tests the API endpoints we just created for the Transactional Chat Cockpit
 * Run this script while the server is running to verify functionality
 */

const baseUrl = 'http://localhost:3000/api';

// Demo credentials - you'll need to create these users or use existing ones
const testCredentials = {
  email: '<EMAIL>',
  password: 'demo-password' // This would be your actual password
};

let authToken = '';

async function makeRequest(endpoint, method = 'GET', body = null, requireAuth = true) {
  const headers = {
    'Content-Type': 'application/json'
  };

  if (requireAuth && authToken) {
    headers['Authorization'] = `Bearer ${authToken}`;
  }

  const config = {
    method,
    headers
  };

  if (body) {
    config.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(`${baseUrl}${endpoint}`, config);
    const data = await response.json();
    
    console.log(`\n📡 ${method} ${endpoint}`);
    console.log(`Status: ${response.status}`);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    return { response, data };
  } catch (error) {
    console.error(`❌ Error calling ${endpoint}:`, error.message);
    return { error };
  }
}

async function runTests() {
  console.log('🧪 Testing Transactional Chat API Integration');
  console.log('============================================\n');

  // Test 1: Health check
  console.log('1️⃣ Testing Health Check...');
  await makeRequest('/health', 'GET', null, false);

  // Test 2: Try to access a demo transaction (this should fail without auth)
  console.log('\n2️⃣ Testing Unauthorized Access...');
  await makeRequest('/transactional-chat/tx-004', 'GET', null, false);

  // Note: For a full test, you would need to:
  // 1. Implement authentication (login) to get a token
  // 2. Then test the protected endpoints
  
  console.log('\n✅ Basic API structure tests completed!');
  console.log('\n📋 To test authenticated endpoints, you need to:');
  console.log('   1. Create demo users with proper authentication');
  console.log('   2. Login to get JWT tokens');
  console.log('   3. Test the transactional chat endpoints with tokens');
  
  console.log('\n🎯 Demo Transaction IDs available for testing:');
  console.log('   - tx-001: Step 1 - Payment Info Setup');
  console.log('   - tx-002: Step 2 - Negotiation');
  console.log('   - tx-003: Step 3 - Waiting for First Payer');
  console.log('   - tx-004: Step 4 - Confirm Receipt (KEY STEP)');
  console.log('   - tx-005: Step 5 - Your Turn to Pay');
  console.log('   - tx-006: Step 6 - Waiting for Second Payer Confirmation');
  console.log('   - tx-007: Step 7 - Transaction Complete');

  console.log('\n🚀 API Endpoints ready:');
  console.log('   GET    /api/transactional-chat/:transactionId');
  console.log('   POST   /api/transactional-chat/:transactionId/messages');
  console.log('   POST   /api/transactional-chat/:transactionId/actions');
  console.log('   GET    /api/transactional-chat/:transactionId/timer');
  console.log('   GET    /api/payment-info');
  console.log('   POST   /api/payment-info');
  console.log('   PUT    /api/payment-info/:id');
  console.log('   DELETE /api/payment-info/:id');
}

// Check if we're in Node.js environment
if (typeof fetch === 'undefined') {
  console.log('❌ This test script requires Node.js 18+ with fetch support');
  console.log('📋 Alternative: Test the endpoints using:');
  console.log('   - Postman');
  console.log('   - curl commands');
  console.log('   - Your frontend application');
  process.exit(1);
} else {
  runTests().catch(console.error);
}
