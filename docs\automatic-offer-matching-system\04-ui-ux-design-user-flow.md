# Automatic Offer Matching System - UI/UX Design & User Flow

## Document Overview
**Feature:** Automatic Offer Matching System  
**Version:** MVP (Minimum Viable Product)  
**Date:** December 2024  
**Status:** Foundation & Design Phase

## Table of Contents
1. [Design Philosophy](#design-philosophy)
2. [User Experience Overview](#user-experience-overview)
3. [Mobile-First Design System](#mobile-first-design-system)
4. [User Flow Documentation](#user-flow-documentation)
5. [Component Specifications](#component-specifications)
6. [Interaction Patterns](#interaction-patterns)
7. [Notification Design](#notification-design)
8. [Accessibility Guidelines](#accessibility-guidelines)

## Design Philosophy

### Mobile-First Principles
Based on MUNygo's established mobile-first architecture, the matching system follows these core principles:

1. **Touch-First Design**
   - Minimum 44px touch targets for all interactive elements
   - Thumb-zone optimization for primary actions
   - Gesture-based interactions (swipe to accept/decline)
   - Haptic feedback for confirmation actions

2. **Progressive Enhancement**
   - Core functionality works on all devices
   - Enhanced features for larger screens
   - Graceful degradation for older devices
   - Offline-first design where possible

3. **Visual Hierarchy**
   - Critical information (compatibility score, time remaining) prominently displayed
   - Secondary details available on demand
   - Clear visual distinction between action types
   - Consistent with existing MUNygo design language

4. **Performance-Focused**
   - Lazy loading for match details
   - Optimized images and icons
   - Minimal network requests
   - Smooth 60fps interactions

### Integration with Existing Design System
The matching system extends MUNygo's current design patterns:

- **Naive UI Components:** Leverage existing button, card, and modal components
- **Color Scheme:** Consistent with current theme system (light/dark mode)
- **Typography:** Following established font sizes and line heights
- **Spacing:** Using existing spacing scale and grid system
- **Icons:** Extending current icon library with match-specific icons

## User Experience Overview

### Primary User Journey
```mermaid
graph TD
    A[User Creates/Updates Offer] --> B{Match Detection Engine}
    B -->|Match Found| C[Push Notification]
    C --> D[Match Card in Dashboard]
    D --> E[User Views Match Details]
    E --> F{User Decision}
    F -->|Accept| G[Waiting for Other User]
    F -->|Decline| H[Match Removed]
    G --> I{Other User Response}
    I -->|Accept| J[Chat Session Created]
    I -->|Decline| K[Match Declined]
    J --> L[Transaction Flow Begins]
```

### Key User Scenarios

#### Scenario 1: New User Receives First Match
1. User creates their first offer
2. System finds compatible match within minutes
3. Push notification appears: "Great news! We found a match for your CAD→IRR offer"
4. User taps notification → opens match card
5. User sees compatibility score (92%) and other user's reputation
6. User accepts match → receives confirmation and next steps

#### Scenario 2: Experienced User Managing Multiple Matches
1. User has 5 active matches
2. Dashboard shows prioritized list (highest compatibility first)
3. One match expires in 2 hours (highlighted in orange)
4. User quickly swipes right to accept high-priority match
5. User swipes left to decline less attractive match
6. Real-time updates show other users' responses

#### Scenario 3: Mobile User in Transit
1. User receives match notification while commuting
2. Quick glance at notification shows key info (currency pair, compatibility)
3. User opens app → match card loads instantly
4. One-thumb operation to accept/decline
5. If accepted, clear indication of next steps
6. Seamless transition to chat when both users accept

## Mobile-First Design System

### Screen Size Breakpoints
Following MUNygo's responsive design strategy:

```css
/* Mobile-first breakpoints for match components */
.match-container {
  /* Base: Mobile (320px-767px) */
  padding: 1rem;
  gap: 1rem;
}

@media (min-width: 768px) {
  .match-container {
    /* Tablet enhancement */
    padding: 1.5rem;
    gap: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
  }
}

@media (min-width: 1024px) {
  .match-container {
    /* Desktop enhancement */
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    max-width: 1200px;
  }
}
```

### Component Sizing Standards
- **Touch Targets:** Minimum 44px (iOS HIG) / 48dp (Material Design)
- **Card Spacing:** 16px margins, 12px internal padding
- **Text Sizing:** 16px minimum for body text, 14px for secondary
- **Icon Sizing:** 24px for primary actions, 20px for secondary

### Color System for Matches
Extending MUNygo's existing color palette:

```typescript
interface MatchColors {
  // Compatibility indicators
  highCompatibility: '#10b981';   // Green - 85%+ compatibility
  mediumCompatibility: '#f59e0b'; // Amber - 70-84% compatibility
  lowCompatibility: '#ef4444';    // Red - Below 70%
  
  // Status indicators
  pending: '#6b7280';             // Gray - Awaiting response
  accepted: '#059669';            // Dark green - Accepted
  declined: '#dc2626';            // Dark red - Declined
  expired: '#9ca3af';             // Light gray - Expired
  
  // Urgency indicators
  expiringUrgent: '#dc2626';      // Red - < 2 hours
  expiringWarning: '#d97706';     // Orange - < 12 hours
  expiringNormal: '#6b7280';      // Gray - > 12 hours
}
```

## User Flow Documentation

### Flow 1: Receiving and Responding to a Match

#### Mobile User Flow
```
[Push Notification] 
"🎯 New match found! 92% compatibility"
    ↓ [Tap notification]
[Match Dashboard]
┌─────────────────────────────┐
│ 🎯 Your Matches (3 new)    │
│                            │
│ ┌─────────────────────────┐ │
│ │ 🟢 92% Match            │ │
│ │ TraderAli99             │ │
│ │ CAD 1,000 ↔ IRR 52M    │ │
│ │ ⏰ 23h remaining        │ │
│ │ [👁️ View] [✓ Accept]   │ │
│ └─────────────────────────┘ │
│                            │
│ ┌─────────────────────────┐ │
│ │ 🟡 78% Match           │ │
│ │ CryptoKing              │ │
│ │ EUR 800 ↔ IRR 42M      │ │
│ │ ⏰ 15h remaining        │ │
│ │ [👁️ View] [✓ Accept]   │ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
    ↓ [Tap "View" on top match]
[Match Details Modal]
┌─────────────────────────────┐
│ ← Match Details             │
│                            │
│ TraderAli99 ⭐⭐⭐⭐⭐     │
│ Reputation: 4.2 (127 trades)│
│                            │
│ 💱 Exchange Details        │
│ You give: CAD 1,000        │
│ You get: IRR 52,000,000    │
│ Rate: 52,000 IRR/CAD       │
│                            │
│ 🎯 Compatibility: 92%      │
│ ✓ Amount perfect match     │
│ ✓ Rate within 3% tolerance │
│ ✓ Both verified users      │
│                            │
│ ⏰ Expires in 22h 45m      │
│                            │
│ ┌─────────────────────────┐ │
│ │    [Decline] [Accept]   │ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
    ↓ [Tap "Accept"]
[Acceptance Confirmation]
┌─────────────────────────────┐
│ ✅ Match Accepted!          │
│                            │
│ 🎉 Great choice! TraderAli99│
│ has been notified of your   │
│ acceptance.                │
│                            │
│ Next steps:                │
│ • Waiting for their response│
│ • You'll get notified when │
│   they respond             │
│ • Chat will open if they   │
│   accept too               │
│                            │
│ ⏰ They have 22h to respond │
│                            │
│ [✓ Got it]                 │
└─────────────────────────────┘
```

#### Gesture-Based Quick Actions
For power users on mobile:

```
[Match Card with Gesture Support]
┌─────────────────────────────┐
│ 🟢 92% Match               │ ← Swipe right to accept
│ TraderAli99                │ ← Swipe left to decline  
│ CAD 1,000 ↔ IRR 52M       │ ← Tap for details
│ ⏰ 23h remaining           │ ← Long press for options
└─────────────────────────────┘

Swipe Right Animation:
┌─────────────────────────────┐
│ ✅ ACCEPTING...            │
│ TraderAli99                │
│ CAD 1,000 ↔ IRR 52M       │
│ ⏰ 23h remaining           │
└─────────────────────────────┘

Swipe Left Animation:
┌─────────────────────────────┐
│ ❌ DECLINING...            │
│ TraderAli99                │
│ CAD 1,000 ↔ IRR 52M       │
│ ⏰ 23h remaining           │
└─────────────────────────────┘
```

### Flow 2: Match Status Updates

#### Real-time Status Changes
```mermaid
sequenceDiagram
    participant U1 as User A
    participant S as Server
    participant U2 as User B
    
    U1->>S: Accept match
    S->>U1: Confirm acceptance
    S->>U2: Push notification: "User A accepted!"
    U2->>S: Accept match
    S->>U1: Push notification: "Match complete!"
    S->>U2: Confirm acceptance
    S->>U1: Create chat session
    S->>U2: Create chat session
    U1->>U1: Redirect to chat
    U2->>U2: Redirect to chat
```

#### Mobile Status Update UI
```
[Push Notification]
"✅ TraderAli99 accepted your match! Chat is ready."

[Updated Match Card]
┌─────────────────────────────┐
│ ✅ Match Successful!        │
│ TraderAli99                │
│ CAD 1,000 ↔ IRR 52M       │
│ Both users accepted        │
│ [💬 Open Chat]            │
└─────────────────────────────┘
```

## Component Specifications

### 1. MatchCard Component

#### Mobile Layout (Primary)
```vue
<template>
  <div class="match-card" :class="cardStatusClass">
    <!-- Header with compatibility and status -->
    <div class="match-header">
      <div class="compatibility-badge" :class="compatibilityClass">
        {{ match.compatibility.score }}% Match
      </div>
      <div class="time-remaining" :class="urgencyClass">
        ⏰ {{ formattedTimeRemaining }}
      </div>
    </div>
    
    <!-- User info -->
    <div class="user-info">
      <div class="user-avatar">
        <img :src="match.otherUser.avatar" :alt="match.otherUser.username" />
      </div>
      <div class="user-details">
        <h3 class="username">{{ match.otherUser.username }}</h3>
        <div class="reputation">
          <ReputationIcon :level="match.otherUser.reputationLevel" />
          <span>{{ match.otherUser.reputationScore }}</span>
        </div>
      </div>
    </div>
    
    <!-- Exchange details -->
    <div class="exchange-info">
      <div class="currency-flow">
        <div class="your-currency">
          <span class="amount">{{ formatCurrency(match.currencies.yourAmount) }}</span>
          <span class="currency">{{ match.currencies.yourCurrency }}</span>
        </div>
        <div class="exchange-icon">↔</div>
        <div class="their-currency">
          <span class="amount">{{ formatCurrency(match.currencies.theirAmount) }}</span>
          <span class="currency">{{ match.currencies.theirCurrency }}</span>
        </div>
      </div>
      <div class="exchange-rate">
        Rate: {{ formatRate(match.currencies.exchangeRate) }}
      </div>
    </div>
    
    <!-- Action buttons -->
    <div class="action-buttons">
      <n-button 
        type="default" 
        size="large" 
        @click="showDetails"
        class="view-button"
      >
        👁️ View Details
      </n-button>
      <n-button 
        type="success" 
        size="large" 
        @click="acceptMatch"
        class="accept-button"
        :loading="isAccepting"
      >
        ✓ Accept
      </n-button>
    </div>
  </div>
</template>

<style scoped>
.match-card {
  background: var(--card-color);
  border-radius: 12px;
  padding: 16px;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  touch-action: manipulation;
  transition: transform 0.2s ease;
}

.match-card:active {
  transform: scale(0.98);
}

.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.compatibility-badge {
  padding: 4px 8px;
  border-radius: 16px;
  font-weight: 600;
  font-size: 14px;
}

.compatibility-high { background: #d1fae5; color: #065f46; }
.compatibility-medium { background: #fef3c7; color: #92400e; }
.compatibility-low { background: #fee2e2; color: #991b1b; }

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.user-avatar img {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  object-fit: cover;
}

.user-details {
  margin-left: 12px;
  flex: 1;
}

.username {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.reputation {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: var(--text-color-2);
}

.exchange-info {
  background: var(--bg-color-2);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.currency-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.your-currency, .their-currency {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.amount {
  font-size: 16px;
  font-weight: 600;
}

.currency {
  font-size: 12px;
  color: var(--text-color-2);
}

.exchange-icon {
  font-size: 24px;
  color: var(--primary-color);
}

.exchange-rate {
  text-align: center;
  font-size: 14px;
  color: var(--text-color-2);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.view-button, .accept-button {
  flex: 1;
  height: 44px; /* Minimum touch target */
  border-radius: 8px;
  font-weight: 600;
}

@media (min-width: 768px) {
  .match-card {
    max-width: 400px;
    margin: 16px auto;
  }
  
  .action-buttons {
    flex-direction: row;
  }
}
</style>
```

### 2. MatchDetailsModal Component

#### Full-Screen Mobile Modal
```vue
<template>
  <n-modal 
    v-model:show="isVisible" 
    preset="card"
    class="match-details-modal"
    :class="{ 'mobile-fullscreen': isMobile }"
  >
    <!-- Header with back button -->
    <template #header>
      <div class="modal-header">
        <n-button text @click="close" class="back-button">
          ← Back
        </n-button>
        <h2>Match Details</h2>
      </div>
    </template>
    
    <!-- Scrollable content -->
    <div class="modal-content">
      <!-- User profile section -->
      <div class="user-profile-section">
        <div class="user-avatar-large">
          <img :src="match.otherUser.avatar" :alt="match.otherUser.username" />
        </div>
        <h3>{{ match.otherUser.username }}</h3>
        <div class="reputation-display">
          <ReputationIcon :level="match.otherUser.reputationLevel" size="large" />
          <span>{{ match.otherUser.reputationScore }} points</span>
          <span class="trade-count">({{ match.otherUser.tradeCount }} trades)</span>
        </div>
      </div>
      
      <!-- Compatibility analysis -->
      <div class="compatibility-section">
        <h4>🎯 Compatibility Analysis</h4>
        <div class="compatibility-score">
          <div class="score-circle" :class="compatibilityClass">
            {{ match.compatibility.score }}%
          </div>
          <div class="compatibility-factors">
            <div 
              v-for="factor in match.compatibility.factors" 
              :key="factor"
              class="factor-item"
            >
              ✓ {{ formatFactor(factor) }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- Exchange breakdown -->
      <div class="exchange-section">
        <h4>💱 Exchange Breakdown</h4>
        <div class="exchange-comparison">
          <div class="offer-comparison">
            <div class="your-offer">
              <h5>Your Offer</h5>
              <div class="offer-details">
                <div class="currency-amount">
                  {{ formatCurrency(match.offers.yourOffer.amount) }} 
                  {{ match.currencies.yourCurrency }}
                </div>
                <div class="rate-info">
                  Base rate: {{ formatRate(match.offers.yourOffer.baseRate) }}
                </div>
                <div class="effective-rate">
                  Your rate: {{ formatRate(match.offers.yourOffer.effectiveRate) }}
                </div>
              </div>
            </div>
            
            <div class="exchange-arrow">↔</div>
            
            <div class="their-offer">
              <h5>Their Offer</h5>
              <div class="offer-details">
                <div class="currency-amount">
                  {{ formatCurrency(match.offers.theirOffer.amount) }} 
                  {{ match.currencies.theirCurrency }}
                </div>
                <div class="rate-info">
                  Base rate: {{ formatRate(match.offers.theirOffer.baseRate) }}
                </div>
                <div class="effective-rate">
                  Their rate: {{ formatRate(match.offers.theirOffer.effectiveRate) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Timeline -->
      <div class="timeline-section">
        <h4>⏰ Match Timeline</h4>
        <div class="timeline">
          <div class="timeline-item completed">
            <div class="timeline-icon">✓</div>
            <div class="timeline-content">
              <div class="timeline-title">Match Detected</div>
              <div class="timeline-time">{{ formatDate(match.timeline.matchDetected) }}</div>
            </div>
          </div>
          <div class="timeline-item completed">
            <div class="timeline-icon">🔔</div>
            <div class="timeline-content">
              <div class="timeline-title">Both Users Notified</div>
              <div class="timeline-time">{{ formatDate(match.timeline.userANotified) }}</div>
            </div>
          </div>
          <div class="timeline-item pending">
            <div class="timeline-icon">⏳</div>
            <div class="timeline-content">
              <div class="timeline-title">Awaiting Responses</div>
              <div class="timeline-time">{{ formattedTimeRemaining }} remaining</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Potential outcome -->
      <div class="outcome-section" v-if="match.potentialOutcome">
        <h4>🎯 What Happens Next?</h4>
        <div class="outcome-preview">
          <div class="outcome-step">
            <div class="step-icon">💬</div>
            <div class="step-text">
              Chat session will be created for direct communication
            </div>
          </div>
          <div class="outcome-step">
            <div class="step-icon">📝</div>
            <div class="step-text">
              Transaction process begins with payment coordination
            </div>
          </div>
          <div class="outcome-step">
            <div class="step-icon">⏱️</div>
            <div class="step-text">
              Estimated completion: {{ match.potentialOutcome.estimatedCompletionTime }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Sticky action buttons -->
    <template #action>
      <div class="action-section">
        <div class="action-buttons-modal">
          <n-button 
            size="large" 
            type="error" 
            @click="declineMatch"
            class="decline-button"
            :loading="isDeclining"
          >
            ❌ Decline
          </n-button>
          <n-button 
            size="large" 
            type="success" 
            @click="acceptMatch"
            class="accept-button"
            :loading="isAccepting"
          >
            ✅ Accept Match
          </n-button>
        </div>
      </div>
    </template>
  </n-modal>
</template>

<style scoped>
.match-details-modal.mobile-fullscreen {
  padding: 0;
  height: 100vh;
  max-height: 100vh;
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  font-size: 16px;
  padding: 8px;
}

.modal-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 4px; /* For scrollbar space */
}

.user-profile-section {
  text-align: center;
  padding: 24px 0;
  border-bottom: 1px solid var(--border-color);
}

.user-avatar-large img {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  object-fit: cover;
  margin-bottom: 12px;
}

.reputation-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.trade-count {
  color: var(--text-color-2);
  font-size: 14px;
}

.compatibility-section, 
.exchange-section, 
.timeline-section, 
.outcome-section {
  padding: 24px 0;
  border-bottom: 1px solid var(--border-color);
}

.compatibility-section:last-child,
.exchange-section:last-child,
.timeline-section:last-child,
.outcome-section:last-child {
  border-bottom: none;
}

.compatibility-score {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 16px;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  background: var(--success-color-hover);
  color: white;
}

.compatibility-factors {
  flex: 1;
}

.factor-item {
  padding: 4px 0;
  color: var(--success-color);
  font-size: 14px;
}

.offer-comparison {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 16px;
}

.your-offer, .their-offer {
  flex: 1;
  background: var(--bg-color-2);
  padding: 16px;
  border-radius: 8px;
}

.exchange-arrow {
  font-size: 24px;
  color: var(--primary-color);
}

.currency-amount {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.rate-info, .effective-rate {
  font-size: 14px;
  color: var(--text-color-2);
  margin-bottom: 4px;
}

.timeline {
  margin-top: 16px;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.timeline-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.timeline-item.completed .timeline-icon {
  background: var(--success-color);
  color: white;
}

.timeline-item.pending .timeline-icon {
  background: var(--warning-color);
  color: white;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 14px;
  color: var(--text-color-2);
}

.outcome-preview {
  margin-top: 16px;
}

.outcome-step {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.step-icon {
  font-size: 20px;
}

.step-text {
  font-size: 14px;
  color: var(--text-color-2);
}

.action-section {
  padding: 16px 0 0 0;
  border-top: 1px solid var(--border-color);
}

.action-buttons-modal {
  display: flex;
  gap: 12px;
}

.decline-button, .accept-button {
  flex: 1;
  height: 48px;
  border-radius: 8px;
  font-weight: 600;
}

/* Mobile optimizations */
@media (max-width: 767px) {
  .offer-comparison {
    flex-direction: column;
    gap: 12px;
  }
  
  .exchange-arrow {
    transform: rotate(90deg);
  }
  
  .compatibility-score {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}
</style>
```

### 3. MatchNotificationBell Component

#### Mobile-Optimized Notification Indicator
```vue
<template>
  <div class="match-notification-bell" @click="openMatchDashboard">
    <n-badge 
      :value="unreadMatchCount" 
      :max="99"
      :show="unreadMatchCount > 0"
      type="error"
    >
      <n-button 
        text 
        class="notification-button"
        :class="{ 'has-matches': unreadMatchCount > 0 }"
      >
        <template #icon>
          <n-icon size="24">
            <MatchIcon />
          </n-icon>
        </template>
      </n-button>
    </n-badge>
    
    <!-- Pulse animation for new matches -->
    <div 
      v-if="hasNewMatches" 
      class="pulse-animation"
    ></div>
  </div>
</template>

<style scoped>
.match-notification-bell {
  position: relative;
  display: inline-block;
}

.notification-button {
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.notification-button.has-matches {
  background: var(--primary-color-hover);
  color: var(--primary-color);
}

.pulse-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: var(--primary-color);
  opacity: 0.6;
  animation: pulse 2s infinite;
  pointer-events: none;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

/* Ensure minimum touch target */
.notification-button {
  min-width: 44px;
  min-height: 44px;
}
</style>
```

## Interaction Patterns

### Gesture-Based Interactions

#### Swipe Actions for Match Cards
```typescript
interface SwipeGestureConfig {
  swipeThreshold: number;      // 120px minimum swipe distance
  velocityThreshold: number;   // 0.3 minimum velocity
  snapBackDuration: number;    // 300ms animation
  confirmationDelay: number;   // 500ms before action executes
}

// Swipe right = Accept
// Swipe left = Decline
// Tap = View details
// Long press = Show options menu
```

#### Touch Feedback Patterns
```css
/* Haptic feedback for supported devices */
.match-card:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* Visual feedback for actions */
.accepting {
  background: linear-gradient(45deg, #10b981, #059669);
  color: white;
}

.declining {
  background: linear-gradient(45deg, #ef4444, #dc2626);
  color: white;
}
```

### Loading States and Transitions

#### Skeleton Loading for Match Cards
```vue
<template>
  <div class="match-card-skeleton" v-if="isLoading">
    <div class="skeleton-header">
      <div class="skeleton-badge"></div>
      <div class="skeleton-time"></div>
    </div>
    <div class="skeleton-user">
      <div class="skeleton-avatar"></div>
      <div class="skeleton-name"></div>
    </div>
    <div class="skeleton-exchange">
      <div class="skeleton-amount"></div>
      <div class="skeleton-rate"></div>
    </div>
    <div class="skeleton-buttons">
      <div class="skeleton-button"></div>
      <div class="skeleton-button"></div>
    </div>
  </div>
</template>

<style scoped>
.skeleton-badge, .skeleton-time, .skeleton-avatar, 
.skeleton-name, .skeleton-amount, .skeleton-rate, 
.skeleton-button {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
</style>
```

## Notification Design

### Push Notification Templates

#### High-Priority Match Found
```json
{
  "title": "🎯 Great match found!",
  "body": "92% compatibility • CAD 1,000 ↔ IRR 52M • Expires in 24h",
  "data": {
    "type": "MATCH_FOUND",
    "matchId": "MATCH_20241201_001",
    "priority": "HIGH",
    "deepLink": "/matches/MATCH_20241201_001"
  },
  "android": {
    "priority": "high",
    "notification": {
      "channel_id": "matches_high",
      "color": "#10b981",
      "vibrate": [100, 50, 100]
    }
  },
  "ios": {
    "payload": {
      "aps": {
        "sound": "match_found.caf",
        "badge": "+1"
      }
    }
  }
}
```

#### Match Response Notification
```json
{
  "title": "✅ TraderAli99 accepted your match!",
  "body": "Chat is now ready. Start your exchange.",
  "data": {
    "type": "MATCH_ACCEPTED",
    "matchId": "MATCH_20241201_001",
    "chatSessionId": "chat_abc123",
    "deepLink": "/chat/chat_abc123"
  }
}
```

### In-App Notification Design

#### Toast Notifications
```vue
<template>
  <div class="match-toast" :class="toastType">
    <div class="toast-icon">
      <component :is="iconComponent" />
    </div>
    <div class="toast-content">
      <div class="toast-title">{{ title }}</div>
      <div class="toast-message">{{ message }}</div>
    </div>
    <div class="toast-action" v-if="actionText">
      <n-button size="small" @click="handleAction">
        {{ actionText }}
      </n-button>
    </div>
  </div>
</template>

<style scoped>
.match-toast {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  margin: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  border-left: 4px solid var(--primary-color);
}

.toast-icon {
  font-size: 24px;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.toast-message {
  font-size: 14px;
  color: var(--text-color-2);
}

.match-toast.success {
  border-left-color: var(--success-color);
}

.match-toast.warning {
  border-left-color: var(--warning-color);
}

.match-toast.error {
  border-left-color: var(--error-color);
}
</style>
```

## Accessibility Guidelines

### Screen Reader Support
```html
<!-- Match card with proper ARIA labels -->
<div 
  class="match-card" 
  role="button"
  tabindex="0"
  :aria-label="`Match with ${match.otherUser.username}, ${match.compatibility.score}% compatibility, ${formattedTimeRemaining} remaining`"
  @keydown.enter="showDetails"
  @keydown.space="showDetails"
>
  <!-- Card content -->
</div>
```

### Color Accessibility
- **Contrast Ratios:** Minimum 4.5:1 for normal text, 3:1 for large text
- **Color Indicators:** Never rely solely on color; include icons and text
- **Dark Mode Support:** Full compatibility with existing dark theme

### Keyboard Navigation
- **Tab Order:** Logical navigation through match cards and actions
- **Keyboard Shortcuts:** Space/Enter to open details, Arrow keys to navigate
- **Focus Management:** Clear focus indicators and proper focus trapping in modals

### Reduced Motion Support
```css
@media (prefers-reduced-motion: reduce) {
  .match-card, .pulse-animation, .skeleton-loading {
    animation: none;
  }
  
  .match-card:active {
    transform: none;
  }
  
  * {
    transition-duration: 0.01ms !important;
  }
}
```

---

## Implementation Roadmap

### Phase 1: Core Components (Week 1-2)
1. MatchCard component with basic layout
2. Match dashboard page integration
3. Basic gesture recognition for swipe actions
4. Integration with existing notification system

### Phase 2: Enhanced UX (Week 3-4)
1. MatchDetailsModal with full feature set
2. Real-time status updates and animations
3. Advanced touch interactions and haptic feedback
4. Comprehensive accessibility implementation

### Phase 3: Optimization (Week 5-6)
1. Performance optimization for large match lists
2. Advanced gesture patterns and shortcuts
3. Offline support and data persistence
4. A/B testing framework for UX improvements

This UI/UX design documentation provides a comprehensive foundation for implementing a mobile-first, accessible, and intuitive automatic offer matching system that seamlessly integrates with MUNygo's existing design language and user experience patterns.
