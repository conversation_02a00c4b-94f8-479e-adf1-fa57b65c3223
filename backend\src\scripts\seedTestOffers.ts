import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting to seed test offers...');

  // Get test users
  const testUsers = await prisma.user.findMany({
    where: {
      email: {
        in: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
      }
    }
  });

  if (testUsers.length === 0) {
    console.error('No test users found. Please run seed:test-users first.');
    return;
  }

  const [user1, user2, user3] = testUsers;

  const testOffers = [
    // User 1 (h) offers
    {
      userId: user1.id,
      type: 'SELL' as const,
      currencyPair: 'USD-IRR',
      amount: 1000,
      baseRate: 42000, // 42,000 IRR per USD
      adjustmentForLowerRep: 0.02, // 2% adjustment for lower reputation
      adjustmentForHigherRep: -0.01, // 1% discount for higher reputation
      status: 'ACTIVE' as const
    },
    {
      userId: user1.id,
      type: 'BUY' as const,
      currencyPair: 'USD-TRY',
      amount: 1000,
      baseRate: 30, // 30 TRY per USD
      adjustmentForLowerRep: 0.03,
      adjustmentForHigherRep: -0.015,
      status: 'ACTIVE' as const
    },
    // User 2 (h2) offers
    {
      userId: user2.id,
      type: 'SELL' as const,
      currencyPair: 'EUR-TRY',
      amount: 500,
      baseRate: 35, // 35 TRY per EUR
      adjustmentForLowerRep: 0.025,
      adjustmentForHigherRep: -0.01,
      status: 'ACTIVE' as const
    },
    {
      userId: user2.id,
      type: 'BUY' as const,
      currencyPair: 'EUR-IRR',
      amount: 400,
      baseRate: 45000, // 45,000 IRR per EUR
      adjustmentForLowerRep: 0.02,
      adjustmentForHigherRep: -0.01,
      status: 'ACTIVE' as const
    },
    // User 3 (h3) offers
    {
      userId: user3.id,
      type: 'SELL' as const,
      currencyPair: 'IRR-TRY',
      amount: 50000000, // 50M IRR
      baseRate: 0.0008, // 0.0008 TRY per IRR
      adjustmentForLowerRep: 0.03,
      adjustmentForHigherRep: -0.015,
      status: 'ACTIVE' as const
    },
    {
      userId: user3.id,
      type: 'BUY' as const,
      currencyPair: 'USD-IRR',
      amount: 800,
      baseRate: 42000, // 42,000 IRR per USD
      adjustmentForLowerRep: 0.025,
      adjustmentForHigherRep: -0.01,
      status: 'ACTIVE' as const
    }
  ];

  for (const offerData of testOffers) {
    try {
      // Check if similar offer already exists
      const existing = await prisma.offer.findFirst({
        where: {
          userId: offerData.userId,
          currencyPair: offerData.currencyPair,
          amount: offerData.amount
        }
      });

      if (!existing) {
        const offer = await prisma.offer.create({
          data: {
            userId: offerData.userId,
            type: offerData.type,
            currencyPair: offerData.currencyPair,
            amount: offerData.amount,
            baseRate: offerData.baseRate,
            adjustmentForLowerRep: offerData.adjustmentForLowerRep,
            adjustmentForHigherRep: offerData.adjustmentForHigherRep,
            status: offerData.status,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });

        const user = testUsers.find(u => u.id === offerData.userId);
        console.log(`Created offer for ${user?.email}: ${offerData.type} ${offerData.amount} on ${offerData.currencyPair} at rate ${offerData.baseRate}`);
      } else {
        const user = testUsers.find(u => u.id === offerData.userId);
        console.log(`Similar offer already exists for ${user?.email}: ${offerData.currencyPair}`);
      }
    } catch (error) {
      console.error(`Failed to create offer:`, error);
    }
  }

  console.log('Test offers seeding complete.');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('Test offers seeding script failed:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
