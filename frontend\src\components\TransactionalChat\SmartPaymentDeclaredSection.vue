<template>
  <div class="smart-payment-declared-section" data-testid="smart-payment-declared-section">
    
    <!-- Compact Status Header -->
    <div class="status-header">
      <div class="status-left">
        <div class="status-icon">✅</div>
        <div class="status-content">
          <h4 class="status-title">
            {{ t('transactionalChat.actionCards.paymentDeclared.title', { 
              amount: formattedAmount 
            }) }}
          </h4>
          <p class="status-subtitle">
            {{ t('transactionalChat.actionCards.paymentDeclared.waitingTitle', {
              recipient: recipientName
            }) }}
          </p>
        </div>
      </div>
      
      <!-- Timer (compact, on the right) -->
      <div v-if="timerDisplayValue" class="timer-compact" :class="timerColorClass">
        <div class="timer-value">{{ timerDisplayValue }}</div>
        <div class="timer-label">{{ timerLabel }}</div>
      </div>
    </div>

    <!-- Quick Details (condensed) -->
    <div class="quick-details">
      <div class="detail-row">
        <span class="label">{{ t('transactionalChat.actionCards.paymentDeclared.sentTo') }}:</span>
        <span class="value">{{ recipientName }}</span>
      </div>
      <div class="detail-row">
        <span class="label">{{ t('transactionalChat.actionCards.paymentDeclared.amountSent') }}:</span>
        <span class="value amount">{{ formattedAmount }}</span>
      </div>
      <div v-if="paymentDeclaration.trackingNumber" class="detail-row">
        <span class="label">{{ t('transactionalChat.actionCards.paymentDeclared.trackingNumber') }}:</span>
        <span class="value">{{ paymentDeclaration.trackingNumber }}</span>
      </div>
    </div>

    <!-- Waiting Status (minimal) -->
    <div class="waiting-minimal">
      <span class="waiting-icon">⏳</span>
      <span class="waiting-text">
        {{ t('transactionalChat.actionCards.paymentDeclared.waitingDescription', {
          recipient: recipientName,
          amount: formattedAmount
        }) }}
      </span>
    </div>

    <!-- Compact Actions -->
    <div v-if="canContactSupport" class="compact-actions">
      <button 
        class="support-button"
        @click="handleContactSupport"
        data-testid="contact-support-button"
      >
        📞 {{ t('transactionalChat.actionCards.paymentDeclared.contactSupport') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useMessage } from 'naive-ui'
import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic'
import { useTimerDisplay } from '@/composables/useTimerDisplay'


interface PaymentDeclaration {
  amount: number
  currency: string
  declaredBy: string
  declaredAt: string // ISO string
  trackingNumber?: string
  reference?: string
}

interface Props {
  paymentDeclaration: PaymentDeclaration
  recipientName: string
  chatSessionId?: string
  canContactSupport?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  chatSessionId: undefined,
  canContactSupport: true
})

const emit = defineEmits<{
  contactSupport: []
}>()

const { t } = useI18n()
const message = useMessage()


// Timer Logic (using same composable as other components)
const {
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
} = useTransactionFlowLogic(
  computed(() => props.chatSessionId || null),
  message
);

// Use shared timer display logic
const {
  timerDisplayValue,
  timerColorClass,
  timerLabel
} = useTimerDisplay({
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
});

// Computed properties
const formattedAmount = computed(() => {
  console.log('[DECLARED] Formatting amount', { 
    amount: props.paymentDeclaration.amount, 
    currency: props.paymentDeclaration.currency 
  })
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: props.paymentDeclaration.currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(props.paymentDeclaration.amount)
})

// Methods
const handleContactSupport = () => {
  console.log('[DECLARED] Contact support requested')
  emit('contactSupport')
  message.info(t('transactionalChat.actionCards.paymentDeclared.supportContacted'))
}
</script>

<style scoped>
.smart-payment-declared-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 100%;
}

/* Compact Status Header */
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: var(--tc-success-light);
  border: 1px solid var(--tc-success);
  border-radius: 8px;
  gap: 12px;
}

.status-left {
  display: flex;
  gap: 12px;
  flex: 1;
  min-width: 0; /* Allow text truncation */
}

.status-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.status-content {
  flex: 1;
  min-width: 0;
}

.status-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--tc-success);
  line-height: 1.3;
}

.status-subtitle {
  margin: 0;
  font-size: 13px;
  color: var(--tc-success);
  opacity: 0.8;
  line-height: 1.3;
}

/* Compact Timer */
.timer-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  min-width: 70px;
  flex-shrink: 0;
  border: 1px solid #10b981;
  background-color: rgba(16, 185, 129, 0.1);
}

.timer-compact.timer-critical {
  border-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  animation: pulse 2s infinite;
}

.timer-compact.timer-warning {
  border-color: #fb923c;
  background-color: rgba(251, 146, 60, 0.1);
}

.timer-compact .timer-value {
  font-size: 14px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  color: #065f46;
  line-height: 1;
}

.timer-compact .timer-label {
  font-size: 10px;
  font-weight: 500;
  color: #065f46;
  opacity: 0.7;
  line-height: 1;
  margin-top: 2px;
}

.timer-compact.timer-critical .timer-value,
.timer-compact.timer-critical .timer-label {
  color: #dc2626;
}

.timer-compact.timer-warning .timer-value,
.timer-compact.timer-warning .timer-label {
  color: #c2410c;
}

/* Quick Details */
.quick-details {
  background: var(--tc-bg-subtle);
  border: 1px solid var(--tc-border-light);
  border-radius: 8px;
  padding: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid var(--tc-border-light);
}

.detail-row:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.detail-row:first-child {
  padding-top: 0;
}

.label {
  font-size: 13px;
  color: var(--tc-text-secondary);
  font-weight: 500;
}

.value {
  font-size: 13px;
  color: var(--tc-text-primary);
  font-weight: 600;
  text-align: right;
}

.value.amount {
  color: var(--tc-success);
  font-family: monospace;
}

/* Waiting Status (minimal) */
.waiting-minimal {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: var(--tc-warning-light);
  border: 1px solid var(--tc-warning);
  border-radius: 8px;
}

.waiting-icon {
  font-size: 20px;
  flex-shrink: 0;
  animation: pulse 2s infinite;
}

.waiting-text {
  font-size: 13px;
  color: var(--tc-warning);
  line-height: 1.4;
  flex: 1;
}

/* Compact Actions */
.compact-actions {
  display: flex;
  justify-content: center;
}

.support-button {
  padding: 10px 16px;
  background: var(--tc-bg-subtle);
  border: 1px solid var(--tc-border-medium);
  border-radius: 6px;
  color: var(--tc-text-secondary);
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.support-button:hover:not(:disabled) {
  background: var(--tc-primary-light);
  color: var(--tc-primary);
  border-color: var(--tc-primary);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* Mobile Optimization */
@media (max-width: 480px) {
  .status-header {
    padding: 12px;
    gap: 8px;
  }
  
  .status-title {
    font-size: 15px;
  }
  
  .status-subtitle {
    font-size: 12px;
  }
  
  .timer-compact {
    min-width: 60px;
    padding: 6px 8px;
  }
  
  .timer-compact .timer-value {
    font-size: 12px;
  }
  
  .timer-compact .timer-label {
    font-size: 9px;
  }
  
  .quick-details {
    padding: 10px;
  }
  
  .detail-row {
    padding: 4px 0;
  }
  
  .label,
  .value {
    font-size: 12px;
  }
  
  .waiting-minimal {
    padding: 10px;
    gap: 8px;
  }
  
  .waiting-text {
    font-size: 12px;
  }
}

/* RTL Support */
[dir="rtl"] .status-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .status-left {
  flex-direction: row-reverse;
}

[dir="rtl"] .detail-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .waiting-minimal {
  flex-direction: row-reverse;
}

[dir="rtl"] .value {
  text-align: left;
}
</style>
