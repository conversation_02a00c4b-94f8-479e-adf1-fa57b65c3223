import { Hono } from 'hono';
import { Server } from 'socket.io';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { authMiddleware, AuthVariables } from '../middleware/auth';
import { TransactionalChatService } from '../services/transactionalChatService';
import { NotificationService } from '../services/notificationService';
import { ChatService } from '../services/chatService';
import { TransactionService, getTransactionByChatSessionId } from '../services/transactionService';
import { PayerNegotiationService } from '../services/payerNegotiationService';

// Validation schemas
const SendMessageSchema = z.object({
  messageText: z.string().min(1, 'Message cannot be empty').max(1000, 'Message too long')
});

const PerformActionSchema = z.object({
  actionType: z.string().min(1, 'Action type is required'),
  data: z.any().optional()
});

// Variables available to handlers in this route module
type TransactionalChatRoutesVariables = AuthVariables & {
  transactionalChatService: TransactionalChatService;
  io: Server;
  notificationService: NotificationService;
  chatService: ChatService;
  transactionService: TransactionService;
};

export function createTransactionalChatRoutes(
  prisma: PrismaClient,
  payerNegotiationService: PayerNegotiationService
) {
  const transactionalChatRoutes = new Hono<{ Variables: TransactionalChatRoutesVariables }>();

  // Middleware to inject services
  transactionalChatRoutes.use('*', authMiddleware, async (c, next) => {
    const ioFromContext = c.get('io');
    const notificationServiceFromContext = c.get('notificationService');
    const chatServiceFromContext = c.get('chatService');
    const transactionServiceFromContext = c.get('transactionService');

    if (!ioFromContext || !notificationServiceFromContext || !chatServiceFromContext || !transactionServiceFromContext) {
      console.error('[TransactionalChatRoutes] Required services not found in context');
      return c.json({ error: 'Server configuration error' }, 500);
    }

    const transactionalChatService = new TransactionalChatService(
      prisma,
      ioFromContext,
      notificationServiceFromContext,
      chatServiceFromContext,
      transactionServiceFromContext,
      payerNegotiationService
    );

    c.set('transactionalChatService', transactionalChatService);
    await next();
  });

  /**
   * GET /api/transactional-chat/by-chat-session/:chatSessionId
   * Get transaction ID from chat session ID and redirect to transaction chat
   */
  transactionalChatRoutes.get('/by-chat-session/:chatSessionId', async (c) => {
    const { chatSessionId } = c.req.param();
    const { userId } = c.get('jwtPayload');

    try {
      console.log(`[TransactionalChatRoutes] GET /by-chat-session/:chatSessionId - ${chatSessionId} for user ${userId}`);
      
      // Use the existing method from TransactionService to get transaction by chatSessionId
      const transaction = await getTransactionByChatSessionId(chatSessionId, userId);
      
      if (!transaction) {
        return c.json({ 
          success: false, 
          error: 'Transaction not found for this chat session or access denied' 
        }, 404);
      }

      return c.json({
        success: true,
        data: {
          transactionId: transaction.id,
          chatSessionId: chatSessionId
        }
      });
    } catch (error: any) {
      console.error(`[TransactionalChatRoutes] Error resolving chat session to transaction:`, error);
      
      return c.json({ 
        success: false, 
        error: 'Failed to resolve chat session to transaction' 
      }, 500);
    }
  });

  /**
   * GET /api/transactional-chat/:transactionId
   * Get complete transaction chat details including messages, steps, and state
   */
  transactionalChatRoutes.get('/:transactionId', async (c) => {
    const { transactionId } = c.req.param();
    const { userId } = c.get('jwtPayload');
    const transactionalChatService = c.get('transactionalChatService');

    try {
      console.log(`[TransactionalChatRoutes] GET /:transactionId - ${transactionId} for user ${userId}`);
      
      const transactionChatDetails = await transactionalChatService.getTransactionChatDetails(
        transactionId,
        userId
      );

      return c.json({
        success: true,
        data: transactionChatDetails
      });
    } catch (error: any) {
      console.error(`[TransactionalChatRoutes] Error fetching transaction chat details:`, error);
      
      if (error.message?.includes('not found') || error.message?.includes('access denied')) {
        return c.json({ 
          success: false, 
          error: 'Transaction not found or access denied' 
        }, 404);
      }
      
      return c.json({ 
        success: false, 
        error: 'Failed to fetch transaction chat details' 
      }, 500);
    }
  });

  /**
   * POST /api/transactional-chat/:transactionId/messages
   * Send a chat message in the transaction context
   */
  transactionalChatRoutes.post(
    '/:transactionId/messages',
    zValidator('json', SendMessageSchema),
    async (c) => {
      const { transactionId } = c.req.param();
      const { userId } = c.get('jwtPayload');
      const { messageText } = c.req.valid('json');
      const transactionalChatService = c.get('transactionalChatService');

      try {
        console.log(`[TransactionalChatRoutes] POST /:transactionId/messages - ${transactionId} from user ${userId}`);
        
        const messagePayload = await transactionalChatService.sendTransactionMessage(
          transactionId,
          userId,
          messageText
        );

        return c.json({
          success: true,
          data: messagePayload,
          message: 'Message sent successfully'
        });
      } catch (error: any) {
        console.error(`[TransactionalChatRoutes] Error sending message:`, error);
        
        if (error.message?.includes('not found') || error.message?.includes('access denied')) {
          return c.json({ 
            success: false, 
            error: 'Transaction not found or access denied' 
          }, 404);
        }
        
        return c.json({ 
          success: false, 
          error: 'Failed to send message' 
        }, 500);
      }
    }
  );

  /**
   * POST /api/transactional-chat/:transactionId/actions
   * Perform a transaction action (confirm receipt, send payment, etc.)
   */
  transactionalChatRoutes.post(
    '/:transactionId/actions',
    zValidator('json', PerformActionSchema),
    async (c) => {
      const { transactionId } = c.req.param();
      const { userId } = c.get('jwtPayload');
      const { actionType, data } = c.req.valid('json');
      const transactionalChatService = c.get('transactionalChatService');

      try {
        console.log(`[TransactionalChatRoutes] POST /:transactionId/actions - ${actionType} for transaction ${transactionId}, user ${userId}`);
        
        await transactionalChatService.performTransactionAction(
          transactionId,
          userId,
          actionType,
          data
        );

        return c.json({
          success: true,
          message: 'Action performed successfully'
        });
      } catch (error: any) {
        console.error(`[TransactionalChatRoutes] Error performing action:`, error);
        
        if (error.message?.includes('not found') || error.message?.includes('access denied')) {
          return c.json({ 
            success: false, 
            error: 'Transaction not found or access denied' 
          }, 404);
        }
        
        if (error.message?.includes('Invalid transaction state') || error.message?.includes('Unknown action type')) {
          return c.json({ 
            success: false, 
            error: error.message 
          }, 400);
        }
        
        return c.json({ 
          success: false, 
          error: 'Failed to perform action' 
        }, 500);
      }
    }
  );

  /**
   * GET /api/transactional-chat/:transactionId/timer
   * Get current timer status for time-sensitive actions
   */
  transactionalChatRoutes.get('/:transactionId/timer', async (c) => {
    const { transactionId } = c.req.param();
    const { userId } = c.get('jwtPayload');
    const transactionalChatService = c.get('transactionalChatService');

    try {
      console.log(`[TransactionalChatRoutes] GET /:transactionId/timer - ${transactionId} for user ${userId}`);
      
      const transactionChatDetails = await transactionalChatService.getTransactionChatDetails(
        transactionId,
        userId
      );

      return c.json({
        success: true,
        data: {
          timer: transactionChatDetails.timer || { isActive: false, remainingSeconds: 0 }
        }
      });
    } catch (error: any) {
      console.error(`[TransactionalChatRoutes] Error fetching timer:`, error);
      
      if (error.message?.includes('not found') || error.message?.includes('access denied')) {
        return c.json({ 
          success: false, 
          error: 'Transaction not found or access denied' 
        }, 404);
      }
      
      return c.json({ 
        success: false, 
        error: 'Failed to fetch timer status' 
      }, 500);
    }
  });

  return transactionalChatRoutes;
}
