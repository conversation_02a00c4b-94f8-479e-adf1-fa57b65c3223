// Simple script to verify i18n key consistency
import fs from 'fs';

const enOffersPath = 'c:/Code/MUNygo/frontend/src/locales/en/offers.json';
const faOffersPath = 'c:/Code/MUNygo/frontend/src/locales/fa/offers.json';

function extractKeys(obj, prefix = '') {
  const keys = [];
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof value === 'object' && value !== null) {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  return keys;
}

try {
  const enData = JSON.parse(fs.readFileSync(enOffersPath, 'utf8'));
  const faData = JSON.parse(fs.readFileSync(faOffersPath, 'utf8'));
  
  const enKeys = extractKeys(enData).sort();
  const faKeys = extractKeys(faData).sort();
  
  console.log('English keys count:', enKeys.length);
  console.log('Persian keys count:', faKeys.length);
  
  const missingInFa = enKeys.filter(key => !faKeys.includes(key));
  const missingInEn = faKeys.filter(key => !enKeys.includes(key));
  
  if (missingInFa.length > 0) {
    console.log('\nKeys missing in Persian:', missingInFa);
  }
  
  if (missingInEn.length > 0) {
    console.log('\nKeys missing in English:', missingInEn);
  }
  
  if (missingInFa.length === 0 && missingInEn.length === 0) {
    console.log('\n✅ All i18n keys are consistent between English and Persian!');
  }
  
  // Check for obsolete top-level status keys
  const obsoleteKeys = ['statusActive', 'statusInactive'];
  const foundObsoleteInEn = obsoleteKeys.filter(key => enKeys.includes(key));
  const foundObsoleteInFa = obsoleteKeys.filter(key => faKeys.includes(key));
  
  if (foundObsoleteInEn.length === 0 && foundObsoleteInFa.length === 0) {
    console.log('✅ Obsolete status keys have been removed successfully!');
  } else {
    console.log('❌ Found obsolete keys:');
    if (foundObsoleteInEn.length > 0) console.log('  English:', foundObsoleteInEn);
    if (foundObsoleteInFa.length > 0) console.log('  Persian:', foundObsoleteInFa);
  }
  
} catch (error) {
  console.error('Error:', error.message);
}
