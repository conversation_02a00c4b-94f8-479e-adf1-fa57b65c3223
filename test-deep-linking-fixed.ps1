# Deep Linking Notification Tests Runner
# Run comprehensive tests for the deep linking functionality

Write-Host "🚀 Deep Linking Notification Feature - Comprehensive Test Suite" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Cyan

# Check if we're in the correct directory
$currentDir = Get-Location
$expectedDir = "C:\Code\MUNygo"

if ($currentDir.Path -ne $expectedDir) {
    Write-Host "❌ Please run this script from the MUNygo root directory: $expectedDir" -ForegroundColor Red
    Write-Host "Current directory: $($currentDir.Path)" -ForegroundColor Yellow
    exit 1
}

# Check if node modules are installed
Write-Host "🔍 Checking dependencies..." -ForegroundColor Yellow

if (!(Test-Path "frontend\node_modules")) {
    Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Yellow
    Set-Location "frontend"
    npm install
    Set-Location ".."
}

if (!(Test-Path "backend\node_modules")) {
    Write-Host "📦 Installing backend dependencies..." -ForegroundColor Yellow
    Set-Location "backend"
    npm install
    Set-Location ".."
}

Write-Host "✅ Dependencies checked" -ForegroundColor Green

# Frontend Tests
Write-Host "`n📱 FRONTEND TESTS" -ForegroundColor Cyan
Write-Host ("-" * 30) -ForegroundColor Cyan

Write-Host "🧪 Running NotificationBell Component Tests..." -ForegroundColor Yellow
Set-Location "frontend"
npm test src/test/components/NotificationBell.test.ts
$frontendResult1 = $LASTEXITCODE

Write-Host "🧪 Running OfferDetailsView Component Tests..." -ForegroundColor Yellow
npm test src/test/views/OfferDetailsView.test.ts
$frontendResult2 = $LASTEXITCODE

Write-Host "🧪 Running OfferService Tests..." -ForegroundColor Yellow
npm test src/test/services/offerService.test.ts
$frontendResult3 = $LASTEXITCODE

Write-Host "🧪 Running Deep Linking Integration Tests..." -ForegroundColor Yellow
npm test src/test/integration/deepLinking.test.ts
$frontendResult4 = $LASTEXITCODE

Set-Location ".."

# Backend Tests
Write-Host "`n🔧 BACKEND TESTS" -ForegroundColor Cyan
Write-Host ("-" * 30) -ForegroundColor Cyan

Write-Host "🧪 Running Offer Details Endpoint Tests..." -ForegroundColor Yellow
Set-Location "backend"
npm test src/test/routes/offerDetails.test.ts
$backendResult1 = $LASTEXITCODE
Set-Location ".."

# Type Checking
Write-Host "`n🔍 TYPE CHECKING" -ForegroundColor Cyan
Write-Host ("-" * 30) -ForegroundColor Cyan

Write-Host "🔍 Frontend TypeScript Check..." -ForegroundColor Yellow
Set-Location "frontend"
npx tsc --noEmit
$frontendTypeCheck = $LASTEXITCODE
Set-Location ".."

Write-Host "🔍 Backend TypeScript Check..." -ForegroundColor Yellow
Set-Location "backend"
npx tsc --noEmit
$backendTypeCheck = $LASTEXITCODE
Set-Location ".."

# Calculate Results
$testResults = @(
    @{ Name = "NotificationBell Component"; Result = $frontendResult1 },
    @{ Name = "OfferDetailsView Component"; Result = $frontendResult2 },
    @{ Name = "OfferService"; Result = $frontendResult3 },
    @{ Name = "Deep Linking Integration"; Result = $frontendResult4 },
    @{ Name = "Backend Offer Details Endpoint"; Result = $backendResult1 },
    @{ Name = "Frontend Type Check"; Result = $frontendTypeCheck },
    @{ Name = "Backend Type Check"; Result = $backendTypeCheck }
)

$totalTests = $testResults.Count
$passedTests = ($testResults | Where-Object { $_.Result -eq 0 }).Count
$failedTests = $totalTests - $passedTests

# Generate Report
Write-Host "`n📊 TEST RESULTS SUMMARY" -ForegroundColor Cyan
Write-Host ("=" * 50) -ForegroundColor Cyan

Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "✅ Passed: $passedTests" -ForegroundColor Green
Write-Host "❌ Failed: $failedTests" -ForegroundColor Red
$successRate = [math]::Round(($passedTests / $totalTests) * 100, 1)
Write-Host "📈 Success Rate: $successRate%" -ForegroundColor White

if ($failedTests -gt 0) {
    Write-Host "`n❌ FAILED TESTS:" -ForegroundColor Red
    Write-Host ("-" * 20) -ForegroundColor Red
    foreach ($test in $testResults | Where-Object { $_.Result -ne 0 }) {
        Write-Host "• $($test.Name)" -ForegroundColor Red
    }
}

Write-Host "`n✅ PASSED TESTS:" -ForegroundColor Green
Write-Host ("-" * 20) -ForegroundColor Green
foreach ($test in $testResults | Where-Object { $_.Result -eq 0 }) {
    Write-Host "• $($test.Name)" -ForegroundColor Green
}

Write-Host "`n🎯 DEEP LINKING FEATURES TESTED:" -ForegroundColor Cyan
Write-Host ("-" * 40) -ForegroundColor Cyan
$features = @(
    "Notification click handling",
    "Entity-based navigation (OFFER, CHAT_SESSION, TRANSACTION)",
    "Fallback type-based navigation",
    "Legacy notification support (data.offerId, data.chatSessionId)",
    "Error handling (navigation failures, missing entities)",
    "Offer details loading and display",
    "Interest management in offer details",
    "Owner vs visitor permissions",
    "Backend API endpoint security and data integrity",
    "Multiple notification types and statuses"
)

foreach ($feature in $features) {
    Write-Host "• ✅ $feature" -ForegroundColor Green
}

Write-Host "`n🚀 NEXT STEPS:" -ForegroundColor Cyan
Write-Host ("-" * 20) -ForegroundColor Cyan

if ($failedTests -eq 0) {
    Write-Host "• ✅ All tests passing! Ready for manual testing" -ForegroundColor Green
    Write-Host "• 🔄 Test the deep linking flow end-to-end:" -ForegroundColor Yellow
    Write-Host "  1. Start backend: cd backend; npm run dev" -ForegroundColor White
    Write-Host "  2. Start frontend: cd frontend; npm run dev" -ForegroundColor White
    Write-Host "  3. Create notifications and test clicking them" -ForegroundColor White
    Write-Host "  4. Verify navigation to correct pages" -ForegroundColor White
    Write-Host "  5. Test error scenarios (deleted offers, etc.)" -ForegroundColor White
} else {
    Write-Host "• ❌ Fix failing tests before proceeding" -ForegroundColor Red
    Write-Host "• 🔍 Review error outputs above" -ForegroundColor Yellow
    Write-Host "• 🛠️  Address any type errors or logic issues" -ForegroundColor Yellow
}

Write-Host "`n📋 MANUAL TESTING CHECKLIST:" -ForegroundColor Cyan
Write-Host ("-" * 35) -ForegroundColor Cyan
$checklist = @(
    "Click notification with OFFER entity → navigates to /offers/:id",
    "Click notification with CHAT_SESSION entity → navigates to /chat/:id",
    "Click notification with TRANSACTION entity → navigates to /transactions/:id",
    "Click legacy notification with data.offerId → navigates correctly",
    "Click legacy notification with data.chatSessionId → navigates correctly",
    "Click notification without entity data → uses type fallback",
    "Offer details page loads correctly for visitors",
    "Offer details page loads correctly for owners",
    "Express interest button works for visitors",
    "Edit/status buttons work for owners",
    "Error handling for deleted/missing offers",
    "Notifications are marked as read after clicking",
    "Mobile responsive design works correctly"
)

foreach ($item in $checklist) {
    Write-Host "• [ ] $item" -ForegroundColor White
}

Write-Host "`n🎉 Test run complete!" -ForegroundColor Cyan

if ($failedTests -eq 0) {
    Write-Host "🚀 Ready to start development servers and test manually!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "⚠️  Please fix failing tests before manual testing." -ForegroundColor Yellow
    exit 1
}
