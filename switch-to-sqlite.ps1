# Switch back to SQLite Development Environment
# This script stops PostgreSQL container and switches back to SQLite

Write-Host "=== Switching to SQLite Development Environment ===" -ForegroundColor Green

# Stop PostgreSQL container
Write-Host "Stopping PostgreSQL container..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml down

# Copy the original SQLite environment file back
Write-Host "Switching to SQLite environment..." -ForegroundColor Yellow
Copy-Item ".\backend\.env.sqlite" ".\backend\.env" -Force

Write-Host "=== SQLite Development Environment Ready! ===" -ForegroundColor Green
Write-Host "Database: ./backend/prisma/dev.db" -ForegroundColor Cyan
Write-Host "You can now run: npm run dev" -ForegroundColor Cyan
