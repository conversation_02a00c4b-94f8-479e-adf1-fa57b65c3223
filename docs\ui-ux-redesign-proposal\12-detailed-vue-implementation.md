# Detailed Vue Implementation for Innovative Transaction-Chat UI

## 🔧 Complete Component Architecture

Based on the detailed analysis of the current `ChatView.vue` and `TransactionFlowCardV3.vue`, here's the complete implementation for the new unified approach.

## 1. Main Unified Chat Component

```vue
<!-- UnifiedTransactionChat.vue -->
<template>
  <div class="unified-transaction-chat">
    <!-- Single scrolling container -->
    <div 
      ref="messageStreamRef" 
      class="message-stream"
      @scroll="handleScroll"
    >
      <!-- Combined message stream -->
      <div
        v-for="item in unifiedMessageStream"
        :key="item.id"
        class="stream-item"
      >
        <!-- Regular chat message -->
        <ChatMessage 
          v-if="item.type === 'chat'"
          :message="item"
          :is-own="item.senderId === authStore.user?.id"
          :other-user="otherParticipant"
        />
        
        <!-- Transaction system message -->
        <TransactionSystemMessage 
          v-else-if="item.type === 'transaction'"
          :message="item"
          :transaction="currentTransaction"
          :current-user-id="authStore.user?.id"
          @action="handleTransactionAction"
        />
        
        <!-- Status update message -->
        <StatusUpdateMessage 
          v-else-if="item.type === 'status'"
          :message="item"
          :transaction="currentTransaction"
        />
        
        <!-- Timer warning message -->
        <TimerWarningMessage 
          v-else-if="item.type === 'timer'"
          :message="item"
          :time-remaining="item.timeRemaining"
          :urgency="item.urgency"
        />
      </div>
      
      <!-- Loading indicator -->
      <div v-if="isLoadingMessages" class="loading-indicator">
        <n-spin size="small" />
      </div>
      
      <!-- Empty state -->
      <div v-if="unifiedMessageStream.length === 0" class="empty-state">
        <n-empty description="Start your conversation">
          <template #icon>
            <n-icon :component="ChatIcon" />
          </template>
        </n-empty>
      </div>
    </div>
    
    <!-- Context-aware action bar -->
    <SmartActionBar 
      v-if="showActionBar"
      :transaction="currentTransaction"
      :current-state="transactionState"
      :other-user="otherParticipant"
      :time-remaining="actionTimeRemaining"
      @primary-action="handlePrimaryAction"
      @secondary-action="handleSecondaryAction"
      @need-help="handleNeedHelp"
    />
    
    <!-- Fixed message input -->
    <div class="message-input-container">
      <n-input
        v-model:value="messageText"
        type="textarea"
        :placeholder="inputPlaceholder"
        :autosize="{ minRows: 1, maxRows: 4 }"
        @keydown.enter.exact.prevent="sendMessage"
        :disabled="isInputDisabled"
        class="message-input"
      >
        <template #suffix>
          <div class="input-actions">
            <n-button 
              v-if="messageText.trim()"
              type="primary" 
              @click="sendMessage"
              :loading="isSendingMessage"
              size="small"
            >
              Send
            </n-button>
            <n-button 
              v-else
              text
              @click="showQuickActions"
              size="small"
            >
              <n-icon :component="QuickActionsIcon" />
            </n-button>
          </div>
        </template>
      </n-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { 
  useMessage,
  useNotification,
  type MessageApi,
  type NotificationApi
} from 'naive-ui'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chatStore'
import { useTransactionStore } from '@/stores/transactionStore'
import { useTranslation } from '@/composables/useTranslation'

// Components
import ChatMessage from './ChatMessage.vue'
import TransactionSystemMessage from './TransactionSystemMessage.vue'
import StatusUpdateMessage from './StatusUpdateMessage.vue'
import TimerWarningMessage from './TimerWarningMessage.vue'
import SmartActionBar from './SmartActionBar.vue'

// Icons
import { 
  ChatBubbleOutline as ChatIcon,
  MoreHoriz as QuickActionsIcon
} from '@vicons/material'

const { t } = useTranslation()
const route = useRoute()
const messageApi = useMessage()
const notificationApi = useNotification()

// Stores
const authStore = useAuthStore()
const chatStore = useChatStore()
const transactionStore = useTransactionStore()

// Refs
const messageStreamRef = ref<HTMLDivElement>()
const messageText = ref('')
const isLoadingMessages = ref(false)
const isSendingMessage = ref(false)

// Computed
const currentTransaction = computed(() => transactionStore.currentTransaction)
const otherParticipant = computed(() => chatStore.otherParticipant)
const transactionState = computed(() => currentTransaction.value?.status)

const unifiedMessageStream = computed(() => {
  const chatMessages = chatStore.messages.map(msg => ({
    ...msg,
    type: 'chat',
    timestamp: new Date(msg.createdAt)
  }))
  
  const transactionMessages = transactionStore.systemMessages.map(msg => ({
    ...msg,
    type: 'transaction',
    timestamp: new Date(msg.createdAt)
  }))
  
  const statusMessages = transactionStore.statusUpdates.map(msg => ({
    ...msg,
    type: 'status',
    timestamp: new Date(msg.createdAt)
  }))
  
  const timerMessages = transactionStore.timerWarnings.map(msg => ({
    ...msg,
    type: 'timer',
    timestamp: new Date(msg.createdAt)
  }))
  
  // Combine and sort by timestamp
  return [
    ...chatMessages,
    ...transactionMessages,
    ...statusMessages,
    ...timerMessages
  ].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
})

const showActionBar = computed(() => {
  return currentTransaction.value && 
         transactionState.value !== 'COMPLETED' && 
         transactionState.value !== 'CANCELLED'
})

const actionTimeRemaining = computed(() => {
  if (!currentTransaction.value?.timeRemaining) return null
  return transactionStore.getFormattedTimeRemaining(currentTransaction.value.id)
})

const inputPlaceholder = computed(() => {
  if (isInputDisabled.value) {
    return t('chat.inputDisabled')
  }
  return t('chat.typeMessage')
})

const isInputDisabled = computed(() => {
  return currentTransaction.value?.status === 'COMPLETED' ||
         currentTransaction.value?.status === 'CANCELLED'
})

// Methods
const sendMessage = async () => {
  if (!messageText.value.trim() || isSendingMessage.value) return
  
  try {
    isSendingMessage.value = true
    await chatStore.sendMessage(messageText.value.trim())
    messageText.value = ''
    scrollToBottom()
  } catch (error) {
    messageApi.error(t('chat.sendError'))
    console.error('Send message error:', error)
  } finally {
    isSendingMessage.value = false
  }
}

const handleTransactionAction = async (action: string, payload?: any) => {
  try {
    switch (action) {
      case 'declarePayment':
        await transactionStore.declarePayment(payload)
        break
      case 'confirmReceipt':
        await transactionStore.confirmReceipt(payload)
        break
      case 'reportIssue':
        await transactionStore.reportIssue(payload)
        break
      case 'agreeToRate':
        await transactionStore.agreeToRate(payload)
        break
      default:
        console.warn('Unknown transaction action:', action)
    }
  } catch (error) {
    messageApi.error(t('transaction.actionError'))
    console.error('Transaction action error:', error)
  }
}

const handlePrimaryAction = async (action: string, payload?: any) => {
  await handleTransactionAction(action, payload)
}

const handleSecondaryAction = async (action: string, payload?: any) => {
  await handleTransactionAction(action, payload)
}

const handleNeedHelp = () => {
  // Open help modal or redirect to support
  notificationApi.info({
    title: t('help.title'),
    content: t('help.contactSupport'),
    duration: 5000
  })
}

const handleScroll = () => {
  // Handle scroll events for pagination, etc.
}

const scrollToBottom = async () => {
  await nextTick()
  if (messageStreamRef.value) {
    messageStreamRef.value.scrollTop = messageStreamRef.value.scrollHeight
  }
}

const showQuickActions = () => {
  // Show quick action menu
}

// Lifecycle
onMounted(async () => {
  const sessionId = route.params.sessionId as string
  if (sessionId) {
    await chatStore.initializeSession(sessionId)
    await transactionStore.loadTransaction(sessionId)
    scrollToBottom()
  }
})

onUnmounted(() => {
  chatStore.cleanup()
  transactionStore.cleanup()
})

// Watch for new messages
watch(
  () => unifiedMessageStream.value.length,
  () => {
    nextTick(() => scrollToBottom())
  }
)
</script>

<style scoped>
.unified-transaction-chat {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--n-body-color);
}

.message-stream {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1rem;
  padding-bottom: 2rem;
  scroll-behavior: smooth;
}

.stream-item {
  margin-bottom: 1rem;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  padding: 1rem;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.message-input-container {
  padding: 1rem;
  border-top: 1px solid var(--n-border-color);
  background: var(--n-body-color);
}

.message-input {
  width: 100%;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .message-stream {
    padding: 0.75rem;
  }
  
  .message-input-container {
    padding: 0.75rem;
  }
  
  .stream-item {
    margin-bottom: 0.75rem;
  }
}

/* Touch-friendly scrolling */
@media (pointer: coarse) {
  .message-stream {
    scroll-behavior: auto;
    -webkit-overflow-scrolling: touch;
  }
}
</style>
```

## 2. Smart Transaction System Message Component

```vue
<!-- TransactionSystemMessage.vue -->
<template>
  <div 
    class="transaction-system-message" 
    :class="[messageTypeClass, urgencyClass]"
  >
    <div class="message-header">
      <div class="header-left">
        <n-icon :component="messageIcon" class="status-icon" />
        <span class="message-title">{{ messageTitle }}</span>
      </div>
      <div class="header-right">
        <n-tag 
          :type="statusTagType" 
          size="small"
          class="status-tag"
        >
          {{ statusTagText }}
        </n-tag>
        <span class="timestamp">{{ formattedTimestamp }}</span>
      </div>
    </div>
    
    <div class="message-content">
      <!-- Payment Declaration -->
      <template v-if="message.subType === 'payment-declared'">
        <div class="payment-summary">
          <div class="amount-section">
            <span class="amount">{{ formatAmount(message.amount) }}</span>
            <span class="currency">{{ message.currency }}</span>
          </div>
          <div class="payer-section">
            <n-avatar 
              :src="payerUser?.profile?.avatarUrl" 
              size="small" 
              class="payer-avatar"
            />
            <span class="payer-text">from {{ payerUser?.username }}</span>
          </div>
          <div v-if="message.reference" class="reference-section">
            <span class="ref-label">Reference:</span>
            <span class="ref-number">{{ message.reference }}</span>
            <n-button 
              size="tiny" 
              text 
              @click="copyReference"
              class="copy-btn"
            >
              Copy
            </n-button>
          </div>
        </div>
        
        <div v-if="showConfirmationActions" class="inline-actions">
          <n-button 
            type="success" 
            size="medium"
            @click="confirmPayment"
            :loading="isConfirming"
            class="primary-action"
          >
            {{ t('transaction.confirmReceipt') }}
          </n-button>
          <n-button 
            type="default" 
            size="small"
            @click="reportIssue"
            class="secondary-action"
          >
            {{ t('transaction.reportIssue') }}
          </n-button>
        </div>
      </template>
      
      <!-- Rate Agreement -->
      <template v-if="message.subType === 'rate-agreement'">
        <div class="rate-summary">
          <div class="exchange-rate">
            <span class="rate-value">{{ message.rate }}</span>
            <span class="rate-unit">{{ message.fromCurrency }}/{{ message.toCurrency }}</span>
          </div>
          <div class="amount-breakdown">
            <div class="from-amount">
              {{ formatAmount(message.fromAmount) }} {{ message.fromCurrency }}
            </div>
            <n-icon :component="ArrowRightIcon" class="exchange-arrow" />
            <div class="to-amount">
              {{ formatAmount(message.toAmount) }} {{ message.toCurrency }}
            </div>
          </div>
        </div>
        
        <div v-if="showRateActions" class="inline-actions">
          <n-button 
            type="primary" 
            size="medium"
            @click="agreeToRate"
            :loading="isAgreeing"
            class="primary-action"
          >
            {{ t('transaction.agreeToRate') }}
          </n-button>
          <n-button 
            type="default" 
            size="small"
            @click="negotiateRate"
            class="secondary-action"
          >
            {{ t('transaction.negotiate') }}
          </n-button>
        </div>
      </template>
      
      <!-- Transaction Status Update -->
      <template v-if="message.subType === 'status-update'">
        <div class="status-update">
          <div class="status-description">
            {{ message.description }}
          </div>
          <div v-if="message.nextAction" class="next-action">
            Next: {{ message.nextAction }}
          </div>
        </div>
      </template>
      
      <!-- Payer Negotiation -->
      <template v-if="message.subType === 'payer-negotiation'">
        <div class="negotiation-summary">
          <div class="proposal-info">
            <span class="proposal-text">{{ message.proposalText }}</span>
          </div>
          <div v-if="message.reasoning" class="reasoning">
            <span class="reasoning-label">Reason:</span>
            <span class="reasoning-text">{{ message.reasoning }}</span>
          </div>
        </div>
        
        <div v-if="showNegotiationActions" class="inline-actions">
          <n-button 
            type="primary" 
            size="medium"
            @click="acceptProposal"
            :loading="isAccepting"
            class="primary-action"
          >
            {{ t('transaction.acceptProposal') }}
          </n-button>
          <n-button 
            type="default" 
            size="small"
            @click="counterPropose"
            class="secondary-action"
          >
            {{ t('transaction.counterPropose') }}
          </n-button>
        </div>
      </template>
      
      <!-- Timer Warning -->
      <template v-if="message.subType === 'timer-warning'">
        <div class="timer-warning">
          <div class="timer-content">
            <n-icon :component="TimerIcon" class="timer-icon" />
            <div class="timer-info">
              <span class="timer-title">{{ message.warningText }}</span>
              <span 
                class="timer-countdown" 
                :class="timerUrgencyClass"
              >
                {{ message.timeRemaining }}
              </span>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useMessage } from 'naive-ui'
import { useTranslation } from '@/composables/useTranslation'
import { useAuthStore } from '@/stores/auth'
import { formatAmount } from '@/utils/currencyUtils'
import { 
  PaymentIcon,
  TrendingUpIcon,
  InfoIcon,
  HandshakeIcon,
  TimerIcon,
  ArrowRightIcon
} from '@vicons/material'

interface Props {
  message: TransactionSystemMessage
  transaction: Transaction
  currentUserId: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  action: [action: string, payload?: any]
}>()

const { t } = useTranslation()
const messageApi = useMessage()
const authStore = useAuthStore()

// Reactive state
const isConfirming = ref(false)
const isAgreeing = ref(false)
const isAccepting = ref(false)

// Computed properties
const messageTypeClass = computed(() => {
  return `type-${props.message.subType.replace('_', '-')}`
})

const urgencyClass = computed(() => {
  if (props.message.urgency === 'high') return 'urgent'
  if (props.message.urgency === 'medium') return 'warning'
  return 'info'
})

const messageIcon = computed(() => {
  switch (props.message.subType) {
    case 'payment-declared': return PaymentIcon
    case 'rate-agreement': return TrendingUpIcon
    case 'payer-negotiation': return HandshakeIcon
    case 'timer-warning': return TimerIcon
    default: return InfoIcon
  }
})

const messageTitle = computed(() => {
  switch (props.message.subType) {
    case 'payment-declared': return t('transaction.paymentDeclared')
    case 'rate-agreement': return t('transaction.rateProposed')
    case 'payer-negotiation': return t('transaction.payerProposal')
    case 'timer-warning': return t('transaction.timeWarning')
    default: return t('transaction.update')
  }
})

const statusTagType = computed(() => {
  switch (props.message.status) {
    case 'pending': return 'warning'
    case 'completed': return 'success'
    case 'failed': return 'error'
    default: return 'info'
  }
})

const statusTagText = computed(() => {
  return t(`transaction.status.${props.message.status}`)
})

const formattedTimestamp = computed(() => {
  return new Date(props.message.createdAt).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })
})

const payerUser = computed(() => {
  return props.transaction.participants.find(
    p => p.id === props.message.actorId
  )
})

const showConfirmationActions = computed(() => {
  return props.message.subType === 'payment-declared' &&
         props.message.status === 'pending' &&
         props.message.actorId !== props.currentUserId
})

const showRateActions = computed(() => {
  return props.message.subType === 'rate-agreement' &&
         props.message.status === 'pending' &&
         !props.message.respondedBy?.includes(props.currentUserId)
})

const showNegotiationActions = computed(() => {
  return props.message.subType === 'payer-negotiation' &&
         props.message.status === 'pending' &&
         props.message.targetUserId === props.currentUserId
})

const timerUrgencyClass = computed(() => {
  if (!props.message.timeRemaining) return ''
  const minutes = parseInt(props.message.timeRemaining.split(':')[1])
  if (minutes < 10) return 'critical'
  if (minutes < 30) return 'warning'
  return 'normal'
})

// Methods
const confirmPayment = async () => {
  isConfirming.value = true
  try {
    emit('action', 'confirmReceipt', {
      messageId: props.message.id,
      transactionId: props.transaction.id
    })
  } finally {
    isConfirming.value = false
  }
}

const reportIssue = () => {
  emit('action', 'reportIssue', {
    messageId: props.message.id,
    transactionId: props.transaction.id
  })
}

const agreeToRate = async () => {
  isAgreeing.value = true
  try {
    emit('action', 'agreeToRate', {
      messageId: props.message.id,
      transactionId: props.transaction.id
    })
  } finally {
    isAgreeing.value = false
  }
}

const negotiateRate = () => {
  emit('action', 'negotiateRate', {
    messageId: props.message.id,
    transactionId: props.transaction.id
  })
}

const acceptProposal = async () => {
  isAccepting.value = true
  try {
    emit('action', 'acceptProposal', {
      messageId: props.message.id,
      transactionId: props.transaction.id
    })
  } finally {
    isAccepting.value = false
  }
}

const counterPropose = () => {
  emit('action', 'counterPropose', {
    messageId: props.message.id,
    transactionId: props.transaction.id
  })
}

const copyReference = async () => {
  if (props.message.reference) {
    await navigator.clipboard.writeText(props.message.reference)
    messageApi.success(t('common.copied'))
  }
}
</script>

<style scoped>
.transaction-system-message {
  background: var(--n-card-color);
  border: 1px solid var(--n-border-color);
  border-radius: 12px;
  padding: 1rem;
  margin: 0.5rem 0;
  position: relative;
  overflow: hidden;
}

/* Message type styling */
.type-payment-declared {
  border-left: 4px solid var(--n-warning-color);
  background: var(--n-warning-color-suppl);
}

.type-rate-agreement {
  border-left: 4px solid var(--n-info-color);
  background: var(--n-info-color-suppl);
}

.type-payer-negotiation {
  border-left: 4px solid var(--n-primary-color);
  background: var(--n-primary-color-suppl);
}

.type-timer-warning {
  border-left: 4px solid var(--n-error-color);
  background: var(--n-error-color-suppl);
}

/* Urgency styling */
.urgent {
  animation: pulse 2s infinite;
}

.warning {
  box-shadow: 0 0 0 1px var(--n-warning-color);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-icon {
  font-size: 1.25rem;
  color: var(--n-primary-color);
}

.message-title {
  font-weight: 600;
  font-size: 0.95rem;
  color: var(--n-text-color);
}

.timestamp {
  font-size: 0.8rem;
  color: var(--n-text-color-3);
}

.message-content {
  color: var(--n-text-color-2);
}

/* Payment summary styling */
.payment-summary {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.amount-section {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.amount {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--n-text-color);
}

.currency {
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--n-text-color-2);
}

.payer-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.payer-avatar {
  flex-shrink: 0;
}

.payer-text {
  font-size: 0.9rem;
  color: var(--n-text-color-2);
}

.reference-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--n-body-color);
  border-radius: 6px;
}

.ref-label {
  font-size: 0.85rem;
  color: var(--n-text-color-3);
}

.ref-number {
  font-family: monospace;
  font-size: 0.85rem;
  color: var(--n-text-color);
}

.copy-btn {
  margin-left: auto;
}

/* Rate summary styling */
.rate-summary {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.exchange-rate {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  justify-content: center;
}

.rate-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--n-primary-color);
}

.rate-unit {
  font-size: 0.9rem;
  color: var(--n-text-color-2);
}

.amount-breakdown {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.from-amount,
.to-amount {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--n-text-color);
}

.exchange-arrow {
  color: var(--n-text-color-3);
}

/* Inline actions */
.inline-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.primary-action {
  min-width: 120px;
}

.secondary-action {
  min-width: 100px;
}

/* Timer warning styling */
.timer-warning {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background: var(--n-error-color-suppl);
  border-radius: 8px;
}

.timer-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.timer-icon {
  font-size: 1.5rem;
  color: var(--n-error-color);
}

.timer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.timer-title {
  font-weight: 600;
  color: var(--n-text-color);
}

.timer-countdown {
  font-family: monospace;
  font-size: 1.1rem;
  font-weight: 700;
}

.timer-countdown.critical {
  color: var(--n-error-color);
  animation: pulse 1s infinite;
}

.timer-countdown.warning {
  color: var(--n-warning-color);
}

.timer-countdown.normal {
  color: var(--n-success-color);
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .transaction-system-message {
    padding: 0.75rem;
    margin: 0.4rem 0;
  }
  
  .message-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .header-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .inline-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .primary-action,
  .secondary-action {
    width: 100%;
  }
  
  .amount-breakdown {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .exchange-arrow {
    transform: rotate(90deg);
  }
}

/* Touch-friendly interactions */
@media (pointer: coarse) {
  .inline-actions button {
    min-height: 44px;
  }
  
  .copy-btn {
    min-height: 32px;
    padding: 0.25rem 0.5rem;
  }
}
</style>
```

## 3. Smart Context-Aware Action Bar

```vue
<!-- SmartActionBar.vue -->
<template>
  <div 
    class="smart-action-bar" 
    :class="[currentStateClass, urgencyClass]"
    v-if="shouldShow"
  >
    <!-- Payment Due State -->
    <template v-if="currentState === 'payment-due'">
      <div class="context-section">
        <div class="urgency-indicator">
          <n-icon :component="UrgentIcon" class="urgency-icon" />
          <span class="urgency-text">{{ t('transaction.paymentDue') }}</span>
        </div>
        <div class="time-info">
          <n-countdown 
            :value="deadlineTimestamp" 
            format="HH:mm:ss"
            @finish="handleTimeout"
            class="countdown-timer"
          />
        </div>
        <div class="amount-info">
          <span class="send-label">Send</span>
          <span class="amount">{{ formatAmount(transaction.amount) }}</span>
          <span class="currency">{{ transaction.currency }}</span>
        </div>
      </div>
      
      <div class="action-section">
        <n-button 
          type="error" 
          size="large"
          @click="handleDeclarePayment"
          :loading="isLoading"
          class="primary-action-button"
        >
          <template #icon>
            <n-icon :component="PaymentIcon" />
          </template>
          {{ t('transaction.declarePayment') }}
        </n-button>
        <n-button 
          type="default" 
          size="medium"
          @click="handleNeedHelp"
          class="secondary-action-button"
        >
          <template #icon>
            <n-icon :component="HelpIcon" />
          </template>
          {{ t('common.needHelp') }}
        </n-button>
      </div>
    </template>
    
    <!-- Awaiting Confirmation State -->
    <template v-if="currentState === 'awaiting-confirmation'">
      <div class="context-section">
        <div class="status-indicator">
          <n-avatar 
            :src="otherUser?.profile?.avatarUrl" 
            size="small" 
            class="user-avatar"
          />
          <div class="status-text">
            <span class="main-text">{{ otherUser?.username }} declared payment</span>
            <span class="sub-text">{{ formatAmount(transaction.amount) }} {{ transaction.currency }}</span>
          </div>
        </div>
        <div v-if="transaction.reference" class="reference-info">
          <span class="ref-label">Ref:</span>
          <span class="ref-value">{{ transaction.reference }}</span>
        </div>
      </div>
      
      <div class="action-section">
        <n-button 
          type="success" 
          size="large"
          @click="handleConfirmReceipt"
          :loading="isLoading"
          class="primary-action-button"
        >
          <template #icon>
            <n-icon :component="CheckIcon" />
          </template>
          {{ t('transaction.confirmReceipt') }}
        </n-button>
        <n-button 
          type="warning" 
          size="medium"
          @click="handleReportIssue"
          class="secondary-action-button"
        >
          <template #icon>
            <n-icon :component="WarningIcon" />
          </template>
          {{ t('transaction.notReceived') }}
        </n-button>
      </div>
    </template>
    
    <!-- Waiting State -->
    <template v-if="currentState === 'waiting'">
      <div class="context-section">
        <div class="waiting-indicator">
          <n-spin size="small" class="waiting-spinner" />
          <div class="waiting-text">
            <span class="main-text">
              {{ t('transaction.waitingFor', { name: otherUser?.username }) }}
            </span>
            <span class="sub-text">
              {{ getWaitingDescription() }}
            </span>
          </div>
        </div>
        <div class="elapsed-time">
          <n-icon :component="TimeIcon" class="time-icon" />
          <span class="elapsed-text">
            {{ formatElapsedTime(transaction.lastActivityAt) }}
          </span>
        </div>
      </div>
      
      <div class="action-section">
        <n-button 
          type="default" 
          size="medium"
          @click="handleSendReminder"
          :loading="isLoading"
          class="secondary-action-button"
        >
          <template #icon>
            <n-icon :component="NotificationIcon" />
          </template>
          {{ t('transaction.sendReminder') }}
        </n-button>
      </div>
    </template>
    
    <!-- Negotiation State -->
    <template v-if="currentState === 'negotiation'">
      <div class="context-section">
        <div class="negotiation-info">
          <div class="proposal-text">
            {{ getCurrentProposalText() }}
          </div>
          <div v-if="negotiationReason" class="reason-text">
            {{ negotiationReason }}
          </div>
        </div>
      </div>
      
      <div class="action-section">
        <n-button 
          type="primary" 
          size="large"
          @click="handleAcceptProposal"
          :loading="isLoading"
          class="primary-action-button"
        >
          <template #icon>
            <n-icon :component="AgreeIcon" />
          </template>
          {{ t('transaction.acceptProposal') }}
        </n-button>
        <n-button 
          type="default" 
          size="medium"
          @click="handleCounterPropose"
          class="secondary-action-button"
        >
          <template #icon>
            <n-icon :component="NegotiateIcon" />
          </template>
          {{ t('transaction.counterPropose') }}
        </n-button>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useMessage } from 'naive-ui'
import { useTranslation } from '@/composables/useTranslation'
import { formatAmount, formatElapsedTime } from '@/utils/formatters'
import { 
  PaymentIcon,
  CheckCircleOutline as CheckIcon,
  WarningAmber as WarningIcon,
  HelpOutline as HelpIcon,
  PriorityHigh as UrgentIcon,
  AccessTime as TimeIcon,
  NotificationsNone as NotificationIcon,
  ThumbUp as AgreeIcon,
  SwapHoriz as NegotiateIcon
} from '@vicons/material'

interface Props {
  transaction: Transaction
  currentState: TransactionState
  otherUser: User
  timeRemaining?: string | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  primaryAction: [action: string, payload?: any]
  secondaryAction: [action: string, payload?: any]
  needHelp: []
}>()

const { t } = useTranslation()
const messageApi = useMessage()

const isLoading = ref(false)

// Computed properties
const shouldShow = computed(() => {
  return props.transaction && 
         props.currentState !== 'completed' && 
         props.currentState !== 'cancelled'
})

const currentStateClass = computed(() => {
  return `state-${props.currentState.replace('_', '-')}`
})

const urgencyClass = computed(() => {
  if (props.currentState === 'payment-due' && props.timeRemaining) {
    const minutes = getMinutesFromTimeString(props.timeRemaining)
    if (minutes < 30) return 'critical'
    if (minutes < 60) return 'warning'
  }
  return 'normal'
})

const deadlineTimestamp = computed(() => {
  if (props.timeRemaining && props.transaction.deadline) {
    return new Date(props.transaction.deadline).getTime()
  }
  return Date.now() + 60 * 60 * 1000 // 1 hour default
})

const negotiationReason = computed(() => {
  return props.transaction.negotiation?.currentProposal?.reasoning
})

// Methods
const handleDeclarePayment = async () => {
  isLoading.value = true
  try {
    emit('primaryAction', 'declarePayment', {
      transactionId: props.transaction.id
    })
  } finally {
    isLoading.value = false
  }
}

const handleConfirmReceipt = async () => {
  isLoading.value = true
  try {
    emit('primaryAction', 'confirmReceipt', {
      transactionId: props.transaction.id
    })
  } finally {
    isLoading.value = false
  }
}

const handleReportIssue = () => {
  emit('secondaryAction', 'reportIssue', {
    transactionId: props.transaction.id
  })
}

const handleNeedHelp = () => {
  emit('needHelp')
}

const handleSendReminder = async () => {
  isLoading.value = true
  try {
    emit('secondaryAction', 'sendReminder', {
      transactionId: props.transaction.id,
      targetUserId: props.otherUser.id
    })
    messageApi.success(t('transaction.reminderSent'))
  } finally {
    isLoading.value = false
  }
}

const handleAcceptProposal = async () => {
  isLoading.value = true
  try {
    emit('primaryAction', 'acceptProposal', {
      transactionId: props.transaction.id,
      proposalId: props.transaction.negotiation?.currentProposal?.id
    })
  } finally {
    isLoading.value = false
  }
}

const handleCounterPropose = () => {
  emit('secondaryAction', 'counterPropose', {
    transactionId: props.transaction.id
  })
}

const handleTimeout = () => {
  messageApi.warning(t('transaction.timeExpired'))
  // Handle timeout logic
}

const getWaitingDescription = () => {
  switch (props.currentState) {
    case 'waiting-payment':
      return t('transaction.waitingPayment')
    case 'waiting-confirmation':
      return t('transaction.waitingConfirmation')
    default:
      return t('transaction.waitingAction')
  }
}

const getCurrentProposalText = () => {
  const proposal = props.transaction.negotiation?.currentProposal
  if (!proposal) return ''
  
  if (proposal.proposedPayerId === props.transaction.currentUserId) {
    return t('transaction.youPayFirst')
  } else {
    return t('transaction.otherPaysFirst', { name: props.otherUser.username })
  }
}

const getMinutesFromTimeString = (timeString: string): number => {
  const parts = timeString.split(':')
  return parseInt(parts[0]) * 60 + parseInt(parts[1])
}
</script>

<style scoped>
.smart-action-bar {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--n-card-color);
  border-top: 1px solid var(--n-border-color);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  z-index: 100;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* State-specific styling */
.state-payment-due {
  background: linear-gradient(135deg, 
    var(--n-error-color-suppl) 0%, 
    var(--n-card-color) 100%);
  border-top-color: var(--n-error-color);
}

.state-awaiting-confirmation {
  background: linear-gradient(135deg, 
    var(--n-success-color-suppl) 0%, 
    var(--n-card-color) 100%);
  border-top-color: var(--n-success-color);
}

.state-waiting {
  background: linear-gradient(135deg, 
    var(--n-info-color-suppl) 0%, 
    var(--n-card-color) 100%);
  border-top-color: var(--n-info-color);
}

.state-negotiation {
  background: linear-gradient(135deg, 
    var(--n-primary-color-suppl) 0%, 
    var(--n-card-color) 100%);
  border-top-color: var(--n-primary-color);
}

/* Urgency styling */
.critical {
  animation: urgentPulse 2s infinite;
  box-shadow: 0 0 20px rgba(var(--n-error-color-rgb), 0.3);
}

.warning {
  box-shadow: 0 0 10px rgba(var(--n-warning-color-rgb), 0.2);
}

.context-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-section {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* Context section styling */
.urgency-indicator,
.status-indicator,
.waiting-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.urgency-icon {
  font-size: 1.5rem;
  color: var(--n-error-color);
  animation: pulse 1s infinite;
}

.urgency-text,
.main-text {
  font-weight: 600;
  color: var(--n-text-color);
  font-size: 0.95rem;
}

.sub-text {
  font-size: 0.85rem;
  color: var(--n-text-color-2);
}

.time-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.countdown-timer {
  font-family: monospace;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--n-error-color);
}

.amount-info {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}

.send-label {
  font-size: 0.9rem;
  color: var(--n-text-color-2);
}

.amount {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--n-text-color);
}

.currency {
  font-size: 1rem;
  font-weight: 500;
  color: var(--n-text-color-2);
}

.user-avatar {
  flex-shrink: 0;
}

.status-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.reference-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--n-body-color);
  border-radius: 6px;
}

.ref-label {
  font-size: 0.85rem;
  color: var(--n-text-color-3);
}

.ref-value {
  font-family: monospace;
  font-size: 0.85rem;
  color: var(--n-text-color);
}

.waiting-spinner {
  flex-shrink: 0;
}

.waiting-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.elapsed-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-icon {
  font-size: 1rem;
  color: var(--n-text-color-3);
}

.elapsed-text {
  font-size: 0.85rem;
  color: var(--n-text-color-2);
}

.negotiation-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.proposal-text {
  font-weight: 600;
  color: var(--n-text-color);
}

.reason-text {
  font-size: 0.9rem;
  color: var(--n-text-color-2);
  font-style: italic;
}

/* Action button styling */
.primary-action-button {
  min-width: 160px;
  min-height: 48px;
  font-weight: 600;
}

.secondary-action-button {
  min-width: 120px;
  min-height: 44px;
}

/* Animations */
@keyframes urgentPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(var(--n-error-color-rgb), 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(var(--n-error-color-rgb), 0.5);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .smart-action-bar {
    padding: 0.75rem;
    gap: 0.75rem;
  }
  
  .action-section {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .primary-action-button,
  .secondary-action-button {
    width: 100%;
  }
  
  .urgency-indicator,
  .status-indicator,
  .waiting-indicator {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .time-info,
  .amount-info {
    align-self: flex-end;
  }
}

/* Landscape mobile optimization */
@media (max-width: 768px) and (orientation: landscape) {
  .smart-action-bar {
    flex-direction: row;
    align-items: center;
  }
  
  .context-section {
    flex: 1;
    min-width: 0;
  }
  
  .action-section {
    flex-direction: row;
    flex-shrink: 0;
  }
}

/* Touch optimization */
@media (pointer: coarse) {
  .primary-action-button,
  .secondary-action-button {
    min-height: 48px;
    touch-action: manipulation;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .smart-action-bar {
    border-top-width: 2px;
  }
  
  .critical {
    border: 2px solid var(--n-error-color);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .critical,
  .urgency-icon {
    animation: none;
  }
}
</style>
```

## 4. Payment Readiness Gate System Message

```vue
<!-- PaymentReadinessGateMessage.vue -->
<template>
  <div class="payment-readiness-gate-message system-message">
    <div class="message-header">
      <div class="header-left">
        <n-icon :component="BankIcon" class="gate-icon" />
        <span class="message-title">{{ t('transaction.paymentDetailsRequired') }}</span>
      </div>
      <div class="header-right">
        <n-tag :type="gateStatusType" size="small">
          {{ gateStatusText }}
        </n-tag>
      </div>
    </div>
    
    <div class="gate-content">
      <!-- Explanation -->
      <div class="gate-explanation">
        <p class="explanation-text">
          {{ t('transaction.paymentGateExplanation') }}
        </p>
      </div>
      
      <!-- Dual User Status Display -->
      <div class="payment-status-grid">
        <!-- Current User Status -->
        <div class="status-item" :class="{ ready: currentUserReady }">
          <div class="user-section">
            <n-avatar :src="currentUser?.profile?.avatarUrl" size="small" />
            <div class="user-info">
              <span class="user-name">{{ t('common.you') }}</span>
              <div class="status-indicator" :class="currentUserStatusClass">
                <n-icon :component="currentUserStatusIcon" size="14" />
                <span class="status-text">{{ currentUserStatusText }}</span>
              </div>
            </div>
          </div>
          
          <div class="action-section">
            <n-button 
              v-if="!currentUserReady"
              type="primary" 
              size="small"
              @click="openPaymentForm"
              class="status-action-btn"
            >
              {{ t('transaction.addDetails') }}
            </n-button>
            <n-button 
              v-else
              type="default" 
              size="small"
              @click="editPaymentDetails"
              class="status-action-btn"
            >
              {{ t('common.edit') }}
            </n-button>
          </div>
        </div>
        
        <!-- Other User Status -->
        <div class="status-item" :class="{ ready: otherUserReady }">
          <div class="user-section">
            <n-avatar :src="otherUser?.profile?.avatarUrl" size="small" />
            <div class="user-info">
              <span class="user-name">{{ otherUser?.username }}</span>
              <div class="status-indicator" :class="otherUserStatusClass">
                <n-icon :component="otherUserStatusIcon" size="14" />
                <span class="status-text">{{ otherUserStatusText }}</span>
              </div>
            </div>
          </div>
          
          <div class="action-section">
            <n-button 
              v-if="currentUserReady && !otherUserReady"
              type="default" 
              size="small"
              @click="sendReminder"
              class="status-action-btn"
            >
              {{ t('transaction.remind') }}
            </n-button>
          </div>
        </div>
      </div>
      
      <!-- Progress Indicator -->
      <div class="gate-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${gateProgress}%` }"></div>
        </div>
        <span class="progress-text">{{ progressText }}</span>
      </div>
      
      <!-- Expandable Payment Form -->
      <div v-if="showPaymentForm" class="payment-form-container">
        <div class="form-backdrop" @click="closePaymentForm"></div>
        <div class="payment-form-card">
          <div class="form-header">
            <h4 class="form-title">{{ t('transaction.yourPaymentDetails') }}</h4>
            <p class="form-subtitle">{{ t('transaction.detailsOnlySharedIfNeeded') }}</p>
            <n-button 
              text 
              @click="closePaymentForm"
              class="close-btn"
            >
              <n-icon :component="CloseIcon" />
            </n-button>
          </div>
          
          <!-- Profile Details Option (if available) -->
          <div v-if="hasProfileDetails && !showNewForm" class="profile-option">
            <div class="saved-details-preview">
              <div class="preview-header">
                <n-icon :component="SavedIcon" />
                <span class="preview-title">{{ t('transaction.savedDetails') }}</span>
              </div>
              <div class="details-preview">
                <div class="detail-row">
                  <span class="detail-label">{{ t('transaction.bank') }}:</span>
                  <span class="detail-value">{{ maskedBankName }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">{{ t('transaction.account') }}:</span>
                  <span class="detail-value">{{ maskedAccount }}</span>
                </div>
              </div>
            </div>
            
            <div class="profile-actions">
              <n-button 
                type="primary" 
                @click="useProfileDetails"
                :loading="isUsingProfile"
                size="large"
                block
              >
                {{ t('transaction.useSavedDetails') }}
              </n-button>
              <n-button 
                type="default"
                @click="showNewDetailsForm"
                size="medium"
                block
              >
                {{ t('transaction.useDifferentDetails') }}
              </n-button>
            </div>
          </div>
          
          <!-- New Details Form -->
          <div v-if="!hasProfileDetails || showNewForm" class="new-details-form">
            <n-form
              ref="paymentFormRef"
              :model="paymentForm"
              :rules="paymentFormRules"
              label-placement="top"
            >
              <n-form-item :label="t('transaction.bankName')" path="bankName">
                <n-input 
                  v-model:value="paymentForm.bankName"
                  :placeholder="t('transaction.bankNamePlaceholder')"
                  size="large"
                  class="form-input"
                />
              </n-form-item>
              
              <n-form-item :label="t('transaction.accountNumber')" path="accountNumber">
                <n-input 
                  v-model:value="paymentForm.accountNumber"
                  :placeholder="t('transaction.accountNumberPlaceholder')"
                  size="large"
                  type="password"
                  show-password-on="click"
                  class="form-input"
                />
              </n-form-item>
              
              <n-form-item :label="t('transaction.accountHolder')" path="accountHolderName">
                <n-input 
                  v-model:value="paymentForm.accountHolderName"
                  :placeholder="t('transaction.accountHolderPlaceholder')"
                  size="large"
                  class="form-input"
                />
              </n-form-item>
              
              <n-form-item>
                <n-checkbox 
                  v-model:checked="paymentForm.saveToProfile"
                  class="save-profile-checkbox"
                >
                  <div class="checkbox-content">
                    <n-icon :component="SaveIcon" />
                    <span>{{ t('transaction.saveToProfile') }}</span>
                  </div>
                </n-checkbox>
              </n-form-item>
            </n-form>
            
            <div class="form-actions">
              <n-button 
                type="primary" 
                size="large"
                @click="submitPaymentDetails"
                :loading="isSubmitting"
                block
                class="submit-btn"
              >
                {{ t('transaction.saveAndContinue') }}
              </n-button>
              <n-button 
                v-if="hasProfileDetails"
                type="default"
                size="medium"
                @click="backToProfileOption"
                block
                class="back-btn"
              >
                {{ t('common.back') }}
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { useTranslation } from '@/composables/useTranslation'
import { useAuthStore } from '@/stores/auth'
import { 
  AccountBalance as BankIcon,
  CheckCircle as CheckIcon,
  Schedule as PendingIcon,
  Add as AddIcon,
  Close as CloseIcon,
  BookmarkBorder as SavedIcon,
  Save as SaveIcon
} from '@vicons/material'

interface Props {
  currentUser: User
  otherUser: User
  currentUserStatus: 'pending' | 'confirmed'
  otherUserStatus: 'pending' | 'confirmed' 
  hasProfileDetails?: boolean
  profileDetails?: PaymentDetails
}

const props = defineProps<Props>()
const emit = defineEmits<{
  submitPaymentDetails: [details: PaymentDetails]
  useProfileDetails: []
  sendReminder: []
  editDetails: []
}>()

const { t } = useTranslation()
const messageApi = useMessage()
const authStore = useAuthStore()

// Reactive state
const showPaymentForm = ref(false)
const showNewForm = ref(false)
const isSubmitting = ref(false)
const isUsingProfile = ref(false)

const paymentForm = ref({
  bankName: '',
  accountNumber: '',
  accountHolderName: '',
  saveToProfile: true
})

const paymentFormRules = {
  bankName: {
    required: true,
    message: t('validation.bankNameRequired'),
    trigger: 'blur'
  },
  accountNumber: {
    required: true,
    message: t('validation.accountNumberRequired'),
    trigger: 'blur'
  },
  accountHolderName: {
    required: true,
    message: t('validation.accountHolderRequired'),
    trigger: 'blur'
  }
}

// Computed properties
const currentUserReady = computed(() => props.currentUserStatus === 'confirmed')
const otherUserReady = computed(() => props.otherUserStatus === 'confirmed')
const bothUsersReady = computed(() => currentUserReady.value && otherUserReady.value)

const gateProgress = computed(() => {
  if (bothUsersReady.value) return 100
  if (currentUserReady.value || otherUserReady.value) return 50
  return 0
})

const gateStatusType = computed(() => {
  if (bothUsersReady.value) return 'success'
  if (currentUserReady.value || otherUserReady.value) return 'warning'
  return 'info'
})

const gateStatusText = computed(() => {
  if (bothUsersReady.value) return t('transaction.bothReady')
  if (gateProgress.value === 50) return t('transaction.halfReady')
  return t('transaction.waitingForDetails')
})

const progressText = computed(() => {
  if (bothUsersReady.value) {
    return t('transaction.proceedingToNegotiation')
  }
  const readyCount = [currentUserReady.value, otherUserReady.value].filter(Boolean).length
  return t('transaction.readyProgress', { ready: readyCount, total: 2 })
})

const currentUserStatusIcon = computed(() => {
  return currentUserReady.value ? CheckIcon : PendingIcon
})

const currentUserStatusText = computed(() => {
  return currentUserReady.value ? t('transaction.ready') : t('transaction.pending')
})

const currentUserStatusClass = computed(() => {
  return currentUserReady.value ? 'status-ready' : 'status-pending'
})

const otherUserStatusIcon = computed(() => {
  return otherUserReady.value ? CheckIcon : PendingIcon
})

const otherUserStatusText = computed(() => {
  return otherUserReady.value ? t('transaction.ready') : t('transaction.pending')
})

const otherUserStatusClass = computed(() => {
  return otherUserReady.value ? 'status-ready' : 'status-pending'
})

const maskedBankName = computed(() => {
  return props.profileDetails?.bankName || ''
})

const maskedAccount = computed(() => {
  const account = props.profileDetails?.accountNumber || ''
  if (account.length > 4) {
    return `****-${account.slice(-4)}`
  }
  return '****-****'
})

// Methods
const openPaymentForm = () => {
  showPaymentForm.value = true
  showNewForm.value = !props.hasProfileDetails
}

const closePaymentForm = () => {
  showPaymentForm.value = false
  showNewForm.value = false
}

const showNewDetailsForm = () => {
  showNewForm.value = true
}

const backToProfileOption = () => {
  showNewForm.value = false
}

const submitPaymentDetails = async () => {
  isSubmitting.value = true
  try {
    emit('submitPaymentDetails', {
      bankName: paymentForm.value.bankName,
      accountNumber: paymentForm.value.accountNumber,
      accountHolderName: paymentForm.value.accountHolderName,
      saveToProfile: paymentForm.value.saveToProfile
    })
    
    messageApi.success(t('transaction.detailsSaved'))
    closePaymentForm()
  } catch (error) {
    messageApi.error(t('transaction.saveError'))
  } finally {
    isSubmitting.value = false
  }
}

const useProfileDetails = async () => {
  isUsingProfile.value = true
  try {
    emit('useProfileDetails')
    messageApi.success(t('transaction.profileDetailsUsed'))
    closePaymentForm()
  } finally {
    isUsingProfile.value = false
  }
}

const sendReminder = () => {
  emit('sendReminder')
  messageApi.success(t('transaction.reminderSent'))
}

const editPaymentDetails = () => {
  emit('editDetails')
  openPaymentForm()
}

// Watch for both users ready to auto-advance
watch(bothUsersReady, (ready) => {
  if (ready) {
    setTimeout(() => {
      messageApi.info({
        content: t('transaction.advancingToNegotiation'),
        duration: 3000
      })
    }, 1000)
  }
})
</script>

<style scoped>
.payment-readiness-gate-message {
  background: linear-gradient(135deg, var(--n-info-color-suppl) 0%, var(--n-card-color) 100%);
  border: 1px solid var(--n-info-color);
  border-radius: 12px;
  padding: 1.25rem;
  margin: 0.75rem 0;
  position: relative;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.gate-icon {
  font-size: 1.5rem;
  color: var(--n-info-color);
}

.message-title {
  font-weight: 600;
  font-size: 1.05rem;
  color: var(--n-text-color);
}

.gate-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.gate-explanation {
  padding: 0.75rem;
  background: var(--n-body-color);
  border-radius: 8px;
  border-left: 3px solid var(--n-info-color);
}

.explanation-text {
  margin: 0;
  color: var(--n-text-color-2);
  font-size: 0.9rem;
  line-height: 1.4;
}

.payment-status-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--n-card-color);
  border-radius: 8px;
  border: 1px solid var(--n-border-color);
  transition: all 0.3s ease;
}

.status-item.ready {
  border-color: var(--n-success-color);
  background: var(--n-success-color-suppl);
}

.user-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.user-name {
  font-weight: 500;
  color: var(--n-text-color);
  font-size: 0.9rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator.status-ready {
  color: var(--n-success-color);
}

.status-indicator.status-pending {
  color: var(--n-warning-color);
}

.status-text {
  font-size: 0.8rem;
  font-weight: 500;
}

.status-action-btn {
  min-width: 80px;
}

.gate-progress {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progress-bar {
  height: 6px;
  background: var(--n-border-color);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--n-info-color), var(--n-success-color));
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 0.85rem;
  color: var(--n-text-color-2);
  font-weight: 500;
}

/* Payment Form Overlay */
.payment-form-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.form-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.payment-form-card {
  position: relative;
  background: var(--n-card-color);
  border-radius: 16px;
  padding: 1.5rem;
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.form-header {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.form-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--n-text-color);
}

.form-subtitle {
  margin: 0;
  font-size: 0.85rem;
  color: var(--n-text-color-2);
  line-height: 1.4;
}

.close-btn {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
}

.profile-option {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.saved-details-preview {
  padding: 1rem;
  background: var(--n-body-color);
  border-radius: 8px;
  border: 1px solid var(--n-border-color);
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.preview-title {
  font-weight: 500;
  color: var(--n-text-color);
}

.details-preview {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 0.85rem;
  color: var(--n-text-color-2);
}

.detail-value {
  font-size: 0.85rem;
  color: var(--n-text-color);
  font-weight: 500;
}

.profile-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.new-details-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-input {
  border-radius: 8px;
}

.save-profile-checkbox {
  width: 100%;
}

.checkbox-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.submit-btn {
  height: 48px;
  font-weight: 600;
}

.back-btn {
  height: 40px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .payment-form-container {
    padding: 0.5rem;
  }
  
  .payment-form-card {
    padding: 1.25rem;
    border-radius: 12px;
    max-height: 90vh;
  }
  
  .payment-status-grid {
    gap: 0.5rem;
  }
  
  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .user-section {
    width: 100%;
  }
  
  .status-action-btn {
    width: 100%;
    min-width: auto;
  }
}

/* Touch optimizations */
@media (pointer: coarse) {
  .status-action-btn {
    min-height: 44px;
  }
  
  .close-btn {
    min-width: 44px;
    min-height: 44px;
  }
}
</style>
```

This implementation provides:

1. **Unified Message Stream**: All content flows in a single, chronological stream
2. **Context-Aware System Messages**: Transaction updates appear as intelligent, actionable messages
3. **Smart Action Bar**: Dynamic bottom bar that shows exactly what the user needs to do
4. **Mobile-First Design**: Touch-friendly interactions, proper sizing, responsive layouts
5. **Performance Optimized**: Single scrolling container, efficient rendering
6. **Accessibility**: Proper ARIA labels, high contrast support, reduced motion options
7. **Type Safety**: Full TypeScript support with proper interfaces

The key innovation is eliminating the competing screen space problem by integrating transaction management directly into the chat flow, making it feel natural and conversational while providing clear, actionable information exactly when users need it.
