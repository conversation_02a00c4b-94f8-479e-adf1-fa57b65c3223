const http = require('http');

// Test the actual browse endpoint
async function testBrowseAPI() {
  console.log('=== Testing Browse API Endpoint ===');
  
  // Using a valid token from the frontend
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.N8_cU3mE1Df3D_vhF5C3Db_HNQMjJ9bnL3SJJzrOBjo';
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/offers/browse',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          console.log('Status Code:', res.statusCode);
          console.log('Response Headers:', res.headers);
          
          if (res.statusCode === 200) {
            const offers = JSON.parse(data);
            console.log('\n--- API RESPONSE DATA ---');
            console.log('Number of offers:', offers.length);
            
            offers.forEach((offer, index) => {
              console.log(`\nOffer ${index + 1}:`);
              console.log('  ID:', offer.id);
              console.log('  Type:', offer.type);
              console.log('  Amount:', offer.amount);
              console.log('  Creator:', offer.offerCreatorUsername);
              console.log('  Current User Interest:', offer.currentUserHasShownInterest);
              console.log('  Interest Status:', offer.currentUserInterestStatus);
              console.log('  Transaction Status:', offer.transactionStatus);
              console.log('  Negotiation Status:', offer.negotiationStatus);
              console.log('  Chat Session ID:', offer.chatSessionId);
            });
            
            // Look for the specific offer we know has completed transaction
            const targetOffer = offers.find(o => o.id === 'cmb40jjag002dvly8m8r2gwku');
            if (targetOffer) {
              console.log('\n--- TARGET OFFER (Should show Completed) ---');
              console.log('Transaction Status:', targetOffer.transactionStatus);
              console.log('Negotiation Status:', targetOffer.negotiationStatus);
              console.log('Expected Frontend Display: Completed');
            }
            
            resolve(offers);
          } else {
            console.log('Error Response:', data);
            reject(new Error(`HTTP ${res.statusCode}: ${data}`));
          }
        } catch (error) {
          console.error('Error parsing response:', error);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error('Request error:', error);
      reject(error);
    });

    req.end();
  });
}

testBrowseAPI().catch(console.error);
