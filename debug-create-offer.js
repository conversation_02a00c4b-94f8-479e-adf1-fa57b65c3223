// Test script for authenticated API calls
// Copy your token from localStorage and run this test

const authToken = 'eyJhbGciOiJIUzI1NiIs...'; // Replace with your full token

fetch('http://localhost:3000/api/offers', {
  method: 'POST',
  headers: {
    'Authorization': `<PERSON><PERSON> ${authToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    type: 'BUY',
    currencyPair: 'CAD-IRR', 
    amount: 200,
    rate: 26000,
    description: 'Test offer',
    location: 'Toronto'
  })
})
.then(response => {
  console.log('Status:', response.status);
  return response.text();
})
.then(data => {
  console.log('Response:', data);
})
.catch(error => {
  console.error('Error:', error);
});
