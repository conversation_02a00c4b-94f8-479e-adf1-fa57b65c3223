#!/bin/bash

# Debug Logs Management Script for CentOS Host
# Usage: ./debug-logs.sh [ACTION] [OPTIONS]
# Actions: view, search, stats, cleanup, export

set -e

# Configuration
CONTAINER_NAME="munygo-backend"
LOG_PATH="/app/logs/client-reports.log"
BACKEND_PORT="3004"

# Default values
ACTION="view"
COUNT=10
PATTERN=""
EXPORT_PATH="./debug-reports-export.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Help function
show_help() {
    echo "Debug Reports Management Script for CentOS"
    echo ""
    echo "Usage: $0 [ACTION] [OPTIONS]"
    echo ""
    echo "Actions:"
    echo "  view     - View recent debug reports (default)"
    echo "  search   - Search for specific patterns in reports"
    echo "  stats    - Show log statistics"
    echo "  cleanup  - Trigger log rotation and cleanup"
    echo "  export   - Export all reports to JSON file"
    echo ""
    echo "Options:"
    echo "  -c, --count NUM      Number of reports to show (default: 10)"
    echo "  -p, --pattern TEXT   Search pattern for search action"
    echo "  -e, --export PATH    Export file path (default: ./debug-reports-export.json)"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 view -c 5                    # Show last 5 reports"
    echo "  $0 search -p \"ERROR\"           # Search for ERROR pattern"
    echo "  $0 stats                        # Show statistics"
    echo "  $0 export -e reports.json       # Export to specific file"
}

# Parse command line arguments
parse_args() {
    if [[ $# -eq 0 ]]; then
        ACTION="view"
        return
    fi
    
    ACTION="$1"
    shift
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--count)
                COUNT="$2"
                shift 2
                ;;
            -p|--pattern)
                PATTERN="$2"
                shift 2
                ;;
            -e|--export)
                EXPORT_PATH="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Check if container is running
check_container() {
    if ! docker ps --filter "name=$CONTAINER_NAME" --filter "status=running" --quiet | grep -q .; then
        echo -e "${RED}❌ Container '$CONTAINER_NAME' is not running. Please start the backend container first.${NC}"
        exit 1
    fi
}

# Check if log file exists in container
check_log_file() {
    if ! docker exec "$CONTAINER_NAME" test -f "$LOG_PATH" 2>/dev/null; then
        echo -e "${YELLOW}🔍 No log file found yet. No reports have been submitted.${NC}"
        return 1
    fi
    return 0
}

# View recent debug reports
view_reports() {
    check_container
    
    echo -e "${GREEN}📋 Fetching last $COUNT debug reports...${NC}"
    
    if ! check_log_file; then
        return
    fi
    
    # Get the reports from container
    reports_raw=$(docker exec "$CONTAINER_NAME" tail -n "$COUNT" "$LOG_PATH")
    
    if [[ -z "$reports_raw" ]]; then
        echo -e "${YELLOW}🔍 No debug reports found.${NC}"
        return
    fi
    
    echo -e "\n${CYAN}📊 Recent reports:${NC}"
    
    # Process each line as JSON
    echo "$reports_raw" | while IFS= read -r line; do
        if [[ -n "$line" ]]; then
            # Extract key fields using jq if available, otherwise use basic parsing
            if command -v jq >/dev/null 2>&1; then
                report_id=$(echo "$line" | jq -r '.reportId // "N/A"')
                timestamp=$(echo "$line" | jq -r '.timestamp // "N/A"')
                session_id=$(echo "$line" | jq -r '.sessionId // "N/A"')
                log_count=$(echo "$line" | jq -r '.logCount // "N/A"')
                current_url=$(echo "$line" | jq -r '.currentUrl // "N/A"')
                user_notes=$(echo "$line" | jq -r '.userNotes // ""')
                error_count=$(echo "$line" | jq '[.logs[]? | select(.level == "ERROR")] | length')
                
                echo -e "\n${WHITE}🆔 Report ID: $report_id${NC}"
                echo -e "${GRAY}📅 Timestamp: $timestamp${NC}"
                echo -e "${GRAY}🔗 Session: $session_id${NC}"
                echo -e "${GRAY}📝 Log Count: $log_count${NC}"
                echo -e "${GRAY}🌐 URL: $current_url${NC}"
                
                if [[ -n "$user_notes" && "$user_notes" != "null" ]]; then
                    echo -e "${YELLOW}💬 User Notes: $user_notes${NC}"
                fi
                
                if [[ "$error_count" -gt 0 ]]; then
                    echo -e "${RED}❌ Recent Errors: $error_count${NC}"
                fi
            else
                # Fallback without jq - just show raw JSON (less pretty)
                echo -e "${WHITE}📄 Report:${NC}"
                echo "$line" | head -c 200
                echo "..."
            fi
            echo -e "${GRAY}─────────────────────────────────────────${NC}"
        fi
    done
}

# Search for patterns in reports
search_reports() {
    check_container
    
    if [[ -z "$PATTERN" ]]; then
        echo -e "${RED}❌ Please provide a search pattern with -p parameter${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}🔍 Searching for pattern: '$PATTERN'...${NC}"
    
    if ! check_log_file; then
        return
    fi
    
    # Search for pattern in log file
    matches=$(docker exec "$CONTAINER_NAME" grep -i "$PATTERN" "$LOG_PATH" 2>/dev/null || true)
    
    if [[ -z "$matches" ]]; then
        echo -e "${YELLOW}🔍 No matches found for pattern: '$PATTERN'${NC}"
        return
    fi
    
    match_count=$(echo "$matches" | wc -l)
    echo -e "\n${CYAN}📊 Found $match_count matching reports:${NC}"
    
    # Process matching lines
    echo "$matches" | while IFS= read -r line; do
        if [[ -n "$line" ]] && command -v jq >/dev/null 2>&1; then
            report_id=$(echo "$line" | jq -r '.reportId // "N/A"')
            timestamp=$(echo "$line" | jq -r '.timestamp // "N/A"')
            current_url=$(echo "$line" | jq -r '.currentUrl // "N/A"')
            
            echo -e "\n${WHITE}🆔 Report ID: $report_id${NC}"
            echo -e "${GRAY}📅 Timestamp: $timestamp${NC}"
            echo -e "${GRAY}🌐 URL: $current_url${NC}"
            echo -e "${CYAN}🎯 Match: $(echo "$line" | grep -i -o ".\{0,50\}$PATTERN.\{0,50\}")${NC}"
            echo -e "${GRAY}─────────────────────────────────────────${NC}"
        fi
    done
}

# Show log statistics
show_stats() {
    check_container
    
    echo -e "${GREEN}📊 Fetching log statistics...${NC}"
    
    if ! check_log_file; then
        return
    fi
    
    # Get file stats from container
    echo -e "\n${CYAN}📈 Log File Statistics:${NC}"
    
    line_count=$(docker exec "$CONTAINER_NAME" wc -l "$LOG_PATH" | awk '{print $1}')
    file_size=$(docker exec "$CONTAINER_NAME" ls -lh "$LOG_PATH" | awk '{print $5}')
    file_date=$(docker exec "$CONTAINER_NAME" ls -l "$LOG_PATH" | awk '{print $6, $7, $8}')
    
    echo -e "${WHITE}📄 Total Reports: $line_count${NC}"
    echo -e "${WHITE}📦 File Size: $file_size${NC}"
    echo -e "${WHITE}📅 Last Modified: $file_date${NC}"
    
    # Try to get additional file info
    docker exec "$CONTAINER_NAME" sh -c "find /app/logs -name 'client-reports*.log' -ls 2>/dev/null || true" | while read -r file_info; do
        if [[ -n "$file_info" ]]; then
            echo -e "${GRAY}📁 $file_info${NC}"
        fi
    done
    
    # Try to get API stats if available
    echo -e "\n${CYAN}🌐 Attempting to fetch API statistics...${NC}"
    
    # Get container IP or use localhost
    container_ip=$(docker inspect "$CONTAINER_NAME" --format='{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' 2>/dev/null || echo "localhost")
    
    if curl -s "http://$container_ip:$BACKEND_PORT/api/debug/stats" >/dev/null 2>&1; then
        api_stats=$(curl -s "http://$container_ip:$BACKEND_PORT/api/debug/stats")
        if echo "$api_stats" | jq -e '.success' >/dev/null 2>&1; then
            echo -e "${CYAN}📊 API Statistics:${NC}"
            echo "$api_stats" | jq -r '
                "Total Reports: " + (.data.reports.totalReports | tostring) + "\n" +
                "Log File Size: " + (.data.storage.totalSizeMB | tostring) + " MB\n" +
                "Total Files: " + (.data.storage.totalFiles | tostring) + "\n" +
                "Log Directory: " + .data.logDirectory
            '
        else
            echo -e "${YELLOW}ℹ️ API stats not available${NC}"
        fi
    else
        echo -e "${YELLOW}ℹ️ Could not connect to API (container may not expose port $BACKEND_PORT)${NC}"
    fi
}

# Trigger log cleanup
cleanup_logs() {
    check_container
    
    echo -e "${GREEN}🧹 Triggering log cleanup...${NC}"
    
    # Get container IP
    container_ip=$(docker inspect "$CONTAINER_NAME" --format='{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' 2>/dev/null || echo "localhost")
    
    if curl -s -X POST "http://$container_ip:$BACKEND_PORT/api/debug/cleanup" >/dev/null 2>&1; then
        response=$(curl -s -X POST "http://$container_ip:$BACKEND_PORT/api/debug/cleanup")
        if echo "$response" | jq -e '.success' >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Log cleanup completed successfully${NC}"
        else
            echo -e "${RED}❌ Log cleanup failed${NC}"
            echo "$response" | jq -r '.message // "Unknown error"'
        fi
    else
        echo -e "${RED}❌ Could not connect to cleanup API${NC}"
    fi
}

# Export debug reports
export_reports() {
    check_container
    
    echo -e "${GREEN}📤 Exporting debug reports to: $EXPORT_PATH${NC}"
    
    if ! check_log_file; then
        return
    fi
    
    # Copy all log contents from container
    docker exec "$CONTAINER_NAME" cat "$LOG_PATH" > "/tmp/raw_reports.log"
    
    # Convert to proper JSON array if jq is available
    if command -v jq >/dev/null 2>&1; then
        echo "["$(cat "/tmp/raw_reports.log" | tr '\n' ',' | sed 's/,$//')"]" | jq '.' > "$EXPORT_PATH"
        report_count=$(cat "/tmp/raw_reports.log" | wc -l)
    else
        # Fallback: just copy the raw file
        cp "/tmp/raw_reports.log" "$EXPORT_PATH"
        report_count=$(cat "$EXPORT_PATH" | wc -l)
    fi
    
    rm -f "/tmp/raw_reports.log"
    
    echo -e "${GREEN}✅ Exported $report_count reports to $EXPORT_PATH${NC}"
}

# Main execution
main() {
    parse_args "$@"
    
    case "$ACTION" in
        "view")
            view_reports
            ;;
        "search")
            search_reports
            ;;
        "stats")
            show_stats
            ;;
        "cleanup")
            cleanup_logs
            ;;
        "export")
            export_reports
            ;;
        *)
            echo -e "${RED}❌ Unknown action: $ACTION${NC}"
            show_help
            exit 1
            ;;
    esac
}

# Check dependencies
if ! command -v docker >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not installed or not in PATH${NC}"
    exit 1
fi

# Run main function
main "$@"
