import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { zValidator } from '@hono/zod-validator';
import { 
  AudioTranscriptionRequestSchema,
  VoiceToReportRequestSchema,
  type AudioTranscriptionResponse,
  type VoiceToReportResponse
} from '../types/schemas/aiSchemas';
import { AiService } from '../services/aiService';
import { AudioTranscriptionService } from '../services/audioTranscriptionService';

/**
 * AI Routes for voice-powered bug reporting
 * Handles audio transcription and AI analysis endpoints
 */
export function createAiRoutes(): Hono {
  const router = new Hono();
  
  // Initialize services
  const aiService = new AiService();
  const transcriptionService = new AudioTranscriptionService();

  /**
   * GET /ai/status
   * Check AI service availability
   */
  router.get('/status', async (c) => {
    try {
      const status = {
        aiServiceAvailable: aiService.isAvailable(),
        transcriptionServiceAvailable: transcriptionService.isAvailable(),
        supportedAudioFormats: AudioTranscriptionService.getSupportedFormats(),
        bestAudioFormat: AudioTranscriptionService.getBestSupportedFormat(),
        maxAudioDuration: 60, // seconds
        supportedLanguages: ['en', 'fa'],
        features: {
          transcription: transcriptionService.isAvailable(),
          aiAnalysis: aiService.isAvailable(),
          voiceToReport: transcriptionService.isAvailable() && aiService.isAvailable(),
        },
      };

      return c.json({
        success: true,
        status,
      });
    } catch (error: any) {
      console.error('[AiRoutes] Error checking AI status:', error);
      return c.json({
        success: false,
        error: 'Failed to check AI service status',
      }, 500);
    }
  });

  /**
   * POST /ai/transcribe
   * Transcribe audio to text
   */
  router.post(
    '/transcribe',
    zValidator('json', AudioTranscriptionRequestSchema),
    async (c) => {
      try {
        const requestData = c.req.valid('json');
        
        console.log(`[AiRoutes] Transcription request received - Duration: ${requestData.duration}s`);

        const result = await transcriptionService.transcribeAudio(requestData);
        
        const response: AudioTranscriptionResponse = result;
        
        if (result.success) {
          console.log(`[AiRoutes] Transcription successful - Confidence: ${result.confidence}, Length: ${result.transcription?.length} chars`);
        } else {
          console.warn(`[AiRoutes] Transcription failed: ${result.error}`);
        }

        return c.json(response);
      } catch (error: any) {
        console.error('[AiRoutes] Error in transcription endpoint:', error);
        return c.json({
          success: false,
          error: 'Internal server error during transcription',
        } as AudioTranscriptionResponse, 500);
      }
    }
  );

  /**
   * POST /ai/voice-to-report
   * Direct audio-to-report processing using Gemini 2.5 Flash
   * Optimized single-step processing without intermediate transcription
   */
  router.post(
    '/voice-to-report',
    zValidator('json', VoiceToReportRequestSchema),
    async (c) => {
      const startTime = Date.now();
      try {
        const requestData = c.req.valid('json');
        console.log(`[AiRoutes] Voice-to-report request received - Duration: ${requestData.duration}s, MimeType: ${requestData.mimeType}`);
        
        if (requestData.audioData) {
          const crypto = require('crypto');
          const audioBuffer = Buffer.from(requestData.audioData, 'base64');
          const audioHash = crypto.createHash('sha256').update(audioBuffer).digest('hex');
          console.log(`[AiRoutes] Audio buffer received: size=${audioBuffer.length} bytes, sha256=${audioHash}`);
          
          // Check AI service availability before processing
          if (!aiService.isAvailable()) {
            console.error('[AiRoutes] AI service is not available - missing API key or initialization failed');
            return c.json({
              success: false,
              error: 'AI service is not available. Please check server configuration.',
              processingTime: Date.now() - startTime
            } as VoiceToReportResponse, 503);
          }
          
          // Direct audio-to-report processing using Gemini 2.5 Flash
          console.log(`[AiRoutes] Starting AI processing at ${Date.now() - startTime}ms from request start`);
          const aiStart = Date.now();
          
          // Create an AbortController to handle request cancellation
          const abortController = new AbortController();
          
          // Set up a timeout for the entire request (frontend timeout is typically 60s)
          const requestTimeout = setTimeout(() => {
            console.log(`[AiRoutes] ⏰ Request timeout reached, aborting AI processing`);
            abortController.abort();
          }, 90000); // 90 seconds - shorter than frontend timeout to allow fallback response delivery
          
          try {
            const result = await aiService.processAudioToReport(
              audioBuffer,
              requestData.mimeType,
              requestData.userContext,
              requestData.predefinedTags,
              abortController.signal
            );
            
            clearTimeout(requestTimeout);
            
            const aiDuration = Date.now() - aiStart;
            const totalDuration = Date.now() - startTime;
            
            console.log(`[AiRoutes] AI service processAudioToReport completed in ${aiDuration}ms, total endpoint time: ${totalDuration}ms`);
            
            if (result && result.success) {
              if (result.isFallback) {
                console.log(`[AiRoutes] ✅ AI report generated via FALLBACK processing. Transcription length: ${result.transcription ? result.transcription.length : 0}, Processing time: ${result.processingTime}ms`);
              } else {
                console.log(`[AiRoutes] ✅ AI report generated successfully. Transcription length: ${result.transcription ? result.transcription.length : 0}, Processing time: ${result.processingTime}ms`);
              }
            } else {
              console.warn(`[AiRoutes] ❌ AI report failed: ${result.error}`);
              if (result.processingTime && result.processingTime > 45000) {
                console.warn(`[AiRoutes] ⚠️  Long processing time detected: ${result.processingTime}ms - this may indicate Gemini API issues`);
              }
              
              // Log suggestions for timeout issues
              if (result.error && result.error.includes('timeout')) {
                console.warn(`[AiRoutes] 💡 Timeout troubleshooting suggestions:`);
                console.warn(`[AiRoutes]   - Audio size was ${audioBuffer.length} bytes (consider shorter recordings)`);
                console.warn(`[AiRoutes]   - Duration was ${requestData.duration}s (consider max 30s)`);
                console.warn(`[AiRoutes]   - Try clearer audio quality`);
                console.warn(`[AiRoutes]   - Check Gemini API status at https://console.cloud.google.com/`);
              }
            }
            
            return c.json(result);
          } catch (error: any) {
            clearTimeout(requestTimeout);
            throw error; // Re-throw to be handled by outer catch block
          }
        } else {
          console.warn('[AiRoutes] No audioData provided in request');
          return c.json({
            success: false,
            error: 'No audioData provided',
            processingTime: Date.now() - startTime
          } as VoiceToReportResponse, 400);
        }
      } catch (error: any) {
        const totalDuration = Date.now() - startTime;
        console.error(`[AiRoutes] ENDPOINT ERROR in voice-to-report after ${totalDuration}ms:`, error);
        console.error(`[AiRoutes] Error details: name=${error.name}, message=${error.message}, code=${error.code}`);
        
        // Provide more specific error responses based on error type
        let errorMessage = error.message || 'Internal server error during voice-to-report processing';
        
        if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
          errorMessage = `Request timed out after ${totalDuration}ms. Please try again with a shorter audio clip.`;
          return c.json({
            success: false,
            error: errorMessage,
            processingTime: totalDuration
          } as VoiceToReportResponse, 504); // Gateway Timeout
        } else if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND') {
          errorMessage = 'Unable to connect to AI service. Please try again later.';
          return c.json({
            success: false,
            error: errorMessage,
            processingTime: totalDuration
          } as VoiceToReportResponse, 502); // Bad Gateway
        }
        
        return c.json({
          success: false,
          error: errorMessage,
          processingTime: totalDuration
        } as VoiceToReportResponse, 500);
      }
    }
  );

  /**
   * GET /ai/config
   * Get AI configuration for frontend
   */
  router.get('/config', async (c) => {
    try {
      const config = {
        maxAudioDuration: 60, // seconds
        supportedLanguages: ['en', 'fa'],
        supportedAudioFormats: AudioTranscriptionService.getSupportedFormats(),
        recommendedAudioFormat: AudioTranscriptionService.getBestSupportedFormat(),
        features: {
          transcription: transcriptionService.isAvailable(),
          aiAnalysis: aiService.isAvailable(),
          voiceToReport: transcriptionService.isAvailable() && aiService.isAvailable(),
        },
      };

      return c.json({
        success: true,
        config,
      });
    } catch (error: any) {
      console.error('[AiRoutes] Error getting AI config:', error);
      return c.json({
        success: false,
        error: 'Failed to get AI configuration',
      }, 500);
    }
  });

  return router;
}
