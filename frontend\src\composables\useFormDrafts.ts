import { ref, computed, readonly, toRaw } from 'vue';
import type { ReportDetails, FormDraft, FormDraftStorage } from '@/types/logging';
import { useClientLogger } from '@/composables/useClientLogger';
import { useI18n } from 'vue-i18n';
import { useMessage } from 'naive-ui';

const STORAGE_KEY = 'munygo-form-drafts';
const AUTO_SAVE_DELAY_MS = 2000; // 2 seconds
const CLEANUP_INTERVAL_DAYS = 7; // Shorter cleanup period since we only keep one

// Global state for form drafts
const formDrafts = ref<FormDraft[]>([]);
const activeDraftId = ref<string | null>(null);
const autoSaveTimer = ref<number | null>(null);
const lastAutoSaveAt = ref<string | null>(null);
const isInitialized = ref(false);
const lastSaveError = ref<string | null>(null);
const currentFormData = ref<ReportDetails | null>(null);

/**
 * Composable for managing form draft auto-saving and restoration
 */
export function useFormDrafts() {
  const { t } = useI18n();
  const message = useMessage();
  const logger = useClientLogger();

  // Computed properties
  // Note: These are kept for backward compatibility but overridden below
  const activeDraft = computed(() => 
    activeDraftId.value 
      ? formDrafts.value.find(draft => draft.id === activeDraftId.value) 
      : null
  );

  /**
   * Load form drafts from localStorage
   */
  const loadFormDrafts = (): void => {
    if (isInitialized.value) return;

    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const storage: FormDraftStorage = JSON.parse(stored);
        formDrafts.value = storage.drafts || [];
        activeDraftId.value = storage.activeDraftId || null;

        // Clean up old drafts
        cleanupOldDrafts();

        logger.logInfo('Form drafts loaded', {
          count: formDrafts.value.length,
          activeDraftId: activeDraftId.value,
          lastCleanup: storage.lastCleanup
        });
      }
      isInitialized.value = true;
    } catch (error) {
      console.error('Failed to load form drafts:', error);
      formDrafts.value = [];
      activeDraftId.value = null;
      isInitialized.value = true;
    }
  };

  /**
   * Save form drafts to localStorage with enhanced error handling
   */
  const saveFormDrafts = (): boolean => {
    try {
      const storage: FormDraftStorage = {
        drafts: formDrafts.value,
        activeDraftId: activeDraftId.value || undefined,
        lastCleanup: new Date().toISOString()
      };
      
      localStorage.setItem(STORAGE_KEY, JSON.stringify(storage));
      lastSaveError.value = null;
      return true;
    } catch (error) {
      console.error('Failed to save form drafts:', error);
      
      // Handle specific localStorage errors
      let errorMessage = t('debug.draftSaveError');
      if (error instanceof DOMException) {
        if (error.name === 'QuotaExceededError' || error.name === 'NS_ERROR_DOM_QUOTA_REACHED') {
          errorMessage = t('debug.storageQuotaExceeded');
        } else if (error.name === 'SecurityError') {
          errorMessage = t('debug.storageSecurityError');
        }
      }
      
      lastSaveError.value = errorMessage;
      logger.logError('Failed to save form drafts to localStorage', error);
      
      // Show user notification for critical errors
      message.warning(errorMessage);
      return false;
    }
  };

  /**
   * Create or update the single draft with robust serialization
   */
  const saveDraft = (formData: ReportDetails): string => {
    const now = new Date().toISOString();
    
    try {
      // Use toRaw to get plain JavaScript object and prevent circular references
      const rawFormData = toRaw(formData);
      
      // Create a clean, serializable copy of the form data
      const cleanFormData: ReportDetails = {
        type: rawFormData.type,
        severity: rawFormData.severity,
        title: rawFormData.title || '',
        description: rawFormData.description || '',
        stepsToReproduce: rawFormData.stepsToReproduce || '',
        expectedBehavior: rawFormData.expectedBehavior || '',
        actualBehavior: rawFormData.actualBehavior || '',
        additionalNotes: rawFormData.additionalNotes || '',
        reportTags: Array.isArray(rawFormData.reportTags) ? [...rawFormData.reportTags] : []
      };
      
      // Always use the same draft ID for single draft mode
      const SINGLE_DRAFT_ID = 'debug-report-draft';
      
      const existingIndex = formDrafts.value.findIndex(draft => draft.id === SINGLE_DRAFT_ID);
      if (existingIndex !== -1) {
        // Update existing draft
        formDrafts.value[existingIndex] = {
          ...formDrafts.value[existingIndex],
          formData: cleanFormData,
          lastModified: now
        };
      } else {
        // Create new draft (replace any existing drafts)
        const newDraft: FormDraft = {
          id: SINGLE_DRAFT_ID,
          formData: cleanFormData,
          timestamp: now,
          lastModified: now
        };

        formDrafts.value = [newDraft]; // Replace all drafts with just this one
      }
      
      const saveSuccess = saveFormDrafts();
      if (saveSuccess) {
        lastAutoSaveAt.value = now;
        
        logger.logInfo('Form draft saved', {
          draftId: SINGLE_DRAFT_ID,
          hasTitle: !!cleanFormData.title,
          hasDescription: !!cleanFormData.description
        });
      }

      return SINGLE_DRAFT_ID;
    } catch (error) {
      console.error('Error preparing draft data:', error);
      logger.logError('Failed to prepare draft data for saving', error);
      throw error;
    }
  };

  /**
   * Auto-save form data with debouncing and emergency save on beforeunload
   */
  const autoSaveDraft = (formData: ReportDetails): void => {
    // Store current form data for emergency save
    currentFormData.value = formData;
    
    // Clear existing timer
    if (autoSaveTimer.value) {
      clearTimeout(autoSaveTimer.value);
    }

    // Only auto-save if there's meaningful content
    const hasContent = formData.title.trim() || 
                      formData.description.trim() || 
                      formData.stepsToReproduce?.trim() ||
                      formData.expectedBehavior?.trim() ||
                      formData.actualBehavior?.trim() ||
                      formData.additionalNotes?.trim();

    if (!hasContent) {
      return;
    }

    // Set new timer
    autoSaveTimer.value = window.setTimeout(() => {
      try {
        const savedDraftId = saveDraft(formData);
        
        // Set as active draft
        activeDraftId.value = savedDraftId;
      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    }, AUTO_SAVE_DELAY_MS);
  };

  /**
   * Load a specific draft
   */
  const loadDraft = (draftId: string): ReportDetails | null => {
    const draft = formDrafts.value.find(d => d.id === draftId);
    if (draft) {
      activeDraftId.value = draftId;
      saveFormDrafts();
      
      logger.logInfo('Form draft loaded', {
        draftId,
        timestamp: draft.timestamp,
        lastModified: draft.lastModified
      });
      
      return { ...draft.formData };
    }
    return null;
  };

  /**
   * Get the current draft (there's only one)
   */
  const getCurrentDraft = (): ReportDetails | null => {
    if (formDrafts.value.length === 0) {
      return null;
    }
    
    const currentDraft = formDrafts.value[0];
    return loadDraft(currentDraft.id);
  };

  /**
   * Manual save - force immediate save without debouncing
   */
  const manualSaveDraft = (formData: ReportDetails): boolean => {
    try {
      const savedDraftId = saveDraft(formData);
      activeDraftId.value = savedDraftId;
      message.success(t('debug.draftSavedManually'));
      return true;
    } catch (error) {
      console.error('Manual save failed:', error);
      message.error(t('debug.draftSaveError'));
      return false;
    }
  };

  /**
   * Emergency save - called on beforeunload
   */
  const emergencySave = (): void => {
    if (currentFormData.value && autoSaveTimer.value) {
      // There's pending data and an active timer - save immediately
      try {
        clearTimeout(autoSaveTimer.value);
        autoSaveTimer.value = null;
        saveDraft(currentFormData.value);
        logger.logInfo('Emergency draft save completed');
      } catch (error) {
        console.error('Emergency save failed:', error);
      }
    }
  };

  /**
   * Setup beforeunload listener for emergency saves
   */
  const setupEmergencySave = (): void => {
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', emergencySave);
    }
  };

  /**
   * Cleanup beforeunload listener
   */
  const cleanupEmergencySave = (): void => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('beforeunload', emergencySave);
    }
  };

  /**
   * Delete a specific draft
   */
  const deleteDraft = (draftId: string): void => {
    const index = formDrafts.value.findIndex(draft => draft.id === draftId);
    if (index !== -1) {
      formDrafts.value.splice(index, 1);
      
      // Clear active draft if it was deleted
      if (activeDraftId.value === draftId) {
        activeDraftId.value = null;
      }
      
      saveFormDrafts();
      
      logger.logInfo('Form draft deleted', {
        draftId,
        remainingDrafts: formDrafts.value.length
      });
    }
  };

  /**
   * Clear all drafts
   */
  const clearAllDrafts = (): void => {
    formDrafts.value = [];
    activeDraftId.value = null;
    
    if (autoSaveTimer.value) {
      clearTimeout(autoSaveTimer.value);
      autoSaveTimer.value = null;
    }
    
    saveFormDrafts();
    logger.logInfo('All form drafts cleared');
  };

  /**
   * Clear the active draft (after successful submission)
   */
  const clearActiveDraft = (): void => {
    // Clear any pending auto-save timer
    if (autoSaveTimer.value) {
      clearTimeout(autoSaveTimer.value);
      autoSaveTimer.value = null;
    }
    
    // Clear current form data reference
    currentFormData.value = null;
    
    // Delete the active draft
    if (activeDraftId.value) {
      deleteDraft(activeDraftId.value);
      activeDraftId.value = null;
    }
    
    // Clear any save errors
    lastSaveError.value = null;
    lastAutoSaveAt.value = null;
  };

  /**
   * Clean up old drafts
   */
  const cleanupOldDrafts = (): void => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - CLEANUP_INTERVAL_DAYS);
    
    const initialCount = formDrafts.value.length;
    formDrafts.value = formDrafts.value.filter(draft => {
      const draftDate = new Date(draft.timestamp);
      return draftDate > cutoffDate;
    });
    
    const removedCount = initialCount - formDrafts.value.length;
    if (removedCount > 0) {
      saveFormDrafts();
      logger.logInfo('Cleaned up old form drafts', {
        removedCount,
        remainingCount: formDrafts.value.length
      });
    }
  };

  /**
   * Get draft statistics
   */
  const getDraftStats = () => {
    return {
      total: formDrafts.value.length,
      activeDraftId: activeDraftId.value,
      oldestDraft: formDrafts.value.length > 0 
        ? formDrafts.value[formDrafts.value.length - 1].timestamp 
        : null,
      newestDraft: formDrafts.value.length > 0 
        ? formDrafts.value[0].timestamp 
        : null,
      lastAutoSave: lastAutoSaveAt.value
    };
  };

  /**
   * Check if form has unsaved changes compared to active draft
   */
  const hasUnsavedChanges = (currentFormData: ReportDetails): boolean => {
    if (!activeDraft.value) {
      // No active draft, check if form has any content
      return !!(currentFormData.title.trim() || 
               currentFormData.description.trim() || 
               currentFormData.stepsToReproduce?.trim() ||
               currentFormData.expectedBehavior?.trim() ||
               currentFormData.actualBehavior?.trim() ||
               currentFormData.additionalNotes?.trim());
    }

    const draft = activeDraft.value.formData;
    return (
      currentFormData.title !== draft.title ||
      currentFormData.description !== draft.description ||
      currentFormData.type !== draft.type ||
      currentFormData.severity !== draft.severity ||
      currentFormData.stepsToReproduce !== draft.stepsToReproduce ||
      currentFormData.expectedBehavior !== draft.expectedBehavior ||
      currentFormData.actualBehavior !== draft.actualBehavior ||
      currentFormData.additionalNotes !== draft.additionalNotes
    );
  };

  // Ensure initialization when accessing computed properties
  const ensureInitialized = () => {
    if (typeof window !== 'undefined' && !isInitialized.value) {
      loadFormDrafts();
    }
  };

  // Override computed properties to ensure initialization
  const hasDraftsComputed = computed(() => {
    ensureInitialized();
    return formDrafts.value.length > 0;
  });

  const draftCountComputed = computed(() => {
    ensureInitialized();
    return formDrafts.value.length;
  });

  const activeDraftComputed = computed(() => {
    ensureInitialized();
    return activeDraftId.value
      ? formDrafts.value.find(draft => draft.id === activeDraftId.value)
      : null;
  });

  return {
    // State
    formDrafts: readonly(formDrafts),
    hasDrafts: hasDraftsComputed,
    draftCount: draftCountComputed,
    activeDraft: activeDraftComputed,
    activeDraftId: readonly(activeDraftId),
    lastAutoSaveAt: readonly(lastAutoSaveAt),
    lastSaveError: readonly(lastSaveError),

    // Methods
    saveDraft,
    autoSaveDraft,
    manualSaveDraft,
    loadDraft,
    getCurrentDraft,
    deleteDraft,
    clearAllDrafts,
    clearActiveDraft,
    cleanupOldDrafts,
    getDraftStats,
    hasUnsavedChanges,
    emergencySave,
    setupEmergencySave,
    cleanupEmergencySave
  };
}
