<template>
  <div 
    v-if="shouldShowBanner" 
    class="pinned-action-banner"
    data-testid="pinned-action-banner"
  >
    <div class="banner-content">
      <div class="action-info">
        <div class="action-icon">
          <n-icon :component="getActionIcon(pinnedAction?.actionType || '')" />
        </div>
        <div class="action-text">
          <h4 class="action-title">{{ pinnedAction ? $t(pinnedAction.title) : '' }}</h4>
          <p class="action-subtitle">{{ getActionSubtitle(pinnedAction?.actionType || '') }}</p>
        </div>
      </div>
      
      <n-button
        type="primary"
        size="small"
        ghost
        class="view-details-btn"
        data-testid="view-details-btn"
        @click="scrollToAction"
      >
        <template #icon>
          <n-icon :component="ArrowDownIcon" />
        </template>
        {{ $t('transactionalChat.pinnedBanner.viewDetails') }}
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NButton, NIcon } from 'naive-ui';
import { 
  CreditCard as CreditCardIcon,
  Users as HandshakeIcon,
  CircleCheck as CheckCircleIcon,
  Send as SendIcon,
  Star as StarIcon,
  ArrowDown as ArrowDownIcon
} from '@vicons/tabler';
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore';
import { useI18n } from 'vue-i18n';

const transactionStore = useTransactionalChatStore();
const { t } = useI18n();

// Use the computed property that considers visibility
const shouldShowBanner = computed(() => transactionStore.shouldShowPinnedBanner);
const pinnedAction = computed(() => transactionStore.pinnedAction);

const getActionIcon = (actionType: string) => {
  switch (actionType) {
    case 'paymentInfo':
      return CreditCardIcon;
    case 'negotiation':
      return HandshakeIcon;
    case 'confirmReceipt':
      return CheckCircleIcon;
    case 'yourTurnToPay':
      return SendIcon;
    case 'rateExperience':
      return StarIcon;
    default:
      return CheckCircleIcon;
  }
};

const getActionSubtitle = (actionType: string): string => {
  switch (actionType) {
    case 'paymentInfo':
      return t('transactionalChat.pinnedBanner.subtitles.paymentInfo');
    case 'negotiation':
      return t('transactionalChat.pinnedBanner.subtitles.negotiation');
    case 'confirmReceipt':
      return t('transactionalChat.pinnedBanner.subtitles.confirmReceipt');
    case 'yourTurnToPay':
      return t('transactionalChat.pinnedBanner.subtitles.yourTurnToPay');
    case 'rateExperience':
      return t('transactionalChat.pinnedBanner.subtitles.rateExperience');
    default:
      return t('transactionalChat.pinnedBanner.subtitles.default');
  }
};

const scrollToAction = () => {
  if (pinnedAction.value) {
    transactionStore.scrollToActionCard(pinnedAction.value.cardId);
  }
};
</script>

<style scoped>
.pinned-action-banner {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--pinned-banner-bg);
  border: 1px solid var(--pinned-banner-border);
  border-radius: 12px;
  margin: 12px 16px;
  padding: 16px;
  box-shadow: var(--pinned-banner-shadow);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.action-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0; /* Prevents flex item from overflowing */
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--primary-color-suppl);
  border-radius: 50%;
  color: var(--primary-color);
  flex-shrink: 0;
}

.action-text {
  flex: 1;
  min-width: 0;
}

.action-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color-1);
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-subtitle {
  font-size: 12px;
  color: var(--text-color-3);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.view-details-btn {
  flex-shrink: 0;
  min-width: 88px;
  height: 32px;
  font-size: 12px;
  border-radius: 8px;
}

/* Light theme variables */
:root {
  --pinned-banner-bg: rgba(255, 255, 255, 0.95);
  --pinned-banner-border: rgba(239, 239, 245, 0.8);
  --pinned-banner-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Dark theme variables */
html.dark {
  --pinned-banner-bg: rgba(24, 24, 28, 0.95);
  --pinned-banner-border: rgba(255, 255, 255, 0.12);
  --pinned-banner-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Mobile optimization */
@media (max-width: 480px) {
  .pinned-action-banner {
    margin: 8px 12px;
    padding: 12px;
  }
  
  .banner-content {
    gap: 8px;
  }
  
  .action-icon {
    width: 36px;
    height: 36px;
  }
  
  .action-title {
    font-size: 13px;
  }
  
  .action-subtitle {
    font-size: 11px;
  }
  
  .view-details-btn {
    min-width: 80px;
    height: 30px;
    font-size: 11px;
  }
}

/* Animation for when banner appears/disappears */
.pinned-action-banner {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Hover effects */
.pinned-action-banner:hover {
  transform: translateY(-1px);
  box-shadow: var(--pinned-banner-shadow), 0 2px 8px rgba(var(--primary-color-rgb), 0.15);
}

.view-details-btn:hover {
  transform: scale(1.05);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .pinned-action-banner {
    border-width: 2px;
    box-shadow: none;
  }
  
  .action-icon {
    border: 2px solid var(--primary-color);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .pinned-action-banner,
  .view-details-btn {
    transition: none;
    animation: none;
  }
  
  .pinned-action-banner:hover,
  .view-details-btn:hover {
    transform: none;
  }
}
</style>
