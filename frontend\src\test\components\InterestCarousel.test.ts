import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import InterestCarousel from '@/components/InterestCarousel.vue'
import type { InterestRequestFrontend } from '@/types/offer'

// Mock the stores and composables
vi.mock('@/composables/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}))

vi.mock('@/stores/language', () => ({
  useLanguageStore: () => ({
    currentLanguage: 'en'
  })
}))

describe('InterestCarousel', () => {
  const mockInterests: InterestRequestFrontend[] = [
    {
      id: 'test-1',
      userId: 'user-1',
      username: 'TestUser1',
      status: 'PENDING',
      reputationLevel: 3,
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'test-2',
      userId: 'user-2',
      username: 'TestUser2',
      status: 'ACCEPTED',
      reputationLevel: 5,
      chatSessionId: 'chat-123',
      createdAt: '2024-01-02T00:00:00Z'
    }
  ]

  const defaultProps = {
    interests: mockInterests,
    processingInterests: new Set<string>()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should emit acceptInterest with correct payload when accept button is clicked', async () => {
    const wrapper = mount(InterestCarousel, {
      props: defaultProps,
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })]
      }
    })

    // Find and click the accept button for the first (pending) interest
    const acceptButton = wrapper.find('[data-testid="accept-button"]') || 
                        wrapper.find('.accept-button')
    
    if (acceptButton.exists()) {
      await acceptButton.trigger('click')
      
      // Check that the acceptInterest event was emitted with correct payload
      expect(wrapper.emitted('acceptInterest')).toBeTruthy()
      expect(wrapper.emitted('acceptInterest')?.[0]).toEqual(['test-1'])
    }
  })

  it('should emit declineInterest with correct payload when decline button is clicked', async () => {
    const wrapper = mount(InterestCarousel, {
      props: defaultProps,
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })]
      }
    })

    // Find and click the decline button for the first (pending) interest
    const declineButton = wrapper.find('[data-testid="decline-button"]') || 
                         wrapper.find('.decline-button')
    
    if (declineButton.exists()) {
      await declineButton.trigger('click')
      
      // Check that the declineInterest event was emitted with correct payload
      expect(wrapper.emitted('declineInterest')).toBeTruthy()
      expect(wrapper.emitted('declineInterest')?.[0]).toEqual(['test-1'])
    }
  })

  it('should emit goToChat with correct payload when chat button is clicked', async () => {
    const wrapper = mount(InterestCarousel, {
      props: defaultProps,
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })]
      }
    })

    // Navigate to the second slide (accepted interest with chat)
    const nextButton = wrapper.find('.next-button')
    if (nextButton.exists()) {
      await nextButton.trigger('click')
      await wrapper.vm.$nextTick()
    }

    // Find and click the chat button
    const chatButton = wrapper.find('[data-testid="chat-button"]') || 
                      wrapper.find('.chat-button')
    
    if (chatButton.exists()) {
      await chatButton.trigger('click')
      
      // Check that the goToChat event was emitted with correct payload
      expect(wrapper.emitted('goToChat')).toBeTruthy()
      expect(wrapper.emitted('goToChat')?.[0]).toEqual(['chat-123'])
    }
  })

  it('should have proper TypeScript type checking for emit calls', () => {
    // This test ensures that our defineEmits interface is working correctly
    // If there were type issues, TypeScript compilation would fail
    expect(true).toBe(true)
  })
})
