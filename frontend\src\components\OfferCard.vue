<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { 
  NCard, 
  NButton, 
  NText, 
  NDescriptions, 
  NDescriptionsItem, 
  NStatistic, 
  NAlert, 
  NTag, 
  useMessage 
} from 'naive-ui'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { offerService } from '@/services/offerService'
import { useTranslation } from '@/composables/useTranslation'
import { navigateToChat } from '@/utils/chatNavigation'
import type { BrowseOffer } from '@/types/offer'
import { formatAmountForDisplay } from '@/utils/currencyUtils'

const { t } = useTranslation()
const router = useRouter()
const message = useMessage()
const authStore = useAuthStore()
const isLoading = ref(false)

const props = defineProps<{
  offer: BrowseOffer
}>()

// Debugging watch - uncomment to use

// Uncomment for debugging only
// watch(() => props.offer, (newOffer, oldOffer) => {
//   console.log('[OfferCard] props.offer changed (watcher):', newOffer);
//   if (newOffer) {
//     console.log('[OfferCard] props.offer.currentUserHasShownInterest (watcher):', newOffer.currentUserHasShownInterest);
//   }
// }, { deep: true });

// Debugging watch - check transaction status data
watch(() => props.offer, (newOffer: BrowseOffer) => {
  if (newOffer && newOffer.id === 'cmb40jjag002dvly8m8r2gwku') {
    console.log('[OfferCard DEBUG] Target offer data:', {
      id: newOffer.id,
      type: newOffer.type,
      amount: newOffer.amount,
      creator: newOffer.offerCreatorUsername,
      transactionStatus: newOffer.transactionStatus,
      negotiationStatus: newOffer.negotiationStatus,
      currentUserInterestStatus: newOffer.currentUserInterestStatus,
      currentUserHasShownInterest: newOffer.currentUserHasShownInterest
    });
  }
}, { deep: true, immediate: true });

const hasCurrentUserShownInterest = computed(() => {
  console.log(`[OfferCard] COMPUTED hasCurrentUserShownInterest: offer ID ${props.offer?.id}, raw prop value: ${props.offer?.currentUserHasShownInterest}, computed to: ${!!props.offer?.currentUserHasShownInterest}`);
  return !!props.offer?.currentUserHasShownInterest;
});

const currentUserInterestStatus = computed(() => {
  console.log(`[OfferCard] COMPUTED currentUserInterestStatus: offer ID ${props.offer?.id}, status: ${props.offer?.currentUserInterestStatus}`);
  return props.offer?.currentUserInterestStatus;
});

const isInterestAccepted = computed(() => {
  const result = currentUserInterestStatus.value === 'ACCEPTED';
  console.log(`[OfferCard] COMPUTED isInterestAccepted: offer ID ${props.offer?.id}, result: ${result}`);
  return result;
});

const isTransactionCompleted = computed(() => {
  return transactionStatusDisplay.value === 'Completed';
});

const isTransactionInProgress = computed(() => {
  return transactionStatusDisplay.value === 'In Progress';
});

const isTransactionNegotiating = computed(() => {
  return transactionStatusDisplay.value === 'Negotiating';
});

const hasChatSession = computed(() => {
  const result = !!props.offer?.chatSessionId;
  console.log(`[OfferCard] COMPUTED hasChatSession: offer ID ${props.offer?.id}, chatSessionId: ${props.offer?.chatSessionId}, result: ${result}`);
  return result;
});

const isOwnOffer = computed(() => {
  return authStore.user?.id === props.offer?.offerCreatorId
})

const canShowInterest = computed(() => {
  const user = authStore.user;
  const isPhoneVerified = user ? user.phoneVerified === true : false;
  const localHasInterest = hasCurrentUserShownInterest.value; 
  const result = !isOwnOffer.value && isPhoneVerified && !localHasInterest;
  
  console.log(`[OfferCard] COMPUTED canShowInterest: offer ID ${props.offer?.id}`, {
    isOwnOffer: isOwnOffer.value,
    isPhoneVerified,
    hasCurrentUserShownInterest: localHasInterest,
    canShowInterestResult: result
  });
  return result;
})

const openChat = async (event?: Event) => {
  if (event) {
    event.stopPropagation(); // Prevent event bubbling
  }
  if (props.offer?.chatSessionId) {
    console.log(`[OfferCard] Opening chat for session: ${props.offer.chatSessionId}`);
    // Navigate to chat instead of just showing a message
    await navigateToChat(router, { chatSessionId: props.offer.chatSessionId });
  } else {
    console.warn(`[OfferCard] No chat session ID available for offer ${props.offer?.id}`);
    message.error(t('offers.chatNotAvailable'));
  }
}

const rateDifference = computed(() => {
  if (props.offer.calculatedApplicableRate != null && props.offer.baseRate != null) {
    const applicableRate = Number(props.offer.calculatedApplicableRate)
    const baseRate = Number(props.offer.baseRate)
    if (!isNaN(applicableRate) && !isNaN(baseRate)) {
      return applicableRate - baseRate
    }
  }
  return 0
})

const isBonus = computed(() => {
  if (isOwnOffer.value || Math.abs(rateDifference.value) < 0.00001) return false;
  if (props.offer.type === 'BUY') {
    return rateDifference.value > 0;
  } else { 
    return rateDifference.value < 0;
  }
});

const isPenalty = computed(() => {
  if (isOwnOffer.value || Math.abs(rateDifference.value) < 0.00001) return false;
  if (props.offer.type === 'BUY') {
    return rateDifference.value < 0;
  } else { 
    return rateDifference.value > 0;
  }
});

const rateEffectText = computed(() => {
  if (isOwnOffer.value || Math.abs(rateDifference.value) < 0.00001) {
    return ''
  }
  const diff = Math.abs(rateDifference.value)
  const formattedDiff = formatAmountForDisplay(diff, 'IRR', true)

  if (isBonus.value) {
    return t('offers.bonusText', { amount: formattedDiff })
  }
  if (isPenalty.value) {
    return t('offers.penaltyText', { amount: formattedDiff })
  }
  return ''
})

const rateEffectType = computed<'success' | 'warning' | 'info'> (() => {
  if (isBonus.value) return 'success'
  if (isPenalty.value) return 'warning'
  return 'info' 
})

const offerCreatorRepTagType = computed<'success' | 'warning' | 'info' | 'default'> (() => {
  const level = props.offer.offerCreatorReputationLevel;
  if (level === null || level === undefined) return 'default';
  if (level >= 4) return 'success'; 
  if (level <= 2) return 'warning'; 
  return 'info'; 
});

// Transaction status computed properties
const transactionStatusDisplay = computed(() => {
  const transactionStatus = props.offer.transactionStatus;
  const negotiationStatus = props.offer.negotiationStatus;
  
  console.log(`[OfferCard] transactionStatusDisplay computed for offer ${props.offer.id}:`, {
    transactionStatus,
    negotiationStatus,
    willReturn: !transactionStatus ? 'null' : 'will map status'
  });
  
  if (!transactionStatus) return null;
  
  // Map transaction status to display text
  switch (transactionStatus) {
    case 'AWAITING_FIRST_PAYER_DESIGNATION':
      return negotiationStatus === 'FINALIZED' ? t('offers.transactionStatusInProgress') : t('offers.transactionStatusNegotiating');
    case 'AWAITING_FIRST_PAYER_PAYMENT':
    case 'AWAITING_SECOND_PAYER_CONFIRMATION':
    case 'AWAITING_SECOND_PAYER_PAYMENT':
    case 'AWAITING_FIRST_PAYER_CONFIRMATION':
      return t('offers.transactionStatusInProgress');
    case 'COMPLETED':
      return t('offers.transactionStatusCompleted');
    case 'CANCELLED':
      return t('offers.transactionStatusCancelled');
    case 'DISPUTED':
      return 'Disputed';
    default:
      return null;
  }
});

const transactionStatusTagType = computed<'success' | 'warning' | 'info' | 'error' | 'default'>(() => {
  const status = transactionStatusDisplay.value;
  switch (status) {
    case 'Completed':
      return 'success';
    case 'In Progress':
      return 'info';
    case 'Negotiating':
      return 'warning';
    case 'Cancelled':
    case 'Disputed':
      return 'error';
    default:
      return 'default';
  }
});

const showInterest = async (event?: Event) => {
  if (event) {
    event.stopPropagation(); // Prevent event bubbling
  }
  
  console.log(`[OfferCard] showInterest CALLED. Current state for offer ${props.offer?.id}:`, {
    isLoading: isLoading.value,
    hasCurrentUserShownInterest: hasCurrentUserShownInterest.value,
    canShowInterest: canShowInterest.value
  });

  if (isLoading.value || hasCurrentUserShownInterest.value) {
    console.log(`[OfferCard] showInterest: GUARD TRIGGERED for offer ${props.offer?.id} (isLoading: ${isLoading.value}, hasInterest: ${hasCurrentUserShownInterest.value}). Returning early.`);
    return; 
  }

  isLoading.value = true
  try {
    console.log(`[OfferCard] Calling offerService.showInterest for offer ${props.offer.id}...`);
    await offerService.showInterest(props.offer.id)
    console.log(`[OfferCard] offerService.showInterest SUCCEEDED for offer ${props.offer.id}.`);
    message.success('Interest shown successfully!')
  } catch (error: any) {
    const errorMessage = error?.response?.data?.message || error?.message || 'Failed to show interest'
    console.error(`[OfferCard] offerService.showInterest FAILED for offer ${props.offer.id}:`, errorMessage);
    message.error(errorMessage)
  } finally {
    isLoading.value = false
    console.log(`[OfferCard] showInterest FINALLY block for offer ${props.offer?.id}. isLoading set to false.`);
    console.log(`[OfferCard] State AFTER finally for offer ${props.offer?.id}: hasCurrentUserShownInterest: ${hasCurrentUserShownInterest.value}, canShowInterest: ${canShowInterest.value}`);
  }
}
</script>

<template>
  <NCard 
    class="offer-card" 
    :bordered="true" 
    :content-style="{ paddingTop: '10px', paddingBottom: '12px', paddingLeft: '16px', paddingRight: '16px' }"
    :header-style="{ paddingTop: '10px', paddingBottom: '8px', paddingLeft: '16px', paddingRight: '16px' }"
  >    <template #header>
      <NText strong style="font-size: 1em;">
        {{ offer.type === 'BUY' ? t('offers.buying') : t('offers.selling') }} {{ Number(offer.amount).toLocaleString() }} CAD
      </NText>
    </template>

    <NDescriptions 
      label-placement="top" 
      :column="1" 
      size="small" 
      class="offer-meta-details"
      :label-style="{ fontSize: '0.85em', paddingBottom: '2px' }"
      :content-style="{ fontSize: '0.9em', paddingBottom: '6px' }"
    >      <NDescriptionsItem :label="t('offers.creator')">
        {{ offer.offerCreatorUsername || t('offers.unknownUser') }}
        <NTag round size="small" :type="offerCreatorRepTagType" style="margin-left: 6px; transform: translateY(-1px);">
          {{ t('offers.level') }} {{ offer.offerCreatorReputationLevel === null || offer.offerCreatorReputationLevel === undefined ? 'N/A' : offer.offerCreatorReputationLevel }}
        </NTag>
        <NTag 
          v-if="transactionStatusDisplay" 
          round 
          size="small" 
          :type="transactionStatusTagType" 
          style="margin-left: 6px; transform: translateY(-1px);"
        >
          {{ transactionStatusDisplay }}
        </NTag>
      </NDescriptionsItem>
      <NDescriptionsItem :label="t('offers.baseRate')">
        <NStatistic :value="Number(offer.baseRate)" :precision="2">
          <template #suffix> IRR/CAD</template>
        </NStatistic>
      </NDescriptionsItem>
      <NDescriptionsItem :label="t('offers.yourApplicableRate')">
        <NStatistic :value="Number(offer.calculatedApplicableRate)" :precision="2">
          <template #suffix> IRR/CAD</template>
        </NStatistic>
      </NDescriptionsItem>
    </NDescriptions>    <div v-if="!isOwnOffer && (isBonus || isPenalty)">
      <NAlert 
        :title="isBonus ? t('offers.rateAdvantage') : t('offers.rateAdjustment')" 
        :type="rateEffectType" 
        :closable="false" 
        style="margin-bottom: 12px; padding: 8px 12px;"
        :title-style="{ fontSize: '0.9em' }"
        :content-style="{ fontSize: '0.85em' }"
      >
        {{ rateEffectText }}
      </NAlert>
    </div><div v-if="!isOwnOffer" class="interest-section">
      <!-- Show different buttons/messages based on transaction status -->
      <div v-if="isTransactionCompleted">
        <NButton
          type="success"
          disabled
          block
          size="medium"        >
          {{ t('offers.transactionCompleted') }}
        </NButton>
        <NText type="success" class="action-message">
          {{ t('offers.transactionCompletedMessage') }}
        </NText>
      </div>
      
      <div v-else-if="isTransactionInProgress || isTransactionNegotiating">
        <NButton
          v-if="hasChatSession"
          type="info"
          @click="openChat"
          block
          size="medium"
        >
          {{ t('offers.continueTransaction') }}
        </NButton>
        <NText type="info" class="action-message">
          {{ isTransactionNegotiating ? t('offers.transactionNegotiating') : t('offers.transactionInProgress') }}
        </NText>
      </div>
      
      <!-- Show Chat button if interest is accepted and has chat session but no active transaction -->
      <div v-else-if="isInterestAccepted && hasChatSession">
        <NButton
          type="success"
          @click="openChat"
          block
          size="medium"
        >
          {{ t('offers.chatNow') }}
        </NButton>
        <NText type="success" class="action-message">
          {{ t('offers.interestAcceptedMessage') }}
        </NText>
      </div>
      
      <!-- Show regular interest button if not accepted -->
      <div v-else>
        <NButton
          :disabled="!canShowInterest"
          :loading="isLoading"
          type="primary"
          @click="showInterest"
          block
          size="medium"
        >
          {{ hasCurrentUserShownInterest ? t('offers.interestShown') : (isLoading ? t('offers.showingInterest') : t('offers.showInterest')) }}
        </NButton>

        <NText v-if="!authStore.user?.phoneVerified && !isOwnOffer && !hasCurrentUserShownInterest && !isLoading && !canShowInterest" type="error" class="action-message">
          {{ t('profile.verifyPhoneToShowInterest') }}
        </NText>
        <NText v-else-if="hasCurrentUserShownInterest && !isInterestAccepted && !isLoading" type="success" class="action-message">
          {{ t('offers.interestPendingMessage') }}
        </NText>
        <NText v-else-if="currentUserInterestStatus === 'DECLINED'" type="error" class="action-message">
          {{ t('offers.interestDeclinedMessage') }}
        </NText>
      </div>
    </div>
    <div v-else-if="isOwnOffer" class="own-offer-indicator">
      <NTag type="info" round size="small">{{ t('offers.ownOfferIndicator') }}</NTag>
    </div>
  </NCard>
</template>

<style scoped>
.offer-card {
  margin-bottom: 12px; 
  transition: box-shadow 0.3s ease-in-out;
}
.offer-card:hover {
  box-shadow: 0 3px 10px rgba(0,0,0,0.08); 
}

.offer-meta-details {
  margin-bottom: 10px; 
}

.interest-section {
  margin-top: 12px; 
  display: flex;
  flex-direction: column;
  gap: 8px; 
}

.action-message {
  font-size: 0.8em; 
  text-align: center;
  padding: 2px 0; 
}

.own-offer-indicator {
  margin-top: 10px; 
  text-align: center;
}


:deep(.n-statistic-value__content) {
  font-size: 1.1em; 
  font-weight: 600;
}
:deep(.n-descriptions-item-label) {
  font-weight: 500;
  font-size: 0.9em; 
}
:deep(.n-card-header__main) {
 font-weight: 600; 
}
</style>
