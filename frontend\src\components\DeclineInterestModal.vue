<template>
  <n-modal
    v-model:show="myOffersStore.showDeclineModal"
    preset="dialog"
    title="Decline Interest Request"
    positive-text="Confirm Decline"
    negative-text="Cancel"
    @positive-click="confirmDecline"
    @negative-click="cancelDecline"
  >
    <n-space vertical style="padding-top: 10px;">
      <p v-if="myOffersStore.interestToDecline">
        Declining interest from user: <strong>{{ myOffersStore.interestToDecline.username }}</strong>
        for offer: {{ myOffersStore.interestToDecline.offerType }} {{ myOffersStore.interestToDecline.offerAmount }} CAD.
      </p>
      <p>Please provide a reason for declining this interest request (optional):</p>
      <n-select
        v-model:value="myOffersStore.declineReasonCode"
        placeholder="Select a reason (optional)"
        :options="declineReasonOptions"
        clearable
      />
      <n-input
        v-if="myOffersStore.declineReasonCode === 'OTHER'"
        v-model:value="otherReasonText"
        type="textarea"
        placeholder="Please specify your reason"
        :autosize="{
          minRows: 2,
          maxRows: 5
        }"
      />
    </n-space>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useMyOffersStore } from '@/stores/myOffersStore';
import { useMessage } from 'naive-ui';
import { NModal, NSpace, NSelect, NInput } from 'naive-ui';

const myOffersStore = useMyOffersStore();
const message = useMessage();
const otherReasonText = ref<string>('');

const declineReasonOptions = [
  { label: 'Offer terms not suitable', value: 'TERMS_NOT_SUITABLE' },
  { label: 'User reputation too low', value: 'REPUTATION_TOO_LOW' },
  { label: 'Already found a match', value: 'ALREADY_MATCHED' },
  { label: 'Offer no longer available', value: 'OFFER_UNAVAILABLE' },
  { label: 'Other (specify below)', value: 'OTHER' },
];

async function confirmDecline() {
  if (!myOffersStore.interestToDecline || !myOffersStore.interestToDecline.id) {
    message.error('No interest selected for decline.');
    return;
  }

  let reasonToSend = myOffersStore.declineReasonCode; 

  if (myOffersStore.declineReasonCode === 'OTHER') {
    reasonToSend = otherReasonText.value.trim() || 'OTHER_UNSPECIFIED';
    if (!otherReasonText.value.trim()) {
      message.warning('Please specify your reason when selecting "Other".');
      return;
    }
  }

  try {
    await myOffersStore.declineInterest(myOffersStore.interestToDecline.id, reasonToSend);
    resetModal();
  } catch (error: any) {
    message.error(error.message || 'Failed to decline interest.');
  }
}

function cancelDecline() {
  resetModal();
}

function resetModal() {
  myOffersStore.showDeclineModal = false;
  otherReasonText.value = '';
  myOffersStore.declineReasonCode = undefined;
  myOffersStore.interestToDecline = null;
}
</script>