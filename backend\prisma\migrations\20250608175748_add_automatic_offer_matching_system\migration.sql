/*
  Warnings:

  - You are about to drop the column `displayName` on the `tags` table. All the data in the column will be lost.
  - Added the required column `display_name` to the `tags` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "MatchStatus" AS ENUM ('PENDING', 'BOTH_ACCEPTED', 'PARTIAL_ACCEPT', 'DECLINED', 'EXPIRED', 'CONVERTED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "MatchResponse" AS ENUM ('ACCEPTED', 'DECLINED', 'IGNORED');

-- Handle tags table column rename safely
ALTER TABLE "tags" RENAME COLUMN "displayName" TO "display_name";

-- CreateTable
CREATE TABLE "offer_matches" (
    "id" TEXT NOT NULL,
    "matchId" TEXT NOT NULL,
    "offer_a_id" TEXT NOT NULL,
    "offer_b_id" TEXT NOT NULL,
    "user_a_id" TEXT NOT NULL,
    "user_b_id" TEXT NOT NULL,
    "compatibilityScore" DECIMAL(4,3) NOT NULL DEFAULT 0.000,
    "match_criteria" JSONB,
    "currency_a" VARCHAR(3) NOT NULL,
    "currency_b" VARCHAR(3) NOT NULL,
    "amount_a" DECIMAL(15,2) NOT NULL,
    "amount_b" DECIMAL(15,2) NOT NULL,
    "rate_a_to_b" DECIMAL(10,6) NOT NULL,
    "rate_b_to_a" DECIMAL(10,6) NOT NULL,
    "status" "MatchStatus" NOT NULL DEFAULT 'PENDING',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "expires_at" TIMESTAMP(3) NOT NULL,
    "user_a_response" "MatchResponse",
    "user_a_responded_at" TIMESTAMP(3),
    "user_b_response" "MatchResponse",
    "user_b_responded_at" TIMESTAMP(3),
    "chat_session_id" TEXT,
    "transaction_id" TEXT,
    "declined_by_user_id" TEXT,
    "decline_reason" VARCHAR(100),
    "accepted_by_user_id" TEXT,
    "processing_lock" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "offer_matches_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "match_configurations" (
    "id" TEXT NOT NULL,
    "config_key" VARCHAR(50) NOT NULL,
    "config_value" JSONB NOT NULL,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "match_configurations_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "offer_matches_matchId_key" ON "offer_matches"("matchId");

-- CreateIndex
CREATE UNIQUE INDEX "offer_matches_chat_session_id_key" ON "offer_matches"("chat_session_id");

-- CreateIndex
CREATE UNIQUE INDEX "offer_matches_transaction_id_key" ON "offer_matches"("transaction_id");

-- CreateIndex
CREATE INDEX "offer_matches_user_a_id_idx" ON "offer_matches"("user_a_id");

-- CreateIndex
CREATE INDEX "offer_matches_user_b_id_idx" ON "offer_matches"("user_b_id");

-- CreateIndex
CREATE INDEX "offer_matches_offer_a_id_idx" ON "offer_matches"("offer_a_id");

-- CreateIndex
CREATE INDEX "offer_matches_offer_b_id_idx" ON "offer_matches"("offer_b_id");

-- CreateIndex
CREATE INDEX "offer_matches_status_idx" ON "offer_matches"("status");

-- CreateIndex
CREATE INDEX "offer_matches_created_at_idx" ON "offer_matches"("created_at");

-- CreateIndex
CREATE INDEX "offer_matches_expires_at_idx" ON "offer_matches"("expires_at");

-- CreateIndex
CREATE INDEX "offer_matches_currency_a_currency_b_idx" ON "offer_matches"("currency_a", "currency_b");

-- CreateIndex
CREATE UNIQUE INDEX "offer_matches_offer_a_id_offer_b_id_key" ON "offer_matches"("offer_a_id", "offer_b_id");

-- CreateIndex
CREATE UNIQUE INDEX "match_configurations_config_key_key" ON "match_configurations"("config_key");

-- CreateIndex
CREATE INDEX "match_configurations_config_key_idx" ON "match_configurations"("config_key");

-- CreateIndex
CREATE INDEX "match_configurations_is_active_idx" ON "match_configurations"("is_active");

-- AddForeignKey
ALTER TABLE "offer_matches" ADD CONSTRAINT "offer_matches_offer_a_id_fkey" FOREIGN KEY ("offer_a_id") REFERENCES "Offer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "offer_matches" ADD CONSTRAINT "offer_matches_offer_b_id_fkey" FOREIGN KEY ("offer_b_id") REFERENCES "Offer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "offer_matches" ADD CONSTRAINT "offer_matches_user_a_id_fkey" FOREIGN KEY ("user_a_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "offer_matches" ADD CONSTRAINT "offer_matches_user_b_id_fkey" FOREIGN KEY ("user_b_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "offer_matches" ADD CONSTRAINT "offer_matches_chat_session_id_fkey" FOREIGN KEY ("chat_session_id") REFERENCES "ChatSession"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "offer_matches" ADD CONSTRAINT "offer_matches_transaction_id_fkey" FOREIGN KEY ("transaction_id") REFERENCES "Transaction"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "offer_matches" ADD CONSTRAINT "offer_matches_declined_by_user_id_fkey" FOREIGN KEY ("declined_by_user_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "offer_matches" ADD CONSTRAINT "offer_matches_accepted_by_user_id_fkey" FOREIGN KEY ("accepted_by_user_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
