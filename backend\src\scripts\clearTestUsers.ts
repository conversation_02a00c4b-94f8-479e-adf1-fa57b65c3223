import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting to clear test users...');

  const testEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

  for (const email of testEmails) {
    try {
      // First, get the user ID to delete related records
      const user = await prisma.user.findUnique({
        where: { email },
        select: { id: true }
      });

      if (user) {
        // Delete related records first (if any)
        await prisma.paymentReceivingInfo.deleteMany({
          where: { userId: user.id }
        });

        await prisma.offer.deleteMany({
          where: { userId: user.id }
        });

        // Delete chat sessions where user is a participant
        await prisma.chatMessage.deleteMany({
          where: { senderId: user.id }
        });

        const chatSessions = await prisma.chatSession.findMany({
          where: {
            OR: [
              { userOneId: user.id },
              { userTwoId: user.id }
            ]
          }
        });

        for (const session of chatSessions) {
          await prisma.chatMessage.deleteMany({
            where: { chatSessionId: session.id }
          });
        }

        await prisma.chatSession.deleteMany({
          where: {
            OR: [
              { userOneId: user.id },
              { userTwoId: user.id }
            ]
          }
        });

        // Delete transactions
        await prisma.transaction.deleteMany({
          where: {
            OR: [
              { currencyAProviderId: user.id },
              { currencyBProviderId: user.id }
            ]
          }
        });

        // Finally, delete the user
        await prisma.user.delete({
          where: { email }
        });

        console.log(`Successfully deleted user: ${email}`);
      } else {
        console.log(`User ${email} not found, skipping...`);
      }
    } catch (error) {
      console.error(`Failed to delete user ${email}:`, error);
    }
  }

  console.log('Test user cleanup process complete.');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('Cleanup script failed:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
