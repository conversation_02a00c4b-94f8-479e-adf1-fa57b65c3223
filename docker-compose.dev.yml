services:
  # PostgreSQL Database for Local Development
  postgres-dev:
    image: postgres:15-alpine
    container_name: munygo-postgres-dev
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-munygo_dev}
      POSTGRES_USER: ${POSTGRES_USER:-munygo_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-munygo_password}
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"  # Use port 5433 to avoid conflict with production
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U munygo_user -d munygo_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_dev_data:
