import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import * as nodemailer from 'nodemailer';
import * as emailService from '../services/email';

// Mock nodemailer
vi.mock('nodemailer', async () => {
  const actual = await vi.importActual<typeof nodemailer>('nodemailer');
  // Keep track of the mock sendMail function
  const mockSendMail = vi.fn(async (opts) => ({
    messageId: 'test-message-id',
    ...opts, // Return parts of opts to allow checking 'to' field in results
  }));
  const mockCreateTransport = vi.fn(() => ({
    sendMail: mockSendMail,
  }));
  return {
    ...actual,
    createTransport: mockCreateTransport,
    getTestMessageUrl: vi.fn(() => 'http://ethereal.test/preview'),
    createTestAccount: vi.fn(async () => ({
      user: 'testuser',
      pass: 'testpass',
      smtp: { host: 'smtp.ethereal.email', port: 587, secure: false },
      web: 'https://ethereal.email'
    })),
    // Expose mocks for inspection if needed, though direct calls are better
    _mockSendMail: mockSendMail,
    _mockCreateTransport: mockCreateTransport,
  };
});

// Store original NODE_ENV
const originalNodeEnv = process.env.NODE_ENV;
const originalFrontendUrl = process.env.FRONTEND_URL;

describe('email service', () => {
  beforeEach(async () => {
    // Reset mocks and environment before each test
    vi.clearAllMocks();
    
    // Clean up environment variables to ensure clean state
    delete process.env.NODE_ENV;
    delete process.env.FRONTEND_URL;
    delete process.env.SMTP_HOST;
    delete process.env.SMTP_PORT;
    delete process.env.SMTP_USER;
    delete process.env.SMTP_PASS;
    
    // Set default test environment
    process.env.NODE_ENV = 'test';
    
    // Initialize transporter with current env settings
    await emailService.initializeEmailTransporter();
  });

  afterEach(() => {
    // Restore original environment variables after each test
    process.env.NODE_ENV = originalNodeEnv;
    process.env.FRONTEND_URL = originalFrontendUrl;
    
    // Clean up any production env vars
    delete process.env.SMTP_HOST;
    delete process.env.SMTP_PORT;
    delete process.env.SMTP_USER;
    delete process.env.SMTP_PASS;
  });

  describe('sendVerificationEmail - Basic Functionality', () => {
    it('sends verification email with correct parameters', async () => {
      const email = '<EMAIL>';
      const token = 'sometoken';
      await emailService.sendVerificationEmail(email, token);

      // Get the arguments passed to the mocked sendMail
      const sendMailMock = (nodemailer as any)._mockSendMail;
      expect(sendMailMock).toHaveBeenCalledTimes(1);
      const mailOptions = sendMailMock.mock.calls[0][0];

      expect(mailOptions.to).toBe(email);
      expect(mailOptions.subject).toBe('Verify your email address');
      expect(mailOptions.text).toContain(token);
      expect(mailOptions.html).toContain(token);
      expect(mailOptions.from).toBe('"MUNygo" <<EMAIL>>');
    });

    it('uses the default verification URL when FRONTEND_URL is not set', async () => {
      const email = '<EMAIL>';
      const token = 'othertoken';
      const defaultFrontendUrl = 'http://localhost:5173'; // Default from email.ts
      await emailService.sendVerificationEmail(email, token);

      const sendMailMock = (nodemailer as any)._mockSendMail;
      const mailOptions = sendMailMock.mock.calls[0][0];
      const expectedUrl = `${defaultFrontendUrl}/verify-email?token=${token}`;

      expect(mailOptions.text).toContain(expectedUrl);
      expect(mailOptions.html).toContain(`href="${expectedUrl}"`);
    });

    it('uses the FRONTEND_URL environment variable for verification URL when set', async () => {
      const email = '<EMAIL>';
      const token = 'customtoken';
      const customFrontendUrl = 'https://munygo.app';
      process.env.FRONTEND_URL = customFrontendUrl; // Set custom URL for this test

      await emailService.sendVerificationEmail(email, token);

      const sendMailMock = (nodemailer as any)._mockSendMail;
      const mailOptions = sendMailMock.mock.calls[0][0];
      const expectedUrl = `${customFrontendUrl}/verify-email?token=${token}`;

      expect(mailOptions.text).toContain(expectedUrl);
      expect(mailOptions.html).toContain(`href="${expectedUrl}"`);
    });

    it('returns message info with correct structure', async () => {
      const email = '<EMAIL>';
      const token = 'testtoken';
      
      const info = await emailService.sendVerificationEmail(email, token);
      
      expect(info).toBeDefined();
      expect(info.messageId).toBe('test-message-id');
      expect((info as any).to).toBe(email); // Cast because mock returns opts
    });
  });

  describe('sendVerificationEmail - Content Validation', () => {
    it('should generate proper HTML email structure', async () => {
      const email = '<EMAIL>';
      const token = 'htmltoken';
      
      await emailService.sendVerificationEmail(email, token);
      
      const sendMailMock = (nodemailer as any)._mockSendMail;
      const mailOptions = sendMailMock.mock.calls[0][0];
      
      expect(mailOptions.html).toContain('<h1>Welcome to MUNygo!</h1>');
      expect(mailOptions.html).toContain('style="display: inline-block');
      expect(mailOptions.html).toContain('background: #4CAF50');
      expect(mailOptions.html).toContain('This link will expire in 24 hours');
    });

    it('should properly handle special characters in email and token', async () => {
      const email = '<EMAIL>';
      const token = 'token_with-special.chars123';
      
      await emailService.sendVerificationEmail(email, token);
      
      const sendMailMock = (nodemailer as any)._mockSendMail;
      const mailOptions = sendMailMock.mock.calls[0][0];
      
      expect(mailOptions.to).toBe(email);
      expect(mailOptions.text).toContain(token);
      expect(mailOptions.html).toContain(token);
    });

    it('should handle very long tokens', async () => {
      const email = '<EMAIL>';
      const token = 'a'.repeat(500); // Very long token
      
      await emailService.sendVerificationEmail(email, token);
      
      const sendMailMock = (nodemailer as any)._mockSendMail;
      const mailOptions = sendMailMock.mock.calls[0][0];
      
      expect(mailOptions.text).toContain(token);
      expect(mailOptions.html).toContain(token);
    });
  });

  describe('sendVerificationEmail - Environment Behavior', () => {
    it('logs preview URL in development environment', async () => {
      process.env.NODE_ENV = 'development';
      await emailService.initializeEmailTransporter(); 

      const spy = vi.spyOn(console, 'log');
      await emailService.sendVerificationEmail('<EMAIL>', 'devtoken');

      expect(nodemailer.getTestMessageUrl).toHaveBeenCalled();
      expect(spy).toHaveBeenCalledWith('Preview URL:', 'http://ethereal.test/preview');
      spy.mockRestore();
    });

    it('does NOT log preview URL in production environment', async () => {
      process.env.NODE_ENV = 'production';
      process.env.SMTP_HOST = 'smtp.prod.com';
      process.env.SMTP_PORT = '465';
      process.env.SMTP_USER = 'produser';
      process.env.SMTP_PASS = 'prodpass';

      await emailService.initializeEmailTransporter();

      const spy = vi.spyOn(console, 'log');
      await emailService.sendVerificationEmail('<EMAIL>', 'prodtoken');

      expect(nodemailer.getTestMessageUrl).not.toHaveBeenCalled();
      expect(spy).not.toHaveBeenCalledWith(expect.stringContaining('Preview URL:'), expect.anything());

      spy.mockRestore();
    });
  });

  describe('sendVerificationEmail - Error Handling', () => {
    it('should handle sendMail failures gracefully', async () => {
      const sendMailMock = (nodemailer as any)._mockSendMail;
      const mockError = new Error('SMTP connection failed');
      sendMailMock.mockRejectedValueOnce(mockError);

      await expect(
        emailService.sendVerificationEmail('<EMAIL>', 'errortoken')
      ).rejects.toThrow('SMTP connection failed');
    });

    it('should handle network timeouts', async () => {
      const sendMailMock = (nodemailer as any)._mockSendMail;
      const timeoutError = new Error('Network timeout');
      timeoutError.name = 'ETIMEDOUT';
      sendMailMock.mockRejectedValueOnce(timeoutError);

      await expect(
        emailService.sendVerificationEmail('<EMAIL>', 'timeouttoken')
      ).rejects.toThrow('Network timeout');
    });

    it('should handle authentication failures', async () => {
      const sendMailMock = (nodemailer as any)._mockSendMail;
      const authError = new Error('Invalid credentials');
      authError.name = 'EAUTH';
      sendMailMock.mockRejectedValueOnce(authError);

      await expect(
        emailService.sendVerificationEmail('<EMAIL>', 'authtoken')
      ).rejects.toThrow('Invalid credentials');
    });
  });

  describe('initializeEmailTransporter - Basic Functionality', () => {
    it('should use ethereal settings in development', async () => {
      vi.clearAllMocks(); // Clear mocks from beforeEach
      process.env.NODE_ENV = 'development';
      
      await emailService.initializeEmailTransporter();
      
      const createTransportMock = (nodemailer as any)._mockCreateTransport;
      expect(createTransportMock).toHaveBeenCalledTimes(1);
      expect(createTransportMock).toHaveBeenCalledWith(expect.objectContaining({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: expect.objectContaining({
          user: 'testuser',
          pass: 'testpass'
        })
      }));
    });

    it('should use production settings in production', async () => {
      vi.clearAllMocks();
      process.env.NODE_ENV = 'production';
      process.env.SMTP_HOST = 'smtp.prod.com';
      process.env.SMTP_PORT = '465';
      process.env.SMTP_USER = 'produser';
      process.env.SMTP_PASS = 'prodpass';

      await emailService.initializeEmailTransporter();
      
      const createTransportMock = (nodemailer as any)._mockCreateTransport;
      expect(createTransportMock).toHaveBeenCalledTimes(1);
      expect(createTransportMock).toHaveBeenCalledWith(expect.objectContaining({
        host: 'smtp.prod.com',
        port: 465,
        secure: false, 
        auth: { user: 'produser', pass: 'prodpass' },
      }));
    });

    it('should allow reinitialization of transporter', async () => {
      vi.clearAllMocks();
      
      process.env.NODE_ENV = 'development';
      await emailService.initializeEmailTransporter();
      
      process.env.NODE_ENV = 'production';
      process.env.SMTP_HOST = 'smtp.new.com';
      process.env.SMTP_PORT = '587';
      process.env.SMTP_USER = 'newuser';
      process.env.SMTP_PASS = 'newpass';
      
      await emailService.initializeEmailTransporter();
      
      const createTransportMock = (nodemailer as any)._mockCreateTransport;
      expect(createTransportMock).toHaveBeenCalledTimes(2);
      
      expect(createTransportMock.mock.calls[1][0]).toEqual(expect.objectContaining({
        host: 'smtp.new.com',
        port: 587,
        secure: false,
        auth: { user: 'newuser', pass: 'newpass' }
      }));
    });
  });

  describe('initializeEmailTransporter - Edge Cases', () => {
    it('should handle missing SMTP environment variables in production', async () => {
      vi.clearAllMocks();
      process.env.NODE_ENV = 'production';
      
      await emailService.initializeEmailTransporter();
      
      const createTransportMock = (nodemailer as any)._mockCreateTransport;
      expect(createTransportMock).toHaveBeenCalledWith(expect.objectContaining({
        host: undefined,
        port: NaN,
        secure: false,
        auth: { user: undefined, pass: undefined }
      }));
    });

    it('should handle invalid SMTP_PORT values', async () => {
      vi.clearAllMocks();
      process.env.NODE_ENV = 'production';
      process.env.SMTP_HOST = 'smtp.test.com';
      process.env.SMTP_PORT = 'invalid-port';
      process.env.SMTP_USER = 'user';
      process.env.SMTP_PASS = 'pass';
      
      await emailService.initializeEmailTransporter();
      
      const createTransportMock = (nodemailer as any)._mockCreateTransport;
      expect(createTransportMock).toHaveBeenCalledWith(expect.objectContaining({
        host: 'smtp.test.com',
        port: NaN,
        secure: false,
        auth: { user: 'user', pass: 'pass' }
      }));
    });

    it('should handle createTestAccount failures in development', async () => {
      vi.clearAllMocks();
      process.env.NODE_ENV = 'development';
      
      const createTestAccountMock = nodemailer.createTestAccount as vi.Mock;
      const mockError = new Error('Failed to create test account');
      createTestAccountMock.mockRejectedValueOnce(mockError);
      
      await expect(emailService.initializeEmailTransporter()).rejects.toThrow('Failed to create test account');
    });

    it('should handle createTransport failures', async () => {
      vi.clearAllMocks();
      process.env.NODE_ENV = 'development';
      
      const createTransportMock = (nodemailer as any)._mockCreateTransport as vi.Mock;
      const mockError = new Error('Failed to create transport');
      createTransportMock.mockImplementationOnce(() => {
        throw mockError;
      });
      
      await expect(emailService.initializeEmailTransporter()).rejects.toThrow('Failed to create transport');
    });
  });

  describe('initializeEmailTransporter - Production Secure Flag Behavior', () => {
    beforeEach(() => {
      // Clear mocks from the main beforeEach and set production env
      vi.clearAllMocks();
      process.env.NODE_ENV = 'production';
      process.env.SMTP_USER = 'prodsecuser';
      process.env.SMTP_PASS = 'prodsecpass';
    });

    // afterEach is handled by the main afterEach to reset NODE_ENV and SMTP vars

    it('should set secure to false when SMTP_PORT is 587 in production, as per current implementation', async () => {
      process.env.SMTP_HOST = 'smtp.securetest.com';
      process.env.SMTP_PORT = '587';
      
      await emailService.initializeEmailTransporter();
      
      const createTransportMock = (nodemailer as any)._mockCreateTransport;
      expect(createTransportMock).toHaveBeenCalledWith(expect.objectContaining({
        host: 'smtp.securetest.com',
        port: 587,
        secure: false, // Current implementation hardcodes false
      }));
    });

    it('should set secure to false even when SMTP_PORT is 465 in production, as per current implementation', async () => {
      process.env.SMTP_HOST = 'smtp.securetest.com';
      process.env.SMTP_PORT = '465';
      
      await emailService.initializeEmailTransporter();
      
      const createTransportMock = (nodemailer as any)._mockCreateTransport;
      expect(createTransportMock).toHaveBeenCalledWith(expect.objectContaining({
        host: 'smtp.securetest.com',
        port: 465,
        secure: false, // Current implementation hardcodes false, despite comment suggesting true for 465
      }));
    });
  });

  describe('Email Service - Transporter Not Initialized', () => {
    let uninitializedEmailService: typeof emailService;

    beforeEach(async () => {
      vi.resetModules(); // Reset module cache to get a fresh module state
      // Nodemailer mock factory at the top level will be re-applied on import
      uninitializedEmailService = (await import('../services/email')) as typeof emailService;
      
      // Ensure a clean environment for this specific suite
      delete process.env.FRONTEND_URL;
      process.env.NODE_ENV = 'test'; // Keep NODE_ENV consistent for tests
      // Crucially, DO NOT call uninitializedEmailService.initializeEmailTransporter()
    });

    afterEach(() => {
      // Restore original environment variables that might have been changed by this suite
      // The main afterEach will also run, this is for specific changes in this suite's beforeEach
      process.env.NODE_ENV = originalNodeEnv;
      process.env.FRONTEND_URL = originalFrontendUrl;
    });

    it('should throw a TypeError if sendVerificationEmail is called before transporter is initialized', async () => {
      await expect(
        uninitializedEmailService.sendVerificationEmail('<EMAIL>', 'noinittoken')
      ).rejects.toThrow(TypeError); // Specifically a TypeError

      await expect(
        uninitializedEmailService.sendVerificationEmail('<EMAIL>', 'noinittoken')
      ).rejects.toThrowError(/Cannot read properties of undefined \(reading 'sendMail'\)/);
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle multiple concurrent email sends', async () => {
      const emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
      const tokens = ['token1', 'token2', 'token3'];
      
      // Ensure sendMail mock is clear for this test
      const sendMailMock = (nodemailer as any)._mockSendMail;
      sendMailMock.mockClear(); 

      const promises = emails.map((email, index) => 
        emailService.sendVerificationEmail(email, tokens[index])
      );
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(3);
      results.forEach((result, index) => {
        expect(result.messageId).toBe('test-message-id');
        expect((result as any).to).toBe(emails[index]);
      });
      
      expect(sendMailMock).toHaveBeenCalledTimes(3);
    });

    it('should handle partial failures in concurrent sends', async () => {
      const sendMailMock = (nodemailer as any)._mockSendMail;
      
      sendMailMock.mockClear(); // Clear previous calls
      sendMailMock
        .mockResolvedValueOnce({ messageId: 'success-1', to: '<EMAIL>' } as any)
        .mockRejectedValueOnce(new Error('SMTP failed'))
        .mockResolvedValueOnce({ messageId: 'success-3', to: '<EMAIL>' } as any);
      
      const promises = [
        emailService.sendVerificationEmail('<EMAIL>', 'token1'),
        emailService.sendVerificationEmail('<EMAIL>', 'token2'),
        emailService.sendVerificationEmail('<EMAIL>', 'token3')
      ];
      
      const results = await Promise.allSettled(promises);
      
      expect(results[0].status).toBe('fulfilled');
      if (results[0].status === 'fulfilled') {
        expect((results[0].value as any).to).toBe('<EMAIL>');
      }
      
      expect(results[1].status).toBe('rejected');
      if (results[1].status === 'rejected') {
        expect(results[1].reason.message).toBe('SMTP failed');
      }

      expect(results[2].status).toBe('fulfilled');
      if (results[2].status === 'fulfilled') {
         expect((results[2].value as any).to).toBe('<EMAIL>');
      }
      expect(sendMailMock).toHaveBeenCalledTimes(3);    });
  });
});
