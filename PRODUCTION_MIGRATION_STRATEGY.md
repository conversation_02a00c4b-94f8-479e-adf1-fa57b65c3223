# Production Database Migration Strategy for Git-based Deployments

## Current Workflow vs. Proper Migration Workflow

### ❌ What NOT to do in Production
```bash
# DON'T use this in production
docker-compose exec backend npx prisma db push --accept-data-loss
```
**Why?** `prisma db push` is for development only. It can cause data loss and doesn't track migration history.

### ✅ Proper Production Workflow

## 1. Development Phase (Windows)

When you make database changes locally:

```bash
# 1. Modify your schema.prisma file
# 2. Create a migration
npx prisma migrate dev --name "add_new_feature"
# 3. Commit BOTH schema.prisma AND migration files
git add .
git commit -m "Add new feature with database changes"
git push origin main
```

**Important:** Always commit both:
- `backend/prisma/schema.prisma` (your schema)
- `backend/prisma/migrations/` (your migration files)

## 2. Production Deployment (CentOS Host)

### Complete Deployment Script for CentOS Host

Create this script on your CentOS host machine:

```bash
#!/bin/bash
# File: deploy-with-migrations.sh

set -e  # Exit on any error

echo "🚀 Starting production deployment with database migrations..."

# 1. Pull latest changes
echo "📥 Pulling latest changes from git..."
git pull origin main

# 2. Check if there are new migrations
echo "🔍 Checking for new migrations..."
NEW_MIGRATIONS=$(docker-compose exec backend npx prisma migrate status --short 2>/dev/null | grep "following migration" || echo "")

if [ -n "$NEW_MIGRATIONS" ]; then
    echo "📋 New migrations detected:"
    echo "$NEW_MIGRATIONS"
    
    # 3. Apply migrations BEFORE rebuilding containers
    echo "🔄 Applying database migrations..."
    docker-compose exec backend npx prisma migrate deploy
    
    if [ $? -ne 0 ]; then
        echo "❌ Migration failed! Stopping deployment."
        exit 1
    fi
    
    echo "✅ Migrations applied successfully"
else
    echo "✅ No new migrations to apply"
fi

# 4. Rebuild and restart containers
echo "🔨 Rebuilding containers..."
docker-compose build

echo "🔄 Restarting containers..."
docker-compose up -d

# 5. Generate Prisma client (in case of schema changes)
echo "🔄 Regenerating Prisma client..."
docker-compose exec backend npx prisma generate

# 6. Health check
echo "🏥 Running health check..."
sleep 5
docker-compose exec backend node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.\$connect().then(() => {
    console.log('✅ Database connection successful');
    return prisma.\$disconnect();
}).catch(err => {
    console.error('❌ Database connection failed:', err.message);
    process.exit(1);
});
"

echo "🎉 Deployment completed successfully!"
```

## 3. Alternative: Migration-First Deployment

For even safer deployments, apply migrations BEFORE pulling code:

```bash
#!/bin/bash
# File: safe-deploy.sh

echo "🚀 Safe production deployment..."

# 1. Pull changes
git pull origin main

# 2. Apply migrations first (before container rebuild)
echo "🔄 Applying migrations..."
docker-compose exec backend npx prisma migrate deploy

# 3. Only rebuild if migrations succeeded
if [ $? -eq 0 ]; then
    echo "✅ Migrations successful, rebuilding containers..."
    docker-compose build
    docker-compose up -d
    docker-compose exec backend npx prisma generate
    echo "🎉 Deployment complete!"
else
    echo "❌ Migrations failed, keeping old containers running"
    exit 1
fi
```

## 4. Best Practices for Production Migrations

### A. Always use `prisma migrate deploy` in production
```bash
# ✅ Correct for production
docker-compose exec backend npx prisma migrate deploy

# ❌ Never use this in production
docker-compose exec backend npx prisma db push
```

### B. Check migration status before deployment
```bash
# Check what migrations will be applied
docker-compose exec backend npx prisma migrate status
```

### C. Backup database before major migrations
```bash
# Before applying migrations
docker-compose exec postgres pg_dump -U username dbname > backup_$(date +%Y%m%d_%H%M%S).sql
```

### D. Test migrations in staging first
1. Apply migrations to staging environment
2. Test thoroughly
3. Only then apply to production

## 5. Complete Production Deployment Workflow

### Your new workflow should be:

1. **Development (Windows):**
   ```bash
   # Make schema changes
   npx prisma migrate dev --name "descriptive_name"
   git add .
   git commit -m "Add feature X with database changes"
   git push origin main
   ```

2. **Production (CentOS):**
   ```bash
   # Run the deployment script
   ./deploy-with-migrations.sh
   ```

## 6. Troubleshooting Migration Issues

### If migrations fail in production:

```bash
# Check migration status
docker-compose exec backend npx prisma migrate status

# Reset migrations (DANGER: only if safe)
docker-compose exec backend npx prisma migrate reset --force

# Apply specific migration
docker-compose exec backend npx prisma migrate resolve --applied "migration_name"
```

### Rolling back migrations:
```bash
# Restore from backup
docker-compose exec postgres psql -U username -d dbname < backup_file.sql

# Rebuild containers with previous version
git checkout previous_commit
docker-compose build
docker-compose up -d
```

## 7. Environment Variables for Production

Make sure your production `.env` has:
```
DATABASE_URL="********************************************/dbname"
NODE_ENV=production
```

## Summary

**Key Changes to Your Workflow:**

1. **Always create migrations locally:** Use `npx prisma migrate dev`
2. **Commit migration files:** Include `prisma/migrations/` in git
3. **Use deployment script:** Apply migrations BEFORE container rebuild
4. **Use `migrate deploy`:** Never use `db push` in production
5. **Test in staging:** Always test migrations before production

This ensures your production database stays consistent and you have proper migration history tracking!
