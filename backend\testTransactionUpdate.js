// Simple test to trigger a transaction update and see the socket payload
const axios = require('axios');

async function testTransactionUpdate() {
  try {
    const response = await axios.post(
      'http://localhost:3000/api/transactions/cmb3yoxpc000zvljku0tznqal/confirm-receipt',
      {},
      {
        headers: {
          'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE'
        }
      }
    );
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Since we need a valid JWT token, let's just test by making an API call
// But we can also just trigger it manually through the UI
console.log('Please trigger the confirm receipt action through the UI to see the debug logs...');
