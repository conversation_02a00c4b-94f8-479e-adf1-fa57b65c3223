#!/bin/bash

# Force rebuild production environment without cache
# This ensures all environment variables and code changes are properly applied

echo "🚀 Starting forced rebuild of production environment (no cache)..."

# Stop and remove all containers, networks, and volumes
echo "📦 Stopping and removing existing containers..."
docker-compose down --volumes --remove-orphans

# Remove all related images to force complete rebuild
echo "🗑️ Removing existing images..."
docker-compose rm -f
docker rmi munygo-backend:latest munygo-frontend:latest 2>/dev/null || true

# Clear Docker build cache
echo "🧹 Clearing Docker build cache..."
docker builder prune -f

# Build and start with no cache
echo "🔨 Building and starting services (no cache)..."
docker-compose build --no-cache --pull
docker-compose up -d

# Show status
echo "📊 Checking service status..."
docker-compose ps

echo "✅ Forced rebuild complete!"
echo ""
echo "📋 Next steps:"
echo "1. Wait 30-60 seconds for services to fully start"
echo "2. Check logs: docker-compose logs -f"
echo "3. Test application at your configured URL"
echo "4. Check admin panel and debug report button visibility"
echo ""
echo "🔍 To verify environment variables in frontend:"
echo "   docker-compose exec frontend sh -c 'printenv | grep VITE'"
echo ""
echo "🔍 To check backend logs:"
echo "   docker-compose logs backend"
