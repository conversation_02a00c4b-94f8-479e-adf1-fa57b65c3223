<template>
  <div class="debug-dashboard">
    <!-- Custom Header -->
    <header class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="dashboard-title">Debug Reports Dashboard</h1>
          <p class="dashboard-subtitle">Manage and review client-side debug reports</p>
        </div>
        <div class="header-actions">
          <button 
            class="btn btn-primary" 
            @click="refreshReports" 
            :disabled="loading"
            :class="{ loading: loading }"
          >
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
            </svg>
            {{ loading ? 'Loading...' : 'Refresh' }}
          </button>
          <button class="btn btn-secondary" @click="exportAllReports" :disabled="!hasReports">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
            </svg>
            Export All
          </button>
          <button class="btn btn-ghost" @click="toggleTheme">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,1 12,2Z"/>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
      <!-- Filters Section -->
      <section class="filters-section" :class="{ collapsed: !showFilters }">
        <div class="filters-header" @click="toggleFilters">
          <h3 class="filters-title">Filters & Search</h3>
          <button class="btn btn-ghost btn-sm">
            <svg class="icon chevron" viewBox="0 0 24 24" :class="{ rotated: !showFilters }">
              <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
            </svg>
          </button>
        </div>
        
        <div class="filters-content" v-show="showFilters">
          <div class="filters-grid">
            <!-- Search Input -->
            <div class="filter-group">
              <label class="filter-label">Search Reports</label>
              <div class="search-input-container">
                <svg class="search-icon" viewBox="0 0 24 24">
                  <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
                <input 
                  v-model="searchTerm" 
                  type="text" 
                  class="search-input"
                  placeholder="Search by user ID, error type, or message..."
                  @input="debouncedSearch"
                />
                <button 
                  v-if="searchTerm" 
                  class="clear-search" 
                  @click="clearSearch"
                >
                  <svg viewBox="0 0 24 24">
                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Level Filter -->
            <div class="filter-group">
              <label class="filter-label">Log Level</label>
              <div class="custom-select">
                <select v-model="selectedLevel" @change="applyFilters">
                  <option value="">All Levels</option>
                  <option value="error">Error</option>
                  <option value="warn">Warning</option>
                  <option value="info">Info</option>
                  <option value="debug">Debug</option>
                </select>
                <svg class="select-arrow" viewBox="0 0 24 24">
                  <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
                </svg>
              </div>
            </div>

            <!-- Date Range Filter -->
            <div class="filter-group">
              <label class="filter-label">Date Range</label>
              <div class="date-range">
                <input 
                  v-model="dateRange.start" 
                  type="date" 
                  class="date-input"
                  @change="applyFilters"
                />
                <span class="date-separator">to</span>
                <input 
                  v-model="dateRange.end" 
                  type="date" 
                  class="date-input"
                  @change="applyFilters"
                />
              </div>
            </div>

            <!-- Page Size -->
            <div class="filter-group">
              <label class="filter-label">Page Size</label>
              <div class="custom-select">
                <select v-model="pageSize" @change="changePageSize">
                  <option :value="10">10 per page</option>
                  <option :value="25">25 per page</option>
                  <option :value="50">50 per page</option>
                  <option :value="100">100 per page</option>
                </select>
                <svg class="select-arrow" viewBox="0 0 24 24">
                  <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
                </svg>
              </div>
            </div>

            <!-- Clear All Filters -->
            <div class="filter-group">
              <button class="btn btn-ghost btn-full" @click="clearAllFilters">
                <svg class="icon" viewBox="0 0 24 24">
                  <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                </svg>
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Reports Statistics -->
      <section class="stats-section" v-if="hasReports">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">{{ totalReports }}</div>
            <div class="stat-label">Total Reports</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ errorCount }}</div>
            <div class="stat-label">Errors</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ warningCount }}</div>
            <div class="stat-label">Warnings</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ recentCount }}</div>
            <div class="stat-label">Last 24h</div>
          </div>
        </div>
      </section>

      <!-- Data Table -->
      <section class="table-section">
        <div class="table-header">
          <h3 class="table-title">Debug Reports</h3>
          <div class="table-actions">
            <span class="results-count" v-if="hasReports">
              Showing {{ startIndex }} - {{ endIndex }} of {{ totalReports }} reports
            </span>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading-state">
          <div class="spinner"></div>
          <p>Loading debug reports...</p>
        </div>

        <!-- Empty State -->
        <div v-else-if="!hasReports" class="empty-state">
          <svg class="empty-icon" viewBox="0 0 24 24">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M16,11H8V13H16V11M16,15H8V17H16V15Z"/>
          </svg>
          <h3>No Reports Found</h3>
          <p>No debug reports match your current filters.</p>
          <button class="btn btn-primary" @click="clearAllFilters">
            Clear Filters
          </button>
        </div>

        <!-- Data Table -->
        <div v-else class="table-container">
          <table class="custom-table">
            <thead>
              <tr>
                <th 
                  v-for="column in tableColumns" 
                  :key="column.key"
                  :class="{ sortable: column.sortable, active: sortBy === column.key }"
                  @click="column.sortable && sortTable(column.key)"
                >
                  <div class="th-content">
                    <span>{{ column.title }}</span>
                    <svg 
                      v-if="column.sortable" 
                      class="sort-icon" 
                      viewBox="0 0 24 24"
                      :class="{ 
                        desc: sortBy === column.key && sortOrder === 'desc',
                        asc: sortBy === column.key && sortOrder === 'asc'
                      }"
                    >
                      <path d="M3,13H15L10.5,7.5L6,13H3M6,17L10.5,22.5L15,17H6Z"/>
                    </svg>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr 
                v-for="report in paginatedReports" 
                :key="report.id"
                class="table-row"
                @click="openReportDetails(report)"
              >
                <td class="timestamp-cell">
                  <div class="timestamp">
                    {{ formatDateTime(report.timestamp) }}
                  </div>
                </td>
                <td class="level-cell">
                  <span class="level-badge" :class="`level-${report.level}`">
                    {{ report.level.toUpperCase() }}
                  </span>
                </td>
                <td class="user-cell">
                  <div class="user-info">
                    <span class="user-id">{{ report.userId || 'Anonymous' }}</span>
                  </div>
                </td>
                <td class="message-cell">
                  <div class="message-content">
                    <span class="message-text">{{ truncateMessage(report.message) }}</span>
                    <span v-if="report.message.length > 100" class="message-more">...</span>
                  </div>
                </td>
                <td class="url-cell">
                  <div class="url-content">
                    {{ report.url ? getUrlPath(report.url) : 'N/A' }}
                  </div>
                </td>
                <td class="actions-cell">
                  <div class="action-buttons">
                    <button 
                      class="btn btn-sm btn-ghost" 
                      @click.stop="openReportDetails(report)"
                    >
                      <svg class="icon" viewBox="0 0 24 24">
                        <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                      </svg>
                    </button>
                    <button 
                      class="btn btn-sm btn-ghost" 
                      @click.stop="downloadSingleReport(report)"
                    >
                      <svg class="icon" viewBox="0 0 24 24">
                        <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="hasReports && totalPages > 1" class="pagination-container">
          <div class="pagination">
            <button 
              class="page-btn" 
              :disabled="currentPage === 1"
              @click="goToPage(1)"
            >
              <svg class="icon" viewBox="0 0 24 24">
                <path d="M18.41,16.59L13.82,12L18.41,7.41L17,6L11,12L17,18L18.41,16.59M6,6H8V18H6V6Z"/>
              </svg>
            </button>
            <button 
              class="page-btn" 
              :disabled="currentPage === 1"
              @click="goToPage(currentPage - 1)"
            >
              <svg class="icon" viewBox="0 0 24 24">
                <path d="M15.41,16.59L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.59Z"/>
              </svg>
            </button>

            <div class="page-numbers">
              <button 
                v-for="page in visiblePages" 
                :key="page"
                class="page-btn page-number" 
                :class="{ active: page === currentPage }"
                @click="goToPage(page)"
              >
                {{ page }}
              </button>
            </div>

            <button 
              class="page-btn" 
              :disabled="currentPage === totalPages"
              @click="goToPage(currentPage + 1)"
            >
              <svg class="icon" viewBox="0 0 24 24">
                <path d="M8.59,16.59L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.59Z"/>
              </svg>
            </button>
            <button 
              class="page-btn" 
              :disabled="currentPage === totalPages"
              @click="goToPage(totalPages)"
            >
              <svg class="icon" viewBox="0 0 24 24">
                <path d="M5.59,7.41L10.18,12L5.59,16.59L7,18L13,12L7,6L5.59,7.41M16,6H18V18H16V6Z"/>
              </svg>
            </button>
          </div>
          <div class="pagination-info">
            Page {{ currentPage }} of {{ totalPages }}
          </div>
        </div>
      </section>
    </main>

    <!-- Custom Modal for Report Details -->
    <div v-if="selectedReport" class="modal-overlay" @click="closeModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">Debug Report Details</h2>
          <button class="modal-close" @click="closeModal">
            <svg viewBox="0 0 24 24">
              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
            </svg>
          </button>
        </div>
        
        <div class="modal-content">
          <div class="report-details">
            <div class="detail-section">
              <h4>Basic Information</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>Report ID</label>
                  <span>{{ selectedReport.id }}</span>
                </div>
                <div class="detail-item">
                  <label>Timestamp</label>
                  <span>{{ formatDateTime(selectedReport.timestamp) }}</span>
                </div>
                <div class="detail-item">
                  <label>Level</label>
                  <span class="level-badge" :class="`level-${selectedReport.level}`">
                    {{ selectedReport.level.toUpperCase() }}
                  </span>
                </div>
                <div class="detail-item">
                  <label>User ID</label>
                  <span>{{ selectedReport.userId || 'Anonymous' }}</span>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h4>Error Details</h4>
              <div class="detail-item full-width">
                <label>Message</label>
                <div class="code-block">{{ selectedReport.message }}</div>
              </div>
              <div class="detail-item full-width" v-if="selectedReport.stackTrace">
                <label>Stack Trace</label>
                <div class="code-block stack-trace">{{ selectedReport.stackTrace }}</div>
              </div>
            </div>

            <div class="detail-section">
              <h4>Context Information</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>URL</label>
                  <span>{{ selectedReport.url || 'N/A' }}</span>
                </div>
                <div class="detail-item">
                  <label>User Agent</label>
                  <span>{{ selectedReport.userAgent || 'N/A' }}</span>
                </div>
                <div class="detail-item full-width" v-if="selectedReport.additionalData">
                  <label>Additional Data</label>
                  <div class="code-block">{{ formatJSON(selectedReport.additionalData) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-primary" @click="downloadSingleReport(selectedReport)">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
            </svg>
            Download Report
          </button>
          <button class="btn btn-secondary" @click="closeModal">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAdminDebugStore } from '@/stores/adminDebugStore'
import { useThemeStore } from '@/stores/theme'
import type { DebugReport } from '@/types/admin'

const adminStore = useAdminDebugStore()

// Reactive data
const showFilters = ref(true)
const searchTerm = ref('')
const selectedLevel = ref('')
const dateRange = ref({
  start: '',
  end: ''
})
const pageSize = ref(25)
const currentPage = ref(1)
const sortBy = ref('timestamp')
const sortOrder = ref<'asc' | 'desc'>('desc')
const selectedReport = ref<DebugReport | null>(null)
const searchDebounceTimer = ref<number>(0)

// Table columns configuration
const tableColumns = [
  { key: 'timestamp', title: 'Timestamp', sortable: true },
  { key: 'level', title: 'Level', sortable: true },
  { key: 'userId', title: 'User ID', sortable: true },
  { key: 'message', title: 'Message', sortable: false },
  { key: 'url', title: 'URL', sortable: false },
  { key: 'actions', title: 'Actions', sortable: false }
]

// Computed properties
const loading = computed(() => adminStore.loading)
const reports = computed(() => adminStore.reports)
const hasReports = computed(() => reports.value.length > 0)
const totalReports = computed(() => adminStore.totalReports)

const filteredReports = computed(() => {
  let filtered = [...reports.value]

  // Apply search filter
  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(report => 
      report.message.toLowerCase().includes(search) ||
      (report.userId && report.userId.toLowerCase().includes(search)) ||
      (report.url && report.url.toLowerCase().includes(search))
    )
  }

  // Apply level filter
  if (selectedLevel.value) {
    filtered = filtered.filter(report => report.level === selectedLevel.value)
  }

  // Apply date range filter
  if (dateRange.value.start) {
    const startDate = new Date(dateRange.value.start)
    filtered = filtered.filter(report => new Date(report.timestamp) >= startDate)
  }
  if (dateRange.value.end) {
    const endDate = new Date(dateRange.value.end + 'T23:59:59')
    filtered = filtered.filter(report => new Date(report.timestamp) <= endDate)
  }

  // Apply sorting
  filtered.sort((a, b) => {
    let aVal: any = a[sortBy.value as keyof DebugReport]
    let bVal: any = b[sortBy.value as keyof DebugReport]

    if (sortBy.value === 'timestamp') {
      aVal = new Date(aVal).getTime()
      bVal = new Date(bVal).getTime()
    }

    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase()
      bVal = bVal.toLowerCase()
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1
    } else {
      return aVal < bVal ? 1 : -1
    }
  })

  return filtered
})

const paginatedReports = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredReports.value.slice(start, end)
})

const totalPages = computed(() => Math.ceil(filteredReports.value.length / pageSize.value))

const startIndex = computed(() => {
  if (!hasReports.value) return 0
  return (currentPage.value - 1) * pageSize.value + 1
})

const endIndex = computed(() => {
  if (!hasReports.value) return 0
  return Math.min(currentPage.value * pageSize.value, filteredReports.value.length)
})

const visiblePages = computed(() => {
  const pages = []
  const maxVisible = 5
  let start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)

  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  return pages
})

// Statistics
const errorCount = computed(() => reports.value.filter(r => r.level === 'error').length)
const warningCount = computed(() => reports.value.filter(r => r.level === 'warn').length)
const recentCount = computed(() => {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return reports.value.filter(r => new Date(r.timestamp) >= yesterday).length
})

// Methods
const toggleFilters = () => {
  showFilters.value = !showFilters.value
}

const toggleTheme = () => {
  const themeStore = useThemeStore()
  themeStore.toggleTheme()
}

const refreshReports = async () => {
  await adminStore.fetchReports({
    page: currentPage.value,
    limit: pageSize.value
  })
}

const debouncedSearch = () => {
  clearTimeout(searchDebounceTimer.value)
  searchDebounceTimer.value = window.setTimeout(() => {
    currentPage.value = 1
    applyFilters()
  }, 300)
}

const clearSearch = () => {
  searchTerm.value = ''
  applyFilters()
}

const applyFilters = () => {
  currentPage.value = 1
}

const clearAllFilters = () => {
  searchTerm.value = ''
  selectedLevel.value = ''
  dateRange.value = { start: '', end: '' }
  currentPage.value = 1
}

const changePageSize = () => {
  currentPage.value = 1
  adminStore.setLimit(pageSize.value)
}

const sortTable = (column: string) => {
  if (sortBy.value === column) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = column
    sortOrder.value = 'desc'
  }
}

const goToPage = (page: number) => {
  currentPage.value = page
  adminStore.setCurrentPage(page)
}

const openReportDetails = (report: DebugReport) => {
  selectedReport.value = report
}

const closeModal = () => {
  selectedReport.value = null
}

const downloadSingleReport = async (report: DebugReport) => {
  try {
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `debug-report-${report.id}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to download report:', error)
  }
}

const exportAllReports = async () => {
  try {
    await adminStore.downloadReport()
  } catch (error) {
    console.error('Failed to export reports:', error)
  }
}

// Utility functions
const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

const truncateMessage = (message: string, maxLength = 100) => {
  return message.length > maxLength ? message.substring(0, maxLength) : message
}

const getUrlPath = (url: string) => {
  try {
    return new URL(url).pathname
  } catch {
    return url
  }
}

const formatJSON = (data: any) => {
  try {
    return JSON.stringify(data, null, 2)
  } catch {
    return String(data)
  }
}

// Lifecycle
onMounted(() => {
  refreshReports()
})

// Watchers
watch([selectedLevel, dateRange], () => {
  applyFilters()
}, { deep: true })
</script>

<style scoped>
/* Reset and Base Styles */
.debug-dashboard {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* CSS Variables for Theming */
:root {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-elevated: #ffffff;
  --bg-hover: #f1f5f9;
  --border-color: #e2e8f0;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --accent-primary: #3b82f6;
  --accent-hover: #2563eb;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --radius: 8px;
  --radius-lg: 12px;
}

/* Dark Mode Variables */
:root.dark {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-elevated: #334155;
  --bg-hover: #475569;
  --border-color: #475569;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

/* Header Styles */
.dashboard-header {
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.title-section {
  flex: 1;
}

.dashboard-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
  line-height: 1.2;
}

.dashboard-subtitle {
  color: var(--text-secondary);
  margin: 0;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--accent-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--accent-hover);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-hover);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.btn-sm {
  padding: 0.5rem;
  font-size: 0.75rem;
}

.btn-full {
  width: 100%;
}

.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  width: 1rem;
  height: 1rem;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  color: white;
}

/* Icon Styles */
.icon {
  width: 1rem;
  height: 1rem;
  fill: currentColor;
  flex-shrink: 0;
}

.chevron {
  transition: transform 0.2s ease;
}

.chevron.rotated {
  transform: rotate(-180deg);
}

/* Main Content */
.dashboard-main {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Filters Section */
.filters-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: all 0.3s ease;
}

.filters-header {
  padding: 1.25rem 1.5rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: between;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.filters-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
  flex: 1;
}

.filters-content {
  padding: 1.5rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

/* Input Styles */
.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  width: 1.25rem;
  height: 1.25rem;
  color: var(--text-muted);
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.clear-search {
  position: absolute;
  right: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  background: none;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-search:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.clear-search svg {
  width: 1rem;
  height: 1rem;
}

/* Custom Select */
.custom-select {
  position: relative;
}

.custom-select select {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  appearance: none;
}

.custom-select select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.select-arrow {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  color: var(--text-muted);
  pointer-events: none;
}

/* Date Range */
.date-range {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.date-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.date-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.date-separator {
  color: var(--text-muted);
  font-size: 0.875rem;
  flex-shrink: 0;
}

/* Statistics Section */
.stats-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  text-align: center;
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Table Section */
.table-section {
  background: var(--bg-elevated);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.table-header {
  padding: 1.25rem 1.5rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.results-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Loading and Empty States */
.loading-state, .empty-state {
  padding: 4rem 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--border-color);
  border-top-color: var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.empty-icon {
  width: 4rem;
  height: 4rem;
  color: var(--text-muted);
  margin: 0 auto 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.empty-state p {
  margin: 0 0 1.5rem;
}

/* Custom Table */
.table-container {
  overflow-x: auto;
}

.custom-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.custom-table th {
  background: var(--bg-secondary);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  white-space: nowrap;
}

.custom-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.custom-table th.sortable:hover {
  background: var(--bg-hover);
}

.th-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sort-icon {
  width: 1rem;
  height: 1rem;
  color: var(--text-muted);
  transition: all 0.2s ease;
}

.sort-icon.asc {
  color: var(--accent-primary);
  transform: rotate(180deg);
}

.sort-icon.desc {
  color: var(--accent-primary);
}

.custom-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  vertical-align: top;
}

.table-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: var(--bg-hover);
}

/* Table Cell Styles */
.timestamp-cell {
  width: 180px;
}

.timestamp {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.level-cell {
  width: 100px;
}

.level-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.level-error {
  background: rgb(239 68 68 / 0.1);
  color: var(--error);
}

.level-warn {
  background: rgb(245 158 11 / 0.1);
  color: var(--warning);
}

.level-info {
  background: rgb(59 130 246 / 0.1);
  color: var(--accent-primary);
}

.level-debug {
  background: rgb(16 185 129 / 0.1);
  color: var(--success);
}

.user-cell {
  width: 120px;
}

.user-id {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.8rem;
}

.message-cell {
  min-width: 300px;
  max-width: 400px;
}

.message-content {
  word-break: break-word;
}

.message-text {
  color: var(--text-primary);
}

.message-more {
  color: var(--text-muted);
  font-style: italic;
}

.url-cell {
  width: 200px;
}

.url-content {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.8rem;
  color: var(--text-secondary);
  word-break: break-all;
}

.actions-cell {
  width: 100px;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Pagination */
.pagination-container {
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.page-btn:hover:not(:disabled) {
  background: var(--bg-hover);
  border-color: var(--accent-primary);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn.active {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

.pagination-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-container {
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.modal-close {
  width: 2rem;
  height: 2rem;
  border: none;
  background: none;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.modal-close svg {
  width: 1.25rem;
  height: 1.25rem;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.modal-footer {
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Report Details */
.report-details {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.detail-section h4 {
  margin: 0 0 1rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.detail-item span {
  color: var(--text-primary);
}

.code-block {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: 1rem;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
}

.stack-trace {
  max-height: 200px;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .dashboard-main {
    padding: 1rem;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .table-container {
    overflow-x: scroll;
  }

  .custom-table {
    min-width: 800px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
  }

  .modal-container {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>
