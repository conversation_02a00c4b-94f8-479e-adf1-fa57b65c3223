import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { darkTheme, lightTheme, type GlobalThemeOverrides } from 'naive-ui';

export const useThemeStore = defineStore('theme', () => {
  const isDark = ref(true); // Default to dark mode

  // Initialize theme from localStorage
  const initializeTheme = () => {
    const saved = localStorage.getItem('theme');
    if (saved) {
      isDark.value = saved === 'dark';
    } else {
      // Check system preference
      isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    updateDocumentTheme();
  };

  // Toggle theme
  const toggleTheme = () => {
    isDark.value = !isDark.value;
    localStorage.setItem('theme', isDark.value ? 'dark' : 'light');
    updateDocumentTheme();
  };

  // Update document class for CSS
  const updateDocumentTheme = () => {
    document.documentElement.setAttribute('data-theme', isDark.value ? 'dark' : 'light');
    document.documentElement.style.colorScheme = isDark.value ? 'dark' : 'light';
  };

  // Naive UI theme
  const naiveTheme = computed(() => isDark.value ? darkTheme : lightTheme);

  // Dark theme overrides
  const darkThemeOverrides: GlobalThemeOverrides = {
    common: {
      primaryColor: '#646cff',
      primaryColorHover: '#535bf2',
      primaryColorPressed: '#4338ca',
      textColorBase: '#ffffff',
      textColor1: 'rgba(255, 255, 255, 0.9)',
      textColor2: 'rgba(255, 255, 255, 0.8)',
      textColor3: 'rgba(255, 255, 255, 0.7)',
      inputColorDisabled: 'rgba(255, 255, 255, 0.1)',
      placeholderColor: 'rgba(255, 255, 255, 0.5)',
      placeholderColorDisabled: 'rgba(255, 255, 255, 0.3)',
      bodyColor: '#0f0f23',
      cardColor: 'rgba(255, 255, 255, 0.1)',
      modalColor: '#1a1a2e'
    },
    Input: {
      color: 'rgba(0, 0, 0, 0.3)',
      colorFocus: 'rgba(0, 0, 0, 0.4)',
      colorDisabled: 'rgba(255, 255, 255, 0.1)',
      textColor: '#ffffff',
      textColorDisabled: 'rgba(255, 255, 255, 0.5)',
      placeholderColor: 'rgba(255, 255, 255, 0.5)',
      placeholderColorDisabled: 'rgba(255, 255, 255, 0.3)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      borderHover: '1px solid rgba(255, 255, 255, 0.3)',
      borderFocus: '1px solid #646cff',
      borderDisabled: '1px solid rgba(255, 255, 255, 0.1)'
    }
  };

  // Light theme overrides
  const lightThemeOverrides: GlobalThemeOverrides = {
    common: {
      primaryColor: '#646cff',
      primaryColorHover: '#535bf2',
      primaryColorPressed: '#4338ca',
      textColorBase: '#213547',
      textColor1: 'rgba(33, 53, 71, 0.9)',
      textColor2: 'rgba(33, 53, 71, 0.8)',
      textColor3: 'rgba(33, 53, 71, 0.7)',
      bodyColor: '#f8fafc',
      cardColor: 'rgba(255, 255, 255, 0.9)',
      modalColor: '#ffffff'
    },
    Input: {
      color: 'rgba(255, 255, 255, 0.9)',
      colorFocus: 'rgba(255, 255, 255, 1)',
      colorDisabled: 'rgba(0, 0, 0, 0.05)',
      textColor: '#213547',
      textColorDisabled: 'rgba(33, 53, 71, 0.5)',
      placeholderColor: 'rgba(33, 53, 71, 0.5)',
      placeholderColorDisabled: 'rgba(33, 53, 71, 0.3)',
      border: '1px solid rgba(33, 53, 71, 0.2)',
      borderHover: '1px solid rgba(33, 53, 71, 0.3)',
      borderFocus: '1px solid #646cff',
      borderDisabled: '1px solid rgba(33, 53, 71, 0.1)'
    }
  };

  // Current theme overrides
  const themeOverrides = computed(() => 
    isDark.value ? darkThemeOverrides : lightThemeOverrides
  );

  return {
    isDark,
    naiveTheme,
    themeOverrides,
    toggleTheme,
    initializeTheme
  };
});
