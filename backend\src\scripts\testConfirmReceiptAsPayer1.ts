import axios from 'axios';

const API_BASE = 'http://localhost:3000/api';

async function testConfirmReceiptAsPayer1() {
  try {
    // <NAME_EMAIL> (payer1 who should confirm receipt)
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: '11111111'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Logged <NAME_EMAIL> successfully');

    // Now make the API call to confirm receipt
    const transactionId = 'cmb18be2q0006vlyohi1dfi0d';
    
    console.log('🔄 Making confirm receipt API call...');
    const confirmResponse = await axios.post(
      `${API_BASE}/transactions/${transactionId}/confirm-receipt`,
      {},
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Confirm receipt successful!');
    console.log('Response:', JSON.stringify(confirmResponse.data, null, 2));
    console.log('🎉 This should have triggered a system message via the fixed socket room emission!');
    
  } catch (error: any) {
    console.log('❌ Error occurred:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
    } else {
      console.log('Message:', error.message);
    }
  }
}

testConfirmReceiptAsPayer1();
