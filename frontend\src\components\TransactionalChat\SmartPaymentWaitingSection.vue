<template>
  <div class="smart-payment-waiting-section" data-testid="smart-payment-waiting-section">
    <div class="waiting-icon">⏳</div>
    <h4 class="waiting-title" data-testid="waiting-title">{{ t('transactionalChat.actionCards.yourTurnToPay.waitingTitle', { name: otherUserName }) }}</h4>
    <p class="waiting-description" data-testid="waiting-description">{{ t('transactionalChat.actionCards.yourTurnToPay.waitingDescription') }}</p>
    
    <!-- Timer Section (following timer.md guide) -->
    <div v-if="timerDisplayValue"
         class="timer-display"
         :class="timerColorClass"
         data-testid="payment-waiting-timer">
      <div class="timer-content">
        <span class="timer-label">{{ timerLabel }}</span>
        <span class="timer-value">{{ timerDisplayValue }}</span>
      </div>
    </div>
    
    <div class="loading-spinner" data-testid="payment-waiting-spinner"></div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'
import { useTransactionStore } from '@/stores/transaction';
import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic'
import { useTimerDisplay } from '@/composables/useTimerDisplay'
import { TransactionStatusEnum } from '@/types/transaction'

const { t } = useI18n()
const transactionalChatStore = useTransactionalChatStore()
const transactionStore = useTransactionStore()

const otherUserName = computed(() => {
  return transactionalChatStore.otherUser?.name || t('common.theOtherUser');
});

// Timer Logic (following timer.md guide exactly)
const {
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
} = useTransactionFlowLogic(
  computed(() => transactionalChatStore.chatSessionId || null)
);

// Use shared timer display logic
const {
  timerDisplayValue,
  timerColorClass,
  timerLabel
} = useTimerDisplay({
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
});
</script>

<style scoped>
.smart-payment-waiting-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  text-align: center;
  background: var(--n-color);
  border-radius: 12px;
  margin: 16px 0;
}

.waiting-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.waiting-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--n-text-color);
  margin-bottom: 8px;
}

.waiting-description {
  font-size: 14px;
  color: var(--n-text-color-2);
  margin-bottom: 20px;
  line-height: 1.5;
}

/* Timer Area (from timer.md guide) */
.timer-display {
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  margin: 12px 0;
  border: 2px solid #10b981; /* Default green for plenty of time */
  background-color: rgba(16, 185, 129, 0.1);
  color: #065f46;
  transition: all 0.3s ease;
  font-weight: 600;
}

/* Plenty of time - Green (>1.5 hours) */
.timer-display.timer-plenty {
  background-color: rgba(16, 185, 129, 0.1);
  color: #065f46;
  border-color: #10b981;
}

/* Moderate time - Light Green (1-1.5 hours) */
.timer-display.timer-moderate {
  background-color: rgba(34, 197, 94, 0.1);
  color: #166534;
  border-color: #22c55e;
}

/* Warning - Orange (30min-1hour) */
.timer-display.timer-warning {
  background-color: rgba(251, 146, 60, 0.1);
  color: #c2410c;
  border-color: #fb923c;
}

/* Critical - Red with pulse (<30 minutes) */
.timer-display.timer-critical {
  background-color: rgba(239, 68, 68, 0.15);
  color: #dc2626;
  border-color: #ef4444;
  animation: pulse 2s infinite;
}

/* Expired - Dark Red (time ran out) */
.timer-display.timer-expired {
  background-color: rgba(245, 101, 101, 0.2);
  color: #991b1b;
  border-color: #f56565;
  border-style: dashed;
}

/* Elapsed - Gray (counting up after expiry) */
.timer-display.timer-elapsed {
  background-color: rgba(156, 163, 175, 0.1);
  color: #4b5563;
  border-color: #9ca3af;
  border-style: dotted;
}

/* Default fallback */
.timer-display.timer-default {
  background-color: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border-color: #3b82f6;
}

.timer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.timer-label {
  font-size: 0.85em;
  font-weight: 500;
}

.timer-value {
  font-size: 1.3em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #ddd;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-top: 16px;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .smart-payment-waiting-section {
    padding: 16px;
    margin: 8px 0;
  }
  
  .waiting-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }
  
  .waiting-title {
    font-size: 16px;
  }
  
  .waiting-description {
    font-size: 13px;
  }
  
  .timer-display {
    padding: 10px 12px;
  }
  
  .timer-value {
    font-size: 18px;
  }
}
</style>
