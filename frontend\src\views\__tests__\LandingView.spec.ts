import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import LandingView from '../LandingView.vue';
import { NButton } from 'naive-ui';

describe('LandingView.vue', () => {
  it('renders landing page content', () => {
    const wrapper = mount(LandingView, {
      global: { components: { NButton } }
    });
    expect(wrapper.text()).toContain('Welcome to ArzAni');
    expect(wrapper.text()).toContain('Your platform for secure, reputation-based matchmaking.');
    // Check for Login and Register buttons
    const buttons = wrapper.findAllComponents(NButton);
    expect(buttons.length).toBeGreaterThanOrEqual(2);
  });
});
