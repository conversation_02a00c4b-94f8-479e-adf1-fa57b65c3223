import apiClient from './apiClient';
import type { Transaction } from '@/types/transaction'; // Corrected to TransactionStatusEnum
import { 
  type AgreeToTermsPayload, // Import from new location
  type DesignateFirstPayerPayload, 
  type DeclarePaymentPayload, 
  type CancelTransactionPayload, 
  type DisputeTransactionPayload 
} from '@/types/transaction'; // Import from new location

const BASE_URL = '/transactions';

/**
 * Fetches the details of a specific transaction.
 * @param transactionId - The ID of the transaction to fetch.
 * @returns A promise that resolves to the transaction details.
 */
export const getTransactionById = async (transactionId: string): Promise<Transaction> => {
  const response = await apiClient.get<Transaction>(`${BASE_URL}/${transactionId}`);
  return response.data;
};

/**
 * Fetches a transaction by its chatSessionId
 * @param chatSessionId - The ID of the chat session.
 * @returns A promise that resolves to the transaction details or null if not found.
 */
export const getTransactionByChatSessionId = async (chatSessionId: string): Promise<Transaction | null> => {
  try {
    const response = await apiClient.get<Transaction>(`${BASE_URL}/chat/${chatSessionId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching transaction by chatSessionId ${chatSessionId}:`, error);
    if ((error as any).response && (error as any).response.status === 404) {
      return null; 
    }
    throw error;
  }
};

/**
 * Allows the current user to agree to the terms of a transaction.
 * @param transactionId - The ID of the transaction.
 * @param payload - Optional payload, e.g., a tracking number.
 * @returns A promise that resolves to the updated transaction details.
 */
export const agreeToTerms = async (transactionId: string, payload?: AgreeToTermsPayload): Promise<Transaction> => {
  const response = await apiClient.post<Transaction>(`${BASE_URL}/${transactionId}/agree`, payload);
  return response.data;
};

/**
 * Allows the current user (who must be part of the transaction) to designate the first payer.
 * This action is typically performed after both parties have agreed to the terms.
 * @param transactionId - The ID of the transaction.
 * @param payload - The ID of the user who will make the first payment.
 * @returns A promise that resolves to the updated transaction details.
 */
export const designateFirstPayer = async (transactionId: string, payload: DesignateFirstPayerPayload): Promise<Transaction> => {
  const response = await apiClient.post<Transaction>(`${BASE_URL}/${transactionId}/designate-first-payer`, payload);
  return response.data;
};

/**
 * Allows the current user to declare that they have made a payment.
 * @param transactionId - The ID of the transaction.
 * @param payload - Optional payload, e.g., a tracking number.
 * @returns A promise that resolves to the updated transaction details.
 */
export const declarePayment = async (transactionId: string, payload: DeclarePaymentPayload): Promise<Transaction> => {
  const response = await apiClient.post<Transaction>(`${BASE_URL}/${transactionId}/declare-payment`, payload);
  return response.data;
};

/**
 * Allows the current user to confirm receipt of a payment.
 * @param transactionId - The ID of the transaction.
 * @returns A promise that resolves to the updated transaction details.
 */
export const confirmReceipt = async (transactionId: string): Promise<Transaction> => {
  const response = await apiClient.post<Transaction>(`${BASE_URL}/${transactionId}/confirm-receipt`);
  return response.data;
};

/**
 * Allows the current user to cancel a transaction.
 * @param transactionId - The ID of the transaction.
 * @param payload - Payload containing the reason for cancellation.
 * @returns A promise that resolves to the updated transaction details.
 */
export const cancelTransaction = async (transactionId: string, payload: CancelTransactionPayload): Promise<Transaction> => {
  const response = await apiClient.post<Transaction>(`${BASE_URL}/${transactionId}/cancel`, payload);
  return response.data;
};

/**
 * Allows the current user to dispute a transaction.
 * @param transactionId - The ID of the transaction.
 * @param payload - Payload containing the reason for the dispute.
 * @returns A promise that resolves to the updated transaction details.
 */
export const disputeTransaction = async (transactionId: string, payload: DisputeTransactionPayload): Promise<Transaction> => {
  const response = await apiClient.post<Transaction>(`${BASE_URL}/${transactionId}/dispute`, payload);
  return response.data;
};
