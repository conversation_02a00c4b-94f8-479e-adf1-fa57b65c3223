const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test credentials
const user1Credentials = { email: '<EMAIL>', password: 'h1' };
const user2Credentials = { email: '<EMAIL>', password: 'h2' };

async function login(credentials) {
  try {
    const response = await axios.post(`${BASE_URL}/api/auth/login`, credentials);
    return response.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function getMatches(token) {
  try {
    const response = await axios.get(`${BASE_URL}/api/matches`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  } catch (error) {
    console.error('Failed to get matches:', error.response?.data || error.message);
    return [];
  }
}

async function updateOffer(token, offerId, updates) {
  try {
    const response = await axios.put(`${BASE_URL}/api/offers/${offerId}`, updates, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  } catch (error) {
    console.error('Failed to update offer:', error.response?.data || error.message);
    throw error;
  }
}

async function getOffers(token) {
  try {
    const response = await axios.get(`${BASE_URL}/api/offers`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  } catch (error) {
    console.error('Failed to get offers:', error.response?.data || error.message);
    return [];
  }
}

async function testOfferUpdateMatching() {
  console.log('=== Testing Offer Update Matching ===\n');

  try {
    // Login both users
    console.log('1. Logging in users...');
    const token1 = await login(user1Credentials);
    const token2 = await login(user2Credentials);
    console.log('✓ Both users logged in successfully\n');

    // Get current offers
    console.log('2. Getting current offers...');
    const offers1 = await getOffers(token1);
    const offers2 = await getOffers(token2);
    
    console.log(`User 1 offers: ${offers1.length}`);
    console.log(`User 2 offers: ${offers2.length}`);
    
    if (offers1.length === 0 || offers2.length === 0) {
      console.log('❌ Need offers from both users to test. Please create offers first.');
      return;
    }

    // Show offer details
    console.log('\nUser 1 offers:');
    offers1.forEach(offer => {
      console.log(`  - ID: ${offer.id}, Type: ${offer.type}, Amount: ${offer.amount}, Rate: ${offer.baseRate}`);
    });

    console.log('\nUser 2 offers:');
    offers2.forEach(offer => {
      console.log(`  - ID: ${offer.id}, Type: ${offer.type}, Amount: ${offer.amount}, Rate: ${offer.baseRate}`);
    });

    // Get current matches
    console.log('\n3. Getting current matches...');
    const matches1Before = await getMatches(token1);
    const matches2Before = await getMatches(token2);
    
    console.log(`User 1 current matches: ${matches1Before.length}`);
    console.log(`User 2 current matches: ${matches2Before.length}`);

    // Update an offer to create a potential match
    console.log('\n4. Updating an offer to create matching conditions...');
    
    const offerToUpdate = offers1[0]; // Update first offer from user 1
    const targetOffer = offers2[0]; // Target to match with user 2's first offer
    
    console.log(`Updating offer ${offerToUpdate.id} to match with offer ${targetOffer.id}`);
    console.log(`Target offer: Type=${targetOffer.type}, Amount=${targetOffer.amount}, Rate=${targetOffer.baseRate}`);
    
    // Update to match the target offer's conditions
    const oppositeType = targetOffer.type === 'BUY' ? 'SELL' : 'BUY';
    const updates = {
      type: oppositeType,
      amount: targetOffer.amount,
      baseRate: targetOffer.baseRate
    };
    
    console.log(`Updating to: Type=${oppositeType}, Amount=${targetOffer.amount}, Rate=${targetOffer.baseRate}`);
    
    const updatedOffer = await updateOffer(token1, offerToUpdate.id, updates);
    console.log('✓ Offer updated successfully');

    // Wait a moment for immediate matching to process
    console.log('\n5. Waiting for immediate matching to process...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check for new matches
    console.log('\n6. Checking for new matches after update...');
    const matches1After = await getMatches(token1);
    const matches2After = await getMatches(token2);
    
    console.log(`User 1 matches after update: ${matches1After.length} (was ${matches1Before.length})`);
    console.log(`User 2 matches after update: ${matches2After.length} (was ${matches2Before.length})`);

    if (matches1After.length > matches1Before.length || matches2After.length > matches2Before.length) {
      console.log('✅ SUCCESS: New matches found after offer update!');
      
      // Show the new matches
      if (matches1After.length > matches1Before.length) {
        console.log('\nNew matches for User 1:');
        matches1After.slice(matches1Before.length).forEach(match => {
          console.log(`  - Match ID: ${match.id}, Status: ${match.status}`);
        });
      }
      
      if (matches2After.length > matches2Before.length) {
        console.log('\nNew matches for User 2:');
        matches2After.slice(matches2Before.length).forEach(match => {
          console.log(`  - Match ID: ${match.id}, Status: ${match.status}`);
        });
      }
    } else {
      console.log('⚠️  No new matches found. This could mean:');
      console.log('   - The offers still don\'t match exactly');
      console.log('   - A match already existed between these offers');
      console.log('   - The matching conditions aren\'t met');
    }

    console.log('\n7. Waiting for scheduled matching cycle...');
    console.log('   (The MatchingJobService runs every 30 seconds)');
    await new Promise(resolve => setTimeout(resolve, 35000)); // Wait 35 seconds

    // Check again after scheduled matching
    const matches1Final = await getMatches(token1);
    const matches2Final = await getMatches(token2);
    
    console.log(`User 1 matches after scheduled cycle: ${matches1Final.length}`);
    console.log(`User 2 matches after scheduled cycle: ${matches2Final.length}`);

    if (matches1Final.length > matches1After.length || matches2Final.length > matches2After.length) {
      console.log('✅ Additional matches found in scheduled cycle');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testOfferUpdateMatching();
