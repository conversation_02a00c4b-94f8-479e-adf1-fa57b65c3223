# Translation System TODO Documentation

## Current State
- ✅ Vue i18n setup with Persian (fa) as default and English (en) as secondary
- ✅ Language store with persistence and RTL/LTR support
- ✅ Translation composable for easy access
- ✅ Language selector component
- ✅ Basic translations for landing page, auth forms, and footer

## Phase 1: Complete UI Translation Coverage (High Priority)

### 1. Core Components Missing Translations
- [ ] **OfferCard.vue** - Offer status, labels, buttons
- [ ] **OfferForm.vue** - Form labels, validation messages, placeholders
- [ ] **OfferDetailsModal.vue** - Modal content, action buttons
- [ ] **OfferInterestCard.vue** - Interest status, action buttons
- [ ] **ChatWindow.vue** - Chat interface labels, placeholders
- [ ] **ChatIcon.vue** - Tooltip texts
- [ ] **NotificationBell.vue** - Notification messages
- [ ] **ProfileCard.vue** - Profile labels and fields
- [ ] **PhoneNumberInput.vue** - Input labels and validation messages
- [ ] **VerificationModal.vue** - Verification steps and messages
- [ ] **ReputationIcon.vue** - Reputation level descriptions

### 2. Views Requiring Translation
- [ ] **BrowseOffersView.vue** - Page title, filters, empty states
- [ ] **CreateOfferView.vue** - Page title, form sections, help text
- [ ] **EditOfferView.vue** - Page title, update messages
- [ ] **HomeView.vue** - Dashboard content, welcome messages
- [ ] **MyOffersView.vue** - Page title, offer management buttons
- [ ] **ProfileView/** - All profile-related content
  - [ ] **ProfileView.vue** - Main profile content
  - [ ] **UserOffers.vue** - User's offers section
  - [ ] **UserProfileCard.vue** - Profile editing form
  - [ ] **UserStats.vue** - Statistics labels
- [ ] **NotFoundView.vue** - 404 error messages

### 3. Translation Keys to Add

#### Offer Management
```typescript
offer: {
  status: {
    active: 'فعال | Active',
    inactive: 'غیرفعال | Inactive', 
    completed: 'تکمیل شده | Completed',
    cancelled: 'لغو شده | Cancelled'
  },
  actions: {
    create: 'ایجاد آگهی | Create Offer',
    edit: 'ویرایش | Edit',
    cancel: 'لغو | Cancel',
    complete: 'تکمیل | Complete',
    showInterest: 'اعلام علاقه | Show Interest'
  },
  form: {
    title: 'عنوان | Title',
    description: 'توضیحات | Description',
    amount: 'مبلغ | Amount',
    rate: 'نرخ | Rate',
    location: 'موقعیت | Location'
  }
}
```

#### Chat & Communication
```typescript
chat: {
  placeholder: 'پیام خود را بنویسید... | Type your message...',
  send: 'ارسال | Send',
  online: 'آنلاین | Online',
  offline: 'آفلاین | Offline',
  typing: 'در حال تایپ... | Typing...'
}
```

#### Notifications
```typescript
notifications: {
  newInterest: 'علاقه جدید به آگهی شما | New interest in your offer',
  interestAccepted: 'علاقه شما پذیرفته شد | Your interest was accepted',
  interestDeclined: 'علاقه شما رد شد | Your interest was declined',
  offerUpdated: 'آگهی به‌روزرسانی شد | Offer updated'
}
```

#### Validation Messages
```typescript
validation: {
  required: 'این فیلد الزامی است | This field is required',
  minLength: 'حداقل {0} کاراکتر | Minimum {0} characters',
  maxLength: 'حداکثر {0} کاراکتر | Maximum {0} characters',
  invalidEmail: 'ایمیل نامعتبر | Invalid email',
  invalidPhone: 'شماره تلفن نامعتبر | Invalid phone number'
}
```

#### Error Messages
```typescript
errors: {
  networkError: 'خطا در اتصال | Network error',
  serverError: 'خطای سرور | Server error',
  unauthorized: 'دسترسی غیرمجاز | Unauthorized access',
  notFound: 'یافت نشد | Not found',
  rateLimitExceeded: 'تعداد درخواست بیش از حد مجاز | Rate limit exceeded'
}
```

### 4. Dynamic Content Translation
- [ ] **Date/Time Formatting** - Persian calendar support
- [ ] **Number Formatting** - Persian/English numerals
- [ ] **Currency Display** - Rial formatting for both languages
- [ ] **Pluralization Rules** - Persian vs English plural forms

### 5. Translation Utilities Needed
- [ ] **Missing Translation Detection** - Dev tool to find untranslated strings
- [ ] **Translation Key Validation** - Ensure all keys exist in both languages
- [ ] **RTL/LTR Layout Fixes** - Component-specific direction adjustments
- [ ] **Font Loading** - Persian font support optimization

## Phase 2: Advanced Translation Features (Medium Priority)

### 1. Translation Management Tools
- [ ] **Translation Extraction Script** - Auto-extract translatable strings
- [ ] **Translation Validation Script** - Check for missing/extra keys
- [ ] **Translation Coverage Report** - Show completion percentage

### 2. Performance Optimizations
- [ ] **Lazy Loading** - Load translations only when needed
- [ ] **Translation Caching** - Browser cache for translations
- [ ] **Bundle Splitting** - Separate translation bundles

### 3. Developer Experience
- [ ] **TypeScript Integration** - Type-safe translation keys
- [ ] **Translation Helpers** - Custom composables for complex translations
- [ ] **Hot Reload** - Development translation updates without refresh

## Phase 3: Production Readiness (Lower Priority)

### 1. Translation Workflow
- [ ] **Professional Translation Review** - Native speaker validation
- [ ] **Translation Memory** - Consistency across similar terms
- [ ] **Style Guide** - Translation conventions and tone

### 2. User Experience
- [ ] **Language Detection** - Auto-detect user's preferred language
- [ ] **Smooth Transitions** - Animated language switching
- [ ] **Accessibility** - Screen reader support for both languages

### 3. Analytics & Monitoring
- [ ] **Translation Usage Analytics** - Track which translations are used
- [ ] **Error Reporting** - Monitor translation-related errors
- [ ] **A/B Testing** - Test different translation variations

## Implementation Commands

### Setup Development Environment
```powershell
# Navigate to frontend directory
cd C:\Code\MUNygo\frontend

# Install development dependencies if needed
npm install

# Start development server
npm run dev
```

### Translation File Management
```powershell
# Check current translation files
Get-Content .\src\utils\translations.ts | Select-String -Pattern "fa:|en:"

# Backup current translations
Copy-Item .\src\utils\translations.ts .\src\utils\translations.backup.ts
```

### Testing Translation Changes
```powershell
# Run frontend tests
npm test

# Build for production to check for issues
npm run build
```

## Notes
- All translation keys should be defined in separate JSON files for each language (e.g., `fa.json`, `en.json`)
Each key should have consistent naming across all language files
- Maintain consistent terminology across the application
- Consider cultural context, not just literal translation
- Test both RTL (Persian) and LTR (English) layouts thoroughly
- Keep translation keys organized in logical groupings
- Use interpolation for dynamic content: `$t('message', { name: 'value' })`

## Estimated Timeline
- **Phase 1**: 1-2 weeks (core UI translation)
- **Phase 2**: 1 week (advanced features)
- **Phase 3**: 1 week (production polish)

## Priority Order
1. Complete missing component translations
2. Add validation and error message translations
3. Implement translation utilities
4. Polish and optimize for production