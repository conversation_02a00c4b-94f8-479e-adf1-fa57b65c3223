-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "NotificationType" ADD VALUE 'MATCH_FOUND';
ALTER TYPE "NotificationType" ADD VALUE 'MATCH_ACCEPTED_BY_OTHER';
ALTER TYPE "NotificationType" ADD VALUE 'MATCH_DECLINED_BY_OTHER';
ALTER TYPE "NotificationType" ADD VALUE 'MATCH_EXPIRED';
ALTER TYPE "NotificationType" ADD VALUE 'MATCH_CONVERTED_TO_CHAT';
