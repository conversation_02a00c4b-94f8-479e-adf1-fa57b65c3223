/**
 * Debug logging utility for development environment
 * 
 * This utility provides a consistent way to handle debug logging
 * that is automatically disabled in production builds.
 */

/**
 * Debug logger that only logs in development mode
 */
export const debugLogger = {
  /**
   * Log debug information (only in development)
   */
  log: (...args: any[]) => {
    if (import.meta.env.DEV) {
      console.log(...args);
    }
  },

  /**
   * Log error information (only in development)
   */
  error: (...args: any[]) => {
    if (import.meta.env.DEV) {
      console.error(...args);
    }
  },

  /**
   * Log warning information (only in development)
   */
  warn: (...args: any[]) => {
    if (import.meta.env.DEV) {
      console.warn(...args);
    }
  },

  /**
   * Log info information (only in development)
   */
  info: (...args: any[]) => {
    if (import.meta.env.DEV) {
      console.info(...args);
    }
  }
};
