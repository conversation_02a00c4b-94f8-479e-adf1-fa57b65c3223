/* Clean, modern design system with trustworthy typography */
.negotiation-card {
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  max-width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  animation: slideInUp 0.3s ease-out;
}

/* Card Header & Status - Consistent Structure */
.card-header {
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.card-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.status-text.loading {
  color: #3b82f6;
}

.status-text.success {
  color: #16a34a;
}

.status-text.error {
  color: #dc2626;
}

/* Content Body - Dynamic based on state */
.card-content {
  padding: 20px;
}

/* Loading State */
.content-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-spinner {
  font-size: 32px;
  animation: spin 2s linear infinite;
  margin-bottom: 16px;
}

.loading-message {
  color: #64748b;
  font-size: 16px;
}

/* Recommendation Statement - Central & Prominent */
.recommendation-statement {
  text-align: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border-left: 4px solid #3b82f6;
}

.recommendation-main {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
  line-height: 1.4;
}

.recommendation-reason {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
  font-style: italic;
}

.info-icon {
  background: #e2e8f0;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #64748b;
  flex-shrink: 0;
}

/* Smart Payment Flow */
.payment-flow-smart {
  margin: 24px 0;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.payment-flow-smart.updated {
  border-left: 4px solid #f59e0b;
  background: #fffbeb;
}

.flow-step-smart {
  display: flex;
  align-items: center;
  gap: 16px;
}

.step-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: #334155;
  color: white;
  border-radius: 50%;
  font-size: 14px;
  font-weight: 700;
  flex-shrink: 0;
}

.step-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}

.participant {
  font-weight: 600;
  font-size: 14px;
  text-align: center;
  flex: 1;
}

.participant.payer {
  color: #dc2626;
}

.participant.receiver {
  color: #16a34a;
}

.arrow-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 2;
}

.payment-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex: 2;
}

.payment-direction {
  font-size: 18px;
  font-weight: bold;
  color: #3b82f6;
}

.amount {
  font-size: 13px;
  font-weight: 700;
  color: #475569;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: #e2e8f0;
  margin-bottom: 4px;
  white-space: nowrap;
}

.arrow-line-right {
  width: 100%;
  height: 2px;
  background-color: #cbd5e1;
  position: relative;
}

.arrow-line-right::before {
  content: '→';
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #94a3b8;
  font-weight: bold;
}

.arrow-line-right::after {
  content: '▶';
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #94a3b8;
}

/* Message Input Section - Expandable */
.message-input-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  margin-top: 16px;
  animation: slideDown 0.3s ease-out;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.optional-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 400;
  font-style: italic;
}

.message-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  margin-bottom: 8px;
  box-sizing: border-box;
  background: white;
  color: #1e293b; /* Fix: Set text color to be visible */
}

.message-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.message-footer {
  display: flex;
  justify-content: flex-end;
}

.char-count {
  font-size: 12px;
  color: #64748b;
  font-family: monospace;
}

/* Proposal Message Display - Chat-like */
.proposal-message-display {
  background: #f1f5f9;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  border-left: 4px solid #f59e0b;
}

.proposal-message-display .message-header {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.proposal-message-display .message-content {
  font-size: 14px;
  color: #64748b;
  font-style: italic;
  line-height: 1.4;
}

/* Proposal Statement */
.proposal-statement {
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  padding: 16px;
  background: #fef3c7;
  border-radius: 8px;
  border-left: 4px solid #f59e0b;
}

/* Final Proposal Warning - Inline Alert */
.final-proposal-warning {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border: 1px solid #fecaca;
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
}

.warning-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.warning-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-title {
  font-size: 14px;
  font-weight: 600;
  color: #991b1b;
  margin-bottom: 4px;
}

.warning-message {
  font-size: 13px;
  color: #7f1d1d;
  line-height: 1.4;
}

/* Waiting State */
.content-waiting {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 20px;
}

.waiting-animation {
  margin-bottom: 16px;
}

.waiting-dots {
  display: flex;
  gap: 4px;
}

.waiting-dots span {
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite;
}

.waiting-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.waiting-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

.waiting-message {
  font-size: 16px;
  color: #64748b;
  line-height: 1.4;
}

.sent-message-summary {
  background: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
  border: 1px solid #e2e8f0;
}

.summary-label {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 4px;
}

.summary-content {
  font-size: 14px;
  color: #1e293b;
  font-style: italic;
}

/* Success State */
.content-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-radius: 12px;
  color: #166534;
}

.success-animation {
  margin-bottom: 16px;
}

.success-checkmark {
  font-size: 48px;
  color: #16a34a;
  animation: checkmarkPop 0.6s ease-out;
}

.success-message {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 8px;
}

.transition-message {
  font-size: 14px;
  color: #166534;
  opacity: 0.8;
  margin-bottom: 16px;
}

.transition-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.transition-dots {
  display: flex;
  gap: 4px;
}

.transition-dots span {
  width: 6px;
  height: 6px;
  background: #16a34a;
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite;
}

.transition-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.transition-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

.transition-text {
  font-size: 12px;
  color: #166534;
  opacity: 0.7;
}

/* Cancelled State */
.content-cancelled {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border-radius: 12px;
  color: #991b1b;
}

.cancelled-icon {
  font-size: 48px;
  color: #dc2626;
  margin-bottom: 16px;
}

.cancelled-message {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
}

/* Action Footer - Dynamic Buttons */
.card-actions {
  padding: 16px 20px 20px;
  border-top: 1px solid #f1f5f9;
  background: #fafbfc;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.actions-single {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.message-actions {
  display: flex;
  gap: 12px;
}

/* Action Buttons */
.action-btn {
  border: none;
  border-radius: 12px;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 48px;
  position: relative;
  overflow: hidden;
}

.action-btn.primary {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(22, 163, 74, 0.3);
}

.action-btn.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #15803d 0%, #166534 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 163, 74, 0.4);
}

.action-btn.secondary {
  background: #f8fafc;
  color: #3b82f6;
  border: 2px solid #e2e8f0;
}

.action-btn.secondary:hover:not(:disabled) {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.action-btn.tertiary {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.action-btn.tertiary:hover:not(:disabled) {
  background: #fee2e2;
}

.action-btn.large {
  padding: 16px 24px;
  font-size: 18px;
  min-height: 52px;
  width: 100%;
}

.action-btn.full-width {
  grid-column: 1 / -1;
  width: 100%;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-spinner {
  animation: spin 1s linear infinite;
}

.btn-icon {
  font-size: 18px;
}

.final-badge {
  background: #fbbf24;
  color: #92400e;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  margin-left: 8px;
}

.waiting-status {
  font-size: 14px;
  color: #64748b;
  text-align: center;
  font-style: italic;
}

/* Modal - Clean & Modern */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.modal-card {
  background: white;
  border-radius: 16px;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  animation: scaleIn 0.3s ease-out;
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.modal-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.modal-content {
  padding: 16px 24px;
}

.modal-message {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 16px 24px 24px;
}

.modal-btn {
  flex: 1;
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-btn.primary {
  background: #3b82f6;
  color: white;
}

.modal-btn.primary:hover {
  background: #2563eb;
}

.modal-btn.secondary {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.modal-btn.secondary:hover {
  background: #e2e8f0;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 200px;
    transform: translateY(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

@keyframes checkmarkPop {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Mobile Optimizations - Progressive Enhancement */
@media (max-width: 768px) {
  .card-header {
    padding: 16px 16px 12px;
  }
  
  .card-content {
    padding: 16px;
  }
  
  .card-actions {
    padding: 12px 16px 16px;
  }
  
  .card-title {
    font-size: 16px;
  }
  
  .recommendation-main {
    font-size: 16px;
  }
  
  .action-btn {
    font-size: 15px;
    padding: 12px 16px;
    min-height: 44px;
  }
  
  .action-btn.large {
    font-size: 16px;
    min-height: 48px;
  }
  
  .flow-step-smart {
    gap: 12px;
  }

  .participant {
    font-size: 13px;
  }

  .amount {
    font-size: 12px;
  }
}

/* Very Small Screens */
@media (max-width: 480px) {
  .actions-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .modal-overlay {
    padding: 16px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .recommendation-statement {
    padding: 16px 12px;
  }
  
  .payment-flow-smart {
    padding: 12px;
  }
}

/* RTL Support */
[dir="rtl"] .card-header,
[dir="rtl"] .card-content,
[dir="rtl"] .card-actions {
  direction: rtl;
}

[dir="rtl"] .flow-step-smart {
  flex-direction: row-reverse;
}

[dir="rtl"] .step-content {
  flex-direction: row-reverse;
}

[dir="rtl"] .arrow-line-right::after {
    content: '◀';
    left: -8px;
    right: auto;
}

[dir="rtl"] .message-actions {
  flex-direction: row-reverse;
}

[dir="rtl"] .actions-grid {
  direction: rtl;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .negotiation-card {
    border: 2px solid #000;
  }
  
  .action-btn.primary {
    background: #000;
    color: #fff;
  }
  
  .payment-flow-smart {
    border: 2px solid #333;
  }
}

/* Timer Styles */
.timer-area {
  text-align: center;
  font-size: 1em;
  padding: 12px;
  border-radius: 8px;
  margin: 12px 0;
  border: 1px solid var(--tc-border-light);
  background-color: var(--tc-background-light);
}

.timer-area.critical { 
  background-color: rgba(208, 48, 80, 0.1);
  color: #d03050;
  font-weight: bold; 
  border-color: #d03050;
}

.timer-area.info { 
  background-color: rgba(24, 160, 251, 0.1);
  color: #18a0fb;
  border-color: #18a0fb;
}

.timer-area.expired { 
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
  font-weight: bold; 
  border-color: #faad14;
}

.timer-area.elapsed {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border-color: #52c41a;
}

.timer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.timer-label {
  font-size: 0.85em;
  color: var(--tc-text-secondary);
  font-weight: 500;
}

.timer-value {
  font-size: 1.3em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  color: var(--tc-text-primary);
}

.timer-critical .timer-value {
  color: #d03050;
}

.timer-expired .timer-value {
  color: #faad14;
}

.timer-elapsed .timer-value {
  color: #52c41a;
}

/* Dark mode support for timer */
[data-theme="dark"] .timer-area {
  background-color: var(--tc-background-dark);
  border-color: var(--tc-border-dark);
}

[data-theme="dark"] .timer-area.critical { 
  background-color: rgba(208, 48, 80, 0.15); 
  color: #ff7875;
  border-color: #ff7875;
}

[data-theme="dark"] .timer-area.info { 
  background-color: rgba(24, 160, 251, 0.15); 
  color: #69c0ff;
  border-color: #69c0ff;
}

[data-theme="dark"] .timer-area.expired { 
  background-color: rgba(250, 173, 20, 0.15); 
  color: #ffc53d;
  border-color: #ffc53d;
}

[data-theme="dark"] .timer-area.elapsed {
  background-color: rgba(82, 196, 26, 0.15);
  color: #95de64;
  border-color: #95de64;
}

[data-theme="dark"] .timer-critical .timer-value {
  color: #ff7875;
}

[data-theme="dark"] .timer-expired .timer-value {
  color: #ffc53d;
}

[data-theme="dark"] .timer-elapsed .timer-value {
  color: #95de64;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .negotiation-card,
  .message-input-section,
  .modal-overlay,
  .modal-card {
    animation: none;
  }
  
  .action-btn:hover {
    transform: none;
  }
  
  .waiting-dots span {
    animation: none;
  }
  
  .success-checkmark {
    animation: none;
  }
}
