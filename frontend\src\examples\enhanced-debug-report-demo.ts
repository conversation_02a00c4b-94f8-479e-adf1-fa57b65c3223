/**
 * Demonstration of Enhanced Debug Report Features
 * 
 * This file shows how to use the new offline reports, form drafts, 
 * and reset form functionality in the enhanced debug report system.
 */

import { useOfflineReports } from '@/composables/useOfflineReports';
import { useFormDrafts } from '@/composables/useFormDrafts';
import type { ReportDetails, ClientReportPayload } from '@/types/logging';

// Example usage of the enhanced debug report functionality
export function demonstrateEnhancedDebugReports() {
  const offlineReports = useOfflineReports();
  const formDrafts = useFormDrafts();

  // Example 1: Working with offline reports
  const demonstrateOfflineReports = () => {
    console.log('=== Offline Reports Demo ===');
    
    // Check if there are pending offline reports
    console.log('Has offline reports:', offlineReports.hasOfflineReports.value);
    console.log('Offline report count:', offlineReports.offlineReportCount.value);
    
    // Example report payload
    const exampleReportPayload: ClientReportPayload = {
      logs: [],
      reportDetails: {
        type: 'bug',
        severity: 'high',
        title: 'Example offline bug report',
        description: 'This is an example bug report stored offline',
        stepsToReproduce: '1. Go offline\n2. Submit report\n3. Report is stored locally',
        expectedBehavior: 'Report should be submitted when online',
        actualBehavior: 'Report is stored offline for later submission',
        additionalNotes: 'This demonstrates offline functionality'
      },
      timestamp: new Date().toISOString(),
      sessionId: 'demo-session-123'
    };

    // Add an offline report
    const reportId = offlineReports.addOfflineReport(exampleReportPayload);
    console.log('Added offline report with ID:', reportId);
    
    // Get statistics
    const stats = offlineReports.getOfflineReportStats();
    console.log('Offline report statistics:', stats);
    
    // Process offline reports (when connection is restored)
    // offlineReports.processOfflineReports();
  };

  // Example 2: Working with form drafts
  const demonstrateFormDrafts = () => {
    console.log('=== Form Drafts Demo ===');
    
    // Check if there are existing drafts
    console.log('Has drafts:', formDrafts.hasDrafts.value);
    console.log('Draft count:', formDrafts.draftCount.value);
    
    // Example form data
    const exampleFormData: ReportDetails = {
      type: 'feature-request',
      severity: 'medium',
      title: 'Add dark mode support',
      description: 'It would be great to have a dark mode option for better user experience',
      stepsToReproduce: '',
      expectedBehavior: '',
      actualBehavior: '',
      additionalNotes: 'This would help users who prefer dark themes'
    };

    // Save a draft
    const draftId = formDrafts.saveDraft(exampleFormData);
    console.log('Saved draft with ID:', draftId);
    
    // Auto-save (simulating user typing)
    const updatedFormData = {
      ...exampleFormData,
      description: exampleFormData.description + ' - Updated with more details'
    };
    formDrafts.autoSaveDraft(updatedFormData, draftId);
    console.log('Auto-saved draft');
    
    // Load the latest draft
    const latestDraft = formDrafts.getLatestDraft();
    console.log('Latest draft:', latestDraft);
    
    // Get draft statistics
    const draftStats = formDrafts.getDraftStats();
    console.log('Draft statistics:', draftStats);
    
    // Check for unsaved changes using the updated form data
    const hasUnsavedChanges = formDrafts.hasUnsavedChanges(updatedFormData);
    console.log('Has unsaved changes:', hasUnsavedChanges);
    
    // Also demonstrate checking with original data (should show no changes since it matches the initial draft)
    const hasUnsavedChangesOriginal = formDrafts.hasUnsavedChanges(exampleFormData);
    console.log('Has unsaved changes (original data):', hasUnsavedChangesOriginal);
    
    // Demonstrate different unsaved change scenarios
    const demonstrateUnsavedChangeScenarios = () => {
      console.log('--- Unsaved Change Scenarios ---');
      
      // Scenario 1: Create a new form data that differs from the saved draft
      const modifiedFormData: ReportDetails = {
        ...updatedFormData,
        title: 'Modified title for testing',
        severity: 'low' as const
      };
      
      console.log('Modified form data vs saved draft:', formDrafts.hasUnsavedChanges(modifiedFormData));
      
      // Scenario 2: Create form data identical to the latest saved draft (should show no changes)
      const latestDraft = formDrafts.getLatestDraft();
      if (latestDraft) {
        console.log('Latest draft vs itself:', formDrafts.hasUnsavedChanges(latestDraft.formData));
      }
      
      // Scenario 3: Empty form data (should show changes if there's an active draft)
      const emptyFormData: ReportDetails = {
        type: 'bug',
        severity: 'low',
        title: '',
        description: '',
        stepsToReproduce: '',
        expectedBehavior: '',
        actualBehavior: '',
        additionalNotes: ''
      };
      
      console.log('Empty form vs saved draft:', formDrafts.hasUnsavedChanges(emptyFormData));
    };
    
    demonstrateUnsavedChangeScenarios();
  };

  // Example 3: Cleanup operations
  const demonstrateCleanup = () => {
    console.log('=== Cleanup Demo ===');
    
    // Clean up old drafts
    formDrafts.cleanupOldDrafts();
    console.log('Cleaned up old drafts');
    
    // Clean up old offline reports
    offlineReports.cleanupOldReports();
    console.log('Cleaned up old offline reports');
    
    // Clear all drafts (for testing)
    // formDrafts.clearAllDrafts();
    
    // Clear all offline reports (for testing)
    // offlineReports.clearOfflineReports();
  };

  // Example 4: Event handling
  const demonstrateEventHandling = () => {
    console.log('=== Event Handling Demo ===');
    
    // Watch for connection changes
    // This would typically be done in a Vue component
    console.log('Connection status:', offlineReports.isOnline.value);
    console.log('Processing queue:', offlineReports.isProcessingQueue.value);
    
    // Watch for draft changes
    console.log('Active draft ID:', formDrafts.activeDraftId.value);
    console.log('Last auto-save:', formDrafts.lastAutoSaveAt.value);
  };

  // Run all demonstrations
  return {
    demonstrateOfflineReports,
    demonstrateFormDrafts,
    demonstrateCleanup,
    demonstrateEventHandling,
    
    // Run all demos
    runAllDemos: () => {
      demonstrateOfflineReports();
      demonstrateFormDrafts();
      demonstrateCleanup();
      demonstrateEventHandling();
    }
  };
}

// Example usage in a Vue component
export const exampleVueComponentUsage = `
<template>
  <div>
    <!-- Enhanced Debug Report Button with new features -->
    <DebugReportButtonEnhanced />
    
    <!-- Status indicators -->
    <div v-if="!connectionStore.isConnected" class="offline-indicator">
      ⚠️ You're offline. Reports will be saved locally.
    </div>
    
    <div v-if="offlineReports.hasOfflineReports" class="pending-reports">
      📤 {{ offlineReports.offlineReportCount }} reports pending submission
    </div>
    
    <div v-if="formDrafts.hasDrafts" class="draft-indicator">
      📝 {{ formDrafts.draftCount }} draft(s) saved
    </div>
  </div>
</template>

<script setup lang="ts">
import { useOfflineReports } from '@/composables/useOfflineReports';
import { useFormDrafts } from '@/composables/useFormDrafts';
import { useConnectionStore } from '@/stores/connection';
import DebugReportButtonEnhanced from '@/components/DebugReportButtonEnhanced.vue';

const offlineReports = useOfflineReports();
const formDrafts = useFormDrafts();
const connectionStore = useConnectionStore();
</script>
`;

// Example of manual offline report processing
export const manualOfflineProcessing = async () => {
  const offlineReports = useOfflineReports();
  
  // Check if we have offline reports and are online
  if (offlineReports.hasOfflineReports.value && offlineReports.isOnline.value) {
    console.log('Processing offline reports manually...');
    await offlineReports.processOfflineReports();
    console.log('Offline reports processed');
  }
};

// Example of manual draft management
export const manualDraftManagement = () => {
  const formDrafts = useFormDrafts();
  
  // Get all drafts
  const allDrafts = formDrafts.formDrafts.value;
  console.log('All drafts:', allDrafts);
  
  // Delete a specific draft
  if (allDrafts.length > 0) {
    const firstDraftId = allDrafts[0].id;
    formDrafts.deleteDraft(firstDraftId);
    console.log('Deleted draft:', firstDraftId);
  }
  
  // Clear active draft
  formDrafts.clearActiveDraft();
  console.log('Cleared active draft');
};
