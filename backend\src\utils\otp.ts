import { authenticator } from '@otplib/preset-default';

// Generate a new Base32 encoded secret for OTP using otplib's built-in generator
export function generateOtpSecret(): string {
  return authenticator.generateSecret();
}

// Generate a 6-digit OTP token from a secret
export function generateOtpToken(secret: string): string {
  // Ensure the secret is correctly formatted (Base32)
  // otplib handles this internally, but good practice to be aware
  authenticator.options = { step: 300, window: 1 }; // 5-minute validity (step=300s), check current token only (window=1)
  return authenticator.generate(secret);
}

// Verify an OTP token against a secret
export function verifyOtpToken(token: string, secret: string): boolean {
  try {
    authenticator.options = { step: 300, window: 1 }; // Match generation options
    return authenticator.verify({ token, secret });
  } catch (error) {
    console.error('OTP verification error:', error);
    return false;
  }
}
