import { createI18n } from 'vue-i18n'
import type { Language } from '@/stores/language'

// Import FA translations
import faAi from '@/locales/fa/ai.json'
import faApp from '@/locales/fa/app.json'
import faAuth from '@/locales/fa/auth.json'
import faChat from '@/locales/fa/chat.json'
import faCommon from '@/locales/fa/common.json'
import faConnection from '@/locales/fa/connection.json'
import faCurrency from '@/locales/fa/currency.json'
import faDebug from '@/locales/fa/debug.json'
import faDebugReport from '@/locales/fa/debugReport.json'
import faErrors from '@/locales/fa/errors.json'
import faHomeView from '@/locales/fa/homeView.json'
import faInterests from '@/locales/fa/interests.json'
import faLanding from '@/locales/fa/landing.json'
import faLanguage from '@/locales/fa/language.json'
import faLevel from '@/locales/fa/level.json'
import faNavigation from '@/locales/fa/navigation.json'
import faNotifications from '@/locales/fa/notifications.json'
import faOfferDetails from '@/locales/fa/offerDetails.json'
import faOfferForm from '@/locales/fa/offerForm.json'
import faOffers from '@/locales/fa/offers.json'
import faProfile from '@/locales/fa/profile.json'
import faStatus from '@/locales/fa/status.json'
import faTheme from '@/locales/fa/theme.json'
import faTransaction from '@/locales/fa/transaction.json'
import faTransactionFlow from '@/locales/fa/transactionFlow.json'
import faTransactionInfo from '@/locales/fa/transactionInfo.json'
import faCreateOffer from '@/locales/fa/createOffer.json'
import faCreateOfferSimplified from '@/locales/fa/createOfferSimplified.json'
import faBrowseOffers from '@/locales/fa/browseOffers.json'
import faTransactionTimeline from '@/locales/fa/transactionTimeline.json'
import faValidation from '@/locales/fa/validation.json'
import faVoice from '@/locales/fa/voice.json'
import faVoiceAnalysis from '@/locales/fa/voiceAnalysis.json'
import faMatches from '@/locales/fa/matches.json' // Add this line
import faTransactionalChat from '@/locales/fa/transactionalChat.json'
import faSystemMessages from '@/locales/fa/systemMessages.json'
import faPaymentMethodSelector from '@/locales/fa/paymentMethodSelector.json'
import faPaymentMethods from '@/locales/fa/paymentMethods.json'

// Import EN translations (only existing files)
import enAi from '@/locales/en/ai.json'
import enApp from '@/locales/en/app.json'
import enAuth from '@/locales/en/auth.json'
import enChat from '@/locales/en/chat.json'
import enCommon from '@/locales/en/common.json'
import enConnection from '@/locales/en/connection.json'
import enCurrency from '@/locales/en/currency.json'
import enDebug from '@/locales/en/debug.json'
import enDebugReport from '@/locales/en/debugReport.json'
import enErrors from '@/locales/en/errors.json'
import enHomeView from '@/locales/en/homeView.json'
import enInterests from '@/locales/en/interests.json'
import enLanding from '@/locales/en/landing.json'
import enLanguage from '@/locales/en/language.json'
import enLevel from '@/locales/en/level.json'
import enNavigation from '@/locales/en/navigation.json'
import enNotifications from '@/locales/en/notifications.json'
import enOfferDetails from '@/locales/en/offerDetails.json'
import enOfferForm from '@/locales/en/offerForm.json'
import enOffers from '@/locales/en/offers.json'
import enProfile from '@/locales/en/profile.json'
import enStatus from '@/locales/en/status.json'
import enTheme from '@/locales/en/theme.json'
import enCreateOffer from '@/locales/en/createOffer.json'
import enCreateOfferSimplified from '@/locales/en/createOfferSimplified.json'
import enBrowseOffers from '@/locales/en/browseOffers.json'
import enTransaction from '@/locales/en/transaction.json'
import enTransactionFlow from '@/locales/en/transactionFlow.json'
import enTransactionInfo from '@/locales/en/transactionInfo.json'
import enTransactionTimeline from '@/locales/en/transactionTimeline.json'
import enValidation from '@/locales/en/validation.json'
import enVoice from '@/locales/en/voice.json'
import enVoiceAnalysis from '@/locales/en/voiceAnalysis.json'
import enMatches from '@/locales/en/matches.json' // Add this line
import enTransactionalChat from '@/locales/en/transactionalChat.json'
import enSystemMessages from '@/locales/en/systemMessages.json'
import enPaymentMethodSelector from '@/locales/en/paymentMethodSelector.json'
import enPaymentMethods from '@/locales/en/paymentMethods.json'

// Combine all FA translations with proper namespacing
const fa = {
  ai: faAi,
  app: faApp,
  auth: faAuth,
  chat: faChat,
  common: faCommon,
  connection: faConnection,
  currency: faCurrency,
  debug: faDebug,
  debugReport: faDebugReport,
  errors: faErrors,
  homeView: faHomeView,
  interests: faInterests,
  landing: faLanding,
  language: faLanguage,
  level: faLevel,
  navigation: faNavigation,
  notifications: faNotifications,
  offerDetails: faOfferDetails,
  offerForm: faOfferForm,
  offers: faOffers,
  profile: faProfile,
  status: faStatus,
  theme: faTheme,
  transaction: faTransaction,
  transactionFlow: faTransactionFlow,
  transactionInfo: faTransactionInfo,
  transactionTimeline: faTransactionTimeline,
  createOffer: faCreateOffer,
  createOfferSimplified: faCreateOfferSimplified,
  browseOffers: faBrowseOffers,
  validation: faValidation,
  voice: faVoice,
  voiceAnalysis: faVoiceAnalysis,
  matches: faMatches, // Add this line
  transactionalChat: faTransactionalChat,
  systemMessages: faSystemMessages,
  paymentMethodSelector: faPaymentMethodSelector,
  paymentMethods: faPaymentMethods
}

// Combine all EN translations with proper namespacing
const en = {
  ai: enAi,
  app: enApp,
  auth: enAuth,
  chat: enChat,
  common: enCommon,
  connection: enConnection,
  currency: enCurrency,
  debug: enDebug,
  debugReport: enDebugReport,
  errors: enErrors,
  homeView: enHomeView,
  interests: enInterests,
  landing: enLanding,
  language: enLanguage,
  level: enLevel,
  navigation: enNavigation,
  notifications: enNotifications,
  offerDetails: enOfferDetails,
  offerForm: enOfferForm,
  offers: enOffers,
  profile: enProfile,
  status: enStatus,
  theme: enTheme,
  transaction: enTransaction,
  transactionFlow: enTransactionFlow,
  transactionInfo: enTransactionInfo,
  transactionTimeline: enTransactionTimeline,
  createOffer: enCreateOffer,
  createOfferSimplified: enCreateOfferSimplified,
  browseOffers: enBrowseOffers,
  validation: enValidation,
  voice: enVoice,
  voiceAnalysis: enVoiceAnalysis,
  matches: enMatches, // Add this line
  transactionalChat: enTransactionalChat,
  systemMessages: enSystemMessages,
  paymentMethodSelector: enPaymentMethodSelector,
  paymentMethods: enPaymentMethods
}

export type MessageSchema = typeof fa

const messages = {
  fa,
  en
} as const

// Create i18n instance
export const i18n = createI18n<[MessageSchema], Language>({
  legacy: false, // Use Composition API
  locale: 'fa', // Default language
  fallbackLocale: 'en',
  messages,
  globalInjection: true,
  silentTranslationWarn: true,
  silentFallbackWarn: true,
  warnHtmlMessage: false
})

export default i18n

// Export types for better TypeScript support
export type AvailableLocale = keyof typeof messages
export type LocaleMessage = typeof messages['fa']
