// Test script to verify the improved transaction lifecycle and offer availability
// Tests that offers remain available for matching until both users agree to terms

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testImprovedOfferLifecycle() {
  console.log('\n=== Testing Improved Offer Lifecycle ===');
  
  try {
    // 1. Get test users
    const users = await prisma.user.findMany({
      where: { email: { contains: 'test' } },
      take: 3
    });
    
    if (users.length < 3) {
      console.log('❌ Need at least 3 test users. Run seedTestUsers.ts first.');
      return;
    }
    
    const [user1, user2, user3] = users;
    console.log(`📊 Test users: ${user1.email}, ${user2.email}, ${user3.email}`);
    
    // 2. Create a BUY offer from user1
    const buyOffer = await prisma.offer.create({
      data: {
        userId: user1.id,
        type: 'BUY',
        currencyPair: 'CAD-IRR',
        amount: 1000,
        baseRate: 32000,
        status: 'ACTIVE',
        adjustmentForLowerRep: 0,
        adjustmentForHigherRep: 0
      }
    });
    
    console.log(`✅ Created BUY offer ${buyOffer.id} from ${user1.email}`);
    
    // 3. Create matching SELL offer from user2  
    const sellOffer = await prisma.offer.create({
      data: {
        userId: user2.id,
        type: 'SELL',
        currencyPair: 'CAD-IRR',
        amount: 1000,
        baseRate: 32000,
        status: 'ACTIVE',
        adjustmentForLowerRep: 0,
        adjustmentForHigherRep: 0
      }
    });
    
    console.log(`✅ Created SELL offer ${sellOffer.id} from ${user2.email}`);
    
    // 4. Create another matching SELL offer from user3
    const sellOffer2 = await prisma.offer.create({
      data: {
        userId: user3.id,
        type: 'SELL',
        currencyPair: 'CAD-IRR',
        amount: 1000,
        baseRate: 32000,
        status: 'ACTIVE',
        adjustmentForLowerRep: 0,
        adjustmentForHigherRep: 0
      }
    });
    
    console.log(`✅ Created second SELL offer ${sellOffer2.id} from ${user3.email}`);
    
    // 5. Test matching - should find both SELL offers
    console.log('\n--- Testing Matching Before Any Transactions ---');
    
    const matchingResult = await fetch(`http://localhost:3000/api/offers/${buyOffer.id}/match`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${await getAuthToken(user1.email)}`,
        'Content-Type': 'application/json'
      }
    });
    
    const matches = await matchingResult.json();
    console.log(`📊 Matches found: ${matches.data?.length || 0}`);
    
    if (matches.data?.length > 0) {
      console.log(`✅ User1's offer can match with ${matches.data.length} other offers`);
    }
    
    // 6. Accept first match (user1 accepts user2's offer)
    const firstMatchId = matches.data[0]?.matchId;
    if (firstMatchId) {
      console.log(`\n--- Accepting Match ${firstMatchId} ---`);
      
      const acceptResult = await fetch(`http://localhost:3000/api/matches/${firstMatchId}/accept`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${await getAuthToken(user1.email)}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`📊 User1 accept result: ${acceptResult.status}`);
      
      // User2 also accepts
      const acceptResult2 = await fetch(`http://localhost:3000/api/matches/${firstMatchId}/accept`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${await getAuthToken(user2.email)}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`📊 User2 accept result: ${acceptResult2.status}`);
      
      // 7. Check transaction was created
      const transaction = await prisma.transaction.findFirst({
        where: { 
          OR: [
            { currencyAProviderId: user1.id, currencyBProviderId: user2.id },
            { currencyAProviderId: user2.id, currencyBProviderId: user1.id }
          ]
        },
        orderBy: { createdAt: 'desc' }
      });
      
      if (transaction) {
        console.log(`✅ Transaction created: ${transaction.id}, Status: ${transaction.status}`);
        
        // 8. Verify offers are still ACTIVE (transaction is AWAITING_FIRST_PAYER_DESIGNATION)
        const updatedBuyOffer = await prisma.offer.findUnique({ where: { id: buyOffer.id } });
        const updatedSellOffer = await prisma.offer.findUnique({ where: { id: sellOffer.id } });
        
        console.log(`📊 Buy offer status: ${updatedBuyOffer?.status}`);
        console.log(`📊 Sell offer status: ${updatedSellOffer?.status}`);
        
        if (updatedBuyOffer?.status === 'ACTIVE' && updatedSellOffer?.status === 'ACTIVE') {
          console.log(`✅ CORRECT: Offers remain ACTIVE until both users agree to terms`);
        } else {
          console.log(`❌ INCORRECT: Offers should remain ACTIVE until commitment`);
        }
        
        // 9. Test matching again - user3's offer should still be able to match with user1's offer
        console.log(`\n--- Testing Matching After Transaction Created But Before Commitment ---`);
        
        const matchingResult2 = await fetch(`http://localhost:3000/api/offers/${buyOffer.id}/match`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${await getAuthToken(user1.email)}`,
            'Content-Type': 'application/json'
          }
        });
        
        const matches2 = await matchingResult2.json();
        console.log(`📊 New matches found: ${matches2.data?.length || 0}`);
        
        if (matches2.data?.length > 0) {
          console.log(`✅ CORRECT: User1's offer can still match with other offers`);
          
          // Check if user3's offer is in the matches
          const user3Match = matches2.data.find(match => 
            match.offerA.userId === user3.id || match.offerB.userId === user3.id
          );
          
          if (user3Match) {
            console.log(`✅ CORRECT: User3's offer is available for matching`);
          } else {
            console.log(`❌ INCORRECT: User3's offer should be available for matching`);
          }
        } else {
          console.log(`❌ INCORRECT: User1's offer should still be available for new matches`);
        }
        
        // 10. Simulate both users agreeing to terms
        console.log(`\n--- Simulating Both Users Agreeing to Terms ---`);
        
        // User1 agrees to terms
        const agreeResult1 = await fetch(`http://localhost:3000/api/transactions/${transaction.id}/agree-terms`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${await getAuthToken(user1.email)}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({})
        });
        
        console.log(`📊 User1 agree terms result: ${agreeResult1.status}`);
        
        // User2 agrees to terms
        const agreeResult2 = await fetch(`http://localhost:3000/api/transactions/${transaction.id}/agree-terms`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${await getAuthToken(user2.email)}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({})
        });
        
        console.log(`📊 User2 agree terms result: ${agreeResult2.status}`);
        
        // 11. Check transaction status changed
        const updatedTransaction = await prisma.transaction.findUnique({
          where: { id: transaction.id }
        });
        
        console.log(`📊 Updated transaction status: ${updatedTransaction?.status}`);
        
        if (updatedTransaction?.status === 'AWAITING_FIRST_PAYER_PAYMENT') {
          console.log(`✅ CORRECT: Transaction moved to payment phase after both agreed`);
          
          // 12. Verify offers are now COMPLETED
          const finalBuyOffer = await prisma.offer.findUnique({ where: { id: buyOffer.id } });
          const finalSellOffer = await prisma.offer.findUnique({ where: { id: sellOffer.id } });
          
          console.log(`📊 Final buy offer status: ${finalBuyOffer?.status}`);
          console.log(`📊 Final sell offer status: ${finalSellOffer?.status}`);
          
          if (finalBuyOffer?.status === 'COMPLETED' && finalSellOffer?.status === 'COMPLETED') {
            console.log(`✅ CORRECT: Offers marked as COMPLETED after both users committed`);
          } else {
            console.log(`❌ INCORRECT: Offers should be COMPLETED after commitment`);
          }
          
          // 13. Test matching again - should not find the committed offers
          console.log(`\n--- Testing Matching After Both Users Committed ---`);
          
          const matchingResult3 = await fetch(`http://localhost:3000/api/offers/${sellOffer2.id}/match`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${await getAuthToken(user3.email)}`,
              'Content-Type': 'application/json'
            }
          });
          
          const matches3 = await matchingResult3.json();
          console.log(`📊 Matches for user3 after commitment: ${matches3.data?.length || 0}`);
          
          // Should not find user1's or user2's offers since they're committed
          const hasCommittedOffers = matches3.data?.some(match => 
            [user1.id, user2.id].includes(match.offerA.userId) || 
            [user1.id, user2.id].includes(match.offerB.userId)
          );
          
          if (!hasCommittedOffers) {
            console.log(`✅ CORRECT: Committed offers are excluded from new matches`);
          } else {
            console.log(`❌ INCORRECT: Committed offers should not be available for matching`);
          }
        }
      }
    }
    
    console.log('\n=== Test Complete ===');
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
  } finally {
    // Cleanup
    try {
      await prisma.offer.deleteMany({
        where: {
          currencyPair: 'CAD-IRR',
          amount: 1000
        }
      });
      console.log('🧹 Cleaned up test offers');
    } catch (e) {
      console.log('⚠️ Cleanup error:', e.message);
    }
  }
}

// Helper function to get auth token
async function getAuthToken(email) {
  const response = await fetch('http://localhost:3000/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: email,
      password: 'password123'
    })
  });
  
  const result = await response.json();
  return result.data?.token;
}

// Run the test
testImprovedOfferLifecycle()
  .catch(console.error)
  .finally(() => prisma.$disconnect());
