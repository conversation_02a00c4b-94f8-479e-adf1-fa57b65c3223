import { describe, it, expect, beforeEach, vi } from 'vitest';
import { MatchingService } from '../../services/matchingService';
import { NotificationService } from '../../services/notificationService';

// Mock dependencies
const mockPrismaOfferFindUnique = vi.hoisted(() => vi.fn());
const mockPrismaOfferFindMany = vi.hoisted(() => vi.fn());
const mockPrismaOfferUpdate = vi.hoisted(() => vi.fn());
const mockPrismaOfferMatchCreate = vi.hoisted(() => vi.fn());
const mockPrismaOfferMatchFindMany = vi.hoisted(() => vi.fn());
const mockPrismaOfferMatchFindUnique = vi.hoisted(() => vi.fn());
const mockPrismaOfferMatchUpdate = vi.hoisted(() => vi.fn());
const mockPrismaOfferMatchUpdateMany = vi.hoisted(() => vi.fn());
const mockPrismaOfferMatchFindFirst = vi.hoisted(() => vi.fn());
const mockPrismaTransaction = vi.hoisted(() => vi.fn());

vi.mock('@prisma/client', () => {
  return {
    PrismaClient: vi.fn().mockImplementation(() => ({
      offer: {
        findUnique: mockPrismaOfferFindUnique,
        findMany: mockPrismaOfferFindMany,
        update: mockPrismaOfferUpdate,
      },
      offerMatch: {
        create: mockPrismaOfferMatchCreate,
        findMany: mockPrismaOfferMatchFindMany,
        findUnique: mockPrismaOfferMatchFindUnique,
        update: mockPrismaOfferMatchUpdate,
        updateMany: mockPrismaOfferMatchUpdateMany,
      },
      $transaction: mockPrismaTransaction,
    })),
    OfferType: {
      BUY: 'BUY',
      SELL: 'SELL',
    },
    OfferStatus: {
      ACTIVE: 'ACTIVE',
      COMPLETED: 'COMPLETED',
      CANCELLED: 'CANCELLED',
    },
    MatchStatus: {
      PENDING: 'PENDING',
      ACCEPTED: 'ACCEPTED',
      DECLINED: 'DECLINED',
      EXPIRED: 'EXPIRED',
    },
    MatchResponse: {
      PENDING: 'PENDING',
      ACCEPTED: 'ACCEPTED',
      DECLINED: 'DECLINED',
    },    NotificationType: {
      CHAT_MESSAGE_RECEIVED: 'CHAT_MESSAGE_RECEIVED',
    },
    TransactionStatus: {
      PENDING: 'PENDING',
      AGREEMENT_REACHED: 'AGREEMENT_REACHED',
      PAYMENT_DECLARED: 'PAYMENT_DECLARED',
      PAYMENT_CONFIRMED: 'PAYMENT_CONFIRMED',
      COMPLETED: 'COMPLETED',
      CANCELLED: 'CANCELLED',
      DISPUTED: 'DISPUTED',
    },
  };
});

// Mock Socket.IO
const mockSocketTo = vi.fn().mockReturnThis();
const mockSocketEmit = vi.fn();
const mockSocket = {
  to: mockSocketTo,
  emit: mockSocketEmit,
};

// Mock NotificationService
const mockCreateNotification = vi.fn();

// Use a proper mock for the NotificationService that includes all required methods
const mockNotificationService = {
  createNotification: mockCreateNotification,
  getNotificationsForUser: vi.fn(),
  markNotificationAsRead: vi.fn(),
  markAllNotificationsAsRead: vi.fn(),
} as any;

describe('MatchingService', () => {
  let matchingService: MatchingService;
  let mockPrisma: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Create mock prisma instance
    mockPrisma = {
      offer: {
        findUnique: mockPrismaOfferFindUnique,
        findMany: mockPrismaOfferFindMany,
        update: mockPrismaOfferUpdate,
      },
      offerMatch: {
        create: mockPrismaOfferMatchCreate,
        findMany: mockPrismaOfferMatchFindMany,
        findUnique: mockPrismaOfferMatchFindUnique,
        update: mockPrismaOfferMatchUpdate,
        updateMany: mockPrismaOfferMatchUpdateMany,
        findFirst: mockPrismaOfferMatchFindFirst,
      },
      $transaction: mockPrismaTransaction,
    };

    matchingService = new MatchingService(
      mockPrisma,
      mockSocket as any,
      mockNotificationService
    );
  });

  describe('findPotentialMatches', () => {
    it('should find exact rate matches for cross-currency pairs', async () => {
      const offerId = 'offer1';
      const mockSourceOffer = {
        id: offerId,
        userId: 'user1',
        type: 'SELL',
        currencyPair: 'CAD-IRR',
        amount: 1000,
        baseRate: 30000,
        adjustmentForLowerRep: 500,
        adjustmentForHigherRep: -200,
        status: 'ACTIVE',
        user: {
          id: 'user1',
          username: 'seller',
          email: '<EMAIL>',
          reputationLevel: 3,
        },
      };

      const mockCompatibleOffers = [
        {
          id: 'offer2',
          userId: 'user2',
          type: 'BUY',
          currencyPair: 'CAD-IRR',
          amount: 30000000, // 30M IRR
          baseRate: 30000, // Exact same rate
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: 'ACTIVE',
          user: {
            id: 'user2',
            username: 'buyer',
            email: '<EMAIL>',
            reputationLevel: 4,
          },
        },
      ];

      mockPrismaOfferFindUnique.mockResolvedValue(mockSourceOffer);
      mockPrismaOfferFindMany.mockResolvedValue(mockCompatibleOffers);
      mockPrismaOfferMatchCreate.mockResolvedValue({
        id: 'match1',
        offerAId: offerId,
        offerBId: 'offer2',
        userAId: 'user1',
        userBId: 'user2',
        status: 'PENDING',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        matchId: 'MATCH_20250608_NB2WJC',
        compatibilityScore: 1,
        matchCriteria: { type: 'exact_rate_match', timestamp: new Date().toISOString(), tolerance: 0.01 },
        rateAToB: 30000,
        rateBToA: 30000,
        offerA: mockSourceOffer,
        offerB: mockCompatibleOffers[0],
        userA: mockSourceOffer.user,
        userB: mockCompatibleOffers[0].user,
      });
      mockPrismaOfferMatchFindFirst.mockResolvedValue(null);

      const matches = await matchingService.findPotentialMatches(offerId);      expect(matches).toHaveLength(1);
      expect(mockPrismaOfferFindUnique).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: offerId },
          include: expect.any(Object),
        })
      );
      expect(mockPrismaOfferFindMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: 'ACTIVE',
            type: 'BUY',
            currencyPair: 'CAD-IRR',
            userId: { not: 'user1' },
            amount: 1000,
            baseRate: {
              gte: expect.any(Number),
              lte: expect.any(Number),
            },
          }),
          include: expect.any(Object),
        })
      );
    });

    it('should return empty array if source offer not found', async () => {
      mockPrismaOfferFindUnique.mockResolvedValue(null);

      const matches = await matchingService.findPotentialMatches('nonexistent');

      expect(matches).toEqual([]);
      expect(mockPrismaOfferFindMany).not.toHaveBeenCalled();
    });    it('should filter out offers from the same user', async () => {
      const mockSourceOffer = {
        id: 'offer1',
        userId: 'user1',
        type: 'SELL',
        currencyPair: 'CAD-IRR',
        baseRate: 30000,
        amount: 1000,
        status: 'ACTIVE',
        user: { id: 'user1', username: 'test', email: '<EMAIL>', reputationLevel: 3 },
      };

      mockPrismaOfferFindUnique.mockResolvedValue(mockSourceOffer);
      mockPrismaOfferFindMany.mockResolvedValue([]);

      await matchingService.findPotentialMatches('offer1');

      expect(mockPrismaOfferFindMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: 'ACTIVE',
            type: 'BUY',
            currencyPair: 'CAD-IRR',
            userId: { not: 'user1' },
            amount: 1000,
            baseRate: {
              gte: expect.any(Number),
              lte: expect.any(Number),
            },
          }),
          include: expect.any(Object),
        })
      );
    });    it('should handle BUY offers correctly', async () => {
      const mockSourceOffer = {
        id: 'offer1',
        userId: 'user1',
        type: 'BUY',
        currencyPair: 'CAD-IRR',
        baseRate: 30000,
        amount: 1000,
        status: 'ACTIVE',
        user: { id: 'user1', username: 'test', email: '<EMAIL>', reputationLevel: 3 },
      };

      mockPrismaOfferFindUnique.mockResolvedValue(mockSourceOffer);
      mockPrismaOfferFindMany.mockResolvedValue([]);

      await matchingService.findPotentialMatches('offer1');

      // Should look for SELL offers with CAD-IRR pair
      expect(mockPrismaOfferFindMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: 'ACTIVE',
            type: 'SELL',
            currencyPair: 'CAD-IRR',
            userId: { not: 'user1' },
            amount: 1000,
            baseRate: {
              gte: expect.any(Number),
              lte: expect.any(Number),
            },
          }),
          include: expect.any(Object),
        })
      );
    });    it('should handle non-cross-currency pairs', async () => {
      const mockSourceOffer = {
        id: 'offer1',
        userId: 'user1',
        type: 'SELL',
        currencyPair: 'USD-EUR',
        baseRate: 1.1,
        amount: 1000,
        status: 'ACTIVE',
        user: { id: 'user1', username: 'test', email: '<EMAIL>', reputationLevel: 3 },
      };

      mockPrismaOfferFindUnique.mockResolvedValue(mockSourceOffer);
      mockPrismaOfferFindMany.mockResolvedValue([]);

      await matchingService.findPotentialMatches('offer1');

      // Should look for BUY offers with USD-EUR pair (reverse)
      expect(mockPrismaOfferFindMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: 'ACTIVE',
            type: 'BUY',
            currencyPair: 'USD-EUR',
            userId: { not: 'user1' },
            amount: 1000,
            baseRate: {
              gte: expect.any(Number),
              lte: expect.any(Number),
            },
          }),
          include: expect.any(Object),
        })
      );
    });
  });

  describe('createMatch', () => {
    it('should create a match between two compatible offers', async () => {
      const offerA = {
        id: 'offer1',
        userId: 'user1',
        type: 'SELL',
        currencyPair: 'CAD-IRR',
        amount: 1000,
        baseRate: 30000,
        user: { id: 'user1', username: 'seller', email: '<EMAIL>' },
      };

      const offerB = {
        id: 'offer2',
        userId: 'user2',
        type: 'BUY',
        currencyPair: 'CAD-IRR',
        amount: 30000000,
        baseRate: 30000,
        user: { id: 'user2', username: 'buyer', email: '<EMAIL>' },
      };      const mockCreatedMatch = {
        id: 'match1',
        offerAId: 'offer1',
        offerBId: 'offer2',
        userAId: 'user1',
        userBId: 'user2',
        currencyA: 'CAD',
        currencyB: 'IRR',
        amountA: 1000,
        amountB: 30000000,
        rate: 30000,
        status: 'PENDING',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        matchId: 'MATCH_20250608_TEST',
        compatibilityScore: 1,
        matchCriteria: { type: 'exact_rate_match', timestamp: new Date().toISOString(), tolerance: 0.01 },
        rateAToB: 30000,
        rateBToA: 30000,
        userA: offerA.user,
        userB: offerB.user,
        offerA: {
          ...offerA,
          user: offerA.user,
        },
        offerB: {
          ...offerB,
          user: offerB.user,
        },
      };

      mockPrismaOfferMatchCreate.mockResolvedValue(mockCreatedMatch);
      mockCreateNotification.mockResolvedValue({});

      const match = await matchingService.createMatch(offerA, offerB);      expect(match).toEqual(mockCreatedMatch);
      expect(mockPrismaOfferMatchCreate).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            offerAId: 'offer1',
            offerBId: 'offer2',
            userAId: 'user1',
            userBId: 'user2',
            currencyA: 'CAD',
            currencyB: 'IRR',
            amountA: 1000,
            amountB: 30000000,
            rateAToB: expect.any(Number),
            rateBToA: expect.any(Number),
            matchId: expect.any(String),
            compatibilityScore: expect.any(Number),
            expiresAt: expect.any(Date),
            matchCriteria: expect.any(Object),
          }),
          include: expect.any(Object),
        })
      );      // Should emit socket events to both users
      expect(mockSocketTo).toHaveBeenCalledWith('user_user1');
      expect(mockSocketTo).toHaveBeenCalledWith('user_user2');
      expect(mockSocketEmit).toHaveBeenCalledTimes(2);

      // Should create notifications for both users
      expect(mockCreateNotification).toHaveBeenCalledTimes(2);
    });
  });

  describe('acceptMatch', () => {    it('should successfully accept a match and create transaction/chat', async () => {
      const matchId = 'match1';
      const userId = 'user2';
      const mockMatch = {
        id: matchId,
        userAId: 'user1',
        userBId: 'user2',
        status: 'PENDING',
        userAResponse: 'PENDING',
        userBResponse: null, // userBResponse is null initially
        offerAId: 'offer1',
        offerBId: 'offer2',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        userA: { id: 'user1', username: 'userA', email: '<EMAIL>' },
        userB: { id: 'user2', username: 'userB', email: '<EMAIL>' },
        offerA: { id: 'offer1', currencyPair: 'CAD-IRR', user: { id: 'user1', username: 'userA', email: '<EMAIL>' } },
        offerB: { id: 'offer2', currencyPair: 'CAD-IRR', user: { id: 'user2', username: 'userB', email: '<EMAIL>' } },
      };

      const mockPartialAcceptMatch = {
        ...mockMatch,
        userBResponse: 'ACCEPTED',
        status: 'PARTIAL_ACCEPT',
      };

      const mockFinalMatch = {
        ...mockMatch,
        userBResponse: 'ACCEPTED',
        userAResponse: 'ACCEPTED',
        status: 'BOTH_ACCEPTED',
      };

      // Mock transaction callback with proper transaction context
      let updateCallCount = 0;
      mockPrismaTransaction.mockImplementation(async (callback) => {
        const txMock = {
          offerMatch: {
            findUnique: vi.fn()
              .mockResolvedValueOnce(mockMatch) // Initial findUnique
              .mockResolvedValueOnce(mockFinalMatch), // Final findUnique after conversion
            update: vi.fn()
              .mockImplementation(() => {
                updateCallCount++;
                if (updateCallCount === 1) {
                  // First update: user B accepts
                  return Promise.resolve(mockPartialAcceptMatch);
                } else {
                  // Second update: status to BOTH_ACCEPTED
                  return Promise.resolve(mockFinalMatch);
                }
              }),
          },
          offer: {
            update: mockPrismaOfferUpdate,
          },
          chatSession: {
            create: vi.fn().mockResolvedValue({ id: 'chat1' }),
          },
          transaction: {
            create: vi.fn().mockResolvedValue({ id: 'transaction1' }),
          },
        };
        return await callback(txMock);
      });      const result = await matchingService.acceptMatch(matchId, userId);

      expect(result.match).toBeDefined();
      expect(result.match).not.toBeNull();
      if (result.match) {
        expect(result.match.userBResponse).toBe('ACCEPTED');
      }
      expect(mockPrismaTransaction).toHaveBeenCalled();
    });

    it('should throw error if match not found', async () => {
      mockPrismaTransaction.mockImplementation(async (callback) => {
        return await callback({
          offerMatch: {
            findUnique: mockPrismaOfferMatchFindUnique,
          },
        });
      });

      mockPrismaOfferMatchFindUnique.mockResolvedValue(null);

      await expect(matchingService.acceptMatch('nonexistent', 'user1'))
        .rejects.toThrow('Match not found');
    });

    it('should throw error if user not authorized', async () => {
      const mockMatch = {
        id: 'match1',
        userAId: 'user1',
        userBId: 'user2',
        status: 'PENDING',
      };

      mockPrismaTransaction.mockImplementation(async (callback) => {
        return await callback({
          offerMatch: {
            findUnique: mockPrismaOfferMatchFindUnique,
          },
        });
      });

      mockPrismaOfferMatchFindUnique.mockResolvedValue(mockMatch);

      await expect(matchingService.acceptMatch('match1', 'user3'))
        .rejects.toThrow('Unauthorized to accept this match');
    });

    it('should throw error if already responded', async () => {
      const mockMatch = {
        id: 'match1',
        userAId: 'user1',
        userBId: 'user2',
        status: 'PENDING',
        userBResponse: 'ACCEPTED', // Already accepted
      };

      mockPrismaTransaction.mockImplementation(async (callback) => {
        return await callback({
          offerMatch: {
            findUnique: mockPrismaOfferMatchFindUnique,
          },
        });
      });

      mockPrismaOfferMatchFindUnique.mockResolvedValue(mockMatch);

      await expect(matchingService.acceptMatch('match1', 'user2'))
        .rejects.toThrow('You have already responded to this match');
    });
  });

  describe('declineMatch', () => {
    it('should successfully decline a match', async () => {
      const matchId = 'match1';
      const userId = 'user2';
      const reason = 'Changed my mind';
      
      const mockMatch = {
        id: matchId,
        userAId: 'user1',
        userBId: 'user2',
        status: 'PENDING',
        userBResponse: 'PENDING',
        userA: { id: 'user1', username: 'userA', email: '<EMAIL>' },
        userB: { id: 'user2', username: 'userB', email: '<EMAIL>' },
      };      const mockUpdatedMatch = {
        ...mockMatch,
        userBResponse: 'DECLINED',
        status: 'DECLINED',
        declineReason: reason,
        declinedByUserId: userId,
        updatedAt: expect.any(Date),
      };

      mockPrismaOfferMatchFindUnique.mockResolvedValue(mockMatch);
      mockPrismaOfferMatchUpdate.mockResolvedValue(mockUpdatedMatch);

      const result = await matchingService.declineMatch(matchId, userId, reason);

      expect(result.userBResponse).toBe('DECLINED');
      expect(result.declineReason).toBe(reason);      expect(mockPrismaOfferMatchUpdate).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: matchId },
          data: expect.objectContaining({
            status: 'DECLINED',
            declineReason: reason,
            declinedByUserId: userId,
            updatedAt: expect.any(Date),
          }),
          include: expect.any(Object),
        })
      );
    });

    it('should throw error if match not found', async () => {
      mockPrismaOfferMatchFindUnique.mockResolvedValue(null);

      await expect(matchingService.declineMatch('nonexistent', 'user1'))
        .rejects.toThrow('Match not found');
    });
  });

  describe('getUserMatches', () => {
    it('should retrieve all matches for a user', async () => {
      const userId = 'user1';
      const mockMatches = [
        {
          id: 'match1',
          userAId: 'user1',
          userBId: 'user2',
          status: 'PENDING',
          createdAt: new Date(),
        },
        {
          id: 'match2',
          userAId: 'user3',
          userBId: 'user1',
          status: 'ACCEPTED',
          createdAt: new Date(),
        },
      ];

      mockPrismaOfferMatchFindMany.mockResolvedValue(mockMatches);

      const matches = await matchingService.getUserMatches(userId);

      expect(matches).toEqual(mockMatches);
      expect(mockPrismaOfferMatchFindMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: [
              { userAId: userId },
              { userBId: userId },
            ],
          }),
          include: expect.any(Object),
          orderBy: { createdAt: 'desc' },
        })
      );
    });

    it('should filter matches by status when provided', async () => {
      const userId = 'user1';
      const status = 'PENDING';

      mockPrismaOfferMatchFindMany.mockResolvedValue([]);

      await matchingService.getUserMatches(userId, status as any);

      expect(mockPrismaOfferMatchFindMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: [
              { userAId: userId },
              { userBId: userId },
            ],
            status: status,
          }),
          include: expect.any(Object),
          orderBy: { createdAt: 'desc' },
        })
      );
    });
  });

  describe('cleanupExpiredMatches', () => {
    it('should mark expired matches and emit events', async () => {
      const expiredMatches = [
        {
          id: 'match1',
          userAId: 'user1',
          userBId: 'user2',
          status: 'PENDING',
          createdAt: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 hours ago
          userA: { id: 'user1', username: 'userA', email: '<EMAIL>' },
          userB: { id: 'user2', username: 'userB', email: '<EMAIL>' },
        },
      ];

      mockPrismaOfferMatchFindMany.mockResolvedValue(expiredMatches);
      mockPrismaOfferMatchUpdate.mockResolvedValue({});

      const result = await matchingService.cleanupExpiredMatches();

      expect(result).toBe(1);
      expect(mockPrismaOfferMatchFindMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.any(Object),
          include: expect.any(Object),
        })
      );

      expect(mockPrismaOfferMatchUpdate).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: 'match1' },
          data: expect.objectContaining({
            status: 'EXPIRED',
          }),
        })
      );
    });
  });
});
