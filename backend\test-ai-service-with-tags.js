const axios = require('axios');

// Test the complete AI flow with predefined tags
async function testAiServiceWithPredefinedTags() {
  console.log('🧪 Testing AI Service with Predefined Tags...\n');

  const baseUrl = 'http://localhost:3000';
  
  // Sample predefined tags
  const predefinedTags = {
    bug: ['ui-layout', 'performance', 'authentication', 'navigation'],
    feature: ['enhancement', 'new-functionality', 'user-experience'],
    question: ['how-to', 'configuration', 'troubleshooting'],
    performance: ['slow-loading', 'memory-usage', 'api-response']
  };

  // Test 1: AI Analysis with predefined tags
  console.log('📝 Test 1: AI Analysis with predefined tags');
  try {
    const analysisResponse = await axios.post(`${baseUrl}/api/ai/analyze`, {
      transcription: 'The login button is not working properly when I click it. It seems slow and sometimes doesnt respond.',
      language: 'en',
      userContext: {
        currentPage: '/login',
        userAgent: 'test-browser'
      },
      predefinedTags: predefinedTags
    });    if (analysisResponse.data.success) {
      console.log('✅ Analysis successful');
      const report = analysisResponse.data.generatedReport;
      console.log(`   Report Type: ${report.suggestedType || 'undefined'}`);
      console.log(`   Title: ${report.title}`);
      console.log(`   Full Report:`, JSON.stringify(report, null, 2));
      
      if (report.suggestedTags && Array.isArray(report.suggestedTags)) {
        console.log(`   Tags:`, report.suggestedTags.map(t => `"${t.tag}" (${t.origin})`).join(', '));
        
        // Verify tag origins
        const predefinedTagsUsed = report.suggestedTags.filter(t => t.origin === 'PREDEFINED');
        const aiTagsUsed = report.suggestedTags.filter(t => t.origin === 'AI_SUGGESTED');
        console.log(`   Predefined tags used: ${predefinedTagsUsed.length}`);
        console.log(`   AI-suggested tags used: ${aiTagsUsed.length}`);
      } else {
        console.log('   Tags: None or invalid format');
      }
    } else {
      console.log('❌ Analysis failed:', analysisResponse.data.error);
    }
  } catch (error) {
    console.log('❌ Analysis request failed:', error.response?.data || error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Voice-to-Report with predefined tags (using mock audio data)
  console.log('🎤 Test 2: Voice-to-Report with predefined tags');
  try {
    // Create a minimal base64 audio buffer (this would normally be real audio)
    const mockAudioData = Buffer.from('mock audio data').toString('base64');
    
    const voiceResponse = await axios.post(`${baseUrl}/api/ai/voice-to-report`, {
      audioData: mockAudioData,
      mimeType: 'audio/webm',
      duration: 5,
      language: 'en',
      userContext: {
        currentPage: '/offers',
        userAgent: 'test-browser'
      },
      predefinedTags: predefinedTags
    });    if (voiceResponse.data.success) {
      console.log('✅ Voice-to-report successful');
      const report = voiceResponse.data.generatedReport;
      console.log(`   Report Type: ${report.suggestedType || 'undefined'}`);
      console.log(`   Title: ${report.title}`);
      console.log(`   Full Report:`, JSON.stringify(report, null, 2));
      
      if (report.suggestedTags && Array.isArray(report.suggestedTags)) {
        console.log(`   Tags:`, report.suggestedTags.map(t => `"${t.tag}" (${t.origin})`).join(', '));
        
        // Verify tag origins
        const predefinedTagsUsed = report.suggestedTags.filter(t => t.origin === 'PREDEFINED');
        const aiTagsUsed = report.suggestedTags.filter(t => t.origin === 'AI_SUGGESTED');
        console.log(`   Predefined tags used: ${predefinedTagsUsed.length}`);
        console.log(`   AI-suggested tags used: ${aiTagsUsed.length}`);
      } else {
        console.log('   Tags: None or invalid format');
      }
    } else {
      console.log('❌ Voice-to-report failed:', voiceResponse.data.error);
    }
  } catch (error) {
    console.log('❌ Voice-to-report request failed:', error.response?.data || error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Verify AI service status
  console.log('🔍 Test 3: AI Service Status');
  try {
    const statusResponse = await axios.get(`${baseUrl}/api/ai/status`);
    
    if (statusResponse.data.success) {
      console.log('✅ AI service status retrieved');
      const status = statusResponse.data.status;
      console.log(`   AI Service Available: ${status.aiServiceAvailable}`);
      console.log(`   Transcription Available: ${status.transcriptionServiceAvailable}`);
      console.log(`   Voice-to-Report Available: ${status.features.voiceToReport}`);
    } else {
      console.log('❌ Status check failed:', statusResponse.data.error);
    }
  } catch (error) {
    console.log('❌ Status request failed:', error.response?.data || error.message);
  }

  console.log('\n✨ AI Service test completed!');
}

// Run the test
testAiServiceWithPredefinedTags().catch(console.error);
