<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Offer Mobile Test - MUNygo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 0.5rem;
            color: #333;
        }

        .test-container {
            max-width: 100%;
            width: 100%;
            margin: 0 auto;
            padding: 0.5rem 0;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 1rem;
            color: white;
            padding: 0 0.5rem;
            animation: fadeInUp 0.8s ease forwards;
        }

        .hero-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.3;
        }

        .hero-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
            line-height: 1.5;
            padding: 0 1rem;
        }

        .form-container {
            padding: 0 0.5rem;
            animation: fadeInUp 0.8s ease forwards;
        }

        .form-card {
            border-radius: 12px;
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            margin: 0;
            padding: 1rem;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        .form-section {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: #333;
        }

        .radio-group {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.75rem;
            width: 100%;
        }

        .radio-option {
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            touch-action: manipulation;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .radio-option:hover {
            border-color: #2080f0;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(32, 128, 240, 0.2);
        }

        .radio-option.active {
            border-color: #2080f0;
            background: rgba(32, 128, 240, 0.1);
            transform: scale(1.02);
        }

        .radio-content h3 {
            margin: 0.25rem 0 0.125rem 0;
            font-size: 1rem;
            font-weight: 600;
            color: #333;
        }

        .radio-content p {
            margin: 0;
            font-size: 0.8rem;
            color: #666;
            line-height: 1.3;
        }

        .input-container {
            position: relative;
            width: 100%;
        }

        .form-input {
            width: 100%;
            min-height: 44px;
            padding: 0.75rem;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #2080f0;
            box-shadow: 0 0 0 3px rgba(32, 128, 240, 0.1);
        }

        .currency-prefix {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            font-weight: 600;
            color: #18a058;
            font-size: 1rem;
            pointer-events: none;
        }

        .prefixed-input {
            padding-left: 4rem;
        }

        .quick-amounts {
            display: flex;
            flex-wrap: wrap;
            gap: 0.375rem;
            width: 100%;
            margin-top: 0.5rem;
        }

        .quick-amount-btn {
            min-height: 32px;
            font-size: 0.8rem;
            flex: 1;
            min-width: calc(50% - 0.2rem);
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-amount-btn:hover {
            background: #e0e0e0;
            border-color: #2080f0;
        }

        .rate-helper {
            margin-top: 0.75rem;
            padding: 0.75rem;
            background: rgba(32, 128, 240, 0.1);
            border-radius: 6px;
            font-size: 0.85rem;
        }

        .calculation-line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            margin-bottom: 0.375rem;
        }

        .total-amount {
            color: #18a058;
            font-size: 1rem;
            font-weight: 600;
        }

        .submit-section {
            margin-top: 1.5rem;
            padding: 0 0.5rem;
        }

        .submit-btn {
            height: 48px;
            width: 100%;
            background: linear-gradient(135deg, #2080f0, #1668dc);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            touch-action: manipulation;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(32, 128, 240, 0.3);
        }

        .submit-btn:active {
            transform: scale(0.98);
        }

        .helper-text {
            text-align: center;
            margin-top: 0.75rem;
            padding: 0 0.5rem;
            font-size: 0.8rem;
            line-height: 1.4;
            color: rgba(255, 255, 255, 0.9);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (min-width: 768px) {
            .test-container {
                max-width: 600px;
                padding: 1rem 0;
            }
            
            .hero-section {
                margin-bottom: 1.5rem;
                padding: 0;
            }
            
            .hero-title {
                font-size: 1.75rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
                padding: 0;
            }
            
            .radio-group {
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
            }
            
            .form-card {
                padding: 1.5rem;
                border-radius: 16px;
            }
            
            .form-section {
                margin-bottom: 1.25rem;
            }
            
            .form-container {
                padding: 0;
            }
            
            .submit-section {
                margin-top: 2rem;
                padding: 0;
            }
        }

        @media (min-width: 1024px) {
            .test-container {
                padding: 2rem 0;
            }
            
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1.125rem;
            }
            
            .form-card {
                padding: 2rem;
                border-radius: 20px;
            }
            
            .form-section {
                margin-bottom: 1.5rem;
            }
        }

        /* Device simulation styles */
        .device-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 0.5rem;
            border-radius: 6px;
            font-size: 0.8rem;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="device-info" id="deviceInfo">Mobile View</div>
    
    <div class="test-container">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="hero-icon">💱</div>
            <h1 class="hero-title">Create New Offer</h1>
            <p class="hero-subtitle">Create a currency exchange offer and connect with other users</p>
        </div>

        <!-- Form -->
        <div class="form-container">
            <div class="form-card">
                <div class="card-header">
                    <span>💰</span>
                    <span>Offer Details</span>
                </div>

                <!-- Offer Type -->
                <div class="form-section">
                    <label class="form-label">What do you want to do?</label>
                    <div class="radio-group">
                        <div class="radio-option active" onclick="selectOption(this, 'sell')">
                            <div class="radio-content">
                                <div style="font-size: 2rem; margin-bottom: 0.25rem;">📤</div>
                                <h3>Sell CAD</h3>
                                <p>Sell Canadian Dollars for Iranian Rial</p>
                            </div>
                        </div>
                        <div class="radio-option" onclick="selectOption(this, 'buy')">
                            <div class="radio-content">
                                <div style="font-size: 2rem; margin-bottom: 0.25rem;">📥</div>
                                <h3>Buy CAD</h3>
                                <p>Buy Canadian Dollars with Iranian Rial</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Amount -->
                <div class="form-section">
                    <label class="form-label">Amount (CAD)</label>
                    <div class="input-container">
                        <span class="currency-prefix">CAD $</span>
                        <input type="number" class="form-input prefixed-input" placeholder="Enter amount" value="1000" min="0.01" max="10000" step="50">
                    </div>
                    <div class="quick-amounts">
                        <button class="quick-amount-btn" onclick="setAmount(500)">$500</button>
                        <button class="quick-amount-btn" onclick="setAmount(1000)">$1000</button>
                        <button class="quick-amount-btn" onclick="setAmount(2000)">$2000</button>
                        <button class="quick-amount-btn" onclick="setAmount(5000)">$5000</button>
                    </div>
                </div>

                <!-- Exchange Rate -->
                <div class="form-section">
                    <label class="form-label">Exchange Rate</label>
                    <div class="input-container">
                        <input type="number" class="form-input" placeholder="Rate per CAD" value="50000" min="1" max="100000">
                        <span style="position: absolute; right: 0.75rem; top: 50%; transform: translateY(-50%); font-size: 0.8rem; color: #666;">IRR per CAD</span>
                    </div>
                    
                    <div class="rate-helper">
                        <div class="calculation-line">
                            <span>Total Value:</span>
                            <strong class="total-amount">50,000,000 IRR</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit -->
        <div class="submit-section">
            <button class="submit-btn" onclick="createOffer()">
                ✨ Create Offer
            </button>
            <div class="helper-text">
                Your offer will be visible to other users and they can express interest
            </div>
        </div>
    </div>

    <script>
        function updateDeviceInfo() {
            const width = window.innerWidth;
            const info = document.getElementById('deviceInfo');
            if (width < 768) {
                info.textContent = `Mobile: ${width}px`;
            } else if (width < 1024) {
                info.textContent = `Tablet: ${width}px`;
            } else {
                info.textContent = `Desktop: ${width}px`;
            }
        }

        function selectOption(element, type) {
            // Remove active from all options
            document.querySelectorAll('.radio-option').forEach(opt => {
                opt.classList.remove('active');
            });
            // Add active to clicked option
            element.classList.add('active');
            console.log('Selected:', type);
        }

        function setAmount(amount) {
            const input = document.querySelector('.prefixed-input');
            input.value = amount;
            updateCalculation();
        }

        function updateCalculation() {
            const amount = parseFloat(document.querySelector('.prefixed-input').value) || 0;
            const rate = parseFloat(document.querySelector('input[placeholder="Rate per CAD"]').value) || 0;
            const total = amount * rate;
            
            document.querySelector('.total-amount').textContent = 
                new Intl.NumberFormat('en-US').format(total) + ' IRR';
        }

        function createOffer() {
            // Simulate button feedback
            const btn = document.querySelector('.submit-btn');
            btn.style.transform = 'scale(0.95)';
            btn.textContent = '⏳ Creating...';
            
            setTimeout(() => {
                btn.style.transform = '';
                btn.textContent = '✅ Offer Created!';
                btn.style.background = 'linear-gradient(135deg, #18a058, #16844e)';
                
                setTimeout(() => {
                    btn.textContent = '✨ Create Offer';
                    btn.style.background = 'linear-gradient(135deg, #2080f0, #1668dc)';
                }, 2000);
            }, 1000);
        }

        // Event listeners
        window.addEventListener('resize', updateDeviceInfo);
        updateDeviceInfo();

        // Add input listeners for real-time calculation
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', updateCalculation);
        });

        // Initial calculation
        updateCalculation();

        console.log('✅ Create Offer Mobile Test Ready!');
        console.log('📱 Test the responsive behavior by resizing the window');
        console.log('🎯 Key mobile features:');
        console.log('  - Touch-friendly 44px+ targets');
        console.log('  - Mobile-first responsive design');
        console.log('  - Thumb-zone optimized layout');
        console.log('  - Progressive enhancement for larger screens');
    </script>
</body>
</html>
