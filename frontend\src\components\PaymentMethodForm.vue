<template>
  <div class="payment-method-form">
    <NForm
      ref="formRef"
      :model="formModel"
      :rules="rules"
      @submit.prevent="handleSubmit"
      label-placement="top"
    >
      <!-- Currency Field (auto-populated and read-only) -->
      <NFormItem :label="t('paymentMethodSelector.form.currency')" path="currency" size="small">
        <NInput
          v-model:value="formModel.currency"
          readonly
          :placeholder="t('paymentMethodSelector.form.currencyPlaceholder', { currency: formModel.currency || 'unknown currency' })"
          size="small"
          data-testid="currency-input"
        />
      </NFormItem>

      <!-- Basic Information -->
      <NGrid :cols="1" :x-gap="12" :y-gap="12">
        <NGi>
          <NFormItem :label="t('paymentMethodSelector.form.bankName')" path="bankName" size="small">
            <NInput
              v-model:value="formModel.bankName"
              :placeholder="t('paymentMethodSelector.form.bankNamePlaceholder')"
              size="small"
              data-testid="bank-name-input"
            />
          </NFormItem>
        </NGi>

        <NGi>
          <NFormItem :label="t('paymentMethodSelector.form.accountNumber')" path="accountNumber" size="small">
            <NInput
              v-model:value="formModel.accountNumber"
              :placeholder="t('paymentMethodSelector.form.accountNumberPlaceholder')"
              type="text"
              size="small"
              data-testid="account-number-input"
            />
          </NFormItem>
        </NGi>

        <NGi>
          <NFormItem :label="t('paymentMethodSelector.form.accountHolderName')" path="accountHolderName" size="small">
            <NInput
              v-model:value="formModel.accountHolderName"
              :placeholder="t('paymentMethodSelector.form.accountHolderNamePlaceholder')"
              size="small"
              data-testid="account-holder-name-input"
            />
          </NFormItem>
        </NGi>
      </NGrid>

      <!-- Form Actions -->
      <div class="form-actions" style="margin-top: 16px; padding-top: 16px;">
        <NSpace justify="end" size="small">
          <NButton
            @click="$emit('cancel')"
            :disabled="loading"
            size="small"
            data-testid="cancel-btn"
          >
            {{ t('paymentMethodSelector.form.cancel') }}
          </NButton>
          <NButton
            type="primary"
            attr-type="submit"
            :loading="loading"
            size="small"
            data-testid="submit-btn"
          >
            {{ method ? t('paymentMethodSelector.form.update') : t('paymentMethodSelector.form.create') }}
          </NButton>
        </NSpace>
      </div>
    </NForm>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NSpace,
  NGrid,
  NGi,
  type FormInst,
  type FormRules
} from 'naive-ui';
import { useI18n } from 'vue-i18n';
import type { PaymentMethodWithValidation } from '@/types/paymentMethods';

// Props and Emits
interface Props {
  method?: PaymentMethodWithValidation | null;
  currency: string;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  method: null,
  loading: false
});

const emit = defineEmits<{
  submit: [data: any];
  cancel: [];
}>();

// Composables
const { t } = useI18n();

// Form ref and model
const formRef = ref<FormInst | null>(null);

// Form model
const formModel = ref({
  currency: props.currency,
  paymentMethodType: 'BANK_TRANSFER', // Default to bank transfer
  bankName: '',
  accountNumber: '',
  accountHolderName: '',
  isActive: true
});

// Form validation rules
const rules: FormRules = {
  bankName: [
    { required: true, message: t('paymentMethodSelector.form.validation.bankNameRequired'), trigger: ['blur', 'input'] },
    { min: 1, max: 100, message: t('paymentMethodSelector.form.validation.bankNameLength'), trigger: ['blur', 'input'] }
  ],
  accountNumber: [
    { required: true, message: t('paymentMethodSelector.form.validation.accountNumberRequired'), trigger: ['blur', 'input'] },
    { min: 1, max: 50, message: t('paymentMethodSelector.form.validation.accountNumberLength'), trigger: ['blur', 'input'] }
  ],
  accountHolderName: [
    { required: true, message: t('paymentMethodSelector.form.validation.accountHolderNameRequired'), trigger: ['blur', 'input'] },
    { min: 1, max: 100, message: t('paymentMethodSelector.form.validation.accountHolderNameLength'), trigger: ['blur', 'input'] }
  ]
};

// Methods
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    
    // Prepare the data for submission
    const submitData = {
      currency: formModel.value.currency.toUpperCase(),
      paymentMethodType: formModel.value.paymentMethodType,
      bankName: formModel.value.bankName.trim(),
      accountNumber: formModel.value.accountNumber.trim(),
      accountHolderName: formModel.value.accountHolderName.trim(),
      ...(props.method && { isActive: formModel.value.isActive })
    };

    emit('submit', submitData);
  } catch (error) {
    // Form validation failed
    console.error('Form validation failed:', error);
  }
};

// Initialize form when editing
const initializeForm = () => {
  if (props.method) {
    formModel.value = {
      currency: props.method.currency,
      paymentMethodType: props.method.paymentMethodType,
      bankName: props.method.bankName || '',
      accountNumber: props.method.accountNumber || '',
      accountHolderName: props.method.accountHolderName || '',
      isActive: props.method.isActive
    };
  } else {
    // Reset form for new method
    formModel.value = {
      currency: props.currency,
      paymentMethodType: 'BANK_TRANSFER',
      bankName: '',
      accountNumber: '',
      accountHolderName: '',
      isActive: true
    };
  }
};

// Watch for prop changes
watch(() => props.method, initializeForm, { immediate: true });
watch(() => props.currency, (newCurrency) => {
  if (!props.method) {
    formModel.value.currency = newCurrency;
  }
});

// Lifecycle
onMounted(() => {
  initializeForm();
});
</script>

<style scoped>
.payment-method-form {
  max-width: 100%;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

.advanced-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

/* Dark theme support */
.dark .section-title {
  color: #f3f4f6;
  border-bottom-color: #4b5563;
}

.dark .advanced-section {
  border-top-color: #374151;
}

.dark .form-actions {
  border-top-color: #374151;
}

/* Responsive design */
@media (max-width: 768px) {
  .advanced-section .n-grid {
    grid-template-columns: 1fr !important;
  }
}
</style>
