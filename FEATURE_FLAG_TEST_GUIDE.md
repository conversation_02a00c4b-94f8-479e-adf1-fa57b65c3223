# 🧪 HomeView Feature Flag Testing Guide

## Quick Test Steps

### Method 1: Using Browser Console Commands

1. **Open browser console** (F12 → Console tab)

2. **Copy and paste this test script:**
```javascript
// Quick feature flag test
console.log('🏠 Current state:', localStorage.getItem('useNewHomeDesign'));

// Enable new design
localStorage.setItem('useNewHomeDesign', 'true');
console.log('✅ Enabled new design');

// Check every second for changes (will auto-sync)
let checkCount = 0;
const checkFeatureFlag = setInterval(() => {
  checkCount++;
  const storeState = window.__PINIA__?.state?.value?.uiPreferences?.useNewHomeDesign;
  console.log(`Check ${checkCount}: Store=${storeState}, LocalStorage=${localStorage.getItem('useNewHomeDesign')}`);
  
  if (checkCount >= 5) {
    clearInterval(checkFeatureFlag);
    console.log('🎯 Test complete! You should see the new component-based design.');
  }
}, 500);
```

3. **Look for console messages** like:
   - `🏠 HomeView: Feature flag state:`
   - `🔄 Syncing store with localStorage:`
   - `🔄 Storage change detected for useNewHomeDesign:`

### Method 2: Using Helper Functions

1. **Open console and run:**
```javascript
enableNewHomeDesign()  // Will show success message
```

2. **Wait 1-2 seconds** (automatic sync)

3. **Check the page** - you should see new component-based design

4. **To disable:**
```javascript
disableNewHomeDesign()  // Will show success message
```

5. **Wait 1-2 seconds** for sync

## 🔍 What to Look For

### When Feature Flag is ENABLED (`true`):
- ✅ Console shows: `🏠 HomeView: Feature flag state: { storeValue: true }`
- ✅ Page uses new component-based sections
- ✅ All functionality works the same

### When Feature Flag is DISABLED (`false`):
- ✅ Console shows: `🏠 HomeView: Feature flag state: { storeValue: false }`
- ✅ Page uses original monolithic design
- ✅ All functionality works the same

## 🐛 Troubleshooting

### If Nothing Happens:
1. **Check console for errors**
2. **Verify console messages appear**
3. **Try refreshing the page**
4. **Make sure you're on the HomeView page**

### If Store Doesn't Sync:
```javascript
// Manual store sync
window.__PINIA__.state.value.uiPreferences.syncFromLocalStorage();
```

### Debug Information:
```javascript
// Check all states
console.log('Debug info:', {
  localStorage: localStorage.getItem('useNewHomeDesign'),
  storeState: window.__PINIA__?.state?.value?.uiPreferences?.useNewHomeDesign,
  piniaExists: !!window.__PINIA__,
  currentPath: window.location.pathname
});
```

## ✅ Success Indicators

When working correctly, you should see:
1. **Console messages** confirming state changes
2. **Automatic sync** within 1 second
3. **Visual changes** in the HomeView layout
4. **No page refresh needed** after initial setup

## 🎯 Expected Behavior

- **Setting localStorage**: Triggers immediate sync
- **Store updates**: Reflected in UI within 1 second  
- **Both designs**: Work identically with same functionality
- **Zero errors**: No console errors during toggle
