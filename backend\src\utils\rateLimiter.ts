/**
 * Represents a rate limit entry for a specific phone number
 */
interface RateLimitEntry {
  /** Number of OTP send attempts within the current window */
  sendAttempts: number;
  /** Number of verify attempts for the current OTP */
  verifyAttempts: number;
  /** Timestamp of the last OTP send attempt */
  lastSendTime: number;
  /** Timestamp until which the number is blocked (if applicable) */
  blockUntil: number;
  /** Current OTP value (if tracking is enabled) */
  currentOtp?: string;
  /** Last time this entry was accessed (for cleanup) */
  lastAccessed: number;
}

/**
 * Rate limiter for OTP operations to prevent abuse and protect against brute-force attacks.
 * Uses the Singleton pattern to ensure a single, application-wide rate limiter instance.
 * 
 * Features:
 * - Limits number of OTP send requests per time window
 * - Enforces cooldown periods between consecutive send attempts
 * - Limits number of verification attempts per OTP
 * - Implements blocking periods for exceeded limits
 * - Automatically cleans up stale entries to prevent memory leaks
 */
class OtpRateLimiter {
  private static instance: OtpRateLimiter;
  private limits: Map<string, RateLimitEntry>;
  
  // Rate limit settings (configurable via environment variables in a production setup)
  private readonly MAX_SEND_ATTEMPTS = 3; // Max OTP send requests per hour
  private readonly MAX_VERIFY_ATTEMPTS = 5; // Max verify attempts per OTP
  private readonly BLOCK_DURATION = 60 * 60 * 1000; // 1 hour block in milliseconds
  private readonly WINDOW_DURATION = 60 * 60 * 1000; // 1 hour rolling window in milliseconds
  private readonly COOLDOWN_BETWEEN_SENDS = 60 * 1000; // 1 minute cooldown in milliseconds
  private readonly CLEANUP_INTERVAL = 6 * 60 * 60 * 1000; // Run cleanup every 6 hours
  private readonly ENTRY_TTL = 24 * 60 * 60 * 1000; // Remove entries older than 24 hours
  
  /**
   * Private constructor to prevent direct instantiation
   */
  private constructor() {
    this.limits = new Map();
    // Schedule periodic cleanup of stale entries
    setInterval(() => this.cleanupStaleEntries(), this.CLEANUP_INTERVAL);
  }

  /**
   * Get the singleton instance of the rate limiter
   * @returns The OtpRateLimiter instance
   */
  public static getInstance(): OtpRateLimiter {
    if (!OtpRateLimiter.instance) {
      OtpRateLimiter.instance = new OtpRateLimiter();
    }
    return OtpRateLimiter.instance;
  }
  /**
   * Check if a phone number is allowed to request a new OTP
   * 
   * Implements three levels of rate limiting:
   * 1. Block period check: If the number is blocked, no sends are allowed
   * 2. Cooldown period check: A minimum time between consecutive sends
   * 3. Maximum sends per window check: Limits the total number of sends in a period
   * 
   * @param phoneNumber The phone number to check (E.164 format)
   * @returns Object containing whether the send is allowed, remaining attempts, and block time if applicable
   */
  public checkSendLimit(phoneNumber: string): { allowed: boolean; remainingAttempts: number; blockedUntil?: number } {
    try {
      // Normalize the phone number to prevent bypass attempts
      phoneNumber = this.normalizePhoneNumber(phoneNumber);
      const now = Date.now();
      const entry = this.getEntry(phoneNumber);

      // Check if blocked
      if (entry.blockUntil > now) {
        return {
          allowed: false,
          remainingAttempts: 0,
          blockedUntil: entry.blockUntil
        };
      }

      // Enforce cooldown between sends
      if (entry.lastSendTime > 0 && now - entry.lastSendTime < this.COOLDOWN_BETWEEN_SENDS) {
        return {
          allowed: false,
          remainingAttempts: this.MAX_SEND_ATTEMPTS - entry.sendAttempts,
          blockedUntil: entry.lastSendTime + this.COOLDOWN_BETWEEN_SENDS
        };
      }

      // Reset attempts if window has expired
      if (now - entry.lastSendTime >= this.WINDOW_DURATION) {
        entry.sendAttempts = 0;
        entry.verifyAttempts = 0;
      }

      const remainingAttempts = this.MAX_SEND_ATTEMPTS - entry.sendAttempts;

      if (remainingAttempts <= 0) {
        // Block for 1 hour if max attempts reached
        entry.blockUntil = now + this.BLOCK_DURATION;
        return {
          allowed: false,
          remainingAttempts: 0,
          blockedUntil: entry.blockUntil
        };
      }

      return {
        allowed: true,
        remainingAttempts
      };
    } catch (error) {
      console.error('Error in checkSendLimit:', error);
      // Default to allowed in case of errors, to prevent blocking legitimate users
      return {
        allowed: true,
        remainingAttempts: this.MAX_SEND_ATTEMPTS
      };
    }
  }

  public async recordSendAttempt(phoneNumber: string, otp?: string): Promise<void> {
    const entry = this.getEntry(phoneNumber);
    entry.sendAttempts++;
    entry.lastSendTime = Date.now();
    entry.verifyAttempts = 0; // Reset verify attempts for new OTP
    if (otp) {
      entry.currentOtp = otp;
    }
  }

  public async checkVerifyLimit(phoneNumber: string): Promise<{ allowed: boolean; remainingAttempts: number; blockedUntil?: number }> {
    const now = Date.now();
    const entry = this.getEntry(phoneNumber);

    // Check if blocked
    if (entry.blockUntil > now) {
      return {
        allowed: false,
        remainingAttempts: 0,
        blockedUntil: entry.blockUntil
      };
    }

    const remainingAttempts = this.MAX_VERIFY_ATTEMPTS - entry.verifyAttempts;

    if (remainingAttempts <= 0) {
      // Block for 1 hour if max attempts reached
      entry.blockUntil = now + this.BLOCK_DURATION;
      return {
        allowed: false,
        remainingAttempts: 0,
        blockedUntil: entry.blockUntil
      };
    }

    return {
      allowed: true,
      remainingAttempts
    };
  }

  public async recordVerifyAttempt(phoneNumber: string): Promise<void> {
    const entry = this.getEntry(phoneNumber);
    entry.verifyAttempts++;
  }
  private getEntry(phoneNumber: string): RateLimitEntry {
    if (!this.limits.has(phoneNumber)) {
      this.limits.set(phoneNumber, {
        sendAttempts: 0,
        verifyAttempts: 0,
        lastSendTime: 0,
        blockUntil: 0,
        lastAccessed: Date.now(),
      });
    }
    const entry = this.limits.get(phoneNumber)!;
    entry.lastAccessed = Date.now(); // Update last accessed time
    return entry;
  }

  /**
   * Cleanup stale entries from the rate limiter
   */
  private cleanupStaleEntries(): void {
    const now = Date.now();
    for (const [phoneNumber, entry] of this.limits.entries()) {
      if (now - entry.lastAccessed > this.ENTRY_TTL) {
        this.limits.delete(phoneNumber);
      }
    }
  }

  /**
   * Normalize phone number format
   */
  private normalizePhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters and normalize format
    return phoneNumber.replace(/\D/g, '');
  }

  // For testing/debugging
  public clearLimits(): void {
    this.limits.clear();
  }
}

export default OtpRateLimiter;
