#!/usr/bin/env node

/**
 * Comprehensive test script for the Enhanced Tagging System
 * 
 * This script tests the complete migration from hardcoded frontend tags
 * to a backend-driven, database-authoritative tagging system.
 * 
 * Tests cover:
 * - Database tag/category population
 * - Backend API functionality
 * - Tag-report-type associations
 * - i18n support
 * - AI integration readiness
 * - Analytics capabilities
 */

const { PrismaClient } = require('@prisma/client');

async function runTaggingSystemTests() {
  const prisma = new PrismaClient();
  let testsPassed = 0;
  let testsFailed = 0;

  function logTest(name, passed, details = '') {
    if (passed) {
      console.log(`✅ ${name}`);
      testsPassed++;
    } else {
      console.log(`❌ ${name} ${details}`);
      testsFailed++;
    }
  }

  console.log('🏷️  Enhanced Tagging System - Comprehensive Test Suite');
  console.log('======================================================\n');

  try {
    // Test 1: Database Schema Verification
    console.log('📊 Testing Database Schema...');
    
    const categories = await prisma.tagCategory.findMany();
    logTest('TagCategory table exists and populated', categories.length > 0, `(Found ${categories.length} categories)`);
    
    const tags = await prisma.tag.findMany();
    logTest('Tag table exists and populated', tags.length > 0, `(Found ${tags.length} tags)`);
    
    const associations = await prisma.tagReportTypeAssociation.findMany();
    logTest('TagReportTypeAssociation table exists and populated', associations.length > 0, `(Found ${associations.length} associations)`);

    // Test 2: Category Structure
    console.log('\n📂 Testing Category Structure...');
    
    const categoryNames = categories.map(c => c.name);
    const expectedCategories = ['UI_UX', 'PERFORMANCE', 'FUNCTIONALITY', 'TRANSACTION', 'AUTHENTICATION', 'BROWSER_COMPATIBILITY', 'MOBILE', 'ACCESSIBILITY'];
    
    for (const expected of expectedCategories) {
      logTest(`Category "${expected}" exists`, categoryNames.includes(expected));
    }

    // Test 3: Tag Structure and Multilingual Support
    console.log('\n🏷️  Testing Tag Structure and i18n...');
    
    const sampleTag = tags.find(t => t.name === 'UI_LAYOUT_BROKEN');
    if (sampleTag) {
      const displayName = sampleTag.displayName;
      logTest('Tag has multilingual displayName', typeof displayName === 'object' && displayName.en && displayName.fa);
      logTest('Tag has English label', displayName.en && displayName.en.length > 0);
      logTest('Tag has Persian label', displayName.fa && displayName.fa.length > 0);
    } else {
      logTest('Sample tag UI_LAYOUT_BROKEN exists', false);
    }

    // Test 4: Tag-Report-Type Associations
    console.log('\n🔗 Testing Tag-Report-Type Associations...');
    
    const reportTypes = [...new Set(associations.map(a => a.reportType))];
    const expectedReportTypes = ['BUG', 'FEATURE_REQUEST', 'PERFORMANCE_ISSUE', 'UI_FEEDBACK', 'SECURITY_CONCERN'];
    
    for (const expected of expectedReportTypes) {
      logTest(`Report type "${expected}" has tag associations`, reportTypes.includes(expected));
    }

    // Test 5: Tag Categories Distribution
    console.log('\n📈 Testing Tag Distribution Across Categories...');
    
    const tagsByCategory = await prisma.tag.findMany({
      include: { category: true }
    });
    
    const categoryDistribution = {};
    tagsByCategory.forEach(tag => {
      const categoryName = tag.category?.name || 'UNCATEGORIZED';
      categoryDistribution[categoryName] = (categoryDistribution[categoryName] || 0) + 1;
    });
    
    logTest('Tags distributed across multiple categories', Object.keys(categoryDistribution).length >= 5);
    
    Object.entries(categoryDistribution).forEach(([category, count]) => {
      console.log(`   📁 ${category}: ${count} tags`);
    });

    // Test 6: Analytics Fields
    console.log('\n📊 Testing Analytics Capabilities...');
    
    const tagsWithAnalytics = tags.filter(t => 
      typeof t.usageCount === 'number' && 
      typeof t.weight === 'number' &&
      typeof t.aiRelevance === 'number'
    );
    
    logTest('Tags have analytics fields (usageCount, weight, aiRelevance)', tagsWithAnalytics.length === tags.length);

    // Test 7: System vs User Tags
    console.log('\n🔧 Testing System vs User Tag Classification...');
    
    const systemTags = tags.filter(t => t.isSystem);
    const userTags = tags.filter(t => !t.isSystem);
    
    logTest('System tags exist', systemTags.length > 0, `(${systemTags.length} system tags)`);
    logTest('User tags capability ready', userTags.length >= 0, `(${userTags.length} user tags)`);

    // Test 8: Tag Activation Status
    console.log('\n⚡ Testing Tag Activation Status...');
    
    const activeTags = tags.filter(t => t.isActive);
    const inactiveTags = tags.filter(t => !t.isActive);
    
    logTest('Active tags exist', activeTags.length > 0, `(${activeTags.length} active tags)`);
    logTest('Tag activation system ready', typeof activeTags[0]?.isActive === 'boolean');

    // Test 9: AI Integration Readiness
    console.log('\n🤖 Testing AI Integration Readiness...');
    
    const tagsWithAiRelevance = tags.filter(t => t.aiRelevance !== null && t.aiRelevance >= 0);
    logTest('Tags have AI relevance scores', tagsWithAiRelevance.length > 0);
    
    const tagsByReportType = {};
    associations.forEach(assoc => {
      if (!tagsByReportType[assoc.reportType]) {
        tagsByReportType[assoc.reportType] = [];
      }
      const tag = tags.find(t => t.id === assoc.tagId);
      if (tag) {
        tagsByReportType[assoc.reportType].push(tag.name);
      }
    });
    
    logTest('AI context mapping ready', Object.keys(tagsByReportType).length > 0);

    // Test 10: Enhanced Debug Report Integration
    console.log('\n🐛 Testing Enhanced Debug Report Integration...');
    
    // Check if DebugReportButtonEnhanced component exists and uses new system
    const fs = require('fs');
    const debugReportPath = './frontend/src/components/DebugReportButtonEnhanced.vue';
    
    if (fs.existsSync(debugReportPath)) {
      const componentContent = fs.readFileSync(debugReportPath, 'utf8');
      logTest('DebugReportButtonEnhanced component exists', true);
      logTest('Component uses TagSelector', componentContent.includes('TagSelector'));
      logTest('Component uses tagStore', componentContent.includes('useTagStore') || componentContent.includes('tagStore'));
      logTest('No hardcoded tags in component', !componentContent.includes("tags: ['") && !componentContent.includes('hardcoded'));
    } else {
      logTest('DebugReportButtonEnhanced component exists', false);
    }

    // Test 11: Backend API Readiness
    console.log('\n🌐 Testing Backend API Readiness...');
    
    const tagServicePath = './backend/src/services/tagService.ts';
    const tagRoutesPath = './backend/src/routes/tagRoutes.ts';
    
    logTest('TagService exists', fs.existsSync(tagServicePath));
    logTest('Tag routes exist', fs.existsSync(tagRoutesPath));
    
    if (fs.existsSync(tagServicePath)) {
      const serviceContent = fs.readFileSync(tagServicePath, 'utf8');
      logTest('TagService has CRUD operations', 
        serviceContent.includes('createTag') && 
        serviceContent.includes('updateTag') && 
        serviceContent.includes('getAllTags'));
      logTest('TagService has AI integration', serviceContent.includes('getTagsForAiContext'));
      logTest('TagService has analytics', serviceContent.includes('getTagUsageStats'));
    }

    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    console.log(`✅ Tests Passed: ${testsPassed}`);
    console.log(`❌ Tests Failed: ${testsFailed}`);
    console.log(`📈 Success Rate: ${Math.round((testsPassed / (testsPassed + testsFailed)) * 100)}%`);

    if (testsFailed === 0) {
      console.log('\n🎉 All tests passed! Enhanced Tagging System is fully functional.');
      console.log('\n🚀 Migration from hardcoded frontend tags to backend-driven system: COMPLETE');
      console.log('\n✨ Key improvements achieved:');
      console.log('   • Centralized tag management via database');
      console.log('   • Full i18n support (English/Persian)');
      console.log('   • Tag-report-type associations');
      console.log('   • AI integration capabilities');
      console.log('   • Analytics and usage tracking');
      console.log('   • Admin management interface ready');
      console.log('   • Extensible and maintainable architecture');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the issues above.');
    }

  } catch (error) {
    console.error('❌ Test execution failed:', error);
    console.log('\n💡 Make sure the database is running and migrated.');
  } finally {
    await prisma.$disconnect();
  }
}

// Run the tests
runTaggingSystemTests().catch(console.error);
