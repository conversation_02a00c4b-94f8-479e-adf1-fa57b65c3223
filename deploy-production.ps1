# Production Deployment Script for Debug Report System Update
# Run this script on your production host machine

param(
    [switch]$BackupFirst = $true,
    [switch]$ZeroDowntime = $true,
    [string]$BackupPath = ".\backups"
)

Write-Host "🚀 Starting MUNygo Production Deployment" -ForegroundColor Green
Write-Host "Debug Report System Update" -ForegroundColor Yellow

# Check if we're in the right directory
if (-Not (Test-Path "docker-compose.yml")) {
    Write-Error "❌ docker-compose.yml not found. Are you in the MUNygo project directory?"
    exit 1
}

# Create backup directory if it doesn't exist
if ($BackupFirst -and -Not (Test-Path $BackupPath)) {
    New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
}

# Step 1: Create backup
if ($BackupFirst) {
    Write-Host "📦 Creating database backup..." -ForegroundColor Yellow
    $backupFile = "$BackupPath\backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"
    
    try {
        docker exec munygo-postgres pg_dump -U $env:POSTGRES_USER -d $env:POSTGRES_DB > $backupFile
        Write-Host "✅ Backup created: $backupFile" -ForegroundColor Green
    }
    catch {
        Write-Warning "⚠️ Backup failed, but continuing. Manual backup recommended."
    }
}

# Step 2: Pull latest code
Write-Host "📥 Pulling latest code..." -ForegroundColor Yellow
try {
    git pull origin main
    Write-Host "✅ Code updated successfully" -ForegroundColor Green
}
catch {
    Write-Error "❌ Git pull failed. Please resolve conflicts manually."
    exit 1
}

# Step 3: Check environment variables
Write-Host "🔧 Checking environment variables..." -ForegroundColor Yellow
$envFile = ".env"
if (Test-Path $envFile) {
    $envContent = Get-Content $envFile
    
    # Check for required debug report variables
    $hasLogDir = $envContent | Where-Object { $_ -match "CLIENT_LOG_DIRECTORY" }
    $hasDebugEnabled = $envContent | Where-Object { $_ -match "VITE_ENABLE_DEBUG_REPORT" }
    
    if (-Not $hasLogDir) {
        Add-Content $envFile "CLIENT_LOG_DIRECTORY=/app/logs"
        Write-Host "✅ Added CLIENT_LOG_DIRECTORY to .env" -ForegroundColor Green
    }
    
    if (-Not $hasDebugEnabled) {
        Add-Content $envFile "VITE_ENABLE_DEBUG_REPORT=true"
        Write-Host "✅ Added VITE_ENABLE_DEBUG_REPORT to .env" -ForegroundColor Green
    }
}
else {
    Write-Warning "⚠️ .env file not found. Please create it based on .env.example"
}

# Step 4: Deploy based on strategy
if ($ZeroDowntime) {
    Write-Host "🔄 Starting zero-downtime deployment..." -ForegroundColor Yellow
    
    # Stop only backend
    Write-Host "Stopping backend service..." -ForegroundColor Yellow
    docker compose stop backend
    
    # Run migration
    Write-Host "Running database migration..." -ForegroundColor Yellow
    docker compose run --rm backend npx prisma migrate deploy
    
    # Rebuild and start everything
    Write-Host "Rebuilding and starting services..." -ForegroundColor Yellow
    docker compose up -d --build
}
else {
    Write-Host "🔄 Starting full restart deployment..." -ForegroundColor Yellow
    
    # Stop all services
    docker compose down
    
    # Rebuild and start with migration
    docker compose up -d --build
}

# Step 5: Verify deployment
Write-Host "🔍 Verifying deployment..." -ForegroundColor Yellow

# Wait for services to be ready
Start-Sleep -Seconds 30

# Check container status
Write-Host "Checking container status..." -ForegroundColor Yellow
$containers = docker compose ps --format "table {{.Name}}\t{{.Status}}"
Write-Host $containers

# Check if all containers are running
$runningContainers = docker compose ps --filter "status=running" --format "{{.Name}}"
$expectedContainers = @("munygo-postgres", "munygo-backend", "munygo-frontend")

$allRunning = $true
foreach ($expected in $expectedContainers) {
    if ($runningContainers -notcontains $expected) {
        Write-Warning "⚠️ Container $expected is not running"
        $allRunning = $false
    }
}

if ($allRunning) {
    Write-Host "✅ All containers are running" -ForegroundColor Green
}

# Test health endpoints
Write-Host "Testing health endpoints..." -ForegroundColor Yellow

try {
    $backendHealth = Invoke-RestMethod -Uri "http://localhost:3004/health" -TimeoutSec 10
    Write-Host "✅ Backend health check passed" -ForegroundColor Green
}
catch {
    Write-Warning "⚠️ Backend health check failed"
}

# Check database tables
Write-Host "Verifying database schema..." -ForegroundColor Yellow
try {
    $dbCheck = docker exec munygo-postgres psql -U $env:POSTGRES_USER -d $env:POSTGRES_DB -c "\dt DebugReport*" 2>$null
    if ($dbCheck -match "DebugReport") {
        Write-Host "✅ Debug report tables found in database" -ForegroundColor Green
    }
    else {
        Write-Warning "⚠️ Debug report tables not found. Migration may have failed."
    }
}
catch {
    Write-Warning "⚠️ Could not verify database schema"
}

# Final status
Write-Host ""
Write-Host "🎉 Deployment completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Test the debug report feature in your frontend"
Write-Host "2. Check admin dashboard for debug reports"
Write-Host "3. Monitor logs: docker compose logs -f"
Write-Host "4. Check application functionality"
Write-Host ""

if ($BackupFirst) {
    Write-Host "💾 Backup location: $backupFile" -ForegroundColor Cyan
}

Write-Host "📊 View logs with: docker compose logs backend --tail=50" -ForegroundColor Cyan
Write-Host "🔍 Monitor containers: docker compose ps" -ForegroundColor Cyan
