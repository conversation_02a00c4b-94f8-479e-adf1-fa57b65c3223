## Useful and Repeated Commands

### Set User Level (Inside Docker Container)

To set a user's level, execute the following command within the running `munygo-backend` Docker container:

```bash
docker exec -it munygo-backend node dist/scripts/setTestUserLevel.js <EMAIL> <level_number>
```

Replace `<EMAIL>` with the target user's email and `<level_number>` with the desired level (e.g., 4).

**Example:**

```bash
docker exec -it munygo-backend node dist/scripts/setTestUserLevel.js <EMAIL> 4
```