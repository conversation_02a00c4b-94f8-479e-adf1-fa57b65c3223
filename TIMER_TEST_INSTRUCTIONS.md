# Timer Functionality Test Instructions

## Test Data Created
✅ **Transaction ID**: `cmb2liziw0009vlx8ivmhvd55`
✅ **Chat Session ID**: `cmb2lizip0007vlx8vca1msze`
✅ **Payment Due Date**: 2025-05-24T21:01:45.941Z (2 hours from creation)
✅ **Transaction Status**: `AWAITING_FIRST_PAYER_PAYMENT`
✅ **First Payer**: TimerTestUserB (<EMAIL>)

## Login Credentials
**User A (Offer Creator):**
- Email: `<EMAIL>`
- Password: `password123`

**User B (First Payer - Should see countdown timer):**
- Email: `<EMAIL>`
- Password: `password123`

## Test Procedure

### 1. Test Countdown Timer (User B)
1. Open http://localhost:5174/login
2. Login as User B: `<EMAIL>` / `password123`
3. Navigate to: http://localhost:5174/chat/cmb2lizip0007vlx8vca1msze
4. **Expected Result**: Should see a countdown timer showing time remaining until payment deadline (2 hours)
5. **Timer Display**: Should show format like "1h 59m 30s" and count down
6. **Timer Style**: Should be in normal state (not critical/expired)

### 2. Test Timer Updates (Both Users)
1. Verify timer updates every second
2. Check that both users see the same timer information
3. Verify timer shows critical state when < 30 minutes remaining
4. Verify timer shows expired state when time passes

### 3. Test Elapsed Timer (Simulate)
To test elapsed timer functionality:
1. Use backend script to manually update transaction to `AWAITING_SECOND_PAYER_CONFIRMATION`
2. Navigate back to chat
3. **Expected Result**: Timer should show elapsed time with "+" prefix (e.g., "+15m 30s")

## Timer Implementation Details

### Countdown Timer States:
- **Normal**: Green/blue color, shows time remaining
- **Critical**: Orange/red color when < 30 minutes left
- **Expired**: Red color, shows "Time Expired"

### Elapsed Timer:
- Shows "+" prefix
- Used for confirmation steps
- Tracks how long past the deadline

### Frontend Components Modified:
- `useTransactionFlowLogic.ts`: Timer calculation logic
- `TransactionFlowCardV3.vue`: Timer display UI

### Backend Configuration:
- `DEFAULT_PAYMENT_WINDOW_HOURS = 2`
- `calculatePaymentDueDate()` properly calculates 2-hour deadline

## Testing URLs

**Application**: http://localhost:5174/
**Login**: http://localhost:5174/login
**Direct Chat**: http://localhost:5174/chat/cmb2lizip0007vlx8vca1msze
**Prisma Studio**: http://localhost:5555/ (for database inspection)

## Verification Checklist

- [ ] Countdown timer displays correctly
- [ ] Timer updates every second
- [ ] Timer shows correct time remaining
- [ ] Timer changes to critical state near deadline
- [ ] Timer shows expired state after deadline
- [ ] Elapsed timer works for confirmation steps
- [ ] Timer is consistent between users
- [ ] Timer persists across page refreshes
- [ ] System messages display correctly

## Manual Timer Testing

If you want to test with a shorter timer for faster testing:

```typescript
// In createWorkingTimerTest.ts, change:
const paymentDueDate = new Date(now.getTime() + (2 * 60 * 1000)); // 2 minutes instead of 2 hours
```

Then recreate the test data for faster timer testing.
