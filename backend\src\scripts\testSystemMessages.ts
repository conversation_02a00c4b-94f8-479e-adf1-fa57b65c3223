import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testSystemMessages() {
  try {
    // Get users
    const users = await prisma.user.findMany({
      select: { id: true, username: true, email: true }
    });
    console.log('Users:', JSON.stringify(users, null, 2));

    // Get current transaction
    const transactions = await prisma.transaction.findMany({
      include: {
        chatSession: {
          select: { id: true, userOneId: true, userTwoId: true }
        }
      }
    });
    console.log('Transactions:', JSON.stringify(transactions, null, 2));

    // Get chat messages for the active transaction
    if (transactions.length > 0) {
      const chatSessionId = transactions[0].chatSessionId;
      const messages = await prisma.chatMessage.findMany({
        where: { chatSessionId },
        orderBy: { createdAt: 'asc' }
      });
      console.log(`\nChat messages for session ${chatSessionId}:`, JSON.stringify(messages, null, 2));
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSystemMessages();
