import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock canvas-confetti before any imports
vi.mock('canvas-confetti', () => ({
  default: vi.fn()
}))

import { celebrateTransactionCompletion, simpleCelebration, sideCannonsCelebration } from '@/utils/confetti'

describe('Confetti Utility', () => {
  let mockConfetti: any

  beforeEach(async () => {
    vi.clearAllMocks()
    vi.clearAllTimers()
    vi.useFakeTimers()
    
    // Get the mocked confetti function
    const confettiModule = await import('canvas-confetti')
    mockConfetti = confettiModule.default
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should export all confetti functions', () => {
    expect(celebrateTransactionCompletion).toBeDefined()
    expect(simpleCelebration).toBeDefined()
    expect(sideCannonsCelebration).toBeDefined()
  })
  it('should call confetti when celebrating transaction completion', () => {
    celebrateTransactionCompletion()

    // Should call confetti immediately for the center burst
    expect(mockConfetti).toHaveBeenCalled()
    expect(mockConfetti).toHaveBeenCalledWith(expect.objectContaining({
      particleCount: 50,
      origin: { x: 0.5, y: 0.6 }
    }))

    // Fast-forward timers to trigger delayed bursts
    vi.advanceTimersByTime(200)
    expect(mockConfetti).toHaveBeenCalledTimes(2)

    vi.advanceTimersByTime(200)
    // The function creates multiple timed bursts plus continuous confetti
    expect(mockConfetti.mock.calls.length).toBeGreaterThanOrEqual(3)

    // Fast-forward to trigger continuous confetti
    vi.advanceTimersByTime(250)
    expect(mockConfetti.mock.calls.length).toBeGreaterThan(3)
  })

  it('should call confetti for simple celebration', () => {
    simpleCelebration()

    expect(mockConfetti).toHaveBeenCalledWith(expect.objectContaining({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 }
    }))
  })

  it('should call confetti for side cannons celebration', () => {
    sideCannonsCelebration()

    // Fast-forward to trigger the interval
    vi.advanceTimersByTime(150)

    // Should have called confetti for both cannons
    expect(mockConfetti).toHaveBeenCalledTimes(2)
    
    // Check if both left and right cannons were fired
    const calls = mockConfetti.mock.calls
    expect(calls.some(call => call[0].origin?.x === 0)).toBe(true) // Left cannon
    expect(calls.some(call => call[0].origin?.x === 1)).toBe(true) // Right cannon
  })

  it('should handle errors gracefully', () => {
    // Mock confetti to throw an error
    mockConfetti.mockImplementationOnce(() => {
      throw new Error('Confetti error')
    })

    // Should not throw an error when confetti fails
    expect(() => celebrateTransactionCompletion()).not.toThrow()
    expect(() => simpleCelebration()).not.toThrow()
    expect(() => sideCannonsCelebration()).not.toThrow()
  })

  it('should log success messages when confetti works', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

    celebrateTransactionCompletion()

    expect(consoleSpy).toHaveBeenCalledWith('[Confetti] Transaction completion celebration triggered! 🎉')

    consoleSpy.mockRestore()
  })

  it('should log error messages when confetti fails', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    mockConfetti.mockImplementationOnce(() => {
      throw new Error('Test error')
    })

    celebrateTransactionCompletion()

    expect(consoleSpy).toHaveBeenCalledWith('[Confetti] Error triggering confetti effect:', expect.any(Error))

    consoleSpy.mockRestore()
  })
})
