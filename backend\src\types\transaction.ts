import { z } from 'zod';
import { TransactionStatus } from '@prisma/client'; // Assuming this is generated

// Zod schema for a full transaction object (e.g., for API responses)
export const TransactionSchema = z.object({
  id: z.string().cuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
  status: z.nativeEnum(TransactionStatus),
  chatSessionId: z.string().cuid(),
  currencyA: z.string(),
  amountA: z.number(),
  currencyAProviderId: z.string().cuid(),
  currencyB: z.string(),
  amountB: z.number(),
  currencyBProviderId: z.string().cuid(),
  agreedFirstPayerId: z.string().cuid().nullable(),
  termsAgreementTimestampPayer1: z.date().nullable(),
  termsAgreementTimestampPayer2: z.date().nullable(),
  paymentExpectedByPayer1: z.date().nullable(),
  paymentDeclaredAtPayer1: z.date().nullable(),
  paymentTrackingNumberPayer1: z.string().nullable(),
  firstPaymentConfirmedByPayer2At: z.date().nullable(),
  paymentExpectedByPayer2: z.date().nullable(),
  paymentDeclaredAtPayer2: z.date().nullable(),
  paymentTrackingNumberPayer2: z.string().nullable(),
  secondPaymentConfirmedByPayer1At: z.date().nullable(),
  cancellationReason: z.string().nullable(),
  disputeReason: z.string().nullable(),
});

export type Transaction = z.infer<typeof TransactionSchema>;

// Input Schemas for Transaction Actions
export const AgreeToTermsInputSchema = z.object({}); // transactionId and userId are path/context params

export const DeclarePaymentInputSchema = z.object({
  paymentTrackingNumber: z.string().optional().nullable(),
});
export type DeclarePaymentInput = z.infer<typeof DeclarePaymentInputSchema>;

export const ConfirmReceiptInputSchema = z.object({}); // transactionId and userId are path/context params

export const CancelTransactionInputSchema = z.object({
  reason: z.string().min(1, 'Cancellation reason is required.'),
});
export type CancelTransactionInput = z.infer<typeof CancelTransactionInputSchema>;

export const DisputeTransactionInputSchema = z.object({
  reason: z.string().min(1, 'Dispute reason is required.'),
});
export type DisputeTransactionInput = z.infer<typeof DisputeTransactionInputSchema>;

// Specific types for socket event payloads if needed
export type TransactionUpdatedPayload = Transaction;

export type TransactionNotificationPayload = {
  transactionId: string;
  message: string;
  // Add other relevant fields for a notification
  nextStep?: TransactionStatus; // e.g., to guide the user
  recipientId: string;
};

// Enum for user roles within a transaction context (could be useful on frontend)
export enum TransactionRole {
  CURRENCY_A_PROVIDER = 'currencyAProvider',
  CURRENCY_B_PROVIDER = 'currencyBProvider',
  FIRST_PAYER = 'firstPayer',
  SECOND_PAYER = 'secondPayer',
  OBSERVER = 'observer', // For users not directly involved but viewing (if applicable)
}
