<script setup lang="ts">
import { computed } from 'vue'
import type { FeedItem } from '@/stores/transactionalChat/transactionalChatStore'

interface Props {
  item: FeedItem
}

const props = defineProps<Props>()

// Computed properties
const isCurrentUser = computed(() => props.item.sender?.isCurrentUser || false)
const senderName = computed(() => props.item.sender?.name || 'Unknown')
const messageContent = computed(() => props.item.content || '')

const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const messageTime = computed(() => formatTime(props.item.timestamp))
</script>

<template>
  <div 
    class="chat-message"
    :class="{
      'message-current-user': isCurrentUser,
      'message-other-user': !isCurrentUser
    }"
    data-testid="chat-message"
  >
    <!-- Message Container -->
    <div class="message-container">
      <!-- Avatar (only for other users) -->
      <div 
        v-if="!isCurrentUser"
        class="message-avatar"
        data-testid="message-avatar"
      >
        <div class="avatar-circle">
          {{ senderName.charAt(0).toUpperCase() }}
        </div>
      </div>
      
      <!-- Message Content -->
      <div class="message-content">
        <!-- Sender Name (only for other users) -->
        <div 
          v-if="!isCurrentUser"
          class="sender-name"
          data-testid="sender-name"
        >
          {{ senderName }}
        </div>
        
        <!-- Message Bubble -->
        <div 
          class="message-bubble"
          :class="{
            'bubble-current-user': isCurrentUser,
            'bubble-other-user': !isCurrentUser
          }"
          data-testid="message-bubble"
        >
          <p class="message-text">{{ messageContent }}</p>
        </div>
        
        <!-- Message Time -->
        <div 
          class="message-time"
          :class="{
            'time-current-user': isCurrentUser,
            'time-other-user': !isCurrentUser
          }"
          data-testid="message-time"
        >
          {{ messageTime }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-message {
  width: 100%;
  margin-bottom: 4px;
}

.message-current-user {
  display: flex;
  justify-content: flex-end;
}

.message-other-user {
  display: flex;
  justify-content: flex-start;
}

.message-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 85%;
}

.message-current-user .message-container {
  flex-direction: row-reverse;
}

/* Avatar */
.message-avatar {
  flex-shrink: 0;
  margin-bottom: 2px;
}

.avatar-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--tc-chat-other), var(--tc-border-medium));
  color: var(--tc-text-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

/* Message Content */
.message-content {
  flex: 1;
  min-width: 0;
}

.sender-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--tc-text-muted);
  margin-bottom: 2px;
  padding-left: 4px;
}

/* Message Bubble */
.message-bubble {
  border-radius: 18px;
  padding: 12px 16px;
  word-wrap: break-word;
  position: relative;
  max-width: 100%;
}

.bubble-current-user {
  background-color: var(--tc-chat-bubble-user);
  color: var(--tc-text-white);
  border-bottom-right-radius: 6px;
}

.bubble-other-user {
  background-color: var(--tc-chat-bubble-other);
  color: var(--tc-text-primary);
  border-bottom-left-radius: 6px;
}

.message-text {
  margin: 0;
  font-size: 15px;
  line-height: 1.4;
  white-space: pre-wrap;
}

/* Message Time */
.message-time {
  font-size: 11px;
  color: var(--tc-text-muted);
  margin-top: 2px;
  padding: 0 4px;
}

.time-current-user {
  text-align: right;
}

.time-other-user {
  text-align: left;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .message-container {
    max-width: 90%;
    gap: 6px;
  }
  
  .avatar-circle {
    width: 28px;
    height: 28px;
    font-size: 11px;
  }
  
  .message-bubble {
    padding: 10px 14px;
    border-radius: 16px;
  }
  
  .bubble-current-user {
    border-bottom-right-radius: 5px;
  }
  
  .bubble-other-user {
    border-bottom-left-radius: 5px;
  }
  
  .message-text {
    font-size: 14px;
  }
  
  .sender-name {
    font-size: 11px;
  }
  
  .message-time {
    font-size: 10px;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .message-container {
    max-width: 95%;
    gap: 4px;
  }
  
  .avatar-circle {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }
  
  .message-bubble {
    padding: 8px 12px;
    border-radius: 14px;
  }
  
  .bubble-current-user {
    border-bottom-right-radius: 4px;
  }
  
  .bubble-other-user {
    border-bottom-left-radius: 4px;
  }
  
  .message-text {
    font-size: 13px;
  }
}

/* RTL Support */
[dir="rtl"] .message-current-user {
  justify-content: flex-start;
}

[dir="rtl"] .message-other-user {
  justify-content: flex-end;
}

[dir="rtl"] .message-current-user .message-container {
  flex-direction: row;
}

[dir="rtl"] .message-other-user .message-container {
  flex-direction: row-reverse;
}

[dir="rtl"] .sender-name {
  padding-right: 4px;
  padding-left: 0;
}

[dir="rtl"] .bubble-current-user {
  border-bottom-right-radius: 18px;
  border-bottom-left-radius: 6px;
}

[dir="rtl"] .bubble-other-user {
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 6px;
}

[dir="rtl"] .time-current-user {
  text-align: left;
  padding-right: 4px;
  padding-left: 0;
}

[dir="rtl"] .time-other-user {
  text-align: right;
  padding-left: 4px;
  padding-right: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bubble-current-user {
    border: 1px solid var(--tc-primary);
  }
  
  .bubble-other-user {
    border: 1px solid var(--tc-border-dark);
  }
  
  .avatar-circle {
    border: 1px solid var(--tc-border-dark);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .message-bubble {
    transition: none;
  }
}
</style>
