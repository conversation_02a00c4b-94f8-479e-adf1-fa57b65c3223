<template>
  <div class="language-selector">
    <n-button
      quaternary
      circle
      size="medium"
      class="lang-button"
      :title="t('language.changeLanguage')"
      @click="toggleLanguage"
    >      <template #icon>
        <span class="lang-icon">{{ currentLanguage === 'fa' ? 'EN' : 'فا' }}</span>
      </template>
    </n-button>
  </div>
</template>

<script setup lang="ts">
import { NButton } from 'naive-ui'
import { useTranslation } from '@/composables/useTranslation'

const { currentLanguage, t, setLanguage } = useTranslation()

const toggleLanguage = () => {
  const newLang = currentLanguage.value === 'fa' ? 'en' : 'fa'
  setLanguage(newLang)
  // Update HTML direction
  document.documentElement.dir = newLang === 'fa' ? 'rtl' : 'ltr'
  document.documentElement.lang = newLang
}
</script>

<style scoped>
.language-selector {
  display: inline-flex;
  align-items: center;
}

.lang-button {
  width: 34px !important;
  height: 34px !important;
  padding: 0 !important;
  transition: all 0.3s ease !important;
}

.lang-button:hover {
  transform: scale(1.05) !important;
}

.lang-icon {
  font-size: 14px;
  font-weight: 600;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: color 0.3s ease; /* Added for smooth color transition */
}

/* Light theme styles for the icon text */
[data-theme='light'] .lang-icon {
  color: #1e293b; /* Dark color for light mode */
  text-shadow: none; /* Remove shadow in light mode for clarity */
}
</style>
