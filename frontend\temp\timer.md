Report: Integrating Timers into the New Payment UI
This report provides a comprehensive analysis of the existing timer functionality and a clear guide for implementing it in your new UI components. The current system is robust, centralized, and designed to be easily reused.

1. Research & Findings: How the Current Timer System Works
The timer logic is brilliantly encapsulated within the useTransactionFlowLogic.ts composable, making it the single source of truth. UI components simply consume its reactive state.

1.1. The Logic Core: useTransactionFlowLogic.ts
This composable is the "brain" of the timer. It's responsible for:

Starting/Stopping: It watches the currentTransaction.status and automatically starts, stops, or resets the timer as the transaction progresses.
Calculating Time: It calculates the time difference between now() and a deadline from the transaction data.
Determining Timer Mode: It intelligently switches between two modes:
Countdown (Payment Window): For payment steps (e.g., AWAITING_FIRST_PAYER_PAYMENT), it counts down from a deadline (paymentExpectedByPayer1).
Count-Up (Elapsed Time): For confirmation steps (e.g., AWAITING_SECOND_PAYER_CONFIRMATION), it counts up from the time the payment was declared (paymentDeclaredAtPayer1). It also switches to this mode if a countdown timer expires to show how long it's overdue.
1.2. The Display Layer: TransactionFlowCardV3.vue & SmartNegotiationSection.vue
These components are consumers of the logic. They don't perform any calculations.

Initialization: They call the composable to get the reactive timer state.
typescript
 Show full code block 
// In SmartNegotiationSection.vue or your new component
import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic';

const {
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer,
  timerLabel // A pre-formatted label for convenience
} = useTransactionFlowLogic(chatSessionId, naiveMessage);
Rendering: They use these reactive refs to conditionally render the timer and apply dynamic styles.
vue
 Show full code block 
<!-- Example from SmartNegotiationSection.vue -->
<div v-if="timerDisplayValue" class="timer-area" :class="{
  'critical': isTimerCritical,
  'expired': isTimerExpired,
  'elapsed': isElapsedTimer
}">
  <span class="timer-label">{{ timerLabel }}</span>
  <span class="timer-value">{{ timerDisplayValue }}</span>
</div>
1.3. Synchronizing for Both Parties
This is a key finding: The system is already designed to show a synchronized timer to both the paying and waiting parties.

Backend as Source of Truth: The timer deadlines (e.g., paymentExpectedByPayer1) are stored in the database as part of the transaction record.
Real-time Updates: When the transaction status changes, the backend sends a TRANSACTION_STATUS_UPDATED event via Socket.IO to both users in the chat.
Shared Frontend State: Both users' applications receive this event, update their shared useTransactionStore, and the useTransactionFlowLogic composable in each client reacts to the exact same data, ensuring the timers are perfectly in sync.
2. Actionable Guide for Your Development Team
To integrate the timer into your new payment component, follow these steps.

Step 1: Consume the Composable
In your new payment component's <script setup> section, import and initialize useTransactionFlowLogic. You will need to pass it a reactive reference to the current chatSessionId.

typescript
 Show full code block 
// In your new payment component (e.g., NewPaymentCard.vue)
import { computed } from 'vue';
import { useMessage } from 'naive-ui';
import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic';
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore';

const props = defineProps<{
  chatSessionId: string;
}>();

const naiveMessage = useMessage();
const transactionalChatStore = useTransactionalChatStore();

// This is the only part you need to add to get all timer logic
const {
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer,
  timerLabel // Use this helpful computed property for the label
} = useTransactionFlowLogic(
  computed(() => props.chatSessionId),
  naiveMessage
);

// A computed property to decide when to show the timer
const timerDisplayValue = computed(() => {
  const tx = currentTransaction.value;
  if (!tx) return null;

  // Define the statuses where the timer should be visible
  const showTimerForStatuses = [
    'AWAITING_FIRST_PAYER_PAYMENT',
    'AWAITING_SECOND_PAYER_CONFIRMATION',
    'AWAITING_SECOND_PAYER_PAYMENT',
    'AWAITING_FIRST_PAYER_CONFIRMATION'
  ];

  return showTimerForStatuses.includes(tx.status) ? timeLeft.value : null;
});
Step 2: Add the Timer to Your Template
Place this snippet in your component's <template> where you want the timer to appear. It will automatically show/hide and change styles based on the state from the composable.

vue
 Show full code block 
<!-- In your new payment component's template -->
<div v-if="timerDisplayValue"
     class="timer-display"
     :class="{
       'timer-critical': isTimerCritical,
       'timer-expired': isTimerExpired,
       'timer-elapsed': isElapsedTimer
     }">
  <div class="timer-content">
    <span class="timer-label">{{ timerLabel }}</span>
    <span class="timer-value">{{ timerDisplayValue }}</span>
  </div>
</div>
Step 3: Add CSS for Styling
Add these styles to your component's <style scoped> section to handle the different timer states. These are adapted from SmartNegotiationSection.vue for consistency.

css
 Show full code block 
.timer-display {
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  margin: 12px 0;
  border: 1px solid #18a0fb; /* Default info color */
  background-color: rgba(24, 160, 251, 0.1);
  color: #18a0fb;
  transition: all 0.3s ease;
}

.timer-display.timer-critical {
  background-color: rgba(208, 48, 80, 0.1);
  color: #d03050;
  border-color: #d03050;
}

.timer-display.timer-expired {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
  border-color: #faad14;
}

.timer-display.timer-elapsed {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border-color: #52c41a;
}

.timer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.timer-label {
  font-size: 0.85em;
  font-weight: 500;
}

.timer-value {
  font-size: 1.3em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}
3. Code Quality & Refinement Suggestion
The handleAction function in useTransactionFlowLogic.ts is slightly more complex than it needs to be for its current usage. It handles multiple types for inputToClear when only a Ref<string> is ever passed. Simplifying this will improve readability and maintainability.

useTransactionFlowLogic.ts
-10
+3
 Show full code block 
  async function handleAction(
    actionType: ActionType,
    actionFn: () => Promise<any>,
    modalToClose?: Ref<boolean>,
    inputToClear?: Ref<string | null | string[]>
    inputToClear?: Ref<string>
  ) {
    if (!currentTransaction.value) {
      if (naiveMessage) naiveMessage.error('No active transaction to perform this action.');
      // Success notification is handled by the store via setNotificationInstance
      // naiveMessage.success(successMessage); // No longer needed here if store handles it
      if (modalToClose) modalToClose.value = false;
      if (inputToClear) {
        if (typeof inputToClear.value === 'string') {
          inputToClear.value = '';
        } else if (inputToClear.value === null) {
          // it's already null, do nothing or handle as needed
        } else if (Array.isArray(inputToClear.value)) {
          inputToClear.value = []; // Example for array type if ever used
        } else {
            inputToClear.value = null; // Default for Ref<string | null>
        }
        // All current usages pass a Ref<string>, so we can simplify this.
        inputToClear.value = '';
      }
    } catch (error: any) {
      // Error is set by the store method called in actionFn, or caught and set here if actionFn doesn't

This change makes the function's intent clearer and removes dead code paths, aligning with best practices for clean code.

I hope this report provides your team with the clarity and direction needed to implement the timer feature successfully.