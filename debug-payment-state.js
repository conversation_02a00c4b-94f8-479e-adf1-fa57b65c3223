const { PrismaClient } = require('@prisma/client');

async function checkPaymentMethodState() {
    const prisma = new PrismaClient();
    
    try {
        console.log('Checking payment method state for user: cmbqrmwcv0001vl48wlfffva4\n');
        
        // Check all payment methods for the user
        const allMethods = await prisma.paymentReceivingInfo.findMany({
            where: {
                userId: 'cmbqrmwcv0001vl48wlfffva4'
            },
            select: {
                id: true,
                currency: true,
                paymentMethodType: true,
                isDefaultForUser: true,
                isActive: true,
                createdAt: true
            },
            orderBy: { createdAt: 'asc' }
        });
        
        console.log('All payment methods:');
        console.table(allMethods);
        
        // Check specifically for CAD currency
        const cadMethods = allMethods.filter(m => m.currency === 'CAD');
        console.log(`\nCAD payment methods (${cadMethods.length}):`);
        console.table(cadMethods);
        
        // Check for constraint violations
        const constraintIssues = [];
        const currencies = [...new Set(allMethods.map(m => m.currency))];
        
        for (const currency of currencies) {
            const defaultMethods = allMethods.filter(m => 
                m.currency === currency && m.isDefaultForUser
            );
            
            if (defaultMethods.length > 1) {
                constraintIssues.push({
                    currency,
                    defaultCount: defaultMethods.length,
                    methodIds: defaultMethods.map(m => m.id)
                });
            }
        }
        
        if (constraintIssues.length > 0) {
            console.log('\n❌ CONSTRAINT VIOLATIONS FOUND:');
            console.table(constraintIssues);
        } else {
            console.log('\n✅ No constraint violations found');
        }
        
    } catch (error) {
        console.error('Error checking payment method state:', error);
    } finally {
        await prisma.$disconnect();
    }
}

checkPaymentMethodState();
