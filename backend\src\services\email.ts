import * as nodemailer from 'nodemailer'; // Corrected import
import { createTestAccount } from 'nodemailer';

// For development, we'll use ethereal.email (fake SMTP service)
// In production, replace with real SMTP credentials
let transporter: nodemailer.Transporter;

export async function initializeEmailTransporter() {
    if (process.env.NODE_ENV === 'production') {        // Production email setup
        transporter = nodemailer.createTransport({
            host: process.env.SMTP_HOST,
            port: Number(process.env.SMTP_PORT),
            secure: false, // false for 587, true for 465
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS,
            },
        });
    } else {
        // Development setup using ethereal.email
        const testAccount = await createTestAccount();
        transporter = nodemailer.createTransport({
            host: 'smtp.ethereal.email',
            port: 587,
            secure: false,
            auth: {
                user: testAccount.user,
                pass: testAccount.pass,
            },
        });
        console.log('Ethereal Email credentials:', testAccount);
    }
}

export async function sendVerificationEmail(email: string, token: string) {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/verify-email?token=${token}`;
    
    const info = await transporter.sendMail({
        from: '"MUNygo" <<EMAIL>>',
        to: email,
        subject: 'Verify your email address',
        text: `Welcome to MUNygo! Please verify your email address by clicking this link: ${verificationUrl}`,
        html: `
            <h1>Welcome to MUNygo!</h1>
            <p>Please verify your email address by clicking the button below:</p>
            <a href="${verificationUrl}" 
               style="display: inline-block; background: #4CAF50; color: white; padding: 10px 20px; 
                      text-decoration: none; border-radius: 5px; margin: 10px 0;">
                Verify Email
            </a>
            <p>Or copy and paste this link in your browser:</p>
            <p>${verificationUrl}</p>
            <p>This link will expire in 24 hours.</p>
        `,
    });

    if (process.env.NODE_ENV !== 'production') {
        // In development, log the preview URL from ethereal.email
        console.log('Preview URL:', nodemailer.getTestMessageUrl(info));
    }

    return info;
}
