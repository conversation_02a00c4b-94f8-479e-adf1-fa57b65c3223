#!/usr/bin/env pwsh

# This script tests the feature flag toggle for the new HomeView design
# Run this from the frontend directory

Write-Host "Testing HomeView Migration Feature Flag..." -ForegroundColor Green

# Start the development server in the background
Write-Host "Starting development server..." -ForegroundColor Yellow
Start-Process -FilePath "npm" -ArgumentList "run", "dev" -WorkingDirectory "." -WindowStyle Hidden

# Wait a moment for the server to start
Start-Sleep -Seconds 5

Write-Host "Development server should be running on http://localhost:5173" -ForegroundColor Green
Write-Host ""
Write-Host "Testing Instructions:" -ForegroundColor Cyan
Write-Host "1. Open the browser and navigate to http://localhost:5173" -ForegroundColor White
Write-Host "2. You should see the ORIGINAL HomeView design (feature flag is OFF by default)" -ForegroundColor White
Write-Host "3. Open browser console and run: localStorage.setItem('useNewHomeDesign', 'true')" -ForegroundColor White
Write-Host "4. Refresh the page - you should see the NEW component-based design" -ForegroundColor White
Write-Host "5. To toggle back: localStorage.setItem('useNewHomeDesign', 'false')" -ForegroundColor White
Write-Host "6. Refresh again to see the original design" -ForegroundColor White
Write-Host ""
Write-Host "Feature Flag Test Complete!" -ForegroundColor Green
Write-Host "Press any key to stop the development server..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop the development server
Get-Process | Where-Object {$_.ProcessName -eq "node" -and $_.MainWindowTitle -like "*vite*"} | Stop-Process -Force
Write-Host "Development server stopped." -ForegroundColor Yellow
