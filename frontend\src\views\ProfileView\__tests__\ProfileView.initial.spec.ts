import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import { useAuthStore } from '@/stores/auth';
import ProfileView from '../ProfileView.vue';
import { NConfigProvider, NMessageProvider, NSpin, NTag } from 'naive-ui';
import type { UserInfo } from '@/types/auth';
import { ref, computed, nextTick } from 'vue';

// --- Mock the entire auth store module ---
// Define refs for mock state and the mock function *outside* vi.mock
const mockUserRef = ref<UserInfo | null>(null);
const fetchUserProfileMock = vi.fn();
const updatePhoneVerificationStatusMock = vi.fn(); // Define mock for the action

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    // Return a mocked store structure
    user: mockUserRef, // Use the ref for reactive state
    fetchUserProfile: fetchUserProfileMock, // Use the mock function
    // Use the imported computed function
    isLoggedIn: computed(() => !!mockUserRef.value), // Example getter mock
    // Assign the mock function for the action
    updatePhoneVerificationStatus: updatePhoneVerificationStatusMock,
  }),
}));
// -----------------------------------------

// Required wrapper to provide Naive UI context
const TestWrapper = {
  template: `
    <n-config-provider>
      <n-message-provider>
        <profile-view />
      </n-message-provider>
    </n-config-provider>
  `,
  components: { ProfileView, NConfigProvider, NMessageProvider },
};

describe('ProfileView.vue - Initial Load & Display', () => {
  // No need for testingPinia instance variable if only using vi.mock

  beforeEach(() => {
    // Reset mocks and mock store state before each test
    vi.resetAllMocks();
    mockUserRef.value = null; // Reset the mock user state

    // Configure default mock behavior (can be overridden in tests)
    fetchUserProfileMock.mockResolvedValue(undefined);
    updatePhoneVerificationStatusMock.mockClear(); // Ensure other mocks are reset if needed
  });

  // afterEach is optional if vi.resetAllMocks covers cleanup

  it('shows loading spinner initially, calls fetchUserProfile, and hides spinner on success', async () => {
    // Arrange: Rely on default mock from beforeEach

    // Act
    const wrapper = mount(TestWrapper, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
        stubs: {}
      },
    });

    // Assert: Initial state
    await nextTick(); // Wait for initial render cycle
    const initialSpin = wrapper.findComponent(NSpin);
    expect(initialSpin.exists(), 'Initial spinner should exist').toBe(true);
    expect(initialSpin.props('show'), 'Initial spinner should be shown').toBe(true);

    expect(fetchUserProfileMock).toHaveBeenCalledTimes(1);

    // Act: Wait for promises
    await flushPromises();
    // Wait for DOM update after promise resolution and state change
    await nextTick();
    // Add one more tick just in case of multi-stage updates
    await nextTick();

    // Assert: Final state
    const finalSpin = wrapper.findComponent(NSpin);
    expect(finalSpin.exists(), 'Spinner component should still exist after fetch').toBe(true);
    expect(finalSpin.props('show'), 'Spinner should be hidden after success').toBe(false); // Re-checking this assertion

    wrapper.unmount();
  });

  it('displays user email and verification status after successful fetch', async () => {
    // Arrange: Define mock user and configure mock action
    const mockUser: UserInfo = {
      id: '1',
      email: '<EMAIL>',
      emailVerified: true,
      phoneVerified: false,
      phoneNumber: null,
    };
    fetchUserProfileMock.mockImplementation(async () => {
      // Simulate the action updating the store state
      mockUserRef.value = mockUser;
    });

    // Act: Mount and wait
    const wrapper = mount(TestWrapper, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
    });
    await flushPromises(); // Wait for fetchUserProfile mock and subsequent re-renders

    // Assert
    expect(wrapper.text()).toContain(mockUser.email);
    // Find the specific tags for verification status
    const tags = wrapper.findAllComponents(NTag);
    const emailTag = tags.find(tag => tag.text().includes('Verified') || tag.text().includes('Not Verified')); // Find email status tag
    const phoneTag = wrapper.find('[data-testid="phone-status-unverified"]');

    expect(emailTag?.text()).toContain('Verified'); // Email status
    expect(phoneTag.exists()).toBe(true);
    expect(phoneTag.text()).toContain('Not Verified');
    expect(wrapper.find('[data-testid="phone-status-verified"]').exists()).toBe(false);
    expect(wrapper.find('[data-testid="phone-verification-title"]').exists()).toBe(true);

    wrapper.unmount();
  });

  it('displays verified phone status and number after successful fetch', async () => {
    // Arrange
    const mockUser: UserInfo = {
      id: '2',
      email: '<EMAIL>',
      emailVerified: true,
      phoneVerified: true,
      phoneNumber: '+15551234567',
    };
    fetchUserProfileMock.mockImplementation(async () => {
      mockUserRef.value = mockUser;
    });

    // Act
    const wrapper = mount(TestWrapper, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
    });
    await flushPromises();

    // Assert
    const verifiedTag = wrapper.find('[data-testid="phone-status-verified"]');
    expect(verifiedTag.exists()).toBe(true);
    expect(verifiedTag.text()).toContain('Verified');
    expect(wrapper.text()).toContain(`(${mockUser.phoneNumber})`);
    expect(wrapper.find('[data-testid="phone-status-unverified"]').exists()).toBe(false);
    expect(wrapper.find('[data-testid="phone-verification-title"]').exists()).toBe(false);
    expect(wrapper.find('[data-testid="phone-verified-message"]').exists()).toBe(true); // Check for success message

    wrapper.unmount();
  });

  it('hides loading spinner after failed profile fetch', async () => {
    // Arrange: Mock fetchUserProfile to reject
    const fetchError = new Error('Network Error');
    fetchUserProfileMock.mockRejectedValue(fetchError);
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    // Act
    const wrapper = mount(TestWrapper, {
      global: { // Complete the global object
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      }, // Close global object
    }); // Close mount options

    // Assert: Initial spinner state
    await nextTick(); // Allow initial setup
    const initialSpin = wrapper.findComponent(NSpin);
    expect(initialSpin.exists(), 'Initial spinner should exist on fail path').toBe(true);
    expect(initialSpin.props('show'), 'Initial spinner should be shown on fail path').toBe(true);

    // Act: Wait for the rejected promise
    await flushPromises();
    // Add ticks for DOM updates after promise rejection and state change
    await nextTick();
    await nextTick();

    // Assert: Final state
    // 1. Spinner should be hidden
    const finalSpin = wrapper.findComponent(NSpin);
    expect(finalSpin.exists(), 'Spinner component should still exist after fail').toBe(true);
    expect(finalSpin.props('show'), 'Spinner should be hidden after fail').toBe(false);

    // 2. Check console error
    expect(consoleSpy).toHaveBeenCalledWith('Failed to fetch user profile:', expect.any(Error));
    // Optionally check the specific error message if needed
    expect(consoleSpy.mock.calls.some(call => call[1]?.message === 'Network Error')).toBe(true);

    consoleSpy.mockRestore(); // Restore console spy
    wrapper.unmount(); // Unmount the wrapper
  }); // Close the 'it' block

}); // Close the 'describe' block