<template>
  <!-- Enhanced Recent Activity Section with Mobile-First Design -->
  <section class="activity-section" v-if="shouldShowSection">
    <!-- Enhanced section title with entrance animation -->
    <h2 class="section-title">{{ $t('homeView.recentActivity') }}</h2>
    
    <!-- Skeleton Loading State -->
    <div v-if="!contentLoaded" class="skeleton-container">
      <div 
        v-for="index in 3" 
        :key="`skeleton-${index}`"
        class="skeleton-card"
        :style="{ animationDelay: `${index * 0.2}s` }"
      />
    </div>
    
    <!-- Enhanced Activity Content -->
    <div v-else class="activity-content">
      <n-grid :cols="1" :x-gap="24" :y-gap="16">
        <n-grid-item 
          v-for="(offer, index) in recentOffers" 
          :key="offer.id"
          class="activity-item"
          :style="{ animationDelay: `${(index * 0.15) + 0.3}s` }"
          @click="handleOfferClick(offer)"
        >
          <n-card class="offer-preview" @mousedown="handleCardPress">
            <div class="offer-preview-content">
              <div class="offer-info">
                <div class="offer-header">
                  <h4>
                    <span class="offer-type-icon">
                      {{ offer.type === 'BUY' ? '💰' : '💸' }}
                    </span>
                    {{ offer.type === 'BUY' ? $t('homeView.buying') : $t('homeView.selling') }} 
                    {{ (offer.currencyPair?.split('-')[0]) || 'Currency' }}
                  </h4>
                  <n-tag 
                    :type="getOfferStatusType(offer.status)" 
                    size="small"
                    class="status-tag"
                  >
                    {{ offer.status }}
                  </n-tag>
                </div>
                <p class="offer-description">
                  {{ formatOfferDescription(offer) }}
                </p>
              </div>
              <div class="offer-amount">
                <div class="amount-display">
                  <span class="currency-symbol">$</span>
                  <span class="amount-value">{{ formatAmount(offer.amount || 0, 'CAD') }}</span>
                </div>
                <div class="amount-label">{{ $t('homeView.amount') }}</div>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>
    
    <!-- Empty State with Animation -->
    <div v-if="contentLoaded && recentOffers.length === 0" class="empty-state">
      <div class="empty-icon">📋</div>
      <h3>{{ $t('homeView.noRecentActivity') }}</h3>
      <p>{{ $t('homeView.noRecentActivityDescription') }}</p>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { 
  NCard, 
  NGrid, 
  NGridItem, 
  NTag 
} from 'naive-ui';
import { formatAmount } from '@/utils/currencyUtils';
import { useOfferStore } from '@/stores/offerStore';
import { useMyOffersStore } from '@/stores/myOffersStore';

const { t } = useI18n();
const offerStore = useOfferStore();
const myOffersStore = useMyOffersStore();

// Enhanced loading states
const contentLoaded = ref(false);

// Types
interface OfferSummary {
  id?: string;
  type: 'BUY' | 'SELL';
  currencyPair?: string;
  baseRate?: number;
  status?: string;
  amount?: number;
}

// Enhanced computed properties
const recentOffers = computed(() => {
  // Show recent offers from both browse offers and user's own offers
  const allOffers = [
    ...offerStore.offers.slice(0, 3), // Use all offers for recent activity preview
    ...myOffersStore.myOffers.slice(0, 2)
  ];
  
  // Remove duplicates and limit to 5
  const uniqueOffers = allOffers.filter((offer, index, self) => 
    offer?.id && index === self.findIndex(o => o?.id === offer.id)
  ).slice(0, 5);
  
  return uniqueOffers;
});

const shouldShowSection = computed(() => {
  return !contentLoaded.value || recentOffers.value.length > 0;
});

// Enhanced loading lifecycle
onMounted(() => {
  // Simulate realistic loading with staggered content reveal
  setTimeout(() => {
    contentLoaded.value = true;
  }, 1200); // Delay to allow other sections to load first
});

// Enhanced interaction handlers
function handleCardPress(event: Event) {
  const cardElement = event.currentTarget as HTMLElement;
  cardElement.style.transform = 'scale(0.98)';
  
  // Reset after short delay (haptic-like feedback)
  setTimeout(() => {
    cardElement.style.transform = '';
  }, 150);
}

function handleOfferClick(offer: OfferSummary) {
  console.log(`📋 Viewing offer: ${offer.id}`);
  // Add haptic feedback or navigation logic here
  // Example: router.push({ name: 'OfferDetails', params: { id: offer.id } });
}

// Utility functions
function getOfferStatusType(status: string) {
  switch (status) {
    case 'ACTIVE':
      return 'success';
    case 'PENDING':
      return 'warning';
    case 'COMPLETED':
      return 'info';
    case 'CANCELLED':
      return 'error';
    default:
      return 'default';
  }
}

function formatOfferDescription(offer: OfferSummary) {  
  if (!offer.currencyPair || !offer.baseRate) {
    return '';
  }
  
  // Extract currencies from the currency pair (e.g., "IRR-CAD" -> ["IRR", "CAD"])
  const currencies = offer.currencyPair.split('-');
  if (currencies.length !== 2) {
    return '';
  }
  
  const [fromCurrency, toCurrency] = currencies;
  
  // Swap the order to show "IRR/CAD" format for proper exchange rate display
  // This shows how many units of the first currency per one unit of the second currency
  return t('homeView.atRate', { rate: `${offer.baseRate} ${toCurrency}/${fromCurrency}` });
}
</script>

<style scoped>
/* Mobile-first enhanced activity section */
.activity-section {
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

/* Enhanced section title with mobile optimization */
.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease 0.1s forwards;
}

/* Light theme section title */
[data-theme="light"] .section-title {
  color: #1e293b;
  text-shadow: none;
}

/* Dark theme section title */
[data-theme="dark"] .section-title {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

/* Enhanced skeleton loading */
.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skeleton-card {
  border-radius: 16px;
  height: 120px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  backdrop-filter: blur(10px);
  opacity: 0;
  animation: skeleton-shimmer 1.5s infinite, fadeInUp 0.6s ease forwards;
}

[data-theme="light"] .skeleton-card {
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
}

/* Enhanced activity content */
.activity-content {
  position: relative;
}

.activity-item {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
}

/* Enhanced offer preview cards with mobile-first design */
.offer-preview {
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  position: relative;
  /* Touch-friendly minimum height */
  min-height: 100px;
  touch-action: manipulation;
}

/* Light theme offer preview */
[data-theme="light"] .offer-preview {
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
}

/* Dark theme offer preview */
[data-theme="dark"] .offer-preview {
  background: rgba(26, 27, 46, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.3);
}

/* Enhanced hover/touch effects */
.offer-preview:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.12);
}

.offer-preview:active {
  transform: translateY(-2px) scale(0.98);
  transition: all 0.1s ease;
}

/* Light theme hover */
[data-theme="light"] .offer-preview:hover {
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.08);
}

/* Dark theme hover */
[data-theme="dark"] .offer-preview:hover {
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.4);
}

/* Enhanced card content layout */
.offer-preview-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.25rem;
  position: relative;
  z-index: 2;
  gap: 1rem;
}

/* Enhanced offer info section */
.offer-info {
  flex: 1;
  min-width: 0;
}

.offer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  gap: 0.5rem;
}

.offer-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
}

.offer-type-icon {
  font-size: 1.125rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Light theme offer header */
[data-theme="light"] .offer-header h4 {
  color: #1e293b;
}

/* Dark theme offer header */
[data-theme="dark"] .offer-header h4 {
  color: rgba(255, 255, 255, 0.95);
}

.status-tag {
  flex-shrink: 0;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
}

.offer-description {
  margin: 0;
  color: #666;
  font-size: 0.875rem;
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* Light theme offer description */
[data-theme="light"] .offer-description {
  color: #64748b;
}

/* Dark theme offer description */
[data-theme="dark"] .offer-description {
  color: rgba(255, 255, 255, 0.75);
}

/* Enhanced amount display */
.offer-amount {
  text-align: right;
  min-width: fit-content;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.amount-display {
  font-size: 1.25rem;
  font-weight: 700;
  color: #18a058;
  display: flex;
  align-items: baseline;
  gap: 0.125rem;
}

.currency-symbol {
  font-size: 1rem;
  opacity: 0.8;
}

.amount-value {
  font-family: tabular-nums;
  font-size: 1.25rem;
}

.amount-label {
  font-size: 0.75rem;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

[data-theme="dark"] .amount-label {
  color: rgba(255, 255, 255, 0.6);
}

/* Enhanced empty state */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease 0.3s forwards;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

[data-theme="light"] .empty-state h3 {
  color: #1e293b;
}

[data-theme="dark"] .empty-state h3 {
  color: rgba(255, 255, 255, 0.9);
}

.empty-state p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.6;
}

[data-theme="light"] .empty-state p {
  color: #64748b;
}

[data-theme="dark"] .empty-state p {
  color: rgba(255, 255, 255, 0.7);
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Tablet enhancements */
@media (min-width: 768px) {
  .activity-section {
    padding: 2.5rem 1.5rem;
  }
  
  .section-title {
    font-size: 1.875rem;
    margin-bottom: 2rem;
  }
  
  .skeleton-card {
    height: 140px;
  }
  
  .offer-preview-content {
    padding: 1.5rem;
    align-items: center;
  }
  
  .offer-header {
    align-items: center;
  }
  
  .offer-header h4 {
    font-size: 1.125rem;
  }
  
  .offer-description {
    font-size: 1rem;
  }
  
  .amount-display {
    font-size: 1.375rem;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  .activity-section {
    padding: 3rem 2rem;
  }
  
  .section-title {
    font-size: 2rem;
    margin-bottom: 2.5rem;
  }
  
  .skeleton-card {
    height: 160px;
  }
  
  .offer-preview-content {
    padding: 2rem;
  }
  
  .offer-header h4 {
    font-size: 1.25rem;
  }
  
  .offer-description {
    font-size: 1.125rem;
  }
  
  .amount-display {
    font-size: 1.5rem;
  }
  
  /* Enhanced desktop hover effects */
  .offer-preview:hover {
    transform: translateY(-6px) scale(1.03);
  }
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .activity-item,
  .section-title,
  .empty-state,
  .skeleton-card {
    animation: none !important;
    transition: none !important;
  }
  
  .offer-preview:hover {
    transform: none !important;
  }
}

/* High contrast accessibility */
@media (prefers-contrast: high) {
  .offer-preview {
    border-width: 2px;
  }
  
  [data-theme="light"] .offer-preview {
    border-color: #000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  
  [data-theme="dark"] .offer-preview {
    border-color: #fff;
    box-shadow: 0 4px 8px rgba(255, 255, 255, 0.3);
  }
}
</style>
