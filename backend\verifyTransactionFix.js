const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testTransactionStatusFix() {
  try {
    console.log('=== Testing Transaction Status Fix ===\n');
      // Find the transaction we saw earlier
    const testTransaction = await prisma.transaction.findFirst({
      where: {
        id: 'cmb0xaj5d0006vl20awiy3tgq' // The full transaction ID
      },
      include: {
        currencyAProvider: { select: { id: true, username: true, email: true } },
        currencyBProvider: { select: { id: true, username: true, email: true } }
      }
    });
    
    if (!testTransaction) {
      console.log('❌ Test transaction not found');
      return;
    }
    
    console.log('📋 Current Transaction State:');
    console.log(`ID: ${testTransaction.id}`);
    console.log(`Status: ${testTransaction.status}`);
    console.log(`First Payer ID: ${testTransaction.agreedFirstPayerId}`);
    console.log(`Currency A Provider: ${testTransaction.currencyAProvider.username || testTransaction.currencyAProvider.email} (${testTransaction.currencyAProviderId})`);
    console.log(`Currency B Provider: ${testTransaction.currencyBProvider.username || testTransaction.currencyBProvider.email} (${testTransaction.currencyBProviderId})`);
    console.log(`Payment declared by payer 1: ${testTransaction.paymentDeclaredAtPayer1 ? 'Yes' : 'No'}`);
    console.log(`Payment declared by payer 2: ${testTransaction.paymentDeclaredAtPayer2 ? 'Yes' : 'No'}`);
    
    // Analysis
    console.log('\n🔍 Analysis:');
    const firstPayerId = testTransaction.agreedFirstPayerId;
    const secondPayerId = firstPayerId === testTransaction.currencyAProviderId 
      ? testTransaction.currencyBProviderId 
      : testTransaction.currencyAProviderId;
    
    console.log(`First Payer: ${firstPayerId === testTransaction.currencyAProviderId ? 'Currency A' : 'Currency B'} Provider`);
    console.log(`Second Payer: ${secondPayerId === testTransaction.currencyAProviderId ? 'Currency A' : 'Currency B'} Provider`);
    
    // Check the logic
    if (testTransaction.paymentDeclaredAtPayer1 && testTransaction.status === 'AWAITING_FIRST_PAYER_CONFIRMATION') {
      console.log('\n❌ BUG CONFIRMED: First payer has declared payment but status is still AWAITING_FIRST_PAYER_CONFIRMATION');
      console.log('   This should be AWAITING_SECOND_PAYER_CONFIRMATION (waiting for second party to confirm first payment)');
      
      // Show what the frontend would display
      console.log('\n🖥️  Frontend Impact:');
      console.log('   Current status maps to: "Confirmation 2" step (index 3)');
      console.log('   Should show: "Confirmation 1" step (index 2)');
      
    } else if (testTransaction.paymentDeclaredAtPayer1 && testTransaction.status === 'AWAITING_SECOND_PAYER_CONFIRMATION') {
      console.log('\n✅ FIX WORKING: First payer has declared payment and status is correctly AWAITING_SECOND_PAYER_CONFIRMATION');
    }
    
    // Check our fix by looking at the service code
    console.log('\n🔧 Our Fix Status:');
    console.log('   ✅ Modified declarePayment method in transactionService.ts');
    console.log('   ✅ Changed status from AWAITING_FIRST_PAYER_CONFIRMATION to AWAITING_SECOND_PAYER_CONFIRMATION');
    console.log('   ✅ Added username fetching for system messages');
    console.log('   ✅ Fixed system message emission without requiring database system user');
    
    console.log('\n📝 Next Steps:');
    console.log('   1. Test the declarePayment endpoint with a valid user to see if the fix works');
    console.log('   2. Check if the frontend correctly displays "Confirmation 1" after the fix');
    console.log('   3. Verify system messages are sent to chat');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testTransactionStatusFix();
