<template>
  <div class="chat-view-layout">
    <!-- Background Chat Area (Full Height) -->
    <div ref="messageListContainerRef" class="messages-scroll-container">
      <n-spin :show="store.isLoadingSession">
        <div v-if="store.sessionError" class="error-message">
          <n-alert :title="t('chat.errorLoadingChat')" type="error">
            {{ store.sessionError }}
          </n-alert>
        </div>

        <div v-if="!store.isLoadingSession && !store.sessionError && store.messages.length === 0" class="empty-chat">
          <n-empty :description="t('chat.noMessagesYet')" />
        </div>        <!-- Message List itself -->
        <div 
          v-if="!store.isLoadingSession && !store.sessionError && store.messages.length > 0" 
          class="message-list"
          :style="{ paddingTop: messageListPadding }"
        >
          <div
            v-for="msg in store.messages"
            :key="msg.messageId"
            class="message-item"
            :class="{
              'sent-by-me': !msg.isSystemMessage && msg.sender?.id === authStore.user?.id,
              'system-message': msg.isSystemMessage, // This handles system messages
              'received-by-me': !msg.isSystemMessage && msg.sender?.id !== authStore.user?.id,
            }"
          >
            <!-- Regular message -->
            <template v-if="!msg.isSystemMessage && msg.sender">
              <n-avatar
                class="message-avatar"
                :src="msg.sender.profile?.avatarUrl || undefined"
                round
                size="small"
              />
              <div class="message-bubble">
                <n-text strong v-if="msg.sender.id !== authStore.user?.id" class="message-sender-name">
                  {{ msg.sender.username }}
                </n-text>
                <n-text class="message-content">{{ msg.content }}</n-text>
                <n-text class="message-timestamp">{{ formatTimestamp(msg.createdAt) }}</n-text>
              </div>
            </template>
            <!-- System message -->
            <template v-if="msg.isSystemMessage">
              <div class="system-message-content">
                <n-text strong>{{ msg.content }}</n-text>
              </div>
              <div> <!-- Wrapper for the timestamp -->
                <n-text class="message-timestamp system-timestamp">{{ formatTimestamp(msg.createdAt) }}</n-text>
              </div>
            </template>
          </div>
        </div>
      </n-spin>
    </div>    <!-- Floating Transaction Section with Glassmorphism -->    <div 
      ref="floatingTransactionRef" 
      class="floating-transaction-section" 
    >
      <!-- Compact User Header -->
      <div v-if="store.otherParticipant" class="chat-user-info-header">
        <n-space align="center" :size="8">
          <n-avatar :src="store.otherParticipant?.profile?.avatarUrl || undefined" round size="small" />
          <div class="user-info-compact">
            <n-text strong>{{ store.otherParticipant?.username || t('chat.title') }}</n-text>
            <n-text :type="store.isConnected ? 'success' : 'error'" depth="3" style="font-size: 0.75em;">
              {{ store.isConnected ? t('chat.online') : t('chat.offline') }}
            </n-text>
          </div>
        </n-space>
      </div>

      <!-- Compact Transaction Info -->
      <div class="transaction-info-wrapper">
        <!-- Transaction Detail Section -->
        <div class="transaction-detail-section">          <div class="section-header-compact">
            <div class="transaction-header-container">
              <TransactionHeaderCollapsed 
                v-if="isTransactionDetailCollapsed && transactionDetails"
                :transaction-details="transactionDetails"
              />
              <n-text v-else strong style="font-size: 0.9em;">{{ $t('chat.transactionDetails') }}</n-text>
              
              <n-button size="tiny" text type="primary" @click="toggleTransactionDetail" class="toggle-btn">
                <template #icon>
                  <n-icon size="14">
                    <ChevronDown v-if="isTransactionDetailCollapsed" />
                    <ChevronUp v-else />
                  </n-icon>
                </template>
              </n-button>
            </div>
          </div>

          <div v-show="!isTransactionDetailCollapsed" class="section-content-compact">            <TransactionDetailCard
              v-if="transactionDetails"
              :title="transactionDetails.title"
              :userA="transactionDetails.userA"
              :userB="transactionDetails.userB"
              :userAPaysAmount="transactionDetails.userAPaysAmount"
              :userAPaysCurrency="transactionDetails.userAPaysCurrency"
              :userBPaysAmount="transactionDetails.userBPaysAmount"
              :userBPaysCurrency="transactionDetails.userBPaysCurrency"
              :exchangeRateText="transactionDetails.exchangeRateText"
            />
            <div v-else-if="store.isLoadingTransactionInfo || store.isLoadingSession" class="loading-placeholder">
              <n-spin size="small" />
              <span style="margin-left: 8px; color: var(--n-text-color-disabled);">Loading transaction details...</span>
            </div>
          </div>
        </div>

        <!-- Transaction Flow Section -->
        <div class="transaction-flow-section">
          <div class="section-header-compact">
            <n-space align="center" justify="space-between" :size="4">
              <div class="header-content-compact">
                <div v-if="isTransactionFlowCollapsed && store.transactionInfo" class="collapsed-info-compact">
                  <n-tag size="tiny" type="info">{{ store.transactionInfo.offerType || 'Exchange' }}</n-tag>
                  <span style="font-size: 0.75em; color: var(--n-text-color-disabled);">{{ getProgressSummary() }}</span>
                </div>
                <n-text v-else strong style="font-size: 0.9em;">Progress</n-text>
              </div>
              
              <n-button size="tiny" text type="primary" @click="toggleTransactionFlow">
                <template #icon>
                  <n-icon size="14">
                    <ChevronDown v-if="isTransactionFlowCollapsed" />
                    <ChevronUp v-else />
                  </n-icon>
                </template>
              </n-button>
            </n-space>
          </div>

          <div v-show="!isTransactionFlowCollapsed" class="section-content-compact">
            <TransactionFlowCardV3 
              v-if="store.currentChatSessionId"
              :chat-session-id="store.currentChatSessionId" 
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Footer -->
    <div class="chat-footer">
      <n-input
        v-model:value="newMessageContent"
        :placeholder="t('chat.typeMessage')"
        @keyup.enter="handleSendMessage"
        :disabled="isSending || !store.currentChatSessionId"
        style="flex-grow: 1; margin-right: 8px;"
      >
        <template #suffix>
          <n-button type="primary" @click="handleSendMessage" :loading="isSending" :disabled="!newMessageContent.trim() || !store.currentChatSessionId">
            {{ t('chat.sendMessage') }}
          </n-button>
        </template>
      </n-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import {
  useMessage,
  NSpin,
} from 'naive-ui';
import { 
  KeyboardArrowDownFilled as ChevronDown,
  KeyboardArrowUpFilled as ChevronUp
} from '@vicons/material';

import { useTranslation } from '@/composables/useTranslation';
import { useChatStore } from '@/stores/chatStore'; // Removed ChatMessageWithId, ChatParticipant
import { useAuthStore } from '@/stores/auth';
import TransactionDetailCard, { type UserTransactionInfo } from '@/components/TransactionDetailCard.vue';
import TransactionFlowCardV3 from '@/components/TransactionFlowCardV3.vue'; // Add this import
import TransactionHeaderCollapsed from '@/components/TransactionHeaderCollapsed.vue';
import { formatAmountForDisplay } from '@/utils/currencyUtils';

const { t } = useTranslation();

const route = useRoute();
const store = useChatStore();
const authStore = useAuthStore();
const messageApi = useMessage();

const newMessageContent = ref('');
const isSending = ref(false);

const messageListContainerRef = ref<HTMLDivElement | null>(null);
const floatingTransactionRef = ref<HTMLDivElement | null>(null);

// Simple collapsible transaction section state
const isTransactionDetailCollapsed = ref(false);
const isTransactionFlowCollapsed = ref(false);

// Simple toggle functions
const toggleTransactionDetail = () => {
  console.log('Toggling transaction detail, current state:', isTransactionDetailCollapsed.value);
  isTransactionDetailCollapsed.value = !isTransactionDetailCollapsed.value;
  console.log('New state:', isTransactionDetailCollapsed.value);
};

const toggleTransactionFlow = () => {
  console.log('Toggling transaction flow, current state:', isTransactionFlowCollapsed.value);
  isTransactionFlowCollapsed.value = !isTransactionFlowCollapsed.value;
  console.log('New state:', isTransactionFlowCollapsed.value);
};

const scrollToBottom = async (smooth: boolean = false) => {
  await nextTick();
  if (messageListContainerRef.value) {
    messageListContainerRef.value.scrollTo({
      top: messageListContainerRef.value.scrollHeight,
      behavior: smooth ? 'smooth' : 'auto',
    });
  }
};

const formatTimestamp = (timestamp: string | Date): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// Add additional computed properties for collapsed summary
const transactionDetails = computed(() => {
  if (import.meta.env.DEV) {
    console.log('[ChatView] Computing transactionDetails...');
  }
  const txInfo = store.transactionInfo;
  const chatParticipants = store.participants; // Use store.participants

  // currentUser can be derived from authStore or potentially from chatParticipants if needed
  const currentUser = authStore.user;

  if (import.meta.env.DEV) {
    console.log('[ChatView] Data check:', {
      txInfo,
      currentUser,
      chatParticipants,
      isLoadingTransactionInfo: store.isLoadingTransactionInfo,
      isLoadingSession: store.isLoadingSession
    });
  }
  if (!txInfo || !currentUser || !chatParticipants || !chatParticipants.currentUser || !chatParticipants.otherUser) {
    // Debug: Log what's actually missing
    if (import.meta.env.DEV) {
      console.log('[ChatView Debug] Missing data check:', {
        txInfo: !!txInfo,
        currentUser: !!currentUser,
        chatParticipants: !!chatParticipants,
        chatParticipantsCurrentUser: !!chatParticipants?.currentUser,
        chatParticipantsOtherUser: !!chatParticipants?.otherUser,
        isLoadingTransactionInfo: store.isLoadingTransactionInfo,
        isLoadingSession: store.isLoadingSession,
        currentChatSessionId: !!store.currentChatSessionId
      });
    }
      // Only warn if we're not in a loading state and we have a session ID
    // Add a small delay to avoid warnings during initial data loading
    if (!store.isLoadingTransactionInfo && !store.isLoadingSession && store.currentChatSessionId) {
      // Only warn once per session to avoid spam
      const warningKey = `transaction-missing-${store.currentChatSessionId}`;
      if (!sessionStorage.getItem(warningKey)) {
        console.warn('ChatView: Transaction info, current user, or chat participants are missing for TransactionDetailCard');
        sessionStorage.setItem(warningKey, 'warned');
      }
    }
    return undefined;
  }
  // Additional check for offerCreator and otherUser in txInfo
  if (!txInfo.offerCreator || !txInfo.otherUser) {
    if (import.meta.env.DEV) {
      console.log('[ChatView Debug] Transaction info missing offerCreator or otherUser:', {
        hasOfferCreator: !!txInfo.offerCreator,
        hasOtherUser: !!txInfo.otherUser,
        txInfo
      });
    }    if (!store.isLoadingTransactionInfo && !store.isLoadingSession && store.currentChatSessionId) {
      // Only warn once per session to avoid spam
      const warningKey = `transaction-offerCreator-missing-${store.currentChatSessionId}`;
      if (!sessionStorage.getItem(warningKey)) {
        console.warn('ChatView: Transaction info missing offerCreator or otherUser fields for TransactionDetailCard');
        sessionStorage.setItem(warningKey, 'warned');
      }
    }
    return undefined;
  }

  // Extract user information from chat participants
  const currentUserFromChat = chatParticipants.currentUser;
  const otherUserFromChat = chatParticipants.otherUser;
    // Determine who is User A (left) and User B (right) for the transaction header
  // Use the offer creator as User A, otherwise sort by user ID for consistency
  let userA: typeof currentUserFromChat;
  let userB: typeof otherUserFromChat;
  // Determine userA and userB for the transaction header.
  // userA is conventionally the offer creator or the one whose currency is the base of the exchange rate.
  const offerCreatorId = txInfo.offerCreator?.id;

  if (offerCreatorId) {
    if (currentUserFromChat.id === offerCreatorId) {
      userA = currentUserFromChat;
      userB = otherUserFromChat;
    } else if (otherUserFromChat.id === offerCreatorId) {
      userA = otherUserFromChat;
      userB = currentUserFromChat;
    } else {
      // This case means offerCreatorId is present but doesn't match either participant.
      // This might indicate a data consistency issue. Fallback to sorting by ID.
      console.warn(
        `ChatView: Offer creator ID (${offerCreatorId}) does not match current user (${currentUserFromChat.id}) or other user (${otherUserFromChat.id}). Falling back to sorting by ID for transaction header.`
      );
      if (currentUserFromChat.id.localeCompare(otherUserFromChat.id) < 0) {
        userA = currentUserFromChat;
        userB = otherUserFromChat;
      } else {
        userA = otherUserFromChat;
        userB = currentUserFromChat;
      }
    }
  } else {
    // Fallback strategy if offerCreatorId is not available (e.g., chatSession.offer is null).
    // Sort by ID to get a consistent assignment for userA and userB.
    console.warn(
      'ChatView: Offer creator ID not available. Falling back to sorting by ID for transaction header.'
    );
    if (currentUserFromChat.id.localeCompare(otherUserFromChat.id) < 0) {
      userA = currentUserFromChat;
      userB = otherUserFromChat;
    } else {
      userA = otherUserFromChat;
      userB = currentUserFromChat;
    }
  }
  // Fallback if userA or userB somehow remain unassigned (should not happen with the logic above)
  if (!userA || !userB) {
    console.error('ChatView: userA or userB is undefined after assignment logic. Data:', {
      userA: userA?.username,
      userB: userB?.username,
      currentUserFromChat: currentUserFromChat?.username,
      otherUserFromChat: otherUserFromChat?.username
    });
    userA = currentUserFromChat;
    userB = otherUserFromChat;
  }

  // Debug log to check if users are distinct
  if (import.meta.env.DEV) {
    console.log('[ChatView] User assignment check:', {
      userAName: userA.username,
      userBName: userB.username,
      userAId: userA.id,
      userBId: userB.id,
      areUsersSame: userA.id === userB.id,
      areUsernamesSame: userA.username === userB.username
    });
  }

  let userAInfo: UserTransactionInfo;
  let userBInfo: UserTransactionInfo;
  let userAId: string;
  let userBId: string;
  let userAName: string;
  let userBName: string;
  
  // Current user is always User A (left side), other user is User B (right side)
  userAInfo = {
    name: userA.username || 'You',
    avatarUrl: undefined,
    level: userA.reputationLevel || 0,
    levelType: 'star',
    avatarBorderColor: '#FFD700',
  };
  userBInfo = {
    name: userB.username || 'Other User',
    avatarUrl: undefined,
    level: userB.reputationLevel || 0,
    levelType: 'shield',
    avatarBorderColor: '#4CAF50',
  };
  userAId = userA.id;
  userBId = userB.id;
  userAName = userA.username;
  userBName = userB.username;

  const currencyPair = txInfo.currencyPair;
  const offerAmount = txInfo.amount;
  const exchangeRate = txInfo.exchangeRate;
  const offerType = txInfo.offerType;

  if (!currencyPair || typeof offerAmount !== 'number' || typeof exchangeRate !== 'number' || !offerType) {
    console.warn('ChatView: Offer details (currencyPair, amount, exchangeRate, offerType) are incomplete.');
    return undefined;
  }

  const parts = currencyPair.split(/[/|-]/).map(c => c.trim());
  if (parts.length < 2) {
    console.warn('ChatView: Invalid currencyPair format:', currencyPair);
    return undefined;
  }  const primaryCurrency = parts[0];
  const secondaryCurrency = parts[1];
  // Use actual backend transaction data instead of recalculating based on offer type
  // The backend already correctly determines who provides what currency based on the offer type
  const { cadSellerId, finalCadAmount, finalAssetAmount, finalAssetCurrency } = txInfo;
  
  if (!cadSellerId || finalCadAmount === null || finalCadAmount === undefined || 
      finalAssetAmount === null || finalAssetAmount === undefined || !finalAssetCurrency) {
    console.warn('ChatView: Missing transaction data from backend');
    return undefined;
  }

  // Determine payments based on actual backend data and user positions
  let finalUserAPaysAmount: number;
  let finalUserAPaysCurrency: string;
  let finalUserBPaysAmount: number;
  let finalUserBPaysCurrency: string;

  // Determine who provides what based on the cadSellerId from backend
  if (cadSellerId === userA.id) {
    // User A (left side) provides CAD, User B (right side) provides asset
    finalUserAPaysAmount = finalCadAmount;
    finalUserAPaysCurrency = 'CAD';
    finalUserBPaysAmount = finalAssetAmount;
    finalUserBPaysCurrency = finalAssetCurrency;
  } else {
    // User B (right side) provides CAD, User A (left side) provides asset
    finalUserAPaysAmount = finalAssetAmount;
    finalUserAPaysCurrency = finalAssetCurrency;
    finalUserBPaysAmount = finalCadAmount;
    finalUserBPaysCurrency = 'CAD';
  }
    const title = `Trade: ${primaryCurrency} for ${secondaryCurrency}`;
  const rateText = `1 ${primaryCurrency} = ${formatAmountForDisplay(exchangeRate, secondaryCurrency, true)} ${secondaryCurrency}`;
  return {
    title,
    userA: userAInfo,
    userB: userBInfo,
    userAPaysAmount: finalUserAPaysAmount,
    userAPaysCurrency: finalUserAPaysCurrency,
    userBPaysAmount: finalUserBPaysAmount,
    userBPaysCurrency: finalUserBPaysCurrency,
    exchangeRateText: rateText,
    // Add user IDs and avatar URLs for collapsed view
    userAId: userAId,
    userBId: userBId,
    userAName: userAName,
    userBName: userBName,
    userAAvatar: undefined, // Add if available
    userBAvatar: undefined, // Add if available
  };
});

const getProgressSummary = () => {
  if (!store.transactionInfo) return t('chat.waitingForDetails');
  
  // Based on available transaction info properties
  const offerType = store.transactionInfo.offerType;
  
  if (offerType === 'BUY') {
    return t('chat.buyingCurrencyExchange');
  } else if (offerType === 'SELL') {
    return t('chat.sellingCurrencyExchange');
  } else {
    return t('chat.exchangeInProgress');
  }
};

// Computed style for message list padding based on collapsed sections
// Replace this computed property in your script section
const messageListPadding = computed(() => {
  // Much smaller base padding for compact layout
  let basePadding = 45; // Compact header height
  
  // Minimal heights for sections
  if (!isTransactionDetailCollapsed.value) {
    basePadding += 80; // Much smaller when expanded
  } else {
    basePadding += 25; // Tiny when collapsed
  }
  
  if (!isTransactionFlowCollapsed.value) {
    basePadding += 70; // Much smaller when expanded
  } else {
    basePadding += 25; // Tiny when collapsed
  }
  
  return `${basePadding}px`;
});

onMounted(async () => {
  const chatSessionId = route.params.chatSessionId as string;
  if (chatSessionId) {
    await store.loadChatSession(chatSessionId);
    setTimeout(() => {
      scrollToBottom(false);
    }, 150);
  } else {
    messageApi.error('Chat session ID is missing.');
  }
});

onUnmounted(() => {
  store.clearChatState();
});

watch(
  () => store.messages,
  async (newMessages, oldMessages) => {
    if (messageListContainerRef.value && newMessages.length > (oldMessages?.length || 0)) {
      const { scrollTop, scrollHeight, clientHeight } = messageListContainerRef.value;
      const scrollThreshold = 50;
      if (scrollHeight - scrollTop - clientHeight < scrollThreshold) {
        await scrollToBottom(true);
      }
    } else if (newMessages.length > 0 && oldMessages?.length === 0) {
      await scrollToBottom(false);
    }
    setTimeout(() => scrollToBottom(true), 100);
  },
  { deep: true, flush: 'post' }
);

watch(
  () => store.sessionError,
  (newError) => {
    if (newError) {
      messageApi.error(`Error in chat session: ${newError}`);
    }
  }
);

const handleSendMessage = async () => {
  if (!newMessageContent.value.trim() || !store.currentChatSessionId) return;
  isSending.value = true;
  try {
    await store.sendMessage(newMessageContent.value.trim());
    newMessageContent.value = '';
  } catch (error: any) {
    messageApi.error(error.message || 'Failed to send message.');
  } finally {
    isSending.value = false;
  }
};
</script>

<style scoped>
/* Compact layout improvements */
.chat-view-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
  background-color: var(--n-body-color, var(--n-color));
  position: relative;
  isolation: isolate;
}

/* Compact floating section */
.floating-transaction-section {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  max-height: 25vh;
  overflow: visible;
  background: transparent;
  transition: all 0.3s ease;
}

/* Compact user header */
.chat-user-info-header {
  padding: 6px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

.user-info-compact {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

/* Ultra-compact transaction wrapper */
.transaction-info-wrapper {
  padding: 4px 12px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  max-height: 20vh;
  overflow-y: auto;
}

/* Compact section headers */
.section-header-compact {
  padding: 4px 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  min-height: 24px;
}

.header-content-compact {
  flex: 1;
  min-width: 0;
}

.transaction-header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}

.transaction-header-container .toggle-btn {
  flex-shrink: 0;
}

/* Styling for arrow icons */
.arrow-icon.n-icon {
  font-size: 15px !important;
}

.user-name {
  font-size: 0.7em;
  font-weight: 500;
  color: var(--n-text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
}

.section-content-compact {
  padding: 4px 0;
}

/* Dark mode for main layout */
[data-theme="dark"] .chat-view-layout {
  background-color: var(--n-body-color);
}

/* Floating Transaction Section with separate navbar and glassmorphism content */
.floating-transaction-section {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  max-height: 45vh;
  
  /* Remove overflow from outer container - no scrollbar here */
  overflow: visible;
  
  /* Remove glassmorphism from the container itself */
  background: transparent;
  border-bottom: none;
  box-shadow: none;
  
  /* Smooth transition for interactions */
  transition: all 0.3s ease;
}

.floating-transaction-section .chat-user-info-header {
  padding: 10px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.25);
  position: relative;
  z-index: 51; /* Higher than parent to ensure it's on top */
  
  /* Enhanced glassmorphism for header */
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* Dark mode for floating user info header */
[data-theme="dark"] .floating-transaction-section .chat-user-info-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  background: rgba(20, 20, 30, 0.4);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.5),
    0 2px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.floating-transaction-section .transaction-info-wrapper {
  padding: 8px 16px;
  border-radius: 0 0 8px 8px;
  position: relative;
  z-index: 52; /* Higher than navbar */
  
  /* Apply glassmorphism only to transaction content */
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Enhanced transaction wrapper for collapsible sections */
.transaction-info-wrapper {
  padding: 8px 16px;
  border-radius: 0 0 8px 8px;
  position: relative;
  z-index: 52; /* Higher than navbar */
  
  /* Apply glassmorphism only to transaction content */
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  
  /* Ensure wrapper doesn't expand beyond reasonable limits */
  max-height: 40vh;
  overflow-y: auto;
}

/* Transaction Scrollable Content - this is where the scroll happens */
.transaction-scrollable-content {
  max-height: 35vh;
  overflow-y: auto;
  padding: 8px 0;
  
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.transaction-scrollable-content::-webkit-scrollbar {
  width: 6px;
}

.transaction-scrollable-content::-webkit-scrollbar-track {
  background: transparent;
}

.transaction-scrollable-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.transaction-scrollable-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Dark mode for scrollable content */
[data-theme="dark"] .transaction-scrollable-content {
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

[data-theme="dark"] .transaction-scrollable-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .transaction-scrollable-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* Invisible spacer to create scrollable space in transaction section */
.scroll-spacer {
  height: 400px; /* Increased for more scrollable space */
  width: 100%;
  pointer-events: none;
  opacity: 0;
}

/* Dark mode glassmorphism for transaction wrapper only */
[data-theme="dark"] .transaction-info-wrapper {
  background: rgba(20, 20, 30, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Transaction Detail Section */
.transaction-detail-section {
  margin-bottom: 12px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

.transaction-detail-section.collapsed {
  margin-bottom: 8px;
}

/* Transaction Flow Section */
.transaction-flow-section {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

/* Loading and Error Placeholders */
.loading-placeholder,
.error-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 10px;
}

.loading-placeholder {
  min-height: 100px;
}

.error-placeholder {
  min-height: 60px;
  opacity: 0.7;
}

/* Collapsed Transaction Detail Bar */
.collapsed-transaction-bar {
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.collapsed-transaction-bar:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .collapsed-transaction-bar {
  background: rgba(20, 20, 30, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .collapsed-transaction-bar:hover {
  background: rgba(20, 20, 30, 0.6);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Collapsed Transaction Flow Bar */
.collapsed-flow-bar {
  cursor: pointer;
  transition: all 0.2s ease;
}

.collapsed-flow-bar:hover {
  transform: translateY(-1px);
}

.collapsed-flow-bar .n-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.collapsed-flow-bar:hover .n-card {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .collapsed-flow-bar .n-card {
  background: rgba(20, 20, 30, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .collapsed-flow-bar:hover .n-card {
  background: rgba(20, 20, 30, 0.6);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Messages container - now full height background */
.messages-scroll-container {
  height: 100%;
  overflow-y: auto;
  padding: 0 12px;
  padding-top: 10px;
  padding-bottom: 60px;
  background-color: var(--n-body-color, var(--n-color));
  box-sizing: border-box;
}

/* Dark mode for messages container */
[data-theme="dark"] .messages-scroll-container {
  background-color: var(--n-body-color);
  /* Improve scrollbar styling in dark mode */
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background: var(--n-color-embedded);
  }
  &::-webkit-scrollbar-thumb {
    background: var(--n-divider-color);
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: var(--n-border-color);
  }
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 12px;
  transition: padding-top 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-footer {
  padding: 8px 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 40;
  height: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}
.message-item {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 70%;
}

.message-item.sent-by-me {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-item.received-by-me {
  margin-right: auto;
}

.message-item.system-message {
  max-width: 100%;
  margin-top: 8px;
  margin-bottom: 8px;
  flex-direction: column; /* Stack content and timestamp vertically */
  align-items: center;   /* Center items horizontally */
  gap: 2px; /* Adjust space between content bubble and timestamp, works with timestamp's margin-top */
}

.message-avatar {
  margin-bottom: 5px;
  flex-shrink: 0;
}

.message-bubble {
  padding: 8px 12px;
  border-radius: 18px;
  display: flex;
  flex-direction: column;
  box-shadow: var(--n-box-shadow-1);
  word-break: break-word;
  transition: all 0.2s ease;
  /* Better contrast for input in dark mode */
  [data-theme="dark"] & {
    background-color: var(--n-color);
    border-color: var(--n-divider-color);
  }
}

/* Received messages (not sent by me, not system) */
.message-item:not(.sent-by-me):not(.system-message) .message-bubble {
  background-color: var(--n-card-color);
  border: 1px solid var(--n-border-color);
}

/* Dark mode for received messages */
[data-theme="dark"] .message-item:not(.sent-by-me):not(.system-message) .message-bubble {
  background-color: var(--n-card-color);
  border: 1px solid var(--n-divider-color);
}

/* Sent messages */
.message-item.sent-by-me .message-bubble {
  background-color: var(--n-color-target);
  border: none;
}

/* Dark mode for sent messages */
[data-theme="dark"] .message-item.sent-by-me .message-bubble {
  background-color: var(--n-primary-color);
  color: #fff;
}

/* Text color for received messages */
.message-item:not(.sent-by-me):not(.system-message) .message-bubble .n-text.message-content {
  color: var(--n-text-color) !important;
}
.message-item:not(.sent-by-me):not(.system-message) .message-bubble .n-text.message-timestamp {
  color: var(--n-text-color-disabled) !important;
}
.message-item:not(.sent-by-me):not(.system-message) .message-bubble .n-text.message-sender-name {
  font-size: 0.8em;
  color: var(--n-text-color-disabled);
  margin-bottom: 2px;
}


/* Text color for sent messages */
.message-item.sent-by-me .message-bubble .n-text {
  color: var(--n-text-color) !important;
}
.message-item.sent-by-me .message-bubble .n-text.message-timestamp {
  opacity: 0.85;
}

/* Dark mode text for sent messages */
[data-theme="dark"] .message-item.sent-by-me .message-bubble .n-text {
  color: #fff !important;
}
[data-theme="dark"] .message-item.sent-by-me .message-bubble .n-text.message-timestamp {
  color: rgba(255, 255, 255, 0.7) !important;
}


.message-content {
  flex-grow: 1;
}

.message-timestamp {
  font-size: 0.75rem;
  margin-top: 4px;
  align-self: flex-end;
}

.error-message, .empty-chat {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

/* New styles for system messages */
.system-message {
  justify-content: center;
  text-align: center;
}

.system-message-content {
  background-color: var(--n-merged-color, var(--n-color-embedded));
  padding: 6px 12px;
  border-radius: 10px;
  display: inline-block;
  max-width: 80%;
  text-align: center;
  border: 1px solid var(--n-border-color);
}

/* Dark mode for system messages */
[data-theme="dark"] .system-message-content {
  background-color: var(--n-color-embedded);
  border: 1px solid var(--n-divider-color);
  color: var(--n-text-color);
}

.message-item.system-message .message-timestamp.system-timestamp {
  color: var(--n-text-color-disabled);
  font-size: 0.7rem;
  margin-top: 2px; 
  display: block; 
  text-align: center; 
}

/* Enhanced dark mode scrollbar styling */
[data-theme="dark"] .messages-scroll-container::-webkit-scrollbar {
  width: 6px;
}
[data-theme="dark"] .messages-scroll-container::-webkit-scrollbar-track {
  background: var(--n-color-embedded);
  border-radius: 3px;
}
[data-theme="dark"] .messages-scroll-container::-webkit-scrollbar-thumb {
  background: var(--n-divider-color);
  border-radius: 3px;
}
[data-theme="dark"] .messages-scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--n-border-color);
}

/* Better message bubble interactions */
.message-bubble {
  transition: all 0.2s ease;
}
.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: var(--n-box-shadow-2);
}
[data-theme="dark"] .message-bubble:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

/* Enhanced transaction wrapper for dark mode - remove since now using glassmorphism */
/* [data-theme="dark"] .transaction-info-wrapper {
  border-top: 1px solid var(--n-divider-color);
} */

/* Better footer input styling in dark mode */
[data-theme="dark"] .chat-footer .n-input {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
}

/* Enhanced input styling for glassmorphism */
.chat-footer .n-input {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  transition: all 0.2s ease;
}

.chat-footer .n-input:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] .chat-footer .n-input:hover {
  background-color: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
}

/* Enhanced error states for dark mode */
[data-theme="dark"] .error-message, 
[data-theme="dark"] .empty-chat {
  color: var(--n-text-color);
}

/* Adjust error and empty states for floating layout */
.error-message, .empty-chat {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  margin-top: max(280px, 25vh); /* Space for floating transaction section */
  height: calc(100vh - max(280px, 25vh) - 72px); /* Account for floating section and footer */
}

/* Transaction Section Headers */
.section-header {
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  min-height: 48px;
}

.section-header:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.18);
}

[data-theme="dark"] .section-header {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .section-header:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.12);
}

.collapsed-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.collapsed-info.mobile-compact {
  gap: 4px;
  font-size: 12px;
  flex-wrap: nowrap;
  overflow: hidden;
}

.user-compact {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 0;
}

.user-compact .username {
  font-size: 0.75em;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
}

.exchange-compact {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  justify-content: center;
  min-width: 0;
}

.amount-tag {
  font-size: 0.7em !important;
  padding: 2px 6px !important;
  border-radius: 12px !important;
}

.arrow-icon {
  color: var(--primary-color);
  flex-shrink: 0;
}

.rate-text {
  font-size: 0.65em;
  color: var(--text-color-3);
  margin-left: 4px;
  white-space: nowrap;
}

.toggle-btn.mobile-icon-only {
  padding: 4px !important;
  min-width: 32px !important;
  width: 32px !important;
  height: 32px !important;
}

.toggle-btn.mobile-icon-only .n-button__content {
  padding: 0 !important;
}

.user-info-block {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* === CONSOLIDATED STYLES FOR COLLAPSED TRANSACTION INFO HEADER === */
.collapsed-info-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 4px 8px;
  gap: 8px;
  box-sizing: border-box;
}

.transaction-segment {
  display: flex;
  align-items: center;
}

.transaction-segment.user-left-section {
  flex: 1;
  justify-content: flex-start;
  min-width: 0;
  gap: 5px;
}

.transaction-segment.user-right-section {
  flex: 1;
  justify-content: flex-end;
  min-width: 0;
  gap: 5px;
}

.transaction-segment.rate-center-section {
  flex-shrink: 0;
  padding: 3px 10px;
  background-color: var(--n-action-color, rgba(255, 255, 255, 0.1));
  border: 1px solid var(--n-border-color);
  border-radius: var(--n-border-radius);
  text-align: center;
}

[data-theme="dark"] .transaction-segment.rate-center-section {
  background-color: var(--n-action-color, rgba(0, 0, 0, 0.25));
  border-color: rgba(255, 255, 255, 0.2);
}

.rate-text-display {
  font-size: 0.88em;
  font-weight: 500;
  color: var(--n-text-color-base);
  white-space: nowrap;
}

.user-name-display {
  font-size: 0.9em;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70px;
  color: var(--n-text-color-base);
}

.amount-tag.n-tag {
  font-size: 0.85em !important;
  padding: 2px 5px !important;
  height: auto !important;
  line-height: 1.3 !important;
  border-radius: 4px !important;
}

.arrow-icon.n-icon {
  font-size: 15px !important;
  color: var(--n-icon-color);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .collapsed-info-compact {
    padding: 2px 4px;
    gap: 5px;
  }
  
  .user-info-block {
    gap: 4px;
  }
  
  .user-name-display {
    font-size: 0.9em;
    max-width: 50px;
  }
  
  .amount-tag.n-tag {
    font-size: 0.85em !important;
    padding: 1px 3px !important;
  }
  
  .rate-text-display {
    font-size: 0.82em;
  }
  
  .transaction-segment.rate-center-section {
    padding: 2px 6px;
  }
    .arrow-icon.n-icon {
    font-size: 14px !important;
  }
}

@media (max-width: 480px) {
  .user-name-display {
    max-width: 45px;
  }
  
  .amount-tag.n-tag {
    font-size: 0.8em !important;
  }
  
  .rate-text-display {
    font-size: 0.8em;
  }
  
  .collapsed-info-compact {
    gap: 4px;
  }

  .user-name-display {
    max-width: 35px; 
  }
  .collapsed-info-compact {
    gap: 4px;
  }
  .transaction-segment.user-left-section,
  .transaction-segment.user-right-section,
  .user-info-block {
    gap: 3px;
  }
}

/* ...existing code... */
</style>
