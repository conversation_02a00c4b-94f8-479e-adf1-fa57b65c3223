<template>
  <section class="stats-section" :class="{ 'enhanced': true }">
    <!-- Enhanced loading skeleton -->
    <div v-if="loading || !contentLoaded" class="stats-skeleton">
      <div class="skeleton-card">
        <div class="stat-skeleton" v-for="n in 3" :key="n" :style="{ animationDelay: `${n * 0.1}s` }">
          <div class="skeleton-icon"></div>
          <div class="skeleton-value"></div>
          <div class="skeleton-label"></div>
        </div>
      </div>
    </div>

    <!-- Enhanced stats with animations -->
    <div v-else class="stats-enhanced" :class="{ 'loaded': contentLoaded }">
      <n-card class="stats-card enhanced-card">
        <n-grid :cols="3" :x-gap="24" responsive="screen" class="stats-grid">
          <n-grid-item>
            <div 
              class="stat-item enhanced-stat animate-fade-up"
              :style="{ animationDelay: '0.1s' }"
              @click="handleStatClick('activeOffers')"
              @touchstart="handleStatPress"
              @mousedown="handleStatPress"
            >
              <div class="stat-icon-container">
                <n-icon size="32" color="#18a058" class="stat-icon">
                  <ShopOutlined />
                </n-icon>
              </div>
              <n-statistic 
                :label="$t('homeView.activeOffersLabel')"
                class="enhanced-statistic"
              >
                <n-number-animation 
                  :from="prevActiveOffers" 
                  :to="stats.activeOffers" 
                  :duration="1200"
                  :precision="0"
                />
              </n-statistic>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div 
              class="stat-item enhanced-stat animate-fade-up"
              :style="{ animationDelay: '0.2s' }"
              @click="handleStatClick('myOffers')"
              @touchstart="handleStatPress"
              @mousedown="handleStatPress"
            >
              <div class="stat-icon-container">
                <n-icon size="32" color="#2080f0" class="stat-icon">
                  <UserOutlined />
                </n-icon>
              </div>
              <n-statistic 
                :label="$t('homeView.myOffers')"
                class="enhanced-statistic"
              >
                <n-number-animation 
                  :from="prevMyOffers" 
                  :to="stats.myOffers" 
                  :duration="1200"
                  :precision="0"
                />
              </n-statistic>
            </div>
          </n-grid-item>
          <n-grid-item>
            <div 
              class="stat-item enhanced-stat animate-fade-up"
              :style="{ animationDelay: '0.3s' }"
              @click="handleStatClick('reputation')"
              @touchstart="handleStatPress"
              @mousedown="handleStatPress"
            >
              <div class="stat-icon-container">
                <n-icon size="32" color="#f0a020" class="stat-icon">
                  <StarOutlined />
                </n-icon>
              </div>
              <n-statistic 
                :label="$t('homeView.reputationLevel')"
                class="enhanced-statistic"
              >
                <n-number-animation 
                  :from="prevReputation" 
                  :to="userReputation" 
                  :duration="1200"
                  :precision="0"
                />
              </n-statistic>
            </div>
          </n-grid-item>
        </n-grid>
      </n-card>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { NCard, NGrid, NGridItem, NIcon, NStatistic, NNumberAnimation } from 'naive-ui'
import { ShopOutlined, UserOutlined, StarOutlined } from '@vicons/antd'

// Props
interface StatsData {
  activeOffers: number
  myOffers: number
}

const props = defineProps<{
  stats: StatsData
  userReputation: number
  loading?: boolean
}>()

// Enhanced loading and animation state
const contentLoaded = ref(false)

// Previous values for smooth transitions
const prevActiveOffers = ref(0)
const prevMyOffers = ref(0)
const prevReputation = ref(0)

// Simulate realistic loading time for skeleton
onMounted(() => {
  // Shorter delay for perceived performance, longer than HeroSection for sequencing
  setTimeout(() => {
    contentLoaded.value = true
  }, 600)
})

// Enhanced interactions with haptic-like feedback
function handleStatPress(event: Event) {
  const statElement = event.currentTarget as HTMLElement
  statElement.style.transform = 'scale(0.98)'
  
  // Reset after short delay (haptic-like feedback)
  setTimeout(() => {
    statElement.style.transform = ''
  }, 150)
}

// Handle stat click for potential navigation or actions
function handleStatClick(statType: string) {
  console.log(`📊 Stat clicked: ${statType}`)
  // Future: Navigate to detailed view or show modal
  // Example: router.push({ name: 'StatsDetail', params: { type: statType } })
}

// Watch for changes to update previous values with improved transitions
watch(() => props.stats.activeOffers, (newVal, oldVal) => {
  if (oldVal !== undefined && oldVal !== newVal) {
    prevActiveOffers.value = oldVal
  }
}, { immediate: true })

watch(() => props.stats.myOffers, (newVal, oldVal) => {
  if (oldVal !== undefined && oldVal !== newVal) {
    prevMyOffers.value = oldVal
  }
}, { immediate: true })

watch(() => props.userReputation, (newVal, oldVal) => {
  if (oldVal !== undefined && oldVal !== newVal) {
    prevReputation.value = oldVal
  }
}, { immediate: true })
</script>

<style scoped>
/* Enhanced Stats Section - Mobile-First with Animations */
.stats-section {
  margin: -30px 16px 40px;
  position: relative;
  z-index: 2;
}

/* Enhanced loading skeleton */
.stats-skeleton {
  margin: -30px 16px 40px;
  position: relative;
  z-index: 2;
}

.skeleton-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.stat-skeleton {
  text-align: center;
  padding: 20px 16px;
  opacity: 0;
  animation: skeletonFadeIn 0.6s ease-out forwards;
}

.skeleton-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  margin: 0 auto 12px;
  animation: shimmer 1.5s infinite;
}

.skeleton-value {
  height: 28px;
  width: 60px;
  background: rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  margin: 0 auto 8px;
  animation: shimmer 1.5s infinite 0.1s;
}

.skeleton-label {
  height: 16px;
  width: 80px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  margin: 0 auto;
  animation: shimmer 1.5s infinite 0.2s;
}

/* Enhanced stats container */
.stats-enhanced {
  opacity: 0;
}

.stats-enhanced.loaded {
  opacity: 1;
  transition: opacity 0.5s ease;
}

.enhanced-card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.enhanced-card:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Enhanced stat items */
.enhanced-stat {
  text-align: center;
  padding: 24px 16px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border-radius: 12px;
  margin: 4px;
  opacity: 0;
  position: relative;
  overflow: hidden;
}

.enhanced-stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.enhanced-stat:hover::before,
.enhanced-stat:focus::before {
  left: 100%;
}

.enhanced-stat:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-1px);
}

.enhanced-stat:active {
  transform: scale(0.98) translateY(-1px);
}

.stat-icon-container {
  margin-bottom: 12px;
  transition: transform 0.2s ease;
}

.enhanced-stat:hover .stat-icon-container {
  transform: scale(1.1);
}

.stat-icon {
  transition: all 0.2s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.enhanced-statistic {
  transition: all 0.2s ease;
}

/* Enhanced number animations */
.enhanced-statistic :deep(.n-statistic__value) {
  font-size: 1.75rem;
  font-weight: 700;
  transition: all 0.2s ease;
}

.enhanced-stat:hover .enhanced-statistic :deep(.n-statistic__value) {
  transform: scale(1.05);
}

.enhanced-statistic :deep(.n-statistic__label) {
  margin-bottom: 4px;
  font-size: 0.9em;
  opacity: 0.8;
  font-weight: 500;
}

/* Entrance animations */
.animate-fade-up {
  animation: fadeUpStats 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Theme support */
[data-theme="light"] .enhanced-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

[data-theme="light"] .skeleton-card {
  background: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .enhanced-card {
  background: rgba(26, 27, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .skeleton-card {
  background: rgba(26, 27, 46, 0.9);
}

[data-theme="dark"] .enhanced-stat:hover {
  background: rgba(255, 255, 255, 0.08);
}

/* Keyframe animations */
@keyframes fadeUpStats {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes skeletonFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.5;
  }
}

/* Tablet enhancements */
@media (min-width: 768px) {
  .stats-section,
  .stats-skeleton {
    margin: -30px 24px 40px;
  }
  
  .skeleton-card {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }
  
  .enhanced-stat {
    padding: 28px 20px;
  }
  
  .enhanced-statistic :deep(.n-statistic__value) {
    font-size: 2rem;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  .enhanced-stat {
    padding: 32px 24px;
  }
  
  .enhanced-statistic :deep(.n-statistic__value) {
    font-size: 2.25rem;
  }
}

/* Mobile optimizations */
@media (max-width: 480px) {
  .stats-section,
  .stats-skeleton {
    margin: -20px 12px 32px;
  }
  
  .skeleton-card {
    padding: 16px;
    gap: 16px;
  }
  
  .enhanced-stat {
    padding: 20px 12px;
    margin: 2px;
  }
  
  .stat-icon-container .stat-icon {
    font-size: 28px !important;
  }
  
  .enhanced-statistic :deep(.n-statistic__value) {
    font-size: 1.5rem;
  }
  
  .enhanced-statistic :deep(.n-statistic__label) {
    font-size: 0.85em;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-up,
  .skeleton-card,
  .shimmer {
    animation: none;
  }
  
  .animate-fade-up {
    opacity: 1;
    transform: none;
  }
  
  .enhanced-stat,
  .enhanced-card {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .enhanced-card {
    border: 2px solid;
    background: canvas;
    color: canvastext;
  }
  
  .enhanced-stat:hover {
    background: highlight;
    color: highlighttext;
  }
}
</style>
