{"understandingTieredPricing": "Understanding Tiered Pricing", "baseRateExplanation": "Your offer sets a <b>base rate</b> for users with similar reputation ({tier}).", "adjustmentExplanation": "You can adjust this rate for other reputation tiers by entering a <b>positive percentage</b> (e.g., 1.25 for 1.25%). The system automatically applies this adjustment.", "lowerTierWorseRates": "Less trusted users ALWAYS get <span class=\"text-worse\">worse rates</span>.", "higherTierBetterRates": "More trusted users ALWAYS get <span class=\"text-better\">better rates</span>.", "offerBasics": "Offer Basics", "offerType": "Offer Type", "sellCAD": "Sell CAD", "buyCAD": "Buy CAD", "currencyPair": "<PERSON><PERSON><PERSON><PERSON>", "amountCAD": "Amount (CAD)", "baseExchangeRate": "Base Exchange Rate (IRR/CAD)", "baseRatePlaceholder": "Base rate for {tier} users, e.g., 150000", "baseRateFeedback": "This is the standard rate for users with similar reputation ({tier}). Adjustments are relative to this base rate.", "tieredAdjustments": "Tiered Adjustments", "forLowerReputationUsers": "For Lower Reputation Users ({tier})", "forHigherReputationUsers": "For Higher Reputation Users ({tier})", "definePenaltyPercentage": "Define Penalty Percentage:", "defineBonusPercentage": "Define Bonus Percentage:", "percentagePlaceholder": "e.g., 1.25", "calculatedImpact": "Calculated Impact", "yourBaseRate": "Your Base Rate ({tier}): <span class=\"rate-value\">{rate} IRR/CAD</span>", "adjustedRate": "{tier} Adjusted Rate: <span class=\"rate-value\">{rate} IRR/CAD</span>", "higherRate": "Higher Rate", "lowerRate": "Lower Rate", "noAdjustment": "No Adjustment", "penaltyPayMore": "{adjustment}% Penalty: They Pay More", "penaltyReceiveLess": "{adjustment}% Penalty: They Receive Less", "bonusPayLess": "{adjustment}% Bonus: They Pay Less", "bonusReceiveMore": "{adjustment}% Bonus: They Receive More", "selectOfferTypeAndRate": "Select offer type and enter base rate to see calculations.", "selectOfferTypeForDetails": "Select an offer type to see specific details.", "createOffer": "Create Offer", "updateOffer": "Update Offer", "tierLabel": "Tier {tier}", "lowerTiersDefault": "Lower Tiers (1-2)", "higherTiersDefault": "Higher Tiers (4-5)", "noLowerTiers": "No Lower Tiers", "noHigherTiers": "No Higher Tiers", "tierSingle": "Tier {tier}", "tiersRange": "Tiers {start}-{end}", "lowerTiers": "Lower Tiers", "higherTiers": "Higher Tiers", "lowerTierUsers": "lower tier users", "higherTierUsers": "higher tier users", "they": "they", "noOne": "no one", "lowerTierSellExplanation": "If you are <b>SELL</b>ing CAD, {entity} pay <span class=\"text-worse\">more</span> IRR for each CAD.", "lowerTierBuyExplanation": "If you are <b>BUY</b>ing CAD, {entity} receive <span class=\"text-worse\">less</span> IRR for each CAD.", "higherTierSellExplanation": "If you are <b>SELL</b>ing CAD, {entity} pay <span class=\"text-better\">less</span> IRR for each CAD.", "higherTierBuyExplanation": "If you are <b>BUY</b>ing CAD, {entity} receive <span class=\"text-better\">more</span> IRR for each CAD.", "helperText": {"lowerRep": "Enter a positive percentage (≥0%). This makes the rate <span class=\"worse\">worse</span> for {tiersText}. {detail}", "higherRep": "Enter a positive percentage (≥0%). This makes the rate <span class=\"better\">better</span> for {tiersText}. {detail}"}, "helperDetails": {"lowerRepSell": "(They will pay {adjustment}% <span class=\"worse\">more</span> IRR per CAD).", "lowerRepBuy": "(They will receive {adjustment}% <span class=\"worse\">less</span> IRR per CAD).", "higherRepSell": "(They will pay {adjustment}% <span class=\"better\">less</span> IRR per CAD).", "higherRepBuy": "(They will receive {adjustment}% <span class=\"better\">more</span> IRR per CAD)."}, "validation": {"offerTypeRequired": "Offer type is required", "amountRequired": "Amount is required", "amountPositive": "Amount must be positive", "baseRateRequired": "Base rate is required", "baseRatePositive": "Base rate must be positive", "adjustmentNonNegative": "Adjustment must be non-negative", "adjustmentMaxHundred": "Adjustment cannot exceed 100%", "currencyPairRequired": "Currency pair selection is required", "invalidAmount": "Invalid amount", "invalidBaseRate": "Invalid base rate"}}