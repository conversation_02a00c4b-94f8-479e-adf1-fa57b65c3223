import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { 
  ParsedReport, 
  FilterOptions, 
  SortOptions
} from '../types/admin';
import { getDatabaseStatusFromApi } from '../types/admin';
import * as adminDebugReportService from '../services/adminDebugReportService';

export const useAdminDebugStore = defineStore('adminDebug', () => {
  // State
  const reports = ref<ParsedReport[]>([]);
  const total = ref(0);
  const totalPages = ref(0);
  const currentPage = ref(1);
  const limit = ref(10);
  const loading = ref(false);
  const error = ref<string | null>(null);
  
  // Selected report for details view
  const selectedReport = ref<ParsedReport | null>(null);
  const selectedReportLoading = ref(false);
    // Filters and sorting
  const filters = ref<FilterOptions>({
    type: undefined,
    severity: undefined,
    status: undefined, // Add status filter
    dateStart: undefined,
    dateEnd: undefined,
    searchQuery: undefined,
  });
  
  const sort = ref<SortOptions>({
    sortBy: 'serverReceivedAt',
    sortOrder: 'desc',
  });
  // Computed
  const hasReports = computed(() => reports.value.length > 0);
  const isEmpty = computed(() => !loading.value && !hasReports.value && !error.value);
  const hasFilters = computed(() => {
    return Object.values(filters.value).some(value => value !== undefined && value !== null && value !== '');
  });
  const totalReports = computed(() => total.value);

  // Actions
  
  /**
   * Fetch reports with current filters, sorting, and pagination
   */
  async function fetchReports(resetPage = false) {
    if (resetPage) {
      currentPage.value = 1;
    }
    
    loading.value = true;
    error.value = null;
    
    try {      const params = {
        page: currentPage.value,
        limit: limit.value,
        sortBy: sort.value.sortBy,
        sortOrder: sort.value.sortOrder,        // Map filter names to match API expectations
        filterByType: filters.value.type,
        filterBySeverity: filters.value.severity,
        filterByStatus: filters.value.status, // Add status filter
        filterByDateStart: filters.value.dateStart,
        filterByDateEnd: filters.value.dateEnd,
        searchQuery: filters.value.searchQuery,
      };
        // Remove undefined/null/empty values
      Object.keys(params).forEach(key => {
        const value = (params as any)[key];
        if (value === undefined || value === null || value === '') {
          delete (params as any)[key];
        }
      });
      
      // Debug log for filter parameters
      console.log('[AdminDebugStore] Fetching reports with params:', params);
      
      const response = await adminDebugReportService.fetchReports(params);
      
      reports.value = response.reports;
      total.value = response.total;
      totalPages.value = response.totalPages;
      currentPage.value = response.currentPage;
      
    } catch (err) {
      console.error('Failed to fetch reports:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch reports';
      reports.value = [];
      total.value = 0;
      totalPages.value = 0;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch a specific report by ID
   */
  async function fetchReportById(reportId: string) {
    selectedReportLoading.value = true;
    error.value = null;
    
    try {
      const report = await adminDebugReportService.fetchReportById(reportId);
      selectedReport.value = report;
      return report;
    } catch (err) {
      console.error('Failed to fetch report:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch report';
      selectedReport.value = null;
      return null;
    } finally {
      selectedReportLoading.value = false;
    }
  }

  /**
   * Update filters and refetch reports
   */
  async function updateFilters(newFilters: Partial<FilterOptions>) {
    filters.value = { ...filters.value, ...newFilters };
    await fetchReports(true); // Reset to page 1 when filtering
  }

  /**
   * Update sorting and refetch reports
   */
  async function updateSort(newSort: Partial<SortOptions>) {
    sort.value = { ...sort.value, ...newSort };
    await fetchReports(true); // Reset to page 1 when sorting changes
  }

  /**
   * Clear all filters
   */
  async function clearFilters() {
    filters.value = {
      type: undefined,
      severity: undefined,
      dateStart: undefined,
      dateEnd: undefined,
      searchQuery: undefined,
    };
    await fetchReports(true);
  }

  /**
   * Change page
   */
  async function changePage(page: number) {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
      await fetchReports();
    }
  }

  /**
   * Change page size
   */
  async function changePageSize(newLimit: number) {
    limit.value = newLimit;
    await fetchReports(true); // Reset to page 1 when page size changes
  }

  /**
   * Download a report as JSON
   */
  async function downloadReport(reportId: string) {
    try {
      await adminDebugReportService.downloadReport(reportId);
    } catch (err) {
      console.error('Failed to download report:', err);
      error.value = err instanceof Error ? err.message : 'Failed to download report';
      throw err;
    }
  }

  /**
   * Clear selected report
   */
  function clearSelectedReport() {
    selectedReport.value = null;
  }

  /**
   * Refresh current view
   */
  async function refresh() {
    await fetchReports();
  }

  /**
   * Set current page
   */
  function setCurrentPage(page: number) {
    currentPage.value = page;
  }

  /**
   * Set page limit
   */
  function setLimit(newLimit: number) {
    limit.value = newLimit;
  }
  /**
   * Export a report (alias for downloadReport)
   */
  async function exportReport(reportId: string) {
    return await downloadReport(reportId);
  }  /**
   * Update report status
   */
  async function updateReportStatus(reportId: string, status: string, comment?: string) {
    console.log('[AdminDebugStore] updateReportStatus called:', { reportId, status, comment });
    try {
      await adminDebugReportService.updateReportStatus(reportId, status, comment);
      console.log('[AdminDebugStore] API call successful, updating local state');
      
      // Convert API status format back to database format for local state
      const dbStatus = getDatabaseStatusFromApi(status);
      console.log('[AdminDebugStore] Converted status:', { apiStatus: status, dbStatus });
      
      // Update the report in the current list if it exists
      const reportIndex = reports.value.findIndex(r => r.reportId === reportId);
      if (reportIndex !== -1) {
        console.log('[AdminDebugStore] Updating report in list at index:', reportIndex);
        reports.value[reportIndex].status = dbStatus;
      } else {
        console.log('[AdminDebugStore] Report not found in current list');
      }
      
      // Update selected report if it's the same one
      if (selectedReport.value?.reportId === reportId) {
        console.log('[AdminDebugStore] Updating selected report');
        selectedReport.value.status = dbStatus;
      }
      
    } catch (err) {
      console.error('[AdminDebugStore] Failed to update report status:', err);
      error.value = err instanceof Error ? err.message : 'Failed to update report status';
      throw err;
    }
  }

  return {
    // State
    reports,
    total,
    totalPages,
    currentPage,
    limit,
    loading,
    error,
    selectedReport,
    selectedReportLoading,
    filters,
    sort,
      // Computed
    hasReports,
    isEmpty,
    hasFilters,
    totalReports,
      // Actions
    fetchReports,
    fetchReportById,
    updateReportStatus,
    updateFilters,
    updateSort,
    clearFilters,
    changePage,
    changePageSize,
    downloadReport,
    exportReport, // Add alias
    clearSelectedReport,
    refresh,
    setCurrentPage, // Add missing method
    setLimit, // Add missing method
  };
});
