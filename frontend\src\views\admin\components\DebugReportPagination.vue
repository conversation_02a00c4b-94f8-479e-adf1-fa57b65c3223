<template>
  <nav class="pagination-controls" v-if="validatedProps.totalPages > 1">
    <div class="pagination-info">
      <span>Page {{ validatedProps.currentPage }} of {{ validatedProps.totalPages }}</span>
      <select :value="validatedProps.pageSize" @change="changeSize($event)" class="page-size-select">
        <option value="10">10/page</option>
        <option value="20">20/page</option>
        <option value="50">50/page</option>
        <option value="100">100/page</option>
      </select>
    </div>
    <div class="page-buttons">
      <button @click="goTo(1)" :disabled="validatedProps.currentPage === 1" class="btn">First</button>
      <button @click="goTo(validatedProps.currentPage - 1)" :disabled="validatedProps.currentPage === 1" class="btn">Prev</button>
      <template v-for="page in visiblePageNumbers" :key="page">
        <button
          v-if="typeof page === 'number'"
          @click="goTo(page)"
          :class="{ active: page === validatedProps.currentPage }"
          class="btn page-number"
        >
          {{ page }}
        </button>
        <span v-else class="ellipsis">...</span>
      </template>
      <button @click="goTo(validatedProps.currentPage + 1)" :disabled="validatedProps.currentPage === validatedProps.totalPages" class="btn">Next</button>
      <button @click="goTo(validatedProps.totalPages)" :disabled="validatedProps.currentPage === validatedProps.totalPages" class="btn">Last</button>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue';

defineOptions({
  name: 'DebugReportPagination'
});

const PAGE_SIZE_OPTIONS = [10, 20, 50, 100] as const;

const props = defineProps<{
  currentPage: number;
  totalPages: number;
  pageSize: number;
}>();

// Validated props with safe defaults and constraints
const validatedProps = computed(() => {
  // Ensure totalPages is a positive integer, minimum 1
  const totalPages = Math.max(1, Math.floor(Math.abs(props.totalPages || 1)));
  
  // Ensure currentPage is a positive integer within valid range
  const currentPage = Math.max(1, Math.min(totalPages, Math.floor(Math.abs(props.currentPage || 1))));
  
  // Ensure pageSize is one of the allowed options
  const pageSize = PAGE_SIZE_OPTIONS.includes(props.pageSize as any) ? props.pageSize : PAGE_SIZE_OPTIONS[0];
  
  return {
    totalPages,
    currentPage,
    pageSize
  };
});

const emit = defineEmits<{
  'change-page': [page: number];
  'change-page-size': [size: number];
}>();

function goTo(page: number) {
  const { totalPages, currentPage } = validatedProps.value;
  if (page >= 1 && page <= totalPages && page !== currentPage) {
    emit('change-page', page);
  }
}

function changeSize(event: Event) {
  const target = event.target as HTMLSelectElement;
  const parsedValue = parseInt(target.value, 10);

  // Validate that the parsed value is a valid page size option
  if (!isNaN(parsedValue) && PAGE_SIZE_OPTIONS.includes(parsedValue as any)) {
    emit('change-page-size', parsedValue);
  } else {
    // Log a warning and reset to default if invalid
    console.warn(`[DebugReportPagination] Invalid page size selected: ${target.value}. Resetting to default (${PAGE_SIZE_OPTIONS[0]}).`);
    emit('change-page-size', PAGE_SIZE_OPTIONS[0]);
  }
}

const visiblePageNumbers = computed(() => {
  const { totalPages, currentPage } = validatedProps.value;
  const pages: (number | string)[] = [];
  const maxVisible = 5;
  
  if (totalPages <= maxVisible + 2) {
    for (let i = 1; i <= totalPages; i++) pages.push(i);
    return pages;
  }

  pages.push(1);

  let start = Math.max(2, currentPage - Math.floor((maxVisible - 2) / 2));
  let end = Math.min(totalPages - 1, currentPage + Math.ceil((maxVisible - 2) / 2));
  
  if (currentPage < maxVisible - 1) {
    end = maxVisible - 1;
  }
  if (currentPage > totalPages - (maxVisible - 2)) {
    start = totalPages - (maxVisible - 2);
  }

  if (start > 2) pages.push('...');

  for (let i = start; i <= end; i++) {
    if (i > 1 && i < totalPages) {
      pages.push(i);
    }
  }

  if (end < totalPages - 1) pages.push('...');
  
  pages.push(totalPages);
  return pages;
});
</script>

<style scoped>
.pagination-controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-base, #e0e0e0);
  font-size: var(--font-size-sm, 0.875rem);
  gap: 1rem;
}

@media (max-width: 768px) {
  .pagination-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.page-size-select {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm, 4px);
  border: 1px solid var(--border-base, #e0e0e0);
  background-color: var(--bg-surface, #fff);
  font-size: var(--font-size-sm, 0.875rem);
}

.page-buttons {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  flex-wrap: wrap;
  justify-content: center;
}

@media (max-width: 768px) {
  .page-buttons {
    justify-content: center;
  }
}

.btn {
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--border-base, #e0e0e0);
  background-color: var(--bg-surface, #fff);
  color: var(--text-primary, #333);
  border-radius: var(--radius-md, 6px);
  cursor: pointer;
  font-size: var(--font-size-sm, 0.875rem);
  transition: all 0.2s ease-in-out;
}

.btn:hover:not(:disabled) {
  background-color: var(--bg-surface-hover, #f8f9fa);
  border-color: var(--primary-300, #93c5fd);
}

.btn:focus {
  outline: 2px solid var(--primary-500, #3b82f6);
  outline-offset: 2px;
}

.btn:disabled {
  color: var(--text-quaternary, #94a3b8);
  cursor: not-allowed;
  background-color: var(--bg-surface-hover, #f8f9fa);
}

.btn.active {
  background-color: var(--primary-500, #3b82f6);
  color: white;
  border-color: var(--primary-500, #3b82f6);
  font-weight: var(--font-weight-bold, 700);
}

.page-number {
  min-width: 36px;
  justify-content: center;
}

.ellipsis {
  padding: 0.375rem 0.5rem;
  color: var(--text-tertiary, #6c757d);
}

/* ===== DARK MODE SUPPORT ===== */

[data-theme="dark"] .pagination-controls {
  border-top-color: var(--border-base-dark, #374151);
}

[data-theme="dark"] .pagination-info span {
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .page-size-select {
  background-color: var(--bg-input-dark, #374151);
  border-color: var(--border-base-dark, #4b5563);
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .page-size-select:focus {
  border-color: var(--primary-500, #3b82f6);
  box-shadow: 0 0 0 3px var(--primary-900, #1e3a8a);
}

[data-theme="dark"] .btn {
  background-color: var(--bg-surface-dark, #1f2937);
  border-color: var(--border-base-dark, #374151);
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .btn:hover:not(:disabled) {
  background-color: var(--bg-surface-hover-dark, #374151);
  border-color: var(--primary-400, #60a5fa);
}

[data-theme="dark"] .btn:focus {
  outline-color: var(--primary-400, #60a5fa);
}

[data-theme="dark"] .btn:disabled {
  color: var(--text-quaternary-dark, #6b7280);
  background-color: var(--bg-surface-disabled-dark, #111827);
  border-color: var(--border-base-dark, #374151);
}

[data-theme="dark"] .btn.active {
  background-color: var(--primary-600, #2563eb);
  color: white;
  border-color: var(--primary-600, #2563eb);
}

[data-theme="dark"] .ellipsis {
  color: var(--text-tertiary-dark, #9ca3af);
}
</style>