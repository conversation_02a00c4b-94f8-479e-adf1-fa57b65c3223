import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    // Fetch all users with detailed information
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        emailVerified: true,
        phoneVerified: true,
        createdAt: true, 
        updatedAt: true,
        username: true,
        reputationScore: true,
        reputationLevel: true
      }
    });
    
    console.log(`Total users found: ${allUsers.length}`);
    
    // Print detailed information about each user
    allUsers.forEach((user, index) => {
      console.log(`\nUser #${index + 1}:`);
      console.log(`ID: ${user.id}`);
      console.log(`Email: ${user.email}`);
      console.log(`Username: ${user.username || '(not set)'}`);
      console.log(`Created At: ${user.createdAt ? user.createdAt.toISOString() : 'NULL'}`);
      console.log(`Updated At: ${user.updatedAt ? user.updatedAt.toISOString() : 'NULL'}`);
      console.log(`Email Verified: ${user.emailVerified}`);
      console.log(`Phone Verified: ${user.phoneVerified}`);
      console.log(`Reputation Score: ${user.reputationScore}`);
      console.log(`Reputation Level: ${user.reputationLevel}`);
      console.log('-'.repeat(50));
    });
    
  } catch (error) {
    console.error('Error fetching users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('Database inspection completed.'))
  .catch(e => {
    console.error('Script failed:', e);
    process.exit(1);
  });
