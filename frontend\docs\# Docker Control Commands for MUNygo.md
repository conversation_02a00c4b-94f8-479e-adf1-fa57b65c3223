# Docker Control Commands for MUNygo

Here are the commands to stop and start the MUNygo application in Docker:

## 🛑 Stop the Application

To stop all containers:
```powershell
# Navigate to your project root directory
cd /path/to/your/munygo/project
docker-compose down
```

To stop and remove all containers, networks, and volumes:
```powershell
# Navigate to your project root directory
cd /path/to/your/munygo/project
docker-compose down -v
```

## 🚀 Start the Application

To start all services:
```powershell
# Navigate to your project root directory
cd /path/to/your/munygo/project
docker-compose up -d
```

## 🔄 Restart Specific Services

To restart just the backend:
```powershell
docker-compose restart backend
```

To restart just the frontend:
```powershell
docker-compose restart frontend
```

## 📋 Check Status

To see running containers and their status:
```powershell
docker-compose ps
```

To view logs:
```powershell
# All logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f

# Specific service logs
docker-compose logs backend
docker-compose logs frontend
```

## 🔨 Rebuild and Start

If you've made changes to the code and need to rebuild:
```powershell
cd C:\Code\MUNygo
docker-compose down
docker-compose build
docker-compose up -d
```

## 🧹 Clean Up

To remove all unused containers, networks, and volumes:
```powershell
docker system prune -f
```

After starting the application, you can access:
- Frontend: http://localhost
- Backend API: http://localhost:3000
- Backend Health Check: http://localhost:3000/health