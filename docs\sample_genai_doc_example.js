// To run this code you need to install the following dependencies:
// npm install @google/genai mime
// npm install -D @types/node

import {
  GoogleGenAI,
  Type,
} from '@google/genai';

async function main() {
  const ai = new GoogleGenAI({
    apiKey: process.env.GEMINI_API_KEY,
  });
  const config = {
    temperature: 0.1,
    responseMimeType: 'application/json',
    responseSchema: {
      type: Type.OBJECT,
      properties: {
        title: {
          type: Type.STRING,
        },
        description: {
          type: Type.STRING,
        },
        steps_to_reproduce: {
          type: Type.STRING,
        },
        severity: {
          type: Type.STRING,
          enum: ["low", "medium", "high"],
        },
        tag: {
          type: Type.ARRAY,
          items: {
            type: Type.STRING,
            enum: ["bug", "improvement", "idea"],
          },
        },
      },
    },
  };
  const model = 'gemini-2.5-flash-preview-05-20';
  const contents = [
    {
      role: 'user',
      parts: [
        {
          inlineData: {
            data: `GkXfo59ChoEBQ...=`,
            mimeType: `audio/ogg`,
          },
        },
        {
          text: `this is recording out of which you should extract a detailed structure json debug object t o report to the devs`,
        },
      ],
    },
    {
      role: 'model',
      parts: [
        {
          text: `**Analyzing the Audio**

...
}`,
        },
      ],
    },
    {
      role: 'user',
      parts: [
        {
          text: `INSERT_INPUT_HERE`,
        },
      ],
    },
  ];

  const response = await ai.models.generateContentStream({
    model,
    config,
    contents,
  });
  let fileIndex = 0;
  for await (const chunk of response) {
    console.log(chunk.text);
  }
}

main();
