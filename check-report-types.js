const { PrismaClient } = require('./backend/node_modules/@prisma/client');

async function checkReportTypes() {
  const prisma = new PrismaClient();
  
  try {
    const associations = await prisma.tagReportTypeAssociation.findMany({
      select: { reportType: true },
      distinct: ['reportType']
    });
    
    console.log('Report types in database:', associations.map(a => a.reportType));
    
    // Also check what the enum values should be
    const allAssociations = await prisma.tagReportTypeAssociation.findMany({
      include: { tag: true }
    });
    
    console.log('\nAll tag-report-type associations:');
    allAssociations.forEach(assoc => {
      console.log(`- ${assoc.tag.name}: ${assoc.reportType}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkReportTypes();
