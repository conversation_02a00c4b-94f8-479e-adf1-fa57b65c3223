// Utils for dynamic status display across offer cards and tables
import type { InterestStatus } from '@/types/offer';

export interface StatusInfo {
  text: string;
  type: 'success' | 'warning' | 'error' | 'info' | 'default';
  icon?: string;
}

type TranslationFunction = (key: string) => string;

export function getInterestDynamicStatus(
  interestStatus: InterestStatus | null | undefined,
  transactionStatus?: string | null,
  negotiationStatus?: string | null,
  t?: TranslationFunction
): StatusInfo | null {  // If no interest status, no tag
  if (!interestStatus) {
    return null;
  }  // Interest shown but not yet accepted/declined
  if (interestStatus === 'PENDING') {
    return { text: t ? t('interests.statuses.pending') : 'Pending', type: 'info', icon: '⏳' };
  }

  // Interest declined
  if (interestStatus === 'DECLINED') {
    return { text: t ? t('interests.statuses.declined') : 'Declined', type: 'error' };
  }

  // Interest cancelled
  if (interestStatus === 'CANCELLED') {
    return { text: t ? t('interests.cancelled') : 'Cancelled', type: 'default' };
  }
  // Interest accepted - now check transaction flow
  if (interestStatus === 'ACCEPTED') {
    if (!transactionStatus) {
      return { text: t ? t('interests.statuses.accepted') : 'Accepted', type: 'success' };
    }

    // Transaction flow stages
    switch (transactionStatus) {
      case 'PENDING_AGREEMENT':
      case 'AWAITING_FIRST_PAYER_DESIGNATION':
        // Check if they're in negotiation phase
        if (negotiationStatus === 'READY_TO_NEGOTIATE' || negotiationStatus === 'PENDING_RESPONSE') {
          return { text: t ? t('interests.negotiating') : 'Negotiating', type: 'warning', icon: '💬' };
        }
        if (negotiationStatus === 'FINALIZED') {
          return { text: t ? t('interests.termsAgreed') : 'Terms Agreed', type: 'success' };
        }
        return { text: t ? t('interests.settingUp') : 'Setting Up', type: 'info' };      
      case 'AWAITING_FIRST_PAYER_PAYMENT':
      case 'AWAITING_FIRST_PAYER_CONFIRMATION':
      case 'AWAITING_FIRST_PAYER_CONFIRMATION_AND_SECOND_PAYER_PAYMENT':
      case 'AWAITING_SECOND_PAYER_PAYMENT':
      case 'AWAITING_SECOND_PAYER_CONFIRMATION':
        return { text: t ? t('interests.inProgress') : 'In Progress', type: 'warning', icon: '🔄' };
      
      case 'COMPLETED':
        return { text: t ? t('interests.complete') : 'Complete', type: 'success', icon: '✅' };
      
      case 'CANCELLED':
        return { text: t ? t('interests.cancelled') : 'Cancelled', type: 'default' };
        case 'DISPUTED':
        return { text: t ? t('interests.disputed') : 'Disputed', type: 'error' };
      
      default:
        return { text: t ? t('interests.statuses.accepted') : 'Accepted', type: 'success' };
    }
  }

  return null;
}

export function getOfferOverallStatus(offer: {
  status: string;
  interests?: Array<{
    status: InterestStatus;
    transactionStatus?: string | null;
    negotiationStatus?: string | null;
  }>;
}, t?: TranslationFunction): StatusInfo | null {
  // If offer is not active, show offer status
  if (offer.status !== 'ACTIVE') {
    const statusText = offer.status.charAt(0) + offer.status.slice(1).toLowerCase();
    return {
      text: t ? (offer.status === 'COMPLETED' ? t('status.complete') : statusText) : statusText,
      type: offer.status === 'COMPLETED' ? 'success' : 'default'
    };
  }

  // Check for pending interests first
  const pendingInterests = offer.interests?.filter(i => i.status === 'PENDING') || [];
  if (pendingInterests.length > 0) {
    return { text: t ? t('status.pending') : 'Pending', type: 'info', icon: '⏳' };
  }

  // Check for any ongoing transactions (accepted interests)
  const activeInterests = offer.interests?.filter(i => i.status === 'ACCEPTED') || [];
  
  if (activeInterests.length === 0) {
    return { text: t ? t('status.active') : 'Active', type: 'success' };
  }

  // Find the most advanced transaction status
  let mostAdvancedStatus: StatusInfo = { text: t ? t('status.active') : 'Active', type: 'success' };
  
  for (const interest of activeInterests) {
    const interestStatus = getInterestDynamicStatus(
      interest.status,
      interest.transactionStatus,
      interest.negotiationStatus,
      t
    );
    
    if (interestStatus) {
      // Prioritize certain statuses
      if (interestStatus.text === (t ? t('status.complete') : 'Complete')) {
        return interestStatus;
      }
      if (interestStatus.text === (t ? t('status.inProgress') : 'In Progress') || 
          interestStatus.text === (t ? t('status.negotiating') : 'Negotiating')) {
        mostAdvancedStatus = interestStatus;
      }
      if (interestStatus.text === (t ? t('status.termsAgreed') : 'Terms Agreed') && 
          mostAdvancedStatus.text === (t ? t('status.active') : 'Active')) {
        mostAdvancedStatus = interestStatus;
      }
    }
  }

  return mostAdvancedStatus;
}
