import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';
import type { TransactionStatusUpdatePayload } from '@/types/socketEvents';
import type { Transaction } from '@/types/transaction';

// We need to mock the store since it's a complex setup
const mockCurrentTransaction = {
  id: 'cmb3yoxpc000zvljku0tznqal',
  status: 'AWAITING_SECOND_PAYER_CONFIRMATION',
  currencyAProvider: {
    id: 'cmav458yl0000vl50932ofx2h',
    username: 'h'
  },
  currencyBProvider: {
    id: 'cmav458yw0001vl50xoubu4w4',
    username: 'h2'
  },
  // ... other transaction fields
  currencyA: 'CAD',
  amountA: 61.34,
  currencyAProviderId: 'cmav458yl0000vl50932ofx2h',
  currencyB: 'IRR',
  amountB: 3262.2157768,
  currencyBProviderId: 'cmav458yw0001vl50xoubu4w4',
  chatSessionId: 'cmb3yoxoz000yvljkw4gkz3z8',
  offerId: 'cmb3yonce000uvljkt19t0eji'
} as Transaction;

const mockSocketPayload: TransactionStatusUpdatePayload = {
  transactionId: 'cmb3yoxpc000zvljku0tznqal',
  chatSessionId: 'cmb3yoxoz000yvljkw4gkz3z8',
  offerId: 'cmb3yonce000uvljkt19t0eji',
  status: 'AWAITING_SECOND_PAYER_PAYMENT',
  currencyA: 'CAD',
  amountA: 61.34,
  currencyAProviderId: 'cmav458yl0000vl50932ofx2h',
  currencyAProviderUsername: 'h',
  currencyB: 'IRR',
  amountB: 3262.2157768,
  currencyBProviderId: 'cmav458yw0001vl50xoubu4w4',
  currencyBProviderUsername: 'h2',
  termsAgreementTimestampPayer1: null,
  termsAgreementTimestampPayer2: null,
  agreedFirstPayerId: 'cmav458yw0001vl50xoubu4w4',
  firstPayerDesignationTimestamp: '2025-05-25T17:58:16.949Z',
  paymentExpectedByPayer1: '2025-05-25T19:58:16.949Z',
  paymentDeclaredAtPayer1: '2025-05-25T17:58:24.042Z',
  paymentTrackingNumberPayer1: '123',
  firstPaymentConfirmedByPayer2At: '2025-05-25T17:59:29.630Z',
  paymentExpectedByPayer2: '2025-05-25T19:59:29.630Z',
  paymentDeclaredAtPayer2: null,
  paymentTrackingNumberPayer2: null,
  secondPaymentConfirmedByPayer1At: null,
  cancellationReason: null,
  cancelledByUserId: null,
  disputeReason: null,
  disputedByUserId: null,
  disputeResolvedAt: null,
  disputeResolutionNotes: null,
  createdAt: '2025-05-25T17:58:04.704Z',
  updatedAt: '2025-05-25T17:59:29.632Z'
};

describe('Transaction Store Socket Update', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it('should preserve provider usernames when updating transaction via socket', () => {
    // Simulate the mapping logic from the transaction store
    const mappedTransaction = {
      ...mockCurrentTransaction,
      id: mockSocketPayload.transactionId,
      status: mockSocketPayload.status,
      // ... other fields that get updated
      firstPaymentConfirmedByPayer2At: mockSocketPayload.firstPaymentConfirmedByPayer2At,
      paymentExpectedByPayer2: mockSocketPayload.paymentExpectedByPayer2,
      updatedAt: mockSocketPayload.updatedAt,
      // The fix: preserve provider objects with usernames from socket payload
      currencyAProvider: {
        id: mockSocketPayload.currencyAProviderId,
        username: mockSocketPayload.currencyAProviderUsername || mockCurrentTransaction.currencyAProvider?.username || 'Unknown User'
      },
      currencyBProvider: {
        id: mockSocketPayload.currencyBProviderId,
        username: mockSocketPayload.currencyBProviderUsername || mockCurrentTransaction.currencyBProvider?.username || 'Unknown User'
      },
    };

    // Verify that usernames are preserved
    expect(mappedTransaction.currencyAProvider.username).toBe('h');
    expect(mappedTransaction.currencyBProvider.username).toBe('h2');
    expect(mappedTransaction.currencyAProvider.id).toBe('cmav458yl0000vl50932ofx2h');
    expect(mappedTransaction.currencyBProvider.id).toBe('cmav458yw0001vl50xoubu4w4');
  });

  it('should fallback to Unknown User if socket payload has no usernames', () => {
    const payloadWithoutUsernames = {
      ...mockSocketPayload,
      currencyAProviderUsername: null,
      currencyBProviderUsername: null
    };

    const mappedTransaction = {
      ...mockCurrentTransaction,
      currencyAProvider: {
        id: payloadWithoutUsernames.currencyAProviderId,
        username: payloadWithoutUsernames.currencyAProviderUsername || mockCurrentTransaction.currencyAProvider?.username || 'Unknown User'
      },
      currencyBProvider: {
        id: payloadWithoutUsernames.currencyBProviderId,
        username: payloadWithoutUsernames.currencyBProviderUsername || mockCurrentTransaction.currencyBProvider?.username || 'Unknown User'
      },
    };

    // Should fall back to existing usernames in current transaction
    expect(mappedTransaction.currencyAProvider.username).toBe('h');
    expect(mappedTransaction.currencyBProvider.username).toBe('h2');
  });

  it('should use Unknown User if both socket and current transaction have no usernames', () => {
    const currentTransactionWithoutUsernames = {
      ...mockCurrentTransaction,
      currencyAProvider: { id: 'cmav458yl0000vl50932ofx2h', username: null },
      currencyBProvider: { id: 'cmav458yw0001vl50xoubu4w4', username: null }
    };

    const payloadWithoutUsernames = {
      ...mockSocketPayload,
      currencyAProviderUsername: null,
      currencyBProviderUsername: null
    };

    const mappedTransaction = {
      ...currentTransactionWithoutUsernames,
      currencyAProvider: {
        id: payloadWithoutUsernames.currencyAProviderId,
        username: payloadWithoutUsernames.currencyAProviderUsername || currentTransactionWithoutUsernames.currencyAProvider?.username || 'Unknown User'
      },
      currencyBProvider: {
        id: payloadWithoutUsernames.currencyBProviderId,
        username: payloadWithoutUsernames.currencyBProviderUsername || currentTransactionWithoutUsernames.currencyBProvider?.username || 'Unknown User'
      },
    };

    expect(mappedTransaction.currencyAProvider.username).toBe('Unknown User');
    expect(mappedTransaction.currencyBProvider.username).toBe('Unknown User');
  });
});
