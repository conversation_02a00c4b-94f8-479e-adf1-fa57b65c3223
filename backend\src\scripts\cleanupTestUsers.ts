import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanup(dryRun = false) {
   try {
    // First, count and show what would be deleted
    const toDelete = await prisma.user.findMany({
      where: {
        email: {
          contains: 'test-user'
        }
      },
      select: {
        id: true,
        email: true,
        username: true
      }
    });
    
    console.log(`Found ${toDelete.length} test users to clean up:`);
    toDelete.forEach(user => {
      console.log(`  - ${user.email} (${user.username || 'no username'})`);
    });
    
    if (dryRun) {
      console.log('Dry run mode - no users were deleted');
      return;
    }
    
    if (toDelete.length === 0) {
      console.log('No test users found');
      return;
    }
    
    // Add confirmation for safety
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const answer = await new Promise<string>((resolve) => {
      readline.question(`Are you sure you want to delete ${toDelete.length} users? (yes/no): `, resolve);
    });
    readline.close();
    
    if (answer.toLowerCase() !== 'yes') {
      console.log('Cleanup cancelled');
      return;
    }
    
    const result = await prisma.user.deleteMany({
      where: {
        email: {
          contains: 'test-user'
        }
      }
    });
    console.log(`Cleaned up ${result.count} test users`);
   } catch (error) {
     console.error('Cleanup failed:', error);
   } finally {
     await prisma.$disconnect();
   }
 }

// Run cleanup if called directly
if (require.main === module) {
  const dryRun = process.argv.includes('--dry-run');
  cleanup(dryRun).catch(console.error);
}
