#!/bin/bash

# PRODUCTION DATABASE MIGRATION SCRIPT - CentOS 9
# ================================================
# This script safely adds debug report tables to existing production database
# WITHOUT losing any existing data

echo "🔒 PRODUCTION DATABASE MIGRATION - SAFE MODE"
echo "=============================================="

# Exit on any error
set -e

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to create timestamped backup using Docker
create_backup() {
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    echo "📦 Creating database backup: $backup_file"
    
    # Extract database connection details from DATABASE_URL
    if [ -z "$DATABASE_URL" ]; then
        echo "❌ DATABASE_URL not found in environment"
        exit 1
    fi
    
    # Parse DATABASE_URL (format: postgresql://user:pass@host:port/dbname)
    DB_URL_REGEX="postgresql://([^:]+):([^@]+)@([^:]+):([^/]+)/(.+)"
    if [[ $DATABASE_URL =~ $DB_URL_REGEX ]]; then
        DB_USER="${BASH_REMATCH[1]}"
        DB_PASS="${BASH_REMATCH[2]}"
        DB_HOST="${BASH_REMATCH[3]}"
        DB_PORT="${BASH_REMATCH[4]}"
        DB_NAME="${BASH_REMATCH[5]}"
    else
        echo "❌ Could not parse DATABASE_URL format"
        exit 1
    fi
    
    # Create backup using Docker container
    echo "🐳 Using Docker container to create backup..."
    docker exec $(docker ps --format "table {{.Names}}" | grep postgres | head -1) pg_dump -U "$DB_USER" -d "$DB_NAME" > "$backup_file"
    
    if [ $? -eq 0 ]; then
        echo "✅ Database backup created successfully: $backup_file"
        echo "📁 Backup size: $(du -h $backup_file | cut -f1)"
        return 0
    else
        echo "❌ Database backup failed!"
        exit 1
    fi
}

# Function to check if debug tables already exist using Docker
check_debug_tables() {
    echo "🔍 Checking if debug report tables already exist..."
    
    docker exec $(docker ps --format "table {{.Names}}" | grep postgres | head -1) psql -U "$DB_USER" -d "$DB_NAME" -t -c \
        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'debug_reports');" | xargs
}

# Function to add debug tables safely
add_debug_tables() {
    echo "📋 Adding debug report tables to existing database..."
    
    # Create a temporary SQL file with just the debug table additions
    cat > add_debug_tables.sql << 'EOF'
-- Add debug report enums if they don't exist
DO $$ BEGIN
    CREATE TYPE debug_report_type AS ENUM ('BUG', 'FEATURE_REQUEST', 'PERFORMANCE', 'UI_UX', 'IMPROVEMENT', 'OTHER');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE debug_report_severity AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE debug_report_status AS ENUM ('NOT_REVIEWED', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', 'REJECTED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create debug_reports table if it doesn't exist
CREATE TABLE IF NOT EXISTS debug_reports (
    id TEXT PRIMARY KEY,
    report_id TEXT UNIQUE NOT NULL,
    user_id TEXT REFERENCES "User"(id) ON DELETE SET NULL,
    title TEXT NOT NULL,
    type debug_report_type NOT NULL,
    severity debug_report_severity NOT NULL,
    status debug_report_status NOT NULL DEFAULT 'NOT_REVIEWED',
    description TEXT NOT NULL,
    steps_to_reproduce TEXT,
    expected_behavior TEXT,
    actual_behavior TEXT,
    additional_notes TEXT,
    browser_info JSONB,
    user_agent TEXT,
    viewport_size TEXT,
    current_url TEXT,
    user_actions JSONB,
    console_logs JSONB,
    ai_analysis JSONB,
    assigned_to TEXT REFERENCES "User"(id) ON DELETE SET NULL,
    resolution_notes TEXT,
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP(3)
);

-- Create debug_report_tags table if it doesn't exist
CREATE TABLE IF NOT EXISTS debug_report_tags (
    id TEXT PRIMARY KEY,
    report_id TEXT NOT NULL REFERENCES debug_reports(id) ON DELETE CASCADE,
    tag TEXT NOT NULL,
    UNIQUE(report_id, tag)
);

-- Create debug_report_status_history table if it doesn't exist
CREATE TABLE IF NOT EXISTS debug_report_status_history (
    id TEXT PRIMARY KEY,
    report_id TEXT NOT NULL REFERENCES debug_reports(id) ON DELETE CASCADE,
    old_status debug_report_status NOT NULL,
    new_status debug_report_status NOT NULL,
    changed_by TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
    change_reason TEXT,
    changed_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create debug_report_comments table if it doesn't exist
CREATE TABLE IF NOT EXISTS debug_report_comments (
    id TEXT PRIMARY KEY,
    report_id TEXT NOT NULL REFERENCES debug_reports(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
    comment TEXT NOT NULL,
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS debug_reports_user_id_idx ON debug_reports(user_id);
CREATE INDEX IF NOT EXISTS debug_reports_type_idx ON debug_reports(type);
CREATE INDEX IF NOT EXISTS debug_reports_severity_idx ON debug_reports(severity);
CREATE INDEX IF NOT EXISTS debug_reports_status_idx ON debug_reports(status);
CREATE INDEX IF NOT EXISTS debug_reports_assigned_to_idx ON debug_reports(assigned_to);
CREATE INDEX IF NOT EXISTS debug_reports_created_at_idx ON debug_reports(created_at);
CREATE INDEX IF NOT EXISTS debug_report_tags_tag_idx ON debug_report_tags(tag);
CREATE INDEX IF NOT EXISTS debug_report_status_history_report_id_idx ON debug_report_status_history(report_id);
CREATE INDEX IF NOT EXISTS debug_report_comments_report_id_idx ON debug_report_comments(report_id);

-- Add new columns to User table if they don't exist (for debug report relations)
DO $$ BEGIN
    -- No need to add columns, existing User table should work
    NULL;
END $$;

-- Update the updated_at trigger for debug_reports if needed
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_debug_reports_updated_at ON debug_reports;
CREATE TRIGGER update_debug_reports_updated_at BEFORE UPDATE ON debug_reports FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
EOF

    # Execute the SQL using Docker container
    docker exec -i $(docker ps --format "table {{.Names}}" | grep postgres | head -1) psql -U "$DB_USER" -d "$DB_NAME" < add_debug_tables.sql
    
    if [ $? -eq 0 ]; then
        echo "✅ Debug report tables added successfully"
        rm add_debug_tables.sql
        return 0
    else
        echo "❌ Failed to add debug report tables"
        rm add_debug_tables.sql
        exit 1
    fi
}

# Main execution
echo "🚀 Starting SAFE production database migration..."
echo ""

# Check prerequisites - we'll use Docker containers instead of host psql
if ! command_exists docker; then
    echo "❌ Docker is not installed"
    echo "📥 Install with: sudo dnf install docker"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker daemon is not running"
    echo "� Start with: sudo systemctl start docker"
    exit 1
fi

# Check if postgres container is running
if ! docker ps --format "table {{.Names}}" | grep -q postgres; then
    echo "❌ PostgreSQL container is not running"
    echo "🔧 Start with: docker-compose up -d postgres"
    exit 1
fi

# Load environment variables
if [ -f .env ]; then
    echo "📄 Loading environment variables..."
    set -a
    source .env
    set +a
else
    echo "❌ .env file not found!"
    exit 1
fi

# Show current database info
echo "📊 Current Database Configuration:"
echo "  Database URL: ${DATABASE_URL%:*}:****@${DATABASE_URL##*@}"
echo ""

# Create backup
create_backup

# Check if debug tables exist
table_exists=$(check_debug_tables)
if [ "$table_exists" = "t" ]; then
    echo "✅ Debug report tables already exist. No migration needed."
    echo "🎯 You can proceed with Docker deployment."
    exit 0
fi

echo "⚠️  Debug report tables not found. Will add them safely."
echo ""

# Final confirmation
read -p "🤔 Proceed with adding debug report tables? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "🛑 Migration cancelled by user"
    exit 0
fi

# Add debug tables
add_debug_tables

echo ""
echo "🎉 Production database migration completed successfully!"
echo ""
echo "✅ What was done:"
echo "  • Created backup of existing database"
echo "  • Added debug report tables (debug_reports, debug_report_tags, etc.)"
echo "  • Added necessary indexes for performance"
echo "  • Preserved ALL existing data"
echo ""
echo "🎯 Next steps:"
echo "  1. Test the migration: Check that your existing app still works"
echo "  2. Deploy new Docker containers with debug features"
echo "  3. Verify debug report button appears for admins"
echo ""
echo "🛡️  Rollback instructions (if needed):"
echo "  1. Stop your application"
echo "  2. Restore from backup: psql < backup_YYYYMMDD_HHMMSS.sql"
echo "  3. Restart your application"
