# Payment Method Currency Filtering Fix

## Issue Description
User reported that when entering payment information, payment methods were not properly separated based on the currency they are receiving. Specifically:
- When buying CAD, user should see CAD payment methods (to receive CAD)
- When selling CAD (receiving IRR), user should see IRR payment methods (to receive IRR)
- The problem was that CAD payment methods were appearing when the user was selling CAD (should only see IRR methods)

## Root Cause Analysis
The backend logic for determining `currencyFrom` and `currencyTo` was correct:
- `currencyTo` represents the currency the user will **receive**
- This is correctly calculated based on whether the user is currencyAProvider or currencyBProvider

However, there were potential issues in the frontend:
1. **Race conditions** between transaction data loading and payment method modal opening
2. **State synchronization** issues between different stores
3. **Currency validation** not happening when currency changes during modal session
4. **Insufficient logging** to debug currency filtering issues

## Changes Made

### 1. Enhanced TransactionalChatStore (`transactionalChatStore.ts`)
- Added comprehensive logging for transaction details and currency selection
- Added explicit state clearing before opening payment form
- Enhanced debugging information to track currency flow

### 2. Enhanced PaymentMethodSelector (`TransactionalChatPaymentMethodSelector.vue`)
- Improved currency selection logic with better fallback handling
- Added currency change watchers to detect and handle currency changes
- Added state synchronization checks to prevent stale data
- Enhanced logging for currency selection debugging

### 3. Enhanced PaymentMethodsIntegration (`usePaymentMethodsIntegration.ts`)
- Added explicit form state reset to avoid stale data
- Enhanced logging with currency-specific information
- Improved error handling with currency context

### 4. Enhanced PaymentMethodsStore (`paymentMethodsStore.ts`)
- Added detailed logging for payment method filtering
- Added debug helper method `clearMethodsForCurrency()`
- Enhanced `getMethodsForTransaction()` with logging

## How the Fix Works

### Before
1. Transaction loads with `currencyTo = "IRR"`
2. Payment modal opens with potential stale currency data
3. Payment methods not properly filtered
4. CAD methods shown when IRR methods expected

### After
1. Transaction loads with `currencyTo = "IRR"`
2. Payment form state explicitly cleared
3. Payment modal opens with correct currency
4. Currency watchers detect any changes
5. Payment methods properly filtered by currency
6. Comprehensive logging for debugging

## Testing Instructions

1. **Test Buying CAD scenario:**
   - Create/join transaction where you buy CAD
   - Check console logs for `currencyTo = "CAD"`
   - Verify only CAD payment methods are shown

2. **Test Selling CAD scenario:**
   - Create/join transaction where you sell CAD  
   - Check console logs for `currencyTo = "IRR"`
   - Verify only IRR payment methods are shown

3. **Check console logs for:**
   - `[TransactionalChat] Selected currency for payment methods: XXX`
   - `[PaymentMethodSelector] Currency selection: { ... }`
   - `[PaymentMethodsStore] Filtered methods for XXX: [...]`

## Additional Debugging Tools

- Browser console access to payment method store
- Manual currency filtering: `paymentMethodsStore.getMethodsByCurrency("CAD")`
- Clear methods for testing: `paymentMethodsStore.clearMethodsForCurrency("CAD")`

## Expected Outcome

✅ **Buying CAD**: User sees only CAD payment methods (to receive CAD)
✅ **Selling CAD**: User sees only IRR payment methods (to receive IRR)  
✅ **No cross-contamination**: Payment methods properly separated by currency
✅ **Clear debugging**: Comprehensive logs to troubleshoot any remaining issues
