import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixReportTypeAssociations() {
  console.log('🔧 Fixing report type associations...');

  try {
    // Map old report types to new enum values
    const reportTypeMapping = {
      'bug': 'BUG',
      'feature-request': 'FEATURE_REQUEST',
      'performance': 'PERFORMANCE',
      'ui-ux': 'UI_UX',
      'improvement': 'IMPROVEMENT',
      'question': 'QUESTION',
      'other': 'OTHER'
    };

    // Get all existing associations
    const associations = await prisma.tagReportTypeAssociation.findMany({
      include: {
        tag: {
          select: {
            name: true
          }
        }
      }
    });

    console.log(`Found ${associations.length} existing associations to fix`);

    // Delete old associations with incorrect report types
    await prisma.tagReportTypeAssociation.deleteMany({
      where: {
        reportType: {
          notIn: ['BUG', 'FEATURE_REQUEST', 'PERFORMANCE', 'UI_UX', 'IMPROVEMENT', 'QUESTION', 'OTHER']
        }
      }
    });

    // Create new associations with correct report types
    for (const [oldType, newType] of Object.entries(reportTypeMapping)) {
      const oldAssociations = associations.filter(a => a.reportType === oldType);
      
      for (const assoc of oldAssociations) {
        await prisma.tagReportTypeAssociation.upsert({
          where: {
            tagId_reportType: {
              tagId: assoc.tagId,
              reportType: newType
            }
          },
          update: {
            weight: assoc.weight
          },
          create: {
            tagId: assoc.tagId,
            reportType: newType,
            weight: assoc.weight
          }
        });
        console.log(`✅ Updated association: ${assoc.tag.name} -> ${newType}`);
      }
    }

    console.log('🎉 Report type associations fixed successfully!');

  } catch (error) {
    console.error('❌ Error fixing associations:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

fixReportTypeAssociations().catch(console.error);
