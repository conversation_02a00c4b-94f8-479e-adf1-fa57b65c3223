// filepath: c:\\Code\\MUNygo\\backend\\src\\services\\twilio.ts
import twilio from 'twilio';
import { z } from 'zod';

// Input validation schemas
const phoneSchema = z.string().regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format (E.164 required)'); // E.164 format
const otpSchema = z.string().length(6, 'OTP must be 6 digits');

// Load credentials and dev override number from environment variables
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const serviceSid = process.env.TWILIO_VERIFY_SERVICE_SID;
const devVerifiedNumber = process.env.TWILIO_DEV_VERIFIED_NUMBER; // Load the dev number

// --- Mocking Variables ---
const enableMockOtp = process.env.MOCK_TWILIO_OTP === 'true';
const mockFixedOtp = process.env.MOCK_FIXED_OTP || '123456'; // Default mock OTP if not set

// Ensure NODE_ENV is checked correctly. Common practice is 'development'.
const isDevelopment = process.env.NODE_ENV === 'development';
console.log(`[INIT] NODE_ENV: ${process.env.NODE_ENV}, isDevelopment: ${isDevelopment}`); // Log environment at startup
console.log(`[INIT] TWILIO_DEV_VERIFIED_NUMBER: ${devVerifiedNumber}`); // Log override number at startup
if (enableMockOtp) {
  console.log(`[INIT_MOCK_OTP] Twilio OTP Mocking is ENABLED. Fixed OTP for verification: ${mockFixedOtp}`);
} else {
  console.log('[INIT_MOCK_OTP] Twilio OTP Mocking is DISABLED.');
}

// Basic check to ensure variables are loaded
if (!enableMockOtp && (!accountSid || !authToken || !serviceSid)) {
  console.error('Twilio environment variables (TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_VERIFY_SERVICE_SID) are not set and mocking is disabled.');
  // In a real app, you might throw an error or exit, depending on your error handling strategy
  // For now, we'll proceed, but the client will likely fail if used.
}

// Initialize Twilio client conditionally
// Only initialize if mocking is disabled and credentials are available
const client = (!enableMockOtp && accountSid && authToken) ? twilio(accountSid, authToken) : undefined;

if (!enableMockOtp && !client) {
  console.warn('[INIT] Twilio client not initialized - either mocking is enabled or credentials are missing.');
  if (!accountSid || !authToken || !serviceSid) {
    console.error('[INIT] Missing Twilio credentials: accountSid, authToken, or serviceSid.');
  }
}

/**
 * Sends an OTP verification code. In development, it sends to TWILIO_DEV_VERIFIED_NUMBER.
 * If MOCK_TWILIO_OTP is true, it logs a mock OTP and bypasses Twilio.
 * @param phoneNumber The phone number provided by the user (E.164 format).
 * @returns A promise that resolves or rejects.
 */
export async function sendOtp(phoneNumber: string): Promise<string | null> {
  console.log(`[sendOtp] Called with phoneNumber: ${phoneNumber}`); // Log entry

  // Validate the *original* phone number format provided by the user
  console.log(`[sendOtp] Validating original number: ${phoneNumber}`);
  try {
    phoneSchema.parse(phoneNumber);
    console.log(`[sendOtp] Original number format valid.`);
  } catch (validationError: any) {
    console.error(`[sendOtp] Original phone number validation failed: ${validationError.message}`);
    // Assuming validationError.errors is an array of objects with a 'message' property,
    // similar to Zod's ZodIssue. If validationError is a ZodError, .issues is preferred.
    // For now, explicitly type 'e' to satisfy TypeScript.
    throw new Error(`Invalid input phone number format: ${validationError.errors.map((e: { message: string }) => e.message).join(', ')}`);
  }
  
  // Determine the target number for the SMS
  let targetPhoneNumber = phoneNumber; // Default to original number
  console.log(`[sendOtp] Initial targetPhoneNumber: ${targetPhoneNumber}`);

  if (isDevelopment) {
      console.log('[sendOtp] Running in development mode.');
      if (devVerifiedNumber) {
          console.log(`[sendOtp] Dev override number found: ${devVerifiedNumber}`);
          try {
              console.log(`[sendOtp] Validating dev override number: ${devVerifiedNumber}`);
              phoneSchema.parse(devVerifiedNumber);
              console.log(`[sendOtp] Dev override number format valid.`);
              targetPhoneNumber = devVerifiedNumber; 
              console.log(`[sendOtp] Target number updated to override: ${targetPhoneNumber}`);
          } catch (devNumError: any) {
              console.error(`[sendOtp] Invalid TWILIO_DEV_VERIFIED_NUMBER format: ${devVerifiedNumber}. Error: ${devNumError.message}. Using original number.`);
          }
      } else {
          console.warn('[sendOtp] DEV MODE active but TWILIO_DEV_VERIFIED_NUMBER not set in .env. Using original number.');
      }
  } else {
       console.log('[sendOtp] Running in non-development mode.');
  }

  // --- MOCKING LOGIC ---
  if (enableMockOtp) {
    console.log(`[MOCK_OTP_SEND] OTP Mocking ENABLED.`);
    console.log(`[MOCK_OTP_SEND]   Target Phone (for logging): ${targetPhoneNumber}`);
    console.log(`[MOCK_OTP_SEND]   >>> USE THIS OTP FOR VERIFICATION: ${mockFixedOtp} <<<`);
    // Simulate a delay if needed
    // await new Promise(resolve => setTimeout(resolve, 500)); 
    return `MOCK_SID_${Date.now()}`;
  }
  // --- END MOCKING LOGIC ---

  if (!client || !serviceSid) {
    console.error('[sendOtp] Twilio client or service SID not initialized (and mocking is disabled).');
    throw new Error('Twilio service is not configured.');
  }

  try {
    console.log(`[sendOtp] Requesting Twilio verification for ${phoneNumber} (API call targeting: ${targetPhoneNumber}) via service ${serviceSid}`);

    const verification = await client.verify.v2
      .services(serviceSid)
      .verifications.create({
         to: targetPhoneNumber, 
         channel: 'sms'
        });

    console.log(`[sendOtp] Twilio request successful. Verification SID: ${verification.sid}`);
    return verification.sid;
  } catch (error: any) {
    console.error('[sendOtp] Error during Twilio operation:', error); 
    throw new Error(`Failed to send OTP: ${error.message || 'Unknown error'}`);
  }
}

/**
 * Verifies an OTP code. In development, it checks against TWILIO_DEV_VERIFIED_NUMBER.
 * If MOCK_TWILIO_OTP is true, it checks against MOCK_FIXED_OTP.
 * @param phoneNumber The phone number *originally provided by the user* and stored in the DB.
 * @param otpCode The 6-digit OTP code entered by the user.
 * @returns A promise that resolves with true if verification is successful, false otherwise.
 */
export async function verifyOtp(phoneNumber: string, otpCode: string): Promise<boolean> {
  console.log(`[verifyOtp] Called with original phoneNumber: ${phoneNumber}, otpCode: ${otpCode}`);

  // Determine the number to use for the check
  let checkPhoneNumber = phoneNumber; 
  console.log(`[verifyOtp] Initial checkPhoneNumber: ${checkPhoneNumber}`);

  if (isDevelopment) {
      console.log('[verifyOtp] Running in development mode.');
      if (devVerifiedNumber) {
          console.log(`[verifyOtp] Dev override number found: ${devVerifiedNumber}`);
          try {
              console.log(`[verifyOtp] Validating dev override number: ${devVerifiedNumber}`);
              phoneSchema.parse(devVerifiedNumber);
              console.log(`[verifyOtp] Dev override number format valid.`);
              checkPhoneNumber = devVerifiedNumber; 
              console.log(`[verifyOtp] Check number updated to override: ${checkPhoneNumber}`);
          } catch (devNumError: any) {
              console.error(`[verifyOtp] Invalid TWILIO_DEV_VERIFIED_NUMBER format: ${devVerifiedNumber}. Error: ${devNumError.message}. Using original number for check.`);
          }
      } else {
          console.warn('[verifyOtp] DEV MODE active but TWILIO_DEV_VERIFIED_NUMBER not set. Using original number for check.');
      }
  } else {
       console.log('[verifyOtp] Running in non-development mode.');
  }

  // Validate input formats (original phone number and OTP)
  try {
    console.log(`[verifyOtp] Validating inputs: original phone=${phoneNumber}, otp=${otpCode}`);
    phoneSchema.parse(phoneNumber); 
    otpSchema.parse(otpCode);       
    console.log(`[verifyOtp] Input formats valid.`);
  } catch (validationError: any) {
    console.error(`[verifyOtp] Invalid input format for verification: ${validationError.message}`);
    return false; // Treat invalid format as a failed verification
  }

  // --- MOCKING LOGIC ---
  if (enableMockOtp) {
    console.log(`[MOCK_OTP_VERIFY] OTP Mocking ENABLED.`);
    console.log(`[MOCK_OTP_VERIFY]   Verifying OTP "${otpCode}" for ${checkPhoneNumber} against mock OTP "${mockFixedOtp}"`);
    const isValid = otpCode === mockFixedOtp;
    if (isValid) {
      console.log('[MOCK_OTP_VERIFY]   Mock OTP is VALID.');
    } else {
      console.log('[MOCK_OTP_VERIFY]   Mock OTP is INVALID.');
    }
    // Simulate a delay if needed
    // await new Promise(resolve => setTimeout(resolve, 500));
    return isValid;
  }
  // --- END MOCKING LOGIC ---

  if (!client || !serviceSid) {
    console.error('[verifyOtp] Twilio client or service SID not initialized (and mocking is disabled).');
    throw new Error('Twilio service is not configured.');
  }

  try {
    console.log(`[verifyOtp] Checking Twilio verification for ${phoneNumber} (API call targeting: ${checkPhoneNumber}) with code ${otpCode}`);

    const verificationCheck = await client.verify.v2
      .services(serviceSid)
      .verificationChecks.create({
         to: checkPhoneNumber, 
         code: otpCode
        });

    console.log(`[verifyOtp] Twilio check status for ${checkPhoneNumber}: ${verificationCheck.status}`);
    return verificationCheck.status === 'approved';
  } catch (error: any) {
    console.error('[verifyOtp] Error during Twilio check:', error);
    return false; 
  }
}
