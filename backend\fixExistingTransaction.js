const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixExistingTransaction() {
  try {
    console.log('=== Fixing Existing Transaction for Testing ===\n');
    
    const transactionId = 'cmb0xaj5d0006vl20awiy3tgq';
    
    // First, show current state
    const before = await prisma.transaction.findUnique({
      where: { id: transactionId },
      select: {
        id: true,
        status: true,
        paymentDeclaredAtPayer1: true,
        paymentDeclaredAtPayer2: true
      }
    });
    
    console.log('🔍 Before Fix:');
    console.log(`Status: ${before.status}`);
    console.log(`Payer 1 declared: ${before.paymentDeclaredAtPayer1 ? 'Yes' : 'No'}`);
    console.log(`Payer 2 declared: ${before.paymentDeclaredAtPayer2 ? 'Yes' : 'No'}`);
    
    // Apply the fix - if first payer has declared payment, status should be AWAITING_SECOND_PAYER_CONFIRMATION
    if (before.paymentDeclaredAtPayer1 && before.status === 'AWAITING_FIRST_PAYER_CONFIRMATION') {
      console.log('\n🔧 Applying fix...');
      
      const updated = await prisma.transaction.update({
        where: { id: transactionId },
        data: {
          status: 'AWAITING_SECOND_PAYER_CONFIRMATION'
        },
        select: {
          id: true,
          status: true,
          paymentDeclaredAtPayer1: true,
          paymentDeclaredAtPayer2: true
        }
      });
      
      console.log('\n✅ After Fix:');
      console.log(`Status: ${updated.status}`);
      console.log(`Payer 1 declared: ${updated.paymentDeclaredAtPayer1 ? 'Yes' : 'No'}`);
      console.log(`Payer 2 declared: ${updated.paymentDeclaredAtPayer2 ? 'Yes' : 'No'}`);
      
      console.log('\n🎯 Expected Frontend Behavior:');
      console.log('   Should now show: "Confirmation 1" step');
      console.log('   Instead of: "Confirmation 2" step');
      
    } else {
      console.log('\n✅ Transaction is already in correct state or not applicable for fix');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixExistingTransaction();
