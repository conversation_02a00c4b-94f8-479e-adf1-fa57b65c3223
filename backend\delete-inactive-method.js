const { PrismaClient } = require('@prisma/client');

async function deleteInactiveDefault() {
  const prisma = new PrismaClient();
  
  try {
    console.log('=== Deleting Inactive Default Method ===');
    
    const problemMethodId = 'cmck91tmw000dvl48co9fwf3h';
    
    // First, let's verify it's the one we want to delete
    const method = await prisma.paymentReceivingInfo.findUnique({
      where: { id: problemMethodId }
    });
    
    console.log('Method to delete:');
    console.log(`- ID: ${method.id.slice(-8)}`);
    console.log(`- Active: ${method.isActive}`);
    console.log(`- Default: ${method.isDefaultForUser}`);
    console.log(`- Currency: ${method.currency}`);
    console.log(`- Bank: ${method.bankName}`);
    
    if (!method.isActive && method.isDefaultForUser) {
      // Safe to delete since it's inactive
      await prisma.paymentReceivingInfo.delete({
        where: { id: problemMethodId }
      });
      
      console.log('\n✅ Successfully deleted inactive default method');
      
      // Verify the fix
      const remaining = await prisma.paymentReceivingInfo.findMany({
        where: {
          userId: 'cmbqrmwcv0001vl48wlfffva4',
          currency: 'CAD'
        }
      });
      
      console.log('\n=== Remaining CAD methods ===');
      remaining.forEach(method => {
        console.log(`- ID: ${method.id.slice(-8)}, Active: ${method.isActive}, Default: ${method.isDefaultForUser}`);
      });
    } else {
      console.log('\n❌ Method is not safe to delete (might be active or not default)');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

deleteInactiveDefault();
