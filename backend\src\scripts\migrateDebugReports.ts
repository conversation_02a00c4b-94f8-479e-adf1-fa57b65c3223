import { PrismaClient } from '@prisma/client';
import { promises as fs } from 'fs';
import { createReadStream } from 'fs';
import { createInterface } from 'readline';
import path from 'path';
import { DebugReportService } from '../services/debugReportService';

/**
 * Migration script to transfer existing debug reports from JSON log files to PostgreSQL database
 */

interface LegacyReportData {
  reportId: string;
  timestamp: string;
  serverReceivedAt: string;
  clientTimestamp: string;
  sessionId?: string;
  userAgent?: string;
  currentUrl?: string;
  userNotes?: string;
  reportType?: string;
  reportSeverity?: string;
  reportTitle?: string;
  reportDescription?: string;
  stepsToReproduce?: string;
  expectedBehavior?: string;
  actualBehavior?: string;
  tags?: string[];
  logCount: number;
  logs: any[];
  userId?: string;
  userEmail?: string;
  username?: string;
  diagnosticData?: any;
}

class DebugReportMigration {
  private prisma: PrismaClient;
  private debugReportService: DebugReportService;
  private logDirectory: string;
  private logFileName: string;

  constructor() {
    this.prisma = new PrismaClient();
    this.debugReportService = new DebugReportService(this.prisma);
    this.logDirectory = process.env.CLIENT_LOG_DIRECTORY || this.getDefaultLogDirectory();
    this.logFileName = 'client-reports.log';
  }

  private getDefaultLogDirectory(): string {
    if (process.env.NODE_ENV === 'production') {
      return process.platform === 'win32' 
        ? path.join(process.env.TEMP || 'C:\\temp', 'munygo-logs')
        : '/var/log/munygo';
    }
    return './logs';
  }

  /**
   * Main migration method
   */
  async migrate(): Promise<void> {
    console.log('🚀 Starting debug reports migration from files to database...');
    
    try {
      // Check if log file exists
      const logFilePath = path.join(this.logDirectory, this.logFileName);
      
      try {
        await fs.access(logFilePath);
      } catch (error) {
        console.log('ℹ️ No existing log file found. Migration completed (no data to migrate).');
        return;
      }

      // Process reports incrementally using async generator for memory efficiency
      console.log('📊 Processing reports incrementally...');

      let migratedCount = 0;
      let skippedCount = 0;
      let errorCount = 0;
      let processedCount = 0;

      for await (const report of this.parseLogFile(logFilePath)) {
        processedCount++;
        
        // Show progress every 100 reports for large files
        if (processedCount % 100 === 0) {
          console.log(`📈 Processed ${processedCount} reports so far...`);
        }
        
        try {
          // Check if report already exists in database
          const existingReport = await this.debugReportService.getReportById(report.reportId);
          
          if (existingReport) {
            console.log(`⏭️ Skipping report ${report.reportId} (already exists in database)`);
            skippedCount++;
            continue;
          }

          // Convert legacy format to new format
          const convertedReport = this.convertLegacyReport(report);
          
          // Create report in database using migration method to preserve reportId
          await this.debugReportService.createReportForMigration(convertedReport, report.userId);
          
          console.log(`✅ Migrated report ${report.reportId}`);
          migratedCount++;
          
        } catch (error) {
          console.error(`❌ Failed to migrate report ${report.reportId}:`, error);
          errorCount++;
        }
      }

      console.log('\n📈 Migration Summary:');
      console.log(`✅ Successfully migrated: ${migratedCount} reports`);
      console.log(`⏭️ Skipped (already exists): ${skippedCount} reports`);
      console.log(`❌ Failed: ${errorCount} reports`);
      console.log(`📊 Total processed: ${processedCount} reports`);

      if (processedCount === 0) {
        console.log('ℹ️ No reports found in log file. Migration completed.');
      } else if (migratedCount > 0) {
        console.log('\n🎉 Migration completed successfully!');
        console.log('💡 Consider backing up the original log files before removing them.');
      }

    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    } finally {
      await this.prisma.$disconnect();
    }
  }

  /**
   * Parse the JSON log file and yield report data one by one for memory efficiency
   */
  private async *parseLogFile(filePath: string): AsyncGenerator<LegacyReportData, void, unknown> {
    const fileStream = createReadStream(filePath);
    const rl = createInterface({
      input: fileStream,
      crlfDelay: Infinity
    });

    for await (const line of rl) {
      if (line.trim()) {
        try {
          const reportData = JSON.parse(line);
          yield reportData;
        } catch (error) {
          console.warn(`⚠️ Failed to parse line: ${line.substring(0, 100)}...`);
        }
      }
    }
  }

  /**
   * Convert legacy report format to new ClientReportPayload format
   */
  private convertLegacyReport(legacyReport: LegacyReportData): any {
    return {
      reportId: legacyReport.reportId, // Preserve original reportId for referential integrity
      timestamp: legacyReport.clientTimestamp || legacyReport.timestamp,
      sessionId: legacyReport.sessionId,
      logs: legacyReport.logs || [],
      reportDetails: {
        type: legacyReport.reportType || 'other',
        severity: legacyReport.reportSeverity || 'medium',
        title: legacyReport.reportTitle || 'Migrated Report',
        description: legacyReport.reportDescription || legacyReport.userNotes || 'No description available',
        stepsToReproduce: legacyReport.stepsToReproduce,
        expectedBehavior: legacyReport.expectedBehavior,
        actualBehavior: legacyReport.actualBehavior,
        additionalNotes: legacyReport.userNotes,
        reportTags: legacyReport.tags || [],
        userContext: {
          currentPage: legacyReport.currentUrl,
          userAgent: legacyReport.userAgent,
          viewport: {
            width: 1920, // Default values since legacy data might not have this
            height: 1080
          },
          userActions: []
        }
      },
      diagnosticData: legacyReport.diagnosticData,
      userIdentification: legacyReport.userId ? {
        userId: legacyReport.userId,
        email: legacyReport.userEmail,
        username: legacyReport.username
      } : undefined
    };
  }

  /**
   * Backup existing log files before migration
   */
  async backupLogFiles(): Promise<void> {
    const logFilePath = path.join(this.logDirectory, this.logFileName);
    const backupPath = path.join(this.logDirectory, `${this.logFileName}.backup.${Date.now()}`);
    
    try {
      await fs.access(logFilePath);
      await fs.copyFile(logFilePath, backupPath);
      console.log(`📁 Created backup: ${backupPath}`);
    } catch (error) {
      console.log('ℹ️ No log file to backup');
    }
  }

  /**
   * Validate migration by comparing counts
   */
  async validateMigration(): Promise<void> {
    console.log('\n🔍 Validating migration...');
    
    try {
      const logFilePath = path.join(this.logDirectory, this.logFileName);
      const fileReportsCount = await this.countReportsInFile(logFilePath);
      const dbStats = await this.debugReportService.getStats();
      
      console.log(`📄 Reports in file: ${fileReportsCount}`);
      console.log(`🗄️ Reports in database: ${dbStats.totalReports}`);
      
      if (dbStats.totalReports >= fileReportsCount) {
        console.log('✅ Migration validation passed');
      } else {
        console.log('⚠️ Migration validation warning: Database has fewer reports than file');
      }
    } catch (error) {
      console.error('❌ Migration validation failed:', error);
    }
  }

  /**
   * Count total reports in log file without loading all into memory
   */
  private async countReportsInFile(filePath: string): Promise<number> {
    let count = 0;
    for await (const _ of this.parseLogFile(filePath)) {
      count++;
    }
    return count;
  }
}

// CLI execution
if (require.main === module) {
  const migration = new DebugReportMigration();
  
  async function runMigration() {
    try {
      console.log('🔄 Creating backup of existing log files...');
      await migration.backupLogFiles();
      
      console.log('🚀 Starting migration...');
      await migration.migrate();
      
      console.log('🔍 Validating migration...');
      await migration.validateMigration();
      
      console.log('\n🎉 Migration process completed!');
      process.exit(0);
    } catch (error) {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    }
  }

  runMigration();
}

export { DebugReportMigration };
