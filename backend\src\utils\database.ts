import { PrismaClient } from '@prisma/client';

/**
 * Shared PrismaClient singleton to prevent connection pool exhaustion
 * This ensures only one PrismaClient instance is used across the application
 */
let prisma: PrismaClient | null = null;

export function getPrismaClient(): PrismaClient {
  if (!prisma) {
    prisma = new PrismaClient();
  }
  return prisma;
}

/**
 * Gracefully disconnect from the database
 * Should be called when the application shuts down
 */
export async function disconnectDatabase(): Promise<void> {
  if (prisma) {
    await prisma.$disconnect();
    prisma = null;
  }
}
