import axios from 'axios';

/**
 * AI API Service
 * Handles communication with the backend AI endpoints for voice processing and analysis
 */

// Types for API requests and responses
export interface AudioTranscriptionRequest {
  audioData: string;
  mimeType: string;
  duration: number;
  language?: string;
}

export interface AudioTranscriptionResponse {
  success: boolean;
  transcription?: string;
  confidence?: number;
  language?: string;
  error?: string;
}

export interface AiGeneratedReport {
  title: string;
  description: string;
  stepsToReproduce?: string;
  expectedBehavior?: string;
  actualBehavior?: string;
  additionalNotes?: string;
  suggestedSeverity: 'low' | 'medium' | 'high' | 'critical';
  suggestedType: 'bug' | 'feature-request' | 'performance' | 'ui-ux' | 'improvement' | 'question' | 'other';
  suggestedTags?: Array<{
    tag: string;
    origin: 'PREDEFINED' | 'AI_SUGGESTED';
  }>;
  confidence: number;
}

export interface VoiceToReportRequest {
  audioData: string;
  mimeType: string;
  duration: number;
  language?: string;
  predefinedTags?: Record<string, string[]>;
  userContext?: {
    currentPage?: string;
    userAgent?: string;
    viewport?: {
      width: number;
      height: number;
    };
  };
}

export interface AnalysisFailure {
  reason: 'POOR_QUALITY_OR_SHORT' | 'TOO_NOISY' | 'IRRELEVANT_CONTENT';
  userMessageKey: string;
  suggestionKeys: string[];
  transcription?: string;
  confidence: number;
}

export interface VoiceToReportResponse {
  success: boolean;
  transcription?: string;
  generatedReport?: AiGeneratedReport;
  analysisFailure?: AnalysisFailure;
  processingTime?: number;
  error?: string;
  errorDetails?: {
    type: string;
    duration: number;
    code?: string;
    message: string;
    response?: number;
    responseData?: any;
  };
}

export interface AiStatusResponse {
  success: boolean;
  status?: {
    aiServiceAvailable: boolean;
    transcriptionServiceAvailable: boolean;
    supportedAudioFormats: string[];
    bestAudioFormat: string;
    maxAudioDuration: number;
    supportedLanguages: string[];
    features: {
      transcription: boolean;
      aiAnalysis: boolean;
      voiceToReport: boolean;
    };
  };
  error?: string;
}

export interface AiConfigResponse {
  success: boolean;
  config?: {
    maxAudioDuration: number;
    supportedLanguages: string[];
    supportedAudioFormats: string[];
    recommendedAudioFormat: string;
    features: {
      transcription: boolean;
      aiAnalysis: boolean;
      voiceToReport: boolean;
    };
  };
  error?: string;
}

class AiApiService {
  private baseUrl: string;

  constructor() {
    const raw = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';
    this.baseUrl = raw.replace(/\/+$/, '').replace(/\/api$/, '');
  }

  /**
   * Get AI service status
   */
  async getStatus(): Promise<AiStatusResponse> {
    try {
      const response = await axios.get(`${this.baseUrl}/api/ai/status`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get AI status:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to get AI status',
      };
    }
  }

  /**
   * Get AI configuration
   */
  async getConfig(): Promise<AiConfigResponse> {
    try {
      const response = await axios.get(`${this.baseUrl}/api/ai/config`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get AI config:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to get AI config',
      };
    }
  }

  /**
   * Transcribe audio to text
   */
  async transcribeAudio(request: AudioTranscriptionRequest): Promise<AudioTranscriptionResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/api/ai/transcribe`, request, {
        timeout: 30000, // 30 second timeout
      });
      return response.data;
    } catch (error: any) {
      console.error('Audio transcription failed:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Audio transcription failed',
      };
    }
  }

  /**
   * Process voice recording to generate bug report (optimized direct processing)
   * Uses Gemini 2.5 Flash's native audio processing capabilities
   * - Eliminates intermediate speech-to-text transcription step
   * - Single API call for audio-to-report conversion
   * - Improved performance and reduced latency
   */
  async processVoiceToReport(request: VoiceToReportRequest): Promise<VoiceToReportResponse> {
    const startTime = Date.now();
    try {
      console.log('[AiApiService] Starting voice-to-report request...');
      const response = await axios.post(`${this.baseUrl}/api/ai/voice-to-report`, request, {
        timeout: 120000, // 120 second timeout - longer than backend timeout to allow fallback processing
        onUploadProgress: (progressEvent) => {
          const progress = progressEvent.loaded / (progressEvent.total || request.audioData.length);
          console.log(`[AiApiService] Upload progress: ${Math.round(progress * 100)}%`);
        }
      });
      
      const totalTime = Date.now() - startTime;
      console.log('[AiApiService] Request completed:', {
        duration: totalTime,
        status: response.status,
        timings: response.data.timings || {}
      });
      
      return response.data;
    } catch (error: any) {
      const totalTime = Date.now() - startTime;
      const errorDetails = {
        type: error.code === 'ECONNABORTED' ? 'TIMEOUT' : 'REQUEST_ERROR',
        duration: totalTime,
        code: error.code,
        message: error.message,
        response: error.response?.status,
        responseData: error.response?.data
      };
      
      console.error('[AiApiService] Voice-to-report failed:', errorDetails);
      
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Voice-to-report processing failed',
        errorDetails
      };
    }
  }
}

// Export singleton instance
export const aiApiService = new AiApiService();
