// Function to get a JWT token by logging in
async function getAuthToken() {
  const loginPayload = {
    email: '<EMAIL>',
    password: 'password123'
  };

  try {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(loginPayload)
    });

    const data = await response.json();
    
    if (data.success && data.token) {
      console.log('✅ Login successful');
      return data.token;
    } else {
      console.log('❌ Login failed, trying to register...');
      return await registerAndLogin();
    }
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
}

// Function to register a test user and get token
async function registerAndLogin() {
  const registerPayload = {
    email: '<EMAIL>',
    password: 'password123'
  };

  try {
    // Register user
    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(registerPayload)
    });

    const registerData = await registerResponse.json();
    
    if (registerData.success) {
      console.log('✅ Registration successful');
      return registerData.token;
    }
  } catch (error) {
    console.error('Registration error:', error);
  }
  
  return null;
}

// Test creating a payment method
async function testCreatePaymentMethod(token) {
  const testPayload = {
    currency: 'IRR',
    paymentMethodType: 'BANK_TRANSFER',
    bankName: 'Test Bank',
    accountNumber: '**********',
    accountHolderName: 'Test User',
    notes: 'Test payment method'
  };

  try {
    const response = await fetch('http://localhost:3000/api/payment-methods', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(testPayload)
    });

    const data = await response.json();
    
    console.log('\n🧪 CREATE PAYMENT METHOD TEST:');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Payment method created successfully');
      return data.data;
    } else {
      console.log('❌ Failed to create payment method');
      console.error('Error details:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Network error during create:', error);
    return null;
  }
}

// Test creating a second payment method for the same currency
async function testCreateSecondPaymentMethod(token) {
  const testPayload = {
    currency: 'IRR', // Same currency as first one
    paymentMethodType: 'DIGITAL_WALLET',
    bankName: 'Another Bank',
    accountNumber: '**********',
    accountHolderName: 'Test User',
    notes: 'Second test payment method'
  };

  try {
    const response = await fetch('http://localhost:3000/api/payment-methods', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(testPayload)
    });

    const data = await response.json();
    
    console.log('\n🧪 CREATE SECOND PAYMENT METHOD TEST (same currency):');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Second payment method created successfully');
      return data.data;
    } else {
      console.log('❌ Failed to create second payment method');
      console.error('Error details:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Network error during second create:', error);
    return null;
  }
}

// Test deleting a payment method
async function testDeletePaymentMethod(token, paymentMethodId) {
  if (!paymentMethodId) {
    console.log('⚠️ No payment method ID provided for deletion test');
    return false;
  }

  try {
    const response = await fetch(`http://localhost:3000/api/payment-methods/${paymentMethodId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const data = await response.json();
    
    console.log('\n🧪 DELETE PAYMENT METHOD TEST:');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Payment method deleted successfully');
      return true;
    } else {
      console.log('❌ Failed to delete payment method');
      console.error('Error details:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ Network error during delete:', error);
    return false;
  }
}

// Test getting all payment methods
async function testGetPaymentMethods(token) {
  try {
    const response = await fetch('http://localhost:3000/api/payment-methods', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const data = await response.json();
    
    console.log('\n🧪 GET PAYMENT METHODS TEST:');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log(`✅ Retrieved ${data.count} payment methods`);
      return data.data;
    } else {
      console.log('❌ Failed to get payment methods');
      console.error('Error details:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Network error during get:', error);
    return null;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Payment Method API Tests...\n');
  
  // Get authentication token
  const token = await getAuthToken();
  if (!token) {
    console.log('❌ Could not get authentication token. Tests aborted.');
    return;
  }

  // Test creating first payment method
  const firstMethod = await testCreatePaymentMethod(token);
  
  // Test creating second payment method (same currency, should work now)
  const secondMethod = await testCreateSecondPaymentMethod(token);
  
  // Get all payment methods
  await testGetPaymentMethods(token);
  
  // Test deleting the first method if it was created
  if (firstMethod?.id) {
    await testDeletePaymentMethod(token, firstMethod.id);
  }
  
  // Test deleting the second method if it was created
  if (secondMethod?.id) {
    await testDeletePaymentMethod(token, secondMethod.id);
  }
  
  // Final check - get all methods again
  await testGetPaymentMethods(token);
  
  console.log('\n🎉 All tests completed!');
}

// Run the tests
runTests().catch(console.error);
