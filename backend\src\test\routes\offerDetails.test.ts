import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { testClient } from 'hono/testing'
import { PrismaClient } from '@prisma/client'
import { sign } from 'jsonwebtoken'
import { Hono } from 'hono'
import createOfferRoutes from '../../routes/offer'
import { NotificationService } from '../../services/notificationService'
import { MatchingService } from '../../services/matchingService'
import { Server } from 'socket.io'

const prisma = new PrismaClient()
const JWT_SECRET = process.env.JWT_SECRET || 'test-secret'

// Mock Socket.IO server
const mockIo = {
  to: vi.fn().mockReturnThis(),
  emit: vi.fn()
} as unknown as Server

// Create mock services
const mockNotificationService = {
  createNotification: vi.fn(),
  emitNotificationToUser: vi.fn()
} as unknown as NotificationService

const mockMatchingService = {
  findPotentialMatches: vi.fn()
} as unknown as MatchingService

// Create test app with offer routes
const offerRouter = createOfferRoutes(mockIo, mockNotificationService, mockMatchingService)
const testApp = new Hono().route('/offers', offerRouter)
const client = testClient(testApp) as any

describe('GET /offers/:id Backend Endpoint', () => {
  let testUser1: any
  let testUser2: any
  let testOffer: any
  let testInterest: any
  let authToken1: string
  let authToken2: string

  beforeEach(async () => {
    // Clear mocks
    vi.clearAllMocks()

    // Clean up existing test data - correct order to respect foreign key constraints
    // Only delete test-specific data to avoid affecting development users
    await prisma.chatMessage.deleteMany({
      where: {
        OR: [
          { chatSession: { userOne: { email: { endsWith: '@test.com' } } } },
          { chatSession: { userTwo: { email: { endsWith: '@test.com' } } } },
          { chatSession: { userOne: { username: { contains: 'test' } } } },
          { chatSession: { userTwo: { username: { contains: 'test' } } } }
        ]
      }
    })
    await prisma.payerNegotiation.deleteMany({
      where: {
        OR: [
          { transaction: { offer: { user: { email: { endsWith: '@test.com' } } } } },
          { transaction: { offer: { user: { username: { contains: 'test' } } } } }
        ]
      }
    })
    await prisma.transaction.deleteMany({
      where: {
        OR: [
          { offer: { user: { email: { endsWith: '@test.com' } } } },
          { offer: { user: { username: { contains: 'test' } } } }
        ]
      }
    })
    await prisma.chatSession.deleteMany({
      where: {
        OR: [
          { userOne: { email: { endsWith: '@test.com' } } },
          { userTwo: { email: { endsWith: '@test.com' } } },
          { userOne: { username: { contains: 'test' } } },
          { userTwo: { username: { contains: 'test' } } }
        ]
      }
    })
    await prisma.offerMatch.deleteMany({
      where: {
        OR: [
          { offerA: { user: { email: { endsWith: '@test.com' } } } },
          { offerB: { user: { email: { endsWith: '@test.com' } } } },
          { offerA: { user: { username: { contains: 'test' } } } },
          { offerB: { user: { username: { contains: 'test' } } } }
        ]
      }
    })
    await prisma.interest.deleteMany({
      where: {
        OR: [
          { interestedUser: { email: { endsWith: '@test.com' } } },
          { interestedUser: { username: { contains: 'test' } } },
          { offer: { user: { email: { endsWith: '@test.com' } } } },
          { offer: { user: { username: { contains: 'test' } } } }
        ]
      }
    })
    await prisma.paymentReceivingInfo.deleteMany({
      where: {
        user: { email: { endsWith: '@test.com' } }
      }
    })
    await prisma.offer.deleteMany({
      where: {
        user: { email: { endsWith: '@test.com' } }
      }
    })
    await prisma.user.deleteMany({
      where: {
        OR: [
          { email: { endsWith: '@test.com' } },
          { username: { contains: 'test' } }
        ]
      }
    })

    // Create test users
    testUser1 = await prisma.user.create({
      data: {
        username: 'offerowner',
        email: '<EMAIL>',
        password: 'hashedpass',
        emailVerified: true,
        reputationLevel: 4
      }
    })

    testUser2 = await prisma.user.create({
      data: {
        username: 'visitor',
        email: '<EMAIL>',
        password: 'hashedpass',
        emailVerified: true,
        reputationLevel: 3
      }
    })    // Create JWT tokens with required fields (userId and email)
    authToken1 = sign({ userId: testUser1.id, email: testUser1.email }, JWT_SECRET)
    authToken2 = sign({ userId: testUser2.id, email: testUser2.email }, JWT_SECRET)

    // Create test offer
    testOffer = await prisma.offer.create({
      data: {
        type: 'SELL',
        amount: 1000,
        baseRate: 150000,
        adjustmentForLowerRep: 2,
        adjustmentForHigherRep: 1,
        status: 'ACTIVE',
        userId: testUser1.id
      }
    })

    // Create test interest
    testInterest = await prisma.interest.create({      data: {
        offerId: testOffer.id,
        interestedUserId: testUser2.id,
        status: 'PENDING'
      }    })
  })

  afterEach(async () => {
    // Clean up test data - correct order to respect foreign key constraints
    // Only delete test-specific data to avoid affecting development users
    await prisma.chatMessage.deleteMany({
      where: {
        OR: [
          { chatSession: { userOne: { email: { endsWith: '@test.com' } } } },
          { chatSession: { userTwo: { email: { endsWith: '@test.com' } } } },
          { chatSession: { userOne: { username: { contains: 'test' } } } },
          { chatSession: { userTwo: { username: { contains: 'test' } } } }
        ]
      }
    })
    await prisma.payerNegotiation.deleteMany({
      where: {
        OR: [
          { transaction: { offer: { user: { email: { endsWith: '@test.com' } } } } },
          { transaction: { offer: { user: { username: { contains: 'test' } } } } }
        ]
      }
    })
    await prisma.transaction.deleteMany({
      where: {
        OR: [
          { offer: { user: { email: { endsWith: '@test.com' } } } },
          { offer: { user: { username: { contains: 'test' } } } }
        ]
      }
    })
    await prisma.chatSession.deleteMany({
      where: {
        OR: [
          { userOne: { email: { endsWith: '@test.com' } } },
          { userTwo: { email: { endsWith: '@test.com' } } },
          { userOne: { username: { contains: 'test' } } },
          { userTwo: { username: { contains: 'test' } } }
        ]
      }
    })
    await prisma.offerMatch.deleteMany({
      where: {
        OR: [
          { offerA: { user: { email: { endsWith: '@test.com' } } } },
          { offerB: { user: { email: { endsWith: '@test.com' } } } },
          { offerA: { user: { username: { contains: 'test' } } } },
          { offerB: { user: { username: { contains: 'test' } } } }
        ]
      }
    })
    await prisma.interest.deleteMany({
      where: {
        OR: [
          { interestedUser: { email: { endsWith: '@test.com' } } },
          { interestedUser: { username: { contains: 'test' } } },
          { offer: { user: { email: { endsWith: '@test.com' } } } },
          { offer: { user: { username: { contains: 'test' } } } }
        ]
      }
    })
    await prisma.paymentReceivingInfo.deleteMany({
      where: {
        user: { email: { endsWith: '@test.com' } }
      }
    })
    await prisma.offer.deleteMany({
      where: {
        user: { email: { endsWith: '@test.com' } }
      }
    })
    await prisma.user.deleteMany({
      where: {
        OR: [
          { email: { endsWith: '@test.com' } },
          { username: { contains: 'test' } }
        ]
      }
    })
  })

  describe('Successful Responses', () => {
    it('should return offer details for owner', async () => {
      const response = await client.offers[':offerId'].$get(
        { param: { offerId: testOffer.id } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(200)
      
      const offer = await response.json() as any
      
      expect(offer).toMatchObject({
        id: testOffer.id,
        type: 'SELL',
        amount: 1000,
        baseRate: 150000,
        adjustmentForLowerRep: 2,
        adjustmentForHigherRep: 1,
        status: 'ACTIVE',
        userId: testUser1.id,
        user: {
          username: 'offerowner',
          reputationLevel: 4
        },
        isOwner: true,
        userInterest: null
      })
        expect(offer.createdAt).toBeDefined()
    })

    it('should return offer details for visitor without interest', async () => {
      // Create a third user without interest
      const testUser3 = await prisma.user.create({
        data: {
          username: 'visitor2',
          email: '<EMAIL>',
          password: 'hashedpass',
          emailVerified: true,
          reputationLevel: 2
        }
      })
      
      const authToken3 = sign({ userId: testUser3.id, email: testUser3.email }, JWT_SECRET)

      const response = await client.offers[':offerId'].$get(
        { param: { offerId: testOffer.id } },
        { headers: { Authorization: `Bearer ${authToken3}` } }
      )

      expect(response.status).toBe(200)
      
      const offer = await response.json() as any
      
      expect(offer).toMatchObject({
        id: testOffer.id,
        type: 'SELL',
        amount: 1000,
        baseRate: 150000,
        status: 'ACTIVE',
        userId: testUser1.id,
        user: {
          username: 'offerowner',
          reputationLevel: 4
        },
        isOwner: false,
        userInterest: null
      })
    })

    it('should return offer details for visitor with existing interest', async () => {
      const response = await client.offers[':offerId'].$get(
        { param: { offerId: testOffer.id } },
        { headers: { Authorization: `Bearer ${authToken2}` } }
      )

      expect(response.status).toBe(200)
      
      const offer = await response.json() as any
      
      expect(offer).toMatchObject({
        id: testOffer.id,
        isOwner: false,
        userInterest: {
          id: testInterest.id,
          status: 'PENDING'
        }
      })
    })

    it('should return correct date format for createdAt', async () => {
      const response = await client.offers[':offerId'].$get(
        { param: { offerId: testOffer.id } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      const offer = await response.json() as any
      
      // Should be ISO string format
      expect(offer.createdAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
      expect(new Date(offer.createdAt).getTime()).toBe(testOffer.createdAt.getTime())
    })
  })

  describe('Different Offer Types and Statuses', () => {
    it('should handle BUY offers correctly', async () => {
      const buyOffer = await prisma.offer.create({
        data: {
          type: 'BUY',
          amount: 2000,
          baseRate: 145000,
          adjustmentForLowerRep: 1.5,
          adjustmentForHigherRep: 0.5,
          status: 'ACTIVE',
          userId: testUser1.id
        }
      })

      const response = await client.offers[':offerId'].$get(
        { param: { offerId: buyOffer.id } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(200)
      
      const offer = await response.json() as any
      expect(offer.type).toBe('BUY')
      expect(offer.amount).toBe(2000)
      expect(offer.baseRate).toBe(145000)
    })

    it('should handle INACTIVE offers correctly', async () => {
      const inactiveOffer = await prisma.offer.create({
        data: {
          type: 'SELL',
          amount: 500,
          baseRate: 155000,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: 'INACTIVE',
          userId: testUser1.id
        }
      })

      const response = await client.offers[':offerId'].$get(
        { param: { offerId: inactiveOffer.id } },
        { headers: { Authorization: `Bearer ${authToken2}` } }
      )

      expect(response.status).toBe(200)
      
      const offer = await response.json() as any
      expect(offer.status).toBe('INACTIVE')
    })
  })

  describe('Interest Status Variations', () => {
    it('should handle ACCEPTED interest status', async () => {
      // Update interest status
      await prisma.interest.update({
        where: { id: testInterest.id },
        data: { status: 'ACCEPTED' }
      })

      const response = await client.offers[':offerId'].$get(
        { param: { offerId: testOffer.id } },
        { headers: { Authorization: `Bearer ${authToken2}` } }
      )

      const offer = await response.json() as any
      expect(offer.userInterest.status).toBe('ACCEPTED')
    })

    it('should handle DECLINED interest status', async () => {
      await prisma.interest.update({
        where: { id: testInterest.id },
        data: { status: 'DECLINED' }
      })

      const response = await client.offers[':offerId'].$get(
        { param: { offerId: testOffer.id } },
        { headers: { Authorization: `Bearer ${authToken2}` } }
      )

      const offer = await response.json() as any
      expect(offer.userInterest.status).toBe('DECLINED')
    })
  })

  describe('Error Cases', () => {
    it('should return 404 for non-existent offer', async () => {
      const response = await client.offers[':offerId'].$get(
        { param: { offerId: 'non-existent-id' } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(404)
      
      const error = await response.json() as any
      expect(error.message).toBe('Offer not found or user details missing')
    })

    it('should return 401 for unauthenticated request', async () => {
      const response = await client.offers[':offerId'].$get({
        param: { offerId: testOffer.id }
      })

      expect(response.status).toBe(401)
    })    
    it('should return 401 for invalid JWT token', async () => {
      const response = await client.offers[':offerId'].$get(
        { param: { offerId: testOffer.id } },
        { headers: { Authorization: 'Bearer invalid-token' } }
      )
      
      expect(response.status).toBe(401)
    })

    it('should handle deleted user gracefully', async () => {
      // Delete related records first to avoid foreign key constraints
      await prisma.offer.delete({
        where: { id: testOffer.id }
      })
      
      // Delete the offer owner
      await prisma.user.delete({
        where: { id: testUser1.id }
      })

      const response = await client.offers[':offerId'].$get(
        { param: { offerId: testOffer.id } },
        { headers: { Authorization: `Bearer ${authToken2}` } }
      )

      expect(response.status).toBe(404)
      expect((await response.json() as any).message).toBe('Offer not found or user details missing')
    })
  })

  describe('Multiple Interests Handling', () => {
    it('should return only current user\'s interest when multiple interests exist', async () => {      // Create another user and interest
      const testUser3 = await prisma.user.create({
        data: {
          username: 'visitor3',
          email: '<EMAIL>',
          password: 'hashedpass',
          emailVerified: true,
          reputationLevel: 5
        }
      })

      await prisma.interest.create({
        data: {          offerId: testOffer.id,
          interestedUserId: testUser3.id,
          status: 'ACCEPTED'
        }
      })

      // Request as testUser2 (who has PENDING interest)
      const response = await client.offers[':offerId'].$get(
        { param: { offerId: testOffer.id } },
        { headers: { Authorization: `Bearer ${authToken2}` } }
      )

      const offer = await response.json() as any
      
      // Should only return testUser2's interest, not testUser3's
      expect(offer.userInterest).toMatchObject({
        id: testInterest.id,
        status: 'PENDING'
      })
    })
  })

  describe('Edge Cases', () => {
    it('should handle offers with zero adjustments', async () => {
      const zeroAdjustmentOffer = await prisma.offer.create({
        data: {
          type: 'SELL',
          amount: 1000,
          baseRate: 150000,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: 'ACTIVE',
          userId: testUser1.id
        }
      })

      const response = await client.offers[':offerId'].$get(
        { param: { offerId: zeroAdjustmentOffer.id } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      const offer = await response.json() as any
      expect(offer.adjustmentForLowerRep).toBe(0)
      expect(offer.adjustmentForHigherRep).toBe(0)
    })

    it('should handle offers with very large amounts', async () => {
      const largeAmountOffer = await prisma.offer.create({
        data: {
          type: 'BUY',
          amount: 999999,
          baseRate: 200000,
          adjustmentForLowerRep: 5,
          adjustmentForHigherRep: 3,
          status: 'ACTIVE',
          userId: testUser1.id
        }
      })

      const response = await client.offers[':offerId'].$get(
        { param: { offerId: largeAmountOffer.id } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      const offer = await response.json() as any
      expect(offer.amount).toBe(999999)
      expect(offer.baseRate).toBe(200000)
    })

    it('should handle malformed offer ID gracefully', async () => {
      const response = await client.offers[':offerId'].$get(
        { param: { offerId: 'malformed-id-123' } },
        { headers: { Authorization: `Bearer ${authToken1}` } }
      )

      expect(response.status).toBe(404)
    })
  })

  describe('Performance and Data Integrity', () => {
    it('should return consistent data across multiple requests', async () => {
      // Make multiple requests for the same offer
      const responses = await Promise.all([
        client.offers[':offerId'].$get(
          { param: { offerId: testOffer.id } },
          { headers: { Authorization: `Bearer ${authToken1}` } }
        ),
        client.offers[':offerId'].$get(
          { param: { offerId: testOffer.id } },
          { headers: { Authorization: `Bearer ${authToken2}` } }
        )
      ])

      const [ownerResponse, visitorResponse] = responses

      const [ownerOffer, visitorOffer] = await Promise.all([
        ownerResponse.json(),
        visitorResponse.json()
      ]) as any[]

      // Core offer data should be identical
      expect(ownerOffer.id).toBe(visitorOffer.id)
      expect(ownerOffer.amount).toBe(visitorOffer.amount)
      expect(ownerOffer.baseRate).toBe(visitorOffer.baseRate)
      expect(ownerOffer.status).toBe(visitorOffer.status)
      expect(ownerOffer.user.username).toBe(visitorOffer.user.username)

      // User-specific data should differ
      expect(ownerOffer.isOwner).toBe(true)
      expect(visitorOffer.isOwner).toBe(false)
      expect(ownerOffer.userInterest).toBeNull()
      expect(visitorOffer.userInterest).not.toBeNull()
    })
  })
})
