import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount, type VueWrapper } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import MatchNotificationCard from '@/components/MatchNotificationCard.vue';
import type { OfferMatch } from '@/types/api';
import { nextTick } from 'vue';

// Mock naive UI components
vi.mock('naive-ui', () => ({
  NCard: { template: '<div class="n-card"><slot /></div>' },
  NTag: { template: '<span class="n-tag" :class="type"><slot /></span>', props: ['type', 'size'] },
  NIcon: { template: '<span class="n-icon"><slot /></span>', props: ['size'] },  NButton: { 
    template: '<button class="n-button" :class="type" :disabled="disabled || loading" :data-testid="$attrs[\'data-testid\']" @click="$emit(\'click\')"><slot /></button>', 
    props: ['type', 'size', 'disabled', 'loading'],
    emits: ['click']
  },
  NButtonGroup: { template: '<div class="n-button-group"><slot /></div>' },
  NSpin: { template: '<div class="n-spin" v-show="show"><slot /></div>', props: ['show', 'size'] },
  NTooltip: { template: '<div class="n-tooltip"><slot /></div>' },
  NModal: { 
    template: '<div class="n-modal" v-if="show"><div class="n-modal-mask"></div><div class="n-modal-body"><slot /></div></div>', 
    props: ['show'],
    emits: ['update:show']
  },
  useMessage: () => ({
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  }),
  useNotification: () => ({
    create: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  })
}));

// Mock icons
vi.mock('@vicons/ionicons5', () => ({
  ArrowForward: { template: '<span>→</span>' },
  Star: { template: '<span>★</span>' },
  Time: { template: '<span>⏰</span>' },
  Person: { template: '<span>👤</span>' },
  Location: { template: '<span>📍</span>' }
}));

// Mock stores
const mockMatchStore = {
  acceptMatch: vi.fn(),
  declineMatch: vi.fn()
};

vi.mock('@/stores/matchStore', () => ({
  useMatchStore: () => mockMatchStore
}));

vi.mock('@/composables/useClientLogger', () => ({
  useClientLogger: () => ({
    logInfo: vi.fn(),
    logError: vi.fn(),
    logWarn: vi.fn()
  })
}));

// Mock i18n
const mockT = vi.fn((key: string) => {
  const translations: Record<string, string> = {
    'matches.newMatchFound': 'New Match Found',
    'matches.exchangeRate': 'Exchange Rate',
    'matches.compatibility': 'Compatibility Score',
    'matches.location': 'Location',
    'matches.otherParty': 'Other Party',
    'matches.expiresIn': 'Expires in',
    'matches.accept': 'Accept', 
    'matches.decline': 'Decline',    'matches.status.declined': 'Declined',
    'matches.status.partial_accept': 'Accepted',
    'matches.status.both_accepted': 'Both Accepted',
    'matches.status.pending': 'Pending',
    'matches.status.expired': 'Expired',
    'matches.status.cancelled': 'Cancelled',
    'matches.status.converted': 'Converted'
  };
  return translations[key] || key;
});

describe('MatchNotificationCard', () => {
  let wrapper: VueWrapper<any>;
  
  const mockMatch: OfferMatch = {
    matchId: 'match123',
    offerAId: 'offer1',
    offerBId: 'offer2',
    userAId: 'user1',
    userBId: 'user2',
    status: 'PENDING',
    compatibilityScore: 95.5,
    currencyA: 'USD',
    currencyB: 'EUR',
    amountA: 1000,
    amountB: 850,
    rateAToB: 0.85,
    location: 'New York',
    createdAt: '2024-01-01T00:00:00Z',
    expiresAt: '2024-01-02T00:00:00Z',
    acceptedBy: [],
    isExpired: false,
    isCurrentUserInvolved: true,
    canCurrentUserRespond: true,
    currentUserResponse: null,
    otherUserName: 'John Doe',
    otherUserReputation: { score: 85, level: 4 }
  };
  beforeEach(() => {
    vi.clearAllMocks();
    
    wrapper = mount(MatchNotificationCard, {
      props: {
        match: mockMatch
      },
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
        mocks: {
          $t: mockT
        }
      }
    });
  });

  describe('rendering', () => {
    it('should render match information correctly', () => {
      expect(wrapper.text()).toContain('New Match Found');
      expect(wrapper.text()).toContain('match123');
      expect(wrapper.text()).toContain('USD');
      expect(wrapper.text()).toContain('EUR');
      expect(wrapper.text()).toContain('1,000');
      expect(wrapper.text()).toContain('850');
    });

    it('should display exchange rate correctly', () => {
      expect(wrapper.text()).toContain('Exchange Rate');
      expect(wrapper.text()).toContain('1 USD = 0.85 EUR');
    });

    it('should display compatibility score', () => {
      expect(wrapper.text()).toContain('Compatibility Score');
      expect(wrapper.text()).toContain('95.5%');
    });

    it('should display location information', () => {
      expect(wrapper.text()).toContain('Location');
      expect(wrapper.text()).toContain('New York');
    });

    it('should display other party information', () => {
      expect(wrapper.text()).toContain('Other Party');
      expect(wrapper.text()).toContain('John Doe');
    });

    it('should show correct status tag', () => {
      const statusTag = wrapper.find('.status-tag');
      expect(statusTag.exists()).toBe(true);
      expect(statusTag.text()).toBe('Pending');
    });
  });

  describe('status handling', () => {
    it('should show pending status correctly', async () => {
      await wrapper.setProps({
        match: { ...mockMatch, status: 'PENDING' }
      });
      
      expect(wrapper.text()).toContain('Pending');
    });

    it('should show accepted status correctly', async () => {
      await wrapper.setProps({
        match: { ...mockMatch, status: 'PARTIAL_ACCEPT' }
      });
      
      expect(wrapper.text()).toContain('Accepted');
    });

    it('should show both accepted status correctly', async () => {
      await wrapper.setProps({
        match: { ...mockMatch, status: 'BOTH_ACCEPTED' }
      });
      
      expect(wrapper.text()).toContain('Both Accepted');
    });

    it('should show declined status correctly', async () => {
      await wrapper.setProps({
        match: { ...mockMatch, status: 'DECLINED' }
      });
      
      expect(wrapper.text()).toContain('Declined');
    });

    it('should show expired status correctly', async () => {
      await wrapper.setProps({
        match: { ...mockMatch, isExpired: true }
      });
      
      expect(wrapper.text()).toContain('Expired');
    });
  });

  describe('action buttons', () => {
    it('should show accept and decline buttons for pending matches', () => {
      const acceptButton = wrapper.find('[data-testid="accept-button"]');
      const declineButton = wrapper.find('[data-testid="decline-button"]');
      
      expect(acceptButton.exists()).toBe(true);
      expect(declineButton.exists()).toBe(true);
      expect(acceptButton.text()).toContain('Accept');
      expect(declineButton.text()).toContain('Decline');
    });

    it('should not show action buttons when user cannot respond', async () => {
      await wrapper.setProps({
        match: { ...mockMatch, canCurrentUserRespond: false }
      });
      
      const acceptButton = wrapper.find('[data-testid="accept-button"]');
      const declineButton = wrapper.find('[data-testid="decline-button"]');
      
      expect(acceptButton.exists()).toBe(false);
      expect(declineButton.exists()).toBe(false);
    });

    it('should not show action buttons for expired matches', async () => {
      await wrapper.setProps({
        match: { ...mockMatch, isExpired: true }
      });
      
      const acceptButton = wrapper.find('[data-testid="accept-button"]');
      const declineButton = wrapper.find('[data-testid="decline-button"]');
      
      expect(acceptButton.exists()).toBe(false);
      expect(declineButton.exists()).toBe(false);
    });
  });

  describe('user interactions', () => {
    it('should call acceptMatch when accept button is clicked', async () => {
      mockMatchStore.acceptMatch.mockResolvedValueOnce(true);
      
      const acceptButton = wrapper.find('[data-testid="accept-button"]');
      await acceptButton.trigger('click');
      
      expect(mockMatchStore.acceptMatch).toHaveBeenCalledWith('match123');
    });

    it('should call declineMatch when decline button is clicked', async () => {
      mockMatchStore.declineMatch.mockResolvedValueOnce(true);
      
      const declineButton = wrapper.find('[data-testid="decline-button"]');
      await declineButton.trigger('click');
      
      // Should trigger decline modal or direct decline
      expect(declineButton.exists()).toBe(true);
    });

    it('should show loading state when accepting match', async () => {
      let resolvePromise: (value: boolean) => void;
      const promise = new Promise<boolean>(resolve => {
        resolvePromise = resolve;
      });
      
      mockMatchStore.acceptMatch.mockReturnValueOnce(promise);
      
      const acceptButton = wrapper.find('[data-testid="accept-button"]');
      await acceptButton.trigger('click');
      
      // Check if button shows loading state
      await nextTick();
      expect(acceptButton.attributes('disabled')).toBeDefined();
      
      // Resolve the promise
      resolvePromise!(true);
      await nextTick();
    });

    it('should handle accept errors gracefully', async () => {
      mockMatchStore.acceptMatch.mockResolvedValueOnce(false);
      
      const acceptButton = wrapper.find('[data-testid="accept-button"]');
      await acceptButton.trigger('click');
      
      expect(mockMatchStore.acceptMatch).toHaveBeenCalledWith('match123');
      // Should not throw error or crash component
    });
  });

  describe('responsive design', () => {
    it('should have mobile-first CSS classes', () => {
      expect(wrapper.find('.match-notification-card').exists()).toBe(true);
      
      // Check for mobile-optimized classes
      const card = wrapper.find('.match-notification-card');
      expect(card.classes()).toContain('match-notification-card');
    });

    it('should have touch-friendly button sizes', () => {
      const buttons = wrapper.findAll('.n-button');
      expect(buttons.length).toBeGreaterThan(0);
      
      // Buttons should have appropriate classes for mobile touch targets
      buttons.forEach(button => {
        expect(button.classes()).toContain('n-button');
      });
    });
  });

  describe('accessibility', () => {
    it('should have proper ARIA labels', () => {
      const acceptButton = wrapper.find('[data-testid="accept-button"]');
      const declineButton = wrapper.find('[data-testid="decline-button"]');
      
      if (acceptButton.exists()) {
        expect(acceptButton.attributes('aria-label')).toBeDefined();
      }
      
      if (declineButton.exists()) {
        expect(declineButton.attributes('aria-label')).toBeDefined();
      }
    });

    it('should have semantic HTML structure', () => {
      expect(wrapper.find('.match-header').exists()).toBe(true);
      expect(wrapper.find('.exchange-info').exists()).toBe(true);
      expect(wrapper.find('.match-details').exists()).toBe(true);
    });
  });

  describe('data formatting', () => {
    it('should format amounts correctly', () => {
      expect(wrapper.text()).toContain('1,000');
      expect(wrapper.text()).toContain('850');
    });

    it('should format exchange rate correctly', () => {
      expect(wrapper.text()).toContain('1 USD = 0.85 EUR');
    });

    it('should format compatibility score as percentage', () => {
      expect(wrapper.text()).toContain('95.5%');
    });

    it('should handle edge cases in formatting', async () => {
      await wrapper.setProps({
        match: { 
          ...mockMatch, 
          amountA: 0.01, 
          amountB: 0.008, 
          rateAToB: 0.8 
        }
      });
      
      // Should handle small amounts
      await nextTick();
      expect(wrapper.text()).toContain('0.01');
      expect(wrapper.text()).toContain('0.008');
    });
  });

  describe('expiration handling', () => {
    it('should show expiration time for active matches', () => {
      // Component should display time until expiration
      expect(wrapper.text()).toContain('Expires in');
    });

    it('should handle expired matches appropriately', async () => {
      await wrapper.setProps({
        match: { ...mockMatch, isExpired: true }
      });
      
      expect(wrapper.text()).toContain('Expired');
      
      // Should not show action buttons for expired matches
      const acceptButton = wrapper.find('[data-testid="accept-button"]');
      expect(acceptButton.exists()).toBe(false);
    });
  });
});
