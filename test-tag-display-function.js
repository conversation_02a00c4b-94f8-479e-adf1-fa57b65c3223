// Test script to verify tag display name function
import { getTagDisplayName } from './frontend/src/utils/tagOriginUtils.js';

// Test various tag formats that might come from the backend
const testTags = [
  // Current backend format (expected)
  { tag: "Performance Issue", origin: "PREDEFINED" },
  { tag: "Bug Fix", origin: "AI_SUGGESTED" },
  
  // Legacy formats
  { tagName: "UI Problem", origin: "USER_DEFINED" },
  { tagId: "3c42a34b-87c7-410c-8e16-c017050e4", origin: "PREDEFINED" },
  
  // Edge cases
  { tag: "", origin: "PREDEFINED" },
  { },
  "Direct String Tag",
  null,
  undefined
];

console.log('🏷️  Testing tag display name function...\n');

testTags.forEach((tag, index) => {
  try {
    const displayName = getTagDisplayName(tag);
    console.log(`Test ${index + 1}:`, {
      input: tag,
      output: displayName
    });
  } catch (error) {
    console.error(`Test ${index + 1} failed:`, error.message);
  }
});

console.log('\n✅ Tag display test complete!');
