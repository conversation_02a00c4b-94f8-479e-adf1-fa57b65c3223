<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Browse Offers Mobile Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }

        .test-container {
            max-width: 100%;
            padding: 1rem;
        }

        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .test-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #2080f0;
        }

        .test-subtitle {
            color: #666;
            font-size: 0.9rem;
        }

        /* Hero Section Demo */
        .hero-demo {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem 1rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .hero-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .hero-subtitle {
            color: #666;
            margin-bottom: 1.5rem;
            font-size: 1rem;
        }

        .search-demo {
            position: relative;
            max-width: 400px;
            margin: 0 auto 1rem;
        }

        .search-input {
            width: 100%;
            padding: 12px 50px 12px 20px;
            border: 2px solid rgba(32, 128, 240, 0.2);
            border-radius: 25px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #2080f0;
            box-shadow: 0 4px 20px rgba(32, 128, 240, 0.2);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        /* Filter Demo */
        .filter-demo {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            background: white;
            color: #666;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            background: #2080f0;
            color: white;
            border-color: #2080f0;
        }

        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Offers Grid Demo */
        .offers-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .offer-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 1.25rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .offer-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(32, 128, 240, 0.3);
        }

        .offer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .offer-type {
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .offer-type.buy {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border: 1px solid rgba(39, 174, 96, 0.2);
        }

        .offer-type.sell {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.2);
        }

        .offer-amount {
            text-align: center;
            margin-bottom: 1rem;
        }

        .amount-value {
            font-size: 1.75rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .amount-rate {
            font-size: 0.875rem;
            color: #666;
        }

        .offer-user {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            padding-top: 0.75rem;
            border-top: 1px solid rgba(0, 0, 0, 0.06);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(32, 128, 240, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: #333;
        }

        .user-reputation {
            font-size: 0.75rem;
            color: #666;
        }

        .offer-action {
            width: 100%;
            padding: 0.75rem;
            border: none;
            border-radius: 25px;
            background: #2080f0;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .offer-action:hover {
            background: #1c6dd0;
            transform: translateY(-1px);
        }

        /* Mobile optimizations */
        @media (max-width: 480px) {
            .test-container {
                padding: 0.5rem;
            }

            .hero-title {
                font-size: 1.5rem;
            }

            .amount-value {
                font-size: 1.5rem;
            }
        }

        /* Tablet enhancements */
        @media (min-width: 768px) {
            .offers-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }

            .hero-title {
                font-size: 2rem;
            }
        }

        /* Desktop enhancements */
        @media (min-width: 1024px) {
            .test-container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 2rem;
            }

            .offers-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 2rem;
            }

            .hero-title {
                font-size: 2.25rem;
            }
        }

        /* Animation keyframes */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-up {
            animation: fadeInUp 0.8s ease forwards;
        }

        /* Feature toggle */
        .feature-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #2080f0;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(32, 128, 240, 0.3);
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Test Header -->
        <div class="test-header animate-fade-up">
            <h1 class="test-title">Enhanced Browse Offers</h1>
            <p class="test-subtitle">Mobile-first responsive design test</p>
        </div>

        <!-- Hero Section Demo -->
        <div class="hero-demo animate-fade-up">
            <h1 class="hero-title">Browse Offers</h1>
            <p class="hero-subtitle">Find the perfect currency exchange match</p>
            
            <div class="search-demo">
                <input 
                    type="text" 
                    class="search-input" 
                    placeholder="Search offers by amount, rate, or user..."
                >
                <span class="search-icon">🔍</span>
            </div>
            
            <div class="filter-demo">
                <button class="filter-btn active">All</button>
                <button class="filter-btn">Buying CAD</button>
                <button class="filter-btn">Selling CAD</button>
                <button class="filter-btn">Advanced Filters</button>
            </div>
        </div>

        <!-- Offers Grid Demo -->
        <div class="offers-grid">
            <!-- Sample Offer Card 1 -->
            <div class="offer-card animate-fade-up">
                <div class="offer-header">
                    <span class="offer-type buy">🔄 BUYING</span>
                    <span class="reputation">⭐ Level 4</span>
                </div>
                <div class="offer-amount">
                    <div class="amount-value">CAD $1,200</div>
                    <div class="amount-rate">57,500 IRR/CAD</div>
                </div>
                <div class="offer-user">
                    <div class="user-avatar">MA</div>
                    <div class="user-info">
                        <div class="user-name">Mohammad_A</div>
                        <div class="user-reputation">98% success • Fast response</div>
                    </div>
                </div>
                <button class="offer-action">💝 Show Interest</button>
            </div>

            <!-- Sample Offer Card 2 -->
            <div class="offer-card animate-fade-up" style="animation-delay: 0.1s">
                <div class="offer-header">
                    <span class="offer-type sell">💰 SELLING</span>
                    <span class="reputation">⭐ Level 3</span>
                </div>
                <div class="offer-amount">
                    <div class="amount-value">CAD $800</div>
                    <div class="amount-rate">58,200 IRR/CAD</div>
                </div>
                <div class="offer-user">
                    <div class="user-avatar">SN</div>
                    <div class="user-info">
                        <div class="user-name">Sara_N</div>
                        <div class="user-reputation">95% success • Fast response</div>
                    </div>
                </div>
                <button class="offer-action">💝 Show Interest</button>
            </div>

            <!-- Sample Offer Card 3 -->
            <div class="offer-card animate-fade-up" style="animation-delay: 0.2s">
                <div class="offer-header">
                    <span class="offer-type buy">🔄 BUYING</span>
                    <span class="reputation">⭐ Level 5</span>
                </div>
                <div class="offer-amount">
                    <div class="amount-value">CAD $2,500</div>
                    <div class="amount-rate">57,800 IRR/CAD</div>
                </div>
                <div class="offer-user">
                    <div class="user-avatar">RK</div>
                    <div class="user-info">
                        <div class="user-name">Reza_K</div>
                        <div class="user-reputation">99% success • Elite trader</div>
                    </div>
                </div>
                <button class="offer-action">💝 Show Interest</button>
            </div>

            <!-- Sample Offer Card 4 -->
            <div class="offer-card animate-fade-up" style="animation-delay: 0.3s">
                <div class="offer-header">
                    <span class="offer-type sell">💰 SELLING</span>
                    <span class="reputation">⭐ Level 2</span>
                </div>
                <div class="offer-amount">
                    <div class="amount-value">CAD $450</div>
                    <div class="amount-rate">58,000 IRR/CAD</div>
                </div>
                <div class="offer-user">
                    <div class="user-avatar">AB</div>
                    <div class="user-info">
                        <div class="user-name">Ahmad_B</div>
                        <div class="user-reputation">92% success • Reliable</div>
                    </div>
                </div>
                <button class="offer-action">💝 Show Interest</button>
            </div>
        </div>
    </div>

    <!-- Feature Toggle Button -->
    <button class="feature-toggle" onclick="toggleFeature()">
        🚀 Enhanced UI
    </button>

    <script>
        // Simple interactivity demo
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        document.querySelectorAll('.offer-card').forEach(card => {
            card.addEventListener('click', () => {
                card.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    card.style.transform = '';
                }, 150);
            });
        });

        function toggleFeature() {
            const btn = document.querySelector('.feature-toggle');
            const isEnhanced = btn.textContent.includes('Enhanced');
            btn.textContent = isEnhanced ? '📱 Legacy UI' : '🚀 Enhanced UI';
            
            // Simulate feature toggle effect
            document.body.style.filter = isEnhanced ? 'grayscale(0.3)' : '';
        }

        // Mobile viewport detection
        function detectMobileViewport() {
            const width = window.innerWidth;
            const testHeader = document.querySelector('.test-subtitle');
            
            if (width < 480) {
                testHeader.textContent = 'Mobile viewport detected - Touch optimized';
            } else if (width < 768) {
                testHeader.textContent = 'Tablet viewport - Progressive enhancement';
            } else {
                testHeader.textContent = 'Desktop viewport - Full feature set';
            }
        }

        window.addEventListener('resize', detectMobileViewport);
        detectMobileViewport();
    </script>
</body>
</html>
