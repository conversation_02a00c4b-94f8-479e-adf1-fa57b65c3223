<script setup lang="ts">
import { computed } from 'vue'
import { NIcon, useMessage } from 'naive-ui'
import { ArrowDown as ArrowDownIcon, MapPin as NavigationIcon } from '@vicons/tabler'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'
import { useAuthStore } from '@/stores/auth'
import { usePayerNegotiationStore } from '@/stores/payerNegotiation'
import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic'
import { useTimerDisplay } from '@/composables/useTimerDisplay'
import { useI18n } from 'vue-i18n'
import { useRtl } from '@/utils/rtl'

const { t } = useI18n()
const message = useMessage()
const transactionalChatStore = useTransactionalChatStore()
const authStore = useAuthStore()
const payerNegotiationStore = usePayerNegotiationStore()
const { direction, isRtl } = useRtl()

// RTL state management
// Debug: direction.value, isRtl.value, locale.value

// Timer Logic (using same composable as SmartPaymentSection)
const {
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
} = useTransactionFlowLogic(
  computed(() => transactionalChatStore.chatSessionId || null),
  message
);

// Use shared timer display logic
const {
  timerDisplayValue,
  timerColorClass,
  timerLabel
} = useTimerDisplay({
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
});

// Timer formatting for status bar (HH:MM only - no seconds for space optimization)
const formatTimeForStatusBar = (timeString: string): string => {
  // timeString comes in format "HH:MM:SS" or "+HH:MM:SS" from useTransactionFlowLogic
  // We need to extract just HH:MM for the status bar
  const isElapsed = timeString.startsWith('+')
  const cleanTime = timeString.replace('+', '')
  const parts = cleanTime.split(':')

  if (parts.length >= 2) {
    const hours = parts[0]
    const minutes = parts[1]
    return `${isElapsed ? '+' : ''}${hours}:${minutes}`
  }

  return timeString // fallback to original if parsing fails
}

// Computed properties

const currentStep = computed(() => transactionalChatStore.currentStep)
const currentStepIndex = computed(() => transactionalChatStore.currentStepIndex)
const totalSteps = computed(() => transactionalChatStore.totalSteps)
const timer = computed(() => transactionalChatStore.timer)
const otherUser = computed(() => transactionalChatStore.otherUser)
const transactionDetails = computed(() => transactionalChatStore.transactionDetails)

const stepTitle = computed(() => {
  const step = currentStep.value
  const otherUserName = otherUser.value?.name || 'User'
  const authStore = useAuthStore()
  const payerNegotiationStore = usePayerNegotiationStore()
  const currentUserId = authStore.user?.id
  
  // Get negotiation details to determine who is the first payer
  const negotiation = payerNegotiationStore.currentNegotiation
  const firstPayerId = negotiation?.finalizedPayerId
  
  // Determine names and amounts based on who is the first payer
  let firstPayerName = 'User'
  let firstPayerAmount = ''
  let firstPayerCurrency = ''
  
  if (firstPayerId && transactionDetails.value) {
    // Determine if current user is the first payer
    const isCurrentUserFirstPayer = firstPayerId === currentUserId
    if (isCurrentUserFirstPayer) {
      firstPayerName = t('common.you') // Use translation for "You"
      firstPayerAmount = String(transactionDetails.value.amountToSend ?? '')
      firstPayerCurrency = transactionDetails.value.currencyFrom
    } else {
      firstPayerName = otherUserName
      // If other user is first payer, they send what current user receives
      firstPayerAmount = String(transactionDetails.value.amountToReceive ?? '')
      firstPayerCurrency = transactionDetails.value.currencyTo
    }
  } else {
    // Fallback to default logic if negotiation data not available
    firstPayerAmount = String(transactionDetails.value?.amountToSend ?? '')
    firstPayerCurrency = transactionDetails.value?.currencyFrom || 'CAD'
  }
  
  switch (step?.key) {
    case 'makePayment': {
      // Step 3: Show different text based on user's turn
      if (transactionalChatStore.isUsersTurn) {
        const userAmount = String(transactionDetails.value?.amountToSend ?? '')
        const userCurrency = transactionDetails.value?.currencyFrom || 'CAD'
        return t('transactionalChat.steps.makePayment', { amount: `${userAmount} ${userCurrency}` })
      } else {
        return t('transactionalChat.steps.waitingPayer1', {
          name: firstPayerName,
          amount: `${firstPayerAmount} ${firstPayerCurrency}`
        })
      }
    }
    case 'makeSecondPayment': {
      // Step 5: Show different text based on user's turn
      if (transactionalChatStore.isUsersTurn) {
        const userAmount = String(transactionDetails.value?.amountToSend ?? '')
        const userCurrency = transactionDetails.value?.currencyFrom || 'CAD'
        return t('transactionalChat.steps.yourTurnToPay', { amount: `${userAmount} ${userCurrency}` })
      } else {
        return t('transactionalChat.steps.waitingPayer2', { name: otherUserName })
      }
    }
    case 'confirmFirstPaymentReceipt': {
      // Step 6: Show different text based on user's turn
      if (transactionalChatStore.isUsersTurn) {
        return t('transactionalChat.steps.confirmFirstPaymentReceipt')
      } else {
        return t('transactionalChat.steps.waitingPayer2', { name: otherUserName })
      }
    }
    default:
      return step ? t(step.titleKey) : ''
  }
})

// Helper function to format amounts in millions for IRR
const formatAmountForShrunk = (amount: string | number, currency: string): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  if (currency === 'IRR' && numAmount >= 1000000) {
    const millions = numAmount / 1000000
    return `${millions.toFixed(1)}M ${currency}`
  }
  return `${numAmount.toLocaleString()} ${currency}`
}

// Optimized title for shrunk mode (shorter, no "اقدام شما")
const shrunkStepTitle = computed(() => {
  const step = currentStep.value
  const otherUserName = otherUser.value?.name || 'User'
  const authStore = useAuthStore()
  const payerNegotiationStore = usePayerNegotiationStore()
  const currentUserId = authStore.user?.id

  // Get negotiation details to determine who is the first payer
  const negotiation = payerNegotiationStore.currentNegotiation
  const firstPayerId = negotiation?.finalizedPayerId

  switch (step?.key) {
    case 'makePayment': {
      if (transactionalChatStore.isUsersTurn) {
        const userAmount = String(transactionDetails.value?.amountToSend ?? '')
        const userCurrency = transactionDetails.value?.currencyFrom || 'CAD'
        const formattedAmount = formatAmountForShrunk(userAmount, userCurrency)
        return t('transactionalChat.steps.makePaymentShort', { amount: formattedAmount })
      } else {
        return t('transactionalChat.steps.waitingPaymentShort', { name: otherUserName })
      }
    }
    case 'makeSecondPayment': {
      if (transactionalChatStore.isUsersTurn) {
        const userAmount = String(transactionDetails.value?.amountToSend ?? '')
        const userCurrency = transactionDetails.value?.currencyFrom || 'CAD'
        const formattedAmount = formatAmountForShrunk(userAmount, userCurrency)
        return t('transactionalChat.steps.makeSecondPaymentShort', { amount: formattedAmount })
      } else {
        return t('transactionalChat.steps.waitingPaymentShort', { name: otherUserName })
      }
    }
    case 'confirmFirstPaymentReceipt': {
      if (transactionalChatStore.isUsersTurn) {
        return t('transactionalChat.steps.confirmReceiptShort')
      } else {
        return t('transactionalChat.steps.waitingConfirmationShort', { name: otherUserName })
      }
    }
    default:
      // Fallback to regular title but try to make it shorter
      return step ? t(step.titleKey) : ''
  }
})

// Use the timer display from composable, formatted for status bar (HH:MM only)
const timerText = computed(() => {
  if (!timerDisplayValue.value) return ''
  return formatTimeForStatusBar(timerDisplayValue.value)
})

// Use timer color from composable
const timerColor = computed(() => {
  if (!timerDisplayValue.value) return 'var(--tc-text-secondary)'

  if (isTimerCritical.value) {
    return 'var(--tc-timer-danger)'
  } else if (isTimerExpired.value) {
    return 'var(--tc-timer-danger)'
  } else {
    return 'var(--tc-timer-normal)'
  }
})



const isUsersTurn = computed(() => transactionalChatStore.isUsersTurn)
const isStatusBarShrunk = computed(() => transactionalChatStore.isStatusBarShrunk)

// Interactive navigation functionality
const pinnedAction = computed(() => transactionalChatStore.pinnedAction)
const isActionCardVisible = computed(() => transactionalChatStore.isActionCardVisible)
const shouldShowNavigationHint = computed(() => {
  return pinnedAction.value !== null && !isActionCardVisible.value
})

// Steps that require a timer in shrunk mode
const stepsWithTimer = ['makePayment', 'confirmReceipt', 'makeSecondPayment', 'confirmFirstPaymentReceipt']

const showShrunkTimer = computed(() => {
  const shrunk = isStatusBarShrunk.value
  const timerActive = !!timerDisplayValue.value
  const stepKey = currentStep.value?.key
  const show = shrunk && timerActive && stepsWithTimer.includes(stepKey)

  // Debug logging to help troubleshoot timer display issues (heavily throttled)
  if (process.env.NODE_ENV === 'development' && Math.random() < 0.01) {
    console.log('🔍 [SmartStatusBar] Timer Debug:', {
      shrunk,
      timerActive,
      timerDisplayValue: timerDisplayValue.value,
      stepKey,
      show
    })
  }

  return show
})

// Handle click on status bar to scroll to action
const handleStatusBarClick = () => {
  if (shouldShowNavigationHint.value && pinnedAction.value) {
    transactionalChatStore.scrollToActionCard(pinnedAction.value.cardId)
  }
}



</script>

<template>
  <div
    class="smart-status-bar"
    :class="[
      isStatusBarShrunk ? 'shrunk' : 'expanded',
      { 'rtl-layout': isRtl }
    ]"
    data-testid="smart-status-bar"
  >
    <!-- Shrunk (compact) mode -->
  <template v-if="isStatusBarShrunk">
      <div
        class="shrunk-bar"
        :class="{
          'shrunk-bar--interactive': shouldShowNavigationHint,
          'shrunk-bar--glowing': shouldShowNavigationHint,
          'shrunk-bar--rtl': isRtl
        }"
        @click="handleStatusBarClick"
      >
        <!-- Timer (always first in DOM, positioned via CSS) -->
        <span
          v-if="showShrunkTimer"
          class="shrunk-timer"
          :class="{
            'shrunk-timer--rtl': isRtl,
            'shrunk-timer--ltr': !isRtl
          }"
          :style="{ color: timerColor }"
          :data-debug="isRtl ? 'rtl-timer' : 'ltr-timer'"
        >
          <template v-if="isRtl">{{ timerText }} •</template>
          <template v-else>• {{ timerText }}</template>
        </span>

        <!-- Content (always after timer in DOM, positioned via CSS) -->
        <div
          class="shrunk-content"
          :class="{
            'shrunk-content--rtl': isRtl,
            'shrunk-content--ltr': !isRtl
          }"
          :data-debug="isRtl ? 'rtl-content' : 'ltr-content'"
        >
          <span class="shrunk-step">
            {{ t('transactionalChat.statusBar.stepShort', { current: currentStepIndex + 1 }) }}
          </span>
          <span
            class="shrunk-title"
            :class="{ 'shrunk-title--clickable': shouldShowNavigationHint }"
          >
            {{ shrunkStepTitle }}
          </span>
        </div>

        <!-- Navigation hint icon -->
        <div
          v-if="shouldShowNavigationHint"
          class="navigation-hint"
          :title="t('transactionalChat.statusBar.clickToScrollToAction')"
        >
          <n-icon :component="NavigationIcon" />
        </div>
      </div>
    </template>
    <!-- Expanded (full) mode -->
    <template v-else>
      <!-- Progress Steps -->
      <div
        class="progress-container"
        data-testid="progress-container"
      >
        <div class="progress-steps">
          <div
            v-for="stepIndex in totalSteps"
            :key="stepIndex"
            class="progress-step"
            :class="{
              'step-completed': stepIndex <= currentStepIndex,
              'step-active': stepIndex === currentStepIndex + 1,
              'step-pending': stepIndex > currentStepIndex + 1
            }"
            :data-testid="`progress-step-${stepIndex}`"
          >
            <div class="step-circle">
              <span v-if="stepIndex <= currentStepIndex" class="step-check">✓</span>
              <span v-else class="step-number">{{ stepIndex }}</span>
            </div>
            <div
              v-if="stepIndex < totalSteps"
              class="step-line"
              :class="{
                'line-completed': stepIndex < currentStepIndex,
                'line-active': stepIndex === currentStepIndex,
                'line-pending': stepIndex > currentStepIndex
              }"
            ></div>
          </div>
        </div>
      </div>
      <!-- Status Info -->
      <div
        class="status-info"
        data-testid="status-info"
      >
        <!-- Main Status Text -->
        <div
          class="status-main"
          :class="{ 'status-main--interactive': shouldShowNavigationHint }"
          @click="handleStatusBarClick"
        >
          <h2
            class="status-title"
            :class="{
              'user-turn': isUsersTurn,
              'status-title--clickable': shouldShowNavigationHint
            }"
            data-testid="status-title"
          >
            {{ stepTitle }}
            <!-- Navigation hint icon for expanded mode -->
            <n-icon
              v-if="shouldShowNavigationHint"
              :component="NavigationIcon"
              class="navigation-hint-inline"
              :title="t('transactionalChat.statusBar.clickToScrollToAction')"
            />
          </h2>
          <!-- Step Counter -->
          <p
            class="step-counter"
            data-testid="step-counter"
          >
            {{ t('transactionalChat.statusBar.step', {
              current: currentStepIndex + 1,
              total: totalSteps
            }) }}
          </p>
        </div>
        <!-- Timer (if active) -->
        <div
          v-if="timerDisplayValue"
          class="timer-container"
          :style="{ color: timerColor }"
          data-testid="timer-container"
        >
          <span class="timer-text">{{ timerText }}</span>
          <div
            class="timer-pulse"
            :class="{
              'pulse-danger': timer.remainingSeconds <= 60,
              'pulse-warning': timer.remainingSeconds <= 300 && timer.remainingSeconds > 60,
              'pulse-normal': timer.remainingSeconds > 300
            }"
          ></div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped>

.smart-status-bar {
  /* True glassmorphism effect - very transparent with strong blur */
  background: rgba(15, 23, 42, 0.4); /* Very transparent dark blue-gray */
  backdrop-filter: blur(24px) saturate(200%);
  -webkit-backdrop-filter: blur(24px) saturate(200%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2); /* Very subtle blue border */
  /* Enhanced layered shadows for floating effect */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08),
              0 8px 40px rgba(0, 0, 0, 0.05),
              0 2px 12px rgba(59, 130, 246, 0.06),
              inset 0 1px 0 rgba(255, 255, 255, 0.06);
  transition: all 0.25s cubic-bezier(0.4,0,0.2,1);
  /* Ensure text is readable on transparent background */
  color: rgba(255, 255, 255, 0.95);
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Expanded mode specific styles */
.smart-status-bar.expanded {
  padding: 16px;
}

/* Shrunk mode specific styles */
.smart-status-bar.shrunk {
  padding: 6px 14px;
  min-height: 36px;
  display: flex;
  align-items: center;
  font-size: 14px;
}

/* Enhanced glassmorphism for dark theme */
[data-theme="dark"] .smart-status-bar {
  background: rgba(8, 15, 30, 0.45); /* Very transparent darker blue for dark theme */
  border-bottom: 1px solid rgba(59, 130, 246, 0.25);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15),
              0 8px 40px rgba(0, 0, 0, 0.1),
              0 2px 12px rgba(59, 130, 246, 0.08),
              inset 0 1px 0 rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.98);
}

/* Light theme adjustments - very transparent and blurry */
[data-theme="light"] .smart-status-bar {
  background: rgba(255, 255, 255, 0.5); /* Very transparent light background */
  border-bottom: 1px solid rgba(59, 130, 246, 0.15);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06),
              0 8px 40px rgba(0, 0, 0, 0.03),
              0 2px 12px rgba(59, 130, 246, 0.04),
              inset 0 1px 0 rgba(255, 255, 255, 0.6);
  color: rgba(30, 41, 59, 0.95); /* Dark text for light theme */
}

.shrunk-bar {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 4px 8px;
  margin: -4px -8px;
}

/* RTL-specific layout - keep flex direction normal, use order for positioning */
.shrunk-bar--rtl {
  /* No direction change - use flex order for positioning */
}

.shrunk-bar--interactive {
  cursor: pointer;
  background: rgba(var(--primary-color-rgb), 0.05);
}

.shrunk-bar--interactive:hover {
  background: rgba(var(--primary-color-rgb), 0.1);
  transform: translateY(-1px);
}

.shrunk-bar--glowing {
  animation: statusBarGlow 2s ease-in-out infinite;
  box-shadow: 0 0 0 1px rgba(var(--primary-color-rgb), 0.3);
}

.shrunk-step {
  font-weight: 600;
  color: rgba(147, 197, 253, 0.9); /* Light blue for glassmorphism */
  margin-right: 4px;
}

/* Ensure step text is visible in both themes */
[data-theme="dark"] .shrunk-step {
  color: rgba(147, 197, 253, 0.95);
}

[data-theme="light"] .shrunk-step {
  color: rgba(59, 130, 246, 0.9); /* Darker blue for better contrast on light background */
}
.shrunk-title {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9); /* White text for glassmorphism */
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60vw;
  transition: color 0.3s ease;
}

.shrunk-title--clickable {
  color: rgba(96, 165, 250, 0.95); /* Bright blue for clickable state */
  font-weight: 600;
}

/* Theme-specific adjustments */
[data-theme="dark"] .shrunk-title {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="light"] .shrunk-title {
  color: rgba(30, 41, 59, 0.9); /* Dark text for light theme */
}

[data-theme="dark"] .shrunk-title--clickable {
  color: rgba(96, 165, 250, 1);
}

[data-theme="light"] .shrunk-title--clickable {
  color: rgba(59, 130, 246, 0.95); /* Darker blue for light theme */
}

.shrunk-timer {
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.85); /* White timer text for glassmorphism */
  flex-shrink: 0; /* Prevent timer from shrinking */
}

/* Timer positioning - use flex order for reliable positioning */
.shrunk-timer--rtl {
  order: 1; /* First position (left side) */
}

.shrunk-timer--ltr {
  order: 3; /* Last position (right side) */
}

/* Content positioning and direction */
.shrunk-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  order: 2; /* Middle position */
}

.shrunk-content--rtl {
  direction: rtl;
  text-align: right;
}

.shrunk-content--ltr {
  direction: ltr;
  text-align: left;
}

/* Theme-specific timer colors */
[data-theme="dark"] .shrunk-timer {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="light"] .shrunk-timer {
  color: rgba(30, 41, 59, 0.8); /* Dark text for light theme */
}

/* Timer color will be overridden by inline styles from timerColor computed */

.navigation-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: rgba(96, 165, 250, 0.9); /* Bright blue for glassmorphism */
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite;
}

.navigation-hint:hover {
  opacity: 1;
  color: rgba(96, 165, 250, 1);
}

/* Progress Steps */
.progress-container {
  margin-bottom: 16px;
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  max-width: 100%;
  overflow-x: auto;
  padding: 0 4px;
}

.progress-step {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  min-width: 0;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  flex-shrink: 0;
  z-index: 2;
  position: relative;
}

/* Step states */
.step-completed .step-circle {
  background-color: var(--tc-step-complete);
  color: var(--tc-text-white);
}

.step-active .step-circle {
  background-color: var(--tc-step-active);
  color: var(--tc-text-white);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.step-pending .step-circle {
  background-color: var(--tc-step-pending);
  color: var(--tc-text-muted);
}

.step-check {
  font-size: 16px;
}

.step-number {
  font-size: 12px;
}

/* Step lines */
.step-line {
  height: 2px;
  flex: 1;
  margin: 0 8px;
  transition: background-color 0.3s ease;
}

.line-completed {
  background-color: var(--tc-step-complete);
}

.line-active {
  background: linear-gradient(to right, var(--tc-step-complete), var(--tc-step-active));
}

.line-pending {
  background-color: var(--tc-step-line);
}

/* Status Info */
.status-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.status-main {
  flex: 1;
  min-width: 0;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 8px;
  margin: -8px;
}

.status-main--interactive {
  cursor: pointer;
  background: rgba(var(--primary-color-rgb), 0.05);
}

.status-main--interactive:hover {
  background: rgba(var(--primary-color-rgb), 0.1);
  transform: translateY(-1px);
}

.status-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: rgba(255, 255, 255, 0.95); /* White text for glassmorphism */
  line-height: 1.4;
  word-wrap: break-word;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.3s ease;
}

.status-title.user-turn {
  color: rgba(147, 197, 253, 0.95); /* Light blue for user turn on glassmorphism */
}

.status-title--clickable {
  color: var(--primary-color);
}

.navigation-hint-inline {
  color: var(--primary-color);
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite;
}

.navigation-hint-inline:hover {
  opacity: 1;
}

.step-counter {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7); /* Muted white for glassmorphism */
  margin: 0;
}

/* Timer */
.timer-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.timer-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.timer-pulse {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 0.8;
}

.pulse-normal {
  background-color: var(--tc-timer-normal);
  animation: pulse-normal 2s infinite;
}

.pulse-warning {
  background-color: var(--tc-timer-warning);
  animation: pulse-warning 1.5s infinite;
}

.pulse-danger {
  background-color: var(--tc-timer-danger);
  animation: pulse-danger 1s infinite;
}

@keyframes pulse-normal {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 1; }
}

@keyframes pulse-warning {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes pulse-danger {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .smart-status-bar {
    padding: 12px;
  }
  
  .progress-steps {
    padding: 0 2px;
  }
  
  .step-circle {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .step-check {
    font-size: 14px;
  }
  
  .step-number {
    font-size: 11px;
  }
  
  .status-title {
    font-size: 15px;
  }
  
  .step-counter {
    font-size: 11px;
  }
  
  .timer-text {
    font-size: 13px;
  }
  
  .status-info {
    gap: 12px;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .step-line {
    margin: 0 4px;
  }
  
  .step-circle {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }
  
  .step-check {
    font-size: 12px;
  }
  
  .step-number {
    font-size: 10px;
  }
}

/* RTL Support */
[dir="rtl"] .progress-steps {
  direction: rtl;
}

[dir="rtl"] .status-info {
  direction: rtl;
}

[dir="rtl"] .status-main {
  text-align: right;
}

[dir="rtl"] .timer-container {
  flex-direction: row-reverse;
}

/* Keyframe animations */
@keyframes statusBarGlow {
  0%, 100% {
    box-shadow: 0 0 0 1px rgba(var(--primary-color-rgb), 0.3);
  }
  50% {
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.5),
                0 0 8px rgba(var(--primary-color-rgb), 0.3);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .shrunk-bar--glowing,
  .navigation-hint,
  .navigation-hint-inline {
    animation: none;
  }

  .shrunk-bar--interactive:hover,
  .status-main--interactive:hover {
    transform: none;
  }
}
</style>
