{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build:docker": "vite build --mode production", "preview": "vite preview", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:notification": "vitest run src/stores/__tests__/notificationStore.test.ts", "test:payment": "vitest run src/components/__tests__/PaymentReadinessGate.test.ts src/stores/__tests__/auth.payment.test.ts", "test:payment-integration": "vitest run src/test/payment-persistence.integration.test.ts", "test:payment-e2e": "tsx src/test/payment-persistence-e2e.ts", "docker:build": "docker build -t munygo-frontend .", "docker:run": "docker run -p 80:80 munygo-frontend"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@iconify/vue": "^5.0.0", "@vicons/antd": "^0.13.0", "@vicons/material": "^0.13.0", "@vicons/tabler": "^0.13.0", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "canvas-confetti": "^1.9.3", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "naive-ui": "^2.41.0", "pinia": "^3.0.2", "socket.io-client": "^4.8.1", "vue-confetti-explosion": "^1.0.2", "vue-i18n": "^11.1.3", "vue-router": "^4.5.1", "zod": "^3.24.3"}, "devDependencies": {"@pinia/testing": "^1.0.1", "@playwright/test": "^1.53.2", "@stagewise-plugins/vue": "^0.5.0", "@stagewise/toolbar-vue": "^0.5.0", "@types/canvas-confetti": "^1.9.0", "@types/jwt-decode": "^2.2.1", "@types/node": "^22.15.30", "@vicons/ionicons5": "^0.13.0", "@vitejs/plugin-vue": "^5.2.2", "@vitest/coverage-v8": "^3.1.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "happy-dom": "^17.4.6", "sass-embedded": "^1.89.2", "typescript": "^5.8.3", "vite": "^6.3.1", "vitest": "^3.1.3", "vue": "^3.5.14", "vue-tsc": "^2.2.8"}, "compilerOptions": {"paths": {"@/*": ["./src/*"]}}}