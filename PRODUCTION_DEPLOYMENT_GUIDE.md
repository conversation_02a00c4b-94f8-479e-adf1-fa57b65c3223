# Production Deployment Guide - Debug Report System Update (CentOS 9)

## Overview

This guide covers deploying the new debug report system features to your **CentOS 9 production environment**, which includes:
- New database schema (DebugReport, DebugReportTag, DebugReportStatusHistory, DebugReportComment)
- Admin dashboard enhancements
- Enhanced frontend debug reporting UI
- API changes for debug report management

## System Requirements

- **OS**: CentOS 9 / RHEL 9
- **Docker**: 20.10+ with Docker Compose v2
- **Git**: For code updates
- **curl/wget**: For health checks
- **netcat (nc)**: For connectivity tests

## Pre-Deployment Checklist

### 1. **Backup Production Database**
Before making any changes, create a full backup of your production database:

```bash
# On your CentOS 9 production host machine
docker exec munygo-postgres pg_dump -U your_db_user -d your_db_name > backup_$(date +%Y%m%d_%H%M%S).sql

# Or if using external PostgreSQL
pg_dump -h your_host -U your_user -d your_db > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. **Check Current Database State**
Verify your current production database schema:

```bash
# Connect to production database
docker exec -it munygo-postgres psql -U your_user -d your_db

# Check if DebugReport table exists
\dt DebugReport*

# If it doesn't exist, you need the migration
# If it exists, check the schema version
SELECT * FROM _prisma_migrations ORDER BY finished_at DESC LIMIT 5;
```

### 3. **Verify System Requirements**
```bash
# Check Docker version
docker --version

# Check Docker Compose version
docker compose version

# Check available disk space
df -h

# Check system resources
free -h
```

## Deployment Methods

### Method 1: Automated Deployment (Recommended)

```bash
# On your CentOS 9 production host machine
cd /path/to/your/munygo/project

# Make scripts executable
chmod +x deploy-production.sh verify-deployment.sh

# Run automated deployment
./deploy-production.sh

# Verify deployment
./verify-deployment.sh
```

### Method 2: Manual Deployment

#### Step 1: Update Code on Production Server

```bash
# On your CentOS 9 production host machine
cd /path/to/your/munygo/project

# Pull latest changes
git pull origin main

# Verify the changes include debug report features
git log --oneline -10
```

#### Step 2: Environment Variables Check

Ensure your production `.env` file includes the debug report settings:

```bash
# Check if these variables are set in your production .env
grep -E "(CLIENT_LOG_DIRECTORY|VITE_ENABLE_DEBUG_REPORT)" .env

# If missing, add them:
echo "CLIENT_LOG_DIRECTORY=/app/logs" >> .env
echo "VITE_ENABLE_DEBUG_REPORT=true" >> .env
```

#### Step 3: Database Migration Strategy

You have two options for the database migration:

##### Option A: Zero-Downtime Migration (Recommended)
```bash
# Stop only the backend to run migration
docker compose stop backend

# Run migration separately
docker compose run --rm backend npx prisma migrate deploy

# Start everything back up
docker compose up -d
```

##### Option B: Full Restart (Simpler, brief downtime)
```bash
# Stop all services
docker compose down

# Rebuild and restart with migration
docker compose up -d --build
```

### Step 4: Verify Deployment

#### 4.1 Check Container Health
```bash
# Check all containers are running
docker compose ps

# Check logs for any errors
docker compose logs backend --tail=50
docker compose logs frontend --tail=50
```

#### 4.2 Verify Database Schema
```bash
# Connect to database
docker exec -it munygo-postgres psql -U your_user -d your_db

# Verify new tables exist
\dt DebugReport*

# Check the tables structure
\d DebugReport
\d DebugReportTag
\d DebugReportStatusHistory
\d DebugReportComment
```

#### 4.3 Test API Endpoints
```bash
# Get backend port (default 3004, check your docker-compose.yml)
BACKEND_PORT=$(grep -A 10 "backend:" docker-compose.yml | grep -E "ports:|^\s*-\s*\".*:3000\"" | head -1 | sed 's/.*"\([0-9]*\):3000".*/\1/' || echo "3004")

# Test basic health check
curl http://localhost:$BACKEND_PORT/health

# Test debug report endpoint (if you have authentication token)
curl -H "Authorization: Bearer your_token" http://localhost:$BACKEND_PORT/api/debug/admin/reports
```

#### 4.4 Test Frontend
```bash
# Get frontend port (default 8081, check your docker-compose.yml)  
FRONTEND_PORT=$(grep -A 10 "frontend:" docker-compose.yml | grep -E "ports:|^\s*-\s*\".*:80\"" | head -1 | sed 's/.*"\([0-9]*\):80".*/\1/' || echo "8081")

# Test frontend responds
curl http://localhost:$FRONTEND_PORT
```

- Visit your frontend URL
- Check if the debug report feature is accessible
- For admin users, verify the admin dashboard shows debug reports

## CentOS 9 Specific Considerations

### Firewall Configuration
If you have firewall enabled, ensure ports are open:

```bash
# Check firewall status
sudo firewall-cmd --state

# Open ports if needed (adjust ports based on your docker-compose.yml)
sudo firewall-cmd --permanent --add-port=3004/tcp  # Backend
sudo firewall-cmd --permanent --add-port=8081/tcp  # Frontend
sudo firewall-cmd --reload
```

### SELinux Considerations
If SELinux is enforcing, you might need to adjust policies:

```bash
# Check SELinux status
getenforce

# If you encounter permission issues with Docker volumes:
sudo setsebool -P container_manage_cgroup true
```

### Docker Service Management
```bash
# Ensure Docker service is enabled and running
sudo systemctl enable docker
sudo systemctl start docker

# Check Docker daemon status
sudo systemctl status docker
```

## Troubleshooting

### If Migration Fails

1. **Check Prisma migration status:**
```bash
docker compose run --rm backend npx prisma migrate status
```

2. **If migration is stuck, reset and retry:**
```bash
# Only if safe to do so - this will lose data!
docker compose run --rm backend npx prisma migrate reset --force
```

3. **Manual migration (last resort):**
```bash
# Get the SQL for manual execution
docker compose run --rm backend npx prisma migrate diff --from-empty --to-schema-datamodel prisma/schema.prisma --script
```

### If Containers Won't Start

1. **Check logs:**
```bash
docker compose logs backend
docker compose logs frontend
```

2. **Common issues:**
   - Database connection issues: Verify DATABASE_URL
   - Build failures: Check if all environment variables are set
   - Port conflicts: Ensure ports 3004 and 8081 are available

### If Frontend Build Fails

1. **Check build args:**
```bash
# Verify VITE_BACKEND_URL_FOR_CLIENT is set correctly
grep VITE_BACKEND_URL_FOR_CLIENT .env
```

2. **Rebuild frontend only:**
```bash
docker compose build frontend --no-cache
docker compose up -d frontend
```

## Post-Deployment Verification

### 1. Feature Testing
- [ ] Users can submit debug reports
- [ ] Admin dashboard shows debug reports
- [ ] Debug report status changes work
- [ ] File uploads work (if applicable)
- [ ] Email notifications work (if configured)

### 2. Performance Check
- [ ] Application responds normally
- [ ] Database queries are efficient
- [ ] No memory leaks in containers

### 3. Monitoring
- [ ] Set up monitoring for new debug report endpoints
- [ ] Monitor database growth (debug reports table)
- [ ] Check log file storage usage

## Environment-Specific Notes

### Docker Compose Production Settings

Your current `docker-compose.yml` includes:
- Persistent volumes for database and logs
- Health checks for all services
- Proper restart policies
- Network isolation

### Key Changes in This Update

1. **Backend Changes:**
   - New database models for debug reports
   - New API endpoints for admin management
   - Enhanced logging and file storage

2. **Frontend Changes:**
   - New admin dashboard components
   - Enhanced debug report submission UI
   - Pagination and filtering for reports

3. **Infrastructure Changes:**
   - Persistent log volume for debug reports
   - Database schema additions

## Rollback Plan

If issues occur, you can rollback:

1. **Code Rollback:**
```bash
git log --oneline -10  # Find previous commit
git checkout previous_commit_hash
docker compose up -d --build
```

2. **Database Rollback:**
```bash
# Stop services
docker compose down

# Restore database backup
docker exec -i munygo-postgres psql -U your_user -d your_db < backup_file.sql

# Start services
docker compose up -d
```

## Maintenance

### Regular Tasks
- Monitor debug report storage growth
- Clean up old debug reports periodically
- Update admin access controls as needed

### Log Rotation
Consider setting up log rotation for debug report files:
```bash
# Add to your host crontab
0 2 * * 0 docker exec munygo-backend find /app/logs -name "*.log" -mtime +30 -delete
```

---

**Important:** Always test these procedures in a staging environment first!

## Environment Configuration

Before deployment, ensure the following environment variables are properly configured in the `.env` file:

### Required Variables
- `GEMINI_API_KEY`: Google Gemini API key for AI-powered debug report processing
- `CLIENT_LOG_DIRECTORY`: Directory for debug report storage (default: `/app/logs`)
- `VITE_ENABLE_DEBUG_REPORT`: Enable debug reporting features (default: `true`)

### Database Configuration
- `POSTGRES_DB`: munygo_db
- `POSTGRES_USER`: munygo_user  
- `POSTGRES_PASSWORD`: U6^#A7sBp&tE%qgRt5Ra

### Other Required Variables
- `JWT_SECRET`: JWT signing secret
- `FRONTEND_URL`: Production frontend URL
- Email and Twilio configuration for notifications
