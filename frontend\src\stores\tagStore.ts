import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { 
  TagCategory, 
  TagWithRelations,
  TagsResponse,
  TagsQuery,
  CreateTagRequest,
  UpdateTagRequest,
  CreateTagCategoryRequest,
  UpdateTagCategoryRequest,
  TagAnalytics,
  TagSuggestionRequest,
  TagSuggestionResponse
} from '@/types/api';
import apiClient from '@/services/apiClient';

export const useTagStore = defineStore('tag', () => {
  // State
  const categories = ref<TagCategory[]>([]);
  const tags = ref<TagWithRelations[]>([]);
  const isLoadingCategories = ref(false);
  const isLoadingTags = ref(false);
  const isLoadingAnalytics = ref(false);
  const error = ref<string | null>(null);
  const analytics = ref<TagAnalytics | null>(null);

  // Computed
  const activeCategories = computed(() => 
    categories.value.filter(cat => cat.isActive)
  );

  const activeTags = computed(() =>
    tags.value.filter(tag => tag.isActive)
  );

  const tagsByCategory = computed(() => {
    const grouped: Record<string, TagWithRelations[]> = {};
    activeTags.value.forEach(tag => {
      const categoryName = tag.category?.name || 'Uncategorized';
      if (!grouped[categoryName]) {
        grouped[categoryName] = [];
      }
      grouped[categoryName].push(tag);    });
    return grouped;
  });

  // Check if AI suggestions are available
  const isAiSuggestionsAvailable = computed(() => {
    // For now, always false until backend endpoint is implemented
    return false;
  });

  // Category Actions
  async function fetchCategories(force = false) {
    if (categories.value.length > 0 && !force) return;

    isLoadingCategories.value = true;
    error.value = null;    try {
      const response = await apiClient.get<{ categories: TagCategory[], total: number }>('/tags/categories');
      categories.value = response.data.categories.sort((a, b) => (a.order || 0) - (b.order || 0));
    } catch (err) {
      error.value = 'Failed to fetch tag categories';
      console.error('Error fetching categories:', err);
      throw err;
    } finally {
      isLoadingCategories.value = false;
    }
  }
  async function createCategory(data: CreateTagCategoryRequest): Promise<TagCategory> {
    try {      const response = await apiClient.post<TagCategory>('/tags/categories', data);
      const newCategory = response.data;
      categories.value.push(newCategory);
      categories.value.sort((a, b) => (a.order || 0) - (b.order || 0));
      return newCategory;
    } catch (err) {
      error.value = 'Failed to create category';
      console.error('Error creating category:', err);
      throw err;
    }
  }
  async function updateCategory(id: string, data: UpdateTagCategoryRequest): Promise<TagCategory> {
    try {
      const response = await apiClient.put<TagCategory>(`/tags/categories/${id}`, data);
      const updatedCategory = response.data;      const index = categories.value.findIndex(cat => cat.id === id);
      if (index !== -1) {
        categories.value[index] = updatedCategory;
        categories.value.sort((a, b) => (a.order || 0) - (b.order || 0));
      }
      return updatedCategory;
    } catch (err) {
      error.value = 'Failed to update category';
      console.error('Error updating category:', err);
      throw err;
    }
  }
  async function deleteCategory(id: string): Promise<void> {
    try {
      await apiClient.delete(`/tags/categories/${id}`);
      categories.value = categories.value.filter(cat => cat.id !== id);
      // Also remove tags from this category
      tags.value = tags.value.filter(tag => tag.categoryId !== id);
    } catch (err) {
      error.value = 'Failed to delete category';
      console.error('Error deleting category:', err);
      throw err;
    }  }
  
  // Tag Actions
  async function fetchTags(query: TagsQuery = {}) {
    isLoadingTags.value = true;
    error.value = null;

    try {
      const params = new URLSearchParams();
      if (query.categoryId) params.append('categoryId', query.categoryId);
      if (query.reportType) params.append('reportType', query.reportType);
      // Only send isActive if explicitly set to false, otherwise let backend default to true
      if (query.isActive === false) params.append('isActive', 'false');
      if (query.search) params.append('search', query.search);
      if (query.limit) params.append('limit', query.limit.toString());
      if (query.offset) params.append('offset', query.offset.toString());
      if (query.includeAnalytics) params.append('includeAnalytics', 'true');

      const response = await apiClient.get<TagsResponse>(`/tags/predefined?${params.toString()}`);
      tags.value = response.data.tags;      // Update categories if they came with the response
      if (response.data.categories?.length) {
        categories.value = response.data.categories.sort((a, b) => (a.order || 0) - (b.order || 0));
      }
    } catch (err) {
      error.value = 'Failed to fetch tags';
      console.error('Error fetching tags:', err);
      throw err;
    } finally {
      isLoadingTags.value = false;
    }
  }
  async function createTag(data: CreateTagRequest): Promise<TagWithRelations> {
    try {
      const response = await apiClient.post<TagWithRelations>('/tags', data);
      const newTag = response.data;
      tags.value.push(newTag);
      return newTag;
    } catch (err) {
      error.value = 'Failed to create tag';
      console.error('Error creating tag:', err);
      throw err;
    }
  }
  async function updateTag(id: string, data: UpdateTagRequest): Promise<TagWithRelations> {
    try {
      const response = await apiClient.put<TagWithRelations>(`/tags/${id}`, data);
      const updatedTag = response.data;
      const index = tags.value.findIndex(tag => tag.id === id);
      if (index !== -1) {
        tags.value[index] = updatedTag;
      }
      return updatedTag;
    } catch (err) {
      error.value = 'Failed to update tag';
      console.error('Error updating tag:', err);
      throw err;
    }
  }
  async function deleteTag(id: string): Promise<void> {
    try {
      await apiClient.delete(`/tags/${id}`);
      tags.value = tags.value.filter(tag => tag.id !== id);
    } catch (err) {
      error.value = 'Failed to delete tag';
      console.error('Error deleting tag:', err);
      throw err;
    }
  }

  // Analytics
  async function fetchAnalytics(): Promise<TagAnalytics> {
    isLoadingAnalytics.value = true;
    error.value = null;

    try {
      const response = await apiClient.get<TagAnalytics>('/tags/analytics');
      analytics.value = response.data;
      return response.data;
    } catch (err) {
      error.value = 'Failed to fetch analytics';
      console.error('Error fetching analytics:', err);
      throw err;
    } finally {
      isLoadingAnalytics.value = false;
    }
  }  // AI Suggestions
  async function getSuggestions(_request: TagSuggestionRequest): Promise<TagSuggestionResponse> {
    try {
      // TODO: Implement tag suggestions endpoint in backend
      // For now, return empty suggestions silently
      if (import.meta.env.DEV) {
        console.debug('[TagStore] AI tag suggestions not implemented, using fallback');
      }
      return {
        suggestions: [],
        confidence: 0
      };
      
      // Once backend endpoint is ready, uncomment:
      // const response = await apiClient.post<TagSuggestionResponse>('/ai/tags/suggestions', request);
      // return response.data;
    } catch (err) {
      error.value = 'Failed to get tag suggestions';
      console.error('Error getting suggestions:', err);
      throw err;
    }
  }

  // Helper functions
  function getTagsByReportType(reportType: string): TagWithRelations[] {
    return activeTags.value.filter(tag => 
      tag.reportTypes.includes(reportType)
    );
  }

  function getTagById(id: string): TagWithRelations | undefined {
    return tags.value.find(tag => tag.id === id);
  }

  function getCategoryById(id: string): TagCategory | undefined {
    return categories.value.find(category => category.id === id);
  }

  function getTagsByCategory(categoryId: string): TagWithRelations[] {
    return activeTags.value.filter(tag => tag.categoryId === categoryId);
  }

  async function searchTags(query: string, reportType?: string): Promise<TagWithRelations[]> {
    await fetchTags({
      search: query,
      reportType,
      isActive: true
    });
    return tags.value;  }

  // Initialize store
  async function initialize() {
    try {
      await Promise.all([
        fetchCategories(),
        fetchTags() // Let backend default to active tags
      ]);
    } catch (err) {
      console.error('Failed to initialize tag store:', err);
    }
  }

  function clearError() {
    error.value = null;
  }

  function reset() {
    categories.value = [];
    tags.value = [];
    analytics.value = null;
    error.value = null;
    isLoadingCategories.value = false;
    isLoadingTags.value = false;
    isLoadingAnalytics.value = false;
  }

  return {
    // State
    categories,
    tags,
    analytics,
    isLoadingCategories,
    isLoadingTags,
    isLoadingAnalytics,
    error,    // Computed
    activeCategories,
    activeTags,
    tagsByCategory,
    isAiSuggestionsAvailable,

    // Category Actions
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,

    // Tag Actions
    fetchTags,
    createTag,
    updateTag,
    deleteTag,

    // Analytics & AI
    fetchAnalytics,
    getSuggestions,

    // Helpers
    getTagsByReportType,
    getTagById,
    getCategoryById,
    getTagsByCategory,
    searchTags,
    initialize,
    clearError,
    reset
  };
});
