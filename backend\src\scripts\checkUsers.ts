import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Fetching all users...');
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        emailVerified: true,
        phoneVerified: true,
        createdAt: true,
        updatedAt: true,
        reputationScore: true,
        reputationLevel: true
      }
    });
    
    console.log('Total users found:', users.length);
    console.log('User data:');
    
    // Print each user
    users.forEach(user => {
      console.log(`User ID: ${user.id}`);
      console.log(`Email: ${user.email}`);
      console.log(`Created At: ${user.createdAt || 'NULL'}`);
      console.log(`Updated At: ${user.updatedAt || 'NULL'}`);
      console.log(`Email Verified: ${user.emailVerified}`);
      console.log(`Phone Verified: ${user.phoneVerified}`);
      console.log(`Reputation Score: ${user.reputationScore}`);
      console.log(`Reputation Level: ${user.reputationLevel}`);
      console.log('----------------------------------------');
    });
  } catch (error) {
    console.error('Error fetching users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('Check completed successfully.'))
  .catch(e => {
    console.error('Script failed:', e);
    process.exit(1);
  });
