import { promises as fs } from 'fs';
import path from 'path';

/**
 * Service for managing log file rotation and cleanup
 */
export class LogRotationService {
  private readonly logDirectory: string;
  private readonly maxFileSize: number; // in bytes
  private readonly maxFiles: number;

  constructor(
    logDirectory: string,
    maxFileSize: number = 10 * 1024 * 1024, // 10MB default
    maxFiles: number = 5 // Keep 5 rotated files
  ) {
    this.logDirectory = logDirectory;
    this.maxFileSize = maxFileSize;
    this.maxFiles = maxFiles;
  }

  /**
   * Check if log file needs rotation and rotate if necessary
   */
  async rotateIfNeeded(logFileName: string): Promise<void> {
    const logFilePath = path.join(this.logDirectory, logFileName);
    
    try {
      const stats = await fs.stat(logFilePath);
      
      if (stats.size >= this.maxFileSize) {
        await this.rotateLogFile(logFileName);
        console.log(`📁 [LogRotation] Rotated ${logFileName} (size: ${Math.round(stats.size / 1024 / 1024)}MB)`);
      }
    } catch (error) {
      // File doesn't exist yet, no rotation needed
      if ((error as any).code !== 'ENOENT') {
        console.error('❌ [LogRotation] Error checking log file:', error);
      }
    }
  }

  /**
   * Rotate the log file by moving it to a numbered backup
   */
  private async rotateLogFile(logFileName: string): Promise<void> {
    const logFilePath = path.join(this.logDirectory, logFileName);
    const baseName = path.parse(logFileName).name;
    const extension = path.parse(logFileName).ext;

    // Shift existing backup files
    for (let i = this.maxFiles - 1; i >= 1; i--) {
      const oldBackup = path.join(this.logDirectory, `${baseName}.${i}${extension}`);
      const newBackup = path.join(this.logDirectory, `${baseName}.${i + 1}${extension}`);
      
      try {
        await fs.access(oldBackup);
        if (i === this.maxFiles - 1) {
          // Delete the oldest file
          await fs.unlink(oldBackup);
        } else {
          // Rename to next number
          await fs.rename(oldBackup, newBackup);
        }
      } catch (error) {
        // File doesn't exist, continue
      }
    }

    // Move current log to .1 backup
    const firstBackup = path.join(this.logDirectory, `${baseName}.1${extension}`);
    try {
      await fs.rename(logFilePath, firstBackup);
    } catch (error) {
      console.error('❌ [LogRotation] Error rotating log file:', error);
    }
  }

  /**
   * Clean up old backup files beyond retention policy
   */
  async cleanupOldLogs(logFileName: string): Promise<void> {
    const baseName = path.parse(logFileName).name;
    const extension = path.parse(logFileName).ext;

    try {
      const files = await fs.readdir(this.logDirectory);
      const backupFiles = files
        .filter(file => file.startsWith(`${baseName}.`) && file.endsWith(extension))
        .filter(file => {
          const match = file.match(new RegExp(`${baseName}\\.(\\d+)${extension.replace('.', '\\.')}`));
          return match && parseInt(match[1]) > this.maxFiles;
        });

      for (const backupFile of backupFiles) {
        const backupPath = path.join(this.logDirectory, backupFile);
        await fs.unlink(backupPath);
        console.log(`🗑️ [LogRotation] Cleaned up old backup: ${backupFile}`);
      }
    } catch (error) {
      console.error('❌ [LogRotation] Error cleaning up old logs:', error);
    }
  }

  /**
   * Get log directory disk usage information
   */
  async getLogDirectoryStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    oldestFile?: string;
    newestFile?: string;
  }> {
    try {
      const files = await fs.readdir(this.logDirectory);
      let totalSize = 0;
      let oldestFile: string | undefined;
      let newestFile: string | undefined;
      let oldestTime = Infinity;
      let newestTime = 0;

      for (const file of files) {
        const filePath = path.join(this.logDirectory, file);
        const stats = await fs.stat(filePath);
        totalSize += stats.size;

        if (stats.mtimeMs < oldestTime) {
          oldestTime = stats.mtimeMs;
          oldestFile = file;
        }
        if (stats.mtimeMs > newestTime) {
          newestTime = stats.mtimeMs;
          newestFile = file;
        }
      }

      return {
        totalFiles: files.length,
        totalSize,
        oldestFile,
        newestFile,
      };
    } catch (error) {
      return {
        totalFiles: 0,
        totalSize: 0,
      };
    }
  }
}
