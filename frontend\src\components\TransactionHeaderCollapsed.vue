<template>
  <div class="transaction-header-collapsed">
    <!-- Left User Section (Logically Left) -->
    <div class="user-section user-left-logical">
      <div class="user-info">
        <span class="user-name">{{ leftUser.name }}</span>
        <n-tag size="small" type="info" class="amount-tag">
          <span v-if="!isRTL">{{ leftUser.amount }} {{ leftUser.currency }}</span>
          <span v-else>{{ leftUser.currency }} {{ leftUser.amount }}</span>
        </n-tag>
      </div>
      <n-icon size="16" class="arrow-icon arrow-right">
        <ChevronRight />
      </n-icon>
    </div>

    <!-- Center: Exchange Rate -->
    <div class="rate-section">
      <span class="rate-text">{{ transactionDetails.exchangeRateText }}</span>
    </div>    <!-- Right User Section (Logically Right) -->
    <div class="user-section user-right-logical">
      <n-icon size="16" class="arrow-icon arrow-left">
        <ChevronLeft />
      </n-icon>
      <div class="user-info">
        <span class="user-name">{{ rightUser.name }}</span>
        <n-tag size="small" type="success" class="amount-tag">
          <span v-if="!isRTL">{{ rightUser.amount }} {{ rightUser.currency }}</span>
          <span v-else>{{ rightUser.currency }} {{ rightUser.amount }}</span>
        </n-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NTag, NIcon } from 'naive-ui'
import { 
  KeyboardArrowRightFilled as ChevronRight,
  KeyboardArrowLeftFilled as ChevronLeft 
} from '@vicons/material'
import { formatAmount } from '@/utils/currencyUtils'

// Check if current language is RTL (Persian)
const isRTL = computed(() => {
  if (typeof document !== 'undefined') {
    return document.documentElement.dir === 'rtl';
  }
  return false; 
})

interface TransactionDetails {
  userAName: string
  userBName: string
  userAPaysAmount: number
  userAPaysCurrency: string
  userBPaysAmount: number
  userBPaysCurrency: string
  exchangeRateText: string
}

const props = defineProps<{
  transactionDetails: TransactionDetails
}>()

// Display userA on the left and userB on the right as determined by parent
const leftUser = computed(() => {
  return {
    name: props.transactionDetails.userAName,
    amount: formatAmount(props.transactionDetails.userAPaysAmount, props.transactionDetails.userAPaysCurrency),
    currency: props.transactionDetails.userAPaysCurrency
  }
})

const rightUser = computed(() => {
  return {
    name: props.transactionDetails.userBName,
    amount: formatAmount(props.transactionDetails.userBPaysAmount, props.transactionDetails.userBPaysCurrency),
    currency: props.transactionDetails.userBPaysCurrency
  }
})
</script>

<style scoped>
.transaction-header-collapsed {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 6px 8px; /* Reduced from 8px 12px */
  gap: 8px; /* Reduced from 12px */
  box-sizing: border-box;
  background-color: var(--n-card-color);
  border-radius: var(--n-border-radius);
  border: 1px solid var(--n-border-color);
  direction: ltr !important; /* Force LTR for the main flex container */
}

.user-section {
  display: flex;
  align-items: center;
  gap: 4px; /* Reduced from 8px */
}

.user-info { /* Added style for spacing and flex layout */
  display: flex;
  align-items: center;
  gap: 4px; /* Reduced from 6px */
  /* flex-direction: row; is the default and is desired here */
}

.user-left-logical {
  flex: 1;
  justify-content: flex-start;
  min-width: 0;
}

.user-right-logical {
  flex: 1;
  justify-content: flex-end;
  min-width: 0;
}

.user-left-logical .user-info {
  /* DOM order: Name, Tag. Default flex-direction: row.
     LTR Visual: Name Tag
     RTL Visual (due to children's direction:rtl): Name (on right) Tag (on left) -> still "Name then Tag" sequence
  */
}

.user-right-logical .user-info {
  /* DOM order: Name, Tag. Default flex-direction: row. (Now consistent with left side)
     For wider screens: We want Tag, Name visual order, so use row-reverse
     LTR Visual: Tag Name (reversed from DOM order)
     RTL Visual: Name Tag (due to children's direction:rtl + row-reverse)
  */
  flex-direction: row-reverse;
}

.rate-section {
  flex-shrink: 0;
  padding: 1px 4px; /* Reduced from 2px 6px */
  background-color: rgba(24, 160, 88, 0.12);
  border: 1px solid rgba(24, 160, 88, 0.2);
  border-radius: 3px;
  text-align: center;
  overflow: hidden; /* Add overflow: hidden to the container */
  /* order: 2; For explicit ordering */
}

.rate-text {
  font-size: 0.8em;
  font-weight: 400;
  color: #333;
  white-space: nowrap;
  overflow: hidden; /* Hide the overflowing part */
  text-overflow: ellipsis; /* Show ... for overflowed text */
  display: block; /* Required for text-overflow to work */
  width: 100%; /* Use full width for ellipsis calculation */
}

.user-name {
  font-size: 0.9em;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
  color: var(--n-text-color);
}

.amount-tag {
  font-size: 0.85em !important;
  padding: 1px 4px !important; /* Reduced from 2px 6px */
  height: auto !important;
  line-height: 1.2 !important; /* Reduced from 1.3 */
  border-radius: 3px !important; /* Reduced from 4px */
}

.arrow-icon {
  color: var(--n-icon-color);
  flex-shrink: 0;
}

/* RTL Specific Styles */
[dir="rtl"] .transaction-header-collapsed {
  /* The parent is already LTR, so children order is fixed. We only manage text direction now. */
}

[dir="rtl"] .user-name,
[dir="rtl"] .amount-tag span { /* Target the span inside for amount/currency */
  direction: rtl;
  unicode-bidi: embed; /* Or bidi-override if embed is not enough */
}

[dir="rtl"] .rate-text {
  direction: ltr; /* Ensure rate text is LTR as it's a standard format */
  unicode-bidi: embed; /* Keep this to ensure proper embedding */
}

/* Adjust user-info for RTL to ensure correct visual order of name and tag */
[dir="rtl"] .user-left-logical .user-info {
  /* DOM order: Name, Tag. With children's 'direction: rtl' achieves correct visual order. */
}

[dir="rtl"] .user-right-logical .user-info {
  /* DOM order: Name, Tag. (Now consistent with left side) With children's 'direction: rtl' achieves correct visual order. */
}

/* Ensure arrows are correct for RTL visual flow if they are part of the text flow */
/* However, since the main container is LTR, their visual position is fixed. */
/* If arrows need to flip based on language direction, they might need specific handling */
/* For now, their visual position is fixed by the LTR parent */


/* Dark theme adjustments */
[data-theme="dark"] .rate-section {
  background-color: rgba(24, 160, 88, 0.15);
  border-color: rgba(24, 160, 88, 0.3);
}

[data-theme="dark"] .rate-text {
  color: #fff;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .transaction-header-collapsed {
    padding: 8px 6px; /* Reduced from 10px 8px */
    gap: 6px; /* Reduced from 8px */
    min-height: 60px; /* Reduced from 65px */
  }
  
  .user-section {
    gap: 3px; /* Reduced from 6px */
    max-width: calc(50% - 45px); /* Reduced space for rate from 50px */
  }
  
  .user-info {
    gap: 4px !important; /* Reduced from 6px */
    flex-direction: column !important; /* Stack username and amount vertically */
    align-items: flex-start !important;
    min-height: 40px; /* Reduced from 45px */
  }
  
  .user-right-logical .user-info {
    align-items: flex-end !important; /* Right-align for right side */
  }
  
  .user-name {
    font-size: 0.9em;
    max-width: none !important; /* Remove width constraint */
    width: 100%;
  }
  
  .amount-tag {
    font-size: 0.7em !important;
    padding: 2px 4px !important; /* Reduced from 3px 6px */
    align-self: stretch; /* Take full width */
    text-align: center;
  }
    .rate-text {
    font-size: 0.75em;
  }
  
  .rate-section {
    padding: 4px 6px; /* Reduced from 6px 8px */
    min-width: 80px; /* Reduced from 90px */
    max-width: 120px; /* Increased from 90px to allow more space */
  }
  
  .arrow-icon {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .transaction-header-collapsed {
    padding: 10px 4px; /* Reduced from 12px 6px */
    gap: 3px; /* Reduced from 4px */
    min-height: 65px; /* Reduced from 70px */
  }
  
  .user-section {
    max-width: calc(50% - 40px); /* Reduced from 45px */
  }
  
  .user-info {
    gap: 6px !important; /* Reduced from 8px */
    min-height: 45px; /* Reduced from 50px */
  }
  
  .user-name {
    font-size: 0.85em;
  }
  
  .amount-tag {
    font-size: 0.65em !important;
    padding: 1px 3px !important; /* Reduced from 2px 4px */
  }    .rate-text {
    font-size: 0.7em;
  }
  
  .rate-section {
    padding: 3px 4px; /* Reduced from 4px 6px */
    min-width: 70px; /* Reduced from 80px */
    max-width: 100px; /* Increased from 80px to allow more space */
  }
  
  .arrow-icon {
    font-size: 12px;
  }
}

/* Very small screens - hide arrows for maximum space */
@media (max-width: 360px) {
  .transaction-header-collapsed {
    padding: 12px 3px; /* Reduced from 14px 4px */
    min-height: 70px; /* Reduced from 75px */
  }
  
  .user-section {
    gap: 0;
    max-width: calc(50% - 35px); /* Reduced from 40px */
  }
  
  .user-info {
    gap: 8px !important; /* Reduced from 10px */
    min-height: 50px; /* Reduced from 55px */
  }
  
  .arrow-icon {
    display: none; /* Hide arrows to save space */
  }
  
  .user-name {
    font-size: 0.8em;
  }
  
  .amount-tag {
    font-size: 0.6em !important;
  }    .rate-section {
    min-width: 65px; /* Reduced from 75px */
    max-width: 90px; /* Increased from 75px to allow more space */
  }
  
  .rate-text {
    font-size: 0.65em;
  }
}
</style>
