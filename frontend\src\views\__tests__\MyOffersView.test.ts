import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActiveP<PERSON> } from 'pinia';
import { ref } from 'vue';
import MyOffersView from '../../views/MyOffersView.vue';
import type { MyOffer, InterestRequestFrontend } from '@/types/offer';

// Mock Naive UI components
vi.mock('naive-ui', () => ({
  NPageHeader: {
    template: '<div class="n-page-header"><slot /></div>',
    props: ['title']
  },
  NDataTable: {
    template: '<div class="n-data-table" :class="{ loading }"><slot /></div>',
    props: ['columns', 'data', 'loading']
  },
  NCard: {
    template: '<div class="n-card"><div class="title">{{ title }}</div><slot /></div>',
    props: ['title']
  },
  NTag: {
    template: '<span class="n-tag" :class="type"><slot /></span>',
    props: ['type', 'size', 'round']
  },
  NDivider: {
    template: '<div class="n-divider"><slot /></div>'
  },
  NList: {
    template: '<div class="n-list"><slot name="header" /><slot /></div>',
    props: ['bordered']
  },
  NListItem: {
    template: '<div class="n-list-item"><slot /></div>'
  },
  NThing: {
    template: '<div class="n-thing"><div class="header"><slot name="header" /></div><div class="description"><slot name="description" /></div><div class="action"><slot name="action" /></div></div>'
  },
  NH4: {
    template: '<h4 class="n-h4"><slot /></h4>'
  },
  NButton: {
    template: '<button class="n-button" :class="[type, size]"><slot /></button>',
    props: ['type', 'size']
  },
  NSpace: {
    template: '<div class="n-space"><slot /></div>'
  },
  NModal: {
    template: '<div class="n-modal" v-if="show"><slot /></div>',
    props: ['show']
  },
  NSelect: {
    template: '<select class="n-select"><slot /></select>',
    props: ['value', 'options']
  }
}));

// Mock the router
const mockRouterPush = vi.fn();
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockRouterPush
  })
}));

// Mock the message service
const mockMessage = {
  success: vi.fn(),
  error: vi.fn(),
  info: vi.fn()
};
vi.mock('naive-ui', () => ({
  ...vi.importActual('naive-ui'),
  useMessage: () => mockMessage
}));

// Mock stores
const mockMyOffersStore = {
  myOffers: ref([]),
  loading: ref(false),
  error: ref(null),
  message: ref(null),
  fetchMyOffers: vi.fn(),
  acceptInterest: vi.fn(),
  declineInterest: vi.fn(),
  handleDeclineReasonSubmit: vi.fn(),
  showDeclineModal: ref(false),
  declineReasonCode: ref(undefined),
  interestToDecline: ref(null),
  openDeclineInterestModal: vi.fn()
};

vi.mock('@/stores/myOffersStore', () => ({
  useMyOffersStore: () => mockMyOffersStore
}));

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    user: { id: 'user1', reputationLevel: 3 }
  })
}));

describe('MyOffersView', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
    mockMyOffersStore.myOffers.value = [];
    mockMyOffersStore.loading.value = false;
    mockMyOffersStore.error.value = null;
  });

  const createMockOffer = (overrides: Partial<MyOffer> = {}): MyOffer => ({
    id: 'offer1',
    type: 'BUY',
    amount: 1000,
    baseRate: 1.35,
    adjustmentForLowerRep: 2.5,
    adjustmentForHigherRep: -1.5,
    status: 'ACTIVE',
    currencyPair: 'CAD/USD',
    createdAt: '2025-05-26T10:00:00Z',
    updatedAt: '2025-05-26T10:00:00Z',
    user: {
      username: 'testuser',
      reputationLevel: 3
    },
    interests: [],
    ...overrides
  });

  const createMockInterest = (overrides: Partial<InterestRequestFrontend> = {}): InterestRequestFrontend => ({
    id: 'interest1',
    offerId: 'offer1',
    interestedUserId: 'user2',
    username: 'interesteduser',
    reputationLevel: 4,
    status: 'PENDING',
    chatSessionId: null,
    reasonCode: null,
    createdAt: '2025-05-26T11:00:00Z',
    transactionStatus: null,
    negotiationStatus: null,
    ...overrides
  });

  describe('Overall Status Display in Table', () => {
    it('should show Active status for offers with no interests', () => {
      const offer = createMockOffer({
        interests: []
      });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      const table = wrapper.find('.n-data-table');
      expect(table.exists()).toBe(true);
      
      // The table should render the offer data
      expect(wrapper.vm.myOffersDisplayData).toHaveLength(1);
      expect(wrapper.vm.myOffersDisplayData[0].id).toBe('offer1');
    });

    it('should show Pending status for offers with pending interests', () => {
      const interest = createMockInterest({
        status: 'PENDING'
      });
      const offer = createMockOffer({
        interests: [interest]
      });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      // Check that the status helper is called correctly
      const overallStatus = wrapper.vm.getOfferOverallStatus(offer);
      expect(overallStatus).toEqual({
        text: 'Pending',
        type: 'info',
        icon: '⏳'
      });
    });

    it('should show Negotiating status for offers with negotiating transactions', () => {
      const interest = createMockInterest({
        status: 'ACCEPTED',
        transactionStatus: 'AWAITING_FIRST_PAYER_DESIGNATION',
        negotiationStatus: 'PENDING_RESPONSE'
      });
      const offer = createMockOffer({
        interests: [interest]
      });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      const overallStatus = wrapper.vm.getOfferOverallStatus(offer);
      expect(overallStatus).toEqual({
        text: 'Negotiating',
        type: 'warning',
        icon: '💬'
      });
    });

    it('should show In Progress status for offers with active payments', () => {
      const interest = createMockInterest({
        status: 'ACCEPTED',
        transactionStatus: 'AWAITING_FIRST_PAYER_PAYMENT',
        negotiationStatus: null
      });
      const offer = createMockOffer({
        interests: [interest]
      });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      const overallStatus = wrapper.vm.getOfferOverallStatus(offer);
      expect(overallStatus).toEqual({
        text: 'In Progress',
        type: 'warning',
        icon: '🔄'
      });
    });

    it('should show Complete status for offers with completed transactions', () => {
      const interest = createMockInterest({
        status: 'ACCEPTED',
        transactionStatus: 'COMPLETED',
        negotiationStatus: null
      });
      const offer = createMockOffer({
        interests: [interest]
      });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      const overallStatus = wrapper.vm.getOfferOverallStatus(offer);
      expect(overallStatus).toEqual({
        text: 'Complete',
        type: 'success',
        icon: '✅'
      });
    });

    it('should prioritize pending interests over accepted ones', () => {
      const pendingInterest = createMockInterest({
        id: 'interest1',
        status: 'PENDING'
      });
      const acceptedInterest = createMockInterest({
        id: 'interest2',
        status: 'ACCEPTED',
        transactionStatus: 'AWAITING_FIRST_PAYER_PAYMENT'
      });
      const offer = createMockOffer({
        interests: [acceptedInterest, pendingInterest]
      });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      const overallStatus = wrapper.vm.getOfferOverallStatus(offer);
      expect(overallStatus).toEqual({
        text: 'Pending',
        type: 'info',
        icon: '⏳'
      });
    });

    it('should show offer status when offer is not active', () => {
      const offer = createMockOffer({
        status: 'CANCELLED',
        interests: []
      });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      const overallStatus = wrapper.vm.getOfferOverallStatus(offer);
      expect(overallStatus).toEqual({
        text: 'Cancelled',
        type: 'default'
      });
    });
  });

  describe('Interest Cards Status Display', () => {
    it('should show correct status in interest cards', () => {
      const interest = createMockInterest({
        status: 'ACCEPTED',
        transactionStatus: 'AWAITING_FIRST_PAYER_DESIGNATION',
        negotiationStatus: 'PENDING_RESPONSE'
      });
      const offer = createMockOffer({
        interests: [interest]
      });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      const interestStatus = wrapper.vm.getInterestStatusInfo(interest);
      expect(interestStatus).toEqual({
        text: 'Negotiating',
        type: 'warning',
        icon: '💬'
      });
    });

    it('should handle null transaction/negotiation status', () => {
      const interest = createMockInterest({
        status: 'ACCEPTED',
        transactionStatus: null,
        negotiationStatus: null
      });
      const offer = createMockOffer({
        interests: [interest]
      });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      const interestStatus = wrapper.vm.getInterestStatusInfo(interest);
      expect(interestStatus).toEqual({
        text: 'Accepted',
        type: 'success'
      });
    });
  });

  describe('Component State Management', () => {
    it('should display loading state correctly', () => {
      mockMyOffersStore.loading.value = true;

      const wrapper = mount(MyOffersView);
      const table = wrapper.find('.n-data-table');
      
      expect(table.classes()).toContain('loading');
    });

    it('should handle empty offers list', () => {
      mockMyOffersStore.myOffers.value = [];

      const wrapper = mount(MyOffersView);
      
      expect(wrapper.vm.myOffersDisplayData).toHaveLength(0);
      expect(wrapper.vm.hasInterestsToShow).toBe(false);
    });

    it('should filter offers with interests correctly', () => {
      const offerWithInterests = createMockOffer({
        id: 'offer1',
        interests: [createMockInterest()]
      });
      const offerWithoutInterests = createMockOffer({
        id: 'offer2',
        interests: []
      });
      mockMyOffersStore.myOffers.value = [offerWithInterests, offerWithoutInterests];

      const wrapper = mount(MyOffersView);
      
      expect(wrapper.vm.offersWithInterestsToDisplay).toHaveLength(1);
      expect(wrapper.vm.offersWithInterestsToDisplay[0].id).toBe('offer1');
      expect(wrapper.vm.hasInterestsToShow).toBe(true);
    });
  });

  describe('Interest Actions', () => {
    it('should call acceptInterest when accept button is clicked', async () => {
      const interest = createMockInterest({ status: 'PENDING' });
      const offer = createMockOffer({ interests: [interest] });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      // Find and click accept button (this would require the actual component to be rendered)
      await wrapper.vm.acceptInterestRequest(interest.id);
      
      expect(mockMyOffersStore.acceptInterest).toHaveBeenCalledWith(interest.id);
    });

    it('should call openDeclineInterestModal when decline button is clicked', async () => {
      const interest = createMockInterest({ status: 'PENDING' });
      const offer = createMockOffer({ interests: [interest] });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      await wrapper.vm.openDeclineModal(interest);
      
      expect(mockMyOffersStore.openDeclineInterestModal).toHaveBeenCalledWith(interest);
    });
  });

  describe('Reactive Updates', () => {
    it('should update when store data changes', async () => {
      const wrapper = mount(MyOffersView);
      
      expect(wrapper.vm.myOffersDisplayData).toHaveLength(0);
      
      // Update store data
      const newOffer = createMockOffer();
      mockMyOffersStore.myOffers.value = [newOffer];
      
      await wrapper.vm.$nextTick();
      
      expect(wrapper.vm.myOffersDisplayData).toHaveLength(1);
    });

    it('should handle real-time status updates', async () => {
      const interest = createMockInterest({
        status: 'PENDING',
        transactionStatus: null,
        negotiationStatus: null
      });
      const offer = createMockOffer({ interests: [interest] });
      mockMyOffersStore.myOffers.value = [offer];

      const wrapper = mount(MyOffersView);
      
      let status = wrapper.vm.getInterestStatusInfo(interest);
      expect(status?.text).toBe('Pending');

      // Simulate real-time update
      interest.status = 'ACCEPTED';
      interest.transactionStatus = 'AWAITING_FIRST_PAYER_PAYMENT';
      
      await wrapper.vm.$nextTick();
      
      status = wrapper.vm.getInterestStatusInfo(interest);
      expect(status?.text).toBe('In Progress');
    });
  });
});
