<template>
  <div class="create-offer-simplified" :class="{ 'rtl': isRTL, 'ltr': !isRTL }" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="hero-icon-container">
          <n-icon size="56" color="white">
            <PlusCircleOutlined />
          </n-icon>
        </div>
        <h1 class="hero-title">{{ $t('createOffer.title') }}</h1>
        <p class="hero-subtitle">{{ $t('createOffer.subtitle') }}</p>
      </div>
    </section>

    <!-- Main Form Section -->
    <section class="form-section">
      <div class="form-container">
        
        <!-- Loading Skeleton -->
        <div v-if="isLoading" class="form-skeleton animate-fade-up">
          <div class="skeleton-card">
            <div class="skeleton-header"></div>
            <div class="skeleton-content">
              <div class="skeleton-field"></div>
              <div class="skeleton-field"></div>
              <div class="skeleton-field short"></div>
            </div>
            <div class="skeleton-footer"></div>
          </div>
        </div>

        <!-- Enhanced Form -->
        <div v-else class="offer-form-container animate-fade-up">
          <n-form 
            ref="formRef" 
            :model="formData" 
            :rules="formRules" 
            label-placement="top"
            class="enhanced-offer-form"
          >
            
            <!-- Step 1: Offer Type Selection -->
            <div class="form-step">
              <div class="step-header">
                <div class="step-indicator">
                  <span class="step-number">1</span>
                </div>
                <div class="step-content">
                  <h2 class="step-title">{{ $t('createOffer.offerType') }}</h2>
                  <p class="step-description">{{ $t('createOffer.chooseOfferType') }}</p>
                </div>
                <!-- Step completion check mark -->
                <div v-if="formData.type" class="step-completed">
                  <n-icon size="24" color="#18a058">
                    <CheckCircleOutlined />
                  </n-icon>
                </div>
              </div>

              <div class="step-body">
                <n-form-item path="type" class="offer-type-form-item">
                  <div class="offer-type-selection">
                    <!-- Sell Option -->
                    <div 
                      class="offer-type-card" 
                      :class="{ 'active': formData.type === 'SELL' }"
                      @click="selectOfferType('SELL')"
                    >
                      <div class="card-icon sell-icon">
                        <n-icon size="48">
                          <ArrowDownOutlined />
                        </n-icon>
                      </div>
                      <div class="card-content">
                        <h3 class="card-title">{{ $t('createOffer.sellCAD') }}</h3>
                        <p class="card-description">{{ $t('createOffer.sellDescription') }}</p>
                      </div>
                      <div class="card-check" v-if="formData.type === 'SELL'">
                        <n-icon size="24" color="#18a058">
                          <CheckCircleOutlined />
                        </n-icon>
                      </div>
                    </div>

                    <!-- Buy Option -->
                    <div 
                      class="offer-type-card" 
                      :class="{ 'active': formData.type === 'BUY' }"
                      @click="selectOfferType('BUY')"
                    >
                      <div class="card-icon buy-icon">
                        <n-icon size="48">
                          <ArrowUpOutlined />
                        </n-icon>
                      </div>
                      <div class="card-content">
                        <h3 class="card-title">{{ $t('createOffer.buyCAD') }}</h3>
                        <p class="card-description">{{ $t('createOffer.buyDescription') }}</p>
                      </div>
                      <div class="card-check" v-if="formData.type === 'BUY'">
                        <n-icon size="24" color="#18a058">
                          <CheckCircleOutlined />
                        </n-icon>
                      </div>
                    </div>
                  </div>
                </n-form-item>
                
                <!-- Next Step Indicator -->
                <div v-if="formData.type" class="next-step-indicator animate-fade-up">
                  <div class="next-step-content">
                    <n-icon size="20" color="#2080f0">
                      <ArrowDownOutlined />
                    </n-icon>
                    <span class="next-step-text">{{ $t('createOffer.nextStep') }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 2: Amount Configuration -->
            <div class="form-step" v-if="formData.type">
              <div class="step-header">
                <div class="step-indicator">
                  <span class="step-number">2</span>
                </div>
                <div class="step-content">
                  <h2 class="step-title">{{ $t('createOffer.amount') }}</h2>
                  <p class="step-description">{{ $t('createOffer.setAmount') }}</p>
                </div>
                <!-- Step completion check mark -->
                <div v-if="formData.amount && formData.amount > 0" class="step-completed">
                  <n-icon size="24" color="#18a058">
                    <CheckCircleOutlined />
                  </n-icon>
                </div>
              </div>

              <div class="step-body">
                <n-form-item path="amount" class="amount-form-item">
                  <div class="amount-input-container">
                    <n-input-number
                      v-model:value="formData.amount"
                      :min="1"
                      :max="50000"
                      :precision="0"
                      :step="100"
                      size="large"
                      class="amount-input"
                      :placeholder="$t('createOffer.amountPlaceholder')"
                      @update:value="handleAmountChange"
                    >
                      <template #prefix>
                        <span class="currency-symbol">CAD $</span>
                      </template>
                    </n-input-number>
                  </div>
                </n-form-item>

                <!-- Quick Amount Selection -->
                <div class="quick-amounts">
                  <h4 class="quick-amounts-title">{{ $t('createOffer.quickAmounts') }}</h4>
                  <div class="quick-amounts-grid">
                    <button 
                      v-for="amount in quickAmounts" 
                      :key="amount"
                      type="button"
                      class="quick-amount-btn"
                      :class="{ 'active': formData.amount === amount }"
                      @click="setQuickAmount(amount)"
                    >
                      ${{ formatAmount(amount) }}
                    </button>
                  </div>
                </div>
                
                <!-- Next Step Indicator -->
                <div v-if="formData.amount && formData.amount > 0" class="next-step-indicator animate-fade-up">
                  <div class="next-step-content">
                    <n-icon size="20" color="#2080f0">
                      <ArrowDownOutlined />
                    </n-icon>
                    <span class="next-step-text">{{ $t('createOffer.viewRatePreview') }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 3: Rate Information (Automated) -->
            <div class="form-step" v-if="formData.type && formData.amount">
              <div class="step-header">
                <div class="step-indicator">
                  <span class="step-number">3</span>
                </div>
                <div class="step-content">
                  <h2 class="step-title">{{ $t('createOfferSimplified.exchangeRate') }}</h2>
                  <p class="step-description">{{ $t('createOfferSimplified.rateDescription') }}</p>
                </div>
              </div>

              <div class="step-body">
                <!-- Rate Information Card -->
                <div class="rate-info-card">
                  <div class="rate-info-header">
                    <n-icon size="24" color="#2080f0">
                      <InfoCircleOutlined />
                    </n-icon>
                    <h3 class="rate-info-title">{{ $t('createOfferSimplified.automaticPricing') }}</h3>
                  </div>
                  
                  <div class="rate-info-content">
                    <div class="rate-source">
                      <n-icon size="20" color="#18a058">
                        <GlobalOutlined />
                      </n-icon>
                      <span class="rate-source-text">{{ $t('createOfferSimplified.rateSource') }}</span>
                    </div>
                    
                    <div class="rate-explanation">
                      <p>{{ $t('createOfferSimplified.rateExplanation') }}</p>
                    </div>

                    <!-- Custom RTL-Compatible Rate Preview -->
                    <div class="custom-rate-preview" v-if="estimatedRate">
                      <div class="custom-preview-header">
                        <span class="custom-preview-label">{{ $t('createOfferSimplified.estimatedValue') }}</span>
                      </div>
                      <div class="custom-preview-calculation">
                        <div class="custom-calc-row">
                          <div class="custom-calc-label">{{ $t('createOffer.amount') }}:</div>
                          <div class="custom-calc-value">{{ formatAmount(formData.amount) }} CAD</div>
                        </div>
                        <div class="custom-calc-row">
                          <div class="custom-calc-label">{{ $t('createOfferSimplified.estimatedRate') }}:</div>
                          <div class="custom-calc-value">~{{ formatRate(estimatedRate) }} IRR</div>
                        </div>
                        <div class="custom-calc-row custom-total-row">
                          <div class="custom-calc-label">{{ $t('createOfferSimplified.estimatedTotal') }}:</div>
                          <div class="custom-calc-value custom-total-value">{{ formatAmount(formData.amount * estimatedRate) }} IRR</div>
                        </div>
                      </div>
                      
                      <div class="custom-rate-disclaimer">
                        <div class="custom-disclaimer-icon">
                          <svg width="16" height="16" viewBox="0 0 1024 1024" fill="currentColor">
                            <path d="M464 720a48 48 0 1 0 96 0a48 48 0 1 0-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"/>
                          </svg>
                        </div>
                        <div class="custom-disclaimer-text">{{ $t('createOfferSimplified.rateDisclaimer') }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Submit Section -->
            <div class="submit-section" v-if="isFormValid">
              <div class="submit-container">
                <n-button 
                  type="primary" 
                  size="large" 
                  block
                  :loading="isSubmitting"
                  :disabled="!isFormValid"
                  @click="handleSubmit"
                  class="submit-button"
                >
                  <template #icon>
                    <n-icon><RocketOutlined /></n-icon>
                  </template>
                  {{ $t('createOffer.createOffer') }}
                </n-button>
                
                <div class="submit-helper">
                  <p class="helper-text">{{ $t('createOffer.submitHelper') }}</p>
                </div>
              </div>
            </div>
          </n-form>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useTranslation } from '@/composables/useTranslation'
import { useAuthStore } from '@/stores/auth'
import { useMessage } from 'naive-ui'
import { 
  NForm, 
  NFormItem, 
  NButton, 
  NIcon, 
  NInputNumber,
  type FormRules
} from 'naive-ui'
import { 
  PlusCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  GlobalOutlined,
  RocketOutlined
} from '@vicons/antd'
import { offerService } from '@/services/offerService'
import type { CreateOfferPayload } from '@/types/offer'

const router = useRouter()
const { isRTL } = useTranslation()
const authStore = useAuthStore()
const message = useMessage()

// Form ref
const formRef = ref()

// Loading states
const isLoading = ref(true)
const isSubmitting = ref(false)

// Form data
const formData = ref({
  type: '' as 'SELL' | 'BUY' | '',
  amount: null as number | null
})

// Quick amount options
const quickAmounts = [500, 1000, 2000, 5000, 10000]

// Estimated rate (this will come from external API later)
const estimatedRate = ref(57000) // Placeholder rate

// Form validation rules
const formRules: FormRules = {
  type: [
    {
      required: true,
      message: 'Please select offer type',
      trigger: 'change'
    }
  ],
  amount: [
    {
      required: true,
      validator: (_rule, value) => {
        if (!value || value <= 0) {
          return new Error('Please enter a valid amount greater than 0')
        }
        if (value > 50000) {
          return new Error('Amount cannot exceed $50,000 CAD')
        }
        return true
      },
      trigger: 'blur'
    }
  ]
}

// Computed properties
const isFormValid = computed(() => {
  return formData.value.type && 
         formData.value.amount && 
         formData.value.amount > 0 &&
         formData.value.amount <= 50000
})

// Methods
function selectOfferType(type: 'SELL' | 'BUY') {
  formData.value.type = type
  
  // Add haptic feedback animation
  nextTick(() => {
    const activeCard = document.querySelector('.offer-type-card.active')
    if (activeCard) {
      activeCard.classList.add('scale-feedback')
      setTimeout(() => {
        activeCard.classList.remove('scale-feedback')
      }, 200)
    }
    
    // Smooth scroll to next step after a brief delay
    setTimeout(() => {
      const nextStep = document.querySelector('.form-step:nth-child(2)')
      if (nextStep) {
        nextStep.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start',
          inline: 'nearest'
        })
        
        // Add attention-grabbing animation to the next step
        nextStep.classList.add('step-entrance-highlight')
        setTimeout(() => {
          nextStep.classList.remove('step-entrance-highlight')
        }, 2000)
      }
    }, 300)
  })
}

function setQuickAmount(amount: number) {
  formData.value.amount = amount
  
  // Add visual feedback
  nextTick(() => {
    const activeBtn = document.querySelector('.quick-amount-btn.active')
    if (activeBtn) {
      activeBtn.classList.add('pulse-feedback')
      setTimeout(() => {
        activeBtn.classList.remove('pulse-feedback')
      }, 300)
    }
    
    // Smooth scroll to next step (Step 3) after selecting amount
    setTimeout(() => {
      const nextStep = document.querySelector('.form-step:nth-child(3)')
      if (nextStep) {
        nextStep.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start',
          inline: 'nearest'
        })
        
        // Add attention-grabbing animation to the rate preview step
        nextStep.classList.add('step-entrance-highlight')
        setTimeout(() => {
          nextStep.classList.remove('step-entrance-highlight')
        }, 2000)
      }
    }, 300)
  })
}

function handleAmountChange(value: number | null) {
  // Trigger rate estimation when amount changes
  if (value && value > 0) {
    // In the future, this will trigger an API call to get real-time rates
    console.log(`Amount changed to: $${value} CAD`)
    
    // Auto-scroll to rate preview when amount is manually entered
    setTimeout(() => {
      const rateStep = document.querySelector('.form-step:nth-child(3)')
      if (rateStep && formData.value.type && formData.value.amount) {
        rateStep.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start',
          inline: 'nearest'
        })
        
        // Highlight the rate step
        rateStep.classList.add('step-entrance-highlight')
        setTimeout(() => {
          rateStep.classList.remove('step-entrance-highlight')
        }, 2000)
      }
    }, 500)
  }
}

function formatAmount(amount: number): string {
  return new Intl.NumberFormat('en-CA', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

function formatRate(rate: number): string {
  return new Intl.NumberFormat('fa-IR').format(rate)
}

async function handleSubmit() {
  if (!formRef.value || !isFormValid.value) return
  
  try {
    await formRef.value.validate()
    
    if (!authStore.user?.id) {
      message.error('Please log in to create an offer')
      return
    }

    isSubmitting.value = true

    // Create simplified payload
    const payload: CreateOfferPayload = {
      type: formData.value.type as 'SELL' | 'BUY',
      amount: formData.value.amount!,
      baseRate: estimatedRate.value, // Will be updated with real-time rate
      adjustmentForLowerRep: 0, // Simplified: no tiered pricing
      adjustmentForHigherRep: 0, // Simplified: no tiered pricing
      currencyPair: 'CAD-IRR',
      description: `${formData.value.type === 'SELL' ? 'Selling' : 'Buying'} ${formData.value.amount} CAD at market rate`
    }

    await offerService.createOffer(payload)
    
    // Success feedback
    message.success('🎉 Offer created successfully!')
    
    // Navigate to my offers
    router.push('/my-offers')
    
  } catch (error: any) {
    console.error('Error creating offer:', error)
    const errorMessage = error?.response?.data?.message || error?.message || 'Failed to create offer. Please try again.'
    message.error(errorMessage)
  } finally {
    isSubmitting.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Simulate initial loading
  setTimeout(() => {
    isLoading.value = false
  }, 800)
  
  // In the future, fetch real-time rates here
  // fetchCurrentRates()
})
</script>

<style scoped>
/* Mobile-first simplified create offer styles */
.create-offer-simplified {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
}

/* Hero Section */
.hero-section {
  padding: 2rem 1rem 1rem;
  text-align: center;
  color: white;
  position: relative;
  z-index: 1;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1" fill-rule="nonzero"><circle cx="30" cy="30" r="4"/></g></g></svg>');
  opacity: 0.2;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 400px;
  margin: 0 auto;
}

.hero-icon-container {
  margin-bottom: 1rem;
  opacity: 0;
  transform: scale(0);
  animation: scaleIn 0.6s ease 0.3s forwards;
}

.hero-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.75rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease 0.1s forwards;
}

.hero-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  line-height: 1.5;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease 0.2s forwards;
}

/* Form Section */
.form-section {
  padding: 1rem;
  margin-top: 1rem;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: calc(100vh - 300px);
}

.form-container {
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
}

/* Loading Skeleton */
.form-skeleton {
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.4s forwards;
}

.skeleton-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.skeleton-header {
  height: 32px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.08), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.skeleton-field {
  height: 56px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.08), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 12px;
}

.skeleton-field.short {
  width: 60%;
}

.skeleton-footer {
  height: 48px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.08), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 12px;
}

/* Enhanced Form */
.offer-form-container {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease 0.3s forwards;
}

.enhanced-offer-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Form Steps */
.form-step {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
  transition: all 0.3s ease;
}

.form-step:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
}

/* Step entrance highlighting animation */
.form-step.step-entrance-highlight {
  animation: stepEntranceHighlight 2s ease-out;
  border-color: #2080f0;
}

@keyframes stepEntranceHighlight {
  0% {
    border-color: #2080f0;
    box-shadow: 0 0 0 0 rgba(32, 128, 240, 0.4);
    transform: scale(1);
  }
  25% {
    border-color: #2080f0;
    box-shadow: 0 0 0 10px rgba(32, 128, 240, 0.2);
    transform: scale(1.02);
  }
  50% {
    border-color: #2080f0;
    box-shadow: 0 0 0 20px rgba(32, 128, 240, 0.1);
    transform: scale(1.01);
  }
  75% {
    border-color: #2080f0;
    box-shadow: 0 0 0 10px rgba(32, 128, 240, 0.05);
    transform: scale(1.005);
  }
  100% {
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transform: scale(1);
  }
}

/* Step Header */
.step-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #2080f0, #1668dc);
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(32, 128, 240, 0.3);
}

.step-number {
  color: white;
  font-weight: 700;
  font-size: 1.25rem;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #1e293b;
}

.step-description {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
}

/* Step completion indicator */
.step-completed {
  flex-shrink: 0;
  animation: scaleIn 0.4s ease;
}

/* Step Body */
.step-body {
  padding: 1.5rem;
}

/* Offer Type Selection */
.offer-type-form-item {
  margin-bottom: 0;
}

.offer-type-selection {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  justify-items: center;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 0 0.5rem;
}

.offer-type-card {
  position: relative;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  min-height: 120px;
  display: flex;
  align-items: center;
  gap: 1rem;
  touch-action: manipulation;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.offer-type-card:hover {
  border-color: #2080f0;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(32, 128, 240, 0.2);
}

.offer-type-card.active {
  border-color: #18a058;
  background: linear-gradient(135deg, rgba(24, 160, 88, 0.1), rgba(24, 160, 88, 0.05));
  transform: scale(1.02);
  box-shadow: 0 8px 24px rgba(24, 160, 88, 0.3);
}

.offer-type-card.scale-feedback {
  transform: scale(0.98) !important;
}

.card-icon {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.sell-icon {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
}

.buy-icon {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  color: #16a34a;
}

.card-content {
  flex: 1;
  padding-right: 3rem; /* Space for checkmark */
}

.rtl .card-content {
  padding-right: 0;
  padding-left: 3rem; /* Space for checkmark in RTL */
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1e293b;
}

.card-description {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
}

.card-check {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  animation: scaleIn 0.3s ease;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: 0.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Amount Input */
.amount-form-item {
  margin-bottom: 1.5rem;
}

.amount-input-container {
  width: 100%;
}

.amount-input {
  width: 100%;
  height: 56px;
  border-radius: 12px;
}

.currency-symbol {
  font-weight: 600;
  color: #18a058;
  font-size: 1.125rem;
}

/* Quick Amounts */
.quick-amounts {
  margin-top: 1rem;
}

.quick-amounts-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
}

.quick-amounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.75rem;
}

.quick-amount-btn {
  height: 48px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  color: #374151;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  touch-action: manipulation;
}

.quick-amount-btn:hover {
  border-color: #2080f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(32, 128, 240, 0.2);
}

.quick-amount-btn.active {
  border-color: #18a058;
  background: linear-gradient(135deg, #18a058, #16a34a);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(24, 160, 88, 0.3);
}

.quick-amount-btn.pulse-feedback {
  animation: pulse 0.3s ease;
}

/* Next Step Indicator */
.next-step-indicator {
  margin-top: 1.5rem;
  text-align: center;
  opacity: 0;
  animation: fadeInUp 0.5s ease 0.2s forwards;
}

.next-step-content {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, rgba(32, 128, 240, 0.1), rgba(32, 128, 240, 0.05));
  border: 1px solid rgba(32, 128, 240, 0.2);
  border-radius: 24px;
  color: #2080f0;
  font-weight: 500;
  font-size: 0.875rem;
  animation: glow 2s ease-in-out infinite alternate;
}

.next-step-text {
  font-weight: 600;
}

@keyframes glow {
  from {
    box-shadow: 0 2px 8px rgba(32, 128, 240, 0.2);
  }
  to {
    box-shadow: 0 4px 16px rgba(32, 128, 240, 0.4);
  }
}

/* Rate Info Card */
.rate-info-card {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border: 1px solid rgba(32, 128, 240, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  margin: 0;
}

.rate-info-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.rate-info-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.rate-info-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.rate-source {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(24, 160, 88, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(24, 160, 88, 0.2);
}

.rate-source-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #166534;
}

.rate-explanation {
  color: #475569;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.rate-explanation p {
  margin: 0;
}

/* Custom RTL-Compatible Rate Preview */
.custom-rate-preview {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.custom-preview-header {
  margin-bottom: 0.75rem;
}

.custom-preview-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.custom-preview-calculation {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

/* Custom calc row with proper RTL handling */
.custom-calc-row {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 1rem;
  align-items: center;
  font-size: 0.875rem;
  direction: ltr; /* Reset direction for proper number display */
}

/* RTL-specific layout for calc row */
.rtl .custom-calc-row {
  grid-template-columns: auto 1fr;
  direction: rtl;
}

.custom-calc-row.custom-total-row {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 0.5rem;
  margin-top: 0.5rem;
  font-size: 1rem;
}

.custom-calc-label {
  color: #64748b;
  font-weight: 500;
  text-align: left;
}

.rtl .custom-calc-label {
  text-align: right;
}

.custom-calc-value {
  font-weight: 600;
  color: #1e293b;
  text-align: right;
  direction: ltr; /* Keep numbers LTR */
}

.rtl .custom-calc-value {
  text-align: left;
  direction: ltr; /* Numbers stay LTR even in RTL context */
}

.custom-calc-value.custom-total-value {
  color: #18a058 !important;
  font-weight: 700;
  font-size: 1.125rem;
}

/* Custom disclaimer with proper RTL support */
.custom-rate-disclaimer {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #b45309;
  background: rgba(251, 191, 36, 0.1);
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid rgba(251, 191, 36, 0.2);
  direction: ltr;
}

.rtl .custom-rate-disclaimer {
  direction: rtl;
}

.custom-disclaimer-icon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  color: #f0a020;
}

.custom-disclaimer-text {
  line-height: 1.4;
  flex: 1;
}

/* Submit Section */
.submit-section {
  margin-top: 1rem;
}

.submit-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
  text-align: center;
}

.submit-button {
  height: 56px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1.125rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(32, 128, 240, 0.3);
  transition: all 0.3s ease;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(32, 128, 240, 0.4);
}

.submit-button:active {
  transform: scale(0.98);
}

.submit-helper {
  margin-top: 1rem;
}

.helper-text {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
  margin: 0;
}

/* Dark theme support */
[data-theme="dark"] .form-step {
  background: rgba(26, 27, 46, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .step-header {
  background: linear-gradient(135deg, #1e293b, #334155);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .step-title {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .step-description {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .offer-type-card {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .card-title {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .card-description {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .quick-amounts-title {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .quick-amount-btn {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .rate-info-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(51, 65, 85, 0.8));
  border-color: rgba(32, 128, 240, 0.3);
}

[data-theme="dark"] .rate-info-title {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .rate-explanation {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .submit-container {
  background: rgba(26, 27, 46, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .helper-text {
  color: rgba(255, 255, 255, 0.7);
}

/* Dark theme support for custom rate preview */
[data-theme="dark"] .custom-rate-preview {
  background: rgba(26, 27, 46, 0.8);
  border-color: rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .custom-preview-label {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .custom-calc-label {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .custom-calc-value {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .custom-rate-disclaimer {
  background: rgba(251, 191, 36, 0.15);
  border-color: rgba(251, 191, 36, 0.3);
  color: #f59e0b;
}

/* Dark theme support for new elements */
[data-theme="dark"] .next-step-content {
  background: linear-gradient(135deg, rgba(32, 128, 240, 0.2), rgba(32, 128, 240, 0.1));
  border-color: rgba(32, 128, 240, 0.4);
  color: #60a5fa;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Extra small screens - ensure no overflow */
@media (max-width: 480px) {
  .step-body {
    padding: 0.75rem;
  }
  
  .offer-type-selection {
    padding: 0;
    gap: 0.75rem;
  }
  
  .offer-type-card {
    padding: 1rem;
    min-height: 100px;
  }
  
  .card-icon {
    width: 48px;
    height: 48px;
  }
  
  .card-title {
    font-size: 1rem;
  }
  
  .card-description {
    font-size: 0.8rem;
  }
}

/* Mobile-specific styles */
@media (max-width: 767px) {
  .step-body {
    padding: 1rem;
  }
  
  .offer-type-selection {
    justify-items: center;
    padding: 0;
    align-items: center;
    gap: 1rem;
  }
  
  .offer-type-card {
    max-width: 100%;
    width: 100%;
    justify-self: center;
    padding: 1.25rem;
  }
  
  /* Ensure proper centering and alignment on mobile for both directions */
  .offer-type-card .card-content {
    flex: 1;
    min-width: 0;
  }
  
  /* Mobile RTL specific */
  .rtl .offer-type-card {
    text-align: right;
  }
  
  .rtl .offer-type-card .card-content {
    text-align: right !important;
    padding-left: 3rem !important;
    padding-right: 0 !important;
  }
  
  .rtl .offer-type-card .card-title {
    text-align: right !important;
  }
  
  .rtl .offer-type-card .card-description {
    text-align: right !important;
  }
  
  /* Mobile LTR specific */
  .ltr .offer-type-card {
    text-align: left;
  }
  
  .ltr .offer-type-card .card-content {
    text-align: left !important;
    padding-right: 3rem !important;
    padding-left: 0 !important;
  }
  
  .ltr .offer-type-card .card-title {
    text-align: left !important;
  }
  
  .ltr .offer-type-card .card-description {
    text-align: left !important;
  }
}

/* Tablet enhancements */
@media (min-width: 768px) {
  .create-offer-simplified {
    padding: 0;
  }
  
  .hero-section {
    padding: 3rem 2rem 2rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
  
  .form-section {
    padding: 2rem;
  }
  
  .enhanced-offer-form {
    gap: 2.5rem;
  }
  
  .offer-type-selection {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    justify-items: center;
    padding: 0 1rem;
    max-width: 100%;
    margin: 0 auto;
  }
  
  .offer-type-card {
    max-width: 280px;
    width: 100%;
    min-width: 250px;
  }
  
  .step-header {
    padding: 2rem;
  }
  
  .step-body {
    padding: 2rem;
  }
  
  .submit-container {
    padding: 2rem;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  .hero-section {
    padding: 4rem 2rem 3rem;
  }
  
  .hero-title {
    font-size: 3rem;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .form-section {
    padding: 3rem 2rem;
  }
  
  .form-container {
    max-width: 700px;
  }
  
  .enhanced-offer-form {
    gap: 3rem;
  }
  
  .offer-type-selection {
    gap: 2.5rem;
    max-width: 680px;
    margin: 0 auto;
  }
  
  .offer-type-card {
    max-width: 340px;
    min-width: 300px;
  }
  
  .form-step:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
  }
}

/* Large desktop optimization */
@media (min-width: 1440px) {
  .offer-type-selection {
    gap: 3rem;
    max-width: 800px;
  }
  
  .offer-type-card {
    max-width: 380px;
    min-width: 320px;
  }
}

/* RTL Support */
.rtl .step-header {
  flex-direction: row-reverse;
}

.rtl .rate-info-header {
  flex-direction: row-reverse;
}

.rtl .rate-source {
  flex-direction: row-reverse;
}

.rtl .rate-disclaimer {
  flex-direction: row-reverse;
}

.rtl .card-check {
  left: 0.75rem;
  right: auto;
}

/* RTL text alignment adjustments */
.rtl .hero-content,
.rtl .step-content,
.rtl .card-content,
.rtl .rate-info-content,
.rtl .submit-container {
  text-align: right;
}

.rtl .step-title,
.rtl .card-title,
.rtl .rate-info-title {
  text-align: right;
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .hero-icon-container,
  .hero-title,
  .hero-subtitle,
  .form-skeleton,
  .offer-form-container,
  .form-step,
  .card-check,
  .step-completed,
  .next-step-indicator,
  .next-step-content {
    animation: none !important;
    transition: none !important;
  }
  
  .form-step:hover,
  .offer-type-card:hover,
  .submit-button:hover {
    transform: none !important;
  }
  
  /* Disable step highlighting for reduced motion users */
  .form-step.step-entrance-highlight {
    animation: none !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
  }
}
</style>
