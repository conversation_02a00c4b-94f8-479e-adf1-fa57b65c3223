import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import type { Mock } from 'vitest'; // Import Mock type
import { mount, flushPromises, VueWrapper, DOMWrapper } from '@vue/test-utils';
import { createP<PERSON>, setActivePinia } from 'pinia';
import type { Pinia } from 'pinia';
import { nextTick } from 'vue';
import RegisterView from '../RegisterView.vue';
import axios from 'axios'; // Import the original axios
import type { AxiosError } from 'axios'; // Import AxiosError type

// --- Mocks ---
const mockRouterPush = vi.fn();
vi.mock('vue-router', () => ({
  useRouter: vi.fn(() => ({
    push: mockRouterPush, // Use the specific mock function
  })),
  RouterLink: {
    props: ['to'],
    template: '<a :href="to"><slot></slot></a>',
  }
}));

// Mock naive-ui's useMessage
const mockMessageSuccess = vi.fn();
vi.mock('naive-ui', async (importOriginal) => {
  const naiveOriginal = await importOriginal<typeof import('naive-ui')>();
  return {
    ...naiveOriginal, // Keep original exports
    useMessage: vi.fn(() => ({
      success: mockMessageSuccess, // Mock the success method
      // Mock other methods like error, warning, info if needed
    })),
  };
});

// Mock axios globally using explicit factory
vi.mock('axios', async (importOriginal) => {
  const actualAxios = await importOriginal<typeof axios>();
  return {
    __esModule: true, // Indicate it's an ES module
    // Mock the default export which is what `import axios from 'axios'` gets
    default: {
      post: vi.fn(),
      isAxiosError: vi.fn(actualAxios.isAxiosError),
      // Add other methods used on the default export if necessary (e.g., get, put)
    },
    // Keep named exports if they are *also* imported/used elsewhere.
    isAxiosError: vi.fn(actualAxios.isAxiosError),
    AxiosError: actualAxios.AxiosError,
    // Add other exports if needed (create, defaults, etc.)
  };
});

// --- End Mocks ---

describe('RegisterView.vue', () => {
  let pinia: Pinia;
  let wrapper: VueWrapper<any>;

  // Variables to hold the specific mock functions
  // These should be assigned from the mocked `axios` object in beforeEach
  let mockedAxiosPost: Mock;
  let mockedAxiosIsAxiosError: Mock;

  // Mount helper
  const mountComponent = (validatePromise?: Promise<void>) => {
    return mount(RegisterView, {
      global: {
        plugins: [pinia],
        stubs: {
          // Simple wrapper stubs for layout
          NCard: true,
          // --- Enhanced NForm Stub ---
          NForm: {
            // Expose a mock 'validate' method that resolves successfully
            setup() {
              // Use the provided promise or default to resolved
              const validate = vi.fn(() => validatePromise ?? Promise.resolve());
              return { validate }; // Expose it for potential direct calls if needed
            },
            emits: ['submit'], // Declare the submit event
            // Template still emits submit, but now the component instance has 'validate'
            template: '<form @submit.prevent="$emit(\'submit\', $event)"><slot /></form>'
          },
          // --- End Enhanced NForm Stub ---
          // Make NFormItem render its slot so NInput can be found inside
          NFormItem: {
            props: ['label'], // Keep label prop if needed for findFormItemByLabel
            template: '<div><slot /></div>'
          },
          // Controlled input stub to support v-model:value
          NInput: {
            props: ['value', 'type'],
            emits: ['update:value', 'input'],
            template: '<input :type="type || \'text\'" :value="value" @input="$emit(\'update:value\', $event.target.value)" />'
          },
          // --- Updated NCheckbox Stub ---
          NCheckbox: {
            props: ['checked'], // Matches v-model:checked
            emits: ['update:checked'], // Matches v-model:checked
            // Template renders a clickable div and includes the slot content
            template: `
              <div class="n-checkbox stubbed-checkbox"
                   role="checkbox"
                   :aria-checked="checked"
                   @click="handleClick">
                <!-- Basic structure to somewhat resemble the original -->
                <div class="n-checkbox-box-wrapper">
                  <div class="n-checkbox-box"></div>
                </div>
                <span class="n-checkbox__label"><slot></slot></span>
              </div>
            `,
            methods: {
              handleClick() {
                // Emit the update event with the toggled value
                this.$emit('update:checked', !this.checked);
              }
            }
          },
          // --- End Updated NCheckbox Stub ---
          NButton: {
            props: ['disabled', 'loading'],
            template: '<button :disabled="disabled || loading"><slot /></button>' // Ensure loading also disables
          },
          NAlert: { props: ['title', 'type'], template: '<div><slot /></div>' },
          RouterLink: { props: ['to'], template: '<a :href="to"><slot /></a>' }
        }
      }
    });
  };


  // Use async here because we use await inside
  beforeEach(async () => {
    // Reset mocks before each test
    vi.resetAllMocks();
    // Use fake timers for setTimeout
    vi.useFakeTimers();

    // Access the mocked functions directly on the imported axios object.
    // The `axios` variable here refers to the `default` export provided by the mock factory.
    mockedAxiosPost = axios.post as Mock;
    mockedAxiosIsAxiosError = (axios.isAxiosError as unknown) as Mock;

    // --- Sanity check mocks --- >
    if (typeof mockedAxiosPost?.mockResolvedValue !== 'function') {
        console.error("mockedAxiosPost is not a Vitest mock function:", mockedAxiosPost);
        throw new Error("Axios post mock setup failed in beforeEach");
    }
     if (typeof mockedAxiosIsAxiosError?.mockImplementation !== 'function') {
        console.error("mockedAxiosIsAxiosError is not a Vitest mock function:", mockedAxiosIsAxiosError);
        // Check the named export as a fallback (less likely needed based on component code)
        const namedMock = (axios as any).constructor.isAxiosError as Mock; // Accessing named export differently
        if (typeof namedMock?.mockImplementation === 'function') {
            console.warn("Using named export mock for isAxiosError as fallback");
            mockedAxiosIsAxiosError = namedMock;
        } else {
            throw new Error("Axios isAxiosError mock setup failed in beforeEach");
        }
    }
    // <--- Sanity check mocks ---

    pinia = createPinia();
    setActivePinia(pinia);

    // Provide default implementations for the mocks
    mockedAxiosPost.mockResolvedValue({ data: { message: 'Default success message' } });

    const actualAxios = await vi.importActual<typeof axios>('axios');
    if (typeof actualAxios.isAxiosError === 'function') {
       mockedAxiosIsAxiosError.mockImplementation(actualAxios.isAxiosError);
    } else {
       mockedAxiosIsAxiosError.mockImplementation(() => false);
       console.warn("Could not get actual implementation for axios.isAxiosError, using fallback.");
    }

    wrapper = mountComponent();
  });

  afterEach(() => {
    // Restore real timers
    vi.useRealTimers();
    if (wrapper) {
      wrapper.unmount();
    }  });
  // --- Test Helpers ---
  const findFormItemByLabel = (w: VueWrapper<any>, labelText: string): VueWrapper<any> | undefined => {
    // Find form items by their actual DOM structure
    const formItems = w.findAll('.n-form-item');
    return formItems.find(item => item.text().includes(labelText));
  };

  const findInputInFormItem = (w: VueWrapper<any>, labelText: string): DOMWrapper<HTMLInputElement> | undefined => {
    const formItem = findFormItemByLabel(w, labelText);
    if (!formItem) {
      console.error(`findInputInFormItem: Could not find form item with label "${labelText}".`);
      return undefined;
    }

    const inputElement = formItem.find<HTMLInputElement>('input');
    if (!inputElement.exists()) {
      console.error(`findInputInFormItem: Could not find input element.`);
      return undefined;
    }

    return inputElement;
  };
  
  const findCheckboxInput = (w: VueWrapper<any>): DOMWrapper<HTMLElement> | undefined => {
    // Find checkbox by its DOM class directly  
    const checkboxDiv = w.find<HTMLElement>('div.n-checkbox');
    if (!checkboxDiv.exists()) {
      console.error('findCheckboxInput: Failed to find checkbox element (div.n-checkbox).');
      return undefined;
    }
    return checkboxDiv;
  };
  // --- End Test Helpers ---  it('renders the registration form correctly', () => {
    expect(wrapper.findComponent({ name: 'NCard' }).exists()).toBe(true);
    expect(wrapper.findComponent({ name: 'NForm' }).exists()).toBe(true);
    expect(findFormItemByLabel(wrapper, 'Email')?.exists()).toBe(true);
    expect(findFormItemByLabel(wrapper, 'Password')?.exists()).toBe(true);
    // Use the new helper to check for the checkbox
    expect(findCheckboxInput(wrapper)?.exists()).toBe(true);
    expect(wrapper.find('a[href="/terms"]').exists()).toBe(true);
    expect(wrapper.find('a[href="/privacy"]').exists()).toBe(true);
    // Check button is initially disabled
    const submitButton = wrapper.findComponent({ name: 'NButton' });
    expect(submitButton.props('disabled')).toBe(true);
  });

  it('enables submit button when terms are accepted', async () => {    // Use the new helper (now finds the div)
    const checkboxElement = findCheckboxInput(wrapper); // Renamed for clarity
    const submitButton = wrapper.findComponent({ name: 'NButton' });
    // Assert the element exists
    expect(checkboxElement?.exists()).toBe(true);
    if (!checkboxElement) throw new Error('Checkbox element (div) not found');

    expect(submitButton.props('disabled')).toBe(true);
    // Use trigger('click') instead of setChecked
    await checkboxElement.trigger('click');
    await nextTick();
    // Add a small delay or another nextTick if needed for reactivity with Naive UI components
    await nextTick();
    expect(submitButton.props('disabled')).toBe(false);

    // Optional: Test unchecking
    await checkboxElement.trigger('click'); // Click again to uncheck
    await nextTick();
    await nextTick();
    expect(submitButton.props('disabled')).toBe(true);
  });
  it('allows typing into email and password fields', async () => {
    const emailInput = findInputInFormItem(wrapper, 'Email');
    const passwordInput = findInputInFormItem(wrapper, 'Password');
    expect(emailInput?.exists()).toBe(true);
    expect(passwordInput?.exists()).toBe(true);
    if (!emailInput || !passwordInput) throw new Error('Inputs not found');
    await emailInput.setValue('<EMAIL>');
    await passwordInput.setValue('password123');
    expect(emailInput.element.value).toBe('<EMAIL>');
    expect(passwordInput.element.value).toBe('password123');
  });  it('calls registration API, shows message, and redirects on successful submission with valid data and accepted terms', async () => {
    const successMsg = 'Registration successful! Please check your email.';
    mockedAxiosPost.mockResolvedValue({ data: { message: successMsg } });

    const emailInput = findInputInFormItem(wrapper, 'Email');
    const passwordInput = findInputInFormItem(wrapper, 'Password');
    // Use the new helper (finds the div)
    const checkboxElement = findCheckboxInput(wrapper);
    const form = wrapper.findComponent({ name: 'NForm' });

    if (!emailInput || !passwordInput || !checkboxElement) throw new Error('Inputs or checkbox element not found');

    await emailInput.setValue('<EMAIL>');
    await passwordInput.setValue('password123');
    // Use trigger click on the element
    await checkboxElement.trigger('click');
    await nextTick(); // Allow v-model update and button state to update

    await form.trigger('submit.prevent');
    await flushPromises(); // Wait for API call

    // Verify API call
    expect(mockedAxiosPost).toHaveBeenCalledTimes(1);
    expect(mockedAxiosPost).toHaveBeenCalledWith('/api/auth/register', {
      email: '<EMAIL>',
      password: 'password123'
    });

    // Verify success message call
    expect(mockMessageSuccess).toHaveBeenCalledTimes(1);
    expect(mockMessageSuccess).toHaveBeenCalledWith('Registration successful! Redirecting to login...');

    // Verify redirect is NOT called immediately
    expect(mockRouterPush).not.toHaveBeenCalled();

    // Advance timers past the setTimeout delay
    vi.advanceTimersByTime(1500);
    await flushPromises(); // Allow any promises after timeout to resolve

    // Verify redirect call after timeout
    expect(mockRouterPush).toHaveBeenCalledTimes(1);
    expect(mockRouterPush).toHaveBeenCalledWith('/login');

    // REMOVED: Assertion for clearing form fields
    // expect(emailInput.element.value).toBe('');
    // expect(passwordInput.element.value).toBe('');
  });  it('displays an API error message on failed registration', async () => {
    const apiErrorMessage = 'Email already exists';
    
    // Create an AxiosError with the proper error response structure
    const mockResponse = {
      data: { message: apiErrorMessage }, // Use message instead of error
      status: 409,
      statusText: 'Conflict',
      headers: {},
      config: {} as any
    };
    
    const apiError = {
      isAxiosError: true,
      response: mockResponse,
      message: 'Request failed with status code 409',
      name: 'AxiosError',
      code: '409'
    } as AxiosError;    // Mock axios.post to reject with our error
    mockedAxiosPost.mockRejectedValue(apiError);
    // Mock isAxiosError to identify our error as an Axios error
    mockedAxiosIsAxiosError.mockReturnValue(true);

    const emailInput = findInputInFormItem(wrapper, 'Email');
    const passwordInput = findInputInFormItem(wrapper, 'Password');
    // Use the new helper
    const checkboxElement = findCheckboxInput(wrapper);
    const form = wrapper.findComponent({ name: 'NForm' });

    if (!emailInput || !passwordInput || !checkboxElement) throw new Error('Inputs or checkbox element not found');

    await emailInput.setValue('<EMAIL>');
    await passwordInput.setValue('password123');
    // Use trigger click
    await checkboxElement.trigger('click');
    await nextTick();

    await form.trigger('submit.prevent');
    await flushPromises();

    expect(mockedAxiosPost).toHaveBeenCalledTimes(1);
    expect(mockedAxiosIsAxiosError).toHaveBeenCalled(); // Check that isAxiosError was called

    await nextTick();
    expect(wrapper.vm.error).toBe(apiErrorMessage);
    const errorAlert = wrapper.findComponent({ name: 'NAlert' });
    expect(errorAlert.exists()).toBe(true);
    expect(errorAlert.props('type')).toBe('error'); // Check alert type
    expect(mockMessageSuccess).not.toHaveBeenCalled();
    expect(mockRouterPush).not.toHaveBeenCalled();
  });

  it('displays a generic error message for non-API errors during registration', async () => {
    const genericErrorMessage = 'An unexpected error occurred. Please try again.';
    const genericError = new Error('Network Error');
    // Mock axios.post to reject
    mockedAxiosPost.mockRejectedValue(genericError);    // Ensure isAxiosError returns false for this error
    mockedAxiosIsAxiosError.mockImplementation((_payload: any): _payload is AxiosError => false);

    const emailInput = findInputInFormItem(wrapper, 'Email');
    const passwordInput = findInputInFormItem(wrapper, 'Password');
    // Use the new helper
    const checkboxElement = findCheckboxInput(wrapper);
    const form = wrapper.findComponent({ name: 'NForm' });

    if (!emailInput || !passwordInput || !checkboxElement) throw new Error('Inputs or checkbox element not found');

    await emailInput.setValue('<EMAIL>');
    await passwordInput.setValue('password123');
    // Use trigger click
    await checkboxElement.trigger('click');
    await nextTick();

    await form.trigger('submit.prevent');
    await flushPromises();

    expect(mockedAxiosPost).toHaveBeenCalledTimes(1);
    expect(mockedAxiosIsAxiosError).toHaveBeenCalled(); // Check that isAxiosError was called

    await nextTick();
    expect(wrapper.vm.error).toBe(genericErrorMessage);
    const errorAlert = wrapper.findComponent({ name: 'NAlert' });
    expect(errorAlert.exists()).toBe(true);
    expect(errorAlert.props('type')).toBe('error'); // Check alert type
    expect(mockMessageSuccess).not.toHaveBeenCalled();
    expect(mockRouterPush).not.toHaveBeenCalled();
  });


  // --- Validation Tests --- //
  it('prevents API call if terms are not accepted', async () => {
    const emailInput = findInputInFormItem(wrapper, 'Email');
    const passwordInput = findInputInFormItem(wrapper, 'Password');
    // Use the new helper
    const checkboxElement = findCheckboxInput(wrapper);
    const form = wrapper.findComponent({ name: 'NForm' });
    const submitButton = wrapper.findComponent({ name: 'NButton' });

    if (!emailInput || !passwordInput || !checkboxElement) throw new Error('Inputs or checkbox element not found');

    await emailInput.setValue('<EMAIL>');
    await passwordInput.setValue('password123');
    // Ensure terms are NOT accepted (initial state is false, so no click needed)
    // await checkboxElement.trigger('click'); // NO CLICK HERE
    await nextTick();

    // Attempt submission
    expect(submitButton.props('disabled')).toBe(true); // Button should be disabled
    // Trigger form validation directly if needed, or just check button state
    // await form.trigger('submit.prevent').catch(() => {}); // Trigger validation

    expect(mockedAxiosPost).not.toHaveBeenCalled();
    expect(mockMessageSuccess).not.toHaveBeenCalled();
    expect(mockRouterPush).not.toHaveBeenCalled();
  });
  it('prevents API call and shows validation error if email is missing', async () => {
    const passwordInput = findInputInFormItem(wrapper, 'Password');
    // Use the new helper
    const checkboxElement = findCheckboxInput(wrapper);
    const form = wrapper.findComponent({ name: 'NForm' });
    if (!passwordInput || !checkboxElement) throw new Error('Input or checkbox element not found');

    await passwordInput.setValue('password123');
    // Use trigger click to accept terms
    await checkboxElement.trigger('click');
    await nextTick();

    await form.trigger('submit.prevent').catch(() => {}); // Catch expected validation rejection
    await flushPromises();
    expect(mockedAxiosPost).not.toHaveBeenCalled();
    // Add assertion for validation message if desired
  });

  it('prevents API call and shows validation error if email is invalid', async () => {
    const emailInput = findInputInFormItem(findFormItemByLabel(wrapper, 'Email'));
    const passwordInput = findInputInFormItem(findFormItemByLabel(wrapper, 'Password'));
    // Use the new helper
    const checkboxElement = findCheckboxInput(wrapper);
    const form = wrapper.findComponent({ name: 'NForm' });
    if (!emailInput || !passwordInput || !checkboxElement) throw new Error('Inputs or checkbox element not found');

    await emailInput.setValue('not-an-email');
    await passwordInput.setValue('password123');
    // Use trigger click to accept terms
    await checkboxElement.trigger('click');
    await nextTick();

    await form.trigger('submit.prevent').catch(() => {}); // Catch expected validation rejection
    await flushPromises();
    expect(mockedAxiosPost).not.toHaveBeenCalled();
    // Add assertion for validation message if desired
  });

  it('prevents API call and shows validation error if password is too short', async () => {
    const emailInput = findInputInFormItem(findFormItemByLabel(wrapper, 'Email'));
    const passwordInput = findInputInFormItem(findFormItemByLabel(wrapper, 'Password'));
    // Use the new helper
    const checkboxElement = findCheckboxInput(wrapper);
    const form = wrapper.findComponent({ name: 'NForm' });
    if (!emailInput || !passwordInput || !checkboxElement) throw new Error('Inputs or checkbox element not found');

    await emailInput.setValue('<EMAIL>');
    await passwordInput.setValue('short');
    // Use trigger click to accept terms
    await checkboxElement.trigger('click');
    await nextTick();

    await form.trigger('submit.prevent').catch(() => {}); // Catch expected validation rejection
    await flushPromises();
    expect(mockedAxiosPost).not.toHaveBeenCalled();
    // Add assertion for validation message if desired
  });

  // --- End Validation Tests --- //


  it('displays loading state during registration', async () => {
    let resolveValidate: () => void;
    const validatePromise = new Promise<void>(resolve => {
      resolveValidate = resolve;
    });

    // Mock the API call to also return a controllable promise
    let resolveApiCall: (value: any) => void;
    const apiPromise = new Promise(resolve => {
        resolveApiCall = resolve;
    });
    // Ensure you are mocking the default export's post method
    (axios.post as Mock).mockReturnValue(apiPromise);

    // Remount with controlled validate promise
    wrapper.unmount();
    wrapper = mountComponent(validatePromise);

    const emailInput = findInputInFormItem(findFormItemByLabel(wrapper, 'Email'));
    const passwordInput = findInputInFormItem(findFormItemByLabel(wrapper, 'Password'));
    const checkboxElement = findCheckboxInput(wrapper);
    const form = wrapper.findComponent({ name: 'NForm' });
    const submitButton = wrapper.findComponent({ name: 'NButton' });

    if (!emailInput || !passwordInput || !checkboxElement) throw new Error('Inputs or checkbox element not found');

    await emailInput.setValue('<EMAIL>');
    await passwordInput.setValue('password123');
    await checkboxElement.trigger('click');
    await nextTick();

    // Trigger submit but don't resolve validate yet
    form.trigger('submit.prevent');
    await nextTick(); // Allow event loop to process trigger

    // Assert loading is FALSE before validation resolves
    expect(wrapper.vm.loading).toBe(false);
    expect(submitButton.props('loading')).toBe(false);

    // Now resolve validate
    resolveValidate!();
    await flushPromises(); // Allow the .then() block of validate() and subsequent microtasks to execute

    // Assert loading is TRUE after validation resolves and before API completes
    expect(wrapper.vm.loading).toBe(true); // Check if this passes now
    expect(submitButton.props('loading')).toBe(true);

    // Now resolve the API call
    resolveApiCall!({ data: { message: 'Success from test' } });
    await flushPromises(); // Allow API promise and subsequent .then/.finally to resolve

    // Check final state (loading should be false after API completes and finally block runs)
    expect(wrapper.vm.loading).toBe(false);
    expect(submitButton.props('loading')).toBe(false);

    // Check for redirect logic if needed (using timers)
    vi.advanceTimersByTime(1500);
    await flushPromises();
    expect(mockRouterPush).toHaveBeenCalledWith('/login');
  });
  it('displays fallback error for Axios error without specific message', async () => {
    const fallbackApiErrorMessage = 'Registration failed. Please try again.';
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {}); // Spy on console.error

    // Create an AxiosError-like object without response.data.message
    const mockResponse = {
      data: {}, // No message here
      status: 500,
      statusText: 'Internal Server Error',
      headers: {},
      config: {} as any
    };
    const apiError = {
      isAxiosError: true,
      response: mockResponse,
      message: 'Request failed with status code 500',
      name: 'AxiosError',
      code: '500'
    } as AxiosError;

    mockedAxiosPost.mockRejectedValue(apiError);
    mockedAxiosIsAxiosError.mockReturnValue(true); // Identify as Axios error

    const emailInput = findInputInFormItem(findFormItemByLabel(wrapper, 'Email'));
    const passwordInput = findInputInFormItem(findFormItemByLabel(wrapper, 'Password'));
    // Use the new helper
    const checkboxElement = findCheckboxInput(wrapper);
    const form = wrapper.findComponent({ name: 'NForm' });

    if (!emailInput || !passwordInput || !checkboxElement) throw new Error('Inputs or checkbox element not found');

    await emailInput.setValue('<EMAIL>');
    await passwordInput.setValue('password123');
    // Use trigger click to accept terms
    await checkboxElement.trigger('click');
    await nextTick();

    await form.trigger('submit.prevent');
    await flushPromises();
    await nextTick();

    // Check that the API was called (it should be, even if it fails)
    expect(mockedAxiosPost).toHaveBeenCalledTimes(1);

    // Check that the fallback error message is displayed
    expect(wrapper.vm.error).toBe(fallbackApiErrorMessage);
    const errorAlert = wrapper.findComponent({ name: 'NAlert' });
    expect(errorAlert.exists()).toBe(true);
    expect(errorAlert.props('type')).toBe('error');
    expect(consoleErrorSpy).toHaveBeenCalled(); // Verify console error was logged

    consoleErrorSpy.mockRestore(); // Restore console.error
  });
  it('debug: component structure', () => {
    console.log('Component HTML:', wrapper.html());
    console.log('All inputs:', wrapper.findAll('input').length);
    console.log('Checkbox inputs:', wrapper.findAll('input[type="checkbox"]').length);
    console.log('NCheckbox components:', wrapper.findAllComponents({ name: 'NCheckbox' }).length);
    // Add check for the input found by the helper
    console.log('Checkbox input found by helper:', findCheckboxInput(wrapper)?.exists());
  });
});
