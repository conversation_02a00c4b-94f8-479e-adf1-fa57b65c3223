# PowerShell script to update specific sections of unit.md

$content = Get-Content -Path "unit.md" -Raw

# Update the Template Slot Limitations to Named Slot Support
$pattern1 = '5\. \*\*Template Slot Limitations\*\*: NButton and similar stubs don''t render template slots, so test attributes instead of icon visibility'
$replace1 = '5. **Named Slot Support**: Component stubs support essential named slots like `#icon` for consistent rendering'
$content = $content -replace $pattern1, $replace1

# Update NButton stub description
$pattern2 = '- `NButton` → `<button data-testid="nbutton"><slot /></button>`'
$replace2 = '- `NButton` → `<button data-testid="nbutton"><slot name="icon" /><slot /></button>`'
$content = $content -replace $pattern2, $replace2

# Update Critical Lessons section
$pattern3 = '2\. \*\*Stub Limitations\*\*: Global stubs may not render template slots \(like icons inside buttons\), so test attributes rather than visual elements'
$replace3 = '2. **Named Slot Support**: Most stubs (especially NButton) have been updated to support named slots like `#icon` which is crucial for testing components that use them'
$content = $content -replace $pattern3, $replace3

# Add Troubleshooting section before the last section
$lastSection = "## Socket.IO Testing"
$troubleshootingSection = @"

## Common Issues and Troubleshooting

### Empty Component Text Content

If your component renders but `wrapper.text()` is empty when it should contain text:

1. **Check Named Slots**: Ensure the component isn't using named slots that aren't supported in the stub
2. **Examine HTML Output**: Use `console.log(wrapper.html())` to debug the actual rendered structure
3. **Verify Icon Slot Rendering**: For buttons with icon text, make sure the NButton stub includes `<slot name="icon" />`

### Reactive State Changes Not Reflected

When testing components that respond to reactive state:

1. **Re-mount the Component**: Create a new instance after changing mock state values
2. **Use nextTick**: Wait for Vue's reactivity to update with `await wrapper.vm.$nextTick()`
3. **Set Mocked Values Before Mounting**: Initialize mock state before creating the component wrapper

### Test-Specific Stubs

If you need to extend or override the global stubs for a specific test:

```typescript
const createComponent = (props = {}) => {
  return mount(MyComponent, {
    props,
    global: {
      stubs: {
        // Override specific stubs
        NButton: {
          template: `<button data-testid="custom-button"><slot name="icon" /><slot /></button>`,
          props: ['loading', 'disabled', 'size', 'type', 'htmlType', 'title'],
          emits: ['click']
        }
      }
    }
  })
}
```

### Testing Slots and Custom Content

When testing components that provide slots to their children:

```typescript
const createComponent = () => {
  return mount(ParentComponent, {
    slots: {
      default: '<div class="custom-content">Slot content</div>',
      header: '<h2>Custom Header</h2>'
    }
  })
}

it('renders slot content', () => {
  wrapper = createComponent()
  expect(wrapper.find('.custom-content').exists()).toBe(true)
  expect(wrapper.find('h2').exists()).toBe(true)
})
```

"@

$content = $content -replace $lastSection, ($troubleshootingSection + "`n" + $lastSection)

# Write the updated content back to the file
$content | Set-Content -Path "unit.md"

Write-Host "Documentation updated successfully!"
