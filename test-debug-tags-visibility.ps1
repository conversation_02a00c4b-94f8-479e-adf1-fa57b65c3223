# PowerShell script to test debug report tags visibility
Write-Host "Testing Debug Report Tags Visibility..." -ForegroundColor Green

# Test if backend is running
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3000/health" -Method GET -ErrorAction Stop
    Write-Host "✓ Backend is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Backend not running. Please start with: npm run dev" -ForegroundColor Red
    exit 1
}

Write-Host "`nFetching debug reports to check tag structure..." -ForegroundColor Yellow

try {
    # Fetch recent debug reports
    $reports = Invoke-RestMethod -Uri "http://localhost:3000/api/admin/debug-reports" -Method GET
    
    if ($reports -and $reports.reports -and $reports.reports.Count -gt 0) {
        Write-Host "✓ Found $($reports.reports.Count) debug reports" -ForegroundColor Green
        
        # Check first few reports for tags
        for ($i = 0; $i -lt [Math]::Min(3, $reports.reports.Count); $i++) {
            $report = $reports.reports[$i]
            Write-Host "`n--- Report $($i + 1) ---" -ForegroundColor Cyan
            Write-Host "Report ID: $($report.reportId)"
            Write-Host "Report Type: $($report.reportType)"
            Write-Host "Title: $($report.reportTitle)"
            
            if ($report.tags -and $report.tags.Count -gt 0) {
                Write-Host "✓ Tags found: $($report.tags.Count)" -ForegroundColor Green
                foreach ($tag in $report.tags) {
                    if ($tag.PSObject.Properties["tag"] -and $tag.PSObject.Properties["origin"]) {
                        Write-Host "  - Tag: '$($tag.tag)' (Origin: $($tag.origin))" -ForegroundColor White
                    } else {
                        Write-Host "  - Tag structure: $($tag | ConvertTo-Json -Compress)" -ForegroundColor Yellow
                    }
                }
            } else {
                Write-Host "✗ No tags found" -ForegroundColor Red
            }
        }
        
        # Get a specific report with detailed info
        $firstReportId = $reports.reports[0].reportId
        Write-Host "`nFetching detailed report: $firstReportId" -ForegroundColor Yellow
        
        $detailedReport = Invoke-RestMethod -Uri "http://localhost:3000/api/admin/debug-reports/$firstReportId" -Method GET
        
        Write-Host "`n--- Detailed Report Structure ---" -ForegroundColor Cyan
        Write-Host "Full report JSON (first 1000 chars):"
        $jsonOutput = $detailedReport | ConvertTo-Json -Depth 10
        if ($jsonOutput.Length -gt 1000) {
            Write-Host $jsonOutput.Substring(0, 1000) + "..." -ForegroundColor Gray
        } else {
            Write-Host $jsonOutput -ForegroundColor Gray
        }
        
        if ($detailedReport.tags) {
            Write-Host "`nTags in detailed report:" -ForegroundColor Green
            Write-Host ($detailedReport.tags | ConvertTo-Json -Depth 3) -ForegroundColor White
        } else {
            Write-Host "✗ No tags in detailed report" -ForegroundColor Red
        }
        
    } else {
        Write-Host "✗ No debug reports found" -ForegroundColor Red
    }
    
} catch {
    Write-Host "✗ Error fetching debug reports: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest completed." -ForegroundColor Green
