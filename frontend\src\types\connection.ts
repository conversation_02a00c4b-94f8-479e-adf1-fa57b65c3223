export enum ConnectionStatus {
  EXCELLENT = 'EXCELLENT',
  GOOD = 'GOOD',
  POOR = 'POOR',
  OFFLINE = 'OFFLINE'
}

export type TransportType = 'websocket' | 'polling';

export interface ConnectionState {
  isConnected: boolean;
  transportType: TransportType;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  lastDisconnectReason: string | null;
  isReconnecting: boolean;
}

export interface ConnectionQualityInfo {
  status: ConnectionStatus;
  message: string;
  icon?: string;
}