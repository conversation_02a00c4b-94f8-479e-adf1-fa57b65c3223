const { PrismaClient } = require('@prisma/client');

async function checkNotificationTable() {
  const prisma = new PrismaClient();
  
  try {
    // Try to get table info using raw SQL
    const result = await prisma.$queryRaw`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='Notification';
    `;
    
    console.log('Notification table check result:', result);
    
    if (result.length === 0) {
      console.log('❌ Notification table does not exist');
    } else {
      console.log('✅ Notification table exists');
    }
    
    // List all tables
    const allTables = await prisma.$queryRaw`
      SELECT name FROM sqlite_master WHERE type='table';
    `;
    
    console.log('\nAll tables in database:');
    allTables.forEach(table => console.log('  -', table.name));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkNotificationTable();
