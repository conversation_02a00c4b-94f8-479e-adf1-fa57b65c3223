#!/bin/bash
# Production Deployment Verification Script - CentOS 9  
# Run this after deployment to verify everything is working

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Database credentials
DB_NAME="munygo_db"
DB_USER="munygo_user"
DB_PASS="U6^#A7sBp&tE%qgRt5Ra"
PROJECT_NAME="munygo"

echo -e "${GREEN}🔍 MUNygo Production Deployment Verification${NC}"
echo -e "${YELLOW}Debug Report System Update Check${NC}"

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}❌ docker-compose.yml not found. Are you in the MUNygo project directory?${NC}"
    exit 1
fi

# Load environment variables
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

echo ""
echo -e "${CYAN}=== Container Status ===${NC}"

# Check container status
docker compose ps

# Check individual container health
EXPECTED_CONTAINERS=("munygo-postgres" "munygo-backend" "munygo-frontend")
HEALTHY_COUNT=0

for container in "${EXPECTED_CONTAINERS[@]}"; do
    if docker compose ps | grep -q "$container.*Up"; then
        HEALTHY_COUNT=$((HEALTHY_COUNT + 1))
        echo -e "${GREEN}✅ $container is running${NC}"
    else
        echo -e "${RED}❌ $container is not running${NC}"
    fi
done

echo ""
echo -e "${CYAN}=== API Health Checks ===${NC}"

# Get backend port
BACKEND_PORT=$(grep -A 10 "backend:" docker-compose.yml | grep -E "ports:|^\s*-\s*\".*:3000\"" | head -1 | sed 's/.*"\([0-9]*\):3000".*/\1/' 2>/dev/null || echo "3004")

# Test backend health
if curl -s --connect-timeout 10 "http://localhost:$BACKEND_PORT/health" > /dev/null; then
    echo -e "${GREEN}✅ Backend API is responding${NC}"
    RESPONSE=$(curl -s --connect-timeout 10 "http://localhost:$BACKEND_PORT/health" || echo "No response")
    echo -e "   Response: ${BLUE}$RESPONSE${NC}"
else
    echo -e "${RED}❌ Backend API health check failed${NC}"
fi

# Get frontend port
FRONTEND_PORT=$(grep -A 10 "frontend:" docker-compose.yml | grep -E "ports:|^\s*-\s*\".*:80\"" | head -1 | sed 's/.*"\([0-9]*\):80".*/\1/' 2>/dev/null || echo "8081")

# Test frontend
if curl -s --connect-timeout 10 "http://localhost:$FRONTEND_PORT" > /dev/null; then
    echo -e "${GREEN}✅ Frontend is responding${NC}"
else
    echo -e "${RED}❌ Frontend health check failed${NC}"
fi

echo ""
echo -e "${CYAN}=== Database Schema Verification ===${NC}"

# Check database connection and debug report tables
if [ -n "$POSTGRES_USER" ] && [ -n "$POSTGRES_DB" ]; then
    # Check if debug report tables exist
    DB_TABLES=$(docker exec munygo-postgres psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%debug%';" 2>/dev/null | tr -d ' ')
    
    if [ -n "$DB_TABLES" ]; then
        echo -e "${GREEN}✅ Debug report tables found:${NC}"
        echo "$DB_TABLES" | while IFS= read -r table; do
            [ -n "$table" ] && echo -e "   - ${BLUE}$table${NC}"
        done
    else
        echo -e "${RED}❌ No debug report tables found${NC}"
    fi
    
    # Check migration status
    MIGRATION_STATUS=$(docker exec munygo-postgres psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT migration_name FROM _prisma_migrations ORDER BY finished_at DESC LIMIT 3;" 2>/dev/null)
    if [ -n "$MIGRATION_STATUS" ]; then
        echo -e "${GREEN}✅ Recent migrations:${NC}"
        echo "$MIGRATION_STATUS" | while IFS= read -r migration; do
            [ -n "$migration" ] && echo -e "   - ${BLUE}$(echo $migration | tr -d ' ')${NC}"
        done
    fi
else
    echo -e "${YELLOW}⚠️ Database credentials not found in environment${NC}"
fi

echo ""
echo -e "${CYAN}=== Log Analysis ===${NC}"

# Check for recent errors in logs
echo -e "${YELLOW}Recent backend logs (last 10 lines):${NC}"
docker compose logs backend --tail=10

echo ""
echo -e "${YELLOW}Recent frontend logs (last 5 lines):${NC}"
docker compose logs frontend --tail=5

# Check for specific error patterns
echo ""
echo -e "${YELLOW}Checking for errors in recent logs...${NC}"

if docker compose logs backend --tail=50 | grep -i -E "(error|fatal|exception)" > /dev/null; then
    echo -e "${YELLOW}⚠️ Found errors in backend logs:${NC}"
    docker compose logs backend --tail=50 | grep -i -E "(error|fatal|exception)" | head -5 | while IFS= read -r line; do
        echo -e "   ${BLUE}$line${NC}"
    done
else
    echo -e "${GREEN}✅ No errors found in recent backend logs${NC}"
fi

echo ""
echo -e "${CYAN}=== Environment Variables Check ===${NC}"

# Check critical environment variables in backend container
ENV_VARS=("CLIENT_LOG_DIRECTORY" "VITE_ENABLE_DEBUG_REPORT" "DATABASE_URL" "JWT_SECRET")
for var in "${ENV_VARS[@]}"; do
    if docker exec ${PROJECT_NAME}-backend env | grep -q "^$var="; then
        echo -e "${GREEN}✅ $var is set${NC}"
    else
        echo -e "${RED}❌ $var is not set${NC}"
    fi
done

echo ""
echo -e "${CYAN}=== Volume and Storage Check ===${NC}"

# Check volumes
VOLUMES=$(docker volume ls --filter "name=${PROJECT_NAME}" --format "{{.Name}}" 2>/dev/null)
if [ -n "$VOLUMES" ]; then
    echo -e "${GREEN}✅ Found volumes:${NC}"
    echo "$VOLUMES" | while IFS= read -r volume; do
        echo -e "   - ${BLUE}$volume${NC}"
    done
else
    echo -e "${YELLOW}⚠️ No project-specific volumes found${NC}"
fi

# Check log directory in backend container
if docker exec ${PROJECT_NAME}-backend ls -la /app/logs >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Log directory exists in backend container${NC}"
    LOG_FILES=$(docker exec ${PROJECT_NAME}-backend ls -1 /app/logs 2>/dev/null | wc -l)
    echo -e "   ${BLUE}Log files count: $LOG_FILES${NC}"
else
    echo -e "${YELLOW}⚠️ Could not verify log directory in backend container${NC}"
fi

echo ""
echo -e "${CYAN}=== Network Connectivity ===${NC}"

# Test internal container communication
if docker exec ${PROJECT_NAME}-backend nc -z ${PROJECT_NAME}-postgres 5432 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend can connect to database${NC}"
else
    echo -e "${RED}❌ Backend cannot connect to database${NC}"
fi

echo ""
echo -e "${CYAN}=== System Resources ===${NC}"

# Check disk usage
DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}')
echo -e "${BLUE}Disk usage: $DISK_USAGE${NC}"

# Check container memory usage
echo -e "${BLUE}Container memory usage:${NC}"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" ${PROJECT_NAME}-backend ${PROJECT_NAME}-frontend ${PROJECT_NAME}-postgres 2>/dev/null || echo "Could not retrieve stats"

echo ""
echo -e "${CYAN}=== Summary ===${NC}"

TOTAL_CONTAINERS=${#EXPECTED_CONTAINERS[@]}
echo -e "${BLUE}Containers: $HEALTHY_COUNT/$TOTAL_CONTAINERS healthy${NC}"

# Overall status
if [ $HEALTHY_COUNT -eq $TOTAL_CONTAINERS ]; then
    echo ""
    echo -e "${GREEN}🎉 Deployment verification completed successfully!${NC}"
    echo -e "${GREEN}Your debug report system update is ready to use.${NC}"
else
    echo ""
    echo -e "${YELLOW}⚠️ Deployment verification found issues.${NC}"
    echo -e "${YELLOW}Please review the output above and address any problems.${NC}"
fi

echo ""
echo -e "${CYAN}📋 Manual verification checklist:${NC}"
echo "□ Visit your frontend URL to test the debug report feature"
echo "□ Log in as admin and check the admin dashboard"
echo "□ Submit a test debug report"
echo "□ Verify debug reports appear in admin dashboard"
echo "□ Test debug report status changes"

echo ""
echo -e "${CYAN}🔧 Useful commands:${NC}"
echo "View all logs: docker compose logs -f"
echo "View backend logs: docker compose logs backend -f"
echo "Check container status: docker compose ps"
echo "Restart services: docker compose restart"
echo "Check database: docker exec -it ${PROJECT_NAME}-postgres psql -U \$POSTGRES_USER -d \$POSTGRES_DB"
