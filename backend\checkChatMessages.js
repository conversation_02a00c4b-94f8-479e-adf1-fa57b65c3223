const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkChatMessages() {
  try {
    console.log('=== Checking Chat Messages ===\n');
      // Find the chat session for our transaction
    const transaction = await prisma.transaction.findUnique({
      where: {
        id: 'cmb0xaj5d0006vl20awiy3tgq'
      },
      include: {
        chatSession: true,
        currencyAProvider: { select: { username: true, email: true } },
        currencyBProvider: { select: { username: true, email: true } }
      }
    });
    
    if (!transaction || !transaction.chatSession) {
      console.log('❌ No transaction or chat session found');
      return;
    }
    
    const chatSession = transaction.chatSession;
      console.log(`📱 Chat Session ID: ${chatSession.id}`);
    console.log(`🔗 Transaction ID: ${transaction.id}`);
      // Get chat messages
    const messages = await prisma.chatMessage.findMany({
      where: {
        chatSessionId: chatSession.id
      },
      include: {
        sender: {
          select: { id: true, username: true, email: true }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });
    
    console.log(`\n💬 Found ${messages.length} messages:`);
    
    messages.forEach((msg, index) => {
      const sender = msg.sender ? (msg.sender.username || msg.sender.email) : 'System';
      const messageType = msg.isSystemMessage ? '[SYSTEM]' : '[USER]';
      console.log(`${index + 1}. ${messageType} ${sender}: ${msg.content}`);
      console.log(`   📅 ${msg.createdAt}`);
    });
    
    // Check for system messages about payment declaration
    const systemMessages = messages.filter(msg => msg.isSystemMessage);
    console.log(`\n🤖 System messages: ${systemMessages.length}`);
    
    const paymentMessages = systemMessages.filter(msg => 
      msg.content.toLowerCase().includes('payment') || 
      msg.content.toLowerCase().includes('declared')
    );
    
    if (paymentMessages.length === 0) {
      console.log('⚠️  No system messages about payment declaration found');
      console.log('   This might indicate our system message fix needs testing');
    } else {
      console.log('✅ Found payment-related system messages');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkChatMessages();
