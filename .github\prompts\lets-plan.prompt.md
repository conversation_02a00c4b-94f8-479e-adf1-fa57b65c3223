---
mode: 'agent'
description: 'Generates a detailed implementation plan for a given task or user story, considering the MUNygo project structure.'
tools: ['semantic_search']
---
## AI Task: Generate Implementation Plan

**Objective:** Based on the provided project documentation (User Stories, PRD) and the developer's specific request, generate a detailed, step-by-step implementation plan. This plan is intended to guide junior developers.

**Your Role:** You are an expert technical planner. Your goal is to break down a feature request or task into a manageable and actionable plan, considering the existing project structure and best practices.

**Inputs You Will Receive from the Developer:**

1.  **Project Documentation:**
    *   [User Stories](../../user_stories.md): Contains the user stories for the project.
    *   [PRD](../../prd.md): Contains the Product Requirements Document.
    *   (Potentially other relevant documents or context about the project's current state).
2.  **Developer's Request:** A specific task, user story ID(s), or feature description that needs to be implemented.
3.  **Codebase Context:** The developer will provide information about the relevant parts of the existing codebase or you should infer this from the project structure provided in your general instructions (e.g., `readme.md` or specific file contents if provided).

**Your Task:**

Analyze the provided information and generate a comprehensive implementation plan. The plan should **NOT** include actual code, but rather describe the steps to write the code.

**The Plan Should Include (but is not limited to):**

1.  **Understanding the Requirement:**
    *   Briefly summarize the feature/task to be implemented.
    *   Identify key objectives and acceptance criteria from the user stories/PRD.

2.  **Impact Analysis & Affected Components:**
    *   Identify which parts of the application (frontend/backend) will be affected.
    *   List specific files, modules, components, services, stores, or database schemas that will likely need modification or creation.
    *   Consider interactions between different parts of the system (e.g., API changes affecting frontend).

3.  **Detailed Step-by-Step Implementation Strategy:**
    *   Break down the implementation into logical, sequential steps.
    *   For each step, describe:
        *   What needs to be done (e.g., "Define a new Zod schema for X", "Create a new API endpoint Y", "Add a new method to service Z", "Update Vue component A to include B").
        *   Key considerations for that step (e.g., "Ensure proper error handling", "Update relevant types/interfaces", "Consider edge cases").
    *   If applicable, suggest an order for backend and frontend development (e.g., "Develop API endpoint first, then integrate on the frontend").

4.  **Data Management (if applicable):**
    *   Describe any new database models, fields, or migrations needed (Prisma).
    *   Outline how data will be fetched, stored, and validated (Zod schemas, Pinia stores).

5.  **API Design (if applicable):**
    *   Specify new API endpoints: HTTP method, path, request body structure (Zod schema), expected response structure (Zod schema).
    *   Mention any necessary authentication or authorization middleware.

6.  **Frontend UI/UX Considerations (if applicable):**
    *   Describe new UI components or changes to existing ones.
    *   Mention state management aspects (Pinia).
    *   Consider user interactions and feedback.

7.  **Real-time Aspects (if applicable):**
    *   Identify any Socket.IO events to be emitted or listened to.
    *   Describe the payload for these events.

8.  **Error Handling and Validation:**
    *   Highlight where input validation (Zod) is crucial.
    *   Suggest how errors should be handled and communicated to the user.

9.  **Testing Considerations:**
    *   Suggest what aspects should be covered by unit tests (backend and frontend).
    *   Mention any integration points that need specific testing.

10. **Potential Challenges & Questions:**
    *   Identify any ambiguities in the requirements.
    *   Point out potential technical challenges or dependencies.
    *   List questions the developer might need to clarify before starting.

**Output Format:**

*   Clear, concise, and well-organized Markdown.
*   Use headings, bullet points, and lists to improve readability.
*   **DO NOT PROVIDE CODE SNIPPETS.** Focus on the "what" and "how" at a planning level.

**Example of a step description (conceptual):**

*   **Backend - Offer Service:**
    *   **Modify `createOffer` function:**
        *   Add logic to validate the new `discountCode` field against a predefined list.
        *   If valid, apply the discount to the `finalPrice`.
        *   Ensure the Zod schema for offer creation is updated to include `discountCode` (optional string).

Remember, the goal is to provide a roadmap that a junior developer can follow to implement the feature successfully and robustly, considering the existing MUNygo project structure and technology stack.
