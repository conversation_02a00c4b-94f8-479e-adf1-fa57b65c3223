<template>
  <div class="demo-container">
    <n-space vertical size="large">
      <!-- Replace the old navbar with the new one -->
      <ModernNavBar class="modern-navbar" />
      
      <!-- Demo Content -->
      <n-card>
        <template #header>
          <h2>Modern NavBar Demo</h2>
        </template>
        
        <n-space vertical size="medium">
          <p>This demo showcases the new ModernNavBar component with the following features:</p>
          
          <n-list bordered>
            <n-list-item>
              <template #prefix>
                <n-icon><CheckmarkCircleOutline /></n-icon>
              </template>
              Responsive design (desktop & mobile hamburger menu)
            </n-list-item>
            
            <n-list-item>
              <template #prefix>
                <n-icon><CheckmarkCircleOutline /></n-icon>
              </template>
              Dark/Light mode support with smooth transitions
            </n-list-item>
            
            <n-list-item>
              <template #prefix>
                <n-icon><CheckmarkCircleOutline /></n-icon>
              </template>
              Full i18n support (English/Persian)
            </n-list-item>
            
            <n-list-item>
              <template #prefix>
                <n-icon><CheckmarkCircleOutline /></n-icon>
              </template>
              RTL/LTR layout support
            </n-list-item>
            
            <n-list-item>
              <template #prefix>
                <n-icon><CheckmarkCircleOutline /></n-icon>
              </template>
              Real-time connection status indicator
            </n-list-item>
            
            <n-list-item>
              <template #prefix>
                <n-icon><CheckmarkCircleOutline /></n-icon>
              </template>
              Integrated notification bell
            </n-list-item>
            
            <n-list-item>
              <template #prefix>
                <n-icon><CheckmarkCircleOutline /></n-icon>
              </template>
              User avatar with dropdown menu
            </n-list-item>
            
            <n-list-item>
              <template #prefix>
                <n-icon><CheckmarkCircleOutline /></n-icon>
              </template>
              Mobile-first responsive design
            </n-list-item>
          </n-list>
          
          <n-alert type="info" :bordered="false">
            <template #icon>
              <n-icon><InformationCircleOutline /></n-icon>
            </template>
            Try resizing your browser window to see the responsive behavior, or switch themes and languages to test the full functionality.
          </n-alert>
          
          <!-- Theme and Language Test Controls -->
          <n-card>
            <template #header>Test Controls</template>
            <n-space>
              <n-button @click="toggleTheme" type="primary">
                Toggle Theme
              </n-button>
              
              <n-button @click="toggleLanguage" type="primary">
                Toggle Language
              </n-button>
              
              <n-button @click="simulateNotification" type="success">
                Test Notification
              </n-button>
            </n-space>
          </n-card>
        </n-space>
      </n-card>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { NSpace, NCard, NList, NListItem, NIcon, NAlert, NButton, useMessage } from 'naive-ui'
import { CheckmarkCircleOutline, InformationCircleOutline } from '@vicons/ionicons5'
import { useThemeStore } from '@/stores/theme'
import { useLanguageStore } from '@/stores/language'
import { useNotificationStore } from '@/stores/notificationStore'
import { FrontendNotificationType, type FrontendNotification } from '@/stores/notificationStore'
import ModernNavBar from '@/components/ModernNavBar.vue'

const themeStore = useThemeStore()
const languageStore = useLanguageStore()
const notificationStore = useNotificationStore()
const message = useMessage()

const toggleTheme = () => {
  themeStore.toggleTheme()
  message.success(`Switched to ${themeStore.isDark ? 'dark' : 'light'} mode`)
}

const toggleLanguage = () => {
  const newLang = languageStore.currentLanguage === 'en' ? 'fa' : 'en'
  languageStore.setLanguage(newLang)
  message.success(`Language changed to ${newLang.toUpperCase()}`)
}

const simulateNotification = () => {
  // Add a test notification to demonstrate the notification bell
  const testNotification: FrontendNotification = {
    id: `test-${Date.now()}`,
    type: FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER,
    message: 'This is a test notification to demonstrate the navbar notification system',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isRead: false,
    userId: 'test-user'
  }
  
  notificationStore.addOrUpdateNotification(testNotification)
  message.success('Test notification added!')
}
</script>

<style scoped>
.demo-container {
  min-height: 100vh;
  background: var(--n-body-color);
  padding: 0;
}

.demo-container :deep(.n-card) {
  margin: 1rem;
}

/* Ensure the demo navbar doesn't conflict with the app's navbar */
.demo-container > .n-space > .modern-navbar {
  position: relative;
  margin-bottom: 1rem;
}
</style>
