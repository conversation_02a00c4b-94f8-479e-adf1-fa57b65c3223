// Test the browse API endpoint with proper authentication to see what data is returned
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testBrowseWithAuth() {
  try {
    console.log('Testing browse API with authentication simulation...\n');

    // First, let's get a user who should see the completed transaction
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true
      },
      take: 5
    });

    console.log('Available users:');
    users.forEach(user => {
      console.log(`- ID: ${user.id}, Username: ${user.username}, Email: ${user.email}`);
    });    // Use a specific user (replace with actual user ID that should see the completed transaction)
    const testUserId = 'cmav458yl0000vl50932ofx2h'; // User 'h'

    console.log(`\nTesting as user: ${testUserId}\n`);

    // Simulate the browse query with proper user context
    const offers = await prisma.offer.findMany({
      where: {
        status: 'ACTIVE',        NOT: {
          userId: testUserId // Exclude offers created by the current user
        }
      },
      include: {        user: {
          select: {
            id: true,
            username: true,
            reputationLevel: true
          }
        },        interests: {
          where: {
            interestedUserId: testUserId
          },
          include: {
            chatSession: {
              select: {
                id: true,
                transaction: {
                  select: {
                    id: true,
                    status: true,
                    payerNegotiation: {
                      select: {
                        status: true
                      }
                    }
                  }
                }
              }
            }
          }
        },
        chatSessions: {
          include: {
            transaction: {
              select: {
                id: true,
                status: true,
                payerNegotiation: {
                  select: {
                    status: true
                  }
                }
              }
            }
          }
        }
      }
    });

    console.log(`Found ${offers.length} offers\n`);

    offers.forEach(offer => {
      console.log(`\n=== OFFER ${offer.id} ===`);
      console.log(`Type: ${offer.type}`);
      console.log(`Amount: ${offer.amount}`);      console.log(`Creator: ${offer.user.username} (ID: ${offer.user.id})`);
      console.log(`Creator Reputation: ${offer.user.reputationLevel}`);
      
      // Check user's interest
      console.log(`\nUser's interest:`, offer.interests.length > 0 ? offer.interests[0] : 'None');
      
      if (offer.interests.length > 0) {
        const interest = offer.interests[0];
        console.log(`Interest Status: ${interest.status}`);
        console.log(`Interest Chat Session:`, interest.chatSession?.id || 'None');
        
        if (interest.chatSession?.transaction) {
          console.log(`Transaction Status: ${interest.chatSession.transaction.status}`);
          console.log(`Negotiation Status: ${interest.chatSession.transaction.payerNegotiation?.status || 'None'}`);
        }
      }
      
      // Check all chat sessions for any transactions
      console.log(`\nAll chat sessions (${offer.chatSessions.length}):`);
      offer.chatSessions.forEach((session, index) => {
        console.log(`  Session ${index + 1}: ${session.id}`);
        if (session.transaction) {
          console.log(`    - Transaction Status: ${session.transaction.status}`);
          console.log(`    - Negotiation Status: ${session.transaction.payerNegotiation?.status || 'None'}`);
        } else {
          console.log(`    - No transaction`);
        }
      });
      
      // Determine what the API should return
      let transactionStatus = null;
      let negotiationStatus = null;
      let currentUserInterestStatus = null;
      let chatSessionId = null;
      
      if (offer.interests.length > 0) {
        const interest = offer.interests[0];
        currentUserInterestStatus = interest.status;
        chatSessionId = interest.chatSessionId;
        
        if (interest.chatSession?.transaction) {
          transactionStatus = interest.chatSession.transaction.status;
          negotiationStatus = interest.chatSession.transaction.payerNegotiation?.status;
        }
      }
      
      // If no transaction from user's interest, check all chat sessions
      if (!transactionStatus) {
        for (const session of offer.chatSessions) {
          if (session.transaction) {
            transactionStatus = session.transaction.status;
            negotiationStatus = session.transaction.payerNegotiation?.status;
            break; // Use the first transaction found
          }
        }
      }
      
      console.log(`\n--- API RESPONSE SHOULD BE ---`);
      console.log(`transactionStatus: ${transactionStatus}`);
      console.log(`negotiationStatus: ${negotiationStatus}`);
      console.log(`currentUserInterestStatus: ${currentUserInterestStatus}`);
      console.log(`chatSessionId: ${chatSessionId}`);
      console.log(`currentUserHasShownInterest: ${offer.interests.length > 0}`);
    });

  } catch (error) {
    console.error('Error testing browse with auth:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testBrowseWithAuth();
