# Production Deployment Verification Script
# Run this after deployment to verify everything is working

Write-Host "🔍 MUNygo Production Deployment Verification" -ForegroundColor Green
Write-Host "Debug Report System Update Check" -ForegroundColor Yellow

# Check if we're in the right directory
if (-Not (Test-Path "docker-compose.yml")) {
    Write-Error "❌ docker-compose.yml not found. Are you in the MUNygo project directory?"
    exit 1
}

Write-Host ""
Write-Host "=== Container Status ===" -ForegroundColor Cyan

# Check container status
$containers = docker compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
Write-Host $containers

# Check individual container health
$expectedContainers = @("munygo-postgres", "munygo-backend", "munygo-frontend")
$healthyContainers = @()

foreach ($container in $expectedContainers) {
    $status = docker inspect $container --format "{{.State.Health.Status}}" 2>$null
    if ($status -eq "healthy" -or $status -eq "") {
        $healthyContainers += $container
        Write-Host "✅ $container is healthy" -ForegroundColor Green
    }
    else {
        Write-Host "❌ $container is unhealthy: $status" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== API Health Checks ===" -ForegroundColor Cyan

# Test backend health
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3004/health" -TimeoutSec 10
    Write-Host "✅ Backend API is responding" -ForegroundColor Green
    Write-Host "   Response: $($response | ConvertTo-Json -Compress)" -ForegroundColor Gray
}
catch {
    Write-Host "❌ Backend API health check failed" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Gray
}

# Test frontend
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Frontend is responding" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Frontend returned status: $($response.StatusCode)" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Frontend health check failed" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Gray
}

Write-Host ""
Write-Host "=== Database Schema Verification ===" -ForegroundColor Cyan

# Check database connection and debug report tables
try {
    # Check if debug report tables exist
    $dbTables = docker exec munygo-postgres psql -U $env:POSTGRES_USER -d $env:POSTGRES_DB -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%debug%';" 2>$null
    
    if ($dbTables) {
        Write-Host "✅ Debug report tables found:" -ForegroundColor Green
        $dbTables.Split("`n") | Where-Object { $_.Trim() -ne "" } | ForEach-Object {
            Write-Host "   - $($_.Trim())" -ForegroundColor Gray
        }
    }
    else {
        Write-Host "❌ No debug report tables found" -ForegroundColor Red
    }
    
    # Check migration status
    $migrationStatus = docker exec munygo-postgres psql -U $env:POSTGRES_USER -d $env:POSTGRES_DB -t -c "SELECT migration_name FROM _prisma_migrations ORDER BY finished_at DESC LIMIT 3;" 2>$null
    if ($migrationStatus) {
        Write-Host "✅ Recent migrations:" -ForegroundColor Green
        $migrationStatus.Split("`n") | Where-Object { $_.Trim() -ne "" } | ForEach-Object {
            Write-Host "   - $($_.Trim())" -ForegroundColor Gray
        }
    }
}
catch {
    Write-Host "❌ Database verification failed" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Gray
}

Write-Host ""
Write-Host "=== Log Analysis ===" -ForegroundColor Cyan

# Check for recent errors in logs
Write-Host "Recent backend logs (last 10 lines):" -ForegroundColor Yellow
docker compose logs backend --tail=10

Write-Host ""
Write-Host "Recent frontend logs (last 5 lines):" -ForegroundColor Yellow
docker compose logs frontend --tail=5

# Check for specific error patterns
Write-Host ""
Write-Host "Checking for errors in recent logs..." -ForegroundColor Yellow

$backendErrors = docker compose logs backend --tail=50 | Select-String -Pattern "ERROR|FATAL|Exception"
if ($backendErrors) {
    Write-Host "⚠️ Found errors in backend logs:" -ForegroundColor Yellow
    $backendErrors | ForEach-Object { Write-Host "   $($_)" -ForegroundColor Gray }
}
else {
    Write-Host "✅ No errors found in recent backend logs" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== Environment Variables Check ===" -ForegroundColor Cyan

# Check critical environment variables
$envVars = @("CLIENT_LOG_DIRECTORY", "VITE_ENABLE_DEBUG_REPORT", "DATABASE_URL", "JWT_SECRET")
foreach ($var in $envVars) {
    $value = docker exec munygo-backend env | Select-String -Pattern "^$var="
    if ($value) {
        Write-Host "✅ $var is set" -ForegroundColor Green
    }
    else {
        Write-Host "❌ $var is not set" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Volume and Storage Check ===" -ForegroundColor Cyan

# Check volumes
$volumes = docker volume ls --filter "name=munygo" --format "{{.Name}}"
if ($volumes) {
    Write-Host "✅ Found volumes:" -ForegroundColor Green
    $volumes.Split("`n") | ForEach-Object {
        Write-Host "   - $_" -ForegroundColor Gray
    }
}

# Check log directory in backend container
try {
    $logDirCheck = docker exec munygo-backend ls -la /app/logs 2>$null
    if ($logDirCheck) {
        Write-Host "✅ Log directory exists in backend container" -ForegroundColor Green
    }
}
catch {
    Write-Host "⚠️ Could not verify log directory in backend container" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Summary ===" -ForegroundColor Cyan

$totalContainers = $expectedContainers.Count
$healthyCount = $healthyContainers.Count

Write-Host "Containers: $healthyCount/$totalContainers healthy" -ForegroundColor $(if ($healthyCount -eq $totalContainers) { "Green" } else { "Yellow" })

# Overall status
if ($healthyCount -eq $totalContainers) {
    Write-Host ""
    Write-Host "🎉 Deployment verification completed successfully!" -ForegroundColor Green
    Write-Host "Your debug report system update is ready to use." -ForegroundColor Green
}
else {
    Write-Host ""
    Write-Host "⚠️ Deployment verification found issues." -ForegroundColor Yellow
    Write-Host "Please review the output above and address any problems." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📋 Manual verification checklist:" -ForegroundColor Cyan
Write-Host "□ Visit your frontend URL to test the debug report feature" -ForegroundColor White
Write-Host "□ Log in as admin and check the admin dashboard" -ForegroundColor White
Write-Host "□ Submit a test debug report" -ForegroundColor White
Write-Host "□ Verify debug reports appear in admin dashboard" -ForegroundColor White
Write-Host "□ Test debug report status changes" -ForegroundColor White
