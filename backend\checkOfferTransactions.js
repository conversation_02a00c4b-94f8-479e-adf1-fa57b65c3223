const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkOffers() {
  try {
    const offers = await prisma.offer.findMany({
      include: {
        user: { select: { username: true, reputationLevel: true } },
        interests: {
          include: {
            chatSession: {
              include: {
                transaction: {
                  include: {
                    payerNegotiation: true
                  }
                }
              }
            }
          }
        },
        chatSessions: {
          include: {
            transaction: {
              include: {
                payerNegotiation: true
              }
            }
          }
        }
      },
      take: 10
    });
    
    console.log('=== OFFERS WITH TRANSACTION STATUS ===');
    console.log('Total offers found:', offers.length);
    
    offers.forEach(offer => {
      console.log('\nOffer ID: ' + offer.id);
      console.log('Type: ' + offer.type + ', Amount: ' + offer.amount + ', Creator: ' + (offer.user?.username || 'Unknown'));
      console.log('Status: ' + offer.status);
      
      // Check interests for transactions
      let hasTransaction = false;
      offer.interests.forEach(interest => {
        if (interest.chatSession?.transaction) {
          hasTransaction = true;
          console.log('  Interest Transaction Status: ' + interest.chatSession.transaction.status);
          if (interest.chatSession.transaction.payerNegotiation) {
            console.log('  Negotiation Status: ' + interest.chatSession.transaction.payerNegotiation.negotiationStatus);
          }
        }
      });
      
      // Check all chat sessions for transactions
      offer.chatSessions.forEach(chatSession => {
        if (chatSession.transaction) {
          hasTransaction = true;
          console.log('  Chat Session Transaction Status: ' + chatSession.transaction.status);
          if (chatSession.transaction.payerNegotiation) {
            console.log('  Chat Session Negotiation Status: ' + chatSession.transaction.payerNegotiation.negotiationStatus);
          }
        }
      });
      
      if (!hasTransaction) {
        console.log('  No transactions found for this offer');
      }
    });
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkOffers();
