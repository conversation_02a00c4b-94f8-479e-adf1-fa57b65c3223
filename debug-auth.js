// Debug script to check authentication status
// Run this in the browser console on the frontend app

console.log('=== Authentication Debug ===');
console.log('Current URL:', window.location.href);

// Check localStorage
const authToken = localStorage.getItem('authToken');
const userInfo = localStorage.getItem('userInfo');

console.log('Auth Token exists:', !!authToken);
console.log('Auth Token (first 20 chars):', authToken ? authToken.substring(0, 20) + '...' : 'null');
console.log('User Info exists:', !!userInfo);

if (userInfo) {
  try {
    const user = JSON.parse(userInfo);
    console.log('User ID:', user?.id);
    console.log('User Email:', user?.email);
    console.log('User verified:', user?.isEmailVerified, user?.isPhoneVerified);
  } catch (e) {
    console.error('Failed to parse user info:', e);
  }
}

// Check if we can access the auth store
if (window.Vue && window.Vue.version) {
  console.log('Vue version:', window.Vue.version);
}

// Try to make a test API call
fetch('http://localhost:3000/api/offers', {
  headers: {
    'Authorization': authToken ? `Bearer ${authToken}` : '',
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('Test API call status:', response.status);
  if (response.status === 401) {
    console.log('❌ Authentication failed - token is invalid or expired');
  } else if (response.status === 200) {
    console.log('✅ Authentication successful');
  }
  return response.text();
})
.then(data => {
  console.log('API Response preview:', data.substring(0, 100));
})
.catch(error => {
  console.error('API call failed:', error);
});
