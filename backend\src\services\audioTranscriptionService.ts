import { 
  AudioTranscriptionRequest, 
  AudioTranscriptionResponse 
} from '../types/schemas/aiSchemas';

/**
 * Audio Transcription Service using Google Speech-to-Text API
 * Handles audio file processing and speech recognition
 */
export class AudioTranscriptionService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.GOOGLE_SPEECH_API_KEY || process.env.GEMINI_API_KEY || '';
    this.baseUrl = 'https://speech.googleapis.com/v1/speech:recognize';
    
    if (!this.apiKey) {
      console.warn('[AudioTranscriptionService] Google Speech API key not found. Transcription features will be disabled.');
    }
  }

  /**
   * Check if transcription service is available
   */
  isAvailable(): boolean {
    return !!this.apiKey;
  }

  /**
   * Transcribe audio data to text
   */
  async transcribeAudio(request: AudioTranscriptionRequest): Promise<AudioTranscriptionResponse> {
    try {
      if (!this.isAvailable()) {
        return {
          success: false,
          error: 'Transcription service is not available. Please check configuration.',
        };
      }

      // Validate audio data
      if (!this.isValidAudioData(request.audioData)) {
        return {
          success: false,
          error: 'Invalid audio data format.',
        };
      }

      // Check duration limits (max 1 minute)
      if (request.duration > 60) {
        return {
          success: false,
          error: 'Audio duration exceeds maximum limit of 60 seconds.',
        };
      }

      const speechResponse = await this.callSpeechApi(request);
      
      if (!speechResponse.success) {
        return {
          success: false,
          error: speechResponse.error,
        };
      }

      return {
        success: true,
        transcription: speechResponse.transcription,
        confidence: speechResponse.confidence,
        language: speechResponse.language || request.language,
      };

    } catch (error: any) {
      console.error('[AudioTranscriptionService] Error transcribing audio:', error);
      return {
        success: false,
        error: `Transcription failed: ${error.message}`,
      };
    }
  }

  /**
   * Validate base64 audio data format and content
   */
  private isValidAudioData(audioData: string): boolean {
    try {
      // Check if it's valid base64
      const decoded = Buffer.from(audioData, 'base64');
      
      // Must have minimum size for audio data
      if (decoded.length < 44) { // Minimum WAV header size
        return false;
      }

      // Check for valid audio file signatures (magic numbers)
      const header = decoded.subarray(0, 12);
      
      // WAV file signature: "RIFF" + 4 bytes + "WAVE"
      if (header.subarray(0, 4).toString() === 'RIFF' && 
          header.subarray(8, 12).toString() === 'WAVE') {
        return this.validateWavStructure(decoded);
      }
      
      // MP3 file signature: ID3 tag or sync frame
      if (header.subarray(0, 3).toString() === 'ID3' || 
          (header[0] === 0xFF && (header[1] & 0xE0) === 0xE0)) {
        return this.validateMp3Structure(decoded);
      }
      
      // WebM/OGG signature
      if (header.subarray(0, 4).toString() === 'OggS') {
        return this.validateOggStructure(decoded);
      }
      
      // M4A/AAC signature: "ftyp"
      if (header.subarray(4, 8).toString() === 'ftyp') {
        return this.validateM4aStructure(decoded);
      }
      
      // FLAC signature
      if (header.subarray(0, 4).toString() === 'fLaC') {
        return this.validateFlacStructure(decoded);
      }
      
      return false;
    } catch {
      return false;
    }
  }

  /**
   * Validate WAV file structure
   */
  private validateWavStructure(buffer: Buffer): boolean {
    try {
      // Check minimum WAV header size
      if (buffer.length < 44) return false;
      
      // Validate chunk size
      const chunkSize = buffer.readUInt32LE(4);
      if (chunkSize + 8 !== buffer.length) return false;
      
      // Look for fmt chunk
      const fmtChunk = buffer.indexOf('fmt ');
      if (fmtChunk === -1 || fmtChunk < 12) return false;
      
      // Basic format validation
      const audioFormat = buffer.readUInt16LE(fmtChunk + 8);
      const numChannels = buffer.readUInt16LE(fmtChunk + 10);
      const sampleRate = buffer.readUInt32LE(fmtChunk + 12);
      
      return audioFormat > 0 && numChannels > 0 && numChannels <= 8 && 
             sampleRate >= 8000 && sampleRate <= 192000;
    } catch {
      return false;
    }
  }

  /**
   * Validate MP3 file structure
   */
  private validateMp3Structure(buffer: Buffer): boolean {
    try {
      // Look for sync frame (0xFF followed by 0xE* or 0xF*)
      for (let i = 0; i < Math.min(buffer.length - 1, 1024); i++) {
        if (buffer[i] === 0xFF && (buffer[i + 1] & 0xE0) === 0xE0) {
          // Basic MP3 frame header validation
          const header = (buffer[i] << 24) | (buffer[i + 1] << 16) | 
                        (buffer[i + 2] << 8) | buffer[i + 3];
          
          // Check version and layer bits
          const version = (header >> 19) & 0x03;
          const layer = (header >> 17) & 0x03;
          
          return version !== 1 && layer !== 0; // Invalid combinations
        }
      }
      return false;
    } catch {
      return false;
    }
  }

  /**
   * Validate OGG file structure
   */
  private validateOggStructure(buffer: Buffer): boolean {
    try {
      // Check OGG page header structure
      if (buffer.length < 27) return false;
      
      // Validate version
      const version = buffer[4];
      if (version !== 0) return false;
      
      // Check page segments
      const pageSegments = buffer[26];
      if (pageSegments > 255) return false;
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate M4A file structure
   */
  private validateM4aStructure(buffer: Buffer): boolean {
    try {
      if (buffer.length < 8) return false;
      
      // Check for valid M4A brand
      const brand = buffer.subarray(8, 12).toString();
      const validBrands = ['M4A ', 'mp41', 'mp42', 'isom'];
      
      return validBrands.includes(brand);
    } catch {
      return false;
    }
  }

  /**
   * Validate FLAC file structure
   */
  private validateFlacStructure(buffer: Buffer): boolean {
    try {
      if (buffer.length < 8) return false;
      
      // Check for STREAMINFO metadata block
      const blockType = buffer[4] & 0x7F;
      return blockType === 0; // STREAMINFO must be first block
    } catch {
      return false;
    }
  }

  /**
   * Call Google Speech-to-Text API
   */
  private async callSpeechApi(request: AudioTranscriptionRequest): Promise<{
    success: boolean;
    transcription?: string;
    confidence?: number;
    language?: string;
    error?: string;
  }> {
    try {
      // Map language codes
      const languageCode = this.mapLanguageCode(request.language || 'en');
      
      // Determine audio encoding from MIME type
      const encoding = this.getAudioEncoding(request.mimeType);
      
      const requestBody = {
        config: {
          encoding,
          sampleRateHertz: 16000, // Standard sample rate for web audio
          languageCode,
          enableAutomaticPunctuation: true,
          enableWordTimeOffsets: false,
          model: 'latest_short', // Optimized for short audio clips
        },
        audio: {
          content: request.audioData,
        },
      };

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Speech API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      
      if (!data.results || data.results.length === 0) {
        return {
          success: false,
          error: 'No speech detected in the audio. Please try speaking more clearly or check your microphone.',
        };
      }

      const result = data.results[0];
      const alternative = result.alternatives[0];
      
      if (!alternative || !alternative.transcript) {
        return {
          success: false,
          error: 'Unable to transcribe the audio. Please try again.',
        };
      }

      return {
        success: true,
        transcription: alternative.transcript.trim(),
        confidence: alternative.confidence || 0.5,
        language: languageCode,
      };

    } catch (error: any) {
      console.error('[AudioTranscriptionService] Speech API call failed:', error);
      return {
        success: false,
        error: `Speech API call failed: ${error.message}`,
      };
    }
  }

  /**
   * Map internal language codes to Google Speech API language codes
   */
  private mapLanguageCode(language: string): string {
    const languageMap: Record<string, string> = {
      'en': 'en-US',
      'fa': 'fa-IR',
    };
    
    return languageMap[language] || 'en-US';
  }

  /**
   * Determine audio encoding from MIME type with robust parsing
   */
  private getAudioEncoding(mimeType: string): string {
    // Normalize and parse MIME type
    const normalizedMimeType = this.normalizeMimeType(mimeType);
    const { baseType, codecs } = this.parseMimeType(normalizedMimeType);
    
    // Primary mapping based on base type and codecs
    const encoding = this.mapToEncoding(baseType, codecs);
    
    // Fallback to legacy static mapping for compatibility
    if (!encoding) {
      return this.legacyEncodingMap(normalizedMimeType);
    }
    
    return encoding;
  }

  /**
   * Normalize MIME type string
   */
  private normalizeMimeType(mimeType: string): string {
    return mimeType
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '') // Remove all whitespace
      .replace(/;+/g, ';') // Normalize multiple semicolons
      .replace(/;$/, ''); // Remove trailing semicolon
  }

  /**
   * Parse MIME type into base type and codecs
   */
  private parseMimeType(mimeType: string): { baseType: string; codecs: string[] } {
    const parts = mimeType.split(';');
    const baseType = parts[0];
    const codecs: string[] = [];
    
    // Extract codec parameters
    for (let i = 1; i < parts.length; i++) {
      const param = parts[i];
      if (param.startsWith('codecs=')) {
        const codecValue = param.substring(7).replace(/['"]/g, ''); // Remove quotes
        // Handle multiple codecs separated by comma
        codecs.push(...codecValue.split(',').map(c => c.trim()));
      }
    }
    
    return { baseType, codecs };
  }

  /**
   * Map base type and codecs to Google Speech API encoding
   */
  private mapToEncoding(baseType: string, codecs: string[]): string | null {
    // WebM container mappings
    if (baseType === 'audio/webm') {
      if (codecs.length === 0 || codecs.includes('opus')) {
        return 'WEBM_OPUS';
      }
      // WebM with other codecs (fallback to opus)
      return 'WEBM_OPUS';
    }
    
    // OGG container mappings
    if (baseType === 'audio/ogg' || baseType === 'application/ogg') {
      if (codecs.length === 0 || codecs.includes('opus')) {
        return 'OGG_OPUS';
      }
      if (codecs.includes('vorbis')) {
        return 'OGG_OPUS'; // Google API uses OGG_OPUS for Vorbis too
      }
    }
    
    // WAV/PCM mappings
    if (baseType === 'audio/wav' || baseType === 'audio/wave' || baseType === 'audio/x-wav') {
      return 'LINEAR16';
    }
    
    // MP3/MPEG mappings
    if (baseType === 'audio/mp3' || baseType === 'audio/mpeg' || baseType === 'audio/x-mp3') {
      return 'MP3';
    }
    
    // MP4/M4A container mappings
    if (baseType === 'audio/mp4' || baseType === 'audio/m4a' || baseType === 'audio/x-m4a') {
      if (codecs.includes('aac') || codecs.includes('mp4a.40.2') || codecs.includes('mp4a.40.5')) {
        return 'MP3'; // Google API uses MP3 for AAC content
      }
      if (codecs.includes('mp3') || codecs.includes('mp4a.40.34')) {
        return 'MP3';
      }
      // Default for MP4 container
      return 'MP3';
    }
    
    // FLAC mappings
    if (baseType === 'audio/flac' || baseType === 'audio/x-flac') {
      return 'FLAC';
    }
    
    // Additional format mappings
    if (baseType === 'audio/aac' || baseType === 'audio/aacp') {
      return 'MP3'; // Google API handles AAC as MP3
    }
    
    if (baseType === 'audio/3gpp' || baseType === 'audio/3gpp2') {
      return 'AMR_WB'; // For mobile recordings
    }
    
    return null;
  }

  /**
   * Legacy static mapping for compatibility
   */
  private legacyEncodingMap(mimeType: string): string {
    const encodingMap: Record<string, string> = {
      // WebM variants
      'audio/webm': 'WEBM_OPUS',
      'audio/webm;codecs=opus': 'WEBM_OPUS',
      'audio/webm;codecs="opus"': 'WEBM_OPUS',
      
      // OGG variants
      'audio/ogg': 'OGG_OPUS',
      'audio/ogg;codecs=opus': 'OGG_OPUS',
      'audio/ogg;codecs="opus"': 'OGG_OPUS',
      'audio/ogg;codecs=vorbis': 'OGG_OPUS',
      'application/ogg': 'OGG_OPUS',
      
      // WAV variants
      'audio/wav': 'LINEAR16',
      'audio/wave': 'LINEAR16',
      'audio/x-wav': 'LINEAR16',
      
      // MP3 variants
      'audio/mp3': 'MP3',
      'audio/mpeg': 'MP3',
      'audio/x-mp3': 'MP3',
      
      // MP4/M4A variants
      'audio/mp4': 'MP3',
      'audio/m4a': 'MP3',
      'audio/x-m4a': 'MP3',
      'audio/mp4;codecs=aac': 'MP3',
      
      // FLAC variants
      'audio/flac': 'FLAC',
      'audio/x-flac': 'FLAC',
      
      // AAC variants
      'audio/aac': 'MP3',
      'audio/aacp': 'MP3',
      
      // Mobile formats
      'audio/3gpp': 'AMR_WB',
      'audio/3gpp2': 'AMR_WB',
    };
    
    return encodingMap[mimeType] || 'WEBM_OPUS';
  }

  /**
   * Get supported audio formats for the client (Based on official Gemini API documentation)
   */
  static getSupportedFormats(): string[] {
    return [
      'audio/webm;codecs=opus', // Best for web recording
      'audio/webm',
      'audio/ogg',              // OGG Vorbis - officially supported by Gemini
      'audio/wav',              // WAV - officially supported by Gemini
      'audio/mp3',              // MP3 - officially supported by Gemini
      'audio/mpeg',
      'audio/aiff',             // AIFF - officially supported by Gemini
      'audio/aac',              // AAC - officially supported by Gemini
      'audio/flac',             // FLAC - officially supported by Gemini
    ];
  }

  /**
   * Get the best supported audio format for the current browser
   */
  static getBestSupportedFormat(): string {
    // This will be used by the frontend to determine the best format
    // Priority: WebM Opus > OGG Opus > WAV
    const formats = AudioTranscriptionService.getSupportedFormats();
    return formats[0]; // Default to WebM Opus
  }
}
