#!/bin/bash

# SIMPLE DOCKER MIGRATION - NO ENV FILE SOURCING
# ===============================================
# Uses Docker Compose to handle environment variables

echo "🐳 Simple Docker Database Migration"
echo "==================================="

# Exit on any error
set -e

# Check if docker-compose is available
if ! command -v docker-compose >/dev/null 2>&1 && ! docker compose version >/dev/null 2>&1; then
    echo "❌ Docker Compose is not available"
    exit 1
fi

# Use docker compose or docker-compose
DOCKER_COMPOSE_CMD="docker-compose"
if ! command -v docker-compose >/dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker compose"
fi

echo "🔍 Checking Docker containers..."

# Check if postgres container is running
if ! $DOCKER_COMPOSE_CMD ps | grep -q "postgres.*Up"; then
    echo "❌ PostgreSQL container is not running"
    echo "🔧 Starting postgres container..."
    $DOCKER_COMPOSE_CMD up -d postgres
    sleep 5
fi

# Get the postgres container name
POSTGRES_CONTAINER=$($DOCKER_COMPOSE_CMD ps --format "table {{.Names}}" | grep postgres | head -1)
if [ -z "$POSTGRES_CONTAINER" ]; then
    POSTGRES_CONTAINER=$(docker ps --format "{{.Names}}" | grep postgres | head -1)
fi

if [ -z "$POSTGRES_CONTAINER" ]; then
    echo "❌ Could not find PostgreSQL container"
    echo "Available containers:"
    docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"
    exit 1
fi

echo "✅ Found PostgreSQL container: $POSTGRES_CONTAINER"

# Get database credentials from docker-compose environment
echo "📊 Reading database configuration from Docker Compose..."
DB_NAME=$($DOCKER_COMPOSE_CMD exec postgres printenv POSTGRES_DB 2>/dev/null || echo "munygo_db")
DB_USER=$($DOCKER_COMPOSE_CMD exec postgres printenv POSTGRES_USER 2>/dev/null || echo "munygo_user")

echo "  Database: $DB_NAME"
echo "  User: $DB_USER"

# Create backup
echo "📦 Creating database backup..."
BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
docker exec $POSTGRES_CONTAINER pg_dump -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo "✅ Database backup created: $BACKUP_FILE"
    echo "📁 Backup size: $(du -h $BACKUP_FILE | cut -f1)"
else
    echo "❌ Backup failed!"
    exit 1
fi

# Check if debug tables already exist
echo "🔍 Checking for existing debug report tables..."
TABLE_EXISTS=$(docker exec $POSTGRES_CONTAINER psql -U "$DB_USER" -d "$DB_NAME" -t -c \
    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'debug_reports');" | xargs)

if [ "$TABLE_EXISTS" = "t" ]; then
    echo "✅ Debug report tables already exist. No migration needed."
    echo "🎯 You can proceed with Docker deployment."
    exit 0
fi

echo "⚠️  Debug report tables not found. Adding them safely..."

# Create the SQL for debug tables
cat > add_debug_tables.sql << 'EOF'
-- Add debug report enums if they don't exist
DO $$ BEGIN
    CREATE TYPE debug_report_type AS ENUM ('BUG', 'FEATURE_REQUEST', 'PERFORMANCE', 'UI_UX', 'IMPROVEMENT', 'OTHER');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE debug_report_severity AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE debug_report_status AS ENUM ('NOT_REVIEWED', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', 'REJECTED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create debug_reports table if it doesn't exist
CREATE TABLE IF NOT EXISTS debug_reports (
    id TEXT PRIMARY KEY,
    report_id TEXT UNIQUE NOT NULL,
    user_id TEXT REFERENCES "User"(id) ON DELETE SET NULL,
    title TEXT NOT NULL,
    type debug_report_type NOT NULL,
    severity debug_report_severity NOT NULL,
    status debug_report_status NOT NULL DEFAULT 'NOT_REVIEWED',
    description TEXT NOT NULL,
    steps_to_reproduce TEXT,
    expected_behavior TEXT,
    actual_behavior TEXT,
    additional_notes TEXT,
    browser_info JSONB,
    user_agent TEXT,
    viewport_size TEXT,
    current_url TEXT,
    user_actions JSONB,
    console_logs JSONB,
    ai_analysis JSONB,
    assigned_to TEXT REFERENCES "User"(id) ON DELETE SET NULL,
    resolution_notes TEXT,
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP(3)
);

-- Create related tables
CREATE TABLE IF NOT EXISTS debug_report_tags (
    id TEXT PRIMARY KEY,
    report_id TEXT NOT NULL REFERENCES debug_reports(id) ON DELETE CASCADE,
    tag TEXT NOT NULL,
    UNIQUE(report_id, tag)
);

CREATE TABLE IF NOT EXISTS debug_report_status_history (
    id TEXT PRIMARY KEY,
    report_id TEXT NOT NULL REFERENCES debug_reports(id) ON DELETE CASCADE,
    old_status debug_report_status NOT NULL,
    new_status debug_report_status NOT NULL,
    changed_by TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
    change_reason TEXT,
    changed_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS debug_report_comments (
    id TEXT PRIMARY KEY,
    report_id TEXT NOT NULL REFERENCES debug_reports(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES "User"(id) ON DELETE CASCADE,
    comment TEXT NOT NULL,
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS debug_reports_user_id_idx ON debug_reports(user_id);
CREATE INDEX IF NOT EXISTS debug_reports_type_idx ON debug_reports(type);
CREATE INDEX IF NOT EXISTS debug_reports_severity_idx ON debug_reports(severity);
CREATE INDEX IF NOT EXISTS debug_reports_status_idx ON debug_reports(status);
CREATE INDEX IF NOT EXISTS debug_reports_assigned_to_idx ON debug_reports(assigned_to);
CREATE INDEX IF NOT EXISTS debug_reports_created_at_idx ON debug_reports(created_at);
CREATE INDEX IF NOT EXISTS debug_report_tags_tag_idx ON debug_report_tags(tag);
CREATE INDEX IF NOT EXISTS debug_report_status_history_report_id_idx ON debug_report_status_history(report_id);
CREATE INDEX IF NOT EXISTS debug_report_comments_report_id_idx ON debug_report_comments(report_id);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_debug_reports_updated_at ON debug_reports;
CREATE TRIGGER update_debug_reports_updated_at BEFORE UPDATE ON debug_reports FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
EOF

# Confirmation
echo ""
echo "🤔 Ready to add debug report tables to your production database."
echo "   This operation is SAFE and will NOT affect existing data."
echo ""
read -p "Proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "🛑 Migration cancelled"
    rm add_debug_tables.sql
    exit 0
fi

# Execute the migration
echo "📋 Adding debug report tables..."
docker exec -i $POSTGRES_CONTAINER psql -U "$DB_USER" -d "$DB_NAME" < add_debug_tables.sql

if [ $? -eq 0 ]; then
    echo "✅ Debug report tables added successfully!"
    rm add_debug_tables.sql
    
    echo ""
    echo "🎉 Migration completed successfully!"
    echo ""
    echo "✅ What was done:"
    echo "  • Created backup: $BACKUP_FILE"
    echo "  • Added debug report tables and indexes"
    echo "  • Preserved ALL existing data"
    echo ""
    echo "🎯 Next step: Run your deployment script"
    echo "   ./deploy-production-centos9.sh"
    
else
    echo "❌ Migration failed!"
    rm add_debug_tables.sql
    exit 1
fi
