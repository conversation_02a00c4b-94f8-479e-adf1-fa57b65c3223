import { describe, it, expect } from 'vitest';
import { getInterestDynamicStatus, getOfferOverallStatus } from '../statusHelpers';
import type { InterestStatus } from '@/types/offer';

// Mock translation function for testing
const mockT = (key: string) => {
  const translations: Record<string, string> = {
    'status.pending': 'در انتظار',
    'status.declined': 'رد شده',
    'status.cancelled': 'لغو شده',
    'status.accepted': 'پذیرفته شده',
    'status.complete': 'تکمیل شده',
    'status.negotiating': 'در حال مذاکره',
    'status.inProgress': 'در حال انجام',
    'status.settingUp': 'در حال تنظیم',
    'status.termsAgreed': 'شرایط توافق شده',
    'status.disputed': 'اختلافی',
    'status.active': 'فعال'
  };
  return translations[key] || key;
};

describe('statusHelpers', () => {
  describe('getInterestDynamicStatus', () => {
    it('should return null for no interest status', () => {
      const result = getInterestDynamicStatus(null);
      expect(result).toBeNull();
    });

    it('should return null for undefined interest status', () => {
      const result = getInterestDynamicStatus(undefined);
      expect(result).toBeNull();
    });

    it('should return Pending for PENDING interest status (without translation)', () => {
      const result = getInterestDynamicStatus('PENDING');
      expect(result).toEqual({
        text: 'Pending',
        type: 'info',
        icon: '⏳'
      });
    });

    it('should return translated Pending for PENDING interest status (with translation)', () => {
      const result = getInterestDynamicStatus('PENDING', null, null, mockT);
      expect(result).toEqual({
        text: 'در انتظار',
        type: 'info',
        icon: '⏳'
      });
    });

    it('should return Declined for DECLINED interest status (without translation)', () => {
      const result = getInterestDynamicStatus('DECLINED');
      expect(result).toEqual({
        text: 'Declined',
        type: 'error'
      });
    });

    it('should return translated Declined for DECLINED interest status (with translation)', () => {
      const result = getInterestDynamicStatus('DECLINED', null, null, mockT);
      expect(result).toEqual({
        text: 'رد شده',
        type: 'error'
      });
    });

    describe('ACCEPTED interest status with transaction progression', () => {
      it('should return Accepted for ACCEPTED status without transaction', () => {
        const result = getInterestDynamicStatus('ACCEPTED');
        expect(result).toEqual({
          text: 'Accepted',
          type: 'success'
        });
      });

      it('should return Setting Up for PENDING_AGREEMENT transaction status', () => {
        const result = getInterestDynamicStatus('ACCEPTED', 'PENDING_AGREEMENT');
        expect(result).toEqual({
          text: 'Setting Up',
          type: 'info'
        });
      });

      it('should return Negotiating for AWAITING_FIRST_PAYER_DESIGNATION with PENDING_RESPONSE negotiation', () => {
        const result = getInterestDynamicStatus('ACCEPTED', 'AWAITING_FIRST_PAYER_DESIGNATION', 'PENDING_RESPONSE');
        expect(result).toEqual({
          text: 'Negotiating',
          type: 'warning',
          icon: '💬'
        });
      });

      it('should return Terms Agreed for AWAITING_FIRST_PAYER_DESIGNATION with FINALIZED negotiation', () => {
        const result = getInterestDynamicStatus('ACCEPTED', 'AWAITING_FIRST_PAYER_DESIGNATION', 'FINALIZED');
        expect(result).toEqual({
          text: 'Terms Agreed',
          type: 'success'
        });
      });

      it('should return In Progress for payment phase statuses', () => {
        const paymentStatuses = [
          'AWAITING_FIRST_PAYER_PAYMENT',
          'AWAITING_FIRST_PAYER_CONFIRMATION',
          'AWAITING_FIRST_PAYER_CONFIRMATION_AND_SECOND_PAYER_PAYMENT',
          'AWAITING_SECOND_PAYER_PAYMENT',
          'AWAITING_SECOND_PAYER_CONFIRMATION'
        ];

        paymentStatuses.forEach(status => {
          const result = getInterestDynamicStatus('ACCEPTED', status);
          expect(result).toEqual({
            text: 'In Progress',
            type: 'warning',
            icon: '🔄'
          });
        });
      });

      it('should return Complete for COMPLETED transaction status', () => {
        const result = getInterestDynamicStatus('ACCEPTED', 'COMPLETED');
        expect(result).toEqual({
          text: 'Complete',
          type: 'success',
          icon: '✅'
        });
      });

      it('should return Cancelled for CANCELLED transaction status', () => {
        const result = getInterestDynamicStatus('ACCEPTED', 'CANCELLED');
        expect(result).toEqual({
          text: 'Cancelled',
          type: 'default'
        });
      });

      it('should return Disputed for DISPUTED transaction status', () => {
        const result = getInterestDynamicStatus('ACCEPTED', 'DISPUTED');
        expect(result).toEqual({
          text: 'Disputed',
          type: 'error'
        });
      });

      it('should default to Accepted for unknown transaction status', () => {
        const result = getInterestDynamicStatus('ACCEPTED', 'UNKNOWN_STATUS');
        expect(result).toEqual({
          text: 'Accepted',
          type: 'success'
        });
      });
    });
  });

  describe('getOfferOverallStatus', () => {
    it('should return offer status when offer is not ACTIVE', () => {
      const offer = {
        status: 'CANCELLED',
        interests: []
      };
      
      const result = getOfferOverallStatus(offer);
      expect(result).toEqual({
        text: 'Cancelled',
        type: 'default'
      });
    });

    it('should return completed status with success type for COMPLETED offers', () => {
      const offer = {
        status: 'COMPLETED',
        interests: []
      };
      
      const result = getOfferOverallStatus(offer);
      expect(result).toEqual({
        text: 'Completed',
        type: 'success'
      });
    });

    it('should return Pending when there are pending interests', () => {
      const offer = {
        status: 'ACTIVE',
        interests: [
          {
            status: 'PENDING' as InterestStatus,
            transactionStatus: null,
            negotiationStatus: null
          }
        ]
      };
      
      const result = getOfferOverallStatus(offer);
      expect(result).toEqual({
        text: 'Pending',
        type: 'info',
        icon: '⏳'
      });
    });

    it('should return Active when there are no interests', () => {
      const offer = {
        status: 'ACTIVE',
        interests: []
      };
      
      const result = getOfferOverallStatus(offer);
      expect(result).toEqual({
        text: 'Active',
        type: 'success'
      });
    });

    it('should return Active when interests array is undefined', () => {
      const offer = {
        status: 'ACTIVE'
      };
      
      const result = getOfferOverallStatus(offer);
      expect(result).toEqual({
        text: 'Active',
        type: 'success'
      });
    });

    it('should prioritize Complete status from accepted interests', () => {
      const offer = {
        status: 'ACTIVE',
        interests: [
          {
            status: 'ACCEPTED' as InterestStatus,
            transactionStatus: 'COMPLETED',
            negotiationStatus: null
          },
          {
            status: 'ACCEPTED' as InterestStatus,
            transactionStatus: 'AWAITING_FIRST_PAYER_PAYMENT',
            negotiationStatus: null
          }
        ]
      };
      
      const result = getOfferOverallStatus(offer);
      expect(result).toEqual({
        text: 'Complete',
        type: 'success',
        icon: '✅'
      });
    });

    it('should show In Progress for active transactions', () => {
      const offer = {
        status: 'ACTIVE',
        interests: [
          {
            status: 'ACCEPTED' as InterestStatus,
            transactionStatus: 'AWAITING_FIRST_PAYER_PAYMENT',
            negotiationStatus: null
          }
        ]
      };
      
      const result = getOfferOverallStatus(offer);
      expect(result).toEqual({
        text: 'In Progress',
        type: 'warning',
        icon: '🔄'
      });
    });

    it('should show Negotiating for negotiation phase', () => {
      const offer = {
        status: 'ACTIVE',
        interests: [
          {
            status: 'ACCEPTED' as InterestStatus,
            transactionStatus: 'AWAITING_FIRST_PAYER_DESIGNATION',
            negotiationStatus: 'PENDING_RESPONSE'
          }
        ]
      };
      
      const result = getOfferOverallStatus(offer);
      expect(result).toEqual({
        text: 'Negotiating',
        type: 'warning',
        icon: '💬'
      });
    });

    it('should show Terms Agreed when negotiation is finalized', () => {
      const offer = {
        status: 'ACTIVE',
        interests: [
          {
            status: 'ACCEPTED' as InterestStatus,
            transactionStatus: 'AWAITING_FIRST_PAYER_DESIGNATION',
            negotiationStatus: 'FINALIZED'
          }
        ]
      };
      
      const result = getOfferOverallStatus(offer);
      expect(result).toEqual({
        text: 'Terms Agreed',
        type: 'success'
      });
    });

    it('should handle mixed interest statuses correctly', () => {
      const offer = {
        status: 'ACTIVE',
        interests: [
          {
            status: 'PENDING' as InterestStatus,
            transactionStatus: null,
            negotiationStatus: null
          },
          {
            status: 'ACCEPTED' as InterestStatus,
            transactionStatus: 'AWAITING_FIRST_PAYER_PAYMENT',
            negotiationStatus: null
          },
          {
            status: 'DECLINED' as InterestStatus,
            transactionStatus: null,
            negotiationStatus: null
          }
        ]
      };
      
      // Should prioritize pending interests over accepted ones
      const result = getOfferOverallStatus(offer);
      expect(result).toEqual({
        text: 'Pending',
        type: 'info',
        icon: '⏳'
      });
    });
  });
});
