{"paymentDetails": {"accountHolderName": "Account Holder Name", "bankName": "Bank Name", "recipientDetails": "Recipient Payment Details", "accountNumber": "Account Number", "maskedAccount": "****{lastDigits}", "institutionName": "Institution", "additionalInfo": "Additional Information", "noDetailsAvailable": "Payment details will be available once negotiation is finalized.", "paymentDueDate": "Payment Due:"}, "title": "Transaction Flow", "loading": "Loading transaction details...", "error": "Unable to load transaction", "transactionId": "Transaction ID", "steps": {"designate": "Designate Payer", "designateDescription": "Decide who pays first", "payment1": "Payment 1", "payment1Description": "First party pays", "confirmation1": "Confirmation 1", "confirmation1Description": "Second party confirms", "payment2": "Payment 2", "payment2Description": "Second party pays", "confirmation2": "Confirmation 2", "confirmation2Description": "First party confirms", "completed": "Completed", "completedDescription": "Transaction finished"}, "userRoles": {"providerOf": "Provider of {currency}", "loadingRole": "Loading role...", "userDataMissing": "User data missing in transaction...", "determiningRole": "Determining role..."}, "currentAction": {"loadingAction": "Loading...", "awaitingDesignation": "Awaiting First Payer Designation", "yourTurnToPay": "Your Turn to Pay", "yourTurnToConfirm": "Your Turn to Confirm Payment", "waitingForPayment": "Waiting for {username} to Pay", "waitingForConfirmation": "Waiting for {username} to Confirm", "transactionCompleted": "Transaction Successfully Completed", "transactionCancelled": "Transaction Cancelled", "transactionDisputed": "Transaction Under Dispute"}, "actionInfo": {"designationPending": "Both parties need to agree on who pays first. Use the negotiation tools below to reach an agreement.", "youPayFirst": "You have been designated to pay first. Please send {amount} {currency} to {username} and then declare your payment.", "youConfirmPayment": "Please confirm that you received {amount} {currency} from {username}.", "otherPartyPaying": "{username} will pay {amount} {currency} to you first. You will confirm once received.", "otherPartyConfirming": "Waiting for {username} to confirm they received your payment of {amount} {currency}.", "youPaySecond": "Now it's your turn to pay {amount} {currency} to {username}.", "youConfirmSecond": "Please confirm that you received the final payment of {amount} {currency} from {username}.", "waitingSecondPayment": "Waiting for {username} to send you {amount} {currency}.", "waitingSecondConfirmation": "Waiting for {username} to confirm receipt of your payment.", "completedExchange": "The exchange of <strong>{amountA} {currencyA}</strong> for <strong>{amountB} {currencyB}</strong> is complete.", "cancellationReason": "Reason", "disputeReason": "Reason", "noReasonProvided": "No reason provided.", "adminReview": "An admin will review.", "followInstructions": "Please follow the instructions."}, "subSteps": {"agreementReached": "Agreement reached on first payer", "paymentInfoProvided": "Payment information provided by both parties", "firstPaymentDeclared": "First payment declared by {username}", "firstPaymentConfirmed": "First payment confirmed by {username}", "secondPaymentDeclared": "Second payment declared by {username}", "secondPaymentConfirmed": "Final payment confirmed by {username}", "allStepsCompleted": "All transaction steps completed successfully", "transactionWasCancelled": "Transaction was cancelled", "transactionWasDisputed": "Transaction was disputed", "designateFirstPayer": "Designate who pays first", "userMakesPayment": "{username} makes payment", "userConfirmsReceipt": "{username} confirms receipt", "transactionComplete": "Transaction Complete!", "transactionCancelled": "Transaction Cancelled", "transactionDisputed": "Transaction Disputed", "designationPending": "Both parties must agree on who pays first. Use the negotiation tools below to reach an agreement.", "youPayFirst": "You have been designated to pay first. Please send {amount} {currency} to {username} and then declare your payment.", "youConfirmPayment": "Please confirm that you have received {amount} {currency} from {username}.", "otherPartyPaying": "{username} will first pay {amount} {currency} to you. You will confirm after receiving.", "otherPartyConfirming": "Waiting for {username} to confirm receipt of your {amount} {currency} payment.", "youPaySecond": "Now it's your turn to pay {amount} {currency} to {username}.", "youConfirmSecond": "Please confirm that you have received the final payment of {amount} {currency} from {username}.", "waitingSecondPayment": "Waiting for {username} to send {amount} {currency}.", "waitingSecondConfirmation": "Waiting for {username} to confirm receipt of your payment.", "completedExchange": "Exchange of <strong>{amountA} {currencyA}</strong> for <strong>{amountB} {currencyB}</strong> completed.", "cancellationReason": "Reason", "disputeReason": "Reason", "noReasonProvided": "No reason provided.", "adminReview": "An admin will review.", "followInstructions": "Please follow the instructions."}, "buttons": {"declarePayment": "Declare Payment", "confirmReceipt": "Confirm Receipt", "cancelTransaction": "Cancel Transaction", "disputeTransaction": "Dispute Transaction", "agreeToProposal": "Agree to {target}", "agreeToSystemRecommendation": "Agree to System Recommendation", "agreeToUserProposal": "Agree to {username}'s Proposal", "proposeOtherPaysFirst": "Propose {username} Pays First Instead", "useProfileDetails": "Use Profile Payment Details"}, "timer": {"timeRemaining": "Time remaining:", "timeElapsed": "Time elapsed:", "timeLeft": "Time left:", "timePassed": "Time passed:", "paymentWindow": "Payment deadline:", "confirmationWindow": "Confirmation window:", "expired": "EXPIRED"}, "negotiation": {"systemRecommendation": "System recommends <strong>{username}</strong> pays first.", "systemRecommendationReason": "Reason: {reason}", "reasonReputation": "{username} has a lower reputation level and should pay first.", "reasonCurrency": "{username} is providing {currency} and should pay first.", "reasonCurrencyGeneric": "{username} is providing the local currency and should pay first.", "reasonOfferCreator": "{username} created the offer and should pay first.", "proposalFrom": "Proposal from {username}", "systemProposal": "System Recommendation", "waitingForResponse": "Waiting for the other party to respond to your proposal.", "bothPartiesAgreed": "Both parties have agreed to the current proposal.", "negotiationFinalized": "Negotiation has been finalized.", "youAgreed": "You have agreed to this proposal.", "otherPartyAgreed": "{username} has agreed to this proposal.", "agreementStatus": "Agreement Status", "pendingResponse": "Pending Response", "finalized": "Finalized", "youAgreedWaitingForFinalize": "You agreed to {username}'s proposal. Waiting for them to finalize or for system processing.", "awaitingYourResponse": "Awaiting your response to {username}'s proposal.", "youAgreedSystemWaiting": "You agreed to the system recommendation. Waiting for {username}.", "otherPartyAgreedSystemWaiting": "{username} agreed to the system recommendation. Waiting for you.", "bothPartiesAgreedFinalizing": "Both parties agreed to the system recommendation. Finalizing...", "agreementReached": "Agreement reached! {username} will pay first.", "negotiationStatus": "Negotiation Status", "proposalActive": "Proposal Active", "agreementReachedTitle": "Agreement Reached", "proposal": "Proposal", "usersProposal": "{username}'s Proposal", "userProposal": "Proposal from User", "reasonLabel": "Reason", "proposedPayer": "Proposed payer: {username}", "systemRecommendationDetermining": "System is determining recommendation..."}, "messages": {"paymentDeclared": "Payment has been declared successfully", "paymentConfirmed": "Payment receipt has been confirmed", "transactionCancelled": "Transaction has been cancelled", "transactionDisputed": "Transaction dispute has been submitted", "proposalSubmitted": "Proposal submitted successfully", "agreementConfirmed": "You have agreed to the current proposal", "paymentDetailsSubmitted": "Payment receiving details submitted successfully", "profileDetailsConfirmed": "Profile details confirmed for this transaction", "errorOccurred": "An error occurred. Please try again.", "paymentGateRequired": "Please provide your payment information to continue", "waitingForOtherParty": "Waiting for the other party to provide their payment information"}, "modals": {"declarePaymentTitle": "Declare Payment Sent", "trackingNumberLabel": "Tracking Number (optional)", "trackingNumberPlaceholder": "Enter tracking number if available", "cancelTransactionTitle": "Cancel Transaction", "cancelReasonLabel": "Reason for cancellation", "cancelReasonPlaceholder": "Please provide a reason for cancelling", "disputeTransactionTitle": "Dispute Transaction", "disputeReasonLabel": "Reason for dispute", "disputeReasonPlaceholder": "Please describe the issue", "proposalTitle": "Propose {username} Pays First", "proposalExplanation": "You are proposing that {username} should pay first instead of you.", "proposalMessageLabel": "Message to support your proposal (optional)", "proposalMessagePlaceholder": "Explain why you think {username} should pay first (e.g., 'I think you should pay first since you have higher reputation')", "submitProposal": "Propose {username} Pays First"}}