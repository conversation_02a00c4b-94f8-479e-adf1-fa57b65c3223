# Mobile-First UI Development for MUNygo

## Mobile-First Design Philosophy

All UI/UX design decisions must prioritize mobile devices as the primary target. Desktop layouts are progressive enhancements of the mobile experience.

## Responsive Design Standards

- Start with mobile viewport (320px-768px) as the base design
- Use CSS media queries to enhance for tablet (768px-1024px) and desktop (1024px+)
- Touch-friendly interface elements with minimum 44px touch targets
- Optimize for thumb navigation and one-handed usage
- Prioritize vertical scrolling over horizontal layouts

## Mobile-First Component Pattern

```vue
<template>
  <!-- Mobile-optimized layout by default -->
  <div class="mobile-container">
    <!-- Stack elements vertically for mobile -->
    <div class="mobile-stack">
      <!-- Content optimized for small screens -->
    </div>
    
    <!-- Enhanced layout for larger screens -->
    <div class="desktop-enhancement">
      <!-- Additional features for desktop -->
    </div>
  </div>
</template>

<style scoped>
/* Mobile-first CSS */
.mobile-container {
  padding: 1rem;
  /* Mobile styles as default */
}

/* Progressive enhancement for larger screens */
@media (min-width: 768px) {
  .mobile-container {
    padding: 2rem;
    /* Tablet enhancements */
  }
}

@media (min-width: 1024px) {
  .mobile-container {
    /* Desktop enhancements */
  }
}
</style>
```

## Naive UI Mobile Optimization

- Use responsive grid systems: `n-grid` with mobile-first breakpoints
- Implement mobile-friendly components: `n-drawer` for navigation, `n-modal` with full-screen mobile behavior
- Leverage responsive spacing utilities and mobile-optimized form controls
- Ensure proper touch feedback with `n-button` size variants ("large" for mobile)

## Skeleton Screen Standards

### Mobile-First Skeleton Design

All skeleton screens must prioritize mobile experience with touch-friendly proportions:

```vue
<template>
  <div class="skeleton-container" data-testid="content-skeleton">
    <!-- Mobile-optimized skeleton -->
    <div class="skeleton-header">
      <div class="skeleton-avatar"></div>
      <div class="skeleton-text-lines">
        <div class="skeleton-line"></div>
        <div class="skeleton-line short"></div>
      </div>
    </div>
    <div class="skeleton-content">
      <div class="skeleton-line"></div>
      <div class="skeleton-line medium"></div>
    </div>
  </div>
</template>

<style scoped>
.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 0.75rem;
}

.skeleton-line.short { width: 60%; }
.skeleton-line.medium { width: 80%; }

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
</style>
```

### Skeleton Requirements

Implement skeleton screens for ALL async loading scenarios:

- **List Views**: Skeleton cards matching expected content layout
- **Detail Views**: Skeleton elements for headers, content, action buttons
- **Forms**: Skeleton inputs while loading initial data
- **Chat/Messages**: Skeleton message bubbles during loading
- **User Profiles**: Skeleton avatars, text lines, stats sections

### Skeleton State Management

```vue
<script setup lang="ts">
const loading = ref(true)
const data = ref(null)

onMounted(async () => {
  try {
    loading.value = true
    data.value = await fetchData()
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <SkeletonCard v-if="loading" data-testid="content-skeleton" />
  <ContentCard v-else-if="data" data-testid="content-loaded" :data="data" />
  <EmptyState v-else data-testid="content-empty" />
</template>
```

## Data Test ID Requirements

### Mandatory Test IDs

ALL interactive elements must include `data-testid` attributes:

```vue
<template>
  <div class="offer-card" data-testid="offer-card">
    <h3 data-testid="offer-title">{{ offer.title }}</h3>
    <n-button data-testid="express-interest-btn" @click="handleInterest">
      Express Interest
    </n-button>
    <n-modal v-model:show="showModal" data-testid="offer-details-modal">
      <div data-testid="modal-content">
        <!-- Modal content -->
      </div>
    </n-modal>
  </div>
</template>
```

### Test ID Naming Patterns

- Buttons: `[action]-btn`
- Forms: `[form-name]-form`
- Inputs: `[field-name]-input`
- Cards: `[content-type]-card`
- Modals: `[modal-type]-modal`
- Loading states: `[content]-skeleton`, `[content]-loading`

## Mobile Performance Priorities

- Lazy load components and images for faster mobile loading
- Minimize bundle size for mobile network constraints
- Use progressive web app features for mobile-like experience
- Implement touch gestures and mobile-specific interactions

## Touch-Friendly Design Standards

- Minimum 44px touch targets for all interactive elements
- Place primary actions within thumb reach (bottom 1/3 of screen)
- Provide visual feedback for touch interactions
- Optimize for one-handed usage patterns
- Ensure adequate spacing between touch targets (8px minimum)

## Responsive Breakpoint Strategy

```css
/* Mobile-first breakpoints */
/* Default styles apply to mobile (320px-767px) */

/* Tablet enhancement */
@media (min-width: 768px) {
  /* Tablet-specific enhancements */
}

/* Desktop enhancement */
@media (min-width: 1024px) {
  /* Desktop-specific enhancements */
}

/* Large desktop optimization */
@media (min-width: 1440px) {
  /* Large screen optimizations */
}
```

## Accessibility for Mobile

- Ensure mobile accessibility with proper contrast ratios
- Use appropriate text sizing that scales well on mobile
- Provide screen reader support for touch interactions
- Include proper ARIA labels for loading states and dynamic content
