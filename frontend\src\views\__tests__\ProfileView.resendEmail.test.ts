import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import { ref } from 'vue';
import ProfileView from '../ProfileView/ProfileView.vue';
import apiClient from '@/services/apiClient';
import { useAuthStore } from '@/stores/auth';

// Mock API client
vi.mock('@/services/apiClient', () => ({
  default: {
    post: vi.fn()
  }
}));

// Mock error handler
vi.mock('@/utils/errorHandler', () => ({
  handleError: vi.fn((error, message, defaultMessage) => {
    return defaultMessage || 'An error occurred';
  })
}));

// Mock Naive UI components
vi.mock('naive-ui', () => ({
  NCard: {
    template: '<div class="n-card"><div class="title">{{ title }}</div><slot /></div>',
    props: ['title']
  },
  NTag: {
    template: '<span class="n-tag" :class="type"><slot /></span>',
    props: ['type', 'size']
  },
  NButton: {
    template: '<button class="n-button" :class="[type, size]" :disabled="loading" @click="$emit(\'click\')"><slot /></button>',
    props: ['type', 'size', 'text', 'loading'],
    emits: ['click']
  },
  NIcon: {
    template: '<span class="n-icon"><slot /></span>',
    props: ['size', 'color']
  },
  NAvatar: {
    template: '<div class="n-avatar" :style="{ width: size + \'px\', height: size + \'px\' }"><slot /></div>',
    props: ['size', 'src']
  },
  NSpace: {
    template: '<div class="n-space"><slot /></div>'
  },
  NGrid: {
    template: '<div class="n-grid"><slot /></div>',
    props: ['cols', 'xGap', 'yGap', 'responsive']
  },
  NGridItem: {
    template: '<div class="n-grid-item"><slot /></div>',
    props: ['span', 'md']
  },
  NSpin: {
    template: '<div class="n-spin" :class="{ spinning: show }"><slot /></div>',
    props: ['show']
  },
  NForm: {
    template: '<form class="n-form"><slot /></form>',
    props: ['model']
  },
  NFormItem: {
    template: '<div class="n-form-item"><slot /></div>',
    props: ['label', 'path']
  },
  NInput: {
    template: '<input class="n-input" :value="value" @input="$emit(\'update:value\', $event.target.value)" />',
    props: ['value', 'placeholder', 'disabled'],
    emits: ['update:value']
  },
  NAlert: {
    template: '<div class="n-alert" :class="type"><slot /></div>',
    props: ['type', 'showIcon']
  },
  NText: {
    template: '<span class="n-text" :class="type"><slot /></span>',
    props: ['type']
  },
  NTooltip: {
    template: '<div class="n-tooltip"><slot /></div>'
  },
  NStatistic: {
    template: '<div class="n-statistic"><div class="label">{{ label }}</div><div class="value">{{ value }}</div></div>',
    props: ['label', 'value']
  },
  NProgress: {
    template: '<div class="n-progress"><div class="percentage">{{ percentage }}%</div></div>',
    props: ['percentage', 'color', 'status']
  },
  NResult: {
    template: '<div class="n-result" :class="status"><div class="title">{{ title }}</div><slot /></div>',
    props: ['status', 'title']
  },
  useMessage: () => ({
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  })
}));

// Mock Vue Router
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn()
  })
}));

// Mock icons
vi.mock('@vicons/antd', () => ({
  UserOutlined: { template: '<span>UserIcon</span>' },
  CameraOutlined: { template: '<span>CameraIcon</span>' },
  StarOutlined: { template: '<span>StarIcon</span>' },
  CalendarOutlined: { template: '<span>CalendarIcon</span>' },
  SecurityScanOutlined: { template: '<span>SecurityIcon</span>' },
  MailOutlined: { template: '<span>MailIcon</span>' },
  PhoneOutlined: { template: '<span>PhoneIcon</span>' },
  SafetyOutlined: { template: '<span>SafetyIcon</span>' },
  CheckCircleOutlined: { template: '<span>CheckIcon</span>' },
  ClockCircleOutlined: { template: '<span>ClockIcon</span>' },
  QuestionCircleOutlined: { template: '<span>QuestionIcon</span>' },
  SendOutlined: { template: '<span>SendIcon</span>' },
  LockOutlined: { template: '<span>LockIcon</span>' },
  CheckOutlined: { template: '<span>CheckOutIcon</span>' },
  ReloadOutlined: { template: '<span>ReloadIcon</span>' },
  EditOutlined: { template: '<span>EditIcon</span>' },
  BarChartOutlined: { template: '<span>BarChartIcon</span>' },
  TrophyOutlined: { template: '<span>TrophyIcon</span>' },
  ShopOutlined: { template: '<span>ShopIcon</span>' },
  SettingOutlined: { template: '<span>SettingIcon</span>' },
  PlusOutlined: { template: '<span>PlusIcon</span>' },
  FileTextOutlined: { template: '<span>FileTextIcon</span>' },
  SearchOutlined: { template: '<span>SearchIcon</span>' },
  LoadingOutlined: { template: '<span>LoadingIcon</span>' }
}));

describe('ProfileView - Resend Email Verification', () => {
  let pinia: any;
  let authStore: any;
  
  beforeEach(() => {
    pinia = createPinia();
    setActivePinia(pinia);
    
    // Mock the auth store
    authStore = useAuthStore();
    authStore.user = ref({
      id: 'test-user-id',
      email: '<EMAIL>',
      emailVerified: false,
      phoneVerified: true,
      username: 'testuser',
      reputationScore: 25,
      reputationLevel: 2,
      createdAt: new Date('2024-01-01')
    });
    
    vi.clearAllMocks();
  });

  const createWrapper = () => {
    return mount(ProfileView, {
      global: {
        plugins: [pinia]
      }
    });
  };  describe('resendVerificationEmail method', () => {
    it('should successfully resend verification email', async () => {
      // Arrange
      const mockResponse = {
        data: {
          message: 'Verification email sent successfully. Please check your email.'
        }
      };
      (apiClient.post as any).mockResolvedValue(mockResponse);
      
      const wrapper = createWrapper();
      const vm = wrapper.vm as any;

      // Act
      await vm.resendVerificationEmail();

      // Assert
      expect(apiClient.post).toHaveBeenCalledWith('/auth/resend-verification-email');
      expect(vm.loadingResendEmail).toBe(false);
    });    it('should handle API errors correctly', async () => {
      // Arrange
      const mockError = {
        response: {
          data: {
            error: 'Email is already verified'
          }
        }
      };
      (apiClient.post as any).mockRejectedValue(mockError);
      
      const wrapper = createWrapper();
      const vm = wrapper.vm as any;

      // Act
      await vm.resendVerificationEmail();

      // Assert
      expect(apiClient.post).toHaveBeenCalledWith('/auth/resend-verification-email');
      expect(vm.loadingResendEmail).toBe(false);
    });    it('should set loading state correctly during API call', async () => {
      // Arrange
      let resolvePromise: any;
      const mockPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      (apiClient.post as any).mockReturnValue(mockPromise);
      
      const wrapper = createWrapper();
      const vm = wrapper.vm as any;

      // Act
      const resendPromise = vm.resendVerificationEmail();
      
      // Assert loading is true during the call
      expect(vm.loadingResendEmail).toBe(true);
      
      // Resolve the promise
      resolvePromise({
        data: { message: 'Success' }
      });
      await resendPromise;
      
      // Assert loading is false after completion
      expect(vm.loadingResendEmail).toBe(false);
    });    it('should reset loading state even when API call fails', async () => {
      // Arrange
      (apiClient.post as any).mockRejectedValue(new Error('Network error'));
      
      // Mock the message service before mounting
      const mockMessage = {
        success: vi.fn(),
        error: vi.fn()
      };
      
      const wrapper = mount(ProfileView, {
        global: {
          plugins: [pinia],
          provide: {
            message: mockMessage
          }
        }
      });
      const vm = wrapper.vm as any;

      // Act
      await vm.resendVerificationEmail();

      // Assert
      expect(vm.loadingResendEmail).toBe(false);
    });
  });
  describe('Resend Email Button', () => {
    it('should render resend email button when email is not verified', () => {
      // Arrange
      authStore.user.emailVerified = false;
      
      // Act
      const wrapper = createWrapper();

      // Assert
      const resendButton = wrapper.find('.resend-btn');
      expect(resendButton.exists()).toBe(true);
      expect(resendButton.text()).toBe('Resend Email');
    });

    it('should not render resend email button when email is verified', () => {
      // Arrange
      authStore.user.emailVerified = true;
      
      // Act
      const wrapper = createWrapper();

      // Assert
      const resendButton = wrapper.find('.resend-btn');
      expect(resendButton.exists()).toBe(false);
    });

    it('should call resendVerificationEmail when button is clicked', async () => {
      // Arrange
      authStore.user.emailVerified = false;
      (apiClient.post as any).mockResolvedValue({
        data: { message: 'Success' }
      });
      
      // Mock the message service before mounting
      const mockMessage = {
        success: vi.fn(),
        error: vi.fn()
      };
      
      const wrapper = mount(ProfileView, {
        global: {
          plugins: [pinia],
          provide: {
            message: mockMessage
          }
        }
      });
      const vm = wrapper.vm as any;

      // Act
      const resendButton = wrapper.find('.resend-btn');
      await resendButton.trigger('click');

      // Assert
      expect(apiClient.post).toHaveBeenCalledWith('/auth/resend-verification-email');
    });    it('should show loading state on button when API call is in progress', async () => {
      // Arrange
      authStore.user.emailVerified = false;
      let resolvePromise: any;
      const mockPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      (apiClient.post as any).mockReturnValue(mockPromise);
      
      // Mock the message service before mounting
      const mockMessage = {
        success: vi.fn(),
        error: vi.fn()
      };
      
      const wrapper = mount(ProfileView, {
        global: {
          plugins: [pinia],
          provide: {
            message: mockMessage
          }
        }
      });
      const vm = wrapper.vm as any;      // Act
      const resendButton = wrapper.find('.resend-btn');
      const clickPromise = resendButton.trigger('click');
      
      // Wait for next tick to allow reactive updates
      await wrapper.vm.$nextTick();

      // Assert button shows loading state
      expect(vm.loadingResendEmail).toBe(true);
      
      // Resolve the promise
      resolvePromise({
        data: { message: 'Success' }
      });
      await clickPromise;
      await wrapper.vm.$nextTick();
      
      // Assert button is no longer loading
      expect(vm.loadingResendEmail).toBe(false);
    });
  });
});
