#!/bin/bash

# Safe Docker Database Migration Script
# This script ensures <PERSON><PERSON> uses the correct database schema without losing data

echo "🔄 Safe Docker Database Migration and Rebuild"
echo "=============================================="

# Step 1: Stop Docker containers
echo "📛 Stopping Docker containers..."
docker-compose down

# Step 2: Clean up Docker images (force rebuild without cache)
echo "🧹 Cleaning up Docker images..."
docker-compose build --no-cache --pull

# Step 3: Start only the PostgreSQL database first
echo "🗄️ Starting PostgreSQL database..."
docker-compose up -d postgres

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
until docker-compose exec postgres pg_isready -U munygo_user -d munygo_db; do
  echo "Waiting for PostgreSQL..."
  sleep 2
done

echo "✅ PostgreSQL is ready!"

# Step 4: Run database migrations in Docker
echo "📊 Running database migrations..."
docker-compose run --rm backend npx prisma migrate deploy

# Step 5: Generate Prisma client
echo "🔧 Generating Prisma client..."
docker-compose run --rm backend npx prisma generate

# Step 6: Start backend service
echo "🚀 Starting backend service..."
docker-compose up -d backend

# Wait for backend to be healthy
echo "⏳ Waiting for backend to be healthy..."
until docker-compose ps backend | grep -q "healthy"; do
  echo "Waiting for backend to be healthy..."
  sleep 5
done

echo "✅ Backend is healthy!"

# Step 7: Start frontend service
echo "🎨 Starting frontend service..."
docker-compose up -d frontend

# Step 8: Verify all services are running
echo "🔍 Verifying all services..."
docker-compose ps

echo ""
echo "🎉 Docker deployment complete!"
echo ""
echo "📋 Services Status:"
echo "- PostgreSQL: $(docker-compose ps postgres | grep -q Up && echo "✅ Running" || echo "❌ Not running")"
echo "- Backend: $(docker-compose ps backend | grep -q Up && echo "✅ Running" || echo "❌ Not running")"  
echo "- Frontend: $(docker-compose ps frontend | grep -q Up && echo "✅ Running" || echo "❌ Not running")"
echo ""
echo "🌐 Access the application:"
echo "- Frontend: http://localhost:8080"
echo "- Backend API: http://localhost:3000"
echo ""
echo "🔧 Useful commands:"
echo "- View logs: docker-compose logs -f"
echo "- Access backend: docker-compose exec backend bash"
echo "- Access database: docker-compose exec postgres psql -U munygo_user -d munygo_db"
