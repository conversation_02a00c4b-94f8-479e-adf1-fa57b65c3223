// Quick test script to verify socket reconnection functionality
// Run this in browser console to test the connection mechanism

console.log('🧪 Testing Socket Reconnection Mechanism...');

// Check if socket manager is available
const socketManager = window.centralizedSocketManager || centralizedSocketManager;

if (!socketManager) {
  console.error('❌ CentralizedSocketManager not found');
} else {
  console.log('✅ CentralizedSocketManager found');
  
  // Check current connection status
  console.log('📊 Current connection status:');
  console.log('- Connected:', socketManager.isConnected());
  console.log('- Socket exists:', !!socketManager.getSocket());
  
  // Test manual reconnection
  console.log('🔄 Testing manual reconnection...');
  socketManager.forceReconnect()
    .then(() => {
      console.log('✅ Manual reconnection successful');
      console.log('- Connected after reconnect:', socketManager.isConnected());
    })
    .catch(error => {
      console.error('❌ Manual reconnection failed:', error);
    });
}

// Check connection store status
try {
  const connectionStore = useConnectionStore();
  console.log('📈 Connection Store Status:');
  console.log('- Quality:', connectionStore.connectionQuality);
  console.log('- Status:', connectionStore.connectionStatus);
  console.log('- Connected:', connectionStore.isConnected);
} catch (error) {
  console.log('⚠️ Could not access connection store (may be normal in console)');
}

console.log('🧪 Test script completed. Check above for results.');
