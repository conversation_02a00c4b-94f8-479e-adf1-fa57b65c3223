# Test script to verify enhanced Gemini API logging
Write-Host "Testing enhanced Gemini API logging..." -ForegroundColor Green

# Make sure backend is running
$backendUrl = "http://localhost:3000"
$testAudioEndpoint = "$backendUrl/api/ai/voice-to-report"

Write-Host "Checking if backend is running..." -ForegroundColor Yellow
try {
    $statusResponse = Invoke-RestMethod -Uri "$backendUrl/api/ai/status" -Method Get -TimeoutSec 5
    if ($statusResponse.success) {
        Write-Host "✅ Backend is running" -ForegroundColor Green
        Write-Host "AI Service Available: $($statusResponse.status.aiServiceAvailable)" -ForegroundColor Cyan
        Write-Host "Voice-to-Report Available: $($statusResponse.status.features.voiceToReport)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Backend status check failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Backend is not running. Please start with: .\start-dev.ps1" -ForegroundColor Red
    exit 1
}

Write-Host "`nNow testing the enhanced logging with a sample request..." -ForegroundColor Yellow
Write-Host "Watch the backend console for detailed logging output!" -ForegroundColor Cyan

# Create a simple test audio data (base64 encoded silence)
$testAudioData = "GkXfo59ChoEBQveBAULygQRC84EIQoKEd2VibUKHgQJChYECGFOAZwEAAAAAAAHTEU2bdLpNu4tTq4QVSalmU6yBoU27i1OrhBZUrmtTrIHGTbuMU6uEElTDZ1OsggEXTbuMU6uEHFO7a1OsggG97AEAAAAAAABZAEAAAAAAAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP/7kGQAAANwAKgpbAAAM4A1BSxgAAI2w4QB5kwAAy5gYAktgAAEAAAABaXRlbQAAAAAAAREAAAABmAEAAAAAAABjb3B5cmlnaHQAAAAAAREAAAAAJA=="

$requestBody = @{
    audioData = $testAudioData
    mimeType = "audio/webm;codecs=opus"
    duration = 2.5
    language = "en"
    userContext = @{
        currentPage = "test-page"
        userAgent = "PowerShell-Test"
        viewport = @{
            width = 1920
            height = 1080
        }
    }
    predefinedTags = @{
        "bug" = @("ui", "performance", "crash")
        "feature-request" = @("enhancement", "new-feature")
    }
} | ConvertTo-Json -Depth 10

Write-Host "Sending test request..." -ForegroundColor Yellow
Write-Host "Request size: $($requestBody.Length) bytes" -ForegroundColor Cyan

try {
    $headers = @{
        'Content-Type' = 'application/json'
    }
    
    # Use longer timeout to see if we get proper logging for long requests
    $response = Invoke-RestMethod -Uri $testAudioEndpoint -Method Post -Body $requestBody -Headers $headers -TimeoutSec 90
    
    Write-Host "✅ Request completed successfully!" -ForegroundColor Green
    Write-Host "Response success: $($response.success)" -ForegroundColor Cyan
    Write-Host "Processing time: $($response.processingTime)ms" -ForegroundColor Cyan
    
    if ($response.success) {
        Write-Host "Generated report title: $($response.generatedReport.title)" -ForegroundColor Green
        Write-Host "Transcription length: $($response.transcription.Length) chars" -ForegroundColor Green
        if ($response.isFallback) {
            Write-Host "⚠️ Report was generated using FALLBACK processing!" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Request failed: $($response.error)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Request failed with exception:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    # Check if it's a timeout
    if ($_.Exception.Message -like "*timeout*") {
        Write-Host "⏰ This appears to be a timeout - check the backend logs for:" -ForegroundColor Yellow
        Write-Host "  - Detailed timing information" -ForegroundColor White
        Write-Host "  - Fallback processing attempts" -ForegroundColor White
        Write-Host "  - Gemini API error details" -ForegroundColor White
    }
}

Write-Host "`n🔍 Check the backend console output for detailed logging including:" -ForegroundColor Cyan
Write-Host "  - Request timing breakdown" -ForegroundColor White
Write-Host "  - Gemini API call details" -ForegroundColor White
Write-Host "  - Error categorization and details" -ForegroundColor White
Write-Host "  - Processing stage timings" -ForegroundColor White
