/*
  Warnings:

  - You are about to drop the column `pushSubscription` on the `User` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId,currency,isDefaultForUser]` on the table `PaymentReceivingInfo` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "PaymentMethodType" AS ENUM ('BANK_TRANSFER', 'DIGITAL_WALLET', 'CRYPTO_WALLET', 'MO<PERSON><PERSON>_MONEY', 'CASH_PICKUP');

-- AlterTable
ALTER TABLE "PaymentReceivingInfo" ADD COLUMN     "bsb" TEXT,
ADD COLUMN     "currency" TEXT NOT NULL DEFAULT 'IRR',
ADD COLUMN     "iban" TEXT,
ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "notes" TEXT,
ADD COLUMN     "paymentMethodType" "PaymentMethodType" NOT NULL DEFAULT 'BANK_TRANSFER',
ADD COLUMN     "routingNumber" TEXT,
ADD COLUMN     "sortCode" TEXT,
ADD COLUMN     "swiftCode" TEXT;

-- AlterTable
ALTER TABLE "User" DROP COLUMN "pushSubscription";

-- AlterTable
ALTER TABLE "debug_report_comments" ALTER COLUMN "user_id" DROP NOT NULL;

-- CreateIndex
CREATE INDEX "PaymentReceivingInfo_userId_currency_idx" ON "PaymentReceivingInfo"("userId", "currency");

-- CreateIndex
CREATE INDEX "PaymentReceivingInfo_userId_currency_isActive_idx" ON "PaymentReceivingInfo"("userId", "currency", "isActive");

-- CreateIndex
CREATE UNIQUE INDEX "PaymentReceivingInfo_userId_currency_default_key" ON "PaymentReceivingInfo"("userId", "currency", "isDefaultForUser");
