import { describe, it, expect } from 'vitest'
import { getPrecision, formatAmount, formatAmountForDisplay } from '../currencyUtils'

describe('currencyUtils', () => {
  describe('getPrecision', () => {
    it('should return 0 for IRR', () => {
      expect(getPrecision('IRR')).toBe(0)
      expect(getPrecision('irr')).toBe(0)
    })

    it('should return 2 for CAD', () => {
      expect(getPrecision('CAD')).toBe(2)
      expect(getPrecision('cad')).toBe(2)
    })

    it('should return 2 for USD and EUR', () => {
      expect(getPrecision('USD')).toBe(2)
      expect(getPrecision('EUR')).toBe(2)
    })

    it('should return 8 for BTC and ETH', () => {
      expect(getPrecision('BTC')).toBe(8)
      expect(getPrecision('ETH')).toBe(8)
    })

    it('should return 2 for unknown currencies', () => {
      expect(getPrecision('UNKNOWN')).toBe(2)
      expect(getPrecision('')).toBe(2)
      expect(getPrecision(null)).toBe(2)
      expect(getPrecision(undefined)).toBe(2)
    })
  })

  describe('formatAmount', () => {
    it('should format IRR amounts without decimals', () => {
      expect(formatAmount(50000, 'IRR')).toBe('50,000')
      expect(formatAmount(1234567, 'IRR')).toBe('1,234,567')
    })

    it('should format CAD amounts with 2 decimals', () => {
      expect(formatAmount(100, 'CAD')).toBe('100.00')
      expect(formatAmount(100.5, 'CAD')).toBe('100.50')
      expect(formatAmount(100.123, 'CAD')).toBe('100.12')
    })

    it('should handle edge cases', () => {
      expect(formatAmount(0, 'IRR')).toBe('0')
      expect(formatAmount(0, 'CAD')).toBe('0.00')
    })
  })

  describe('formatAmountForDisplay', () => {
    it('should format regular amounts using currency precision', () => {
      expect(formatAmountForDisplay(50000, 'IRR')).toBe('50,000')
      expect(formatAmountForDisplay(100, 'CAD')).toBe('100.00')
    })

    it('should format rates with up to 8 decimals when isRate=true', () => {
      const rate = 50123.123456789
      // For IRR, even rates use 0 decimals since it's a whole number currency
      const result = formatAmountForDisplay(rate, 'IRR', true)
      expect(result).toBe('50,123') // Should round to 0 decimals for IRR
      
      // For CAD, rates should allow more decimals
      const cadResult = formatAmountForDisplay(rate, 'CAD', true)
      expect(cadResult).toContain('50,123.12345679') // Should allow more decimals for CAD rates
    })

    it('should respect currency precision as minimum when isRate=true', () => {
      const rate = 100.5
      expect(formatAmountForDisplay(rate, 'CAD', true)).toBe('100.50')
      
      // IRR should still format to 0 decimals even for rates since it's a whole number currency
      expect(formatAmountForDisplay(rate, 'IRR', true)).toBe('101') // Should round 100.5 to 101 for IRR
    })
  })
})
