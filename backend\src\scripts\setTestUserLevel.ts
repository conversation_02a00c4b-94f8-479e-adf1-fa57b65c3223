import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Utility to get a date N days ago
function daysAgo(days: number): Date {
  return new Date(Date.now() - days * 24 * 60 * 60 * 1000);
}

// Helper function to calculate reputation level based on score (matches auth.ts)
function calculateReputationLevel(score: number): number {
  if (score < 10) return 1; // Newcomer
  if (score < 25) return 2; // Verified
  if (score < 40) return 3; // Reliable
  if (score < 60) return 4; // Trusted
  return 5; // Elite
}

async function setUserLevel(email: string, level: 1 | 2 | 3 | 4 | 5) {
  // Calculate appropriate score and verification status for the target level
  let emailVerified: boolean;
  let phoneVerified: boolean;
  let createdAt: Date;
  let reputationScore: number;

  switch (level) {
    case 1: // Newcomer (0-9 points)
      emailVerified = false;
      phoneVerified = false;
      createdAt = new Date();
      reputationScore = 0;
      break;
    case 2: // Verified (10-24 points)
      emailVerified = true;
      phoneVerified = false;
      createdAt = daysAgo(5);
      reputationScore = 15; // 10 (email) + 5 (age)
      break;
    case 3: // Reliable (25-39 points)
      emailVerified = true;
      phoneVerified = true;
      createdAt = daysAgo(5);
      reputationScore = 30; // 10 (email) + 15 (phone) + 5 (age)
      break;
    case 4: // Trusted (40-59 points)
      emailVerified = true;
      phoneVerified = true;
      createdAt = daysAgo(20);
      reputationScore = 45; // 10 (email) + 15 (phone) + 20 (age)
      break;
    case 5: // Elite (60+ points)
      emailVerified = true;
      phoneVerified = true;
      createdAt = daysAgo(40);
      reputationScore = 100; // High score to ensure level 5
      break;
    default:
      throw new Error(`Unsupported level: ${level}. Supported levels: 1, 2, 3, 4, 5`);
  }

  await prisma.user.updateMany({
    where: { email },
    data: {
      emailVerified,
      phoneVerified,
      createdAt,
      reputationScore,
      reputationLevel: level,
    },
  });

  const levelNames = {
    1: 'Newcomer',
    2: 'Verified', 
    3: 'Reliable',
    4: 'Trusted',
    5: 'Elite'
  };

  console.log(`User ${email} set to Level ${level} (${levelNames[level]}) test state:`);
  console.log(`  - Email verified: ${emailVerified}`);
  console.log(`  - Phone verified: ${phoneVerified}`);
  console.log(`  - Reputation score: ${reputationScore}`);
  console.log(`  - Created at: ${createdAt.toISOString()}`);
}

// Usage: npx ts-node setTestUserLevel.ts <email> <level>
const [,, email, levelStr] = process.argv;
if (!email || !levelStr || !['1','2','3','4','5'].includes(levelStr)) {
  console.error('Usage: npx ts-node setTestUserLevel.ts <email> <level: 1|2|3|4|5>');
  console.error('Levels: 1=Newcomer, 2=Verified, 3=Reliable, 4=Trusted, 5=Elite');
  process.exit(1);
}

setUserLevel(email, Number(levelStr) as 1 | 2 | 3 | 4 | 5)
  .then(() => process.exit(0))
  .catch((err) => { console.error(err); process.exit(1); });
