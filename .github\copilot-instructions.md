# GitHub Copilot Instructions - MUNygo Project

## Context-Driven Development with Context7

This is a **mobile-first P2P currency exchange platform** built with Vue 3 + Hono + PostgreSQL. When working on specific areas, Context7 will automatically load relevant specialized guidelines:

- **🧪 Testing**: Reference `.github/contexts/testing-guidelines.md` for unit tests, integration tests, data-testid patterns
- **📱 Mobile UI**: Reference `.github/contexts/mobile-ui-patterns.md` for components, responsive design, skeleton screens
- **🔧 Backend**: Reference `.github/contexts/backend-development.md` for API development, services, dependency injection
- **🗄️ Database**: Reference `.github/contexts/database-operations.md` for Prisma, migrations, queries
- **🚀 Deployment**: Reference `.github/contexts/deployment-workflows.md` for Docker, production deployment, CI/CD

## Core Universal Principles

### Mobile-First Architecture
- **Primary Target**: Mobile devices (320px-768px)
- **Progressive Enhancement**: Tablet (768px+) and desktop (1024px+) are enhancements
- **Touch Targets**: Minimum 44px for all interactive elements
- **Thumb Navigation**: Primary actions in bottom 1/3 of screen

### TypeScript Standards
- **Strict Mode**: Explicit return types, no implicit any
- **Type Imports**: Use `import type` for type-only imports
- **Interface Naming**: Descriptive names without prefixes (`UserProfile`, `OfferDetails`)

### Vue 3 Patterns
- **Composition API**: Always use `<script setup>`
- **Reactive Data**: `ref()` for primitives, `reactive()` for objects
- **CSS Scoping**: Always use `<style scoped>`

### Dependency Injection (Backend)
- **No Circular Imports**: Never import from main `index.ts` in service modules
- **Constructor Injection**: Services receive dependencies via constructor
- **Factory Pattern**: Routes as factory functions accepting services

## Critical Implementation Requirements

### Data Test IDs (Frontend)
**MANDATORY**: All interactive elements must include `data-testid` attributes:

```vue
<n-button data-testid="submit-btn" @click="handleSubmit">Submit</n-button>
<div data-testid="offer-card">{{ offer.title }}</div>
<n-modal data-testid="confirmation-modal">Content</n-modal>
```

**Naming Convention**: `[action]-btn`, `[content]-card`, `[modal-type]-modal`

### Skeleton Screens (Frontend)
**MANDATORY**: Implement skeleton placeholders for ALL loading states:

```vue
<template>
  <SkeletonCard v-if="loading" data-testid="content-skeleton" />
  <ContentCard v-else-if="data" data-testid="content-loaded" :data="data" />
  <EmptyState v-else data-testid="content-empty" />
</template>
```

### Socket.IO Management
**CRITICAL**: Always use `centralizedSocketManager.ts` - never create direct socket connections:

```typescript
import { centralizedSocketManager } from '@/services/centralizedSocketManager'

// Register events in store actions
centralizedSocketManager.on('offer_updated', handleOfferUpdate)
```

## Technology Stack

### Frontend
- **Framework**: Vue 3 + TypeScript + Vite
- **State**: Pinia stores with persistence
- **UI Library**: Naive UI with mobile-first components
- **Testing**: Vitest with happy-dom environment
- **i18n**: Vue I18n (Persian FA + English EN)

### Backend
- **Framework**: Hono (TypeScript web framework)
- **Database**: PostgreSQL with Prisma ORM
- **Real-time**: Socket.IO for live updates
- **Auth**: JWT tokens + email/phone verification (Twilio)
- **Testing**: Vitest with Node.js environment

### Infrastructure
- **Development**: Docker containers + PostgreSQL
- **Deployment**: Docker Compose with migration handling
- **Database**: Prisma migrations with proper rollback support

## Project Structure Quick Reference

```
MUNygo/
├── backend/
│   ├── src/
│   │   ├── index.ts           # Main Hono app entry
│   │   ├── routes/            # API endpoints
│   │   ├── services/          # Business logic
│   │   ├── middleware/        # Auth, validation
│   │   └── types/             # TypeScript definitions
│   └── prisma/
│       ├── schema.prisma      # Database schema
│       └── migrations/        # SQL migrations
├── frontend/
│   ├── src/
│   │   ├── components/        # Vue components (mobile-first)
│   │   ├── stores/            # Pinia state management
│   │   ├── services/          # API clients, socket manager
│   │   ├── views/             # Page-level components
│   │   └── types/             # Frontend type definitions
└── .github/
    └── contexts/              # Specialized development guidelines
```

## Quick Commands

```powershell
# Development
.\dev-start.ps1              # Start PostgreSQL + instructions
cd .\backend\; npm run dev    # Backend server (localhost:3000)
cd .\frontend\; npm run dev   # Frontend server (localhost:5173)

# Testing
cd .\backend\; npm test       # Backend tests
cd .\frontend\; npm test      # Frontend tests
npm run test:watch          # Watch mode (run in respective folder)

# Database
cd .\backend\; npm run prisma:migrate      # Create migration
cd .\backend\; npm run prisma:studio       # Database GUI

# Deployment
.\deploy-complete.ps1       # Full production deployment
```

**CRITICAL COMMAND STRUCTURE**: Always specify the correct directory before running npm commands:
- **Backend commands**: MUST start with `cd .\backend\;`
- **Frontend commands**: MUST start with `cd .\frontend\`
- **Never run**: `npm run dev` from root - this will fail
- **Always run**: `cd .\backend\; npm run dev` or `cd .\frontend\; npm run dev`

## Development Status

**Production-Ready Features:**
- ✅ Complete authentication system (JWT + email/phone verification)
- ✅ Mobile-first responsive design across all components
- ✅ Real-time P2P currency exchange with Socket.IO
- ✅ Advanced transaction flow with payment declarations
- ✅ Centralized notification system with persistence
- ✅ Full internationalization (Persian + English)
- ✅ Comprehensive testing framework with proven patterns

**Current Focus:**
- 🔄 UI/UX redesign with enhanced mobile experience
- 🔄 Performance optimization for mobile networks
- 🔄 Advanced matching algorithms for automatic offer pairing

## Universal Code Standards

### Directory Navigation for Commands
**MANDATORY**: All npm commands must specify the correct directory:

```powershell
# ✅ CORRECT - Backend commands
cd .\backend\; npm run dev
cd \backend\; npm test
cd \backend\; npm run prisma:migrate
cd \backend\; npm run build

# ✅ CORRECT - Frontend commands  
cd .\frontend\; npm run dev
cd .\frontend\; npm test
cd .\frontend\; npm run build

# ❌ WRONG - Never run from root
npm run dev          # This will fail - no package.json in root
npm test            # This will fail - no scripts in root
npm run build       # This will fail - no build scripts in root
```

**AI Model Instructions**: When generating terminal commands that involve npm, yarn, or any package manager commands, ALWAYS prefix with the appropriate directory change (`cd .\backend\;` or `cd .\frontend\;`).

### Error Handling
```typescript
// Use centralized error handler
import { handleError } from '@/utils/errorHandler'

try {
  await apiCall()
} catch (error) {
  handleError(error, 'Operation failed')
}
```

### API Responses
```typescript
// Consistent response format
return c.json({
  success: true,
  data: result,
  message: 'Operation completed'
})
```

### Component Structure
```vue
<script setup lang="ts">
// 1. Vue imports
// 2. Third-party imports  
// 3. Internal services/stores
// 4. Types
// 5. Components
</script>

<template>
  <!-- Mobile-first layout with data-testid -->
</template>

<style scoped>
/* Mobile-first CSS with progressive enhancement */
</style>
```

---

**Remember**: This main file provides universal guidelines. Context7 will automatically load specialized context files based on your specific development tasks (testing, UI components, backend services, database operations, or deployment workflows).