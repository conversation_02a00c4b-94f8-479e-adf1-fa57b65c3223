import apiClient from './apiClient';

export async function acceptInterest(interestId: string): Promise<{ chatSessionId: string }> {
  const response = await apiClient.post(`/offers/interests/${interestId}/accept`);
  return response.data;
}

export async function declineInterest(interestId: string, reasonCode?: string): Promise<{ interestId: string; reasonCode?: string }> {
  const response = await apiClient.post(`/offers/interests/${interestId}/decline`, { reasonCode });
  return response.data;
}
