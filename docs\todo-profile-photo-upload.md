# TODO: Profile Photo Upload Feature

## Task: Implement Profile Photo Upload in ProfileView.vue

### Description
Add functionality to allow users to upload and change their profile photo from the Profile page. The UI already includes a 'Change Photo' button in `ProfileView.vue`, but it is currently non-functional. This task involves implementing the backend and frontend logic to support profile photo uploads.

### Requirements
- Add a file input (hidden or modal) triggered by the 'Change Photo' button.
- Allow users to select and upload an image file (JPG, PNG, etc.).
- Validate file type and size on the frontend before upload.
- Implement an API endpoint in the backend to accept and store the uploaded image (e.g., in cloud storage or local filesystem for dev).
- Update the user's profile with the new avatar URL after successful upload.
- Display the new avatar immediately after upload (optimistic UI update if possible).
- Handle errors gracefully (invalid file, upload failure, etc.).
- Ensure type safety and follow dependency injection best practices for any new services.

### References
- See the `<n-button class="change-avatar-btn">` in `frontend/src/views/ProfileView/ProfileView.vue`.
- Follow project conventions for file upload, API design, and state management.

### Priority
- MVP+ (not required for initial launch, but important for user experience)

---
