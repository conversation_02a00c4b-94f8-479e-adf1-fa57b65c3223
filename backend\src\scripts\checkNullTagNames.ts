import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkNullTagNames() {
  console.log('🔍 Checking for null tagName records...');

  try {    // Check all DebugReportTag records
    const allReportTags = await prisma.debugReportTag.findMany({
      select: {
        id: true,
        tagName: true,
        tagId: true,
        reportId: true,
        origin: true
      }
    });

    console.log(`📊 Total DebugReportTag records: ${allReportTags.length}`);

    // Group by conditions
    const nullTagName = allReportTags.filter(t => t.tagName === null);
    const hasTagName = allReportTags.filter(t => t.tagName !== null);
    const hasTagId = allReportTags.filter(t => t.tagId !== null);
    const emptyTagName = allReportTags.filter(t => t.tagName === '');

    console.log(`📋 Records with null tagName: ${nullTagName.length}`);
    console.log(`📋 Records with non-null tagName: ${hasTagName.length}`);
    console.log(`📋 Records with tagId: ${hasTagId.length}`);
    console.log(`📋 Records with empty string tagName: ${emptyTagName.length}`);    if (nullTagName.length > 0) {
      console.log('\n❌ Null tagName records:');
      nullTagName.forEach(record => {
        console.log(`  ID: ${record.id}, reportId: ${record.reportId}, tagId: ${record.tagId}, origin: ${record.origin}`);
      });
    }

    if (emptyTagName.length > 0) {
      console.log('\n⚠️  Empty tagName records:');
      emptyTagName.forEach(record => {
        console.log(`  ID: ${record.id}, reportId: ${record.reportId}, tagId: ${record.tagId}, origin: ${record.origin}`);
      });
    }

    // Check unique tagNames
    const uniqueTagNames = [...new Set(hasTagName.map(t => t.tagName))];
    console.log(`🏷️  Unique tagNames: ${uniqueTagNames.length}`);
    console.log('Tag names:', uniqueTagNames);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkNullTagNames().catch(console.error);
