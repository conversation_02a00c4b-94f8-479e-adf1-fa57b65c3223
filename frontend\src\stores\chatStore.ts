import { ref, watch, computed } from 'vue';
import { defineStore } from 'pinia';
import type { Socket } from 'socket.io-client';
import apiClient from '@/services/apiClient';
import { useAuthStore } from './auth';
import centralizedSocketManager from '@/services/centralizedSocketManager';
import type { ChatMessage, ChatParticipant, ChatParticipantsInfo } from '@/types/chat';
import type { TransactionInfo } from '@/types/transaction';
import {
  CHAT_MESSAGE_SEND,
  CHAT_MESSAGE_RECEIVE,
  SYSTEM_MESSAGE_RECEIVE, // Import SYSTEM_MESSAGE_RECEIVE
  type ChatMessageSendPayload,
  type ChatMessageReceivePayload,
  type SystemMessagePayload, // Import SystemMessagePayload
} from '@/types/socketEvents';

export const useChatStore = defineStore('chat', () => {
  const authStore = useAuthStore();

  const currentChatSessionId = ref<string | null>(null);
  const messages = ref<ChatMessage[]>([]);
  const participants = ref<ChatParticipantsInfo | null>(null);
  const isLoadingHistory = ref(false);
  const isLoadingParticipants = ref(false);
  const sessionError = ref<string | null>(null);
  const isConnected = ref(false); // Tracks socket connection for chat purposes
  const transactionInfo = ref<TransactionInfo | null>(null);
  const isLoadingTransactionInfo = ref(false);
  // Socket event unsubscribe functions for cleanup
  let chatMessageUnsubscribe: (() => void) | null = null;
  let systemMessageUnsubscribe: (() => void) | null = null;
  let connectUnsubscribe: (() => void) | null = null;
  let disconnectUnsubscribe: (() => void) | null = null;
  let connectErrorUnsubscribe: (() => void) | null = null;

  // Define named handlers for connect/disconnect/error
  const handleSocketConnect = () => {
    isConnected.value = true;
    console.log('[ChatStore] Socket connected via chatStore handler.');
  };

  const handleSocketDisconnect = (reason: Socket.DisconnectReason) => {
    isConnected.value = false;
    console.warn('[ChatStore] Socket disconnected via chatStore handler. Reason:', reason);
  };

  const handleSocketConnectError = (err: Error) => {
    isConnected.value = false;
    console.error('[ChatStore] Socket connection error via chatStore handler:', err.message);
    sessionError.value = 'Chat connection error. Please check your connection or refresh.';
  };

  const otherParticipant = computed<ChatParticipant | null>(() => {
    if (!participants.value || !authStore.user) return null;
    return participants.value.otherUser;
  });

  const isLoadingSession = computed(() => isLoadingHistory.value || isLoadingParticipants.value);
  async function loadChatSession(chatSessionId: string) {
    if (currentChatSessionId.value === chatSessionId && messages.value.length > 0 && !sessionError.value) {
      console.log('[ChatStore] Chat session already loaded:', chatSessionId);
      // Even if loaded, ensure listeners are correctly set
      setupSocketListeners();
      return;
    }
    console.log('[ChatStore] Loading chat session:', chatSessionId);
    currentChatSessionId.value = chatSessionId;
    messages.value = [];
    participants.value = null;
    sessionError.value = null;
    transactionInfo.value = null;

    // Reset loading states before new session load
    isLoadingHistory.value = false;
    isLoadingParticipants.value = false;
    isLoadingTransactionInfo.value = false;

    await fetchChatParticipants(chatSessionId);
    await fetchChatHistory(chatSessionId);
    await fetchTransactionInfo(chatSessionId);

    // Ensure socket listeners are set up for this session
    console.log('[ChatStore] Chat session loaded, ensuring listeners are set up.');
    setupSocketListeners();
  }
  // Fetch transaction info for the chat session (offer type, amount, rate, etc.)
  async function fetchTransactionInfo(chatSessionId: string) {
    if (!chatSessionId) {
      console.log('[ChatStore] fetchTransactionInfo called with empty chatSessionId');
      return;
    }
    console.log('[ChatStore] Starting fetchTransactionInfo for chatSessionId:', chatSessionId);
    isLoadingTransactionInfo.value = true;
    try {
      // Assumes backend endpoint exists: /chat/:chatSessionId/transaction
      // and returns TransactionInfo structure
      console.log('[ChatStore] Making API call to:', `/chat/${chatSessionId}/transaction`);
      const response = await apiClient.get<TransactionInfo>(`/chat/${chatSessionId}/transaction`);
      transactionInfo.value = response.data;
      console.log('[ChatStore] Fetched transaction info:', transactionInfo.value);
    } catch (err: any) {
      console.error('[ChatStore] Error fetching transaction info:', err);
      console.error('[ChatStore] Error response:', err.response);
      transactionInfo.value = null;
    } finally {
      isLoadingTransactionInfo.value = false;
    }
  }
  // Update the fetchChatHistory method to ensure system messages are included
  const fetchChatHistory = async (chatSessionId: string) => {
    if (!authStore.isAuthenticated) {
      console.warn('[ChatStore] User not authenticated, cannot fetch chat history.');
      return;
    }
    console.log(`[ChatStore] Fetching chat history for session: ${chatSessionId}`);
    isLoadingHistory.value = true;
    sessionError.value = null; // Clear previous error
    try {
      // Make sure the API endpoint returns both regular and system messages
      const response = await apiClient.get(`/chat/${chatSessionId}/messages`, {
        params: {
          includeSystemMessages: true  // Make sure backend supports this parameter
        }
      });
      
      // Process and store messages
      messages.value = sortMessages(response.data);
      console.log(`[ChatStore] Fetched ${messages.value.length} messages for session ${chatSessionId}`);
    } catch (error: any) {
      console.error(`[ChatStore] Error fetching chat history:`, error);
      const errResponse = error.response;
      if (errResponse && errResponse.status === 401 && authStore.isAuthenticated) {
        console.warn('[ChatStore] Received 401 error for chat history while authStore reports authenticated. Logging out.');
        sessionError.value = 'Your session may have expired or become invalid. You are being logged out.';
        authStore.logout();
        return; 
      }
      const errorMessage = errResponse?.data?.error || 'Failed to fetch chat history';
      sessionError.value = errorMessage;
      if (errorMessage) console.error('[ChatStore] ' + errorMessage);
    } finally {
      isLoadingHistory.value = false;
    }
  }

  async function fetchChatParticipants(chatSessionId: string) {
    if (!chatSessionId) return;
    isLoadingParticipants.value = true;
    sessionError.value = null; // Clear previous error
    try {
      const response = await apiClient.get<ChatParticipantsInfo>(`/chat/${chatSessionId}/participants`);
      participants.value = response.data;
      console.log('[ChatStore] Fetched chat participants:', participants.value);
    } catch (err: any) {
      console.error('[ChatStore] Error fetching chat participants:', err);
      const errResponse = err.response;
      if (errResponse && errResponse.status === 401 && authStore.isAuthenticated) {
        console.warn('[ChatStore] Received 401 error for chat participants while authStore reports authenticated. Logging out.');
        sessionError.value = 'Your session may have expired or become invalid. You are being logged out.';
        authStore.logout();
        return;
      }
      const errorMessage = errResponse?.data?.error || 'Failed to fetch chat participants';
      sessionError.value = errorMessage;
      if (errorMessage) console.error('[ChatStore] ' + errorMessage);
    } finally {
      isLoadingParticipants.value = false;
    }
  }
  const sendMessage = (messageText: string) => {
    const socket = centralizedSocketManager.getSocket();
    if (!socket || !socket.connected) {
      console.error('[ChatStore] Socket not connected. Cannot send message.');
      sessionError.value = 'Not connected to chat. Please try again.';
      return;
    }
    if (!currentChatSessionId.value) {
      console.error('[ChatStore] No active chat session ID. Cannot send message.');
      return;
    }
    if (!messageText.trim()) {
      console.warn('[ChatStore] Attempted to send empty message.');
      return;
    }

    const payload: ChatMessageSendPayload = {
      chatSessionId: currentChatSessionId.value,
      messageText: messageText,
    };
    console.log('[ChatStore] Sending message:', payload);
    socket.emit(CHAT_MESSAGE_SEND, payload);
  }
  const handleChatMessageReceived = (payload: ChatMessageReceivePayload) => {
    console.log(`[ChatStore] handleChatMessageReceived: Payload for session ${payload.chatSessionId}. Current store session ID: ${currentChatSessionId.value}. Payload:`, JSON.parse(JSON.stringify(payload)));

    const newMessage: ChatMessage = {
      messageId: payload.messageId,
      chatSessionId: payload.chatSessionId,
      sender: {
        id: payload.sender.id,
        username: payload.sender.username,
        reputationLevel: payload.sender.reputationLevel,
      },
      content: payload.content,
      createdAt: payload.createdAt,
      isSystemMessage: payload.isSystemMessage ?? false,
    };

    console.log('[ChatStore] Mapped new user message:', newMessage);    if (newMessage.chatSessionId === currentChatSessionId.value) {
      const existingMessage = messages.value.find(m => m.messageId === payload.messageId);
      if (!existingMessage) {
        messages.value.push(newMessage);
        // Re-sort messages to ensure consistent ordering
        messages.value = sortMessages(messages.value);
        console.log('[ChatStore] Added received USER message to store. Messages count:', messages.value.length);
      } else {
        console.warn('[ChatStore] Received duplicate USER message, ignoring:', payload.messageId);
      }
    } else {
      console.log(`[ChatStore] Received USER message for a different chat session. Expected: ${currentChatSessionId.value}, Got: ${payload.chatSessionId}`);
    }
  }
  const handleSystemMessageReceived = (payload: SystemMessagePayload) => {
    console.log(`[ChatStore] handleSystemMessageReceived: Payload for session ${payload.chatSessionId}. Current store session ID: ${currentChatSessionId.value}. Payload:`, JSON.parse(JSON.stringify(payload)));

    const newSystemMessage: ChatMessage = {
      messageId: payload.messageId,
      chatSessionId: payload.chatSessionId,
      content: payload.content,
      createdAt: payload.createdAt,
      isSystemMessage: true, // Explicitly true
    };

    console.log('[ChatStore] Mapped new system message:', newSystemMessage);    if (newSystemMessage.chatSessionId === currentChatSessionId.value) {
      const existingMessage = messages.value.find(m => m.messageId === payload.messageId);
      if (!existingMessage) {
        messages.value.push(newSystemMessage);
        // Re-sort messages to ensure consistent ordering
        messages.value = sortMessages(messages.value);
        console.log('[ChatStore] Added received SYSTEM message to store. Messages count:', messages.value.length);
      } else {
        console.warn('[ChatStore] Received duplicate SYSTEM message, ignoring:', payload.messageId);
      }
    } else {
      console.log(`[ChatStore] Received SYSTEM message for a different chat session. Expected: ${currentChatSessionId.value}, Got: ${payload.chatSessionId}`);
    }
  }
  function setupSocketListeners() {
    console.log('[ChatStore] Setting up socket listeners with centralized socket manager.');
    
    // Clean up previous listeners
    if (chatMessageUnsubscribe) {
      chatMessageUnsubscribe();
      chatMessageUnsubscribe = null;
    }
    if (systemMessageUnsubscribe) {
      systemMessageUnsubscribe();
      systemMessageUnsubscribe = null;
    }
    if (connectUnsubscribe) {
      connectUnsubscribe();
      connectUnsubscribe = null;
    }
    if (disconnectUnsubscribe) {
      disconnectUnsubscribe();
      disconnectUnsubscribe = null;
    }
    if (connectErrorUnsubscribe) {
      connectErrorUnsubscribe();
      connectErrorUnsubscribe = null;
    }

    // Register handlers with centralized socket manager
    chatMessageUnsubscribe = centralizedSocketManager.on(CHAT_MESSAGE_RECEIVE, handleChatMessageReceived);
    systemMessageUnsubscribe = centralizedSocketManager.on(SYSTEM_MESSAGE_RECEIVE, handleSystemMessageReceived);
    connectUnsubscribe = centralizedSocketManager.on('connect', handleSocketConnect);
    disconnectUnsubscribe = centralizedSocketManager.on('disconnect', handleSocketDisconnect);
    connectErrorUnsubscribe = centralizedSocketManager.on('connect_error', handleSocketConnectError);
    
    console.log('[ChatStore] All chat socket listeners registered with centralized manager.');
  }
  function clearChatState() {
    console.log('[ChatStore] Clearing chat state for session:', currentChatSessionId.value);
    currentChatSessionId.value = null;
    messages.value = [];
    participants.value = null;
    sessionError.value = null;
    isLoadingHistory.value = false;
    isLoadingParticipants.value = false;
    isLoadingTransactionInfo.value = false;
    
    // Clean up socket listeners
    if (chatMessageUnsubscribe) {
      chatMessageUnsubscribe();
      chatMessageUnsubscribe = null;
    }
    if (systemMessageUnsubscribe) {
      systemMessageUnsubscribe();
      systemMessageUnsubscribe = null;
    }
    if (connectUnsubscribe) {
      connectUnsubscribe();
      connectUnsubscribe = null;
    }
    if (disconnectUnsubscribe) {
      disconnectUnsubscribe();
      disconnectUnsubscribe = null;
    }
    if (connectErrorUnsubscribe) {
      connectErrorUnsubscribe();
      connectErrorUnsubscribe = null;
    }
    console.log('[ChatStore] Removed all chat-specific socket listeners.');
  }
  // Initialize socket connection when store is used and user is authenticated
  watch(
    () => authStore.isAuthenticated,
    (isAuth, wasAuth) => {
      console.log(`[ChatStore] Auth state changed: isAuthenticated=${isAuth}, wasAuthenticated=${wasAuth}`);
      if (isAuth) {
        console.log('[ChatStore] User authenticated, setting up socket listeners.');
        const socket = centralizedSocketManager.getSocket();
        if (socket) {
          isConnected.value = socket.connected;
          console.log(`[ChatStore] Socket available. Connected: ${isConnected.value}`);
          setupSocketListeners();
        } else {
          console.log('[ChatStore] Socket not yet available, will be set up when socket connects.');
          isConnected.value = false;
        }
      } else {
        // User logged out or session expired
        console.log('[ChatStore] User not authenticated, clearing chat state.');
        isConnected.value = false;
        clearChatState();
      }
    },
    { immediate: true }
  );
  // Watch currentChatSessionId to re-setup listeners if it changes
  watch(currentChatSessionId, (newId, oldId) => {
    if (newId && newId !== oldId) {
        console.log(`[ChatStore] currentChatSessionId changed from ${oldId} to ${newId}.`);
        // Re-setup listeners for the new session
        setupSocketListeners();
    }
  });  // Sort messages deterministically by timestamp and then by ID for consistent ordering
  const sortMessages = (messages: ChatMessage[]): ChatMessage[] => {
    return messages.sort((a: ChatMessage, b: ChatMessage) => {
      // Primary sort: by timestamp
      const timeA = new Date(a.createdAt).getTime();
      const timeB = new Date(b.createdAt).getTime();
      
      if (timeA !== timeB) {
        return timeA - timeB;
      }
      
      // Tie-breaker: sort by message ID for consistent ordering
      // This ensures deterministic sorting when timestamps are identical
      return a.messageId.localeCompare(b.messageId);
    });
  };

  return {
    currentChatSessionId,
    messages,
    participants,
    isLoadingSession, // Expose computed loading state
    sessionError, // Expose renamed error state
    isConnected,
    otherParticipant,
    transactionInfo,
    isLoadingTransactionInfo,
    loadChatSession,
    sendMessage,
    clearChatState,
    fetchChatHistory, // Exposing for potential manual refresh, though loadChatSession handles it
    fetchChatParticipants,
    fetchTransactionInfo
  };
});
