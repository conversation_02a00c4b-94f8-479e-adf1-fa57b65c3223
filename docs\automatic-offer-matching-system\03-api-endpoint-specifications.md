# Automatic Offer Matching System - API Endpoint Specifications

## Document Overview
**Feature:** Automatic Offer Matching System  
**Version:** MVP (Minimum Viable Product)  
**Date:** December 2024  
**Status:** Foundation & Design Phase

## Table of Contents
1. [API Overview](#api-overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [Core Match Endpoints](#core-match-endpoints)
4. [Match Management Endpoints](#match-management-endpoints)
5. [Configuration Endpoints](#configuration-endpoints)
6. [Real-time Events](#real-time-events)
7. [Error Handling](#error-handling)
8. [Mobile Optimization](#mobile-optimization)

## API Overview

### Base URL Structure
```
Production: https://api.munygo.com/api/matches
Development: http://localhost:3000/api/matches
```

### Design Principles
- **RESTful Design:** Following REST conventions for predictable API behavior
- **Mobile-First:** Optimized payloads and pagination for mobile clients
- **Consistent Patterns:** Aligning with existing MUNygo API patterns
- **Real-time Integration:** Seamless Socket.IO integration for instant updates

### Integration with Existing MUNygo API
The matching API extends the current Hono-based API structure:
- **Route Structure:** `/api/matches/*` following existing `/api/offers/*` pattern
- **Middleware:** Uses existing `authMiddleware` for JWT validation
- **Error Handling:** Consistent with current API error response format
- **Pagination:** Follows existing pagination patterns for mobile optimization

## Authentication & Authorization

### JWT Authentication
All match endpoints require valid JWT authentication using existing middleware:

```typescript
// Existing auth middleware integration
import { authMiddleware, JwtPayload } from '../middleware/auth';

// All match routes protected
matchRouter.use('*', authMiddleware);
```

### Authorization Rules
- **Own Matches Only:** Users can only access their own matches
- **Match Participation:** Users can only interact with matches they're part of
- **Admin Override:** Future admin endpoints for match management (flagged for later)

### Request Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
User-Agent: MUNygo-Mobile/1.0 (for mobile analytics)
```

## Core Match Endpoints

### 1. Get User's Matches
**Endpoint:** `GET /api/matches/my-matches`  
**Purpose:** Retrieve current user's active and recent matches  
**Mobile-Optimized:** Paginated, minimal payload

#### Request Parameters
```typescript
interface GetMatchesQuery {
  page?: number;        // Default: 1
  limit?: number;       // Default: 20, Max: 50
  status?: string[];    // Filter by status: ['PENDING', 'PARTIAL_ACCEPT']
  currency?: string;    // Filter by currency: 'CAD', 'IRR'
  sort?: 'newest' | 'compatibility' | 'expires_soon'; // Default: 'newest'
  include_expired?: boolean; // Default: false
}
```

#### Response Format
```typescript
interface GetMatchesResponse {
  success: true;
  data: {
    matches: MatchSummary[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
    summary: {
      pendingCount: number;
      expiringCount: number; // Expiring in next 24h
      totalActiveCount: number;
    };
  };
}

interface MatchSummary {
  matchId: string;           // Human-readable ID
  otherUser: {
    userId: string;
    username: string;
    reputationLevel: number;
    reputationScore: number;
  };
  currencies: {
    yourCurrency: string;    // Currency you're offering
    theirCurrency: string;   // Currency they're offering
    yourAmount: number;
    theirAmount: number;
    exchangeRate: number;
  };
  compatibility: {
    score: number;           // 0.0-1.0
    factors: string[];       // ['amount_match', 'rate_compatible', 'reputation_similar']
  };
  status: MatchStatus;
  yourResponse?: 'ACCEPTED' | 'DECLINED';
  theirResponse?: 'ACCEPTED' | 'DECLINED';
  createdAt: string;         // ISO date
  expiresAt: string;         // ISO date
  timeRemaining: number;     // Seconds until expiration
}
```

#### Example Request
```http
GET /api/matches/my-matches?page=1&limit=10&status=PENDING&sort=compatibility
```

#### Example Response
```json
{
  "success": true,
  "data": {
    "matches": [
      {
        "matchId": "MATCH_20241201_001",
        "otherUser": {
          "userId": "user_abc123",
          "username": "currency_trader_99",
          "reputationLevel": 3,
          "reputationScore": 85
        },
        "currencies": {
          "yourCurrency": "CAD",
          "theirCurrency": "IRR",
          "yourAmount": 1000.00,
          "theirAmount": 52000000.00,
          "exchangeRate": 52000.0
        },
        "compatibility": {
          "score": 0.92,
          "factors": ["amount_match", "rate_compatible", "reputation_similar"]
        },
        "status": "PENDING",
        "createdAt": "2024-12-01T10:30:00Z",
        "expiresAt": "2024-12-03T10:30:00Z",
        "timeRemaining": 172800
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 15,
      "totalPages": 2,
      "hasNext": true,
      "hasPrev": false
    },
    "summary": {
      "pendingCount": 8,
      "expiringCount": 2,
      "totalActiveCount": 15
    }
  }
}
```

### 2. Get Match Details
**Endpoint:** `GET /api/matches/details/:matchId`  
**Purpose:** Get comprehensive details for a specific match  
**Mobile-Optimized:** Rich data for match decision making

#### Response Format
```typescript
interface MatchDetailsResponse {
  success: true;
  data: {
    match: MatchDetails;
  };
}

interface MatchDetails extends MatchSummary {
  offers: {
    yourOffer: {
      offerId: string;
      type: 'BUY' | 'SELL';
      amount: number;
      baseRate: number;
      adjustmentForLowerRep: number;
      adjustmentForHigherRep: number;
      effectiveRate: number;  // Calculated rate for this user
      createdAt: string;
    };
    theirOffer: {
      offerId: string;
      type: 'BUY' | 'SELL';
      amount: number;
      baseRate: number;
      adjustmentForLowerRep: number;
      adjustmentForHigherRep: number;
      effectiveRate: number;  // Calculated rate for this user
      createdAt: string;
    };
  };
  matchCriteria: {
    amountTolerance: number;      // Percentage
    rateCompatibility: number;    // Percentage difference
    matchedFactors: string[];
  };
  timeline: {
    matchDetected: string;        // ISO date
    userANotified?: string;       // ISO date
    userBNotified?: string;       // ISO date
    userAResponded?: string;      // ISO date
    userBResponded?: string;      // ISO date
  };
  potentialOutcome?: {
    chatSessionWillBeCreated: boolean;
    transactionCanProceed: boolean;
    estimatedCompletionTime: string; // e.g., "2-3 hours"
  };
}
```

#### Example Response
```json
{
  "success": true,
  "data": {
    "match": {
      "matchId": "MATCH_20241201_001",
      "otherUser": { "..." },
      "currencies": { "..." },
      "compatibility": { "..." },
      "status": "PENDING",
      "offers": {
        "yourOffer": {
          "offerId": "offer_xyz789",
          "type": "BUY",
          "amount": 1000.00,
          "baseRate": 51500.0,
          "adjustmentForLowerRep": 500.0,
          "adjustmentForHigherRep": -200.0,
          "effectiveRate": 51800.0,
          "createdAt": "2024-12-01T09:15:00Z"
        },
        "theirOffer": {
          "offerId": "offer_def456",
          "type": "SELL",
          "amount": 52000000.00,
          "baseRate": 52000.0,
          "adjustmentForLowerRep": 300.0,
          "adjustmentForHigherRep": -100.0,
          "effectiveRate": 51900.0,
          "createdAt": "2024-12-01T08:45:00Z"
        }
      },
      "matchCriteria": {
        "amountTolerance": 15.0,
        "rateCompatibility": 8.0,
        "matchedFactors": ["amount_within_tolerance", "rate_compatible", "both_verified_users"]
      },
      "timeline": {
        "matchDetected": "2024-12-01T10:30:00Z",
        "userANotified": "2024-12-01T10:30:05Z",
        "userBNotified": "2024-12-01T10:30:05Z"
      },
      "potentialOutcome": {
        "chatSessionWillBeCreated": true,
        "transactionCanProceed": true,
        "estimatedCompletionTime": "2-3 hours"
      }
    }
  }
}
```

## Match Management Endpoints

### 3. Accept Match - Race Condition Safe
**Endpoint:** `POST /api/matches/accept/:matchId`  
**Purpose:** Accept a match invitation with atomic race condition handling  
**Mobile-Optimized:** Immediate response with clear next steps

#### Business Logic
When both users accept a mutual match, the system creates **exactly ONE transaction** between them, regardless of timing.

#### Request Body
```typescript
interface AcceptMatchRequest {
  message?: string;  // Optional message to other user (max 200 chars)
  deviceId?: string; // For duplicate request detection
  timestamp: string; // Client timestamp for request ordering
}
```

#### Response Format
```typescript
interface AcceptMatchResponse {
  success: true;
  data: {
    matchId: string;
    newStatus: MatchStatus;
    result: {
      type: 'FIRST_ACCEPTANCE' | 'MUTUAL_MATCH' | 'ALREADY_PROCESSED';
      transactionId?: string;  // Created when both users accept
      chatSessionId?: string;  // Created when match becomes mutual
      partnerId: string;       // The other user's ID
      waitingForPartner?: boolean; // True if waiting for other user
    };
    nextSteps: {
      action: 'WAIT' | 'START_CHAT' | 'START_TRANSACTION' | 'REFRESH';
      redirectUrl?: string;
      message: string;
    };
  };
}
```

#### Example Request
```http
POST /api/matches/accept/MATCH_20241201_001
Content-Type: application/json

{
  "message": "Looking forward to trading with you!",
  "deviceId": "mobile-abc123",
  "timestamp": "2024-12-01T10:30:00Z"
}
```

#### Response Examples

**First User Accepts (Waiting for Partner):**
```json
{
  "success": true,
  "data": {
    "matchId": "MATCH_20241201_001",
    "newStatus": "PARTIAL_ACCEPT",
    "result": {
      "type": "FIRST_ACCEPTANCE",
      "partnerId": "user_xyz789",
      "waitingForPartner": true
    },
    "nextSteps": {
      "action": "WAIT",
      "message": "Match accepted! We'll notify you when your partner responds."
    }
  }
}
```

**Both Users Accept (Mutual Match - Creates Transaction):**
```json
{
  "success": true,
  "data": {
    "matchId": "MATCH_20241201_001", 
    "newStatus": "CONVERTED",
    "result": {
      "type": "MUTUAL_MATCH",
      "transactionId": "txn_abc123_xyz789",
      "chatSessionId": "chat_def456", 
      "partnerId": "user_xyz789"
    },
    "nextSteps": {
      "action": "START_TRANSACTION",
      "redirectUrl": "/transaction/txn_abc123_xyz789",
      "message": "Perfect match! Your transaction has been created."
    }
  }
}
```

**Race Condition - Already Processed:**
```json
{
  "success": false,
  "error": {
    "code": "MATCH_ALREADY_PROCESSED",
    "message": "This match was just processed by your partner!",
    "data": {
      "transactionId": "txn_abc123_xyz789",
      "chatSessionId": "chat_def456"
    },
    "nextSteps": {
      "action": "START_CHAT",
      "redirectUrl": "/chat/chat_def456",
      "message": "Check your active transaction with this partner."
    }
  }
}
```

#### Technical Implementation
```typescript
// Race condition safe acceptance handling
async function handleMatchAcceptance(matchId: string, userId: string, data: AcceptMatchRequest) {
  return await db.transaction(async (tx) => {
    // Atomic lock and update
    const match = await tx.offerMatch.findFirst({
      where: { 
        id: matchId,
        status: 'PENDING'  // Only process if still pending
      },
      include: { offerA: true, offerB: true }
    });
    
    if (!match) {
      throw new Error('MATCH_NOT_AVAILABLE');
    }
    
    const isUserA = match.userAId === userId;
    const isUserB = match.userBId === userId;
    
    if (!isUserA && !isUserB) {
      throw new Error('UNAUTHORIZED');
    }
    
    // Mark user's acceptance atomically
    const updateData = isUserA 
      ? { userAResponse: 'ACCEPTED', userARespondedAt: new Date() }
      : { userBResponse: 'ACCEPTED', userBRespondedAt: new Date() };
    
    const updatedMatch = await tx.offerMatch.update({
      where: { id: matchId },
      data: {
        ...updateData,
        acceptedByUserId: userId, // Track who accepted first
        status: 'PARTIAL_ACCEPT'
      }
    });
    
    // Check if both users have now accepted (mutual match)
    if (updatedMatch.userAResponse === 'ACCEPTED' && 
        updatedMatch.userBResponse === 'ACCEPTED') {
      
      // Create ONE transaction for both users
      const transaction = await createSingleTransaction(
        updatedMatch.userAId, 
        updatedMatch.userBId, 
        updatedMatch
      );
      
      // Create chat session
      const chatSession = await createChatSession(
        updatedMatch.userAId, 
        updatedMatch.userBId, 
        transaction.id
      );
      
      // Update match as successfully converted
      await tx.offerMatch.update({
        where: { id: matchId },
        data: {
          status: 'CONVERTED',
          transactionId: transaction.id,
          chatSessionId: chatSession.id
        }
      });
      
      return {
        type: 'MUTUAL_MATCH',
        transactionId: transaction.id,
        chatSessionId: chatSession.id,
        partnerId: isUserA ? updatedMatch.userBId : updatedMatch.userAId
      };
    }
    
    // Only one user has accepted so far
    return {
      type: 'FIRST_ACCEPTANCE',
      partnerId: isUserA ? updatedMatch.userBId : updatedMatch.userAId,
      waitingForPartner: true
    };
  });
}
```

### 4. Decline Match
**Endpoint:** `POST /api/matches/decline/:matchId`  
**Purpose:** Decline a match invitation  
**Mobile-Optimized:** Quick decline with optional feedback

#### Request Body
```typescript
interface DeclineMatchRequest {
  reason?: string;    // Optional decline reason
  feedback?: string;  // Optional feedback for improvement (max 300 chars)
}
```

#### Response Format
```typescript
interface DeclineMatchResponse {
  success: true;
  data: {
    matchId: string;
    newStatus: 'DECLINED';
    message: string;
    suggestedActions?: {
      viewSimilarMatches?: boolean;
      adjustOfferCriteria?: boolean;
      createCounterOffer?: boolean;
    };
  };
}
```

### 5. Cancel Match (Before Expiration)
**Endpoint:** `POST /api/matches/cancel/:matchId`  
**Purpose:** Cancel a match before it expires  
**Authorization:** Only available to match participants

#### Request Body
```typescript
interface CancelMatchRequest {
  reason: string;  // Required cancellation reason
}
```

#### Response Format
```typescript
interface CancelMatchResponse {
  success: true;
  data: {
    matchId: string;
    newStatus: 'CANCELLED';
    message: string;
  };
}
```

## Configuration Endpoints

### 6. Get Matching Preferences
**Endpoint:** `GET /api/matches/preferences`  
**Purpose:** Get user's matching preferences and criteria  
**Mobile-Optimized:** Settings for match algorithm customization

#### Response Format
```typescript
interface MatchPreferencesResponse {
  success: true;
  data: {
    preferences: {
      maxActiveMatches: number;          // Default: 10
      autoAcceptHighCompatibility: boolean; // Default: false
      compatibilityThreshold: number;    // 0.0-1.0, default: 0.7
      amountTolerancePercent: number;    // Default: 15.0
      rateTolerancePercent: number;      // Default: 8.0
      excludeLowReputationUsers: boolean; // Default: false
      minReputationLevel: number;        // 1-5, default: 1
      notificationPreferences: {
        instantNotification: boolean;    // Default: true
        emailSummary: boolean;          // Default: false
        pushNotification: boolean;      // Default: true
      };
    };
    systemDefaults: {
      matchExpirationHours: number;
      maxMatchesPerUser: number;
      globalAmountTolerance: number;
      globalRateTolerance: number;
    };
  };
}
```

### 7. Update Matching Preferences
**Endpoint:** `PUT /api/matches/preferences`  
**Purpose:** Update user's matching preferences  
**Mobile-Optimized:** Touch-friendly settings updates

#### Request Body
```typescript
interface UpdatePreferencesRequest {
  preferences: Partial<MatchPreferences>; // Only include fields to update
}
```

#### Response Format
```typescript
interface UpdatePreferencesResponse {
  success: true;
  data: {
    updatedPreferences: MatchPreferences;
    message: string;
  };
}
```

## Real-time Events

### Socket.IO Event Integration
The matching system integrates with existing `centralizedSocketManager.ts`:

#### Outbound Events (Backend → Frontend)
```typescript
// Match found event
interface MatchFoundPayload {
  matchId: string;
  otherUser: UserSummary;
  compatibility: CompatibilityInfo;
  currencies: CurrencyInfo;
  expiresAt: string;
  notificationPriority: 'HIGH' | 'MEDIUM' | 'LOW';
}

// Match status updated event
interface MatchStatusUpdatedPayload {
  matchId: string;
  newStatus: MatchStatus;
  updatedBy: 'USER' | 'SYSTEM' | 'OTHER_USER';
  message?: string;
  nextSteps?: NextStepsInfo;
}

// Match accepted by other user event
interface MatchAcceptedByOtherPayload {
  matchId: string;
  otherUser: UserSummary;
  chatSessionId?: string;
  transactionId?: string;
  message?: string;
}

// Match declined by other user event
interface MatchDeclinedByOtherPayload {
  matchId: string;
  otherUser: UserSummary;
  reason?: string;
  suggestedActions?: SuggestedActionsInfo;
}
```

#### Inbound Events (Frontend → Backend)
```typescript
// Request match details
interface RequestMatchDetailsPayload {
  matchId: string;
}

// Quick match response
interface QuickMatchResponsePayload {
  matchId: string;
  response: 'ACCEPT' | 'DECLINE';
  message?: string;
}
```

#### Event Constants
```typescript
// Add to existing socketEvents.ts
export const MATCH_FOUND = 'MATCH_FOUND';
export const MATCH_STATUS_UPDATED = 'MATCH_STATUS_UPDATED';
export const MATCH_ACCEPTED_BY_OTHER = 'MATCH_ACCEPTED_BY_OTHER';
export const MATCH_DECLINED_BY_OTHER = 'MATCH_DECLINED_BY_OTHER';
export const MATCH_EXPIRED = 'MATCH_EXPIRED';
export const REQUEST_MATCH_DETAILS = 'REQUEST_MATCH_DETAILS';
export const QUICK_MATCH_RESPONSE = 'QUICK_MATCH_RESPONSE';
```

## Error Handling

### Standard Error Response Format
Following existing MUNygo API error patterns:

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    field?: string;        // For validation errors
    timestamp: string;     // ISO date
  };
}
```

### Match-Specific Error Codes
```typescript
enum MatchErrorCodes {
  // Authentication & Authorization
  MATCH_ACCESS_DENIED = 'MATCH_ACCESS_DENIED',
  MATCH_NOT_PARTICIPANT = 'MATCH_NOT_PARTICIPANT',
  
  // Match State Errors  
  MATCH_NOT_FOUND = 'MATCH_NOT_FOUND',
  MATCH_EXPIRED = 'MATCH_EXPIRED',
  MATCH_ALREADY_RESPONDED = 'MATCH_ALREADY_RESPONDED',
  MATCH_INVALID_STATUS = 'MATCH_INVALID_STATUS',
  
  // Business Logic Errors
  OFFER_NO_LONGER_ACTIVE = 'OFFER_NO_LONGER_ACTIVE',
  USER_MAX_MATCHES_EXCEEDED = 'USER_MAX_MATCHES_EXCEEDED',
  INCOMPATIBLE_MATCH_CRITERIA = 'INCOMPATIBLE_MATCH_CRITERIA',
  
  // System Errors
  MATCHING_ENGINE_UNAVAILABLE = 'MATCHING_ENGINE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Validation Errors
  INVALID_MATCH_ID = 'INVALID_MATCH_ID',
  INVALID_PREFERENCE_VALUE = 'INVALID_PREFERENCE_VALUE',
  MESSAGE_TOO_LONG = 'MESSAGE_TOO_LONG'
}
```

### Example Error Responses
```json
// Match not found
{
  "success": false,
  "error": {
    "code": "MATCH_NOT_FOUND",
    "message": "The specified match could not be found or you don't have access to it.",
    "timestamp": "2024-12-01T10:30:00Z"
  }
}

// Already responded to match
{
  "success": false,
  "error": {
    "code": "MATCH_ALREADY_RESPONDED",
    "message": "You have already responded to this match.",
    "details": {
      "previousResponse": "ACCEPTED",
      "respondedAt": "2024-12-01T09:15:00Z"
    },
    "timestamp": "2024-12-01T10:30:00Z"
  }
}

// Validation error
{
  "success": false,
  "error": {
    "code": "MESSAGE_TOO_LONG",
    "message": "Message must be 200 characters or less.",
    "field": "message",
    "details": {
      "maxLength": 200,
      "actualLength": 245
    },
    "timestamp": "2024-12-01T10:30:00Z"
  }
}
```

## Mobile Optimization

### Response Size Optimization
- **Minimal Payloads:** Essential data only for list views
- **Progressive Loading:** Basic info first, details on demand
- **Compressed Images:** Optimized user avatars and icons
- **Cached Responses:** ETags for unchanged data

### Pagination Strategy
```typescript
// Mobile-optimized pagination
interface MobilePagination {
  page: number;
  limit: number;        // Default: 20, Max: 50
  total: number;
  hasNext: boolean;     // Simplified for mobile infinite scroll
  hasPrev: boolean;
  nextPage?: number;    // For convenience
  prevPage?: number;    // For convenience
}
```

### Connection Resilience
- **Offline Support:** Cache last known matches for offline viewing
- **Retry Logic:** Automatic retry for failed requests
- **Connection Status:** Integrate with existing connection monitoring
- **Background Sync:** Sync matches when connection restored

### Touch-Optimized Interactions
- **Swipe Actions:** Quick accept/decline via swipe gestures
- **Large Touch Targets:** Minimum 44px touch areas
- **Haptic Feedback:** Response confirmation for supported devices
- **Pull-to-Refresh:** Standard mobile refresh pattern

### Performance Monitoring
```typescript
// Mobile performance headers
interface MobilePerformanceHeaders {
  'X-Response-Time': string;      // Server processing time
  'X-Cache-Status': 'HIT' | 'MISS'; // Cache status
  'X-Mobile-Optimized': 'true';   // Indicates mobile-optimized response
  'X-Data-Size': string;          // Response size in bytes
}
```

---

## Implementation Priority

### Phase 1: Core Functionality
1. `GET /api/matches/my-matches` - Essential for user dashboard
2. `GET /api/matches/details/:matchId` - Required for informed decisions
3. `POST /api/matches/accept/:matchId` - Core functionality
4. `POST /api/matches/decline/:matchId` - Core functionality

### Phase 2: Enhanced Features
1. `GET /api/matches/preferences` - User customization
2. `PUT /api/matches/preferences` - Settings management
3. `POST /api/matches/cancel/:matchId` - Advanced control

### Phase 3: Real-time Integration
1. Socket.IO event handlers
2. Real-time status updates
3. Push notification integration
4. Background sync capabilities

This API specification provides a comprehensive, mobile-first foundation for the automatic offer matching system while maintaining consistency with existing MUNygo API patterns and performance requirements.
