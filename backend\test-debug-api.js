const { PrismaClient } = require('@prisma/client');
const jwt = require('jsonwebtoken');

const prisma = new PrismaClient();
const JWT_SECRET = process.env.JWT_SECRET || 'your-default-secret-key';

async function testDebugApi() {
  try {
    // Use an admin user
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!adminUser) {
      console.log('Admin user not found');
      return;
    }
    
    console.log('Using admin user:', adminUser.email);    
    // Create a JWT token for the admin user
    const token = jwt.sign(
      { userId: adminUser.id, email: adminUser.email },
      JWT_SECRET,
      { expiresIn: '1h' }
    );
    
    console.log('Generated JWT token for admin user:', adminUser.email);
    
    // Test the debug reports endpoint
    const response = await fetch('http://localhost:3000/api/debug/admin/reports?page=1&limit=10&sortBy=serverReceivedAt&sortOrder=desc', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('Response status:', response.status);
    const data = await response.text();
    console.log('Response data:', data);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDebugApi();
