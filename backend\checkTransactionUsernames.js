const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkTransaction() {
  try {
    // Find the transaction from the logs
    const transaction = await prisma.transaction.findUnique({
      where: { id: 'cmb3yoxpc000zvljku0tznqal' },
      include: {
        currencyAProvider: {
          select: { id: true, username: true }
        },
        currencyBProvider: {
          select: { id: true, username: true }
        }
      }
    });

    if (transaction) {
      console.log('Transaction found:');
      console.log('ID:', transaction.id);
      console.log('Status:', transaction.status);
      console.log('Currency A Provider:', transaction.currencyAProvider);
      console.log('Currency B Provider:', transaction.currencyBProvider);
    } else {
      console.log('Transaction not found');
    }

    // Also check the users directly
    const userA = await prisma.user.findUnique({
      where: { id: 'cmav458yl0000vl50932ofx2h' },
      select: { id: true, username: true, email: true }
    });

    const userB = await prisma.user.findUnique({
      where: { id: 'cmav458yw0001vl50xoubu4w4' },
      select: { id: true, username: true, email: true }
    });

    console.log('\nUser A:', userA);
    console.log('User B:', userB);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTransaction();
