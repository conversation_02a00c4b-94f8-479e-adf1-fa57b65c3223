// Test script to check the actual backend response format for debug reports and tags
const axios = require('./backend/node_modules/axios');

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

async function testBackendTags() {
  try {
    console.log('🔍 Testing backend debug reports API response...');
    
    // Test the admin debug reports endpoint
    const response = await axios.get(`${API_BASE_URL}/api/admin/debug-reports`, {
      params: {
        page: 1,
        limit: 5
      }
    });
    
    console.log('📊 Response status:', response.status);
    console.log('📦 Response structure:');
    console.log('  - Total reports:', response.data?.total);
    console.log('  - Reports array length:', response.data?.reports?.length);
    
    if (response.data?.reports?.length > 0) {
      const firstReport = response.data.reports[0];
      console.log('\n🔍 First report structure:');
      console.log('  - ID:', firstReport.reportId);
      console.log('  - Title:', firstReport.title);
      console.log('  - Tags structure:', JSON.stringify(firstReport.tags, null, 2));
      console.log('  - Tags simple:', firstReport.tagsSimple);
      console.log('  - Has tags:', firstReport.hasTags);
      
      if (firstReport.tags && firstReport.tags.length > 0) {
        console.log('\n🏷️  Individual tag analysis:');
        firstReport.tags.forEach((tag, index) => {
          console.log(`  Tag ${index + 1}:`, {
            'tag property': tag.tag,
            'tagName property': tag.tagName,
            'tagId property': tag.tagId,
            'origin': tag.origin,
            'full object': tag
          });
        });
      }
    } else {
      console.log('⚠️  No reports found in response');
    }
    
  } catch (error) {
    console.error('❌ Error testing backend tags:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', error.response.data);
    }
  }
}

testBackendTags();
