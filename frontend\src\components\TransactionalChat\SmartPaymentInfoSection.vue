<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import { useMessage } from 'naive-ui'
import { NButton, NIcon, NRadio } from 'naive-ui'
import { ChevronDown, Card } from '@vicons/ionicons5'
import PaymentMethodFormFields from './PaymentMethodFormFields.vue'

interface PaymentMethod {
  id: string
  bankName: string
  accountNumber: string
  accountHolderName: string
  iban?: string
  swiftCode?: string
  routingNumber?: string
  notes?: string
  validationStatus: 'complete' | 'pending'
  isComplete: boolean
  missingFields: string[]
  isDefaultForUser?: boolean
}

interface Props {
  existingMethods: PaymentMethod[]
  currentMethod: PaymentMethod | null
  currency: string
  canEditInline?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  canEditInline: true
})

const emit = defineEmits<{
  methodSelected: [method: PaymentMethod]
  inlineEdit: [method: PaymentMethod]
  newMethodAdded: [method: PaymentMethod]
  methodUpdated: [method: PaymentMethod]
  paymentInfoConfirmed: [method: PaymentMethod]
}>()

const { t } = useI18n()
const message = useMessage()

// Component refs for viewport awareness
const smartPaymentSectionRef = ref<HTMLElement>()
const formContainerRef = ref<HTMLElement>()
const editFormRef = ref<InstanceType<typeof PaymentMethodFormFields>>()
const newFormRef = ref<InstanceType<typeof PaymentMethodFormFields>>()

// Enhanced state management for progressive disclosure
const selectedMethodId = ref<string | null>(null)
const showingChoices = ref(false)
const showingDetails = ref(false)
const isAddingNew = ref(false)
const editingMethodId = ref<string | null>(null)
const editingMethodData = ref<Partial<PaymentMethod> | null>(null)

// New payment method form
const newMethod = ref<Partial<PaymentMethod>>({
  bankName: '',
  accountNumber: '',
  accountHolderName: '',
  iban: '',
  swiftCode: '',
  routingNumber: '',
  notes: ''
})

// Computed properties
const hasExistingMethods = computed(() => {
  return props.existingMethods.length > 0
})

const isEditing = computed(() => editingMethodId.value !== null)

// Card size computation based on current state
const currentState = computed(() => {
  if (isEditing.value || isAddingNew.value) return 'large'
  if (showingChoices.value || showingDetails.value) return 'medium'
  return 'compact'
})

// Smart default detection - prioritize default, fallback to first
const filteredMethods = computed(() => {
  return props.existingMethods.filter((method: PaymentMethod) => method.validationStatus === 'complete')
})

const selectedMethod = computed(() => {
  if (selectedMethodId.value) {
    const found = filteredMethods.value.find((method: PaymentMethod) => method.id === selectedMethodId.value)
    if (found) return found
  }
  // Fallback to default method or first available
  return filteredMethods.value.find((m: PaymentMethod) => m.isDefaultForUser) || filteredMethods.value[0] || null
})

const availableAlternatives = computed(() => {
  return filteredMethods.value.filter((m: PaymentMethod) => m.id !== selectedMethod.value?.id)
})

// Initialize with smart default and handle current method changes
watch(() => props.currentMethod, (newMethod, oldMethod) => {
  console.log('🔄 SmartPaymentInfoSection - currentMethod watcher:', {
    newMethod,
    oldMethod,
    selectedMethodId: selectedMethodId.value
  })
  
  if (newMethod) {
    selectedMethodId.value = newMethod.id
  } else if (filteredMethods.value.length > 0) {
    // If no current method but we have methods, select the default or first one
    const defaultMethod = filteredMethods.value.find(m => m.isDefaultForUser)
    selectedMethodId.value = defaultMethod?.id || filteredMethods.value[0]?.id || null
  }
}, { immediate: true })

// Watch for changes in filtered methods to ensure selection is valid
watch(() => filteredMethods.value, (newMethods) => {
  if (selectedMethodId.value) {
    // Check if currently selected method is still valid
    const isCurrentSelectionValid = newMethods.find(m => m.id === selectedMethodId.value)
    if (!isCurrentSelectionValid && newMethods.length > 0) {
      // If current selection is invalid, select default or first available
      const defaultMethod = newMethods.find(m => m.isDefaultForUser)
      selectedMethodId.value = defaultMethod?.id || newMethods[0]?.id || null
    }
  }
})

// Debug watcher for selectedMethod changes
watch(() => selectedMethod.value, (newMethod, oldMethod) => {
  console.log('🎯 SmartPaymentInfoSection - selectedMethod changed:', {
    newMethod,
    oldMethod,
    selectedMethodId: selectedMethodId.value
  })
})

const canSubmit = computed(() => {
  if (isAddingNew.value) {
    return newMethod.value.bankName &&
           newMethod.value.accountNumber &&
           newMethod.value.accountHolderName
  }
  if (isEditing.value) {
    return editingMethodData.value?.bankName &&
           editingMethodData.value?.accountNumber &&
           editingMethodData.value?.accountHolderName
  }
  return selectedMethodId.value !== null
})

// Computed property for editing method data with default values
const editingMethodDataWithDefaults = computed({
  get: () => editingMethodData.value || {
    bankName: '',
    accountNumber: '',
    accountHolderName: '',
    iban: '',
    swiftCode: '',
    routingNumber: '',
    notes: ''
  },
  set: (value) => {
    editingMethodData.value = value
  }
})

const formRules = computed(() => ({
  bankName: { required: true, message: t('paymentMethods.bankNameRequired') },
  accountNumber: { required: true, message: t('paymentMethods.accountNumberRequired') },
  accountHolderName: { required: true, message: t('paymentMethods.accountHolderRequired') }
}))

// Enhanced methods for progressive disclosure
const showChoices = () => {
  showingChoices.value = true
  showingDetails.value = false
}

const confirmSelection = () => {
  if (selectedMethod.value) {
    console.log('✅ SmartPaymentInfoSection - confirmSelection called:', {
      selectedMethodId: selectedMethodId.value,
      selectedMethod: selectedMethod.value,
      validationStatus: selectedMethod.value.validationStatus
    })
    // Emit the selected method to parent
    emit('methodSelected', selectedMethod.value)
    // Close the selection view
    showingChoices.value = false
  }
}

const startAddingNew = () => {
  isAddingNew.value = true
  showingChoices.value = false
  selectedMethodId.value = null
  editingMethodId.value = null
  
  // Simple scroll to bring card into view
  nextTick(() => {
    scrollCardIntoView()
  })
}

const cancelAddingNew = () => {
  isAddingNew.value = false
  if (hasExistingMethods.value) {
    // Reset to the current method or first available method
    selectedMethodId.value = props.currentMethod?.id || filteredMethods.value[0]?.id || null
  }
  newMethod.value = {
    bankName: '',
    accountNumber: '',
    accountHolderName: '',
    iban: '',
    swiftCode: '',
    routingNumber: '',
    notes: ''
  }
}

const startInlineEdit = (method: PaymentMethod) => {
  editingMethodId.value = method.id
  editingMethodData.value = { ...method }
  showingDetails.value = false
  showingChoices.value = false
  
  // Simple scroll to bring card into view
  nextTick(() => {
    scrollCardIntoView()
  })
}

const cancelInlineEdit = () => {
  editingMethodId.value = null
  editingMethodData.value = null
}

const saveInlineEdit = async () => {
  if (!editingMethodId.value || !editingMethodData.value) return

  // Validate the form before saving
  try {
    await editFormRef.value?.validate()
    emit('methodUpdated', editingMethodData.value as PaymentMethod)
    // Note: Success message is handled by parent ActionCard component to avoid duplication
    cancelInlineEdit()
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

const showDetails = () => {
  showingDetails.value = true
  showingChoices.value = false
}

const hideDetails = () => {
  showingDetails.value = false
}

const cancelChoices = () => {
  console.log('❌ SmartPaymentInfoSection - cancelChoices called:', {
    currentSelectedMethodId: selectedMethodId.value,
    currentMethod: props.currentMethod
  })
  
  showingChoices.value = false
  // Reset to the original current method, don't change the selection
  selectedMethodId.value = props.currentMethod?.id || null
}

const selectMethod = (methodId: string) => {
  console.log('🔄 SmartPaymentInfoSection - selectMethod called:', {
    methodId,
    previousSelection: selectedMethodId.value,
    method: filteredMethods.value.find(m => m.id === methodId)
  })
  selectedMethodId.value = methodId
}

const saveNewMethod = async () => {
  if (!canSubmit.value) return
  
  // Validate the form before saving
  try {
    await newFormRef.value?.validate()
    
    const methodToSave: PaymentMethod = {
      id: `temp-${Date.now()}`, // Temporary ID
      bankName: newMethod.value.bankName!,
      accountNumber: newMethod.value.accountNumber!,
      accountHolderName: newMethod.value.accountHolderName!,
      iban: newMethod.value.iban || '',
      swiftCode: newMethod.value.swiftCode || '',
      routingNumber: newMethod.value.routingNumber || '',
      notes: newMethod.value.notes || '',
      validationStatus: 'complete',
      isComplete: true,
      missingFields: [],
      isDefaultForUser: false
    }
    
    emit('newMethodAdded', methodToSave)
    // Note: Success message is handled by parent ActionCard component to avoid duplication
    cancelAddingNew()
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

const formatMethodDisplay = (method: PaymentMethod) => {
  return `${method.bankName} ${maskAccountNumber(method.accountNumber)}`
}

const getStatusIcon = (method: PaymentMethod) => {
  return method.validationStatus === 'complete' ? '✅' : '⚠️'
}

const getStatusText = (method: PaymentMethod) => {
  return method.validationStatus === 'complete'
    ? t('transactionalChat.payment.status.complete')
    : t('transactionalChat.payment.status.pending')
}

const maskAccountNumber = (accountNumber: string) => {
  if (accountNumber.length <= 4) return accountNumber
  return '*'.repeat(accountNumber.length - 4) + accountNumber.slice(-4)
}

// Simple scroll into view when card becomes large
const scrollCardIntoView = async () => {
  await nextTick()
  
  if (!smartPaymentSectionRef.value) return
  
  // Use browser's built-in scrollIntoView for reliable behavior
  smartPaymentSectionRef.value.scrollIntoView({
    behavior: 'smooth',
    block: 'start',
    inline: 'nearest'
  })
}

const confirmPaymentInfo = () => {
  if (selectedMethod.value) {
    emit('paymentInfoConfirmed', selectedMethod.value)
    message.success(t('transactionalChat.actionCards.paymentInfo.confirmed'))
  }
}

// Lifecycle hooks
onMounted(() => {
  console.log('� SmartPaymentInfoSection mounted')
})

onBeforeUnmount(() => {
  console.log('🔄 SmartPaymentInfoSection unmounted')
})
</script>

<template>
  <div 
    ref="smartPaymentSectionRef"
    class="smart-payment-info-section" 
    :class="{
      'state-compact': currentState === 'compact',
      'state-medium': currentState === 'medium', 
      'state-large': currentState === 'large'
    }"
    data-testid="smart-payment-info-section"
  >
    <!-- Inline Edit Form -->
    <div 
      v-if="isEditing" 
      ref="formContainerRef"
      class="add-new-method-form" 
      data-testid="inline-edit-form"
    >
      <div class="form-header">
        <h4>{{ t('transactionalChat.actionCards.paymentInfo.editMethod') }}</h4>
      </div>
      
      <PaymentMethodFormFields
        ref="editFormRef"
        v-model="editingMethodDataWithDefaults"
        :rules="formRules"
        test-id-prefix="edit-"
        form-class="new-method-form" />
      
      <div class="form-actions mobile-action-grid">
        <n-button 
          quaternary
          @click="cancelInlineEdit"
          data-testid="cancel-edit-btn"
          class="mobile-action-btn">
          {{ t('common.cancel') }}
        </n-button>
        <n-button 
          type="primary" 
          @click="saveInlineEdit"
          :disabled="!canSubmit"
          data-testid="save-edit-btn"
          class="mobile-primary-btn">
          {{ t('transactionalChat.actionCards.paymentInfo.saveChanges') }}
        </n-button>
      </div>
    </div>

    <!-- Progressive Disclosure: Compact Default State -->
    <div v-if="!isEditing && !showingChoices && !showingDetails && hasExistingMethods && selectedMethod" 
         class="compact-default-view" 
         data-testid="compact-default-view">
      <div class="default-method-display">
        <div class="method-summary" @click="showDetails">
          <div class="method-info">
            <div class="method-title">
              {{ formatMethodDisplay(selectedMethod) }}
              <span class="status-indicator" :class="{ 'complete': selectedMethod.validationStatus === 'complete' }">
                {{ getStatusIcon(selectedMethod) }}
              </span>
            </div>
            <div class="method-subtitle">
              {{ getStatusText(selectedMethod) }}
            </div>
          </div>
          <n-icon class="expand-icon" :component="ChevronDown" />
        </div>
        
        <div class="action-buttons">
          <div class="button-row">
            <n-button 
              v-if="availableAlternatives.length > 0"
              size="small" 
              type="primary" 
              quaternary
              @click="showChoices"
              data-testid="change-method-btn"
              class="mobile-action-btn">
              {{ t('transactionalChat.actionCards.paymentInfo.changeMethod') }}
            </n-button>
            <n-button 
              size="small" 
              type="primary" 
              quaternary
              @click="startInlineEdit(selectedMethod)"
              data-testid="edit-method-btn"
              class="mobile-action-btn">
              {{ t('transactionalChat.actionCards.paymentInfo.editMethod') }}
            </n-button>
          </div>
          <n-button 
            size="medium" 
            type="primary"
            @click="confirmPaymentInfo"
            data-testid="confirm-payment-info-btn"
            class="mobile-primary-btn">
            {{ t('transactionalChat.actionCards.paymentInfo.confirmInfo') }}
          </n-button>
        </div>
      </div>
    </div>

    <!-- Progressive Disclosure: Method Selection View -->
    <div v-if="!isEditing && showingChoices" class="method-selection-view" data-testid="method-selection-view">
      <div class="selection-header">
        <h4>{{ t('transactionalChat.actionCards.paymentInfo.selectMethod') }}</h4>
        <n-button 
          size="small" 
          quaternary 
          @click="cancelChoices"
          data-testid="cancel-selection-btn">
          {{ t('common.cancel') }}
        </n-button>
      </div>
      
      <div class="method-options">
        <div 
          v-for="method in filteredMethods" 
          :key="method.id"
          class="method-option"
          :class="{ 'selected': selectedMethodId === method.id }"
          @click="selectMethod(method.id)"
          data-testid="method-option">
          <div class="method-content">
            <div class="method-details">
              <div class="method-name">{{ formatMethodDisplay(method) }}</div>
              <div class="method-status" :class="{ 'complete': method.validationStatus === 'complete' }">
                {{ getStatusIcon(method) }} {{ getStatusText(method) }}
              </div>
            </div>
            <n-radio 
              :checked="selectedMethodId === method.id"
              @update:checked="() => selectMethod(method.id)" />
          </div>
        </div>
      </div>
      
      <div class="selection-actions">
        <n-button 
          type="primary" 
          @click="confirmSelection"
          :disabled="!selectedMethodId"
          data-testid="confirm-selection-btn"
          class="mobile-primary-btn">
          {{ t('transactionalChat.actionCards.paymentInfo.confirmSelection') }}
        </n-button>
        <n-button 
          quaternary 
          @click="startAddingNew"
          data-testid="add-new-method-btn"
          class="mobile-action-btn">
          {{ t('transactionalChat.actionCards.paymentInfo.addNewMethod') }}
        </n-button>
      </div>
    </div>

    <!-- Progressive Disclosure: Method Details View -->
    <div v-if="!isEditing && showingDetails && selectedMethod" 
         class="method-details-view" 
         data-testid="method-details-view">
      <div class="details-header">
        <h4>{{ t('transactionalChat.actionCards.paymentInfo.methodDetails') }}</h4>
        <n-button
          size="small"
          quaternary
          @click="hideDetails"
          data-testid="hide-details-btn"
          class="collapse-details-icon-btn"
        >
          <n-icon :component="ChevronDown" style="transform: rotate(180deg);" />
        </n-button>
      </div>
      
      <div class="method-full-details">
        <div class="detail-row">
          <span class="label">{{ t('paymentMethods.bankName') }}:</span>
          <span class="value">{{ selectedMethod.bankName }}</span>
        </div>
        <div class="detail-row">
          <span class="label">{{ t('paymentMethods.accountNumber') }}:</span>
          <span class="value">{{ selectedMethod.accountNumber }}</span>
        </div>
        <div class="detail-row">
          <span class="label">{{ t('paymentMethods.accountHolderName') }}:</span>
          <span class="value">{{ selectedMethod.accountHolderName }}</span>
        </div>
        <div v-if="selectedMethod.iban" class="detail-row">
          <span class="label">{{ t('paymentMethods.iban') }}:</span>
          <span class="value">{{ selectedMethod.iban }}</span>
        </div>
        <div v-if="selectedMethod.swiftCode" class="detail-row">
          <span class="label">{{ t('paymentMethods.swiftCode') }}:</span>
          <span class="value">{{ selectedMethod.swiftCode }}</span>
        </div>
        <div v-if="selectedMethod.routingNumber" class="detail-row">
          <span class="label">{{ t('paymentMethods.routingNumber') }}:</span>
          <span class="value">{{ selectedMethod.routingNumber }}</span>
        </div>
        <div v-if="selectedMethod.notes" class="detail-row">
          <span class="label">{{ t('paymentMethods.notes') }}:</span>
          <span class="value">{{ selectedMethod.notes }}</span>
        </div>
      </div>
      
      <div class="details-actions">
        <n-button 
          type="primary" 
          @click="startInlineEdit(selectedMethod)"
          data-testid="edit-from-details-btn"
          class="mobile-primary-btn">
          {{ t('transactionalChat.actionCards.paymentInfo.editMethod') }}
        </n-button>
      </div>
    </div>

    <!-- Add New Method Form -->
    <div 
      v-if="!isEditing && isAddingNew" 
      ref="formContainerRef"
      class="add-new-method-form" 
      data-testid="add-new-method-form"
    >
      <div class="form-header">
        <h4>{{ t('transactionalChat.actionCards.paymentInfo.addNewMethod') }}</h4>
        <n-button 
          size="small" 
          quaternary 
          @click="cancelAddingNew"
          data-testid="cancel-add-new-btn">
          {{ t('common.cancel') }}
        </n-button>
      </div>
      
      <PaymentMethodFormFields
        ref="newFormRef"
        v-model="newMethod"
        :rules="formRules"
        test-id-prefix=""
        form-class="new-method-form" />
      
      <div class="form-actions mobile-action-grid single-action">
        <n-button 
          type="primary" 
          @click="saveNewMethod"
          :disabled="!canSubmit"
          data-testid="save-new-method-btn"
          class="mobile-primary-btn">
          {{ t('transactionalChat.actionCards.paymentInfo.saveMethod') }}
        </n-button>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!isEditing && !hasExistingMethods && !isAddingNew" 
         class="empty-state" 
         data-testid="payment-empty-state">
      <div class="empty-content">
        <n-icon class="empty-icon" :component="Card" />
        <h4>{{ t('transactionalChat.actionCards.paymentInfo.noMethods') }}</h4>
        <p>{{ t('transactionalChat.actionCards.paymentInfo.noMethodsDesc') }}</p>
        <n-button 
          type="primary" 
          @click="startAddingNew"
          data-testid="add-first-method-btn"
          class="mobile-primary-btn">
          {{ t('transactionalChat.actionCards.paymentInfo.addFirstMethod') }}
        </n-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.smart-payment-info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: margin 0.3s ease;
}

/* Dynamic bottom spacing based on state */
.smart-payment-info-section.state-compact {
  margin-bottom: 16px;
}

.smart-payment-info-section.state-medium {
  margin-bottom: 40px;
}

.smart-payment-info-section.state-large {
  margin-bottom: 120px; /* Ensure large forms are never cut off */
}

/* On mobile, increase spacing even more */
@media (max-width: 768px) {
  .smart-payment-info-section.state-large {
    margin-bottom: 160px;
  }
}

/* Empty State */
.no-methods-state {
  padding: 24px;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.empty-icon {
  font-size: 48px;
  opacity: 0.7;
}

.empty-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--tc-text-primary);
}

.empty-description {
  margin: 0;
  font-size: 14px;
  color: var(--tc-text-muted);
  line-height: 1.4;
}

/* Existing Methods */
.existing-methods-state {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--tc-text-primary);
}

/* Methods List */
.methods-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.method-item {
  border: 1px solid var(--tc-border-light);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.method-item:hover {
  border-color: var(--tc-border-medium);
}

.method-item.selected {
  border-color: var(--tc-primary);
  box-shadow: 0 0 0 1px var(--tc-primary-light);
}

.method-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  cursor: pointer;
  user-select: none;
}

.method-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.method-primary {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.status-icon {
  font-size: 16px;
}

.bank-name {
  color: var(--tc-text-primary);
}

.account-preview {
  color: var(--tc-text-muted);
  font-family: monospace;
}

.method-secondary {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.holder-name {
  color: var(--tc-text-secondary);
}

.default-badge {
  background: var(--tc-success-light);
  color: var(--tc-success);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.method-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selection-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--tc-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* Method Details */
.method-details {
  padding: 16px;
  border-top: 1px solid var(--tc-border-light);
  background: var(--tc-bg-subtle);
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.detail-label {
  color: var(--tc-text-muted);
  font-weight: 500;
}

.detail-value {
  color: var(--tc-text-primary);
  font-family: monospace;
}

/* Buttons */
.add-method-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* Touch target */
}

.add-method-button.primary {
  background: var(--tc-primary);
  color: white;
}

.add-method-button.primary:hover {
  background: var(--tc-primary-dark);
}

.add-method-button.secondary {
  background: var(--tc-bg-subtle);
  color: var(--tc-text-secondary);
  border: 1px solid var(--tc-border-medium);
}

.add-method-button.secondary:hover {
  background: var(--tc-bg-hover);
  border-color: var(--tc-border-dark);
}

.button-icon {
  font-size: 16px;
  font-weight: bold;
}

.expand-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  background: none;
  border: 1px solid var(--tc-border-light);
  border-radius: 6px;
  color: var(--tc-text-secondary);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.expand-toggle:hover {
  border-color: var(--tc-border-medium);
  color: var(--tc-text-primary);
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px;
}

.action-button.edit {
  background: var(--tc-bg-subtle);
  color: var(--tc-text-secondary);
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button.edit:hover {
  background: var(--tc-bg-hover);
}

/* Add New Form */
.add-new-form {
  padding: 20px;
  background: var(--tc-bg-subtle);
  border-radius: 12px;
  border: 1px solid var(--tc-border-light);
  /* Ensure form has enough bottom margin */
  margin-bottom: 120px;
  position: relative;
}

/* Enhanced form container with proper spacing */
.add-new-method-form {
  padding: 20px;
  background: var(--tc-bg-subtle);
  border-radius: 12px;
  border: 1px solid var(--tc-border-light);
  /* Forms automatically get proper spacing from parent state classes */
  position: relative;
}

/* Method selection and details views */
.method-selection-view,
.method-details-view {
  background: white;
  border: 1px solid var(--tc-border-light);
  border-radius: 12px;
  padding: 20px;
  position: relative;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  margin: 16px 0;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-field.full-width {
  grid-column: 1 / -1;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--tc-text-primary);
}

.field-input,
.field-textarea {
  padding: 10px 12px;
  border: 1px solid var(--tc-border-medium);
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.field-input:focus,
.field-textarea:focus {
  outline: none;
  border-color: var(--tc-primary);
  box-shadow: 0 0 0 1px var(--tc-primary-light);
}

.field-textarea {
  resize: vertical;
  min-height: 60px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.action-button.cancel {
  background: var(--tc-bg-subtle);
  color: var(--tc-text-secondary);
  border: 1px solid var(--tc-border-medium);
}

.action-button.cancel:hover {
  background: var(--tc-bg-hover);
}

.action-button.save {
  background: var(--tc-primary);
  color: white;
}

.action-button.save:hover {
  background: var(--tc-primary-dark);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design */
@media (min-width: 480px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .details-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 768px) {
  .form-actions {
    justify-content: space-between;
  }
}

/* RTL Support */
[dir="rtl"] .method-preview {
  flex-direction: row-reverse;
}

[dir="rtl"] .method-primary,
[dir="rtl"] .method-secondary {
  flex-direction: row-reverse;
}

/* Progressive Disclosure Styles - Enhanced UX */

/* Compact Default View */
.compact-default-view {
  background: var(--tc-bg-subtle);
  border: 1px solid var(--tc-border-light);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease;
}

.compact-default-view:hover {
  border-color: var(--tc-border-medium);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.default-method-display {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.method-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 4px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  min-height: 44px;
}

.method-summary:hover {
  background: var(--tc-bg-hover);
}

.method-title {
  font-weight: 600;
  color: var(--tc-text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.method-subtitle {
  font-size: 14px;
  color: var(--tc-text-secondary);
}

.status-indicator.complete {
  color: var(--tc-success);
}

.expand-icon {
  color: var(--tc-text-muted);
  transition: transform 0.2s ease;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.button-row {
  display: flex;
  gap: 8px;
  justify-content: space-between;
}

.mobile-action-btn {
  flex: 1;
  min-height: 44px !important; /* Touch target */
  font-size: 14px !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
}

.mobile-primary-btn {
  width: 100%;
  min-height: 48px !important; /* Larger touch target for primary action */
  font-size: 16px !important;
  font-weight: 600 !important;
  border-radius: 12px !important;
  background: var(--tc-primary) !important;
  color: white !important;
  border: none !important;
}

.mobile-primary-btn:hover {
  background: var(--tc-primary-dark) !important;
}

.action-buttons .n-button {
  min-width: 120px;
}

.action-buttons .n-button[data-testid="confirm-payment-info-btn"] {
  background: var(--tc-primary);
  color: white;
  border: none;
}

.action-buttons .n-button[data-testid="confirm-payment-info-btn"]:hover {
  background: var(--tc-primary-dark);
}

/* Selection and Details Actions */
.selection-actions,
.details-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.selection-actions .n-button,
.details-actions .n-button {
  width: 100%;
  min-height: 48px;
}

/* Method Selection View */
.method-selection-view {
  background: white;
  border: 1px solid var(--tc-border-light);
  border-radius: 12px;
  padding: 20px;
}

.selection-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.selection-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--tc-text-primary);
}

.method-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.method-option {
  border: 1px solid var(--tc-border-light);
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
}

.method-option:hover {
  border-color: var(--tc-border-medium);
  background: var(--tc-bg-subtle);
}

.method-option.selected {
  border-color: var(--tc-primary);
  background: var(--tc-primary-light);
}

.method-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.method-name {
  font-weight: 500;
  color: var(--tc-text-primary);
  margin-bottom: 4px;
}

.method-status.complete {
  color: var(--tc-success);
}

/* Method Details View */
.method-details-view {
  background: white;
  border: 1px solid var(--tc-border-light);
  border-radius: 12px;
  padding: 20px;
}


.details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  position: relative;
}


.collapse-details-icon-btn {
  min-width: 40px !important;
  min-height: 40px !important;
  width: 40px !important;
  height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50% !important;
  background: var(--tc-bg-subtle) !important;
  color: var(--tc-text-muted) !important;
  border: 1px solid var(--tc-border-light) !important;
  box-shadow: none;
  transition: background 0.2s, color 0.2s;
  margin-right: 0;
  margin-left: 0;
  padding: 0 !important;
}
.collapse-details-icon-btn:hover {
  background: var(--tc-bg-hover) !important;
  color: var(--tc-primary) !important;
}

@media (max-width: 768px) {
  .details-header {
    flex-direction: row;
    align-items: flex-start;
    gap: 8px;
  }
  .collapse-details-icon-btn {
    min-width: 40px !important;
    min-height: 40px !important;
    width: 40px !important;
    height: 40px !important;
    font-size: 20px !important;
    margin-top: 0;
    margin-bottom: 0;
    position: static;
  }
}

@media (max-width: 768px) {
  .details-header {
    flex-direction: row;
    align-items: flex-start;
    gap: 8px;
  }
  .collapse-details-btn {
    min-width: 48px !important;
    min-height: 48px !important;
    font-size: 20px !important;
    margin-top: 0;
    margin-bottom: 0;
    position: static;
  }
}

.details-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--tc-text-primary);
}

.method-full-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--tc-border-light);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  font-weight: 500;
  color: var(--tc-text-secondary);
  min-width: 120px;
  flex-shrink: 0;
}

.detail-row .value {
  color: var(--tc-text-primary);
  word-break: break-word;
}

/* Progressive Disclosure Animations */
.compact-default-view,
.method-selection-view,
.method-details-view,
.add-new-method-form {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Progressive Disclosure */
@media (max-width: 768px) {
  .action-buttons,
  .selection-actions,
  .details-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .button-row {
    gap: 12px;
  }
  
  .mobile-action-btn {
    font-size: 15px !important;
    padding: 12px 16px !important;
  }
  
  .mobile-primary-btn {
    font-size: 17px !important;
    padding: 14px 20px !important;
    margin-top: 8px;
  }
  
  .selection-header,
  .details-header,
  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .detail-row {
    flex-direction: column;
    gap: 4px;
  }
  
  .detail-row .label {
    min-width: auto;
    font-size: 13px;
  }
  
  /* Ensure proper touch targets on mobile */
  .method-summary {
    min-height: 48px;
    padding: 8px;
  }
  
  .method-option {
    min-height: 56px;
    padding: 12px;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .add-new-method-form,
  .method-selection-view,
  .method-details-view {
    /* Mobile-friendly padding */
    padding: 16px;
  }
  
  .form-actions,
  .selection-actions,
  .details-actions {
    /* More prominent action buttons on mobile */
    margin-top: 20px;
    padding: 16px 0;
    border-top: 1px solid var(--tc-border-medium);
    margin-left: -16px;
    margin-right: -16px;
    padding-left: 16px;
    padding-right: 16px;
  }
}

/* Action button styling */
.form-actions {
  position: relative;
  margin-top: 20px;
  padding: 16px 0;
  background: var(--tc-bg-subtle);
  border-radius: 8px;
  border-top: 1px solid var(--tc-border-light);
  margin-left: -20px;
  margin-right: -20px;
  padding-left: 20px;
  padding-right: 20px;
}

.selection-actions,
.details-actions {
  position: relative;
  margin-top: 20px;
  padding: 16px 0;
  background: var(--tc-bg-card);
  border-radius: 8px;
  border-top: 1px solid var(--tc-border-light);
  margin-left: -20px;
  margin-right: -20px;
  padding-left: 20px;
  padding-right: 20px;
}

/* Dark mode support for actions */
[data-theme="dark"] .form-actions,
[data-theme="dark"] .selection-actions,
[data-theme="dark"] .details-actions {
  background: var(--tc-bg-card);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.3);
}
</style>

