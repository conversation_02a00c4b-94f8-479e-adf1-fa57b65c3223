# Test Voice Debug Report Optimization
# This script tests the optimized direct audio-to-report processing

Write-Host "Voice Debug Report Optimization Test" -ForegroundColor Green
Write-Host "=" * 50

# Check if backend is running
Write-Host "Checking backend status..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3000/api/ai/status" -Method GET -TimeoutSec 10 -ErrorAction Stop
    if ($response.success) {
        Write-Host "Backend is running" -ForegroundColor Green
        Write-Host "   AI Service Available: $($response.status.aiServiceAvailable)" -ForegroundColor Cyan
        Write-Host "   Voice to Report: $($response.status.features.voiceToReport)" -ForegroundColor Cyan
        Write-Host "   Supported Audio Formats: $($response.status.supportedAudioFormats -join ', ')" -ForegroundColor Cyan
    } else {
        Write-Host "Backend AI service not available" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Failed to connect to backend" -ForegroundColor Red
    Write-Host "Error Details:" -ForegroundColor Yellow
    Write-Host "   Exception Type: $($_.Exception.GetType().Name)" -ForegroundColor White
    Write-Host "   Error Message: $($_.Exception.Message)" -ForegroundColor White
    if ($_.Exception.Response) {
        Write-Host "   HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor White
        Write-Host "   HTTP Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor White
    }
    if ($_.Exception.InnerException) {
        Write-Host "   Inner Exception: $($_.Exception.InnerException.Message)" -ForegroundColor White
    }
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Cyan
    Write-Host "   - Ensure backend is running: cd backend; npm run dev" -ForegroundColor Yellow
    Write-Host "   - Check if port 3000 is available" -ForegroundColor Yellow
    Write-Host "   - Verify network connectivity" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Optimization Details:" -ForegroundColor Magenta
Write-Host "   - Eliminated speech-to-text transcription step" -ForegroundColor White
Write-Host "   - Direct audio processing with Gemini 2.5 Flash" -ForegroundColor White
Write-Host "   - Single API call for audio-to-report conversion" -ForegroundColor White
Write-Host "   - Enhanced audio format support (WAV, MP3, AIFF, AAC, OGG, FLAC)" -ForegroundColor White

Write-Host ""
Write-Host "To test the optimization:" -ForegroundColor Yellow
Write-Host "   1. Start the frontend: cd frontend; npm run dev" -ForegroundColor White
Write-Host "   2. Navigate to http://localhost:5173" -ForegroundColor White
Write-Host "   3. Open Debug Report modal and use voice recording" -ForegroundColor White
Write-Host "   4. Check browser console for 'Direct audio processing' logs" -ForegroundColor White

Write-Host ""
Write-Host "Expected improvements:" -ForegroundColor Green
Write-Host "   • Faster processing time (single API call)" -ForegroundColor White
Write-Host "   • Better accuracy (native audio understanding)" -ForegroundColor White
Write-Host "   • Reduced API usage and costs" -ForegroundColor White
Write-Host "   • More reliable voice recognition" -ForegroundColor White

Write-Host ""
Write-Host "Voice debug report optimization is ready for testing!" -ForegroundColor Green
