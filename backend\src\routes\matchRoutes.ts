import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { authMiddleware, JwtPayload } from '../middleware/auth';
import { MatchingService } from '../services/matchingService';
import { MatchStatus, MatchResponse } from '@prisma/client';

// Create router factory function to accept MatchingService dependency
export default function createMatchRoutes(matchingService: MatchingService) {
  const matchRouter = new Hono();

  // Validation schemas
  const matchIdParamSchema = z.object({
    matchId: z.string().min(1, 'Match ID is required')
  });

  const acceptMatchSchema = z.object({
    // No additional body data needed for accepting
  });

  const declineMatchSchema = z.object({
    reason: z.string().optional()
  });

  const getUserMatchesQuerySchema = z.object({
    status: z.nativeEnum(MatchStatus).optional()
  });

  /**
   * GET /api/matches - Get all matches for the authenticated user
   */
  matchRouter.get('/', authMiddleware, zValidator('query', getUserMatchesQuerySchema), async (c) => {
    const jwtPayload = c.get('jwtPayload') as JwtPayload;
    const { status } = c.req.valid('query');

    console.log(`[MatchRoutes] Getting matches for user ${jwtPayload.userId}, status filter: ${status || 'all'}`);

    try {
      const matches = await matchingService.getUserMatches(jwtPayload.userId, status);

      console.log(`[MatchRoutes] Found ${matches.length} matches for user ${jwtPayload.userId}`);      return c.json({
        success: true,
        matches: matches.map(match => ({
          id: match.id,
          matchId: match.matchId,
          status: match.status,
          compatibilityScore: Number(match.compatibilityScore),
          currencyA: match.currencyA,
          currencyB: match.currencyB,
          amountA: Number(match.amountA),
          amountB: Number(match.amountB),
          rateAToB: Number(match.rateAToB),
          rateBToA: Number(match.rateBToA),
          createdAt: match.createdAt.toISOString(),
          expiresAt: match.expiresAt.toISOString(),
          userAId: match.userAId,
          userBId: match.userBId,
          userAResponse: match.userAResponse,
          userBResponse: match.userBResponse,
          userARespondedAt: match.userARespondedAt?.toISOString(),
          userBRespondedAt: match.userBRespondedAt?.toISOString(),
          chatSessionId: match.chatSessionId,
          transactionId: match.transactionId,
          // Include other user's info (not both to keep response lean)
          otherUser: match.userAId === jwtPayload.userId ? {
            id: match.userB.id,
            username: match.userB.username,
            email: match.userB.email,
            reputationLevel: match.userB.reputationLevel
          } : {
            id: match.userA.id,
            username: match.userA.username,
            email: match.userA.email,
            reputationLevel: match.userA.reputationLevel
          },
          // Include relevant offer details
          myOffer: match.userAId === jwtPayload.userId ? {
            id: match.offerA.id,
            type: match.offerA.type,
            currencyPair: match.offerA.currencyPair,
            amount: match.offerA.amount,
            baseRate: match.offerA.baseRate
          } : {
            id: match.offerB.id,
            type: match.offerB.type,
            currencyPair: match.offerB.currencyPair,
            amount: match.offerB.amount,
            baseRate: match.offerB.baseRate
          },
          otherOffer: match.userAId === jwtPayload.userId ? {
            id: match.offerB.id,
            type: match.offerB.type,
            currencyPair: match.offerB.currencyPair,
            amount: match.offerB.amount,
            baseRate: match.offerB.baseRate
          } : {
            id: match.offerA.id,
            type: match.offerA.type,
            currencyPair: match.offerA.currencyPair,
            amount: match.offerA.amount,
            baseRate: match.offerA.baseRate
          }
        }))
      });
    } catch (error) {
      console.error(`[MatchRoutes] Error getting matches for user ${jwtPayload.userId}:`, error);
      return c.json({
        success: false,
        error: 'Failed to retrieve matches'
      }, 500);
    }
  });

  /**
   * GET /api/matches/:matchId - Get details for a specific match
   */
  matchRouter.get('/:matchId', authMiddleware, zValidator('param', matchIdParamSchema), async (c) => {
    const jwtPayload = c.get('jwtPayload') as JwtPayload;
    const { matchId } = c.req.valid('param');

    console.log(`[MatchRoutes] Getting match details ${matchId} for user ${jwtPayload.userId}`);

    try {
      const matches = await matchingService.getUserMatches(jwtPayload.userId);
      const match = matches.find(m => m.id === matchId);

      if (!match) {
        return c.json({
          success: false,
          error: 'Match not found or access denied'
        }, 404);
      }

      return c.json({
        success: true,
        match: {
          id: match.id,
          matchId: match.matchId,
          status: match.status,
          compatibilityScore: Number(match.compatibilityScore),
          matchCriteria: match.matchCriteria,
          currencyA: match.currencyA,
          currencyB: match.currencyB,
          amountA: Number(match.amountA),
          amountB: Number(match.amountB),
          rateAToB: Number(match.rateAToB),
          rateBToA: Number(match.rateBToA),
          createdAt: match.createdAt.toISOString(),
          updatedAt: match.updatedAt.toISOString(),
          expiresAt: match.expiresAt.toISOString(),
          userAResponse: match.userAResponse,
          userBResponse: match.userBResponse,
          userARespondedAt: match.userARespondedAt?.toISOString(),
          userBRespondedAt: match.userBRespondedAt?.toISOString(),
          declineReason: match.declineReason,
          // Full user details
          userA: {
            id: match.userA.id,
            username: match.userA.username,
            email: match.userA.email,
            reputationLevel: match.userA.reputationLevel
          },
          userB: {
            id: match.userB.id,
            username: match.userB.username,
            email: match.userB.email,
            reputationLevel: match.userB.reputationLevel
          },
          // Full offer details
          offerA: {
            id: match.offerA.id,
            type: match.offerA.type,
            currencyPair: match.offerA.currencyPair,
            amount: match.offerA.amount,
            baseRate: match.offerA.baseRate,
            adjustmentForLowerRep: match.offerA.adjustmentForLowerRep,
            adjustmentForHigherRep: match.offerA.adjustmentForHigherRep,
            status: match.offerA.status,
            createdAt: match.offerA.createdAt.toISOString()
          },
          offerB: {
            id: match.offerB.id,
            type: match.offerB.type,
            currencyPair: match.offerB.currencyPair,
            amount: match.offerB.amount,
            baseRate: match.offerB.baseRate,
            adjustmentForLowerRep: match.offerB.adjustmentForLowerRep,
            adjustmentForHigherRep: match.offerB.adjustmentForHigherRep,
            status: match.offerB.status,
            createdAt: match.offerB.createdAt.toISOString()
          },
          // Linked resources if available
          chatSessionId: match.chatSessionId,
          transactionId: match.transactionId
        }
      });
    } catch (error) {
      console.error(`[MatchRoutes] Error getting match ${matchId}:`, error);
      return c.json({
        success: false,
        error: 'Failed to retrieve match details'
      }, 500);
    }
  });

  /**
   * POST /api/matches/:matchId/accept - Accept a match
   */
  matchRouter.post('/:matchId/accept', authMiddleware, 
    zValidator('param', matchIdParamSchema),
    zValidator('json', acceptMatchSchema),
    async (c) => {
      const jwtPayload = c.get('jwtPayload') as JwtPayload;
      const { matchId } = c.req.valid('param');

      console.log(`[MatchRoutes] User ${jwtPayload.userId} accepting match ${matchId}`);

      try {
        const result = await matchingService.acceptMatch(matchId, jwtPayload.userId);        console.log(`[MatchRoutes] Match accepted successfully: ${matchId}, status: ${result.status}`);
        console.log(`[MatchRoutes] Chat session ID: ${result.match!.chatSessionId}, Transaction ID: ${result.match!.transactionId}`);
        
        return c.json({
          success: true,
          message: result.status === 'both_accepted' 
            ? 'Match accepted! A transaction has been created.' 
            : 'Match accepted! Waiting for the other user to accept.',
          status: result.status,
          chatSessionId: result.chatSessionId || result.match!.chatSessionId,
          transactionId: result.match!.transactionId,
          match: {
            id: result.match!.id,
            matchId: result.match!.matchId,
            status: result.match!.status,
            userAResponse: result.match!.userAResponse,
            userBResponse: result.match!.userBResponse,
            chatSessionId: result.match!.chatSessionId,
            transactionId: result.match!.transactionId
          }
        });
      } catch (error) {
        console.error(`[MatchRoutes] Error accepting match ${matchId}:`, error);
        
        let errorMessage = 'Failed to accept match';
        let statusCode = 500;        if (error instanceof Error) {
          if (error.message.includes('not found')) {
            errorMessage = 'Match not found';
            statusCode = 404;
          } else if (error.message.includes('Unauthorized')) {
            errorMessage = 'Unauthorized to accept this match';
            statusCode = 403;
          } else if (error.message.includes('expired')) {
            errorMessage = 'This match has expired';
            statusCode = 410;
          } else if (error.message.includes('already responded')) {
            errorMessage = 'You have already responded to this match';
            statusCode = 409;
          } else if (error.message.includes('no longer available')) {
            errorMessage = 'This match is no longer available';
            statusCode = 410;
          }
        }

        return c.json({
          success: false,
          error: errorMessage
        }, statusCode as any);
      }
    }
  );

  /**
   * POST /api/matches/:matchId/decline - Decline a match
   */
  matchRouter.post('/:matchId/decline', authMiddleware,
    zValidator('param', matchIdParamSchema),
    zValidator('json', declineMatchSchema),
    async (c) => {
      const jwtPayload = c.get('jwtPayload') as JwtPayload;
      const { matchId } = c.req.valid('param');
      const { reason } = c.req.valid('json');

      console.log(`[MatchRoutes] User ${jwtPayload.userId} declining match ${matchId} with reason: ${reason || 'none'}`);

      try {
        const match = await matchingService.declineMatch(matchId, jwtPayload.userId, reason);

        console.log(`[MatchRoutes] Match declined successfully: ${matchId}`);

        return c.json({
          success: true,
          message: 'Match declined successfully',
          match: {
            id: match.id,
            matchId: match.matchId,
            status: match.status,
            declineReason: match.declineReason
          }
        });
      } catch (error) {
        console.error(`[MatchRoutes] Error declining match ${matchId}:`, error);
        
        let errorMessage = 'Failed to decline match';
        let statusCode = 500;        if (error instanceof Error) {
          if (error.message.includes('not found')) {
            errorMessage = 'Match not found';
            statusCode = 404;
          } else if (error.message.includes('Unauthorized')) {
            errorMessage = 'Unauthorized to decline this match';
            statusCode = 403;
          } else if (error.message.includes('no longer available')) {
            errorMessage = 'This match is no longer available';
            statusCode = 410;
          }
        }

        return c.json({
          success: false,
          error: errorMessage
        }, statusCode as any);
      }
    }
  );

  /**
   * POST /api/matches/find-for-offer/:offerId - Manually trigger match finding for an offer (dev/admin)
   */
  matchRouter.post('/find-for-offer/:offerId', authMiddleware, async (c) => {
    const jwtPayload = c.get('jwtPayload') as JwtPayload;
    const offerId = c.req.param('offerId');

    console.log(`[MatchRoutes] User ${jwtPayload.userId} manually triggering match finding for offer ${offerId}`);

    try {
      const matches = await matchingService.findPotentialMatches(offerId);

      console.log(`[MatchRoutes] Found ${matches.length} new matches for offer ${offerId}`);

      return c.json({
        success: true,
        message: `Found ${matches.length} new matches`,
        matchesCount: matches.length,
        matches: matches.map(match => ({
          id: match.id,
          matchId: match.matchId,
          compatibilityScore: Number(match.compatibilityScore),
          expiresAt: match.expiresAt.toISOString()
        }))
      });
    } catch (error) {
      console.error(`[MatchRoutes] Error finding matches for offer ${offerId}:`, error);
      return c.json({
        success: false,
        error: 'Failed to find matches'
      }, 500);
    }
  });

  /**
   * POST /api/matches/cleanup-expired - Clean up expired matches (admin/cron job)
   */
  matchRouter.post('/cleanup-expired', authMiddleware, async (c) => {
    const jwtPayload = c.get('jwtPayload') as JwtPayload;

    console.log(`[MatchRoutes] User ${jwtPayload.userId} triggering expired matches cleanup`);

    try {
      const cleanedCount = await matchingService.cleanupExpiredMatches();

      console.log(`[MatchRoutes] Cleaned up ${cleanedCount} expired matches`);

      return c.json({
        success: true,
        message: `Cleaned up ${cleanedCount} expired matches`,
        cleanedCount
      });
    } catch (error) {
      console.error(`[MatchRoutes] Error cleaning up expired matches:`, error);
      return c.json({
        success: false,
        error: 'Failed to clean up expired matches'
      }, 500);
    }
  });

  return matchRouter;
}
