const axios = require('axios');

// Final verification test
async function finalVerification() {
  console.log('🔍 Final End-to-End Verification...\n');

  try {
    const response = await axios.post('http://localhost:3000/api/ai/analyze', {
      transcription: 'Quick test of the new predefined tag system',
      language: 'en',
      predefinedTags: {
        bug: ['test-tag', 'verification'],
        feature: ['final-check']
      }
    });

    if (response.data.success) {
      console.log('✅ API endpoint working with new predefined tags format');
      console.log('✅ Tag origins properly returned:', 
        response.data.generatedReport.suggestedTags?.map(t => `${t.tag}:${t.origin}`).join(', ') || 'None');
      console.log('🎉 VOICE-BASED BUG REPORTING ENHANCEMENT COMPLETE!');
    } else {
      console.log('❌ API test failed:', response.data.error);
    }
  } catch (error) {
    console.log('❌ Request failed:', error.message);
  }
}

finalVerification();
