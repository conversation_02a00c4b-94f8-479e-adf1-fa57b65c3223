// filepath: c:\Code\MUNygo\backend\src\test\token.test.ts
import { describe, it, expect } from 'vitest';
import { generateVerificationToken } from '../utils/token';

describe('token utility', () => {
  describe('generateVerificationToken', () => {
    it('should return a string', () => {
      const token = generateVerificationToken();
      expect(typeof token).toBe('string');
    });

    it('should return a string of length 64', () => {
      // 32 bytes * 2 hex characters per byte = 64 characters
      const token = generateVerificationToken();
      expect(token).toHaveLength(64);
    });

    it('should return a valid hexadecimal string', () => {
      const token = generateVerificationToken();
      // Regex to check if the string contains only hexadecimal characters (0-9, a-f, A-F)
      const hexRegex = /^[0-9a-fA-F]+$/;
      expect(hexRegex.test(token)).toBe(true);
    });
  });
});
