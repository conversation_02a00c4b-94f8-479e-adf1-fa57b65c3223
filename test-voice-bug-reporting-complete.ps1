#!/usr/bin/env pwsh

# Comprehensive Test Script for Enhanced Voice Bug Reporting System
# Tests the complete flow: voice input → AI analysis → user review → report submission → admin display

Write-Host "=== MUNygo Voice Bug Reporting - Complete System Test ===" -ForegroundColor Cyan
Write-Host "Testing enhanced features:" -ForegroundColor Yellow
Write-Host "  ✓ Report type field support" -ForegroundColor Green
Write-Host "  ✓ Hybrid AI tag system (predefined + AI-suggested)" -ForegroundColor Green
Write-Host "  ✓ MUNygo-specific app context in AI prompts" -ForegroundColor Green
Write-Host "  ✓ Tag origin tracking and display" -ForegroundColor Green
Write-Host ""

# Step 1: Verify backend is running
Write-Host "Step 1: Checking backend server..." -ForegroundColor Blue
try {
    $backendResponse = Invoke-RestMethod -Uri "http://localhost:3000/health" -Method GET -TimeoutSec 5
    Write-Host "✓ Backend server is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Backend server not accessible. Please start with 'npm run dev' in backend/" -ForegroundColor Red
    exit 1
}

# Step 2: Verify frontend is running
Write-Host "Step 2: Checking frontend server..." -ForegroundColor Blue
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:5173" -Method GET -TimeoutSec 5
    Write-Host "✓ Frontend server is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Frontend server not accessible. Please start with 'npm run dev' in frontend/" -ForegroundColor Red
    exit 1
}

# Step 3: Test database schema and tag origins
Write-Host "Step 3: Verifying database schema and tag origins..." -ForegroundColor Blue
try {
    $testDbResult = node backend/verify-tag-origins.js
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Database schema and tag origins verified" -ForegroundColor Green
    } else {
        Write-Host "✗ Database schema verification failed" -ForegroundColor Red
        Write-Host $testDbResult -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Database verification error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Test AI service with enhanced prompts
Write-Host "Step 4: Testing AI service with MUNygo context and predefined tags..." -ForegroundColor Blue

$testAiPayload = @{
    transcript = "The transaction flow is broken when I try to confirm payment. The timer keeps running even after I click confirm, and the other user doesn't see my confirmation. This is causing disputes."
    reportType = "bug"
    predefinedTags = @("transaction-flow", "payment-confirmation", "real-time-updates", "timer-issues")
} | ConvertTo-Json -Depth 3

try {
    $headers = @{
        'Content-Type' = 'application/json'
    }
    
    $aiResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/ai/analyze-report" -Method POST -Body $testAiPayload -Headers $headers -TimeoutSec 30
    
    Write-Host "✓ AI analysis completed successfully" -ForegroundColor Green
    Write-Host "  Report Type: $($aiResponse.reportType)" -ForegroundColor Cyan
    Write-Host "  Severity: $($aiResponse.severity)" -ForegroundColor Cyan
    Write-Host "  Tags:" -ForegroundColor Cyan
    
    $predefinedCount = 0
    $aiSuggestedCount = 0
    $userDefinedCount = 0
    
    foreach ($tag in $aiResponse.tags) {
        $originColor = switch ($tag.origin) {
            "PREDEFINED" { "Green" }
            "AI_SUGGESTED" { "Yellow" }
            "USER_DEFINED" { "Blue" }
            default { "White" }
        }
        Write-Host "    - $($tag.tag) ($($tag.origin))" -ForegroundColor $originColor
        
        switch ($tag.origin) {
            "PREDEFINED" { $predefinedCount++ }
            "AI_SUGGESTED" { $aiSuggestedCount++ }
            "USER_DEFINED" { $userDefinedCount++ }
        }
    }
    
    Write-Host "  Tag Origin Summary:" -ForegroundColor Cyan
    Write-Host "    Predefined: $predefinedCount" -ForegroundColor Green
    Write-Host "    AI-Suggested: $aiSuggestedCount" -ForegroundColor Yellow  
    Write-Host "    User-Defined: $userDefinedCount" -ForegroundColor Blue
    
    # Verify that at least some predefined tags were used
    if ($predefinedCount -gt 0) {
        Write-Host "✓ AI successfully used predefined tags" -ForegroundColor Green
    } else {
        Write-Host "⚠ AI did not use any predefined tags (this might be expected depending on context)" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "✗ AI analysis failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 5: Test full report creation
Write-Host "Step 5: Testing complete report submission..." -ForegroundColor Blue

$testReportPayload = @{
    transcript = "Voice input: The transaction flow is broken when I try to confirm payment"
    reportType = "bug"
    severity = "medium"
    tags = @(
        @{ tag = "transaction-flow"; origin = "PREDEFINED" }
        @{ tag = "payment-confirmation"; origin = "PREDEFINED" }
        @{ tag = "ui-responsiveness"; origin = "AI_SUGGESTED" }
        @{ tag = "critical-path"; origin = "AI_SUGGESTED" }
        @{ tag = "user-reported"; origin = "USER_DEFINED" }
    )
    aiAnalysis = @{
        summary = "Transaction confirmation UI issue causing user flow disruption"
        category = "Frontend Bug"
        priority = "High"
        affectedComponent = "Transaction Flow"
    }
    userAgent = "Test-Script/1.0"
    url = "/transaction/confirm"
    logEntries = @(
        @{
            level = "error"
            message = "Transaction confirmation timeout"
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "TransactionService"
        }
    )
} | ConvertTo-Json -Depth 4

try {
    $reportResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/debug-reports" -Method POST -Body $testReportPayload -Headers $headers -TimeoutSec 15
    
    Write-Host "✓ Report submitted successfully" -ForegroundColor Green
    Write-Host "  Report ID: $($reportResponse.id)" -ForegroundColor Cyan
    
    # Store the report ID for later verification
    $reportId = $reportResponse.id
    
} catch {
    Write-Host "✗ Report submission failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 6: Verify report retrieval and tag origins
Write-Host "Step 6: Verifying report retrieval with tag origins..." -ForegroundColor Blue

try {
    Start-Sleep -Seconds 2  # Give the database a moment
    
    $retrievedReport = Invoke-RestMethod -Uri "http://localhost:3000/api/debug-reports/$reportId" -Method GET -Headers $headers -TimeoutSec 10
    
    Write-Host "✓ Report retrieved successfully" -ForegroundColor Green
    Write-Host "  Type: $($retrievedReport.type)" -ForegroundColor Cyan
    Write-Host "  Severity: $($retrievedReport.severity)" -ForegroundColor Cyan
    Write-Host "  Tags with Origins:" -ForegroundColor Cyan
    
    foreach ($tag in $retrievedReport.tags) {
        $originColor = switch ($tag.origin) {
            "PREDEFINED" { "Green" }
            "AI_SUGGESTED" { "Yellow" }
            "USER_DEFINED" { "Blue" }
            default { "White" }
        }
        Write-Host "    - $($tag.tag) [$($tag.origin)]" -ForegroundColor $originColor
    }
    
} catch {
    Write-Host "✗ Report retrieval failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 7: Test admin endpoints
Write-Host "Step 7: Testing admin report listing..." -ForegroundColor Blue

try {
    $adminReports = Invoke-RestMethod -Uri "http://localhost:3000/api/debug-reports?limit=5" -Method GET -Headers $headers -TimeoutSec 10
    
    Write-Host "✓ Admin report listing successful" -ForegroundColor Green
    Write-Host "  Total reports available: $($adminReports.total)" -ForegroundColor Cyan
    Write-Host "  Reports in response: $($adminReports.reports.Count)" -ForegroundColor Cyan
    
    # Check if our test report is in the list
    $ourReport = $adminReports.reports | Where-Object { $_.id -eq $reportId }
    if ($ourReport) {
        Write-Host "✓ Test report found in admin listing" -ForegroundColor Green
        Write-Host "  Tags properly formatted with origins: $($ourReport.tags.Count) tags" -ForegroundColor Cyan
    } else {
        Write-Host "⚠ Test report not found in recent admin listing (might be expected)" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "✗ Admin report listing failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 8: Manual UI verification instructions
Write-Host ""
Write-Host "Step 8: Manual UI Verification Required" -ForegroundColor Blue
Write-Host "Please manually verify the following in the browser:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Frontend Voice Reporting (http://localhost:5173):" -ForegroundColor Cyan
Write-Host "   - Click the floating bug report button" -ForegroundColor White
Write-Host "   - Test voice input or text input" -ForegroundColor White
Write-Host "   - Verify AI analysis includes predefined tags" -ForegroundColor White
Write-Host "   - Check that tags show origin indicators (star = predefined, gear = AI, user = user)" -ForegroundColor White
Write-Host "   - Submit a test report" -ForegroundColor White
Write-Host ""
Write-Host "2. Admin Dashboard:" -ForegroundColor Cyan
Write-Host "   - Navigate to admin section" -ForegroundColor White
Write-Host "   - View reports in table and card grid views" -ForegroundColor White
Write-Host "   - Verify tags display with proper origin styling" -ForegroundColor White
Write-Host "   - Open report details modal and check tag origins" -ForegroundColor White
Write-Host "   - Confirm all report types are properly displayed" -ForegroundColor White

# Final summary
Write-Host ""
Write-Host "=== Test Summary ===" -ForegroundColor Cyan
Write-Host "✓ Backend server operational" -ForegroundColor Green
Write-Host "✓ Frontend server operational" -ForegroundColor Green  
Write-Host "✓ Database schema with tag origins verified" -ForegroundColor Green
Write-Host "✓ AI service with MUNygo context and predefined tags working" -ForegroundColor Green
Write-Host "✓ Report submission with tag origins successful" -ForegroundColor Green
Write-Host "✓ Report retrieval with proper tag origin formatting" -ForegroundColor Green
Write-Host "✓ Admin API endpoints functional" -ForegroundColor Green
Write-Host ""
Write-Host "Enhanced Voice Bug Reporting System is READY! 🎉" -ForegroundColor Green
Write-Host ""
Write-Host "Key Features Verified:" -ForegroundColor Yellow
Write-Host "- Report type field support" -ForegroundColor White
Write-Host "- Hybrid AI tag system (predefined + AI-suggested + user-defined)" -ForegroundColor White
Write-Host "- MUNygo-specific context in AI analysis" -ForegroundColor White
Write-Host "- Tag origin tracking and visual indicators" -ForegroundColor White
Write-Host "- Complete admin dashboard integration" -ForegroundColor White
Write-Host ""
Write-Host "Complete the manual UI verification steps above to finish testing." -ForegroundColor Cyan
