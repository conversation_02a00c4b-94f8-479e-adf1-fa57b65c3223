-- Fix payment method unique constraint issue
-- The current constraint prevents multiple non-default payment methods for the same currency
-- We need to drop the current constraint and create a conditional one

-- Drop the existing problematic constraint
DROP INDEX IF EXISTS "PaymentReceivingInfo_userId_currency_default_key";

-- Create a conditional unique constraint that only applies when isDefaultForUser is true
-- This allows multiple non-default methods per currency while ensuring only one default
CREATE UNIQUE INDEX "PaymentReceivingInfo_userId_currency_default_true_key" 
ON "PaymentReceivingInfo"("userId", "currency") 
WHERE "isDefaultForUser" = true;

-- Verify the fix by showing the current indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'PaymentReceivingInfo' 
    AND indexname LIKE '%default%';
