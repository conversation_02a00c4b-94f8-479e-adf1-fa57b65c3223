import axios from 'axios';
import { useAuthStore } from '@/stores/auth';
import router from '@/router';
import { i18n } from '@/i18n';

// Create an Axios instance that includes the auth token
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
});

// Flag to prevent multiple simultaneous refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: any) => void;
  reject: (reason: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

// Attach token to requests and check for refresh needs
apiClient.interceptors.request.use(async (config) => {
  const authStore = useAuthStore();
  const token = authStore.token;
  
  if (token) {
    // Check if token needs refresh before making the request
    if (authStore.shouldRefreshToken() && !isRefreshing && config.url !== '/auth/refresh') {
      try {
        await authStore.refreshToken();
      } catch (error) {
        console.warn('[ApiClient] Token refresh failed during request interceptor:', error);
        // Continue with the request anyway, let the response interceptor handle it
      }
    }
    
    config.headers.Authorization = `Bearer ${authStore.token}`;
  }
  
  return config;
}, error => {
  return Promise.reject(error);
});

// Global response interceptor for 401 Invalid token with token refresh logic
apiClient.interceptors.response.use(
  response => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (
      error.response &&
      error.response.status === 401 &&
      typeof error.response.data?.error === 'string' &&
      (error.response.data.error.toLowerCase().includes('invalid token') ||
       error.response.data.error.toLowerCase().includes('token expired')) &&
      !originalRequest._retry
    ) {
      const authStore = useAuthStore();
      
      // Don't attempt refresh for the refresh endpoint itself
      if (originalRequest.url?.includes('/auth/refresh')) {
        authStore.logout();
        const sessionExpiredMessage = `${(i18n.global as any).t('errors.sessionExpired')}. ${(i18n.global as any).t('errors.pleaseLoginAgain')}`;
        router.push({ name: 'login', query: { message: sessionExpiredMessage } });
        return Promise.reject(error);
      }

      if (isRefreshing) {
        // If a refresh is already in progress, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }).catch((err) => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const newToken = await authStore.refreshToken();
        processQueue(null, newToken);
        
        // Retry the original request with the new token
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        processQueue(refreshError, null);
        
        // Refresh failed, logout user
        authStore.logout();
        const sessionExpiredMessage = `${(i18n.global as any).t('errors.sessionExpired')}. ${(i18n.global as any).t('errors.pleaseLoginAgain')}`;
        router.push({ name: 'login', query: { message: sessionExpiredMessage } });
        
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }
    
    return Promise.reject(error);
  }
);

export default apiClient;
