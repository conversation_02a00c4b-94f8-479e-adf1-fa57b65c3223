#!/bin/bash

echo "🚀 MUNygo Production Deployment Script"
echo "======================================="

# Exit on any error
set -e

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command_exists docker; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

if ! command_exists docker-compose; then
    echo "❌ Docker Compose is not installed or not in PATH"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Load environment variables
if [ -f .env ]; then
    echo "📄 Loading environment variables from .env..."
    # Export variables for docker-compose
    set -a
    source .env
    set +a
    echo "✅ Environment variables loaded"
else
    echo "❌ .env file not found! Please create it with required variables."
    exit 1
fi

# Verify critical environment variables
echo "🔍 Verifying critical environment variables..."

REQUIRED_VARS=(
    "POSTGRES_DB"
    "POSTGRES_USER" 
    "POSTGRES_PASSWORD"
    "DATABASE_URL"
    "JWT_SECRET"
    "VITE_BACKEND_URL_FOR_CLIENT"
    "VITE_ADMIN_EMAILS"
    "GEMINI_API_KEY"
)

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Required environment variable $var is not set"
        exit 1
    fi
done

echo "✅ All required environment variables are set"

# Show current configuration
echo ""
echo "📊 Current Configuration:"
echo "  Database: $POSTGRES_DB"
echo "  Database User: $POSTGRES_USER"
echo "  Backend URL: $VITE_BACKEND_URL_FOR_CLIENT"
echo "  Debug Reports: ${VITE_ENABLE_DEBUG_REPORT:-true}"
echo "  Admin Emails: $VITE_ADMIN_EMAILS"
echo "  Gemini API: ${GEMINI_API_KEY:0:10}..." # Show only first 10 chars
echo ""

# Ask for confirmation
read -p "🤔 Do you want to proceed with deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "🛑 Deployment cancelled by user"
    exit 0
fi

echo "🧹 Step 1: Cleaning up existing containers and volumes..."
docker-compose down -v --remove-orphans
docker system prune -f
echo "✅ Cleanup completed"

echo "🔧 Step 2: Building images without cache..."
docker-compose build --no-cache --progress=plain
echo "✅ Build completed"

echo "🗄️ Step 3: Starting PostgreSQL and waiting for it to be ready..."
docker-compose up -d postgres
echo "⏳ Waiting for PostgreSQL to be ready..."

# Wait for PostgreSQL to be ready
for i in {1..30}; do
    if docker-compose exec postgres pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" >/dev/null 2>&1; then
        echo "✅ PostgreSQL is ready"
        break
    fi
    echo "  Still waiting... ($i/30)"
    sleep 2
done

if [ $i -eq 30 ]; then
    echo "❌ PostgreSQL failed to start within 60 seconds"
    docker-compose logs postgres
    exit 1
fi

echo "🗃️ Step 4: Running database migrations..."
docker-compose run --rm backend npx prisma migrate deploy
echo "✅ Database migrations completed"

echo "🎯 Step 5: Starting all services..."
docker-compose up -d
echo "✅ All services started"

echo "🏥 Step 6: Health checks..."
echo "⏳ Waiting for services to be healthy..."

# Wait for backend health check
for i in {1..60}; do
    if curl -s http://localhost:3000/health >/dev/null 2>&1; then
        echo "✅ Backend is healthy"
        break
    fi
    echo "  Waiting for backend... ($i/60)"
    sleep 2
done

# Wait for frontend
for i in {1..30}; do
    if curl -s http://localhost:8080 >/dev/null 2>&1; then
        echo "✅ Frontend is accessible"
        break
    fi
    echo "  Waiting for frontend... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📍 Service URLs:"
echo "  🌐 Frontend: http://localhost:8080"
echo "  🔧 Backend API: http://localhost:3000"
echo "  📊 Admin Debug Dashboard: http://localhost:8080/admin/debug-dashboard"
echo ""
echo "🔧 Debug Features:"
echo "  • Debug Report Button: Enabled (${VITE_ENABLE_DEBUG_REPORT:-true})"
echo "  • Admin Emails: $VITE_ADMIN_EMAILS"
echo "  • AI Analysis: Enabled (Gemini API configured)"
echo ""
echo "🧪 Quick Tests:"
echo "  1. Open http://localhost:8080 and register/login"
echo "  2. Check for debug report button (bug icon) in the navbar"
echo "  3. If you're an admin, access: http://localhost:8080/admin/debug-dashboard"
echo ""
echo "📋 To check service status:"
echo "  docker-compose ps"
echo ""
echo "📜 To view logs:"
echo "  docker-compose logs -f [service_name]"
echo ""
echo "🛑 To stop all services:"
echo "  docker-compose down"
