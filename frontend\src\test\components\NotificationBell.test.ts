import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, type VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import NotificationBell from '@/components/NotificationBell.vue'
import type { FrontendNotification } from '@/stores/notificationStore'
import { FrontendNotificationType } from '@/stores/notificationStore'

// Mock router
const mockPush = vi.fn()
const mockReplace = vi.fn()

vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace
  })
}))

// Mock i18n and related modules
vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn().mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'notifications.title': 'Notifications',
        'notifications.noNotifications': 'No notifications',
        'notifications.markAllRead': 'Mark all as read',
        'notifications.clear': 'Clear',
        'notifications.newInterest': 'New Interest',
        'notifications.interestAccepted': 'Interest Accepted',
        'notifications.interestDeclined': 'Interest Declined',
        'notifications.transactionUpdate': 'Transaction Update',
        'notifications.systemNotification': 'System Notification'
      }
      return translations[key] || key
    }),
    locale: { value: 'en' }
  })
}))

// Mock translation composable
vi.mock('@/composables/useTranslation', () => ({
  useTranslation: () => ({
    t: vi.fn().mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'notifications.title': 'Notifications',
        'notifications.noNotifications': 'No notifications',
        'notifications.markAllRead': 'Mark all as read',
        'notifications.clear': 'Clear',
        'notifications.newInterest': 'New Interest',
        'notifications.interestAccepted': 'Interest Accepted',
        'notifications.interestDeclined': 'Interest Declined',
        'notifications.transactionUpdate': 'Transaction Update',
        'notifications.systemNotification': 'System Notification'
      }
      return translations[key] || key
    }),
    currentLanguage: { value: 'en' },
    isRTL: { value: false },
    setLanguage: vi.fn()
  })
}))

// Mock the notification store types
vi.mock('@/stores/notificationStore', () => ({
  FrontendNotificationType: {
    OFFER_INTEREST_RECEIVED: 'OFFER_INTEREST_RECEIVED',
    OFFER_INTEREST_ACCEPTED: 'OFFER_INTEREST_ACCEPTED',
    OFFER_INTEREST_DECLINED: 'OFFER_INTEREST_DECLINED',
    TRANSACTION_UPDATE: 'TRANSACTION_UPDATE',
    SYSTEM_NOTIFICATION: 'SYSTEM_NOTIFICATION'
  }
}))

// Mock stores
vi.mock('@/stores/myOffersStore', () => ({
  useMyOffersStore: vi.fn()
}))

vi.mock('@/stores/language', () => ({
  useLanguageStore: vi.fn()
}))

// Mock useRtl composable
vi.mock('@/utils/rtl', () => ({
  useRtl: () => ({
    direction: { value: 'ltr' }
  })
}))

// Mock naive UI message service
const mockMessage = vi.fn()
vi.mock('naive-ui', () => ({
  useMessage: () => mockMessage
}))

// Mock icon components
vi.mock('@vicons/ionicons5', () => ({
  NotificationsOutline: { template: '<div data-testid="notifications-icon">notification-icon</div>' },
  CheckmarkDoneOutline: { template: '<div data-testid="checkmark-done-icon">checkmark-done-icon</div>' },
  CheckmarkOutline: { template: '<div data-testid="checkmark-icon">checkmark-icon</div>' },
  CloseOutline: { template: '<div data-testid="close-icon">close-icon</div>' },
  HeartOutline: { template: '<div data-testid="heart-icon">heart-icon</div>' },
  InformationCircleOutline: { template: '<div data-testid="info-icon">info-icon</div>' },
  TrendingUpOutline: { template: '<div data-testid="trending-up-icon">trending-up-icon</div>' }
}))

describe('NotificationBell', () => {
  let wrapper: VueWrapper
  let pinia: any
    const createMockNotification = (overrides: Partial<FrontendNotification> = {}): FrontendNotification => ({
    id: 'test-notification-1',
    userId: 'user-123',
    type: 'NEW_INTEREST_ON_YOUR_OFFER' as any,
    message: 'Someone is interested in your offer',
    isRead: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    relatedEntityType: 'OFFER',
    relatedEntityId: 'offer-123',
    actorId: 'actor-123',
    actorUsername: 'testuser',
    data: {},
    ...overrides
  })

  const createComponent = (notificationStoreState = {}) => {
    // Create fresh Pinia instance
    pinia = createPinia()
    setActivePinia(pinia)

    // Mock notification store with default state
    const mockNotificationStore = {
      notifications: [],
      unreadCount: 0,
      markAsRead: vi.fn(),
      clearNotification: vi.fn(),
      markAllAsRead: vi.fn(),
      ...notificationStoreState
    }

    // Mock myOffers store
    const mockMyOffersStore = {
      interests: []
    }

    // Mock language store
    const mockLanguageStore = {
      currentLanguage: 'en',
      setLanguage: vi.fn()
    }

    // Mount component with stubs for Naive UI components
    return mount(NotificationBell, {
      global: {
        plugins: [pinia],
        stubs: {
          'n-button': {
            template: '<button data-testid="nbutton" @click="$emit(\'click\')"><slot /></button>',
            emits: ['click']
          },
          'n-badge': {
            template: '<div data-testid="nbadge"><slot /></div>',
            props: ['value', 'show']
          },
          'n-dropdown': {
            template: '<div data-testid="ndropdown"><slot /></div>',
            props: ['options', 'show', 'placement', 'trigger']
          },
          'n-icon': {
            template: '<div data-testid="nicon"><slot /></div>',
            props: ['size']
          },
          'n-divider': {
            template: '<div data-testid="ndivider"></div>'
          },
          'n-empty': {
            template: '<div data-testid="nempty"><slot /></div>',
            props: ['description']
          },
          'n-text': {
            template: '<span data-testid="ntext"><slot /></span>',
            props: ['type', 'depth']
          }
        },
        mocks: {
          // Mock stores at component level
          $notificationStore: mockNotificationStore,
          $myOffersStore: mockMyOffersStore,
          $languageStore: mockLanguageStore
        }
      }
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    wrapper?.unmount()
  })
  describe('Rendering', () => {
    it('should render notification bell button', () => {
      wrapper = createComponent()
      const button = wrapper.find('[data-testid="nbutton"]')
      expect(button.exists()).toBe(true)
    })

    it('should show badge when there are unread notifications', () => {
      wrapper = createComponent({ unreadCount: 3 })
      
      const badge = wrapper.find('[data-testid="nbadge"]')
      expect(badge.exists()).toBe(true)
    })

    it('should not show badge when no unread notifications', () => {
      wrapper = createComponent({ unreadCount: 0 })
      
      const badge = wrapper.find('[data-testid="nbadge"]')
      expect(badge.exists()).toBe(false)
    })

    it('should show dropdown when button is clicked', async () => {
      wrapper = createComponent()
      const button = wrapper.find('[data-testid="nbutton"]')
      
      await button.trigger('click')
      await nextTick()
      
      const dropdown = wrapper.find('[data-testid="ndropdown"]')
      expect(dropdown.exists()).toBe(true)
    })
  })

  describe('Deep Linking Navigation', () => {
    it('should navigate to offer details for OFFER entity type', async () => {
      const notification = createMockNotification({
        relatedEntityType: 'OFFER',
        relatedEntityId: 'offer-123'
      })
      
      const mockMarkAsRead = vi.fn()
      wrapper = createComponent({ 
        notifications: [notification],
        markAsRead: mockMarkAsRead
      })
      
      // Find and click notification item
      const notificationItem = wrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      expect(mockPush).toHaveBeenCalledWith('/offers/offer-123')
      expect(mockMarkAsRead).toHaveBeenCalledWith('test-notification-1')
    })

    it('should navigate to chat for CHAT_SESSION entity type', async () => {
      const notification = createMockNotification({
        relatedEntityType: 'CHAT_SESSION',
        relatedEntityId: 'chat-456'
      })
      
      const mockMarkAsRead = vi.fn()
      wrapper = createComponent({ 
        notifications: [notification],
        markAsRead: mockMarkAsRead
      })
      
      const notificationItem = wrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      expect(mockPush).toHaveBeenCalledWith('/chat/chat-456')
      expect(mockMarkAsRead).toHaveBeenCalledWith('test-notification-1')
    })

    it('should navigate to transaction for TRANSACTION entity type', async () => {
      const notification = createMockNotification({
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: 'txn-789'
      })
      
      const mockMarkAsRead = vi.fn()
      wrapper = createComponent({ 
        notifications: [notification],
        markAsRead: mockMarkAsRead
      })
      
      const notificationItem = wrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      expect(mockPush).toHaveBeenCalledWith('/transactions/txn-789')
      expect(mockMarkAsRead).toHaveBeenCalledWith('test-notification-1')
    })

    it('should fallback to notification type navigation when no entity info', async () => {
      const notification = createMockNotification({
        type: FrontendNotificationType.NEW_INTEREST_ON_YOUR_OFFER,
        relatedEntityType: undefined,
        relatedEntityId: undefined
      })
      
      const mockMarkAsRead = vi.fn()
      wrapper = createComponent({ 
        notifications: [notification],
        markAsRead: mockMarkAsRead
      })
      
      const notificationItem = wrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      expect(mockPush).toHaveBeenCalledWith('/my-offers')
      expect(mockMarkAsRead).toHaveBeenCalledWith('test-notification-1')
    })

    it('should navigate using data.offerId for legacy notifications', async () => {
      const notification = createMockNotification({
        type: FrontendNotificationType.OFFER_STATUS_CHANGED,
        relatedEntityType: undefined,
        relatedEntityId: undefined,
        data: { offerId: 'legacy-offer-123' }
      })
      
      const mockMarkAsRead = vi.fn()
      wrapper = createComponent({ 
        notifications: [notification],
        markAsRead: mockMarkAsRead
      })
      
      const notificationItem = wrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      expect(mockPush).toHaveBeenCalledWith('/offers/legacy-offer-123')
      expect(mockMarkAsRead).toHaveBeenCalledWith('test-notification-1')
    })

    it('should navigate using data.chatSessionId for legacy chat notifications', async () => {
      const notification = createMockNotification({
        type: FrontendNotificationType.CHAT_MESSAGE_RECEIVED,
        relatedEntityType: undefined,
        relatedEntityId: undefined,
        data: { chatSessionId: 'legacy-chat-456' }
      })
      
      const mockMarkAsRead = vi.fn()
      wrapper = createComponent({ 
        notifications: [notification],
        markAsRead: mockMarkAsRead
      })
      
      const notificationItem = wrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      expect(mockPush).toHaveBeenCalledWith('/chat/legacy-chat-456')
      expect(mockMarkAsRead).toHaveBeenCalledWith('test-notification-1')
    })

    it('should default to home for unknown notification types', async () => {
      const notification = createMockNotification({
        type: 'UNKNOWN_TYPE' as any,
        relatedEntityType: undefined,
        relatedEntityId: undefined,
        data: {}
      })
      
      const mockMarkAsRead = vi.fn()
      wrapper = createComponent({ 
        notifications: [notification],
        markAsRead: mockMarkAsRead
      })
      
      const notificationItem = wrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      expect(mockPush).toHaveBeenCalledWith('/home')
      expect(mockMarkAsRead).toHaveBeenCalledWith('test-notification-1')
    })
  })

  describe('Error Handling', () => {
    it('should handle navigation errors gracefully', async () => {
      const notification = createMockNotification()
      const mockMarkAsRead = vi.fn()
      
      // Mock router.push to throw error
      mockPush.mockRejectedValueOnce(new Error('Navigation failed'))
      
      wrapper = createComponent({ 
        notifications: [notification],
        markAsRead: mockMarkAsRead
      })
      
      const notificationItem = wrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      // Should still mark as read even if navigation fails
      expect(mockMarkAsRead).toHaveBeenCalledWith('test-notification-1')
      expect(mockMessage).toHaveBeenCalledWith('notifications.navigationError', { type: 'error' })
    })

    it('should handle missing notification data gracefully', async () => {
      const notification = createMockNotification({
        relatedEntityType: 'OFFER',
        relatedEntityId: undefined
      })
      
      wrapper = createComponent({ notifications: [notification] })
      
      const notificationItem = wrapper.find('[data-testid="notification-item"]')
      await notificationItem.trigger('click')
      
      // Should fallback to type-based navigation
      expect(mockPush).toHaveBeenCalledWith('/my-offers')
    })
  })

  describe('Notification Actions', () => {
    it('should clear notification when clear button is clicked', async () => {
      const notification = createMockNotification()
      const mockClearNotification = vi.fn()
      
      wrapper = createComponent({ 
        notifications: [notification],
        clearNotification: mockClearNotification
      })
      
      const clearButton = wrapper.find('[data-testid="clear-notification-btn"]')
      await clearButton.trigger('click')
      
      expect(mockClearNotification).toHaveBeenCalledWith('test-notification-1')
    })

    it('should prevent dropdown close when notification item is clicked', async () => {
      const notification = createMockNotification()
      
      wrapper = createComponent({ notifications: [notification] })
      
      const notificationItem = wrapper.find('[data-testid="notification-item"]')
      const clickEvent = new Event('click')
      const stopPropagationSpy = vi.spyOn(clickEvent, 'stopPropagation')
      
      await notificationItem.element.dispatchEvent(clickEvent)
      
      expect(stopPropagationSpy).toHaveBeenCalled()
    })
  })

  describe('Multiple Notifications', () => {
    it('should handle multiple notifications correctly', () => {
      const notifications = [
        createMockNotification({ id: 'notif-1' }),
        createMockNotification({ id: 'notif-2', isRead: true }),
        createMockNotification({ id: 'notif-3' })
      ]
      
      wrapper = createComponent({ 
        notifications,
        unreadCount: 2
      })
      
      const notificationItems = wrapper.findAll('[data-testid="notification-item"]')
      expect(notificationItems).toHaveLength(3)
    })

    it('should show correct unread count', () => {
      wrapper = createComponent({ unreadCount: 5 })
      
      const badge = wrapper.find('[data-testid="nbadge"]')
      expect(badge.text()).toBe('5')
    })
  })
})
