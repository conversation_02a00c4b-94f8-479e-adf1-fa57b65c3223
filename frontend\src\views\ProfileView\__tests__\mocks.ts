import { vi } from 'vitest';

// --- Naive UI Mocks ---
export const messageSuccessSpy = vi.fn();
export const messageErrorSpy = vi.fn();
export const mockNFormValidate = vi.fn().mockResolvedValue(undefined);
export const mockNFormRestoreValidation = vi.fn();
export const mockNInputFocus = vi.fn();

// --- User Data Mock for Profile Testing ---
export const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  emailVerified: false,
  phoneNumber: null,
  phoneVerified: false,
  // username, createdAt, reputationLevel etc. will be added in specific tests
};

// --- Other Mocks (if any become problematic, move them here too) ---

// Example of how you might move other mocks if needed:
// export const fetchUserProfileMock = vi.fn();
// export const updatePhoneVerificationStatusMock = vi.fn();
// export const apiClientGetMock = vi.fn();
// export const apiClientPostMock = vi.fn();
// export const handleErrorMock = vi.fn((err: any, message: any, errorRef?: Ref<string | null>) => {
//   const errorMessage = err?.response?.data?.message || err?.message || 'An unknown error occurred';
//   if (message?.error) message.error(errorMessage);
//   if (errorRef && typeof errorRef === 'object' && 'value' in errorRef) {
//     errorRef.value = errorMessage;
//   }
//   return null;
// });
