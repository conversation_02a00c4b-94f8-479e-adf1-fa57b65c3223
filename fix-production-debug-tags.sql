-- Fix Production Database - Create Missing debug_report_tags Table
-- The production database is missing the entire debug_report_tags table

-- Step 1: Create the tag_origin enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tag_origin') THEN
        CREATE TYPE tag_origin AS ENUM ('PREDEFINED', 'AI_SUGGESTED', 'USER_DEFINED');
    END IF;
END $$;

-- Step 2: Create the complete debug_report_tags table
CREATE TABLE IF NOT EXISTS debug_report_tags (
    id TEXT NOT NULL,
    report_id TEXT NOT NULL,
    tag VARCHAR(50),
    tag_origin tag_origin NOT NULL DEFAULT 'USER_DEFINED',
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    tag_id TEXT,
    tag_name VARCHAR(50),
    
    CONSTRAINT debug_report_tags_pkey PRIMARY KEY (id)
);

-- Step 3: Create all the necessary indexes
CREATE INDEX IF NOT EXISTS debug_report_tags_report_id_idx ON debug_report_tags(report_id);
CREATE INDEX IF NOT EXISTS debug_report_tags_tag_idx ON debug_report_tags(tag);
CREATE INDEX IF NOT EXISTS debug_report_tags_tag_origin_idx ON debug_report_tags(tag_origin);
CREATE INDEX IF NOT EXISTS debug_report_tags_tag_id_idx ON debug_report_tags(tag_id);
CREATE INDEX IF NOT EXISTS debug_report_tags_tag_name_idx ON debug_report_tags(tag_name);

-- Step 4: Add foreign key constraints
-- Foreign key to debug_reports
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'debug_report_tags_report_id_fkey' 
        AND table_name = 'debug_report_tags'
    ) THEN
        ALTER TABLE debug_report_tags 
        ADD CONSTRAINT debug_report_tags_report_id_fkey 
        FOREIGN KEY (report_id) REFERENCES debug_reports(id) ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;
END $$;

-- Foreign key to tags
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'debug_report_tags_tag_id_fkey' 
        AND table_name = 'debug_report_tags'
    ) THEN
        ALTER TABLE debug_report_tags 
        ADD CONSTRAINT debug_report_tags_tag_id_fkey 
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
END $$;

-- Step 5: Verify the table was created successfully
\echo 'Checking debug_report_tags table structure:'
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'debug_report_tags' 
ORDER BY ordinal_position;

\echo 'Checking table constraints:'
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints
WHERE table_name = 'debug_report_tags';
