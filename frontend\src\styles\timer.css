/* Timer Display Styles (shared for all timer displays) */
.timer-display {
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  margin: 12px 0;
  border: 2px solid #10b981; /* Default green for plenty of time */
  background-color: rgba(16, 185, 129, 0.1);
  color: #065f46;
  transition: all 0.3s ease;
  font-weight: 600;
}

/* Plenty of time - Green (>1.5 hours) */
.timer-display.timer-plenty {
  background-color: rgba(16, 185, 129, 0.1);
  color: #065f46;
  border-color: #10b981;
}

/* Moderate time - Light Green (1-1.5 hours) */
.timer-display.timer-moderate {
  background-color: rgba(34, 197, 94, 0.1);
  color: #166534;
  border-color: #22c55e;
}

/* Warning - Orange (30min-1hour) */
.timer-display.timer-warning {
  background-color: rgba(251, 146, 60, 0.1);
  color: #c2410c;
  border-color: #fb923c;
}

/* Critical - Red with pulse (<30 minutes) */
.timer-display.timer-critical {
  background-color: rgba(239, 68, 68, 0.15);
  color: #dc2626;
  border-color: #ef4444;
  animation: pulse 2s infinite;
}

/* Expired - Dark Red (time ran out) */
.timer-display.timer-expired {
  background-color: rgba(245, 101, 101, 0.2);
  color: #991b1b;
  border-color: #f56565;
  border-style: dashed;
}

/* Elapsed - Gray (counting up after expiry) */
.timer-display.timer-elapsed {
  background-color: rgba(156, 163, 175, 0.1);
  color: #4b5563;
  border-color: #9ca3af;
  border-style: dotted;
}

/* Default fallback */
.timer-display.timer-default {
  background-color: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border-color: #3b82f6;
}

.timer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.timer-label {
  font-size: 0.85em;
  font-weight: 500;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
  100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
}
