// Test script to verify the enhanced tagging system
// Run this from the backend directory with: node ../test-tag-system.js

const { PrismaClient } = require('./backend/node_modules/@prisma/client');

async function testTagSystem() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧪 Testing Enhanced Tagging System...\n');

    // Test 1: Check if tag categories exist
    const categories = await prisma.tagCategory.findMany({
      include: {
        tags: {
          include: {
            reportTypes: true
          }
        }
      }
    });
    
    console.log(`✅ Found ${categories.length} tag categories:`);
    categories.forEach(cat => {
      console.log(`  - ${cat.name} (${cat.tags.length} tags)`);
    });
    console.log();

    // Test 2: Check if tags exist and are properly associated
    const tags = await prisma.tag.findMany({
      include: {
        category: true,
        reportTypes: true
      }
    });
    
    console.log(`✅ Found ${tags.length} total tags:`);
    tags.slice(0, 5).forEach(tag => {
      console.log(`  - ${tag.name} (${tag.category?.name || 'No category'}) - ${tag.reportTypes.length} report types`);
    });
    if (tags.length > 5) console.log(`  ... and ${tags.length - 5} more tags`);
    console.log();

    // Test 3: Check report type associations
    const reportTypeAssociations = await prisma.tagReportTypeAssociation.findMany({
      include: {
        tag: true
      }
    });
    
    console.log(`✅ Found ${reportTypeAssociations.length} tag-report-type associations:`);
    const reportTypeCounts = {};
    reportTypeAssociations.forEach(assoc => {
      reportTypeCounts[assoc.reportType] = (reportTypeCounts[assoc.reportType] || 0) + 1;
    });
    Object.entries(reportTypeCounts).forEach(([type, count]) => {
      console.log(`  - ${type}: ${count} tags`);
    });
    console.log();

    // Test 4: Test tag queries by report type
    const bugReportTags = await prisma.tag.findMany({
      where: {
        isActive: true,
        reportTypes: {
          some: {
            reportType: 'BUG_REPORT'
          }
        }
      },
      include: {
        category: true,
        reportTypes: true
      }
    });
    
    console.log(`✅ Found ${bugReportTags.length} tags for BUG_REPORT:`);
    bugReportTags.slice(0, 3).forEach(tag => {
      console.log(`  - ${tag.name} (${tag.category?.name || 'No category'})`);
    });
    if (bugReportTags.length > 3) console.log(`  ... and ${bugReportTags.length - 3} more`);
    console.log();

    console.log('🎉 Enhanced tagging system is working correctly!');
    console.log('\n📊 Summary:');
    console.log(`  • ${categories.length} tag categories`);
    console.log(`  • ${tags.length} total tags`);
    console.log(`  • ${reportTypeAssociations.length} tag-report-type associations`);
    console.log(`  • ${bugReportTags.length} tags available for bug reports`);

  } catch (error) {
    console.error('❌ Error testing tag system:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testTagSystem();
