# User Journey Flow Diagrams

## 📱 Primary User Journey: "I Need USD" (Simplified)

### Flow Overview
```mermaid
graph TD
    A[App Launch] --> B{User Type}
    B -->|New User| C[Quick Registration]
    B -->|Returning User| D[Home Dashboard]
    
    C --> D
    D --> E[I Need USD Button]
    E --> F[Enter Amount]
    F --> G[Auto-Price Display]
    G --> H{User Choice}
    
    H -->|Active| I[Browse Offers]
    H -->|Passive| J[Post Request & Wait]
    
    I --> K[View Offer Cards]
    K --> L[Tap Connect]
    L --> M[Chat Interface]
    
    J --> N[Notification Received]
    N --> M
    
    M --> O[Agree to Terms]
    O --> P[Transaction Flow]
    P --> Q[Complete Exchange]
```

### Detailed Step-by-Step Flow

#### Step 1: Intent Declaration (Home Screen)
```
User Action: Opens MUNygo app
System Response: 
- Shows simplified home screen
- Two primary buttons visible
- Recent activity feed below

User Action: Taps "I NEED USD"
System Response:
- Navigates to amount entry screen
- Shows clear progress (Step 1 of 2)
```

#### Step 2: Amount & Auto-Pricing
```
User Action: Enters desired amount ($500)
System Response:
- Real-time rate fetch from Bonbast
- Auto-calculates Toman equivalent
- Shows current market rate source
- Displays two clear options

User Action: Chooses path (Browse vs Post & Wait)
System Response:
- If Browse: Shows filtered offers
- If Post & Wait: Creates request, shows confirmation
```

#### Step 3A: Active Browsing Path
```
User Action: Browses available USD sellers
System Response:
- Shows offer cards sorted by relevance
- Highlights perfect matches
- Shows distance and availability

User Action: Taps "CONNECT" on preferred offer
System Response:
- Sends interest notification to seller
- Opens chat interface
- Shows "waiting for response" state
```

#### Step 3B: Passive Waiting Path
```
User Action: Posts request and closes app
System Response:
- Creates passive request in system
- Shows confirmation screen
- Sends push notification when match found

System Action: Matching algorithm finds seller
System Response:
- Sends push notification to user
- Opens chat interface when user returns
```

## 🔄 Secondary Journey: "I Have USD" (Simplified)

### Flow Overview
```mermaid
graph TD
    A[Home Dashboard] --> B[I Have USD Button]
    B --> C[Enter Amount Available]
    C --> D[Auto-Price Calculation]
    D --> E[Confirm Offer Details]
    E --> F[Post Offer]
    F --> G[Offer Live Confirmation]
    G --> H[Wait for Interest]
    H --> I[Receive Interest Notification]
    I --> J[Chat Interface]
    J --> K[Transaction Flow]
```

### Simplified Offer Creation
```
Step 1: Amount Entry
- User enters available USD amount
- System calculates Toman value at current rate
- Single screen, no complex options

Step 2: Confirmation & Post
- User reviews auto-calculated details
- Single "POST OFFER" button
- Immediate confirmation and next steps
```

## 💬 Connection & Chat Flow

### Interest Expression to Chat
```mermaid
graph TD
    A[User Sees Offer] --> B[Taps Connect]
    B --> C[Interest Sent]
    C --> D{Seller Response}
    
    D -->|Accept| E[Chat Opens]
    D -->|Decline| F[Decline Notification]
    D -->|No Response| G[Timeout After 24h]
    
    E --> H[Introduction Messages]
    H --> I[Negotiate Details]
    I --> J[Agree to Meet]
    J --> K[Start Transaction]
```

### Chat Interface Flow
```
Initial State:
- System message: "You're now connected with [Name]"
- Suggested conversation starters
- Transaction amount/rate reminder

Active Chat:
- Real-time messaging
- Payment coordination support
- Transaction initiation button

Transaction Integration:
- Embedded transaction controls
- Status updates in chat
- Quick action buttons
```

## ⚡ Transaction Management Flow

### Simplified Transaction Process
```mermaid
graph TD
    A[Both Users Agree] --> B[Payment Readiness Gate]
    B --> C{Both Ready?}
    
    C -->|Yes| D[Payment Declaration Phase]
    C -->|No| E[Wait for Readiness]
    E --> C
    
    D --> F[First User Declares Payment]
    F --> G[Second User Confirms Receipt]
    G --> H[Transaction Complete]
    H --> I[Rating & Feedback]
    
    F --> J[Timeout Timer]
    J -->|Expires| K[Dispute Resolution]
    G --> L[Success Celebration]
```

### Payment Readiness Flow
```
Step 1: Readiness Declaration
- Both users must declare payment info ready
- System validates payment methods
- Clear instructions for each user

Step 2: Payment Execution
- Linear process: Pay → Confirm → Complete
- Real-time status updates
- Clear timers and expectations

Step 3: Completion
- Both users confirm satisfaction
- Rating system
- Transaction history updated
```

## 📱 Mobile Navigation Architecture

### Bottom Tab Structure
```mermaid
graph TD
    A[Bottom Tabs] --> B[💱 Exchange]
    A --> C[🔍 Browse]
    A --> D[💬 Chat]
    A --> E[👤 Profile]
    
    B --> F[Create Offer/Request]
    B --> G[My Active Offers]
    B --> H[Recent Activity]
    
    C --> I[Available Offers]
    C --> J[Filter Options]
    C --> K[Search Function]
    
    D --> L[Active Chats]
    D --> M[Transaction Chats]
    D --> N[Archived Chats]
    
    E --> O[User Profile]
    E --> P[Settings]
    E --> Q[Transaction History]
```

### Screen-to-Screen Navigation
```
Tab Persistence:
- Bottom tabs always visible (except full-screen modes)
- Tab state preserved during navigation
- Back button respects tab context

Modal Navigation:
- Full-screen modals for forms
- Swipe-down to dismiss
- Clear "Done" or "Cancel" actions

Deep Linking:
- Notification deep links to relevant chat
- Share links open to specific offers
- URL structure supports mobile sharing
```

## 🔔 Notification & Alert Flows

### Real-Time Notification System
```mermaid
graph TD
    A[User Action] --> B[Real-time Event]
    B --> C{User Online?}
    
    C -->|Yes| D[In-App Notification]
    C -->|No| E[Push Notification]
    
    D --> F[Update UI Immediately]
    E --> G[Store for Later]
    
    G --> H{User Returns}
    H -->|Yes| I[Show Pending Updates]
    H -->|No| J[Send Reminder]
```

### Notification Types & Priorities
```
High Priority (Immediate):
- Interest received on your offer
- Payment declared in active transaction
- Transaction completion confirmation

Medium Priority (Batched):
- New offers matching your requests
- Profile views or ratings received
- System maintenance notifications

Low Priority (Weekly Summary):
- Market rate changes
- App feature updates
- Community achievements
```

## 📊 Error Handling & Edge Cases

### Error State Flow
```mermaid
graph TD
    A[User Action] --> B{Error Occurs?}
    
    B -->|No| C[Success State]
    B -->|Yes| D[Error Type]
    
    D -->|Network| E[Retry Options]
    D -->|Validation| F[Fix Instructions]
    D -->|System| G[Fallback Options]
    
    E --> H[Automatic Retry]
    F --> I[Highlight Issues]
    G --> J[Alternative Actions]
    
    H --> K{Resolved?}
    K -->|Yes| C
    K -->|No| L[Contact Support]
```

### Offline Handling
```
Offline Detection:
- Monitor network status
- Show offline indicator
- Queue actions for later

Offline Capabilities:
- View cached offers
- Read chat history
- Browse transaction history

Online Recovery:
- Sync queued actions
- Update cached data
- Resolve conflicts gracefully
```

## 🎯 Conversion Optimization Points

### Key Decision Points
```mermaid
graph TD
    A[App Download] --> B[Registration Start]
    B --> C{Complete Registration?}
    
    C -->|Yes| D[First Offer Creation]
    C -->|No| E[Abandonment Point 1]
    
    D --> F{Create First Offer?}
    F -->|Yes| G[Browse or Wait]
    F -->|No| H[Abandonment Point 2]
    
    G --> I{Connect with Someone?}
    I -->|Yes| J[First Transaction]
    I -->|No| K[Abandonment Point 3]
    
    J --> L{Complete Transaction?}
    L -->|Yes| M[Retained User]
    L -->|No| N[Abandonment Point 4]
```

### Optimization Strategies
```
Registration Optimization:
- Single-screen registration
- Social login options
- Skip optional fields

Offer Creation Optimization:
- Pre-filled intelligent defaults
- One-screen creation process
- Immediate success feedback

Connection Optimization:
- One-tap interest expression
- Clear response expectations
- Automatic matching suggestions

Transaction Optimization:
- Step-by-step guidance
- Clear progress indicators
- Automatic dispute resolution
```

---

*These user journey diagrams provide a visual guide for implementing the simplified, mobile-first user experience that prioritizes ease of use and conversion optimization.*
