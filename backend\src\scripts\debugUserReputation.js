const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Helper functions (copied from auth.ts)
function calculateReputationScore(user) {
  let score = 0;
  if (user.emailVerified) {
    score += 10;
  }
  if (user.phoneVerified) {
    score += 15;
  }
  const accountAgeInDays = Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24));
  score += Math.min(accountAgeInDays, 30);
  return score;
}

function calculateReputationLevel(score) {
  if (score < 10) return 1; // Newcomer
  if (score < 25) return 2; // Verified
  if (score < 40) return 3; // Reliable
  if (score < 60) return 4; // Trusted
  return 5; // Elite
}

async function debugUserReputation(email) {
  try {
    console.log(`\n=== DEBUG REPUTATION FOR: ${email} ===`);
    console.log(`Current Date: ${new Date().toISOString()}`);
    console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`Database URL: ${process.env.DATABASE_URL ? 'SET' : 'NOT SET'}`);

    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        phoneVerified: true,
        createdAt: true,
        reputationScore: true,
        reputationLevel: true,
      }
    });

    if (!user) {
      console.log(`❌ User with email ${email} NOT FOUND in database`);
      return;
    }

    console.log('\n📊 DATABASE VALUES:');
    console.log(`- ID: ${user.id}`);
    console.log(`- Email: ${user.email}`);
    console.log(`- Email Verified: ${user.emailVerified}`);
    console.log(`- Phone Verified: ${user.phoneVerified}`);
    console.log(`- Created At: ${user.createdAt ? user.createdAt.toISOString() : 'NULL'}`);
    console.log(`- Stored Reputation Score: ${user.reputationScore}`);
    console.log(`- Stored Reputation Level: ${user.reputationLevel}`);

    const createdAt = user.createdAt || new Date('2025-05-01');
    const accountAgeInDays = Math.floor((Date.now() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24));

    console.log('\n🧮 CALCULATED VALUES:');
    console.log(`- Account Age: ${accountAgeInDays} days`);
    console.log(`- Email Verified Points: ${user.emailVerified ? 10 : 0}`);
    console.log(`- Phone Verified Points: ${user.phoneVerified ? 15 : 0}`);
    console.log(`- Age Points: ${Math.min(accountAgeInDays, 30)}`);

    const calculatedScore = calculateReputationScore({
      emailVerified: user.emailVerified || false,
      phoneVerified: user.phoneVerified || false,
      createdAt,
    });

    const calculatedLevel = calculateReputationLevel(calculatedScore);

    console.log(`- TOTAL CALCULATED SCORE: ${calculatedScore}`);
    console.log(`- CALCULATED LEVEL: ${calculatedLevel}`);

    console.log('\n🎯 WHAT /auth/me ENDPOINT WOULD RETURN:');
    console.log(`- reputationScore: ${calculatedScore}`);
    console.log(`- reputationLevel: ${calculatedLevel}`);

    console.log('\n🔍 SCORE BREAKDOWN:');
    console.log('Level 1: 0-9 points (Newcomer)');
    console.log('Level 2: 10-24 points (Verified)');
    console.log('Level 3: 25-39 points (Reliable)');
    console.log('Level 4: 40-59 points (Trusted)');
    console.log('Level 5: 60+ points (Elite)');

  } catch (error) {
    console.error('❌ Error debugging user reputation:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Usage: node debugUserReputation.js <email>
const [,, email] = process.argv;
if (!email) {
  console.error('Usage: node debugUserReputation.js <email>');
  process.exit(1);
}

debugUserReputation(email)
  .then(() => process.exit(0))
  .catch((err) => { console.error(err); process.exit(1); });
