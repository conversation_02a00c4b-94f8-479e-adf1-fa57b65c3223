{"welcomeBack": "Welcome back to ArzAni", "signIn": "Sign In", "signUp": "Sign Up", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "username": "Username", "phoneNumber": "Phone Number", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "enterPasswordMinChars": "Enter your password (min 8 characters)", "enterConfirmPassword": "Confirm your password", "enterFirstName": "Enter your first name", "enterLastName": "Enter your last name", "enterUsername": "Enter your username", "usernameAvailable": "Username is available", "usernameTaken": "Username is already taken", "usernameCheckError": "Unable to check username availability", "you": "You", "otherParty": "Other Party", "otherUser": "Other User", "notAvailable": "N/A", "usernameSuggestions": "Suggestions", "emailAvailable": "Email is available", "emailTaken": "Email is already registered", "emailCheckError": "Unable to check email availability", "enterPhoneNumber": "Enter your phone number", "noAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "registerHere": "Register here", "loginHere": "Login here", "joinCommunity": "Join our trusted community", "agreeToTerms": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "agree": "I agree", "forgotPassword": "Forgot your password?", "resetPassword": "Reset Password", "rememberMe": "Remember me", "loginError": "<PERSON><PERSON>", "registrationError": "Registration Error", "emailVerificationSent": "Email verification sent", "phoneVerificationRequired": "Phone verification required", "accountCreated": "Account created", "loginSuccessful": "Login successful", "createAccount": "Create Account", "emailNotVerified": "Your email is not verified. Please check your verification email.", "resendVerification": "Resend Verification Email", "verificationEmailSent": "Verification email sent! Please check your inbox.", "logoutError": "Logout Error"}