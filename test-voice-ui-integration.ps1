#!/usr/bin/env pwsh

Write-Host "Voice Debug Report UI Integration Test" -ForegroundColor Cyan
Write-Host ("=" * 50) -ForegroundColor Gray

# Check if frontend is running
$frontendRunning = $false
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5174" -TimeoutSec 5 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        $frontendRunning = $true
        Write-Host "✓ Frontend is running on http://localhost:5174" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Frontend is not running" -ForegroundColor Yellow
}

# Check if backend is running
$backendRunning = $false
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/health" -TimeoutSec 5 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        $backendRunning = $true
        Write-Host "✓ Backend is running on http://localhost:3000" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Backend is not running" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "UI Integration Test Checklist:" -ForegroundColor Cyan
Write-Host "1. Navigate to the frontend URL above" -ForegroundColor White
Write-Host "2. Login or register an account" -ForegroundColor White
Write-Host "3. Click the Debug Report button (bug icon)" -ForegroundColor White
Write-Host "4. In the modal, click the Voice Recording tab" -ForegroundColor White
Write-Host "5. Grant microphone permissions when prompted" -ForegroundColor White
Write-Host "6. Record a voice message describing a bug" -ForegroundColor White
Write-Host "7. Check that the recording stops and processing begins" -ForegroundColor White
Write-Host "8. Verify the UI updates with the generated report" -ForegroundColor White

Write-Host ""
Write-Host "Expected Behavior:" -ForegroundColor Cyan
Write-Host "• Recording indicator appears during recording" -ForegroundColor White
Write-Host "• Processing spinner shows after recording stops" -ForegroundColor White
Write-Host "• Debug report form auto-fills with AI-generated content" -ForegroundColor White
Write-Host "• No intermediate transcription step (direct audio processing)" -ForegroundColor White

Write-Host ""
Write-Host "Debug Console Commands:" -ForegroundColor Cyan
Write-Host "// Check AI Analysis Store state" -ForegroundColor White
Write-Host "console.log(window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps[0]._instance.appContext.app._context.provides[\$pinia].state.value.aiAnalysis)" -ForegroundColor Gray

Write-Host ""
Write-Host "Browser Console Logs to Watch For:" -ForegroundColor Cyan
Write-Host "• 'Direct audio processing with Gemini' - confirms optimization" -ForegroundColor White
Write-Host "• 'AI Analysis Store - Processing voice audio' - store state update" -ForegroundColor White
Write-Host "• 'AI Analysis Store - Analysis complete' - successful completion" -ForegroundColor White

if ($frontendRunning -and $backendRunning) {
    Write-Host ""
    Write-Host "✓ Both services are running - Ready for testing!" -ForegroundColor Green
    Write-Host "Open browser and navigate to: http://localhost:5174" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "❌ Services need to be started:" -ForegroundColor Red
    if (-not $backendRunning) {
        Write-Host "  Start backend: cd backend; npm run dev" -ForegroundColor White
    }
    if (-not $frontendRunning) {
        Write-Host "  Start frontend: cd frontend; npm run dev" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "Voice Debug Report UI Integration Test Complete" -ForegroundColor Cyan
