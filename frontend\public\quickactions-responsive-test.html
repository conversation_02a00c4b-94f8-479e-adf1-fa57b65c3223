<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickActions Responsive Test - Mobile Layout Fix</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .title {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 2rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr; /* Mobile: Single column */
            gap: 1rem;
        }

        .action-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 1.25rem;
            text-align: center;
            min-height: 140px;
            max-width: 400px; /* Prevent overly wide cards on mobile */
            margin: 0 auto; /* Center on mobile */
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .action-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .icon-container {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .action-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: #333;
            line-height: 1.3;
        }

        .action-description {
            font-size: 0.875rem;
            color: #666;
            line-height: 1.5;
            margin-bottom: 1rem;
            flex-grow: 1;
            /* Improved text handling */
            hyphens: auto;
            word-wrap: break-word;
        }

        .action-button {
            background: #2080f0;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-button:hover {
            background: #1968d2;
            transform: translateY(-1px);
        }

        /* Tablet: 2 columns */
        @media (min-width: 768px) {
            .actions-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }
            
            .action-card {
                max-width: none; /* Remove mobile width constraint */
                min-height: 160px;
                padding: 1.5rem;
            }
            
            .action-title {
                font-size: 1.125rem;
            }
            
            .action-description {
                font-size: 0.9rem;
            }
        }

        /* Desktop: 3 columns */
        @media (min-width: 1024px) {
            .actions-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 2rem;
            }
            
            .action-card {
                min-height: 180px;
                padding: 2rem;
            }
            
            .action-title {
                font-size: 1.25rem;
            }
            
            .action-description {
                font-size: 1rem;
            }
        }

        /* Test viewport indicators */
        .viewport-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            z-index: 1000;
        }

        /* Arabic text simulation */
        .rtl-text {
            direction: rtl;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="viewport-indicator" id="viewportIndicator">Mobile View</div>
    
    <div class="container">
        <h2 class="title">عمليات سريع (Quick Actions) - Mobile-First Layout</h2>
        
        <div class="actions-grid">
            <!-- Card 1: Profile -->
            <div class="action-card">
                <div class="icon-container">👤</div>
                <h3 class="action-title rtl-text">پروفایل</h3>
                <p class="action-description rtl-text">اطلاعات پروفایل و جزئیات پرداخت خود را بررسی کنید</p>
                <button class="action-button">مشاهده پروفایل →</button>
            </div>
            
            <!-- Card 2: Matches with badge -->
            <div class="action-card">
                <div class="icon-container">💖 <span style="background: #f0a020; color: white; border-radius: 50%; padding: 2px 6px; font-size: 0.7rem; position: absolute; margin-left: -10px; margin-top: -10px;">4</span></div>
                <h3 class="action-title rtl-text">تطبیق‌های من</h3>
                <p class="action-description rtl-text">تطبیق‌های موجود پیشنهادها را مشاهده و پاسخ دهید</p>
                <button class="action-button">مشاهده تطبیق‌ها →</button>
            </div>
            
            <!-- Card 3: My Offers -->
            <div class="action-card">
                <div class="icon-container">📄</div>
                <h3 class="action-title rtl-text">پیشنهادهای من</h3>
                <p class="action-description rtl-text">پیشنهادهای موجود خود را مدیریت کنید و درخواست‌ها را مشاهده کنید</p>
                <button class="action-button">مشاهده پیشنهادها →</button>
            </div>
        </div>

        <div style="margin-top: 3rem; padding: 2rem; background: rgba(255, 255, 255, 0.1); border-radius: 12px; color: white;">
            <h3 style="margin-bottom: 1rem;">📱 Mobile-First Layout Benefits:</h3>
            <ul style="line-height: 1.8; list-style-position: inside;">
                <li><strong>Mobile (320px-767px):</strong> Single column, comfortable card width, readable text</li>
                <li><strong>Tablet (768px+):</strong> Two columns, better space utilization</li>
                <li><strong>Desktop (1024px+):</strong> Three columns, optimal for large screens</li>
                <li><strong>Text Readability:</strong> Proper line length, hyphens, word-wrap</li>
                <li><strong>Touch-Friendly:</strong> Adequate spacing and touch targets</li>
            </ul>
        </div>
    </div>

    <script>
        // Update viewport indicator
        function updateViewportIndicator() {
            const indicator = document.getElementById('viewportIndicator');
            const width = window.innerWidth;
            
            if (width < 768) {
                indicator.textContent = `Mobile View (${width}px)`;
                indicator.style.background = '#e74c3c';
            } else if (width < 1024) {
                indicator.textContent = `Tablet View (${width}px)`;
                indicator.style.background = '#f39c12';
            } else {
                indicator.textContent = `Desktop View (${width}px)`;
                indicator.style.background = '#27ae60';
            }
        }

        // Update on load and resize
        updateViewportIndicator();
        window.addEventListener('resize', updateViewportIndicator);

        // Add card interaction feedback
        document.querySelectorAll('.action-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98) translateY(-2px)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
