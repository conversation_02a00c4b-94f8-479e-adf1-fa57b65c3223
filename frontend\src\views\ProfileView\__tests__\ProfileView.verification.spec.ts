import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { createTestingP<PERSON> } from '@pinia/testing';
import { useAuthStore } from '@/stores/auth';
import ProfileView from '../ProfileView.vue';
// ---> IMPORT ALL USED NAIVE UI COMPONENTS <---
import {
  NConfigProvider, NMessageProvider, NCard, NSpin, NDescriptions, NDescriptionsItem,
  NTag, NDivider, NH3, NAlert, NForm, NFormItem, NInput, NButton, NIcon, NTooltip
} from 'naive-ui';
import type { UserInfo } from '@/types/auth';
import { ref, computed, nextTick } from 'vue';
import apiClient from '@/services/apiClient';
import type { Mock } from 'vitest';

// --- Mock apiClient ---
// We need to mock the actual implementation of post
vi.mock('@/services/apiClient', () => ({
  default: {
    post: vi.fn(),
    // Add get, put, delete mocks if needed by other component logic
  }
}));
// Cast the mock to access mock functions directly
const apiClientPostMock = apiClient.post as Mock;

// --- Mock naive-ui useMessage ---
const messageSuccessSpy = vi.fn();
const messageErrorSpy = vi.fn(); // Although not used in happy path, good practice to include
vi.mock('naive-ui', async (importOriginal) => {
  const originalNaive = await importOriginal<typeof import('naive-ui')>();
  return {
    ...originalNaive, // Keep original components
    useMessage: () => ({ // Provide mock implementation for useMessage
      success: messageSuccessSpy,
      error: messageErrorSpy,
    }),
  };
});

// --- Mock the auth store module ---
const mockUserRef = ref<UserInfo | null>(null);
const fetchUserProfileMock = vi.fn();
// ---> Modify the mock function to update state <---
const updatePhoneVerificationStatusMock = vi.fn((status: boolean, phoneNumber: string | null) => {
  if (mockUserRef.value) {
    mockUserRef.value.phoneVerified = status;
    mockUserRef.value.phoneNumber = phoneNumber;
  }
});
// -------------------------------------------------

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    user: mockUserRef,
    fetchUserProfile: fetchUserProfileMock,
    isLoggedIn: computed(() => !!mockUserRef.value),
    updatePhoneVerificationStatus: updatePhoneVerificationStatusMock, // Use the state-updating mock
  }),
}));
// -----------------------------------------

// Required wrapper to provide Naive UI context
const TestWrapper = {
  template: `
    <n-config-provider>
      <n-message-provider>
        <profile-view />
      </n-message-provider>
    </n-config-provider>
  `,
  components: {
    ProfileView,
    // ---> REGISTER ALL IMPORTED NAIVE UI COMPONENTS <---
    NConfigProvider,
    NMessageProvider,
    NCard,
    NSpin,
    NDescriptions,
    NDescriptionsItem,
    NTag,
    NDivider,
    NH3,
    NAlert,
    // NForm, // Keep NForm stubbed below
    // NFormItem, // Keep NFormItem stubbed below
    NInput,
    NButton,
    NIcon,
    NTooltip
  },
};

describe('ProfileView.vue - Phone Verification Flow', () => {
  let wrapper: ReturnType<typeof mount>;

  // Function to setup mocks and mount component for each test
  const setupTest = (initialUser: UserInfo) => {
    // Reset mocks before each test setup
    vi.resetAllMocks();
    apiClientPostMock.mockClear();
    messageSuccessSpy.mockClear();
    messageErrorSpy.mockClear();
    updatePhoneVerificationStatusMock.mockClear(); // Make sure this is cleared

    // Set initial user state for the test
    mockUserRef.value = initialUser;

    // Default mock implementations (can be overridden)
    fetchUserProfileMock.mockResolvedValue(undefined); // Mock the initial profile fetch
    apiClientPostMock.mockResolvedValue({ data: {} }); // Default API mock

    // Mount the component
    wrapper = mount(TestWrapper, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
        stubs: { // Keep the stubs for NForm and NFormItem
          NForm: {
            // Provide a basic template so the form content renders
            template: '<form @submit.prevent><slot></slot></form>',
            methods: {
              // Provide a fake 'validate' method that always succeeds instantly
              validate: vi.fn().mockResolvedValue(undefined),
              // Provide a fake 'restoreValidation' method to prevent errors
              restoreValidation: vi.fn(),
            }
          },
          // ---> ADD STUB FOR NFormItem <---
          NFormItem: {
            // Basic template just to render the default slot (where the input is)
            template: '<div><slot></slot></div>',
            // No specific methods needed to mock here usually
          }
          // ---> END OF NFormItem STUB <---
        } // <--- END OF STUBS OBJECT
      },
    });
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
    // Clear any timers if necessary (e.g., from rate limit tests later)
  });

  it('completes the phone verification happy path', async () => {
    // Arrange: Initial unverified user
    const initialUser: UserInfo = {
      id: 'test-user-1',
      email: '<EMAIL>',
      emailVerified: true,
      phoneVerified: false, // Start as unverified
      phoneNumber: null,
    };
    const testPhoneNumber = '+15551234567';
    const testOtpCode = '123456';

    setupTest(initialUser);
    const authStore = useAuthStore(); // Get instance after setup

    // Mock API responses for this specific test
    apiClientPostMock
      .mockResolvedValueOnce({ // Mock for /send-otp
        data: { message: 'OTP sent successfully' }
      })
      .mockResolvedValueOnce({ // Mock for /verify-otp
        data: { message: 'Phone verified successfully', phoneNumber: testPhoneNumber } // Ensure mock returns phone number
      });

    // --- Act & Assert: Initial State ---
    await flushPromises(); // Wait for initial fetchUserProfile mock
    await nextTick(); // Allow watcher and initial DOM updates
    await nextTick(); // Add an extra tick for further stabilization
    const phoneInput = wrapper.find('[data-testid="phone-input"] input');
    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
    const phoneFormElement = wrapper.find('[data-testid="phone-form"]'); // Use specific selector for the phone form

    expect(phoneFormElement.exists()).toBe(true); // Verify phone form exists
    expect(phoneFormElement.isVisible()).toBe(true); // Assert the phone form is initially visible
    expect(phoneInput.exists()).toBe(true); // Check if the actual input exists
    expect(sendOtpButton.exists()).toBe(true);

    // Check the OTP form (which contains otp-input) based on Guideline #24
    // It's controlled by v-show="showOtpForm", which is initially false.
    const otpFormElement = wrapper.find('[data-testid="otp-form"]');
    expect(otpFormElement.exists()).toBe(true); // v-show means it's in the DOM, even if not visible
    expect(otpFormElement.attributes('style')).toContain('display: none;'); // Check style for v-show="false"

    expect(wrapper.find('[data-testid="phone-verified-message"]').exists()).toBe(false); // Verified message hidden
    expect(sendOtpButton.attributes('disabled')).toBeUndefined(); // Good check to ensure button isn't disabled

    // --- Act & Assert: Request OTP ---
    await phoneInput.setValue(testPhoneNumber);

    // --- TRIGGER SUBMIT ON THE FORM ---
    await sendOtpButton.trigger('click');
    // ----------------------------------

    // ---- WAIT HERE ----
    // Wait for the async requestOtp function (including validation and API call) to finish
    await flushPromises();
    // -------------------

    // Assert API call was made
    expect(apiClientPostMock).toHaveBeenCalledWith('/auth/phone/send-otp', { phoneNumber: testPhoneNumber });

    // Add assertion: Ensure no error message was shown (indicating validation likely passed)
    expect(messageErrorSpy).not.toHaveBeenCalled();

    // Wait for DOM updates resulting from the successful API call (e.g., form switch)
    await nextTick();
    await nextTick();

    // ---> FIX: Align expected message with the mock response data <---
    expect(messageSuccessSpy).toHaveBeenCalledWith('OTP sent successfully');
    // -----------------------------------------------------------------

    // Assert that the phone form (which contains the phone-input) is now hidden
    // by checking its style attribute, as per Guideline #24.
    const phoneFormElementAfterRequest = wrapper.find('[data-testid="phone-form"]');
    expect(phoneFormElementAfterRequest.exists()).toBe(true); // It's still in the DOM due to v-show
    expect(phoneFormElementAfterRequest.attributes('style')).toContain('display: none;');

    // Assert that the OTP form is now visible
    const otpFormElementAfterRequest = wrapper.find('[data-testid="otp-form"]');
    expect(otpFormElementAfterRequest.exists()).toBe(true);
    expect(otpFormElementAfterRequest.isVisible()).toBe(true); // OTP form should now be visible

    const otpInput = wrapper.find('[data-testid="otp-input"] input');
    const verifyOtpButton = wrapper.find('[data-testid="verify-otp-button"]');
    // ---> FIX: Find the single form present after the switch <---
    const otpForm = wrapper.find('[data-testid="otp-form"]'); // Use the specific selector for the OTP form
    // -----------------------------------------------------------

    expect(otpInput.exists()).toBe(true);
    expect(verifyOtpButton.exists()).toBe(true);
    expect(otpForm.exists()).toBe(true); // This should now work correctly
    expect(verifyOtpButton.attributes('disabled')).toBeUndefined();

    // --- Act & Assert: Verify OTP ---
    await otpInput.setValue(testOtpCode);
    await verifyOtpButton.trigger('click'); // This now uses the correctly selected OTP form

    // ---- WAIT HERE ----
    // Wait for the async verifyOtp function (including validation, API call, and STORE update)
    await flushPromises();
    // -------------------

    // Assert API call and success message
    expect(apiClientPostMock).toHaveBeenCalledWith('/auth/phone/verify-otp', { otpCode: testOtpCode });
    expect(messageSuccessSpy).toHaveBeenCalledWith('Phone verified successfully');

    // Assert store action was called (mock function now also updates state)
    expect(updatePhoneVerificationStatusMock).toHaveBeenCalledWith(true, testPhoneNumber);

    // ---- WAIT FOR STATE UPDATE TO REFLECT IN DOM ----
    await flushPromises(); // Ensure microtasks from state update are done
    await nextTick(); // Allow final DOM updates
    // ---------------------------------------------------

    // Assert final state: Verification section hidden, verified message shown
    // The otp-input and verify-otp-button are inside a div controlled by v-if,
    // which is removed from the DOM when phoneVerified becomes true.
    expect(wrapper.find('[data-testid="otp-input"] input').exists()).toBe(false);
    expect(wrapper.find('[data-testid="verify-otp-button"]').exists()).toBe(false);
    // ---> This assertion should now pass <---
    expect(wrapper.find('[data-testid="phone-verification-title"]').exists()).toBe(false);
    // ---------------------------------------
    expect(wrapper.find('[data-testid="phone-verified-message"]').exists()).toBe(true);

    // Check the tag inside the descriptions list
    const phoneStatusTag = wrapper.find('[data-testid="phone-status-verified"]');
    expect(phoneStatusTag.exists()).toBe(true);
    expect(phoneStatusTag.text()).toBe('Verified');
    expect(wrapper.text()).toContain(`(${testPhoneNumber})`);

  }); // End of 'it' block

  // ... rest of file ...
});