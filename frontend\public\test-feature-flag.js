// HomeView Feature Flag Test Utility
// Run this in the browser console to test the feature flag

console.log('🧪 HomeView Migration Feature Flag Test');

// Function to test the feature flag
function testHomeViewFeatureFlag() {
  console.log('\n=== HomeView Feature Flag Test ===');
  
  // Check current state
  const currentValue = localStorage.getItem('useNewHomeDesign');
  console.log('1. Current localStorage value:', currentValue);
  
  // Check if store exists
  if (window.__PINIA__ && window.__PINIA__.state.value.uiPreferences) {
    const storeValue = window.__PINIA__.state.value.uiPreferences.useNewHomeDesign;
    console.log('2. Store value:', storeValue);
  } else {
    console.log('2. Store not found - page might need refresh');
  }
  
  console.log('\n📋 Test Steps:');
  console.log('1. Run: enableNewHomeDesign()');
  console.log('2. Refresh the page');
  console.log('3. Run: disableNewHomeDesign()');
  console.log('4. Refresh the page again');
  console.log('\n💡 Or manually set: localStorage.setItem("useNewHomeDesign", "true"), then refresh');
}

// Helper functions for easy testing
function enableNewHomeDesign() {
  localStorage.setItem('useNewHomeDesign', 'true');
  console.log('✅ Enabled new HomeView design');
  console.log('🔄 Please refresh the page to see changes');
  return 'Refresh the page to see the new component-based design!';
}

function disableNewHomeDesign() {
  localStorage.setItem('useNewHomeDesign', 'false');
  console.log('❌ Disabled new HomeView design');
  console.log('🔄 Please refresh the page to see changes');
  return 'Refresh the page to see the original design!';
}

function checkFeatureFlag() {
  const value = localStorage.getItem('useNewHomeDesign');
  const isEnabled = value === 'true';
  console.log(`🎯 Feature flag is: ${isEnabled ? 'ENABLED' : 'DISABLED'}`);
  console.log(`📦 Raw value: "${value}"`);
  return isEnabled;
}

// Make functions globally available
window.testHomeViewFeatureFlag = testHomeViewFeatureFlag;
window.enableNewHomeDesign = enableNewHomeDesign;
window.disableNewHomeDesign = disableNewHomeDesign;
window.checkFeatureFlag = checkFeatureFlag;

// Auto-run the test
testHomeViewFeatureFlag();

console.log('\n🚀 Available functions:');
console.log('- testHomeViewFeatureFlag()');
console.log('- enableNewHomeDesign()');
console.log('- disableNewHomeDesign()');
console.log('- checkFeatureFlag()');
