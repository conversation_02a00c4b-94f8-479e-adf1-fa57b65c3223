// Connection Testing Utilities
import centralizedSocketManager from '@/services/centralizedSocketManager';
import { useConnectionStore } from '@/stores/connection';

export function testConnectionMonitoring() {
  const connectionStore = useConnectionStore();
  
  console.log('🧪 Starting Connection Tests...');
  console.log('Current connection state:', {
    isConnected: connectionStore.isConnected,
    quality: connectionStore.connectionQuality,
    transport: connectionStore.transportType,
    status: connectionStore.connectionStatus,
    browserOnline: navigator.onLine
  });
  
  return {    // Test 1: Force disconnect
    testDisconnect: () => {
      console.log('🔌 Testing disconnect...');
      const socket = centralizedSocketManager.getSocket();
      if (socket) {
        socket.disconnect();
        console.log('Disconnected socket. Check connection icon!');
      }
    },
      // Test 2: Force reconnect
    testReconnect: () => {
      console.log('🔄 Testing reconnect...');
      try {
        centralizedSocketManager.initializeSocket();
        console.log('Initiated reconnection. Check connection icon!');
      } catch (error) {
        console.error('Reconnection failed:', error);
      }
    },
      // Test 3: Simulate browser offline (DevTools alternative)
    testBrowserOffline: () => {
      console.log('🚫 Simulating browser offline...');
      // Temporarily override navigator.onLine
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false
      });
      
      // Trigger offline event
      window.dispatchEvent(new Event('offline'));
      console.log('Browser offline simulated. Check connection icon!');
      
      // Auto-restore after 5 seconds
      setTimeout(() => {
        Object.defineProperty(navigator, 'onLine', {
          writable: true,
          value: true
        });
        window.dispatchEvent(new Event('online'));
        console.log('Browser back online simulated.');
      }, 5000);
    },
      // Test 3.5: Test yellow reconnecting icon (controlled)
    testReconnectingState: () => {
      console.log('🟡 Testing yellow reconnecting icon...');
        // Method: Disconnect and manually control reconnecting state
      const socket = centralizedSocketManager.getSocket();
      
      if (socket) {
        console.log('Step 1: Disconnecting socket...');
        socket.disconnect();
        
        // Wait for disconnect to register, then set reconnecting state
        setTimeout(() => {
          console.log('Step 2: Setting reconnecting state (should see YELLOW icon now)...');
          connectionStore.setReconnecting();
          
          // Hold the reconnecting state for 5 seconds so you can see it
          setTimeout(() => {
            console.log('Step 3: Reconnecting after 5 seconds...');
            socket.connect();
          }, 5000);
          
        }, 500);
      }
    },
    
    // Test 3.6: Force reconnecting state manually (better for testing)
    testForceReconnectingIcon: () => {
      console.log('🟡 Forcing yellow reconnecting icon for 7 seconds...');
      
      // First disconnect if connected
      if (connectionStore.isConnected) {
        connectionStore.setDisconnected('manual_test');
        console.log('Step 1: Set to disconnected (RED icon)');
      }
      
      // Then set to reconnecting state after 1 second
      setTimeout(() => {
        connectionStore.setReconnecting();
        console.log('Step 2: Set to reconnecting (YELLOW spinning icon) - watch for 5 seconds!');
        
        // Restore after 5 seconds
        setTimeout(() => {
          connectionStore.setConnected(true);
          console.log('Step 3: Restored to connected (GREEN icon)');
        }, 5000);
      }, 1000);
    },
      // Test 3.7: Step-by-step state demonstration (with socket control)
    testAllStates: () => {      console.log('🎨 Testing all connection states in sequence...');
      const socket = centralizedSocketManager.getSocket();
      
      // Step 1: Disconnected (Red) - disconnect both socket and store
      if (socket) {
        socket.disconnect();
      }
      connectionStore.setDisconnected('demo');
      console.log('1/3: DISCONNECTED (RED) - 4 seconds');
      
      setTimeout(() => {
        // Step 2: Reconnecting (Yellow) - keep socket disconnected but set reconnecting
        connectionStore.setReconnecting();
        console.log('2/3: RECONNECTING (YELLOW + SPINNING) - 4 seconds - LOOK NOW!');
        
        setTimeout(() => {
          // Step 3: Connected (Green) - reconnect socket and store
          if (socket) {
            socket.connect();
          }
          connectionStore.setConnected(true);
          console.log('3/3: CONNECTED (GREEN) - demo complete!');
        }, 4000);
      }, 4000);
    },
    
    // Test 3.8: Pure store test with health check disabled temporarily
    testYellowIconOnly: () => {
      console.log('🟡 Testing ONLY yellow icon (disabling health check temporarily)...');
        // Store original socket state
      const socket = centralizedSocketManager.getSocket();
      const originalConnected = socket?.connected;
      
      // Disconnect socket to prevent health check interference
      if (socket) {
        socket.disconnect();
        console.log('Disconnected socket to prevent health check interference');
      }
      
      // Set to disconnected first
      connectionStore.setDisconnected('yellow_test');
      console.log('Step 1: RED (disconnected) - 2 seconds');
      
      setTimeout(() => {
        // Set to reconnecting (this should show yellow)
        connectionStore.setReconnecting();
        console.log('Step 2: YELLOW + SPINNING (reconnecting) - showing for 6 seconds - WATCH THE ICON!');
        
        setTimeout(() => {
          // Restore everything
          if (socket && originalConnected) {
            socket.connect();
          }
          connectionStore.setConnected(true);
          console.log('Step 3: GREEN (connected) - test complete!');
        }, 6000);
      }, 2000);
    },
    
    // Test 4: Monitor connection changes
    watchConnection: () => {
      console.log('👁️ Watching connection changes...');
      const interval = setInterval(() => {
        console.log('Connection status:', {
          connected: connectionStore.isConnected,
          quality: connectionStore.connectionQuality,
          transport: connectionStore.transportType,
          browserOnline: navigator.onLine
        });
      }, 2000);
      
      // Stop watching after 30 seconds
      setTimeout(() => {
        clearInterval(interval);
        console.log('Stopped watching connection');
      }, 30000);
    },
    
    // Test 5: Current state
    getState: () => {
      return {
        isConnected: connectionStore.isConnected,
        quality: connectionStore.connectionQuality,
        transport: connectionStore.transportType,
        status: connectionStore.connectionStatus,
        reconnectAttempts: connectionStore.reconnectAttempts,
        browserOnline: navigator.onLine,
        socketConnected: centralizedSocketManager.getSocket()?.connected
      };
    },
    
    // Test 6: Force refresh connection state
    forceRefreshState: () => {      console.log('🔄 Force refreshing connection state...');
      const socket = centralizedSocketManager.getSocket();
      if (socket?.connected && !connectionStore.isConnected) {
        console.log('Socket connected but store disconnected - fixing...');
        connectionStore.setConnected(true);
      } else if (!socket?.connected && connectionStore.isConnected) {
        console.log('Socket disconnected but store connected - fixing...');
        connectionStore.setDisconnected('state_sync');
      }
      return {
        socketConnected: socket?.connected,
        storeConnected: connectionStore.isConnected,
        synced: socket?.connected === connectionStore.isConnected
      };
    }
  };
}

// Make it available globally for testing
declare global {
  interface Window {
    testConnection: ReturnType<typeof testConnectionMonitoring>;
  }
}

// Auto-attach to window in development
if (import.meta.env.DEV) {
  setTimeout(() => {
    window.testConnection = testConnectionMonitoring();
    console.log('🧪 Connection testing available: window.testConnection');
  }, 1000);
}
