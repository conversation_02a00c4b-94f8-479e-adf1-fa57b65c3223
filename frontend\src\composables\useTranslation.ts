import { computed, watch} from 'vue'
import { useI18n } from 'vue-i18n'
import { useLanguageStore } from '@/stores/language'
import type { MessageSchema } from '@/i18n'

export const useTranslation = () => {
  const { t: translate, locale } = useI18n<[MessageSchema]>()
  const languageStore = useLanguageStore()
  
  // Synchronize vue-i18n locale with language store
const syncLocale = (lang: string) => {
    // Only update the store, let the watcher handle locale sync
    languageStore.setLanguage(lang as any)
   }
  
  // Watch for language store changes and sync with vue-i18n
  watch(() => languageStore.currentLanguage, (newLang) => {
    if (locale.value !== newLang) {
      locale.value = newLang as any
    }
  })
    const t = (key: string, values?: Record<string, any>): string => {
    return translate(key, values || {})
  }
  
  return {
    t,
    currentLanguage: computed(() => languageStore.currentLanguage),
    isRTL: computed(() => languageStore.isRTL),
    setLanguage: (lang: string) => {
      syncLocale(lang)
    }
  }
}
