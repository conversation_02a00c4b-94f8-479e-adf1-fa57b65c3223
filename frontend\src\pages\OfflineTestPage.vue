<template>
  <div class="offline-test-page">
    <n-card title="Offline Bug Report Testing">
      <div class="space-y-4">
        <n-alert type="info">
          <p>This page is for testing the offline bug report functionality.</p>
          <p>Use the browser's developer tools to simulate network conditions:</p>
          <ul>
            <li>Open DevTools (F12)</li>
            <li>Go to Network tab</li>
            <li>Set throttling to "Offline" to simulate no connection</li>
            <li>Try submitting a bug report - it should be stored offline</li>
            <li>Set throttling back to "Online" - reports should auto-submit</li>
          </ul>
        </n-alert>

        <div class="test-controls">
          <n-space>
            <n-button @click="simulateOffline" type="warning">
              Simulate Offline (navigator.onLine = false)
            </n-button>
            <n-button @click="simulateOnline" type="success">
              Simulate Online (navigator.onLine = true)
            </n-button>
            <n-button @click="clearOfflineReports" type="error">
              Clear Offline Reports
            </n-button>
          </n-space>
        </div>

        <div class="status-info">
          <n-descriptions bordered :column="2">
            <n-descriptions-item label="Browser Online Status">
              <n-tag :type="isOnline ? 'success' : 'error'">
                {{ isOnline ? 'Online' : 'Offline' }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="Socket Connection">
              <n-tag :type="connectionStore.isConnected ? 'success' : 'error'">
                {{ connectionStore.isConnected ? 'Connected' : 'Disconnected' }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="Offline Reports Count">
              <n-tag type="info">
                {{ offlineReports.offlineReportCount.value }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="Processing Queue">
              <n-tag :type="offlineReports.isProcessingQueue.value ? 'warning' : 'default'">
                {{ offlineReports.isProcessingQueue.value ? 'Processing...' : 'Idle' }}
              </n-tag>
            </n-descriptions-item>
          </n-descriptions>
        </div>

        <div class="debug-report-section">
          <h3>Test Bug Report Submission</h3>
          <DebugReportButtonEnhanced />
        </div>

        <div class="offline-reports-list" v-if="offlineReports.hasOfflineReports.value">
          <h3>Offline Reports ({{ offlineReports.offlineReportCount.value }})</h3>
          <n-list>
            <n-list-item v-for="report in offlineReports.offlineReports.value" :key="report.id">
              <div class="report-item">
                <div class="report-header">
                  <strong>{{ report.reportPayload.reportDetails.title }}</strong>
                  <n-tag size="small">{{ report.reportPayload.reportDetails.type }}</n-tag>
                </div>
                <div class="report-meta">
                  <span>Created: {{ formatDate(report.timestamp) }}</span>
                  <span>Retries: {{ report.retryCount }}</span>
                  <span v-if="report.lastRetryAt">Last Retry: {{ formatDate(report.lastRetryAt) }}</span>
                </div>
              </div>
            </n-list-item>
          </n-list>
        </div>

        <div class="test-instructions">
          <n-card title="Test Instructions">
            <ol>
              <li><strong>Current State:</strong> Backend server is not running (expected for testing)</li>
              <li><strong>Test Offline Storage:</strong> Submit a bug report using the form above</li>
              <li><strong>Expected Result:</strong> Report should be automatically stored offline due to network error</li>
              <li><strong>Verify:</strong> Check that the report appears in "Offline Reports" list below</li>
              <li><strong>Optional:</strong> Use "Simulate Offline" to test browser offline detection</li>
              <li><strong>Note:</strong> Reports will auto-submit when backend becomes available</li>
            </ol>

            <n-alert type="info" style="margin-top: 16px;">
              <strong>Current Test Scenario:</strong> The backend server is not running, so all API calls return 500 errors from the Vite proxy.
              These should be detected as network errors and trigger offline storage automatically.
            </n-alert>
          </n-card>
        </div>

        <div class="debug-console">
          <n-card title="Debug Console">
            <n-space vertical>
              <n-button @click="testDirectly" type="primary">
                Test Network Error Detection Directly
              </n-button>
              <n-button @click="clearConsole" type="default">
                Clear Console
              </n-button>
            </n-space>
            <div class="console-output" ref="consoleOutput">
              <div v-for="(log, index) in consoleLogs" :key="index" :class="['console-line', log.type]">
                <span class="timestamp">{{ formatTime(log.timestamp) }}</span>
                <span class="message">{{ log.message }}</span>
              </div>
            </div>
          </n-card>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useConnectionStore } from '@/stores/connection'
import { useOfflineReports } from '@/composables/useOfflineReports'
import { useClientLogger } from '@/composables/useClientLogger'
import DebugReportButtonEnhanced from '@/components/DebugReportButtonEnhanced.vue'

const message = useMessage()
const connectionStore = useConnectionStore()
const offlineReports = useOfflineReports()
const logger = useClientLogger()

const isOnline = ref(navigator.onLine)
const consoleLogs = ref<Array<{timestamp: string, message: string, type: string}>>([])

const addLog = (message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') => {
  consoleLogs.value.push({
    timestamp: new Date().toISOString(),
    message,
    type
  })
}

// Update online status when it changes
const updateOnlineStatus = () => {
  isOnline.value = navigator.onLine
}

onMounted(() => {
  window.addEventListener('online', updateOnlineStatus)
  window.addEventListener('offline', updateOnlineStatus)
})

onUnmounted(() => {
  window.removeEventListener('online', updateOnlineStatus)
  window.removeEventListener('offline', updateOnlineStatus)
})

const simulateOffline = () => {
  // Override navigator.onLine
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: false
  })
  isOnline.value = false
  connectionStore.setDisconnected('simulated_offline')
  message.warning('Simulated offline mode - navigator.onLine set to false')
}

const simulateOnline = () => {
  // Restore navigator.onLine
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: true
  })
  isOnline.value = true
  connectionStore.setConnected(true)
  message.success('Simulated online mode - navigator.onLine set to true')
}

const clearOfflineReports = () => {
  offlineReports.clearOfflineReports()
  message.info('Offline reports cleared')
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString()
}

const testDirectly = async () => {
  addLog('🧪 Testing network error detection directly...', 'info')

  try {
    const testReport = {
      type: 'bug' as const,
      title: 'Direct Test Report',
      description: 'Testing network error detection directly',
      severity: 'medium' as const
    }

    addLog('📝 Sending test report to server...', 'info')
    const response = await logger.sendLogsToServer(testReport)

    addLog(`📨 Response received: ${JSON.stringify(response)}`, 'info')

    if (response.isNetworkError) {
      addLog('✅ Network error correctly detected!', 'success')
      addLog('💾 This should trigger offline storage in the component', 'info')
    } else if (response.success) {
      addLog('✅ Report sent successfully (backend is running)', 'success')
    } else {
      addLog(`❌ Server error (not network): ${response.message}`, 'error')
    }

    // Check offline reports after a short delay
    setTimeout(() => {
      addLog(`📊 Offline reports count: ${offlineReports.offlineReportCount.value}`, 'info')
    }, 1000)

  } catch (error) {
    addLog(`❌ Test failed: ${error}`, 'error')
  }
}

const clearConsole = () => {
  consoleLogs.value = []
}
</script>

<style scoped>
.offline-test-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.test-controls {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
}

.status-info {
  margin: 16px 0;
}

.debug-report-section {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.offline-reports-list {
  margin-top: 20px;
}

.report-item {
  width: 100%;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.report-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.test-instructions {
  margin-top: 20px;
}

.test-instructions ol {
  padding-left: 20px;
}

.test-instructions li {
  margin-bottom: 8px;
}

.debug-console {
  margin-top: 20px;
}

.console-output {
  max-height: 300px;
  overflow-y: auto;
  background: #1a1a1a;
  color: #ffffff;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin-top: 12px;
}

.console-line {
  margin-bottom: 4px;
  display: flex;
  gap: 8px;
}

.console-line.info {
  color: #ffffff;
}

.console-line.success {
  color: #4ade80;
}

.console-line.error {
  color: #f87171;
}

.console-line.warning {
  color: #fbbf24;
}

.console-line .timestamp {
  color: #9ca3af;
  min-width: 80px;
}

.console-line .message {
  flex: 1;
}
</style>
