// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql" // Changed to PostgreSQL for Docker
  url      = env("DATABASE_URL")
}

// Basic user model
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Email Verification Fields
  emailVerified     Boolean? @default(false)
  verificationToken String?  @unique // Store the verification token

  // Phone Verification Fields
  phoneNumber   String?   @unique
  phoneVerified Boolean?  @default(false)
  otpSecret     String? // Secret used to generate time-based OTPs
  otpTimestamp  DateTime? // Timestamp when the OTP was generated/sent

  // Reputation Fields
  username        String? // Optional username, defaults to email-based if null
  reputationScore Int     @default(0) // Raw numerical score
  reputationLevel Int     @default(1) // Level 1-5 based on score ranges

  // Relations
  offers    Offer[] // Relation to offers created by the user
  interests Interest[] @relation("InterestedUsers")

  // ChatSession relations
  chatSessionsAsCreator            ChatSession[]  @relation("ChatSessionCreator")
  chatSessionsAsInterested         ChatSession[]  @relation("ChatSessionInterested")
  notifications                    Notification[] // Relation to notifications for the user
  sentChatMessages                 ChatMessage[]  @relation("SentMessages") // This is the other side
  transactionsAsCurrencyAProvider  Transaction[]  @relation("CurrencyAProvider")
  transactionsAsCurrencyBProvider  Transaction[]  @relation("CurrencyBProvider")
  transactionsWithAgreedFirstPayer Transaction[]  @relation("AgreedFirstPayer")
  transactionsCancelledBy          Transaction[]  @relation("CancelledByUser") // Back-relation for cancelled transactions
  transactionsDisputedBy           Transaction[]  @relation("DisputedByUser") // Back-relation for disputed transactions

  // New relations
  partyANegotiations   PayerNegotiation[]     @relation("PartyANegotiations")
  partyBNegotiations   PayerNegotiation[]     @relation("PartyBNegotiations")
  paymentReceivingInfo PaymentReceivingInfo[] @relation("UserPaymentReceivingInfo") // Corrected: Added relation name
}

// Enums for Offer Type and Status
enum OfferType {
  BUY
  SELL
}

enum OfferStatus {
  ACTIVE
  INACTIVE
  DEACTIVATED // Assuming this was a typo and should be distinct from INACTIVE or a specific state
  COMPLETED
  CANCELLED
}

// Enum for Transaction Status
enum TransactionStatus {
  AWAITING_FIRST_PAYER_DESIGNATION // Initial status: Parties need to designate who pays first.
  AWAITING_FIRST_PAYER_PAYMENT
  AWAITING_SECOND_PAYER_CONFIRMATION
  AWAITING_SECOND_PAYER_PAYMENT
  AWAITING_FIRST_PAYER_CONFIRMATION
  COMPLETED
  CANCELLED
  DISPUTED
}

// Enum for Receiving Info Status
enum ReceivingInfoStatus {
  PENDING_INPUT
  PROVIDED
  CONFIRMED_FROM_PROFILE
  // Add other statuses if needed, e.g., REJECTED
}

// Enum for Negotiation Status
enum NegotiationStatus {
  PENDING_RECEIVING_INFO          // Initial state, awaiting one or both parties' payment info.
  AWAITING_PARTY_A_RECEIVING_INFO // Party B provided, Party A pending.
  AWAITING_PARTY_B_RECEIVING_INFO // Party A provided, Party B pending.
  READY_TO_NEGOTIATE              // Both parties provided info. System recommendation is generated at this point.
                                  // This status is transient and should immediately move to PENDING_RESPONSE.
  PENDING_RESPONSE                // A proposal (system or user) is active, awaiting response.
  FINALIZED                       // Agreement reached on the first payer.
  EXPIRED                         // Negotiation timed out.
  CANCELLED                       // Negotiation was cancelled by a user or system.
}

model Offer {
  id                     String      @id @default(cuid())
  userId                 String
  user                   User        @relation(fields: [userId], references: [id])
  type                   OfferType
  currencyPair           String      @default("CAD-IRR")
  amount                 Float
  baseRate               Float
  adjustmentForLowerRep  Float
  adjustmentForHigherRep Float
  status                 OfferStatus @default(ACTIVE)
  createdAt              DateTime    @default(now())
  updatedAt              DateTime    @updatedAt
  interests              Interest[]

  // ChatSession relation
  chatSessions ChatSession[]
  transaction  Transaction? // Back-relation for the transaction linked to this offer
}

// Enum for Interest Status
enum InterestStatus {
  PENDING
  ACCEPTED
  DECLINED
}

model Interest {
  id                String         @id @default(cuid())
  offerId           String
  offer             Offer          @relation(fields: [offerId], references: [id], onDelete: Cascade)
  interestedUserId  String
  interestedUser    User           @relation("InterestedUsers", fields: [interestedUserId], references: [id], onDelete: Cascade)
  status            InterestStatus @default(PENDING)
  declineReasonCode String?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  chatSession       ChatSession?   @relation("ChatToInterest") // Back relation for one-to-one

  @@unique([offerId, interestedUserId]) // Added composite unique constraint
  @@index([offerId])
  @@index([interestedUserId])
}

model ChatSession {
  id        String   @id @default(cuid())
  offerId   String
  offer     Offer    @relation(fields: [offerId], references: [id], onDelete: Cascade)
  userOneId String // Typically the offer creator
  userOne   User     @relation("ChatSessionCreator", fields: [userOneId], references: [id], onDelete: Cascade)
  userTwoId String // Typically the user who showed interest
  userTwo   User     @relation("ChatSessionInterested", fields: [userTwoId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  interestId  String?       @unique
  interest    Interest?     @relation("ChatToInterest", fields: [interestId], references: [id], onDelete: SetNull)
  messages    ChatMessage[] // Relation to chat messages
  transaction Transaction? // Relation to the transaction associated with this chat session

  @@index([userOneId])
  @@index([userTwoId])
  @@index([offerId])
}

model ChatMessage {
  id              String      @id @default(cuid())
  chatSessionId   String
  chatSession     ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)
  senderId        String?     // Optional: null for system messages
  sender          User?       @relation("SentMessages", fields: [senderId], references: [id], onDelete: SetNull)
  content         String
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  isSystemMessage Boolean     @default(false) // This field is crucial
  transactionId   String?     // Link to transaction for context if needed

  @@index([chatSessionId])
  @@index([senderId])
  @@index([transactionId])
}

// Enum for Notification Types
enum NotificationType {
  NEW_INTEREST_ON_YOUR_OFFER
  YOUR_INTEREST_ACCEPTED
  YOUR_INTEREST_DECLINED
  CHAT_MESSAGE_RECEIVED // Example for future use
  OFFER_STATUS_UPDATED_BY_OWNER // e.g. offer creator cancelled their own offer
  OFFER_STATUS_CHANGED // Added for general offer status changes by owner
  TRANSACTION_STARTED
  TRANSACTION_ACTION_REQUIRED
  TRANSACTION_UPDATE
  TRANSACTION_PAYMENT_DECLARED // Added
  TRANSACTION_PAYMENT_CONFIRMED // Added
  TRANSACTION_COMPLETED
  TRANSACTION_CANCELLED
  TRANSACTION_DISPUTED
  TRANSACTION_AUTO_CANCELLED_TIMER // Added
  // Add other types as needed
}

// Model for storing Notifications
model Notification {
  id        String           @id @default(uuid())
  userId    String // Recipient of the notification
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  type      NotificationType
  message   String // Display message for the notification
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Optional fields to link to related entities
  relatedEntityType String? // e.g., "OFFER", "INTEREST", "CHAT_SESSION"
  relatedEntityId   String? // ID of the entity the notification refers to

  actorId       String? // ID of the user who triggered the event (if applicable)
  // actor              User?            @relation("NotificationActor", fields: [actorId], references: [id], onDelete:SetNull) // Optional: if you need to link to actor User model
  actorUsername String? // Username of the actor for quick display

  data Json? // For storing additional context, e.g., offer title, decline reason details

  @@index([userId])
  @@index([userId, isRead, createdAt]) // To quickly fetch and sort unread notifications for a user
}

model Transaction {
  id                  String            @id @default(cuid())
  offerId             String?           @unique // Link to the original offer if applicable, made unique for 1-to-1
  chatSessionId       String            @unique // Each transaction must belong to a chat session
  currencyA           String
  amountA             Float
  currencyAProviderId String // User ID of the provider for currency A
  currencyB           String
  amountB             Float
  currencyBProviderId String // User ID of the provider for currency B
  status              TransactionStatus @default(AWAITING_FIRST_PAYER_DESIGNATION) // Default to designation phase
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt

  // Timestamps for terms agreement (consider if these are still needed in the same way)
  termsAgreementTimestampPayer1 DateTime? // Timestamp when provider of currency A agreed
  termsAgreementTimestampPayer2 DateTime? // Timestamp when provider of currency B agreed

  // First payer designation
  agreedFirstPayerId             String? // User ID of the party agreed to pay first
  firstPayerDesignationTimestamp DateTime? // Timestamp when the first payer was agreed upon

  // Payment tracking for the first payer (who is `agreedFirstPayerId`)
  paymentExpectedByPayer1     DateTime? // Deadline for the first payment
  paymentDeclaredAtPayer1     DateTime? // Timestamp when the first payer declared payment
  paymentTrackingNumberPayer1 String? // Optional tracking number for the first payment

  // Confirmation of the first payment (by the other party)
  firstPaymentConfirmedByPayer2At DateTime? // Timestamp when the other party confirmed receipt of the first payment

  // Payment tracking for the second payer (who is NOT `agreedFirstPayerId`)
  paymentExpectedByPayer2     DateTime? // Deadline for the second payment
  paymentDeclaredAtPayer2     DateTime? // Timestamp when the second payer declared payment
  paymentTrackingNumberPayer2 String? // Optional tracking number for the second payment

  // Confirmation of the second payment (by the `agreedFirstPayerId`)
  secondPaymentConfirmedByPayer1At DateTime? // Timestamp when the first payer confirmed receipt of the second payment

  // Cancellation and Dispute
  cancellationReason     String?
  cancelledByUserId      String? // User ID of the party who initiated cancellation
  disputeReason          String?
  disputedByUserId       String? // User ID of the party who initiated dispute
  disputeResolvedAt      DateTime?
  disputeResolutionNotes String?

  // Relations
  offer             Offer?            @relation(fields: [offerId], references: [id])
  chatSession       ChatSession       @relation(fields: [chatSessionId], references: [id])
  currencyAProvider User              @relation("CurrencyAProvider", fields: [currencyAProviderId], references: [id])
  currencyBProvider User              @relation("CurrencyBProvider", fields: [currencyBProviderId], references: [id])
  agreedFirstPayer  User?             @relation("AgreedFirstPayer", fields: [agreedFirstPayerId], references: [id], onDelete: SetNull)
  cancelledByUser   User?             @relation("CancelledByUser", fields: [cancelledByUserId], references: [id], onDelete: SetNull)
  disputedByUser    User?             @relation("DisputedByUser", fields: [disputedByUserId], references: [id], onDelete: SetNull)
  payerNegotiation  PayerNegotiation?

  @@index([currencyAProviderId])
  @@index([currencyBProviderId])
  @@index([agreedFirstPayerId])
  @@index([status])
  @@index([cancelledByUserId])
  @@index([disputedByUserId])
}

model PayerNegotiation {
  negotiationId    String   @id @default(cuid())
  transactionId    String   @unique // Each transaction has one negotiation
  transaction      Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  partyA_Id        String
  partyA           User     @relation("PartyANegotiations", fields: [partyA_Id], references: [id], onDelete: Cascade)
  partyB_Id        String
  partyB           User     @relation("PartyBNegotiations", fields: [partyB_Id], references: [id], onDelete: Cascade)

  partyA_receivingInfoStatus ReceivingInfoStatus @default(PENDING_INPUT)
  partyB_receivingInfoStatus ReceivingInfoStatus @default(PENDING_INPUT)

  // Store the ID of the PaymentReceivingInfo used by each party for this negotiation
  partyA_PaymentReceivingInfoId String?
  partyA_PaymentReceivingInfo   PaymentReceivingInfo? @relation("PartyAReceivingInfo", fields: [partyA_PaymentReceivingInfoId], references: [id], onDelete: SetNull)
  partyB_PaymentReceivingInfoId String?
  partyB_PaymentReceivingInfo   PaymentReceivingInfo? @relation("PartyBReceivingInfo", fields: [partyB_PaymentReceivingInfoId], references: [id], onDelete: SetNull)

  systemRecommendedPayerId    String?
  systemRecommendationRule    String? // e.g., "REPUTATION", "CURRENCY"
  systemRecommendationReason  String? // Textual reason
  systemRecommendationDetails Json?   // Store specific details like reputation scores

  currentProposal_PayerId String? // Who is proposed to pay first
  currentProposal_ById    String? // Who made the current proposal ('system' or a userId)
  currentProposal_Message String? // Optional message with the proposal

  partyA_agreedToCurrentProposal Boolean @default(false)
  partyB_agreedToCurrentProposal Boolean @default(false)

  negotiationStatus NegotiationStatus @default(PENDING_RECEIVING_INFO) // This default will now be valid
  finalizedPayerId  String?
  paymentTimerDueDate DateTime? // Timer for the finalized payer to make their payment

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([partyA_Id])
  @@index([partyB_Id])
  @@index([transactionId])
}

// Define the PaymentReceivingInfo model
model PaymentReceivingInfo {
  id                String   @id @default(cuid())
  userId            String
  user              User     @relation("UserPaymentReceivingInfo", fields: [userId], references: [id])
  bankName          String
  accountNumber     String // Consider encrypting this field
  accountHolderName String
  isDefaultForUser  Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Add these back-relations
  negotiationsAsPartyA PayerNegotiation[] @relation("PartyAReceivingInfo")
  negotiationsAsPartyB PayerNegotiation[] @relation("PartyBReceivingInfo")

  @@index([userId])
}
