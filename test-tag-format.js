// Test script to verify tag format fixes
console.log('Testing tag format fixes...');

// Simulate the debug tag display functionality
const mockTags = [
  { origin: 'PREDEFINED', tag: 'error' },
  { origin: 'PREDEFINED', tag: 'fix-needed' },
  { origin: 'PREDEFINED', tag: 'urgent' },
  { origin: 'AI_SUGGESTED', tag: 'authentication' },
  { origin: 'AI_SUGGESTED', tag: 'login-flow' }
];

console.log('Original tags (old format):', mockTags);

// Test the transformation logic from aiAnalysisStore.ts
const transformedTags = mockTags.map(tag => {
  console.log('[Test] Original tag:', tag);
  const transformedTag = {
    ...(tag.origin === 'PREDEFINED' ? { tagId: tag.tag } : { tagName: tag.tag }),
    origin: tag.origin
  };
  console.log('[Test] Transformed tag:', transformedTag);
  return transformedTag;
});

console.log('Transformed tags (new format):', transformedTags);

// Test the getTagDisplayName logic
function getTagDisplayName(tag) {
  if (tag.tagId) {
    // For predefined tags, look up display name
    const predefinedTags = {
      'error': 'Error',
      'fix-needed': 'Fix Needed',
      'urgent': 'Urgent'
    };
    return predefinedTags[tag.tagId] || tag.tagId;
  } else if (tag.tagName) {
    return tag.tagName;
  } else if (tag.tag) {
    console.warn('[Test] Tag with no display name:', tag);
    return tag.tag;
  }
  return 'Unknown Tag';
}

console.log('Display names:');
transformedTags.forEach(tag => {
  console.log(`${tag.origin}: ${getTagDisplayName(tag)}`);
});
