// filepath: c:\Code\MUNygo\frontend\src\stores\__tests__\auth.spec.ts
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useAuthStore } from '../auth'; // Adjust path as needed

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => { store[key] = value.toString(); }),
    removeItem: vi.fn((key: string) => { delete store[key]; }),
    clear: vi.fn(() => { store = {}; }),
  };
})();
Object.defineProperty(window, 'localStorage', { value: localStorageMock });


describe('Auth Store', () => {
  beforeEach(() => {
    // Create a fresh Pinia instance and make it active before each test
    setActivePinia(createPinia());
    // Clear mock localStorage before each test
    localStorageMock.clear();
    // Reset mock function call counts
    vi.clearAllMocks();
  });

  it('initializes with default values', () => {
    const authStore = useAuthStore();
    expect(authStore.token).toBeNull();
    expect(authStore.user).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);
  });

  it('login function updates state and localStorage', () => {
    const authStore = useAuthStore();
    const testToken = 'test-jwt-token';
    const testUser = { id: '123', email: '<EMAIL>' };

    authStore.login(testToken, testUser);

    expect(authStore.token).toBe(testToken);
    expect(authStore.user).toEqual(testUser);
    expect(authStore.isAuthenticated).toBe(true);
    expect(localStorageMock.setItem).toHaveBeenCalledWith('authToken', testToken);
    expect(localStorageMock.setItem).toHaveBeenCalledWith('userInfo', JSON.stringify(testUser));
  });  it('logout function clears state and localStorage', () => {
    const authStore = useAuthStore();
    // Simulate logged-in state first
    const testToken = 'test-jwt-token';
    const testUser = { id: '123', email: '<EMAIL>' };
    authStore.login(testToken, testUser);

    // Now logout
    authStore.logout();

    expect(authStore.token).toBeNull();
    expect(authStore.user).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('authToken');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('userInfo');
  });

  it('initializeAuth loads state from localStorage', () => {
     const testToken = 'stored-token';
     const testUser = { id: '456', email: '<EMAIL>' };
     localStorageMock.setItem('authToken', testToken);
     localStorageMock.setItem('userInfo', JSON.stringify(testUser));

     const authStore = useAuthStore();
     // Initialize *after* setting localStorage items and creating the store instance
     authStore.initializeAuth();

     expect(authStore.token).toBe(testToken);
     expect(authStore.user).toEqual(testUser);
     expect(authStore.isAuthenticated).toBe(true);
  });

   it('initializeAuth handles missing localStorage items', () => {
     const authStore = useAuthStore();
     authStore.initializeAuth(); // Call initialize with empty localStorage

     expect(authStore.token).toBeNull();
     expect(authStore.user).toBeNull();
     expect(authStore.isAuthenticated).toBe(false);
  });

  // Add more tests: e.g., initializeAuth with invalid JSON in localStorage
});