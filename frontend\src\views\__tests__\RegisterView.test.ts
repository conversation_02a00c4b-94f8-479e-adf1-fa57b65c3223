import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import { nextTick } from 'vue';
import RegisterView from '../RegisterView.vue';

// Mock API client
vi.mock('../../services/apiClient', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn()
  }
}));

// Mock axios
vi.mock('axios', () => ({
  default: {
    post: vi.fn(),
    isAxiosError: vi.fn()
  },
  isAxiosError: vi.fn()
}));

// Mock router
const mockRouterPush = vi.fn();
const mockRouterReplace = vi.fn();

vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockRouterPush,
    replace: mockRouterReplace
  }),
  useRoute: () => ({
    query: {}
  })
}));

// Mock i18n and related modules
vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn().mockImplementation((key: string, values?: any) => {
      const translations: Record<string, string> = {
        'auth.email': 'Email',
        'auth.username': 'Username',
        'auth.password': 'Password',
        'auth.signUp': 'Sign Up',
        'auth.createAccount': 'Create Account',
        'auth.enterEmail': 'Enter your email',
        'auth.enterUsername': 'Enter your username',
        'auth.enterPasswordMinChars': 'Enter your password (min 8 characters)',
        'auth.emailAvailable': 'Email is available',
        'auth.emailTaken': 'Email is already registered',
        'auth.emailCheckError': 'Unable to check email availability',
        'auth.usernameAvailable': 'Username is available',
        'auth.usernameTaken': 'Username is already taken',
        'auth.usernameCheckError': 'Unable to check username availability',
        'auth.usernameSuggestions': 'Suggestions',
        'auth.agreeToTerms': 'I agree to the',
        'auth.termsOfService': 'Terms of Service',
        'auth.and': 'and',
        'auth.privacyPolicy': 'Privacy Policy',
        'auth.joinCommunity': 'Join our community',
        'auth.alreadyHaveAccount': 'Already have an account?',
        'auth.loginHere': 'Login here',
        'validation.emailRequired': 'Email is required',
        'validation.emailInvalid': 'Invalid email format',
        'validation.passwordRequired': 'Password is required',
        'validation.passwordInvalid': `Password must be at least ${values?.min || 8} characters`,
        'validation.usernameMinLength': `Username must be at least ${values?.min || 3} characters`,
        'validation.usernameMaxLength': `Username cannot exceed ${values?.max || 20} characters`,
        'validation.usernameInvalidChars': 'Username can only contain letters, numbers, underscores, and hyphens',
        'validation.usernameInvalidFormat': 'Username cannot start or end with underscore or hyphen',
        'validation.terms': 'You must accept the terms',
        'errors.registration': 'Registration Error',
        'app.success': 'Success',
        'landing.heroTitle': 'MUNygo'
      };
      return translations[key] || key;
    }),
    locale: { value: 'en' }
  }),
  createI18n: vi.fn()
}));

// Mock translation composable
vi.mock('../../composables/useTranslation', () => ({
  useTranslation: () => ({
    t: vi.fn().mockImplementation((key: string, values?: any) => {
      const translations: Record<string, string> = {
        'auth.email': 'Email',
        'auth.username': 'Username',
        'auth.password': 'Password',
        'auth.signUp': 'Sign Up',
        'auth.createAccount': 'Create Account',
        'auth.enterEmail': 'Enter your email',
        'auth.enterUsername': 'Enter your username',
        'auth.enterPasswordMinChars': 'Enter your password (min 8 characters)',
        'auth.emailAvailable': 'Email is available',
        'auth.emailTaken': 'Email is already registered',
        'auth.emailCheckError': 'Unable to check email availability',
        'auth.usernameAvailable': 'Username is available',
        'auth.usernameTaken': 'Username is already taken',
        'auth.usernameCheckError': 'Unable to check username availability',
        'auth.usernameSuggestions': 'Suggestions',
        'auth.agreeToTerms': 'I agree to the',
        'auth.termsOfService': 'Terms of Service',
        'auth.and': 'and',
        'auth.privacyPolicy': 'Privacy Policy',
        'auth.joinCommunity': 'Join our community',
        'auth.alreadyHaveAccount': 'Already have an account?',
        'auth.loginHere': 'Login here',
        'validation.emailRequired': 'Email is required',
        'validation.emailInvalid': 'Invalid email format',
        'validation.passwordRequired': 'Password is required',
        'validation.passwordInvalid': `Password must be at least ${values?.min || 8} characters`,
        'validation.usernameMinLength': `Username must be at least ${values?.min || 3} characters`,
        'validation.usernameMaxLength': `Username cannot exceed ${values?.max || 20} characters`,
        'validation.usernameInvalidChars': 'Username can only contain letters, numbers, underscores, and hyphens',
        'validation.usernameInvalidFormat': 'Username cannot start or end with underscore or hyphen',
        'validation.terms': 'You must accept the terms',
        'errors.registration': 'Registration Error',
        'app.success': 'Success',
        'landing.heroTitle': 'MUNygo'
      };
      return translations[key] || key;
    }),
    currentLanguage: { value: 'en' },
    isRTL: { value: false },
    setLanguage: vi.fn()
  })
}));

// Mock store modules
vi.mock('../../stores/theme', () => ({
  useThemeStore: () => ({
    isDark: { value: false }
  })
}));

vi.mock('../../stores/language', () => ({
  useLanguageStore: () => ({
    currentLanguage: 'en',
    isRTL: false,
    setLanguage: vi.fn()
  })
}));

// Mock other components
vi.mock('../../components/ThemeToggle.vue', () => ({
  default: { template: '<div class="theme-toggle"></div>' }
}));

vi.mock('../../components/LanguageSelector.vue', () => ({
  default: { template: '<div class="language-selector"></div>' }
}));

// Mock Naive UI components and composables
vi.mock('naive-ui', () => ({
  useMessage: vi.fn(() => ({
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  })),
  NForm: {
    template: '<form @submit="$emit(\'submit\', $event)"><slot /></form>',
    methods: {
      validate: vi.fn().mockResolvedValue(true)
    }
  },
  NFormItem: {
    template: '<div class="n-form-item"><label v-if="label">{{ label }}</label><slot /></div>',
    props: ['path', 'label', 'showFeedback', 'showLabel']
  },
  NInput: {
    template: `
      <div class="n-input" :class="{ 'n-input--loading': loading, 'n-input--error': status === 'error', 'n-input--success': status === 'success' }">
        <slot name="prefix" />
        <input 
          :value="value" 
          @input="$emit('update:value', $event.target.value); $emit('input', $event.target.value)"
          @blur="$emit('blur')"
          :placeholder="placeholder"
          :type="type"
        />
        <slot name="suffix" />
      </div>
    `,
    props: ['value', 'placeholder', 'size', 'loading', 'status', 'type', 'showPasswordOn', 'inputProps'],
    emits: ['update:value', 'input', 'blur']
  },
  NButton: {
    template: '<button :disabled="disabled || loading" :type="attrType || \'button\'" class="n-button"><slot /></button>',
    props: ['type', 'loading', 'disabled', 'block', 'size', 'attrType']
  },
  NCheckbox: {
    template: '<label class="n-checkbox"><input type="checkbox" :checked="checked" @change="$emit(\'update:checked\', $event.target.checked)" /><slot /></label>',
    props: ['checked'],
    emits: ['update:checked']
  },
  NIcon: {
    template: '<span class="n-icon"><slot /></span>',
    props: ['color']
  },
  NTag: {
    template: '<span class="n-tag" @click="$emit(\'click\')" :class="type"><slot /></span>',
    props: ['type', 'size'],
    emits: ['click']
  },
  NText: {
    template: '<span class="n-text"><slot /></span>',
    props: ['depth']
  },  NAlert: {
    template: '<div class="n-alert" :class="type"><div v-if="title" class="n-alert__title">{{ title }}</div><slot /></div>',
    props: ['type', 'title', 'closable'],
    emits: ['close']
  }
}));

describe('RegisterView', () => {
  let wrapper: any;
  let pinia: any;
  let mockApiClient: any;

  beforeEach(async () => {
    pinia = createPinia();
    setActivePinia(pinia);
    
    // Get the mocked API client
    mockApiClient = (await import('../../services/apiClient')).default;
    
    // Reset mocks
    vi.clearAllMocks();
    mockRouterPush.mockClear();
    mockRouterReplace.mockClear();
    
    // Mock successful API responses by default
    mockApiClient.get.mockResolvedValue({ data: { available: true } });
    mockApiClient.post.mockResolvedValue({ 
      data: { 
        message: 'Registration successful! Please check your email.',
        token: 'test-token', 
        user: { id: 1, email: '<EMAIL>' }
      } 
    });
  });

  afterEach(() => {
    wrapper?.unmount();
  });

  const createWrapper = (props = {}) => {
    wrapper = mount(RegisterView, {
      global: {
        plugins: [pinia],
        stubs: {
          'router-link': {
            template: '<a :href="to"><slot /></a>',
            props: ['to']
          },
          Transition: {
            template: '<div><slot /></div>'
          }
        }
      },
      props
    });
    return wrapper;
  };

  describe('Component Rendering', () => {
    it('should render all form fields', () => {
      createWrapper();
      
      expect(wrapper.find('input[placeholder="Enter your email"]').exists()).toBe(true);
      expect(wrapper.find('input[placeholder="Enter your username"]').exists()).toBe(true);
      expect(wrapper.find('input[placeholder="Enter your password (min 8 characters)"]').exists()).toBe(true);
      expect(wrapper.find('.n-checkbox').exists()).toBe(true);
      expect(wrapper.find('button[type="submit"]').exists()).toBe(true);
    });

    it('should have proper form labels', () => {
      createWrapper();
      
      const formItems = wrapper.findAll('.n-form-item');
      expect(formItems.some((item: any) => item.text().includes('Email'))).toBe(true);
      expect(formItems.some((item: any) => item.text().includes('Username'))).toBe(true);
      expect(formItems.some((item: any) => item.text().includes('Password'))).toBe(true);
    });

    it('should disable submit button initially', () => {
      createWrapper();
      
      const submitButton = wrapper.find('button[type="submit"]');
      expect(submitButton.attributes('disabled')).toBeDefined();
    });
  });
  describe('Email Availability Checking', () => {
    it('should check email availability after valid email input', async () => {
      createWrapper();
      
      const emailInput = wrapper.find('input[placeholder="Enter your email"]');
      await emailInput.setValue('<EMAIL>');
      await emailInput.trigger('blur');
      
      // Wait for debounce
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/auth/email/check/test%40example.com');
    });

    it('should show success message when email is available', async () => {
      mockApiClient.get.mockResolvedValue({
        data: { available: true, message: 'Email is available' }
      });
      
      createWrapper();
      
      const emailInput = wrapper.find('input[placeholder="Enter your email"]');
      await emailInput.setValue('<EMAIL>');
      await emailInput.trigger('blur');
      
      // Wait for API call and DOM update
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      await nextTick();
      
      expect(wrapper.text()).toContain('Email is available');
      expect(wrapper.find('.feedback-message.success').exists()).toBe(true);
    });

    it('should show error message when email is taken', async () => {
      mockApiClient.get.mockResolvedValue({
        data: { available: false, message: 'Email is already registered' }
      });
      
      createWrapper();
      
      const emailInput = wrapper.find('input[placeholder="Enter your email"]');
      await emailInput.setValue('<EMAIL>');
      await emailInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      await nextTick();
      
      expect(wrapper.text()).toContain('Email is already registered');
      expect(wrapper.find('.feedback-message.error').exists()).toBe(true);
    });

    it('should handle email check API error gracefully', async () => {
      mockApiClient.get.mockRejectedValue({
        response: { data: { error: 'Server error' } }
      });
      
      createWrapper();
      
      const emailInput = wrapper.find('input[placeholder="Enter your email"]');
      await emailInput.setValue('<EMAIL>');
      await emailInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      await nextTick();
      
      expect(wrapper.text()).toContain('Server error');
      expect(wrapper.find('.feedback-message.error').exists()).toBe(true);
    });

    it('should not check availability for invalid email format', async () => {
      createWrapper();
      
      const emailInput = wrapper.find('input[placeholder="Enter your email"]');
      await emailInput.setValue('invalid-email');
      await emailInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      
      expect(mockApiClient.get).not.toHaveBeenCalled();
    });
  });
  describe('Username Availability Checking', () => {
    it('should check username availability after valid input', async () => {
      createWrapper();
      
      const usernameInput = wrapper.find('input[placeholder="Enter your username"]');
      await usernameInput.setValue('testuser');
      await usernameInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/auth/username/check/testuser');
    });

    it('should show success message when username is available', async () => {
      mockApiClient.get.mockResolvedValue({
        data: { available: true, username: 'testuser' }
      });
      
      createWrapper();
      
      const usernameInput = wrapper.find('input[placeholder="Enter your username"]');
      await usernameInput.setValue('testuser');
      await usernameInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      await nextTick();
      
      expect(wrapper.text()).toContain('Username is available');
      expect(wrapper.find('.feedback-message.success').exists()).toBe(true);
    });

    it('should show error message and suggestions when username is taken', async () => {
      mockApiClient.get.mockResolvedValue({
        data: { 
          available: false, 
          message: 'Username is already taken',
          suggestions: ['testuser1', 'testuser2', 'testuser3']
        }
      });
      
      createWrapper();
      
      const usernameInput = wrapper.find('input[placeholder="Enter your username"]');
      await usernameInput.setValue('testuser');
      await usernameInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();      await nextTick();
      
      expect(wrapper.text()).toContain('Username is already taken');
      expect(wrapper.text()).toContain('Suggestions');
      expect(wrapper.text()).toContain('testuser1');
      expect(wrapper.findAll('.n-tag').length).toBeGreaterThan(0);
    });    it('should display username suggestions when username is taken', async () => {
      mockApiClient.get.mockResolvedValueOnce({
        data: { 
          available: false, 
          suggestions: ['testuser1', 'testuser2', 'testuser3']
        }
      });
      
      createWrapper();
      
      const usernameInput = wrapper.find('input[placeholder="Enter your username"]');
      await usernameInput.setValue('testuser');
      await usernameInput.trigger('blur');
      
      // Wait for debounce and API call
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      await nextTick();
      
      // Verify API was called
      expect(mockApiClient.get).toHaveBeenCalledWith('/auth/username/check/testuser');
      
      // Force another nextTick to ensure reactive updates complete
      await nextTick();
      
      // Check that suggestions container exists and log the DOM for debugging
      console.log('Wrapper HTML:', wrapper.html());
      const suggestionsContainer = wrapper.find('.username-suggestions');
      expect(suggestionsContainer.exists()).toBe(true);
      
      // Check that suggestions are rendered as tags
      const suggestionTags = wrapper.findAll('.n-tag');
      expect(suggestionTags.length).toBeGreaterThan(0);
    });

    it('should not check username availability for short usernames', async () => {
      createWrapper();
      
      const usernameInput = wrapper.find('input[placeholder="Enter your username"]');
      await usernameInput.setValue('ab');
      await usernameInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      
      expect(mockApiClient.get).not.toHaveBeenCalled();
    });
  });  describe('Form Submission', () => {
    it('should submit form with valid data', async () => {
      const mockAxios = await import('axios');
      mockAxios.default.post.mockResolvedValue({
        data: { 
          message: 'Registration successful! Please check your email.',
          token: 'test-token', 
          user: { id: 1, email: '<EMAIL>', username: 'testuser' }
        }
      });
      
      createWrapper();
      
      // Fill form
      await wrapper.find('input[placeholder="Enter your email"]').setValue('<EMAIL>');
      await wrapper.find('input[placeholder="Enter your username"]').setValue('testuser');
      await wrapper.find('input[placeholder="Enter your password (min 8 characters)"]').setValue('password123');
      
      // Check the terms checkbox
      const checkbox = wrapper.find('.n-checkbox input[type="checkbox"]');
      await checkbox.setValue(true);
      
      // Submit form
      await wrapper.find('form').trigger('submit');
      await flushPromises();
      
      expect(mockAxios.default.post).toHaveBeenCalledWith('/api/auth/register', {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'password123'
      });
    });

    it('should handle registration success and redirect', async () => {
      const mockAxios = await import('axios');
      
      mockAxios.default.post.mockResolvedValue({
        data: { 
          message: 'Registration successful! Please check your email.',
          token: 'test-token', 
          user: { id: 1, email: '<EMAIL>' }
        }
      });
      
      createWrapper();
      
      // Fill and submit form
      await wrapper.find('input[placeholder="Enter your email"]').setValue('<EMAIL>');
      await wrapper.find('input[placeholder="Enter your password (min 8 characters)"]').setValue('password123');
      
      const checkbox = wrapper.find('.n-checkbox input[type="checkbox"]');
      await checkbox.setValue(true);
      
      await wrapper.find('form').trigger('submit');
      await flushPromises();
      
      // Wait for the redirect timeout
      await new Promise(resolve => setTimeout(resolve, 1600));
      
      expect(mockRouterPush).toHaveBeenCalledWith('/login');
    });    it('should handle registration error', async () => {
      const mockAxios = await import('axios');
      
      const errorResponse = {
        response: { 
          status: 409,
          data: { message: 'Email already in use' }
        }
      };
      
      mockAxios.default.post.mockRejectedValue(errorResponse);
      mockAxios.isAxiosError.mockReturnValue(true);
      
      createWrapper();
      
      // Fill and submit form
      await wrapper.find('input[placeholder="Enter your email"]').setValue('<EMAIL>');
      await wrapper.find('input[placeholder="Enter your password (min 8 characters)"]').setValue('password123');
      
      const checkbox = wrapper.find('.n-checkbox input[type="checkbox"]');
      await checkbox.setValue(true);
      
      await wrapper.find('form').trigger('submit');
      await flushPromises();
      await nextTick();
      
      // Check that the error alert is displayed
      const errorAlert = wrapper.find('.n-alert.error');
      expect(errorAlert.exists()).toBe(true);
      expect(errorAlert.text()).toContain('Email already in use');
    });

    it('should not submit without accepting terms', async () => {
      createWrapper();
      
      await wrapper.find('input[placeholder="Enter your email"]').setValue('<EMAIL>');
      await wrapper.find('input[placeholder="Enter your password (min 8 characters)"]').setValue('password123');
      // Don't check the terms checkbox
      
      const submitButton = wrapper.find('button[type="submit"]');
      expect(submitButton.attributes('disabled')).toBeDefined();
    });

    it('should enable submit button when terms are accepted', async () => {
      createWrapper();
      
      await wrapper.find('input[placeholder="Enter your email"]').setValue('<EMAIL>');
      await wrapper.find('input[placeholder="Enter your password (min 8 characters)"]').setValue('password123');
      
      const submitButton = wrapper.find('button[type="submit"]');
      expect(submitButton.attributes('disabled')).toBeDefined();
      
      // Check the terms checkbox
      const checkbox = wrapper.find('.n-checkbox input[type="checkbox"]');
      await checkbox.setValue(true);
      await nextTick();
      
      expect(submitButton.attributes('disabled')).toBeUndefined();
    });
  });  describe('Input Validation', () => {
    beforeEach(() => {
      // Clear all mock calls before each test in this describe block
      mockApiClient.get.mockClear();
    });

    it('should show loading state while checking availability', async () => {
      // Make the API call pending
      let resolveApiCall: any;
      mockApiClient.get.mockReturnValue(new Promise(resolve => {
        resolveApiCall = resolve;
      }));
      
      createWrapper();
      
      const emailInput = wrapper.find('input[placeholder="Enter your email"]');
      await emailInput.setValue('<EMAIL>');
      await emailInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await nextTick();
      
      expect(wrapper.find('.n-input--loading').exists()).toBe(true);
      
      // Resolve the API call
      resolveApiCall({ data: { available: true } });
      await flushPromises();
    });

    it('should clear feedback messages on input change', async () => {
      mockApiClient.get.mockResolvedValue({
        data: { available: false, message: 'Email is already registered' }
      });
      
      createWrapper();
      
      const emailInput = wrapper.find('input[placeholder="Enter your email"]');
      await emailInput.setValue('<EMAIL>');
      await emailInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      await nextTick();
      
      expect(wrapper.find('.feedback-message').exists()).toBe(true);
      
      // Change input value
      await emailInput.setValue('<EMAIL>');
      await nextTick();
      
      // Feedback should be cleared immediately on input
      expect(wrapper.find('.feedback-message').exists()).toBe(false);
    });    it('should debounce API calls on rapid input changes', async () => {
      // Clear any previous mock calls for this specific test
      vi.clearAllMocks();
      mockApiClient.get.mockClear();
      
      createWrapper();
      
      const emailInput = wrapper.find('input[placeholder="Enter your email"]');
      
      // Make rapid changes, each within the debounce window
      await emailInput.setValue('t');
      await new Promise(resolve => setTimeout(resolve, 100));
      
      await emailInput.setValue('te'); 
      await new Promise(resolve => setTimeout(resolve, 100));
      
      await emailInput.setValue('tes');
      await new Promise(resolve => setTimeout(resolve, 100));
      
      await emailInput.setValue('<EMAIL>');
      
      // Wait less than debounce time to ensure no calls yet
      await new Promise(resolve => setTimeout(resolve, 400));
      expect(mockApiClient.get).not.toHaveBeenCalled();
      
      // Wait for full debounce time (500ms + some buffer)
      await new Promise(resolve => setTimeout(resolve, 200));
      await flushPromises();
      
      // Should only have been called once after all the rapid changes
      expect(mockApiClient.get).toHaveBeenCalledTimes(1);
expect(mockApiClient.get).toHaveBeenCalledWith('/auth/email/check/test%40example.com');
    });
  });
  describe('Visual States', () => {
    it('should show success icon for available email/username', async () => {
      mockApiClient.get.mockResolvedValue({
        data: { available: true }
      });
      
      createWrapper();
      
      const emailInput = wrapper.find('input[placeholder="Enter your email"]');
      await emailInput.setValue('<EMAIL>');
      await emailInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      await nextTick();
      
      expect(wrapper.find('.n-input--success').exists()).toBe(true);
    });

    it('should show error icon for taken email/username', async () => {
      mockApiClient.get.mockResolvedValue({
        data: { available: false }
      });
      
      createWrapper();
      
      const emailInput = wrapper.find('input[placeholder="Enter your email"]');
      await emailInput.setValue('<EMAIL>');
      await emailInput.trigger('blur');
      
      await new Promise(resolve => setTimeout(resolve, 600));
      await flushPromises();
      await nextTick();
      
      expect(wrapper.find('.n-input--error').exists()).toBe(true);
    });
  });
});
