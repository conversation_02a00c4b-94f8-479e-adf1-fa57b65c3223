<template>
  <div class="match-notification-card">
    <!-- Match Header -->
    <div class="match-header">
      <div class="match-info">
        <h3 class="match-title">{{ $t('matches.newMatchFound') }}</h3>
        <p class="match-id">{{ match.matchId }}</p>
      </div>
      <div class="match-status">
        <n-tag 
          :type="getStatusTagType(match.status)" 
          size="small"
          class="status-tag"
        >
          {{ getStatusText(match.status) }}
        </n-tag>
      </div>
    </div>

    <!-- Currency Exchange Info -->
    <div class="exchange-info">
      <div class="currency-pair">
        <div class="currency-from">
          <span class="currency-code">{{ match.currencyA }}</span>
          <span class="amount">{{ formatAmount(match.amountA) }}</span>
        </div>
        <div class="exchange-arrow">
          <n-icon size="20" class="arrow-icon">
            <ArrowForward />
          </n-icon>
        </div>
        <div class="currency-to">
          <span class="currency-code">{{ match.currencyB }}</span>
          <span class="amount">{{ formatAmount(match.amountB) }}</span>
        </div>
      </div>
      
      <div class="rate-info">
        <span class="rate-label">{{ $t('matches.exchangeRate') }}:</span>
        <span class="rate-value">1 {{ match.currencyA }} = {{ formatRate(match.rateAToB) }} {{ match.currencyB }}</span>
      </div>
    </div>

    <!-- Match Details -->
    <div class="match-details">
      <div class="compatibility-score">
        <n-icon size="16" class="score-icon">
          <Star />
        </n-icon>
        <span class="score-text">
          {{ $t('matches.compatibility') }}: {{ formatCompatibilityScore(match.compatibilityScore) }}%
        </span>
      </div>
      
      <div class="other-user-info">
        <div class="user-name">
          <n-icon size="16" class="user-icon">
            <Person />
          </n-icon>
          <span>{{ getOtherUserName() }}</span>
        </div>
        <ReputationIcon 
          :level="getOtherUserReputation()" 
          size="small"
          class="reputation-icon"
        />
      </div>
    </div>

    <!-- Expiration Timer -->
    <div v-if="!match.isExpired && match.timeRemaining" class="expiration-timer">
      <n-icon size="16" class="timer-icon">
        <Time />
      </n-icon>
      <span class="timer-text">
        {{ $t('matches.expiresIn') }}: {{ formatTimeRemaining(match.timeRemaining) }}
      </span>
    </div>    <!-- Status Message for Partial Accepts -->
    <div v-if="match.status === 'PARTIAL_ACCEPT' && match.isCurrentUserInvolved" class="partial-accept-status">
      <div class="waiting-status">
        <n-icon size="16" class="waiting-icon">
          <Time />
        </n-icon>
        <span v-if="match.currentUserResponse && !match.otherUserResponse">
          {{ $t('matches.waitingForOtherUser') }}
        </span>
        <span v-else-if="!match.currentUserResponse && match.otherUserResponse">
          {{ $t('matches.otherUserAcceptedWaitingForYou') }}
        </span>
        <span v-else>
          {{ $t('matches.waitingForOtherUser') }}
        </span>
      </div>
    </div>

    <!-- Action Buttons -->
    <div v-if="match.canCurrentUserRespond" class="action-buttons">
      <n-button
        type="error"
        size="large"
        class="decline-button"
        data-testid="decline-button"
        :loading="declining"
        @click="showDeclineModal = true"
      >
        <template #icon>
          <n-icon><Close /></n-icon>
        </template>
        {{ $t('matches.decline') }}
      </n-button>
      
      <n-button
        type="primary"
        size="large" 
        class="accept-button"
        data-testid="accept-button"
        :loading="accepting"
        @click="handleAccept"
      >
        <template #icon>
          <n-icon><Checkmark /></n-icon>
        </template>
        {{ $t('matches.accept') }}
      </n-button>
    </div>    <!-- Match Successful - Both Accepted or Converted -->
    <div v-else-if="match.status === 'BOTH_ACCEPTED' || match.status === 'CONVERTED'" class="match-successful">
      <div class="success-message">
        <n-icon size="24" class="success-icon">
          <Checkmark />
        </n-icon>
        <span class="success-text">{{ $t('matches.bothAccepted') }}</span>
      </div>      <div class="next-steps-buttons">
        <n-button
          type="primary"
          size="large"
          class="open-chat-button"
          data-testid="open-chat-button"
          @click="openChat"
        >
          <template #icon>
            <n-icon><ChatbubbleOutline /></n-icon>
          </template>
          {{ $t('matches.openChat') }}
        </n-button>
      </div>
    </div><!-- Response Status (for non-partial accepts) -->
    <div v-else-if="match.isCurrentUserInvolved && match.status !== 'PARTIAL_ACCEPT'" class="response-status">
      <div v-if="match.currentUserResponse" class="user-response">
        <n-icon 
          size="20" 
          :class="match.currentUserResponse === 'ACCEPTED' ? 'response-accepted' : 'response-declined'"
        >
          <Checkmark v-if="match.currentUserResponse === 'ACCEPTED'" />
          <Close v-else />
        </n-icon>
        <span>
          {{ $t(`matches.youHave${match.currentUserResponse === 'ACCEPTED' ? 'Accepted' : 'Declined'}`) }}
        </span>
      </div>
    </div>

    <!-- Decline Reason Modal -->
    <n-modal 
      v-model:show="showDeclineModal"
      class="decline-modal"
      :mask-closable="false"
      preset="dialog"
      :title="$t('matches.declineMatch')"
      :positive-text="$t('common.confirm')"
      :negative-text="$t('common.cancel')"
      :loading="declining"
      @positive-click="handleDeclineWithReason"
      @negative-click="showDeclineModal = false"
    >
      <template #default>
        <div class="decline-form">
          <p class="decline-description">
            {{ $t('matches.declineDescription') }}
          </p>
          <n-input
            v-model:value="declineReason"
            type="textarea"
            :placeholder="$t('matches.declineReasonPlaceholder')"
            :rows="3"
            :maxlength="100"
            show-count
            class="decline-reason-input"
          />
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  NTag, 
  NIcon, 
  NButton, 
  NModal, 
  NInput,
  useMessage,
  useNotification
} from 'naive-ui';
import { 
  ArrowForward, 
  Star, 
  Person, 
  Time, 
  Checkmark, 
  Close,
  ChatbubbleOutline
} from '@vicons/ionicons5';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import type { OfferMatch } from '@/types/api';
import { useMatchStore } from '@/stores/matchStore';
import { useAuthStore } from '@/stores/auth';
import ReputationIcon from './ReputationIcon.vue';

interface Props {
  match: OfferMatch;
}

const props = defineProps<Props>();
const { t } = useI18n();
const router = useRouter();
const message = useMessage();
const notification = useNotification();
const matchStore = useMatchStore();
const authStore = useAuthStore();

// State
const accepting = ref(false);
const declining = ref(false);
const showDeclineModal = ref(false);
const declineReason = ref('');

// Computed
const currentUserId = computed(() => authStore.user?.id);

// Computed property to determine the other user in the match
const otherUser = computed(() => {
  if (!currentUserId.value) return null;
  
  // If otherUser is already populated by the backend, use it
  if (props.match.otherUser && props.match.otherUser.id) {
    return props.match.otherUser;
  }
  
  // Fallback: determine other user based on userAId and userBId
  // This shouldn't be needed if backend is working correctly, but provides safety
  const isCurrentUserA = props.match.userAId === currentUserId.value;
  const isCurrentUserB = props.match.userBId === currentUserId.value;
  
  if (isCurrentUserA) {
    // Current user is userA, so other user should be userB
    // Since we don't have userB details in the basic structure, return a placeholder
    return {
      id: props.match.userBId,
      username: `User_${props.match.userBId.slice(0, 8)}`,
      email: '',
      reputationLevel: 1
    };
  } else if (isCurrentUserB) {
    // Current user is userB, so other user should be userA
    return {
      id: props.match.userAId,
      username: `User_${props.match.userAId.slice(0, 8)}`,
      email: '',
      reputationLevel: 1
    };
  }
  
  return null;
});

// Methods
function getStatusTagType(status: string) {
  switch (status) {
    case 'PENDING': return 'warning';
    case 'BOTH_ACCEPTED': return 'success';
    case 'PARTIAL_ACCEPT': return 'info';
    case 'DECLINED': return 'error';
    case 'EXPIRED': return 'default';
    case 'CONVERTED': return 'success';
    default: return 'default';
  }
}

function getStatusText(status: string) {
  return t(`matches.status.${status.toLowerCase()}`);
}

function formatAmount(amount: number) {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount);
}

function formatRate(rate: number) {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 6
  }).format(rate);
}

function formatCompatibilityScore(score: number) {
  return Math.round(score * 100);
}

function formatTimeRemaining(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    return `${remainingSeconds}s`;
  }
}

function getOtherUserName(): string {
  if (!currentUserId.value || !otherUser.value) return '';
  
  return otherUser.value.username || otherUser.value.id.slice(0, 8);
}

function getOtherUserReputation(): number {
  if (!currentUserId.value || !otherUser.value) return 1;
  
  return otherUser.value.reputationLevel;
}

async function handleAccept() {  accepting.value = true;
  
  try {
    const response = await matchStore.acceptMatch(props.match.id);
    
    if (response && response.success) {
      message.success(t('matches.acceptSuccess'));
      
      // Navigate to chat if available
      if (response.chatSessionId) {
        notification.success({
          title: t('matches.matchAccepted'),
          content: t('matches.chatSessionCreated'),
          duration: 5000
        });
        
        // Optional: Navigate to chat
        // router.push(`/chat/${response.chatSessionId}`);
      }
    }
  } catch (error) {
    console.error('Failed to accept match:', error);
    message.error(t('matches.acceptError'));
  } finally {
    accepting.value = false;
  }
}

async function handleDeclineWithReason() {
  declining.value = true;
  
  try {
    await matchStore.declineMatch(props.match.id, declineReason.value);
    message.success(t('matches.declineSuccess'));
    showDeclineModal.value = false;
    declineReason.value = '';
  } catch (error) {
    console.error('Failed to decline match:', error);
    message.error(t('matches.declineError'));
  } finally {
    declining.value = false;
  }
}

// Navigation methods for successful matches
function openChat() {
  console.log('Opening chat for match:', props.match);
  console.log('Full match object:', JSON.stringify(props.match, null, 2));
  console.log('Chat session ID:', props.match.chatSessionId);
  console.log('Match status:', props.match.status);
  
  if (props.match.chatSessionId) {
    console.log('Navigating to chat:', `/chat/${props.match.chatSessionId}`);
    router.push(`/chat/${props.match.chatSessionId}`);
  } else {
    console.warn('Chat session ID is missing. Match details:', {
      id: props.match.id,
      status: props.match.status,
      chatSessionId: props.match.chatSessionId,
      transactionId: props.match.transactionId
    });
    
    // Try to refresh match data first
    message.warning('Chat session not available. Refreshing match data...');
    matchStore.loadMatches().then(() => {
      // Check again after refresh
      const refreshedMatch = matchStore.findMatchById(props.match.id);
      console.log('Refreshed match:', refreshedMatch);
      if (refreshedMatch?.chatSessionId) {
        console.log('Found chat session after refresh, navigating...');
        router.push(`/chat/${refreshedMatch.chatSessionId}`);
      } else {
        console.error('Still no chat session after refresh');
        message.error('Chat session not ready yet. Please try again in a moment.');
      }
    }).catch(error => {
      console.error('Failed to refresh matches:', error);
      message.error('Failed to load chat. Please try again.');
    });
  }
}
</script>

<style scoped>
/* Mobile-first design */
.match-notification-card {
  background: var(--card-background);
  border-radius: 12px;
  padding: 1rem;
  margin: 0.5rem 0;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.match-notification-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* Header */
.match-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.match-info {
  flex: 1;
}

.match-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--text-color-primary);
}

.match-id {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
  margin: 0;
  font-family: monospace;
}

.status-tag {
  margin-left: 0.5rem;
}

/* Exchange Info */
.exchange-info {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.currency-pair {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.currency-from,
.currency-to {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.currency-code {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color-primary);
}

.amount {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
  margin-top: 0.25rem;
}

.exchange-arrow {
  margin: 0 1rem;
  color: var(--primary-color);
}

.arrow-icon {
  transform: rotate(0deg);
}

.rate-info {
  text-align: center;
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

.rate-label {
  margin-right: 0.5rem;
}

.rate-value {
  font-weight: 500;
  color: var(--text-color-primary);
}

/* Match Details */
.match-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.compatibility-score,
.other-user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.score-icon,
.user-icon {
  color: var(--warning-color);
}

.score-text {
  color: var(--text-color-secondary);
}

.other-user-info {
  justify-content: space-between;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color-primary);
}

/* Expiration Timer */
.expiration-timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--warning-color-light);
  border-radius: 6px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.timer-icon {
  color: var(--warning-color);
}

.timer-text {
  font-weight: 500;
  color: var(--warning-color-dark);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.decline-button,
.accept-button {
  flex: 1;
  height: 48px; /* Mobile-friendly touch target */
  border-radius: 8px;
  font-weight: 600;
}

/* Response Status */
.response-status {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--background-secondary);
  border-radius: 8px;
  margin-top: 1rem;
}

.user-response,
.waiting-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.response-accepted {
  color: var(--success-color);
}

.response-declined {
  color: var(--error-color);
}

.waiting-icon {
  color: var(--info-color);
}

/* Decline Modal */
.decline-form {
  padding: 0.5rem 0;
}

.decline-description {
  margin-bottom: 1rem;
  color: var(--text-color-secondary);
  line-height: 1.5;
}

.decline-reason-input {
  width: 100%;
}

/* Match Successful Section */
.match-successful {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--background-secondary);
  border-radius: 8px;
  border: 1px solid var(--success-color-light);
}

.success-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--success-color-light);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.success-icon {
  color: var(--success-color);
}

.success-text {
  font-weight: 600;
  color: var(--success-color-dark);
  font-size: 1rem;
}

.next-steps-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

.open-chat-button,
.view-transaction-button {
  width: 100%;
  height: 48px; /* Mobile-friendly touch target */
  border-radius: 8px;
  font-weight: 600;
}

/* Tablet enhancements */
@media (min-width: 768px) {
  .match-notification-card {
    padding: 1.25rem;
  }
  
  .match-details {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .action-buttons {
    flex-direction: row;
    justify-content: flex-end;
    gap: 1rem;
  }
    .decline-button,
  .accept-button {
    flex: 0 0 auto;
    min-width: 120px;
  }
  
  .next-steps-buttons {
    flex-direction: row;
    justify-content: center;
    gap: 1rem;
  }
  
  .open-chat-button,
  .view-transaction-button {
    flex: 0 0 auto;
    min-width: 160px;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  .match-notification-card {
    padding: 1.5rem;
  }
  
  .currency-pair {
    justify-content: center;
    gap: 2rem;
  }
  
  .exchange-arrow {
    margin: 0 2rem;
  }
}
</style>
