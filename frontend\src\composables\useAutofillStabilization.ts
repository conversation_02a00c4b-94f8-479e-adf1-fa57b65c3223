// filepath: c:\Code\MUNygo\frontend\src\composables\useAutofillStabilization.ts

/**
 * Composable for autofill stabilization in Vue components
 * 
 * Provides utilities to stabilize form elements and prevent
 * autofill overlay DOM manipulation conflicts.
 */

import { onMounted, onUnmounted, ref, type Ref } from 'vue';
import { autofillHandler } from '@/utils/autofillHandler';

interface UseAutofillStabilizationOptions {
  /**
   * Automatically apply stabilization classes to form elements
   */
  autoStabilize?: boolean;
  
  /**
   * CSS selector for elements to stabilize
   */
  selector?: string;
  
  /**
   * Additional CSS classes to apply for stabilization
   */
  additionalClasses?: string[];
}

interface UseAutofillStabilizationReturn {
  /**
   * Manually stabilize a specific element
   */
  stabilizeElement: (element: HTMLElement) => void;
  
  /**
   * Remove stabilization from a specific element
   */
  unstabilizeElement: (element: HTMLElement) => void;
  
  /**
   * Stabilize all form elements in the component
   */
  stabilizeForm: (root?: HTMLElement) => void;
  
  /**
   * Safe wrapper for DOM insertBefore operations in autofill contexts
   */
  safeInsertBefore: (parent: Node, newNode: Node, referenceNode: Node | null) => Node;
  
  /**
   * Safe wrapper for DOM removeChild operations in autofill contexts
   */
  safeRemoveChild: (parent: Node, child: Node) => Node;
  
  /**
   * Check if autofill handler is active
   */
  isStabilized: Ref<boolean>;
}

/**
 * Use autofill stabilization for form components
 */
export function useAutofillStabilization(
  options: UseAutofillStabilizationOptions = {}
): UseAutofillStabilizationReturn {
  const {
    autoStabilize = true,
    selector = 'input[type="email"], input[type="password"], input[type="text"], input[type="tel"], .n-input, .n-form-item',
    additionalClasses = []
  } = options;

  const isStabilized = ref(false);
  const stabilizedElements = new Set<HTMLElement>();

  /**
   * Apply stabilization classes to an element and protect it from autofill conflicts
   */
  const stabilizeElement = (element: HTMLElement): void => {
    if (!element || stabilizedElements.has(element)) {
      return;
    }

    // Add base stabilization class
    element.classList.add('autofill-stable');
    
    // Add any additional classes
    additionalClasses.forEach(className => {
      element.classList.add(className);
    });

    // Apply DOM method interception protection
    autofillHandler.protectElement(element);

    // Track stabilized elements
    stabilizedElements.add(element);
  };

  /**
   * Remove stabilization classes from an element and remove autofill protection
   */
  const unstabilizeElement = (element: HTMLElement): void => {
    if (!element || !stabilizedElements.has(element)) {
      return;
    }

    // Remove base stabilization class
    element.classList.remove('autofill-stable');
    
    // Remove any additional classes
    additionalClasses.forEach(className => {
      element.classList.remove(className);
    });

    // Remove DOM method interception protection
    autofillHandler.unprotectElement(element);

    // Remove from tracking
    stabilizedElements.delete(element);
  };

  /**
   * Stabilize all form elements in the current component
   */
  const stabilizeForm = (root?: HTMLElement): void => {
    const queryRoot = root || document;
    const elements = queryRoot.querySelectorAll(selector);
    elements.forEach((element) => {
      if (element instanceof HTMLElement) {
        stabilizeElement(element);
      }
    });
    isStabilized.value = true;
  };

  /**
   * Remove stabilization from all tracked elements
   */
  const cleanup = (): void => {
    stabilizedElements.forEach(element => {
      unstabilizeElement(element);
    });
    stabilizedElements.clear();
    isStabilized.value = false;
  };

  // Auto-stabilize on mount if enabled
  onMounted(() => {
    if (autoStabilize) {
      // Use a small delay to ensure DOM is fully rendered
      setTimeout(() => {
        stabilizeForm();
      }, 50);
    }
  });

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup();
  });

  return {
    stabilizeElement,
    unstabilizeElement,
    stabilizeForm,
    safeInsertBefore: (parent: Node, newNode: Node, referenceNode: Node | null) => 
      autofillHandler.safeInsertBefore(parent, newNode, referenceNode),
    safeRemoveChild: (parent: Node, child: Node) => 
      autofillHandler.safeRemoveChild(parent, child),
    isStabilized
  };
}

/**
 * Directive for autofill stabilization
 * 
 * Usage: v-autofill-stable
 */
export const vAutofillStable = {
  mounted(el: HTMLElement) {
    el.classList.add('autofill-stable');
    // Apply DOM method interception protection
    autofillHandler.protectElement(el);
  },
  unmounted(el: HTMLElement) {
    el.classList.remove('autofill-stable');
    // Remove DOM method interception protection
    autofillHandler.unprotectElement(el);
  }
};

/**
 * Utility function to check if current browser has autofill issues
 */
export function hasAutofillIssues(): boolean {
  const userAgent = navigator.userAgent.toLowerCase();
  
  // Chrome-based browsers are most likely to have autofill overlay issues
  const isChrome = userAgent.includes('chrome') && !userAgent.includes('edg');
  const isEdge = userAgent.includes('edg');
  const isBrave = userAgent.includes('brave');
  
  return isChrome || isEdge || isBrave;
}

/**
 * Utility function to detect if autofill is likely active
 */
export function isAutofillActive(): boolean {
  // Check for common autofill indicators
  const autofillInputs = document.querySelectorAll('input:-webkit-autofill');
  return autofillInputs.length > 0;
}
