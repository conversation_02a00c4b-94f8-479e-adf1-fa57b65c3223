const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function simulateBrowseEndpoint() {
  try {
    console.log('=== Simulating /offers/browse endpoint ===');
    
    const users = await prisma.user.findMany({
      select: { id: true, username: true, reputationLevel: true }
    });
    
    for (const user of users) {
      console.log(`\n--- Testing as user: ${user.username} (${user.id}) ---`);
      
      const offers = await prisma.offer.findMany({
        where: { 
          status: 'ACTIVE', 
          userId: { not: user.id } 
        },
        include: {
          user: { select: { username: true, reputationLevel: true } },
          interests: {
            where: { interestedUserId: user.id },
            select: { id: true, status: true, chatSession: { select: { id: true } } },
            take: 1
          }
        },
        orderBy: { createdAt: 'desc' },
      });
      
      console.log(`Found ${offers.length} offers for this user:`);
      offers.forEach(offer => {
        console.log(`  - ${offer.type} ${offer.amount} by ${offer.user.username} (ID: ${offer.id})`);
      });
      
      if (offers.length === 0) {
        console.log('  ❌ This user would see NO offers in browse');
      } else {
        console.log('  ✅ This user would see offers in browse');
      }
    }
    
  } catch (error) {
    console.error('Error simulating browse endpoint:', error);
  } finally {
    await prisma.$disconnect();
  }
}

simulateBrowseEndpoint();
