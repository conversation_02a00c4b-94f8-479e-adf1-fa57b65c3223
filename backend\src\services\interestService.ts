import { PrismaClient, InterestStatus, OfferType, TransactionStatus } from '@prisma/client';
import { io } from '../index'; // Correctly import the io instance
import {
  INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY,
  INTEREST_REQUEST_DECLINED,
  TRANSACTION_STATUS_UPDATED,
  TransactionStatusUpdatePayload // Import the payload type
} from '../types/socketEvents';
import { getNotificationService } from '../index'; // Import NotificationService getter
import { NotificationType } from '@prisma/client'; // Import NotificationType
import { createInitialTransaction } from './transactionService'; // Create initial transaction upon interest acceptance
import { getPrismaClient } from '../utils/database';

const prisma = getPrismaClient(); // Use shared PrismaClient singleton

export const interestService = {
  async acceptInterest(interestId: string, currentUserId: string) {
    console.log(`[interestService] Attempting to accept interest: ${interestId} by user: ${currentUserId}`);

    try {
      const result = await prisma.$transaction(async (tx) => {
        // 1. Fetch the interest and related offer details
        const interest = await tx.interest.findUnique({
          where: { id: interestId },
          include: { 
            offer: { // Include offer details, including baseRate and user
              include: {
                user: { select: { id: true, username: true, email: true, reputationLevel: true } }
              }
            },
            interestedUser: { 
              select: { id: true, username: true, email: true, reputationLevel: true } 
            }
          },
        });

        if (!interest) {
          console.error(`[interestService] Interest not found: ${interestId}`);
          throw new Error('Interest not found');
        }
        console.log(`[interestService] Interest found: ${JSON.stringify(interest)}`);

        // Verify current user is the offer creator
        if (interest.offer.userId !== currentUserId) {
          console.error(`[interestService] Unauthorized attempt to accept interest ${interestId} by user ${currentUserId}. Offer creator is ${interest.offer.userId}`);
          throw new Error('Unauthorized to accept this interest');
        }

        // Check if interest is already accepted
        if (interest.status === InterestStatus.ACCEPTED) {
          console.warn(`[interestService] Interest ${interestId} is already accepted.`);
          throw new Error('Interest already accepted');
        }
        
        const offerCreatorId = interest.offer.userId;
        const interestedUserIdActual = interest.interestedUserId;

        if (!offerCreatorId || typeof offerCreatorId !== 'string' || offerCreatorId.trim() === '') {
          console.error(`[interestService] Invalid offerCreatorId: '${offerCreatorId}' (type: ${typeof offerCreatorId}) for interest ${interestId}`);
          throw new Error('Invalid offer creator ID provided.');
        }
        if (!interestedUserIdActual || typeof interestedUserIdActual !== 'string' || interestedUserIdActual.trim() === '') {
          console.error(`[interestService] Invalid interestedUserIdActual: '${interestedUserIdActual}' (type: ${typeof interestedUserIdActual}) for interest ${interestId}`);
          throw new Error('Invalid interested user ID provided.');
        }
        
        console.log(`[interestService] Validated IDs - OfferCreator: ${offerCreatorId}, InterestedUser: ${interestedUserIdActual}`);

        // 2. Create a ChatSession
        console.log(`[interestService] Creating ChatSession for offer: ${interest.offerId}, between ${offerCreatorId} and ${interestedUserIdActual}`);
        const chatSession = await tx.chatSession.create({
          data: {
            offerId: interest.offerId,
            userOneId: offerCreatorId, // Correct field name from schema
            userTwoId: interestedUserIdActual, // Correct field name from schema
            interestId: interest.id, // Link to the interest
          },
        });
        console.log(`[interestService] ChatSession created: ${chatSession.id}`);

        // 3. Update the Interest status to ACCEPTED and link ChatSession
        const updatedInterest = await tx.interest.update({
          where: { id: interestId },
          data: {
            status: InterestStatus.ACCEPTED,
            chatSession: { // Correct way to link the relation via connect
              connect: { id: chatSession.id }
            }
          },          include: { // Re-include offer with baseRate for consistency, though already fetched
            offer: {
              select: {
                id: true,
                currencyPair: true,
                type: true,
                amount: true,
                baseRate: true, 
                adjustmentForLowerRep: true,
                adjustmentForHigherRep: true,
                user: { select: { id: true, username: true, email: true, reputationLevel: true } }
              }
            }
          }
        });
        console.log(`[interestService] Interest ${interestId} status updated to ACCEPTED, linked to chat ${chatSession.id}`);
          // 4. Create initial transaction linked to this chat session
        const { currencyPair, amount: offerAmountA, type: offerType, baseRate, adjustmentForLowerRep, adjustmentForHigherRep } = updatedInterest.offer;
        const [currencyAFromPair, currencyBFromPair] = currencyPair.split('-');
        
        // Fetch user reputation levels for rate adjustment calculation
        const [offerCreator, interestedUser] = await Promise.all([
          tx.user.findUnique({ 
            where: { id: offerCreatorId },
            select: { reputationLevel: true }
          }),
          tx.user.findUnique({ 
            where: { id: interestedUserIdActual },
            select: { reputationLevel: true }
          })
        ]);

        if (!offerCreator || !interestedUser) {
          throw new Error('Could not fetch user reputation levels for transaction creation');
        }

        const creatorRepLevel = offerCreator.reputationLevel ?? 3;
        const takerRepLevel = interestedUser.reputationLevel ?? 3;
          // Calculate reputation-adjusted exchange rate (same logic as in chatRoutes.ts)
        if (!baseRate || baseRate <= 0) {
          throw new Error('Invalid or missing base rate for offer');
        }
        
        // Validate adjustment values - ensure they are defined, numeric, and non-negative
        const validAdjustmentForHigherRep = (typeof adjustmentForHigherRep === 'number' && 
          !isNaN(adjustmentForHigherRep) && adjustmentForHigherRep >= 0) ? adjustmentForHigherRep : 0;
        const validAdjustmentForLowerRep = (typeof adjustmentForLowerRep === 'number' && 
          !isNaN(adjustmentForLowerRep) && adjustmentForLowerRep >= 0) ? adjustmentForLowerRep : 0;
        
        if (!baseRate || baseRate <= 0) {
          throw new Error('Invalid or missing base rate for offer');
        }
        let agreedExchangeRate = baseRate;
        
        if (takerRepLevel !== creatorRepLevel) {
          if (offerType === OfferType.SELL) { 
            if (takerRepLevel > creatorRepLevel) { 
              agreedExchangeRate = (baseRate ?? 1) * (1 - (validAdjustmentForHigherRep / 100));
            } else { 
              agreedExchangeRate = (baseRate ?? 1) * (1 + (validAdjustmentForLowerRep / 100));
            }
          } else { // BUY
            if (takerRepLevel > creatorRepLevel) { 
              agreedExchangeRate = (baseRate ?? 1) * (1 + (validAdjustmentForHigherRep / 100));
            } else { 
              agreedExchangeRate = (baseRate ?? 1) * (1 - (validAdjustmentForLowerRep / 100));
            }
          }
        }
        
        let finalCurrencyA: string, finalAmountA: number, finalCurrencyAProviderId: string;
        let finalCurrencyB: string, finalAmountB: number, finalCurrencyBProviderId: string;

        // If offer type is SELL (e.g. User wants to SELL CAD for IRR):
        // User (offer creator) provides currencyA (CAD) and expects to receive currencyB (IRR).
        // So, currencyAProvider is offerCreatorId, currencyBProvider is interestedUserIdActual.
        if (offerType === OfferType.SELL) { 
          finalCurrencyA = currencyAFromPair;
          finalAmountA = offerAmountA;
          finalCurrencyAProviderId = offerCreatorId;
          finalCurrencyB = currencyBFromPair;
          finalAmountB = offerAmountA * agreedExchangeRate; // Use reputation-adjusted rate
          finalCurrencyBProviderId = interestedUserIdActual;
        } 
        // If offer type is BUY (e.g. User wants to BUY CAD with IRR):
        // User (offer creator) wants to receive currencyA (CAD) and will pay with currencyB (IRR).
        // So, currencyAProvider is interestedUserIdActual (the one giving CAD), currencyBProvider is offerCreatorId (the one giving IRR).
        else { // BUY
          finalCurrencyA = currencyAFromPair;
          finalAmountA = offerAmountA;
          finalCurrencyAProviderId = interestedUserIdActual; 
          finalCurrencyB = currencyBFromPair;
          finalAmountB = offerAmountA * agreedExchangeRate; // Use reputation-adjusted rate
          finalCurrencyBProviderId = offerCreatorId;
        }

        const transaction = await createInitialTransaction(
          tx,
          chatSession.id,
          updatedInterest.offer.id, // Ensure this is the actual offerId (CUID string)
          finalCurrencyA,
          finalAmountA,
          finalCurrencyAProviderId,
          finalCurrencyB,
          finalAmountB,
          finalCurrencyBProviderId
        );
        console.log(`[interestService] Initial transaction ${transaction.id} created for chat ${chatSession.id}`);
        
        // 5. Emit transaction status updated event
        const transactionPayload: TransactionStatusUpdatePayload = {
          chatSessionId: chatSession.id,
          transactionId: transaction.id,
          offerId: interest.offer.id, // Add missing offerId
          status: transaction.status,
          currencyA: transaction.currencyA,
          amountA: transaction.amountA,
          currencyAProviderId: transaction.currencyAProviderId,
          currencyAProviderUsername: null, // Will be fetched separately if needed
          currencyB: transaction.currencyB,
          amountB: transaction.amountB,
          currencyBProviderId: transaction.currencyBProviderId,
          currencyBProviderUsername: null, // Will be fetched separately if needed
          agreedFirstPayerId: transaction.agreedFirstPayerId,
          termsAgreementTimestampPayer1: transaction.termsAgreementTimestampPayer1?.toISOString() || null,
          termsAgreementTimestampPayer2: transaction.termsAgreementTimestampPayer2?.toISOString() || null,
          firstPayerDesignationTimestamp: transaction.firstPayerDesignationTimestamp?.toISOString() || null,
          paymentExpectedByPayer1: transaction.paymentExpectedByPayer1?.toISOString() || null,
          paymentDeclaredAtPayer1: transaction.paymentDeclaredAtPayer1?.toISOString() || null,
          paymentTrackingNumberPayer1: transaction.paymentTrackingNumberPayer1,
          firstPaymentConfirmedByPayer2At: transaction.firstPaymentConfirmedByPayer2At?.toISOString() || null,
          paymentExpectedByPayer2: transaction.paymentExpectedByPayer2?.toISOString() || null,
          paymentDeclaredAtPayer2: transaction.paymentDeclaredAtPayer2?.toISOString() || null,
          paymentTrackingNumberPayer2: transaction.paymentTrackingNumberPayer2,
          secondPaymentConfirmedByPayer1At: transaction.secondPaymentConfirmedByPayer1At?.toISOString() || null,
          cancellationReason: transaction.cancellationReason,
          cancelledByUserId: transaction.cancelledByUserId,
          disputeReason: transaction.disputeReason,
          disputedByUserId: transaction.disputedByUserId,
          disputeResolvedAt: transaction.disputeResolvedAt?.toISOString() || null,
          disputeResolutionNotes: transaction.disputeResolutionNotes,
          updatedAt: transaction.updatedAt.toISOString(),
          createdAt: transaction.createdAt.toISOString(),
        };
        io.to(offerCreatorId).emit(TRANSACTION_STATUS_UPDATED, transactionPayload);
        io.to(interestedUserIdActual).emit(TRANSACTION_STATUS_UPDATED, transactionPayload);
        console.log(`[interestService] Emitted ${TRANSACTION_STATUS_UPDATED} to ${offerCreatorId} and ${interestedUserIdActual}`);

        // Prepare details for INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY event
        const offerCreatorDetails = {
          userId: interest.offer.user.id,
          username: interest.offer.user.username || interest.offer.user.email,
          reputationLevel: interest.offer.user.reputationLevel || 1, // Default if null
        };
        const interestedUserDetails = {
          userId: interest.interestedUser.id,
          username: interest.interestedUser.username || interest.interestedUser.email,
          reputationLevel: interest.interestedUser.reputationLevel || 1, // Default if null
        };

        io.to(offerCreatorId).emit(INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY, {
          interestId: updatedInterest.id,
          offerId: updatedInterest.offer.id,
          chatSessionId: chatSession.id,
          offer: {
            type: updatedInterest.offer.type,
            amount: updatedInterest.offer.amount,
            currencyPair: updatedInterest.offer.currencyPair,
          },
          offerCreator: offerCreatorDetails,
          interestedUser: interestedUserDetails,
          message: `Your chat with ${interestedUserDetails.username} for offer ${updatedInterest.offer.id} is ready.`,
        });

        io.to(interestedUserIdActual).emit(INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY, {
          interestId: updatedInterest.id,
          offerId: updatedInterest.offer.id,
          chatSessionId: chatSession.id,
          offer: {
            type: updatedInterest.offer.type,
            amount: updatedInterest.offer.amount,
            currencyPair: updatedInterest.offer.currencyPair,
          },
          offerCreator: offerCreatorDetails,
          interestedUser: interestedUserDetails,
          message: `Your interest in offer ${updatedInterest.offer.id} was accepted by ${offerCreatorDetails.username}. Chat is ready.`,        });
        console.log(`[interestService] Emitted ${INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY} to both users.`);

        // Create persistent notification for the interested user
        const notificationService = getNotificationService();
        if (notificationService) {
          try {
            await notificationService.createNotification({
              userId: interestedUserIdActual,
              type: NotificationType.YOUR_INTEREST_ACCEPTED,
              message: `Your interest in offer "${updatedInterest.offer.currencyPair || 'Currency Exchange'}" was accepted by ${offerCreatorDetails.username}. Chat is ready.`,
              relatedEntityType: 'CHAT_SESSION',
              relatedEntityId: chatSession.id,
              actorId: offerCreatorId,
              actorUsername: offerCreatorDetails.username,
              data: JSON.stringify({
                interestId: updatedInterest.id,
                offerId: updatedInterest.offer.id,
                chatSessionId: chatSession.id,
                offerTitle: updatedInterest.offer.currencyPair || 'Currency Exchange',
                offerType: updatedInterest.offer.type,
                offerAmount: updatedInterest.offer.amount,
                offerCreatorUsername: offerCreatorDetails.username,
                createdAt: new Date().toISOString(),
              })
            });
            console.log(`[interestService] Created YOUR_INTEREST_ACCEPTED notification for user ${interestedUserIdActual}`);
          } catch (e) {
            console.error(`[interestService] Failed to create notification for accepted interest ${updatedInterest.id}:`, e);
          }
        }

        return { updatedInterest, chatSession, transaction };
      });

      console.log(`[interestService] Successfully accepted interest ${interestId}. Chat session ${result.chatSession.id} and transaction ${result.transaction.id} created.`);
      return result;

    } catch (error: any) {
      console.error(`[interestService] Full error object while accepting interest ${interestId}:`, error);
      if (error.message) {
        console.error(`[interestService] Error message: ${error.message}`);
      }
      if (error.stack) {
        console.error(`[interestService] Error stack: ${error.stack}`);
      }

      if (error.message === 'Interest not found' || 
          error.message === 'Unauthorized to accept this interest' || 
          error.message === 'Interest already accepted' ||
          error.message === 'Invalid offer creator ID provided.' ||
          error.message === 'Invalid interested user ID provided.') {
        throw error; 
      }
      
      let errorMessage = 'Failed to accept interest.';
      if (error.message) { 
        const originalMessage = String(error.message).substring(0, 200); 
        errorMessage += ` Details: ${originalMessage}`;
      }
      throw new Error(errorMessage);
    }
  },

  async declineInterest(interestId: string, currentUserId: string, reasonCode?: string) {
    console.log(`[interestService] Attempting to decline interest: ${interestId} by user: ${currentUserId}, reason: ${reasonCode || 'N/A'}`);
    // const io = getIO(); // Use the imported io directly

    try {
      const interest = await prisma.interest.findUnique({
        where: { id: interestId },
        include: { 
            offer: { 
                include: { 
                    user: { select: { id: true, username: true, email: true }} // Offer creator details
                }
            }, 
            interestedUser: { select: { id: true, username: true, email: true }} // Interested user details
        },
      });

      if (!interest) {
        console.error(`[interestService] Interest not found: ${interestId} for decline.`);
        throw new Error('Interest not found');
      }

      // Verify current user is the offer creator
      if (interest.offer.userId !== currentUserId) {
        console.error(`[interestService] Unauthorized attempt to decline interest ${interestId} by user ${currentUserId}. Offer creator is ${interest.offer.userId}`);
        throw new Error('Unauthorized to decline this interest');
      }
      
      // Check if interest is already declined or accepted
      if (interest.status === InterestStatus.DECLINED) {
        console.warn(`[interestService] Interest ${interestId} is already declined.`);
        throw new Error('Interest already declined');
      }
      if (interest.status === InterestStatus.ACCEPTED) {
        console.warn(`[interestService] Cannot decline interest ${interestId} as it is already accepted.`);
        throw new Error('Cannot decline an accepted interest');
      }

      const updatedInterest = await prisma.interest.update({
        where: { id: interestId },
        data: {
          status: InterestStatus.DECLINED,
          declineReasonCode: reasonCode,
        },
        // Include offer details for the event payload
        include: {
          offer: {
            select: {
              id: true,
              type: true,
              amount: true,
              currencyPair: true,
            }
          }
        }
      });
      console.log(`[interestService] Interest ${interestId} status updated to DECLINED.`);

      const offerCreatorDetails = {
        id: interest.offer.user.id,
        username: interest.offer.user.username || interest.offer.user.email,
      };
      
      // Prepare offer details for the socket event, ensuring updatedInterest.offer is used
      const offerDetailsForEvent = {
        id: updatedInterest.offer.id,
        type: updatedInterest.offer.type,
        amount: updatedInterest.offer.amount,
        currencyPair: updatedInterest.offer.currencyPair,
      };
      
      io.to(interest.interestedUserId).emit(INTEREST_REQUEST_DECLINED, {
        interestId: updatedInterest.id,
        offerId: interest.offerId, // or updatedInterest.offerId, should be same
        offer: offerDetailsForEvent, // Send offer details
        offerCreator: offerCreatorDetails, // Renamed from declinedBy to match frontend type
        reasonCode: updatedInterest.declineReasonCode,
        // message: `Your interest in offer ${interest.offerId} was declined by ${offerCreatorDetails.username}${updatedInterest.declineReasonCode ? ` (Reason: ${updatedInterest.declineReasonCode})` : \'.\'}`,
      });
      console.log(`[interestService] Emitted ${INTEREST_REQUEST_DECLINED} to interested user ${interest.interestedUserId}`);

      // Create notification for the interested user
      const notificationService = getNotificationService();
      if (notificationService && interest.interestedUser) {
        try {
          // Construct a more detailed message
          const offerType = updatedInterest.offer.type;
          const offerAmount = updatedInterest.offer.amount;
          const currencyPair = updatedInterest.offer.currencyPair;
          // Attempt to split currency pair safely
          const baseCurrency = currencyPair.includes('/') ? currencyPair.split('/')[0] : currencyPair;
          const quoteCurrency = currencyPair.includes('/') ? currencyPair.split('/')[1] : '';
          
          let detailedMessage = `Your interest in the ${offerType} offer of ${offerAmount} ${baseCurrency}`;
          if (quoteCurrency) {
            detailedMessage += ` for ${quoteCurrency}`;
          }
          detailedMessage += ` was declined by ${offerCreatorDetails.username}.`;
          if (reasonCode) {
            detailedMessage += ` Reason: ${reasonCode}`;
          } else {
            detailedMessage += ` Reason: Not specified`;
          }

          await notificationService.createNotification({
            userId: interest.interestedUserId,            type: NotificationType.YOUR_INTEREST_DECLINED,
            message: detailedMessage, // Use the new detailed message
            relatedEntityType: 'OFFER',
            relatedEntityId: interest.offerId,
            actorId: offerCreatorDetails.id,
            actorUsername: offerCreatorDetails.username,
            data: JSON.stringify({
              interestId: updatedInterest.id,
              offerId: interest.offerId,
              reasonCode: updatedInterest.declineReasonCode,
              declinedByUsername: offerCreatorDetails.username,
            })
          });
        } catch (e) {
          console.error(`[interestService] Failed to create notification for declined interest ${updatedInterest.id}:`, e);
        }
      }

      return updatedInterest;
    } catch (error: any) {
      console.error(`[interestService] Error declining interest ${interestId}:`, error);
      if (error.message === 'Interest not found' || 
          error.message === 'Unauthorized to decline this interest' ||
          error.message === 'Interest already declined' ||
          error.message === 'Cannot decline an accepted interest') {
        throw error;
      }
      // Default error message for decline
      let errorMessage = 'Failed to decline interest.';
      if (error.message) { 
        const originalMessage = String(error.message).substring(0, 200); 
        errorMessage += ` Details: ${originalMessage}`;
      }
      throw new Error(errorMessage);
    }
  },
};
