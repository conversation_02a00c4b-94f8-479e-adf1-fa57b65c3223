const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function diagnoseProductionDebug() {
  console.log('🔍 Diagnosing production debug reports...\n');
  
  try {
    // 1. Check if we can connect to the database
    console.log('1. Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful\n');

    // 2. Check if DebugReport table exists and its structure
    console.log('2. Checking DebugReport table structure...');
    try {
      const tableInfo = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'debug_reports' 
        ORDER BY ordinal_position;
      `;
      console.log('✅ DebugReport table exists with columns:');
      tableInfo.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(required)'}`);
      });
      console.log('');
    } catch (err) {
      console.log('❌ DebugReport table not found or inaccessible');
      console.log('Error:', err.message);
      return;
    }

    // 3. Count total debug reports
    console.log('3. Counting debug reports...');
    try {
      const count = await prisma.debugReport.count();
      console.log(`✅ Total debug reports: ${count}\n`);
    } catch (err) {
      console.log('❌ Error counting debug reports:', err.message);
      return;
    }

    // 4. Check recent reports (last 5)
    console.log('4. Checking recent debug reports...');
    try {
      const recentReports = await prisma.debugReport.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: { id: true, email: true, username: true }
          },
          tags: true
        }
      });

      if (recentReports.length === 0) {
        console.log('ℹ️  No debug reports found\n');
      } else {
        console.log(`✅ Found ${recentReports.length} recent reports:`);
        recentReports.forEach((report, index) => {
          console.log(`   ${index + 1}. ID: ${report.reportId}`);
          console.log(`      Type: ${report.type}, Severity: ${report.severity}`);
          console.log(`      Title: ${report.title?.substring(0, 50)}...`);
          console.log(`      User: ${report.user ? `${report.user.email} (${report.user.username})` : 'Anonymous'}`);
          console.log(`      Created: ${report.createdAt}`);
          console.log('');
        });
      }
    } catch (err) {
      console.log('❌ Error fetching recent reports:', err.message);
      console.log('Stack trace:', err.stack);
      return;
    }

    // 5. Test the exact query that the API uses
    console.log('5. Testing API query with filters...');
    try {
      const testQuery = await prisma.debugReport.findMany({
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          user: { select: { id: true, email: true, username: true } },
          assignedTo: { select: { id: true, email: true, username: true } },
          tags: true,
          _count: { select: { comments: true } }
        }
      });
      console.log(`✅ API query test successful, returned ${testQuery.length} reports\n`);
    } catch (err) {
      console.log('❌ Error with API query:', err.message);
      console.log('Stack trace:', err.stack);
      return;
    }

    // 6. Check environment variables
    console.log('6. Checking environment variables...');
    const requiredEnvVars = ['DATABASE_URL', 'VITE_ADMIN_EMAILS'];
    requiredEnvVars.forEach(envVar => {
      const value = process.env[envVar];
      if (value) {
        if (envVar === 'DATABASE_URL') {
          // Mask password for security
          const maskedUrl = value.replace(/:[^:@]*@/, ':****@');
          console.log(`   ✅ ${envVar}: ${maskedUrl}`);
        } else {
          console.log(`   ✅ ${envVar}: ${value}`);
        }
      } else {
        console.log(`   ❌ ${envVar}: Not set`);
      }
    });

    console.log('\n🎉 Diagnosis complete!');

  } catch (error) {
    console.error('❌ Fatal error during diagnosis:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the diagnosis
diagnoseProductionDebug();
