<template>
  <div class="tag-selector">
    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <n-spin size="small" />
      <span>{{ t('debug.tags.loading') }}</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-state">
      <n-alert type="error" :show-icon="false" class="error-alert">
        {{ error }}
        <template #action>
          <n-button size="tiny" @click="retry">{{ t('common.retry') }}</n-button>
        </template>
      </n-alert>
    </div>

    <!-- Main Interface -->
    <div v-else class="tag-interface">
      <!-- Compact Header -->
      <div class="header">
        <span class="title">{{ t('debug.tags.selectTags') }}</span>
        <div v-if="hasSelectedTags" class="header-actions">
          <n-badge :value="totalSelectedCount" />
          <n-button text size="tiny" type="error" @click="clearAllTags">
            {{ t('common.clearAll') }}
          </n-button>
        </div>
      </div>

      <!-- AI Suggestions -->
      <div v-if="showAiSuggestions" class="section">
        <div class="section-header">
          <n-icon :component="LightbulbIcon" size="16" />
          <span>{{ t('debug.tags.recommended') }}</span>
        </div>
        <div class="tag-list">
          <n-tag
            v-for="suggestion in aiSuggestions"
            :key="`ai-${suggestion.tagId}`"
            :type="isTagSelected(suggestion.tagId) ? 'success' : 'warning'"
            checkable
            :checked="isTagSelected(suggestion.tagId)"
            size="small"
            @update:checked="(checked) => toggleTag(suggestion.tagId, checked)"
          >
            {{ getTagDisplayName(suggestion.tagId) }}
          </n-tag>
        </div>
      </div>

      <!-- All Tags by Category -->
      <div v-if="allTagsToShow.length > 0" class="section">
        <div class="section-header">
          <n-icon :component="TagIcon" size="16" />
          <span>{{ t('debug.tags.browseAllTags') }}</span>
        </div>
        
        <div v-for="category in categoriesWithTags" :key="category.id" class="category-section">
          <div class="category-header">
            <div class="category-dot" :style="{ backgroundColor: category.color || '#6366f1' }"></div>
            <span class="category-name">{{ category.name }}</span>
            <span v-if="getSelectedInCategory(category.id) > 0" class="selected-count">
              ({{ getSelectedInCategory(category.id) }})
            </span>
          </div>
          <div class="tag-list">
            <n-tag
              v-for="tag in getTagsForCategory(category.id)"
              :key="`tag-${tag.id}`"
              :type="isTagSelected(tag.id) ? 'success' : 'default'"
              checkable
              :checked="isTagSelected(tag.id)"
              size="small"
              @update:checked="(checked) => toggleTag(tag.id, checked)"
            >
              <template v-if="tag.icon" #icon>
                <n-icon :component="getTagIcon(tag.icon)" size="12" />
              </template>
              {{ tag.name }}
            </n-tag>
          </div>
        </div>
      </div>

      <!-- Custom Tags -->
      <div v-if="allowCustomTags" class="section">
        <div class="section-header">
          <n-icon :component="UserIcon" size="16" />
          <span>{{ t('debug.tags.customTags') }}</span>
          <span class="counter">({{ customTags.length }}/{{ maxCustomTags }})</span>
        </div>
        <n-dynamic-tags
          v-model:value="customTags"
          :max="maxCustomTags"
          :placeholder="t('debug.tags.addCustomTag')"
          size="small"
          @create="onCreateCustomTag"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { 
  NInput, 
  NTag, 
  NIcon, 
  NSpin, 
  NButton, 
  NDynamicTags, 
  NBadge,
  NAlert
} from 'naive-ui';
import { 
  Search as SearchIcon,
  Bulb as LightbulbIcon,
  User as UserIcon,
  Bug as BugIcon,
  Bolt as ZapIcon,
  Palette as PaletteIcon,
  Check as CheckIcon,
  Tag as TagIcon,
  X as XIcon
} from '@vicons/tabler';
import { useTagStore } from '@/stores/tagStore';
import type { 
  TagWithRelations, 
  TagSuggestionResponse,
  TagCategory 
} from '@/types/api';

// Component Props
interface Props {
  reportType: string;
  modelValue: string[];
  allowCustomTags?: boolean;
  maxCustomTags?: number;
  showSearch?: boolean;
  enableAiSuggestions?: boolean;
  debugReportData?: {
    title: string;
    description: string;
    userAgent?: string;
    url?: string;
  };
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void;
  (e: 'tags-changed', tags: { id: string; name: string; isCustom: boolean }[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  allowCustomTags: true,
  maxCustomTags: 10,
  showSearch: true,
  enableAiSuggestions: true
});

const emit = defineEmits<Emits>();

// Composables
const { t } = useI18n();
const tagStore = useTagStore();

// Local State
const searchQuery = ref('');
const customTags = ref<string[]>([]);
const aiSuggestions = ref<TagSuggestionResponse['suggestions']>([]);
const isLoadingAi = ref(false);

// Computed Properties
const isLoading = computed(() => 
  tagStore.isLoadingTags || tagStore.isLoadingCategories
);

const error = computed(() => tagStore.error);

const selectedTags = computed({
  get: () => props.modelValue,
  set: (value: string[]) => emit('update:modelValue', value)
});

// New computed properties for redesigned UI
const hasSelectedTags = computed(() => 
  selectedTags.value.length > 0 || customTags.value.length > 0
);

const totalSelectedCount = computed(() => 
  selectedTags.value.length + customTags.value.length
);

const showAiSuggestions = computed(() => 
  props.enableAiSuggestions && aiSuggestions.value.length > 0
);

const visibleCategories = computed(() => 
  tagStore.categories.filter(category => 
    category.isActive && getCategoryTagCount(category.id) > 0
  )
);

const allTagsToShow = computed(() => tagStore.tags.filter(tag => 
  tag.isActive && tag.reportTypes.includes(props.reportType)
));

const categoriesWithTags = computed(() => 
  tagStore.categories.filter(category => 
    category.isActive && getTagsForCategory(category.id).length > 0
  )
);

const selectedTagsWithDetails = computed(() => {
  const regularTags = selectedTags.value.map(tagId => {
    const tag = getTagById(tagId);
    return tag ? {
      id: tagId,
      name: tag.name,
      isCustom: false
    } : null;
  }).filter((tag): tag is { id: string; name: string; isCustom: boolean } => tag !== null);

  const customTagsWithDetails = customTags.value.map(tagName => ({
    id: `custom-${tagName}`,
    name: tagName,
    isCustom: true
  }));

  return [...regularTags, ...customTagsWithDetails];
});

// Methods
function getCategoryTags(categoryId: string): TagWithRelations[] {
  const categoryTags = tagStore.getTagsByCategory(categoryId);
  const filteredTags = categoryTags.filter(tag => 
    tag.reportTypes.includes(props.reportType)
  );
  
  if (searchQuery.value) {
    return filteredTags.filter(tag =>
      tag.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }
  
  return filteredTags;
}

function getCategoryTagCount(categoryId: string): number {
  return getCategoryTags(categoryId).length;
}

function getSelectedCategoryTagCount(categoryId: string): number {
  const categoryTags = getCategoryTags(categoryId);
  return categoryTags.filter(tag => isTagSelected(tag.id)).length;
}

function isTagSelected(tagId: string): boolean {
  return selectedTags.value.includes(tagId);
}

function toggleTag(tagId: string, checked: boolean): void {
  const currentTags = [...selectedTags.value];
  
  if (checked && !currentTags.includes(tagId)) {
    currentTags.push(tagId);
  } else if (!checked) {
    const index = currentTags.indexOf(tagId);
    if (index > -1) {
      currentTags.splice(index, 1);
    }
  }
  
  selectedTags.value = currentTags;
  emitTagsChanged();
}

function removeTag(tagId: string): void {
  if (tagId.startsWith('custom-')) {
    const customTag = tagId.replace('custom-', '');
    customTags.value = customTags.value.filter(tag => tag !== customTag);
  } else {
    toggleTag(tagId, false);
  }
  emitTagsChanged();
}

function removeCustomTag(customTag: string): void {
  customTags.value = customTags.value.filter(tag => tag !== customTag);
  emitTagsChanged();
}

function clearAllTags(): void {
  selectedTags.value = [];
  customTags.value = [];
  emitTagsChanged();
}

function getTagsForCategory(categoryId: string): TagWithRelations[] {
  return allTagsToShow.value.filter(tag => tag.categoryId === categoryId);
}

function getSelectedInCategory(categoryId: string): number {
  return getTagsForCategory(categoryId).filter(tag => isTagSelected(tag.id)).length;
}

function getTagById(tagId: string): TagWithRelations | undefined {
  return tagStore.getTagById(tagId);
}

function getTagDisplayName(tagId: string): string {
  const tag = getTagById(tagId);
  return tag?.name || 'Unknown Tag';
}

function getTagIcon(iconName: string | null) {
  if (!iconName) return null;
  
  const icons: Record<string, any> = {
    'bug': BugIcon,
    'lightbulb': LightbulbIcon,
    'zap': ZapIcon,
    'palette': PaletteIcon
  };
  
  return icons[iconName] || null;
}

function onSearchInput(): void {
  // Debounced search could be implemented here if needed
}

function onCreateCustomTag(label: string): string {
  if (customTags.value.length >= props.maxCustomTags) {
    return '';
  }
  
  // Validate custom tag
  if (label.length < 2 || label.length > 50) {
    return '';
  }
  
  emitTagsChanged();
  return label;
}

function emitTagsChanged(): void {
  emit('tags-changed', selectedTagsWithDetails.value);
}

async function fetchAiSuggestions(): Promise<void> {
  if (!props.enableAiSuggestions || !props.debugReportData) {
    return;
  }
  
  // Check if AI suggestions are available
  if (!tagStore.isAiSuggestionsAvailable) {
    // Silently skip AI suggestions if not available
    return;
  }
  
  isLoadingAi.value = true;
  
  try {
    const response = await tagStore.getSuggestions({
      title: props.debugReportData.title,
      description: props.debugReportData.description,
      reportType: props.reportType,
      userAgent: props.debugReportData.userAgent,
      url: props.debugReportData.url
    });
    
    aiSuggestions.value = response.suggestions;
  } catch (err) {
    console.error('Failed to fetch AI suggestions:', err);
  } finally {
    isLoadingAi.value = false;
  }
}

async function retry(): Promise<void> {
  tagStore.clearError();
  await initialize();
}

async function initialize(): Promise<void> {
  try {
    // First try to load real data
    await tagStore.initialize();
    
    // If no tags were loaded, add mock data for testing
    if (tagStore.tags.length === 0) {
      console.log("No tags found in store, adding mock data for testing");
      addMockTagsForTesting();
    }
    
    if (props.enableAiSuggestions) {
      await fetchAiSuggestions();
      
      // If no AI suggestions were found, add mock suggestions
      if (aiSuggestions.value.length === 0 && props.enableAiSuggestions) {
        addMockAiSuggestionsForTesting();
      }
    }
  } catch (err) {
    console.error('Failed to initialize tag selector:', err);
    // If error, still add mock data so UI can be tested
    addMockTagsForTesting();
  }
}

// For development and testing purposes only
function addMockTagsForTesting() {
  // Add mock categories if none exist
  if (tagStore.categories.length === 0) {
    // These would typically come from the backend
    const mockCategories: TagCategory[] = [
      { 
        id: 'priority', 
        name: 'Priority', 
        description: 'Priority levels for issues',
        color: '#EF4444', 
        isActive: true, 
        order: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      { 
        id: 'technical', 
        name: 'Technical', 
        description: 'Technical issue categories',
        color: '#DC2626', 
        isActive: true, 
        order: 2,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      { 
        id: 'feature', 
        name: 'Feature', 
        description: 'Feature-related tags',
        color: '#3B82F6', 
        isActive: true, 
        order: 3,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      { 
        id: 'improvement', 
        name: 'Improvement', 
        description: 'Improvement suggestions',
        color: '#10B981', 
        isActive: true, 
        order: 4,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      { 
        id: 'performance', 
        name: 'Performance', 
        description: 'Performance-related issues',
        color: '#F59E0B', 
        isActive: true, 
        order: 5,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      { 
        id: 'uiux', 
        name: 'UI/UX', 
        description: 'User interface and experience',
        color: '#8B5CF6', 
        isActive: true, 
        order: 6,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      { 
        id: 'support', 
        name: 'Support', 
        description: 'Support and help requests',
        color: '#6B7280', 
        isActive: true, 
        order: 7,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      { 
        id: 'general', 
        name: 'General', 
        description: 'General purpose tags',
        color: '#6B7280', 
        isActive: true, 
        order: 8,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
    ];
    
    // Update the pinia store's categories ref directly
    tagStore.categories.push(...mockCategories);
  }
  
  // Add mock tags if none exist
  if (tagStore.tags.length === 0) {
    // Mock tags - these would typically come from the backend
    const currentDate = new Date().toISOString();
    const mockTags: TagWithRelations[] = [
      // Priority tags
      { 
        id: 'critical', 
        name: 'Critical', 
        description: 'Critical priority issue',
        color: null,
        icon: 'zap',
        categoryId: 'priority', 
        order: 1,
        isActive: true, 
        reportTypes: ['bug', 'feature'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'priority')!,
        usageCount: 15
      },
      { 
        id: 'high', 
        name: 'High', 
        description: 'High priority issue',
        color: null,
        icon: 'zap',
        categoryId: 'priority', 
        order: 2,
        isActive: true, 
        reportTypes: ['bug', 'feature'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'priority')!,
        usageCount: 12
      },
      { 
        id: 'medium', 
        name: 'Medium', 
        description: 'Medium priority issue',
        color: null,
        icon: null,
        categoryId: 'priority', 
        order: 3,
        isActive: true, 
        reportTypes: ['bug', 'feature'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'priority')!,
        usageCount: 8
      },
      { 
        id: 'low', 
        name: 'Low', 
        description: 'Low priority issue',
        color: null,
        icon: null,
        categoryId: 'priority', 
        order: 4,
        isActive: true, 
        reportTypes: ['bug', 'feature'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'priority')!,
        usageCount: 5
      },
      
      // Technical tags
      { 
        id: 'backend', 
        name: 'Backend', 
        description: 'Backend-related issue',
        color: null,
        icon: null,
        categoryId: 'technical', 
        order: 1,
        isActive: true, 
        reportTypes: ['bug', 'feature'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'technical')!,
        usageCount: 10
      },
      { 
        id: 'frontend', 
        name: 'Frontend', 
        description: 'Frontend-related issue',
        color: null,
        icon: null,
        categoryId: 'technical', 
        order: 2,
        isActive: true, 
        reportTypes: ['bug', 'feature'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'technical')!,
        usageCount: 14
      },
      { 
        id: 'database', 
        name: 'Database', 
        description: 'Database-related issue',
        color: null,
        icon: null,
        categoryId: 'technical', 
        order: 3,
        isActive: true, 
        reportTypes: ['bug', 'feature'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'technical')!,
        usageCount: 6
      },
      
      // Feature tags
      { 
        id: 'new-feature', 
        name: 'New Feature', 
        description: 'Request for new functionality',
        color: null,
        icon: null,
        categoryId: 'feature', 
        order: 1,
        isActive: true, 
        reportTypes: ['feature'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'feature')!,
        usageCount: 7
      },
      { 
        id: 'enhancement', 
        name: 'Enhancement', 
        description: 'Enhancement to existing feature',
        color: null,
        icon: null,
        categoryId: 'feature', 
        order: 2,
        isActive: true, 
        reportTypes: ['feature', 'improvement'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'feature')!,
        usageCount: 9
      },
      
      // UI/UX tags
      { 
        id: 'ui-design', 
        name: 'UI Design', 
        description: 'User interface design issue',
        color: null,
        icon: 'palette',
        categoryId: 'uiux', 
        order: 1,
        isActive: true, 
        reportTypes: ['bug', 'feature', 'improvement'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'uiux')!,
        usageCount: 11
      },
      { 
        id: 'responsive', 
        name: 'Responsive', 
        description: 'Responsive design issue',
        color: null,
        icon: null,
        categoryId: 'uiux', 
        order: 2,
        isActive: true, 
        reportTypes: ['bug', 'feature', 'improvement'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'uiux')!,
        usageCount: 8
      },
      { 
        id: 'accessibility', 
        name: 'Accessibility', 
        description: 'Accessibility improvement',
        color: null,
        icon: null,
        categoryId: 'uiux', 
        order: 3,
        isActive: true, 
        reportTypes: ['bug', 'feature', 'improvement'],
        createdAt: currentDate,
        updatedAt: currentDate,
        category: tagStore.categories.find(cat => cat.id === 'uiux')!,
        usageCount: 4
      },
    ];
    
    // Update the pinia store's tags ref directly
    tagStore.tags.push(...mockTags);
  }
}

function addMockAiSuggestionsForTesting() {
  // If no AI suggestions, add some mock ones
  if (aiSuggestions.value.length === 0) {
    aiSuggestions.value = [
      { 
        tagId: 'ui-design',
        tagName: 'UI Design',
        confidence: 0.89,
        reason: 'Based on issue description mentioning visual elements'
      },
      {
        tagId: 'frontend', 
        tagName: 'Frontend',
        confidence: 0.75,
        reason: 'Issue appears related to user interface components'
      }
    ];
  }
}

// Watchers
watch(() => props.reportType, async () => {
  await fetchAiSuggestions();
});

watch(() => props.debugReportData, async () => {
  await fetchAiSuggestions();
}, { deep: true });

watch(customTags, () => {
  emitTagsChanged();
}, { deep: true });

// Lifecycle
onMounted(async () => {
  await initialize();
});
</script>

<style scoped>
/* Main Container */
.tag-selector {
  max-width: 100%;
}

/* Loading and Error States */
.loading-state {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  font-size: 14px;
  color: var(--n-text-color-2);
}

.error-state {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: var(--n-error-color-suppl);
  border-radius: 6px;
  border: 1px solid var(--n-error-color);
  font-size: 14px;
}

.error-icon {
  color: var(--n-error-color);
}

.error-message {
  color: var(--n-error-color);
  flex: 1;
}

/* Main Container */
.tag-selector {
  max-width: 100%;
}

/* Loading and Error States */
.loading-state {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  font-size: 13px;
  color: var(--n-text-color-2);
}

.error-alert {
  margin: 0;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 0 2px;
}

.title {
  font-size: 14px;
  font-weight: 600;
  color: var(--n-text-color-1);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Sections */
.section {
  margin-bottom: 16px;
}

.section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 500;
  color: var(--n-text-color-2);
}

.counter {
  font-size: 12px;
  color: var(--n-text-color-3);
  font-weight: 400;
}

/* Categories */
.category-section {
  margin-bottom: 12px;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  font-size: 12px;
  color: var(--n-text-color-2);
}

.category-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.category-name {
  font-weight: 500;
}

.selected-count {
  font-size: 11px;
  color: var(--n-text-color-3);
}

/* Tag Lists */
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-left: 14px; /* Align with category header */
}

/* Responsive */
@media (max-width: 640px) {
  .tag-list {
    margin-left: 0;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
