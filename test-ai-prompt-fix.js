/**
 * Quick test to verify AI prompt is correctly structured for bug report generation
 * Tests that the prompt emphasizes bug report as main task with quality check as prerequisite
 */

const fs = require('fs');
const path = require('path');

// Read the AI service file
const aiServicePath = path.join(__dirname, 'backend', 'src', 'services', 'aiService.ts');
const aiServiceContent = fs.readFileSync(aiServicePath, 'utf8');

// Extract the prompt building function
const promptMatch = aiServiceContent.match(/buildDirectAudioAnalysisPrompt[\s\S]*?return `([\s\S]*?)`;/);

if (!promptMatch) {
  console.error('❌ Could not find prompt in AI service');
  process.exit(1);
}

const prompt = promptMatch[1];

console.log('🧪 Testing AI Prompt Structure...\n');

// Test 1: Check that bug report is presented as primary task
const hasPrimaryTask = prompt.includes('YOUR PRIMARY TASK: Generate a comprehensive bug report');
console.log(`✅ Primary task clearly stated: ${hasPrimaryTask ? 'PASS' : 'FAIL'}`);

// Test 2: Check that quality check is presented as prerequisite, not main task
const hasPrerequisite = prompt.includes('PREREQUISITE AUDIO QUALITY CHECK:');
const hasMainWorkflow = prompt.includes('MAIN ANALYSIS WORKFLOW');
console.log(`✅ Quality check as prerequisite: ${hasPrerequisite ? 'PASS' : 'FAIL'}`);
console.log(`✅ Main workflow emphasized: ${hasMainWorkflow ? 'PASS' : 'FAIL'}`);

// Test 3: Check that failure conditions are clearly limited
const hasOnlyStop = prompt.includes('ONLY STOP and return failure if audio has critical issues');
const hasProceedGuidance = prompt.includes('PROCEED with bug report analysis if audio is');
console.log(`✅ Limited failure conditions: ${hasOnlyStop ? 'PASS' : 'FAIL'}`);
console.log(`✅ Clear proceed guidance: ${hasProceedGuidance ? 'PASS' : 'FAIL'}`);

// Test 4: Check error categories are maintained
const hasAllErrorCategories = [
  'POOR_QUALITY_OR_SHORT',
  'TOO_NOISY', 
  'IRRELEVANT_CONTENT'
].every(category => prompt.includes(category));
console.log(`✅ All error categories present: ${hasAllErrorCategories ? 'PASS' : 'FAIL'}`);

// Test 5: Check success response is emphasized as primary outcome
const hasSuccessEmphasis = prompt.includes('SUCCESS RESPONSE FORMAT (primary expected outcome)');
console.log(`✅ Success response emphasized: ${hasSuccessEmphasis ? 'PASS' : 'FAIL'}`);

// Test 6: Check comprehensive analysis instructions
const hasComprehensiveInstructions = prompt.includes('COMPREHENSIVE BUG REPORT GENERATION INSTRUCTIONS');
console.log(`✅ Comprehensive analysis instructions: ${hasComprehensiveInstructions ? 'PASS' : 'FAIL'}`);

console.log('\n📊 SUMMARY:');
const tests = [hasPrimaryTask, hasPrerequisite, hasMainWorkflow, hasOnlyStop, hasProceedGuidance, hasAllErrorCategories, hasSuccessEmphasis, hasComprehensiveInstructions];
const passCount = tests.filter(Boolean).length;
const totalCount = tests.length;

if (passCount === totalCount) {
  console.log(`🎉 ALL TESTS PASSED (${passCount}/${totalCount})`);
  console.log('✅ AI prompt correctly emphasizes bug report generation as primary task');
  console.log('✅ Quality check is positioned as prerequisite, not main focus');
  console.log('✅ Error handling maintains required 3 categories');
} else {
  console.log(`⚠️  SOME TESTS FAILED (${passCount}/${totalCount})`);
  process.exit(1);
}
