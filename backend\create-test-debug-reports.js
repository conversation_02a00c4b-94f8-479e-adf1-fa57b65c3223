const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestDebugReports() {
  try {
    // Get the test user
    const user = await prisma.user.findFirst();
    if (!user) {
      console.log('No user found');
      return;
    }

    console.log('Creating test debug reports...');

    // Create a few test debug reports
    const reports = await Promise.all([
      prisma.debugReport.create({
        data: {
          reportId: 'DBG-test-001',
          userId: user.id,
          type: 'BUG',
          severity: 'HIGH',
          status: 'NOT_REVIEWED',
          priority: 4,
          title: 'Debug reports not loading',
          description: 'When accessing the debug dashboard, reports fail to load with 500 error',
          stepsToReproduce: '1. Login as admin\n2. Go to debug dashboard\n3. Try to view reports',
          expectedBehavior: 'Reports should load successfully',
          actualBehavior: 'Gets 500 Internal Server Error',
          currentUrl: 'https://arzani.husotech.com/debug-dashboard',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          viewportWidth: 1920,
          viewportHeight: 1080,
          sessionId: 'session-123',
          clientTimestamp: new Date(),
          diagnosticData: {
            component: 'DebugDashboard',
            error: 'Request failed with status code 500'
          },
          logs: [
            { level: 'error', message: 'Failed to fetch reports', timestamp: new Date() }
          ],
          userActions: [
            { action: 'click', element: 'debug-reports-tab', timestamp: new Date() }
          ]
        }
      }),
      prisma.debugReport.create({
        data: {
          reportId: 'DBG-test-002', 
          userId: user.id,
          type: 'FEATURE_REQUEST',
          severity: 'MEDIUM',
          status: 'NOT_REVIEWED',
          priority: 3,
          title: 'Add filtering options to debug dashboard',
          description: 'Need ability to filter debug reports by status, type, and date range',
          expectedBehavior: 'Filter controls should be available',
          actualBehavior: 'No filtering options currently exist',
          currentUrl: 'https://arzani.husotech.com/debug-dashboard',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          viewportWidth: 1920,
          viewportHeight: 1080,
          sessionId: 'session-124',
          clientTimestamp: new Date()
        }
      }),
      prisma.debugReport.create({
        data: {
          reportId: 'DBG-test-003',
          userId: user.id,
          type: 'UI_UX',
          severity: 'LOW',
          status: 'IN_PROGRESS',
          priority: 2,
          title: 'Notification bell animation is distracting',
          description: 'The notification bell bounces too aggressively when new notifications arrive',
          stepsToReproduce: '1. Receive a notification\n2. Observe the bell animation',
          expectedBehavior: 'Subtle animation that draws attention without being jarring',
          actualBehavior: 'Aggressive bouncing animation that is distracting',
          assignedToId: user.id,
          assignedAt: new Date(),
          currentUrl: 'https://arzani.husotech.com/browse-offers',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          viewportWidth: 1366,
          viewportHeight: 768,
          sessionId: 'session-125',
          clientTimestamp: new Date()
        }
      })
    ]);

    console.log(`Created ${reports.length} test debug reports:`);
    reports.forEach(report => {
      console.log(`- ${report.reportId}: ${report.title} (${report.type}/${report.severity})`);
    });

  } catch (error) {
    console.error('Error creating test debug reports:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestDebugReports();
