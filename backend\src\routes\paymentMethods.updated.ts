import { Hono } from 'hono';
import { authMiddleware, type AuthVariables } from '../middleware/auth';
import { PaymentMethodService, CreatePaymentMethodPayload, UpdatePaymentMethodPayload } from '../services/paymentMethodService';
import { PrismaClient } from '@prisma/client';
import { ConsoleLogger } from '../utils/logger';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

// Validation schemas
const CreatePaymentMethodSchema = z.object({
  currency: z.string().min(3).max(3).transform(s => s.toUpperCase()),
  paymentMethodType: z.enum(['BANK_TRANSFER', 'DIGITAL_WALLET', 'CRYPTO_WALLET', 'MOBILE_MONEY', 'CASH_PICKUP']),
  bankName: z.string().min(1).max(100),
  accountNumber: z.string().min(1).max(50),
  accountHolderName: z.string().min(1).max(100),
  swiftCode: z.string().max(11).optional(),
  iban: z.string().max(34).optional(),
  routingNumber: z.string().max(20).optional(),
  sortCode: z.string().max(8).optional(),
  bsb: z.string().max(6).optional(),
  notes: z.string().max(500).optional()
});

const UpdatePaymentMethodSchema = CreatePaymentMethodSchema.partial().extend({
  isActive: z.boolean().optional()
});

const CurrencyParamSchema = z.object({
  currency: z.string().min(3).max(3).transform(s => s.toUpperCase())
});

export function createPaymentMethodRoutes(prisma: PrismaClient) {
  const router = new Hono<{ Variables: AuthVariables }>();
  const logger = new ConsoleLogger();
  const paymentMethodService = new PaymentMethodService(prisma, logger);

  // Get all payment methods for the authenticated user
  router.get('/', authMiddleware, async (c) => {
    try {
      const jwtPayload = c.get('jwtPayload');
      if (!jwtPayload?.userId) {
        return c.json({ error: 'Authentication required' }, 401);
      }

      const userId = jwtPayload.userId;
      const paymentMethods = await paymentMethodService.getUserPaymentMethods(userId);

      return c.json({
        success: true,
        data: paymentMethods,
        count: paymentMethods.length
      });
    } catch (error) {
      logger.error('Failed to get payment methods', { error });
      return c.json({ error: 'Failed to retrieve payment methods' }, 500);
    }
  });

  // Get payment methods by currency
  router.get('/currency/:currency', authMiddleware, zValidator('param', CurrencyParamSchema), async (c) => {
    try {
      const jwtPayload = c.get('jwtPayload');
      if (!jwtPayload?.userId) {
        return c.json({ error: 'Authentication required' }, 401);
      }

      const userId = jwtPayload.userId;
      const { currency } = c.req.valid('param');
      
      const paymentMethods = await paymentMethodService.getUserPaymentMethodsByCurrency(userId, currency);

      return c.json({
        success: true,
        data: paymentMethods,
        currency,
        count: paymentMethods.length
      });
    } catch (error) {
      logger.error('Failed to get payment methods by currency', { error });
      return c.json({ error: 'Failed to retrieve payment methods for currency' }, 500);
    }
  });

  // Get default payment method for a currency
  router.get('/currency/:currency/default', authMiddleware, zValidator('param', CurrencyParamSchema), async (c) => {
    try {
      const jwtPayload = c.get('jwtPayload');
      if (!jwtPayload?.userId) {
        return c.json({ error: 'Authentication required' }, 401);
      }

      const userId = jwtPayload.userId;
      const { currency } = c.req.valid('param');
      
      const defaultMethod = await paymentMethodService.getDefaultPaymentMethod(userId, currency);

      if (!defaultMethod) {
        return c.json({ 
          success: true, 
          data: null, 
          message: `No default payment method found for ${currency}` 
        });
      }

      return c.json({
        success: true,
        data: defaultMethod
      });
    } catch (error) {
      logger.error('Failed to get default payment method', { error });
      return c.json({ error: 'Failed to retrieve default payment method' }, 500);
    }
  });

  // Create a new payment method
  router.post('/', authMiddleware, zValidator('json', CreatePaymentMethodSchema), async (c) => {
    try {
      const jwtPayload = c.get('jwtPayload');
      if (!jwtPayload?.userId) {
        return c.json({ error: 'Authentication required' }, 401);
      }

      const userId = jwtPayload.userId;
      const payload = c.req.valid('json');

      // Type assertion after validation
      const validPayload: CreatePaymentMethodPayload = {
        currency: payload.currency,
        paymentMethodType: payload.paymentMethodType,
        bankName: payload.bankName,
        accountNumber: payload.accountNumber,
        accountHolderName: payload.accountHolderName,
        swiftCode: payload.swiftCode,
        iban: payload.iban,
        routingNumber: payload.routingNumber,
        sortCode: payload.sortCode,
        bsb: payload.bsb,
        notes: payload.notes
      };

      const paymentMethod = await paymentMethodService.createPaymentMethod(userId, validPayload);

      return c.json({
        success: true,
        data: paymentMethod,
        message: 'Payment method created successfully'
      }, 201);
    } catch (error) {
      logger.error('Failed to create payment method', { error });
      return c.json({ error: 'Failed to create payment method' }, 500);
    }
  });

  // Update an existing payment method
  router.put('/:id', authMiddleware, zValidator('json', UpdatePaymentMethodSchema), async (c) => {
    try {
      const jwtPayload = c.get('jwtPayload');
      if (!jwtPayload?.userId) {
        return c.json({ error: 'Authentication required' }, 401);
      }

      const userId = jwtPayload.userId;
      const paymentMethodId = c.req.param('id');
      const payload = c.req.valid('json');

      if (!paymentMethodId) {
        return c.json({ error: 'Payment method ID is required' }, 400);
      }

      const paymentMethod = await paymentMethodService.updatePaymentMethod(paymentMethodId, userId, payload);

      return c.json({
        success: true,
        data: paymentMethod,
        message: 'Payment method updated successfully'
      });
    } catch (error) {
      logger.error('Failed to update payment method', { error });
      return c.json({ error: 'Failed to update payment method' }, 500);
    }
  });

  // Set a payment method as default
  router.patch('/:id/set-default', authMiddleware, async (c) => {
    try {
      const jwtPayload = c.get('jwtPayload');
      if (!jwtPayload?.userId) {
        return c.json({ error: 'Authentication required' }, 401);
      }

      const userId = jwtPayload.userId;
      const paymentMethodId = c.req.param('id');

      if (!paymentMethodId) {
        return c.json({ error: 'Payment method ID is required' }, 400);
      }

      const paymentMethod = await paymentMethodService.setAsDefault(paymentMethodId, userId);

      return c.json({
        success: true,
        data: paymentMethod,
        message: 'Payment method set as default successfully'
      });
    } catch (error) {
      logger.error('Failed to set payment method as default', { error });
      return c.json({ error: 'Failed to set payment method as default' }, 500);
    }
  });

  // Deactivate a payment method (soft delete)
  router.delete('/:id', authMiddleware, async (c) => {
    try {
      const jwtPayload = c.get('jwtPayload');
      if (!jwtPayload?.userId) {
        return c.json({ error: 'Authentication required' }, 401);
      }

      const userId = jwtPayload.userId;
      const paymentMethodId = c.req.param('id');

      if (!paymentMethodId) {
        return c.json({ error: 'Payment method ID is required' }, 400);
      }

      await paymentMethodService.deactivatePaymentMethod(paymentMethodId, userId);

      return c.json({
        success: true,
        message: 'Payment method deactivated successfully'
      });
    } catch (error) {
      logger.error('Failed to deactivate payment method', { error });
      return c.json({ error: 'Failed to deactivate payment method' }, 500);
    }
  });

  return router;
}

export default createPaymentMethodRoutes;
