import confetti from 'canvas-confetti';

/**
 * Celebrates transaction completion with confetti animation
 */
export function celebrateTransactionCompletion(): void {
  try {
    // Create multiple confetti bursts for a grand celebration
    const duration = 3000; // 3 seconds
    const animationEnd = Date.now() + duration;
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min;
    }

    // Burst 1: Center explosion with colors representing currencies
    confetti({
      ...defaults,
      particleCount: 50,
      origin: { x: 0.5, y: 0.6 },
      colors: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6'], // Green, blue, amber, red, purple
    });

    // Burst 2: Left side with gold colors
    setTimeout(() => {
      confetti({
        ...defaults,
        particleCount: 30,
        origin: { x: 0.2, y: 0.7 },
        colors: ['#fbbf24', '#f59e0b', '#d97706'], // Gold tones
      });
    }, 200);

    // Burst 3: Right side with celebration colors
    setTimeout(() => {
      confetti({
        ...defaults,
        particleCount: 30,
        origin: { x: 0.8, y: 0.7 },
        colors: ['#10b981', '#06d6a0', '#059669'], // Success greens
      });
    }, 400);

    // Continuous random confetti for the duration
    const interval = setInterval(() => {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        clearInterval(interval);
        return;
      }

      const particleCount = 20 * (timeLeft / duration);
      
      confetti({
        ...defaults,
        particleCount: Math.max(particleCount, 10),
        origin: { x: randomInRange(0.1, 0.9), y: Math.random() - 0.2 },
        colors: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6'],
      });
    }, 250);

    console.log('[Confetti] Transaction completion celebration triggered! 🎉');
  } catch (error) {
    console.error('[Confetti] Error triggering confetti effect:', error);
  }
}

/**
 * Simple confetti burst for general celebrations
 */
export function simpleCelebration(): void {
  try {
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 },
      colors: ['#10b981', '#3b82f6', '#f59e0b'],
    });
  } catch (error) {
    console.error('[Confetti] Error triggering simple confetti effect:', error);
  }
}

/**
 * Side cannons confetti effect
 */
export function sideCannonsCelebration(): void {
  try {
    const end = Date.now() + 2000; // 2 seconds

    const interval = setInterval(() => {
      if (Date.now() > end) {
        clearInterval(interval);
        return;
      }

      // Left cannon
      confetti({
        particleCount: 10,
        angle: 60,
        spread: 55,
        origin: { x: 0, y: 0.8 },
        colors: ['#10b981', '#06d6a0', '#059669'],
      });

      // Right cannon
      confetti({
        particleCount: 10,
        angle: 120,
        spread: 55,
        origin: { x: 1, y: 0.8 },
        colors: ['#3b82f6', '#1d4ed8', '#1e40af'],
      });
    }, 150);
  } catch (error) {
    console.error('[Confetti] Error triggering side cannons confetti effect:', error);
  }
}
