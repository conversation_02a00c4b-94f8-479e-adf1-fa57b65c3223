// Script to update missing createdAt fields for existing users
import { PrismaClient } from '@prisma/client';

async function main() {
  const prisma = new PrismaClient();
  
  try {    // Find all users with very old or missing createdAt (before a reasonable date)
    const cutoffDate = new Date('2024-01-01'); // Adjust this date as needed
    const usersWithoutCreatedAt = await prisma.user.findMany({
      where: {
        createdAt: {
          lt: cutoffDate
        }
      }
    });
    
    console.log(`Found ${usersWithoutCreatedAt.length} users with missing createdAt timestamps.`);
    
    // Update each user with the current date
    // In a production environment, you might want to set a more meaningful date
    // For example, if you have other data points like first login time
    if (usersWithoutCreatedAt.length > 0) {
      for (const user of usersWithoutCreatedAt) {
        await prisma.user.update({
          where: { id: user.id },
          data: { 
            createdAt: new Date()
          }
        });
        console.log(`Updated user ${user.id} (${user.email}) with current timestamp.`);
      }
      
      console.log('All users updated successfully.');
    } else {
      console.log('No users need updating.');
    }
  } catch (error) {
    console.error('Error updating users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('Script completed.'))
  .catch(e => {
    console.error('Script failed:', e);
    process.exit(1);
  });
