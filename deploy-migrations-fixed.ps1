#!/usr/bin/env pwsh

# Deploy Missing Migrations to Production
# This script copies migration files from local development to production backend container
# and applies them to update the database schema

Write-Host "🔄 Starting migration deployment to production..." -ForegroundColor Green

# Check if migration directory exists locally
$localMigrationPath = ".\backend\prisma\migrations\20241204120000_complete_schema_with_debug_reports"
if (-Not (Test-Path $localMigrationPath)) {
    Write-Host "❌ Local migration directory not found: $localMigrationPath" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Found local migration directory" -ForegroundColor Green

# Check if production containers are running
Write-Host "🔍 Checking production containers..." -ForegroundColor Yellow

try {
    $containers = docker-compose ps backend --format "{{.Service}}" 2>$null
    if (-not $containers) {
        Write-Host "❌ Production backend container is not running" -ForegroundColor Red
        Write-Host "💡 Start production environment first with: docker-compose up -d" -ForegroundColor Yellow
        exit 1
    }
    Write-Host "✅ Production backend container is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Error checking container status: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Get backend container ID
Write-Host "🆔 Getting backend container ID..." -ForegroundColor Yellow
try {
    $backendContainerId = $(docker-compose ps -q backend).Trim()
    if (-not $backendContainerId) {
        Write-Host "❌ Could not get backend container ID" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Backend container ID: $backendContainerId" -ForegroundColor Green
} catch {
    Write-Host "❌ Error getting container ID: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create migrations directory in container if it doesn't exist
Write-Host "📁 Creating migrations directory in production container..." -ForegroundColor Yellow
docker-compose exec backend mkdir -p /app/prisma/migrations

# Copy migration directory to production container
Write-Host "📋 Copying migration files to production container..." -ForegroundColor Yellow
docker cp "$localMigrationPath" "${backendContainerId}:/app/prisma/migrations/"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to copy migration files" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Migration files copied successfully" -ForegroundColor Green

# Verify migration files were copied correctly
Write-Host "🔍 Verifying migration files in container..." -ForegroundColor Yellow
docker-compose exec backend ls -la /app/prisma/migrations/

# Apply migrations in production
Write-Host "🚀 Applying migrations to production database..." -ForegroundColor Yellow
Write-Host "⏳ This may take a few moments..." -ForegroundColor Yellow

docker-compose exec backend npx prisma migrate deploy

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Migration deployment failed" -ForegroundColor Red
    Write-Host "💡 Check the output above for error details" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Migrations applied successfully!" -ForegroundColor Green

# Generate Prisma client to ensure it's up to date
Write-Host "🔄 Regenerating Prisma client..." -ForegroundColor Yellow
docker-compose exec backend npx prisma generate

# Test the debug reports API
Write-Host "🧪 Testing debug reports API..." -ForegroundColor Yellow
docker-compose exec backend node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testDebugReports() {
    try {
        // Check if debug_reports table exists and has correct schema
        const tableInfo = await prisma.\$queryRaw\`
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'debug_reports'
            ORDER BY ordinal_position;
        \`;
        
        console.log('✅ debug_reports table schema:');
        console.table(tableInfo);
        
        // Try to fetch debug reports
        const reports = await prisma.debugReport.findMany({
            take: 5,
            include: { user: { select: { email: true, firstName: true, lastName: true } } }
        });
        
        console.log(\`✅ Successfully fetched \${reports.length} debug reports\`);
        
        await prisma.\$disconnect();
        console.log('✅ Database connection test passed');
    } catch (error) {
        console.error('❌ Database test failed:', error.message);
        process.exit(1);
    }
}

testDebugReports();
"

Write-Host "🎉 Migration deployment completed successfully!" -ForegroundColor Green
Write-Host "💡 Debug Dashboard should now work correctly in production" -ForegroundColor Yellow

# Show final status
Write-Host "`n📊 Production Environment Status:" -ForegroundColor Cyan
Write-Host "✅ Migration files copied to container" -ForegroundColor Green
Write-Host "✅ Database schema updated" -ForegroundColor Green
Write-Host "✅ Prisma client regenerated" -ForegroundColor Green
Write-Host "✅ Debug reports API tested" -ForegroundColor Green

Write-Host "`n🔗 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Access Debug Dashboard at: http://your-domain/admin/debug-dashboard" -ForegroundColor White
Write-Host "2. Verify admin user can view debug reports" -ForegroundColor White
Write-Host "3. Test debug report submission and viewing" -ForegroundColor White

Write-Host "`n⚠️  Note: If you encounter any issues, check container logs with:" -ForegroundColor Yellow
Write-Host "   docker-compose logs backend" -ForegroundColor Gray
