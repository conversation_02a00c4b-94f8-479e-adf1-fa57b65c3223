<template>
  <div class="landing-page" :class="{ 'rtl': isRTL, 'ltr': !isRTL }" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">          <div class="logo">
            <img 
              :src="isDark ? '/logo-dark.webp' : '/logo-light.webp'" 
              :alt="t('landing.heroTitle')"
              class="logo-image"
            />
            <div style="display: none;">Current theme: {{ isDark ? 'dark' : 'light' }}</div>
          </div>
          
          <nav class="nav">
            <a href="#features" class="nav-link">{{ t('landing.features') }}</a>
            <a href="#how-it-works" class="nav-link">{{ t('landing.howItWorks') }}</a>
            <a href="#testimonials" class="nav-link">{{ t('landing.testimonials') }}</a>
          </nav>
            <div class="header-actions">
            <ThemeToggle class="theme-toggle" />
            <LanguageSelector class="language-selector" />
            <router-link to="/login">
              <n-button quaternary class="signin-btn">{{ t('auth.signIn') }}</n-button>
            </router-link>
            <router-link to="/register">
              <n-button type="primary" class="signup-btn">{{ t('landing.joinNow') }}</n-button>
            </router-link>
          </div>
        </div>
      </div>
    </header>    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="floating-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
        </div>
      </div>
      
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">            <h1 class="hero-title">
              {{ t('landing.heroExchange') }}
              <span class="gradient-text">{{ t('landing.heroSmarter') }}</span>
            </h1>
            <p class="hero-subtitle">
              {{ t('landing.heroSubtitle') }}
            </p>
            <div class="hero-buttons">
              <router-link to="/register">                <n-button type="primary" size="large" class="cta-button">
                  {{ t('landing.startExchanging') }}
                  <n-icon size="20">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12,4L14.59,6.59L16,5.17L12,1.17L8,5.17L9.41,6.59L12,4M4,12L6.59,9.41L5.17,8L1.17,12L5.17,16L6.59,14.59L4,12M20,12L17.41,14.59L18.83,16L22.83,12L18.83,8L17.41,9.41L20,12M12,20L9.41,17.41L8,18.83L12,22.83L16,18.83L14.59,17.41L12,20Z"/>
                    </svg>
                  </n-icon>
                </n-button>
              </router-link>
              <router-link to="/login">                <n-button quaternary size="large" class="learn-more-btn">
                  {{ t('landing.learnMore') }}
                  <n-icon size="16">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                    </svg>
                  </n-icon>
                </n-button>
              </router-link>
            </div>
            
            <!-- Stats -->
            <div class="stats">
              <div class="stat">
                <div class="stat-value">2.5%</div>
                <div class="stat-label">{{ t('landing.lowerFees') }}</div>
              </div>
              <div class="stat">
                <div class="stat-value">500+</div>
                <div class="stat-label">{{ t('landing.activeUsers') }}</div>
              </div>
              <div class="stat">
                <div class="stat-value">99.8%</div>
                <div class="stat-label">{{ t('landing.successRate') }}</div>
              </div>
            </div>
          </div>
          
          <div class="hero-visual">
            <div class="exchange-widget">
              <div class="widget-header">                <h3 class="widget-title">{{ t('landing.quickExchange') }}</h3>
                <span class="live-rates">
                  <span class="live-indicator"></span>
                  {{ t('landing.liveRates') }}
                </span>
              </div>
              
              <div class="currency-exchange">
                <div class="currency-input">
                  <div class="currency-info">
                    <div class="currency-flag">$</div>
                    <span class="currency-code">{{ t('landing.internationalCurrency') }}</span>
                  </div>
                  <span class="amount">1,000</span>
                </div>
                
                <div class="exchange-icon">
                  <n-icon size="24" class="exchange-arrow">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M17,12L12,17L7,12M12,5V16.5"/>
                    </svg>
                  </n-icon>
                </div>
                
                <div class="currency-input">
                  <div class="currency-info">
                    <div class="currency-flag">﷼</div>
                    <span class="currency-code">{{ t('landing.iranianRial') }}</span>
                  </div>
                  <span class="amount">32,500,000</span>
                </div>
              </div>
              
              <n-button type="primary" block class="find-partner-btn">
                {{ t('landing.findExchangePartner') }}
                <n-icon size="16">
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z"/>
                  </svg>
                </n-icon>
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </section><!-- Features Section -->
    <section id="features" class="features">
      <div class="container">
        <div class="section-header">          <h2 class="section-title">{{ t('landing.whyChoose') }}</h2>
          <p class="section-subtitle">
            {{ t('landing.featuresSubtitle') }}
          </p>
        </div>
        
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <n-icon size="48">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M21,11C21,16.55 17.16,21.74 12,23C6.84,21.74 3,16.55 3,11V5L12,1L21,5V11M12,21C15.75,20 19,15.54 19,11.22V6.3L12,3.18L5,6.3V11.22C5,15.54 8.25,20 12,21M12,6A3,3 0 0,1 15,9C15,10.31 14.17,11.42 13,11.83V14H11V11.83C9.83,11.42 9,10.31 9,9A3,3 0 0,1 12,6M12,8A1,1 0 0,0 11,9A1,1 0 0,0 12,10A1,1 0 0,0 13,9A1,1 0 0,0 12,8Z"/>
                </svg>
              </n-icon>
            </div>            <h3>{{ t('landing.secureTitle') }}</h3>
            <p>{{ t('landing.secureDesc') }}</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <n-icon size="48">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M6 16.5L3 19.44V11H6M11 14.66L9.43 13.32L8 14.64V7H11M16 13L13 16V3H16M18.81 12.81L17 11H22V16L20.21 14.21L13 21.36L9.53 18.34L5.75 22H3L9.47 15.66L13 18.64"/>
                </svg>
              </n-icon>
            </div>            <h3>{{ t('landing.betterRatesTitle') }}</h3>
            <p>{{ t('landing.betterRatesDesc') }}</p>
          </div>
            <div class="feature-card">
            <div class="feature-icon">
              <n-icon size="48">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12C22,10.84 21.79,9.69 21.39,8.61L19.79,10.21C19.93,10.8 20,11.4 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.6,4 13.2,4.07 13.79,4.21L15.4,2.6C14.31,2.21 13.16,2 12,2M19,2L15,6V7.5L12.45,10.05C12.3,10 12.15,10 12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12C14,11.85 14,11.7 13.95,11.55L16.5,9H18L22,5H19V2M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12H16A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8V6Z"/>
                </svg>
              </n-icon>
            </div>            <h3>{{ t('landing.directConnectionTitle') }}</h3>
            <p>{{ t('landing.directConnectionDesc') }}</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <n-icon size="48">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M17.9,17.39C17.64,16.59 16.89,16 16,16H15V13A1,1 0 0,0 14,12H8V10H10A1,1 0 0,0 11,9V7H13A2,2 0 0,0 15,5V4.59C17.93,5.77 20,8.64 20,12C20,14.08 19.2,15.97 17.9,17.39M11,19.93C7.05,19.44 4,16.08 4,12C4,11.38 4.08,10.78 4.21,10.21L9,15V16A2,2 0 0,0 11,18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                </svg>
              </n-icon>
            </div>            <h3>{{ t('landing.globalNetworkTitle') }}</h3>
            <p>{{ t('landing.globalNetworkDesc') }}</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <n-icon size="48">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,3C7.58,3 4,4.79 4,7C4,9.21 7.58,11 12,11C16.42,11 20,9.21 20,7C20,4.79 16.42,3 12,3M4,9V12C4,14.21 7.58,16 12,16C16.42,16 20,14.21 20,12V9C20,11.21 16.42,13 12,13C7.58,13 4,11.21 4,9M4,14V17C4,19.21 7.58,21 12,21C16.42,21 20,19.21 20,17V14C20,16.21 16.42,18 12,18C7.58,18 4,16.21 4,14Z"/>
                </svg>
              </n-icon>
            </div>            <h3>{{ t('landing.smartMatchingTitle') }}</h3>
            <p>{{ t('landing.smartMatchingDesc') }}</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <n-icon size="48">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
                </svg>
              </n-icon>
            </div>            <h3>{{ t('landing.easyVerificationTitle') }}</h3>
            <p>{{ t('landing.easyVerificationDesc') }}</p></div>
        </div>
      </div>
    </section>    <!-- How It Works Section -->
    <section id="how-it-works" class="how-it-works" ref="howItWorksSectionRef">
      <div class="container">
        <div class="section-header">          <h2 class="section-title">{{ t('landing.howItWorks') }}</h2>
          <p class="section-subtitle">{{ t('landing.howItWorksSubtitle') }}</p>
        </div>
        
        <div class="steps">
          <div class="step">
            <div class="step-number">{{ isRTL ? '۱' : '1' }}</div>
            <div class="step-content">              <h3>{{ t('landing.step1Title') }}</h3>
              <p>{{ t('landing.step1Desc') }}</p>
            </div>
          </div>
          
          <div class="step">
            <div class="step-number">{{ isRTL ? '۲' : '2' }}</div>
            <div class="step-content">              <h3>{{ t('landing.step2Title') }}</h3>
              <p>{{ t('landing.step2Desc') }}</p>
            </div>
          </div>
          
          <div class="step">
            <div class="step-number">{{ isRTL ? '۳' : '3' }}</div>
            <div class="step-content">              <h3>{{ t('landing.step3Title') }}</h3>
              <p>{{ t('landing.step3Desc') }}</p>
            </div>
          </div>          <!-- Animated Arrows -->
          <svg 
            class="step-arrow arrow-1-to-2" 
            :class="{ 'show': showArrow1to2, 'animate': animateArrow1to2 }" 
            viewBox="0 0 100 20" 
            preserveAspectRatio="none"
            aria-hidden="true"
          >
            <defs>
              <marker id="arrowhead1" markerWidth="8" markerHeight="6" refX="0" refY="3" orient="auto" class="arrow-marker">
                <polygon points="0 0, 8 3, 0 6" class="arrow-marker-polygon" />
              </marker>
            </defs>
            <path d="M 10 10 L 90 10" class="arrow-path" marker-end="url(#arrowhead1)" />
          </svg>

          <svg 
            class="step-arrow arrow-2-to-3" 
            :class="{ 'show': showArrow2to3, 'animate': animateArrow2to3 }" 
            viewBox="0 0 100 20" 
            preserveAspectRatio="none"
            aria-hidden="true"
          >
            <defs>
              <marker id="arrowhead2" markerWidth="8" markerHeight="6" refX="0" refY="3" orient="auto" class="arrow-marker">
                 <polygon points="0 0, 8 3, 0 6" class="arrow-marker-polygon" />
              </marker>
            </defs>
            <path d="M 10 10 L 90 10" class="arrow-path" marker-end="url(#arrowhead2)" />
          </svg>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="testimonials">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">{{ t('landing.testimonials') }}</h2>
        </div>
          <div class="testimonials-grid">
          <div class="testimonial-card">
            <div class="testimonial-header">
              <div class="avatar">
                <span>{{ isRTL ? 'س' : 'S' }}</span>
              </div>
              <div class="user-info">                <h4>{{ t('landing.testimonialName1') }}</h4>
                <p>{{ t('landing.testimonialLocation1') }}</p>
              </div>
            </div>
            <p class="testimonial-text">{{ t('landing.testimonialQuote1') }}</p>
          </div>
          
          <div class="testimonial-card">
            <div class="testimonial-header">
              <div class="avatar">
                <span>{{ isRTL ? 'ع' : 'A' }}</span>
              </div>
              <div class="user-info">                <h4>{{ t('landing.testimonialName2') }}</h4>
                <p>{{ t('landing.testimonialLocation2') }}</p>
              </div>
            </div>
            <p class="testimonial-text">{{ t('landing.testimonialQuote2') }}</p>
          </div>
        </div>
      </div>
    </section>    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">          <h2>{{ t('landing.readyToStart') }}</h2>
          <p>{{ t('landing.ctaDescription') }}</p>
          <router-link to="/register">
            <n-button type="primary" size="large" class="cta-button-large">
              {{ t('landing.createAccount') }}
            </n-button>
          </router-link>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
 <span class="gradient-text">{{ t('landing.heroTitle') }}</span>

 <p>{{ t('landing.footerDescription') }}</p>          <div class="footer-text">
            <p>{{ t('landing.footerCopyright') }}</p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { NButton, NIcon } from 'naive-ui'
import { useTranslation } from '@/composables/useTranslation'
import LanguageSelector from '@/components/LanguageSelector.vue'
import ThemeToggle from '@/components/ThemeToggle.vue'
import { watch, ref } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { storeToRefs } from 'pinia'
import { useIntersectionObserver } from '@vueuse/core'

const { t, isRTL, currentLanguage } = useTranslation()
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore) // Make it reactive using storeToRefs

// Watch for language changes and update document direction
watch(currentLanguage, (newLang) => {
  document.documentElement.dir = newLang === 'fa' ? 'rtl' : 'ltr'
  document.documentElement.lang = newLang
  document.body.dir = newLang === 'fa' ? 'rtl' : 'ltr'
}, { immediate: true })

// Refs for "How It Works" animation
const howItWorksSectionRef = ref<HTMLElement | null>(null)
const hasAnimatedSteps = ref(false)
const showArrow1to2 = ref(false)
const animateArrow1to2 = ref(false)
const showArrow2to3 = ref(false)
const animateArrow2to3 = ref(false)

const { stop: stopObserver } = useIntersectionObserver(
  howItWorksSectionRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting && !hasAnimatedSteps.value) {
      hasAnimatedSteps.value = true
      stopObserver() // Stop observing after triggering once

      // Sequence the animations
      showArrow1to2.value = true
      setTimeout(() => {
        animateArrow1to2.value = true
      }, 50) // Short delay for SVG to render before animation starts

      setTimeout(() => {
        showArrow2to3.value = true
        setTimeout(() => {
          animateArrow2to3.value = true
        }, 50)
      }, 1050) // Duration of first arrow animation (1s) + buffer
    }
  },
  { threshold: 0.4 } // Trigger when 40% of the section is visible
)
</script>

<style scoped>
.landing-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e  50%, #16213e 100%);
  color: white;
  overflow-x: hidden;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-image {
  height: 100px;
  width: auto;
  transition: opacity 0.3s ease;
}

.logo:hover .logo-image {
  opacity: 0.9;
}

.nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #646cff;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.language-selector {
  background: rgba(255, 255, 255, 0.15) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
}

.language-selector:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  transform: translateY(-1px) !important;
}

.signin-btn {
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.signup-btn {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%) !important;
  border: none !important;
}

/* Hero Section */
.hero {
  padding: 8rem 0 6rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.25;
  animation: float 6s ease-in-out infinite;
  box-shadow: 0 4px 15px rgba(100, 108, 255, 0.2);
}

.shape-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #535bf2 0%, #646cff 100%);
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  top: 40%;
  right: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  margin: 0 0 1.5rem;
  line-height: 1.1;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.gradient-text {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin: 0 0 2rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 3rem;
}

.cta-button {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%) !important;
  border: none !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  padding: 1rem 2rem !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 25px rgba(100, 108, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.cta-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 35px rgba(100, 108, 255, 0.4) !important;
}

/* Button styles for both modes */
.learn-more-btn {
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  font-weight: 600 !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
}

/* Dark mode (default) */
.landing-page .learn-more-btn {
  color: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Light mode */
[data-theme="light"] .landing-page .learn-more-btn {
  color: rgba(30, 41, 59, 0.9) !important;
  border-color: rgba(30, 41, 59, 0.2) !important;
}

/* Hover states */
.landing-page .learn-more-btn:hover {
  border-color: rgba(255, 255, 255, 0.4) !important;
  color: rgba(255, 255, 255, 1) !important;
}

[data-theme="light"] .landing-page .learn-more-btn:hover {
  border-color: rgba(30, 41, 59, 0.4) !important;
  color: rgba(30, 41, 59, 1) !important;
}

/* Stats Section */
.stats {
  display: flex;
  gap: 3rem;
  margin-top: 2rem;
}

.stat {
  text-align: center;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #646cff;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* Exchange Widget */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  overflow: hidden; /* Prevent horizontal overflow */
}

.exchange-widget {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  min-width: 0; /* Allow shrinking below content size */
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  transform-style: preserve-3d;
  perspective: 1000px;
  box-sizing: border-box; /* Include padding in width calculations */
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.widget-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: white;
}

.live-rates {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #646cff;
  font-weight: 500;
}

.live-indicator {
  width: 8px;
  height: 8px;
  background: #22c55e;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.currency-exchange {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.currency-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
}

.currency-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.currency-flag {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: white;
}

.currency-code {
  font-weight: 600;
  color: white;
}

.amount {
  font-size: 1.25rem;
  font-weight: 700;
  color: #646cff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.exchange-icon {
  display: flex;
  justify-content: center;
  margin: 0.5rem 0;
}

.exchange-arrow {
  color: #646cff;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

.find-partner-btn {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%) !important;
  border: none !important;
  font-weight: 600 !important;
  border-radius: 12px !important;
  padding: 1rem !important;
  box-shadow: 0 8px 25px rgba(100, 108, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.find-partner-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 35px rgba(100, 108, 255, 0.4) !important;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden; /* Prevent horizontal overflow */
}

/* Features Section */
.features {
  padding: 6rem 0;
  background: rgba(255, 255, 255, 0.02);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 1rem;
  color: white;
}

.section-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #646cff, transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 20px 60px rgba(100, 108, 255, 0.2);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  color: #646cff;
  margin-bottom: 1.5rem;
  background: rgba(100, 108, 255, 0.1);
  border-radius: 16px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-inline: auto;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(100, 108, 255, 0.2);

  &:hover {
    transform: scale(1.05);
    background: rgba(100, 108, 255, 0.15);
    box-shadow: 0 8px 32px rgba(100, 108, 255, 0.2);
  }

  .n-icon {
    transition: transform 0.3s ease;
  }

  &:hover .n-icon {
    transform: scale(1.1);
  }
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: white;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  font-size: 1rem;
}

/* How It Works Section */
.how-it-works {
  padding: 6rem 0;
  position: relative;
}

.steps {  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  margin-top: 4rem;
  position: relative; /* Ensure arrows are contained */
}

.step {
  text-align: center;
  position: relative;
}

.step-number {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;  margin: 0 auto 2rem;
  box-shadow: 0 10px 30px rgba(100, 108, 255, 0.3);
  position: relative; /* For z-index if arrows overlap numbers */
  z-index: 1;
}

.step-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: white;
}

.step-content p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  font-size: 1rem;
}

/* Styles for Animated Arrows */
.step-arrow {
  position: absolute;
  top: 30px; /* (80px step-number height / 2) - (20px arrow svg height / 2) */
  height: 20px; /* ViewBox height */
  overflow: visible; /* Important for markers to show */
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  z-index: 0; /* Below step numbers if they overlap */
}

.step-arrow.show {
  opacity: 1;
}

.step-arrow .arrow-path {
  stroke-width: 2.5;
  fill: none;
  stroke-linecap: round;
  stroke-dasharray: 80; /* Adjusted length for new path */
  stroke-dashoffset: 80;
  transition: stroke-dashoffset 1s ease-in-out;
  stroke: #646cff;
}

[data-theme="light"] .step-arrow .arrow-path {
  stroke: #4f46e5;
}

[data-theme="dark"] .step-arrow .arrow-path {
  stroke: #646cff;
}

.step-arrow.animate .arrow-path {
  stroke-dashoffset: 0;
}

.step-arrow .arrow-marker-polygon {
  fill: #646cff;
  opacity: 0; /* Hide arrowhead initially */
  transition: opacity 0.3s ease-in-out 0.7s; /* Delay appearance until near end of line animation */
}

[data-theme="light"] .step-arrow .arrow-marker-polygon {
  fill: #4f46e5;
}

[data-theme="dark"] .step-arrow .arrow-marker-polygon {
  fill: #646cff;
}

.step-arrow.animate .arrow-marker-polygon {
  opacity: 1; /* Show arrowhead when animating */
}

/* LTR Positioning for Arrows */
.landing-page.ltr .arrow-1-to-2 {
  left: calc(16.666% + 60px); /* More space from circle edge */
  width: calc(33.333% - 140px); /* Even less width to avoid touching circles */
}

.landing-page.ltr .arrow-2-to-3 {
  left: calc(50% + 60px); /* More space from circle edge */
  width: calc(33.333% - 140px); /* Even less width to avoid touching circles */
}

/* RTL Positioning for Arrows */
.landing-page.rtl .arrow-1-to-2 {
  right: calc(16.666% + 60px);
  width: calc(33.333% - 140px);
  transform: scaleX(-1); /* Flip SVG for RTL */
}

.landing-page.rtl .arrow-2-to-3 {
  right: calc(50% + 60px);
  width: calc(33.333% - 140px);
  transform: scaleX(-1); /* Flip SVG for RTL */
}

/* Testimonials Section */
.testimonials {
  padding: 6rem 0;
  background: rgba(255, 255, 255, 0.02);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
}

.testimonial-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: white;
  font-size: 1.25rem;
}

.user-info h4 {
  margin: 0 0 0.25rem;
  color: white;
  font-weight: 600;
}

.user-info p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.testimonial-text {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  font-style: italic;
}

/* CTA Section */
.cta-section {
  padding: 6rem 0;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.05"><circle cx="36" cy="36" r="3"/></g></svg>');
}

.cta-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.cta-content h2 {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 1rem;
  color: white;
}

.cta-content p {
  font-size: 1.25rem;
  margin: 0 0 2.5rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-button-large {
  background: white !important;
  color: #646cff !important;
  font-weight: 700 !important;
  font-size: 1.25rem !important;
  padding: 1.5rem 3rem !important;
  border-radius: 16px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease !important;
}

.cta-button-large:hover {
  background: rgba(255, 255, 255, 0.95) !important;
  transform: translateY(-3px) !important;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3) !important;
}

/* Footer */
.footer {
  padding: 3rem 0;
  background: #0f0f23;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-logo {
  font-size: 1.75rem;
  font-weight: 700;
}

.footer-text p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.footer-text p:first-child {
  margin-bottom: 0.5rem;
}

/* RTL Support */
.landing-page.rtl .hero-content {
  grid-template-columns: 1fr 1fr;
}

.landing-page.rtl .hero-text {
  text-align: right;
}

.landing-page.rtl .hero-buttons {
  justify-content: flex-start;
  direction: rtl;
}

.landing-page.rtl .hero-buttons a {
  margin-left: 1rem;
  margin-right: 0;
}

/* Fix RTL layout to also respect responsive breakpoints */
@media (max-width: 1024px) {
  .landing-page.rtl .hero-content {
    grid-template-columns: 1fr !important;
    gap: 3rem;
    text-align: center;
  }
  
  .landing-page.rtl .hero-text {
    text-align: center;
  }
  
  .landing-page.rtl .hero-buttons {
    justify-content: center;
    direction: ltr; /* Reset direction for centered buttons */
  }
  
  .landing-page.rtl .hero-buttons a {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
}

.landing-page.rtl .stats {
  justify-content: flex-end;
}

/* Override RTL stats alignment on screens ≤1024px - center them like LTR */
@media (max-width: 1024px) {
  .landing-page.rtl .stats {
    justify-content: center;
  }
}

.landing-page.rtl .header-content {
  direction: rtl;
}

.landing-page.rtl .nav {
  direction: rtl;
}

.landing-page.rtl .footer-content {
  text-align: right;
  direction: rtl;
}

.landing-page.rtl .step {
  text-align: right;
}

.landing-page.rtl .feature-card {
  text-align: right;
}

.landing-page.rtl .testimonial-header {
  direction: rtl;
}

/* Persian font support */
.landing-page.rtl * {
  font-family: 'Vazirmatn', 'Tahoma', 'Arial', sans-serif;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-title {
    font-size: 3.5rem;
  }

  .stats {
    justify-content: center;
  }

  .nav {
    display: none;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 10rem 0 6rem;
  }
  
  .hero-title {
    font-size: 3rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .hero-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .exchange-widget {
    padding: 1.75rem;
    max-width: 90%;
  }
  
  .widget-header {
    margin-bottom: 1.25rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .steps {
    grid-template-columns: 1fr;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .container {
    padding: 0 1rem;
  }

  .hero {
    padding: 6rem 0 4rem;
  }

  .features, .how-it-works, .testimonials {
    padding: 4rem 0;
  }

  .cta-section {
    padding: 4rem 0;
  }

  .cta-content h2 {
    font-size: 2.5rem;
  }
  .stats {
    gap: 2rem;
    justify-content: center;
  }

  .header-actions {
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 12rem 0 6rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    width: 100%;
  }

  .hero-buttons a {
    width: 100%;
  }  .stats {
    flex-direction: column;
    gap: 1.5rem;
    justify-content: center;
    align-items: center;
  }

  .exchange-widget {
    margin: 0;
    padding: 1.5rem;
    max-width: 100%;
    box-sizing: border-box;
  }
  
  .widget-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .widget-title {
    font-size: 1.1rem;
  }
  
  .live-rates {
    font-size: 0.8rem;
  }
  
  .currency-input {
    padding: 0.875rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .currency-info {
    gap: 0.5rem;
  }
  
  .currency-flag {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }
  
  .currency-code {
    font-size: 0.9rem;
  }
  
  .amount {
    font-size: 1.1rem;
  }
  
  .find-partner-btn {
    padding: 0.875rem !important;
    font-size: 0.95rem !important;
  }
}

/* Ultra-small mobile devices (320px and below) */
@media (max-width: 360px) {
  .hero {
    padding: 10rem 0 4rem;
  }
  
  .stats {
    gap: 1rem;
    justify-content: center;
    align-items: center;
  }
  
  .exchange-widget {
    padding: 1rem;
    border-radius: 16px;
  }
  
  .widget-title {
    font-size: 1rem;
  }
  
  .currency-input {
    padding: 0.75rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .currency-info {
    width: 100%;
  }
  
  .amount {
    font-size: 1rem;
    width: 100%;
    text-align: right;
  }
  
  .currency-flag {
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
  }
  
  .currency-code {
    font-size: 0.85rem;
  }
  
  .exchange-icon {
    margin: 0.25rem 0;
  }
  
  .find-partner-btn {
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
  }
}

/* Light Theme Styles */
[data-theme="light"] .landing-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  color: #1e293b;
}

[data-theme="light"] .header {
  background: rgba(248, 250, 252, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .logo-text,
[data-theme="light"] .logo-brand {
  color: #1e293b;
}

[data-theme="light"] .language-selector {
  background: rgba(0, 0, 0, 0.05) !important; /* Light grey background */
  border: 1px solid rgba(0, 0, 0, 0.1) !important; /* Slightly darker border */
}

[data-theme="light"] .language-selector :deep(.n-button__content),
[data-theme="light"] .language-selector :deep(.n-icon) {
  color: #1e293b !important; /* Dark text/icon color */
}

[data-theme="light"] .language-selector:hover {
  background: rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(0, 0, 0, 0.15) !important;
}

[data-theme="light"] .nav-link {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .nav-link:hover {
  color: #646cff;
}

[data-theme="light"] .signin-btn {
  color: #1e293b !important;
  border: 1px solid rgba(30, 41, 59, 0.2) !important;
}

[data-theme="light"] .signup-btn {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%) !important;
  color: white !important;
  border: none !important;
}

[data-theme="light"] .hero-title {
  color: #1e293b;
}

[data-theme="light"] .hero-subtitle {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .stat-value {
  color: #4f46e5; /* A slightly darker shade of purple for light mode */
}

[data-theme="light"] .stat-label {
  color: rgba(30, 41, 59, 0.7);
}

[data-theme="light"] .exchange-widget {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 0 32px rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

[data-theme="light"] .widget-title {
  color: #1e293b;
}

[data-theme="light"] .live-rates {
  color: #4f46e5;
}

[data-theme="light"] .currency-input {
  background: rgba(226, 232, 240, 0.5); /* Light gray background for inputs */
  border: 1px solid rgba(100, 108, 255, 0.2);
}

[data-theme="light"] .currency-code {
  color: #1e293b;
}

[data-theme="light"] .amount {
  color: #4f46e5;
}

[data-theme="light"] .floating-shapes .shape {
  opacity: 0.4;
}

[data-theme="light"] .shape-1 {
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.4) 0%, rgba(219, 39, 119, 0.3) 100%);
  box-shadow: 0 8px 32px rgba(236, 72, 153, 0.2);
}

[data-theme="light"] .shape-2 {
  background: linear-gradient(135deg, rgba(219, 39, 119, 0.35) 0%, rgba(100, 108, 255, 0.25) 100%);
  box-shadow: 0 8px 32px rgba(219, 39, 119, 0.15);
}

[data-theme="light"] .shape-3 {
  background: linear-gradient(135deg, rgba(100, 108, 255, 0.3) 0%, rgba(236, 72, 153, 0.35) 100%);
  box-shadow: 0 8px 32px rgba(100, 108, 255, 0.2);
}

[data-theme="light"] .section-title {
  color: #1e293b;
}

[data-theme="light"] .section-subtitle {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .feature-card {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(100, 108, 255, 0.15);
  color: #1e293b;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

[data-theme="light"] .feature-card h3 {
  color: #1e293b;
}

[data-theme="light"] .feature-card p {
  color: rgba(30, 41, 59, 0.8); /* Darker text for light mode */
}

[data-theme="light"] .step-content h3 {
  color: #1e293b;
}

[data-theme="light"] .step-content p {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .step-number {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  color: white;
}

[data-theme="light"] .testimonial-card {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(100, 108, 255, 0.15);
  color: #1e293b;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

[data-theme="light"] .testimonial-card .user-info h4 {
  color: #111827; /* Darker for heading */
}

[data-theme="light"] .testimonial-card .user-info p {
  color: #4b5563; /* Slightly lighter for sub-text */
}

[data-theme="light"] .testimonial-card .testimonial-text {
  color: #374151; /* Main testimonial text color */
}

[data-theme="light"] .cta-section {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%); /* Keep dark for contrast */
}

[data-theme="light"] .cta-content h2 {
  color: white;
}

[data-theme="light"] .cta-content p {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="light"] .footer {
  background: rgba(248, 250, 252, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  color: #1e293b;
}

[data-theme="light"] .footer .gradient-text {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[data-theme="light"] .footer p { /* General paragraph text in footer */
  color: #374151;
}

[data-theme="light"] .footer-text p { /* Copyright text etc. */
  color: #4b5563;
}

[data-theme="light"] .footer-link {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .footer-link:hover {
  color: #646cff;
}

[data-theme="light"] .social-icon {
  color: rgba(30, 41, 59, 0.8);
}

[data-theme="light"] .social-icon:hover {
  color: #646cff;
}
</style>
