<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useMessage, useDialog } from 'naive-ui'
import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic'
import { useTimerDisplay } from '@/composables/useTimerDisplay'
import { TransactionStatusEnum } from '@/types/transaction'
import TimerDisplay from '@/components/common/TimerDisplay.vue'

interface PaymentDeclaration {
  amount: number
  currency: string
  declaredBy: string
  declaredAt: string // ISO string
  trackingNumber?: string
  reference?: string
}

interface Props {
  paymentDeclaration: PaymentDeclaration
  chatSessionId?: string
}

const props = withDefaults(defineProps<Props>(), {
  chatSessionId: undefined
})

const emit = defineEmits<{
  receiptConfirmed: []
  notReceived: []
}>()

const { t } = useI18n()
const message = useMessage()
const dialog = useDialog()


// Timer Logic (using same composable as SmartPaymentSection)
const {
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
} = useTransactionFlowLogic(
  computed(() => props.chatSessionId || null),
  message
);

// Use shared timer display logic
const {
  timerDisplayValue,
  timerColorClass,
  timerLabel
} = useTimerDisplay({
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
});


// State management
const isSubmitting = ref<boolean>(false)
const showDetails = ref<boolean>(false)


// Computed properties
const formattedAmount = computed(() => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: props.paymentDeclaration.currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(props.paymentDeclaration.amount)
})

const formattedDeclarationTime = computed(() => {
  const date = new Date(props.paymentDeclaration.declaredAt)
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    day: 'numeric',
    month: 'short'
  }).format(date)
})

const canConfirmReceipt = computed(() => {
  return !isSubmitting.value
})


// Methods
const showConfirmationModal = () => {
  dialog.warning({
    title: t('transactionalChat.actionCards.confirmReceipt.confirmationTitle'),
    content: t('transactionalChat.actionCards.confirmReceipt.confirmationMessage', {
      amount: formattedAmount.value,
      currency: props.paymentDeclaration.currency,
      declaredBy: props.paymentDeclaration.declaredBy
    }),
    positiveText: t('transactionalChat.actionCards.confirmReceipt.confirmButton'),
    negativeText: t('transactionalChat.actionCards.confirmReceipt.cancelButton'),
    onPositiveClick: () => {
      confirmReceipt()
    }
  })
}

const confirmReceipt = async () => {
  if (!canConfirmReceipt.value) return
  isSubmitting.value = true
  try {
    emit('receiptConfirmed')
    message.success(t('transactionalChat.actionCards.confirmReceipt.confirmed'))
  } catch (error) {
    message.error(t('transactionalChat.actionCards.confirmReceipt.confirmFailed'))
  } finally {
    isSubmitting.value = false
  }
}

const showNotReceivedModal = () => {
  dialog.warning({
    title: t('transactionalChat.actionCards.confirmReceipt.notReceivedConfirmTitle', 'Report Not Received?'),
    content: t('transactionalChat.actionCards.confirmReceipt.notReceivedConfirmMessage', 'Are you sure you want to report that the payment was not received?'),
    positiveText: t('transactionalChat.actionCards.confirmReceipt.notReceivedConfirmButton', 'Report'),
    negativeText: t('transactionalChat.actionCards.confirmReceipt.cancelButton'),
    onPositiveClick: () => {
      reportNotReceived()
    }
  })
}

const reportNotReceived = async () => {
  try {
    emit('notReceived')
    message.info(t('transactionalChat.actionCards.confirmReceipt.notReceivedReported'))
  } catch (error) {
    message.error(t('transactionalChat.actionCards.confirmReceipt.reportFailed'))
  }
}

// Lifecycle - no longer needed since timer is handled by composable
</script>


<template>
  <div class="smart-receipt-section" data-testid="smart-receipt-section">
    <!-- Timer Display (compact version) -->
    <TimerDisplay 
      :timer-display-value="timerDisplayValue"
      :timer-color-class="timerColorClass"
      :timer-label="timerLabel"
      test-id="receipt-timer"
    />

    <!-- Summary Sentence -->
    <div class="summary-sentence" data-testid="receipt-summary">
      <span class="summary-icon">
        <i class="icon-user"></i>
      </span>
      <span>
        <strong>{{ paymentDeclaration.declaredBy }}</strong>
        {{ t('transactionalChat.actionCards.confirmReceipt.summaryText', 'has declared they sent you') }}
        <strong>{{ formattedAmount }}</strong>
      </span>
    </div>
    <div class="summary-time" data-testid="receipt-declared-at">
      {{ t('transactionalChat.actionCards.confirmReceipt.declaredAt') || 'Declared at' }} {{ formattedDeclarationTime }}
    </div>

    <!-- Show/Hide Details Toggle -->
    <div v-if="paymentDeclaration.trackingNumber || paymentDeclaration.reference" class="details-toggle-row">
      <button class="details-toggle-btn" @click="showDetails = !showDetails" data-testid="toggle-details-btn">
        <span v-if="!showDetails">{{ t('transactionalChat.actionCards.confirmReceipt.showDetails', 'Show Details') }} ▼</span>
        <span v-else>{{ t('transactionalChat.actionCards.confirmReceipt.hideDetails', 'Hide Details') }} ▲</span>
      </button>
    </div>

    <!-- Collapsible Details -->
    <transition name="fade">
      <div v-if="showDetails" class="payment-details-unified" data-testid="receipt-details">
        <div v-if="paymentDeclaration.trackingNumber" class="detail-row">
          <i class="icon-hashtag detail-icon" aria-hidden="true"></i>
          <span class="detail-label">{{ t('transactionalChat.trackingNumber', 'Tracking Number') }}:</span>
          <span class="detail-value">{{ paymentDeclaration.trackingNumber }}</span>
        </div>
        <div v-if="paymentDeclaration.reference" class="detail-row">
          <i class="icon-note detail-icon" aria-hidden="true"></i>
          <span class="detail-label">{{ t('transactionalChat.notes', 'Notes') }}:</span>
          <span class="detail-value">{{ paymentDeclaration.reference }}</span>
        </div>
      </div>
    </transition>

    <!-- Action Buttons (mobile-first, primary action first) -->
    <div class="action-buttons-compact">
      <button 
        class="primary-button-compact"
        :class="{ 'submitting': isSubmitting }"
        @click="showConfirmationModal"
        data-testid="confirm-receipt-button"
        :disabled="!canConfirmReceipt"
      >
        <i class="icon-check submit-icon" aria-hidden="true"></i>
        <span v-if="isSubmitting" class="submit-spinner">⟳</span>
        {{ t('transactionalChat.actionCards.confirmReceipt.button') }}
      </button>
      <button 
        class="secondary-link-compact"
        @click="showNotReceivedModal"
        data-testid="not-received-button"
        :disabled="isSubmitting"
      >
        <i class="icon-flag" aria-hidden="true"></i>
        {{ t('transactionalChat.actionCards.confirmReceipt.notReceived') }}
      </button>
    </div>
  </div>
</template>

<style scoped>
.smart-receipt-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 100%;
}

/* Summary Sentence */
.summary-sentence {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 500;
  color: var(--tc-text-primary);
  background: var(--tc-bg-subtle);
  border-radius: 8px;
  padding: 10px 12px 6px 12px;
}
.summary-icon {
  font-size: 18px;
  color: var(--tc-primary);
  display: flex;
  align-items: center;
}
.summary-time {
  font-size: 12px;
  color: var(--tc-text-muted);
  margin-left: 4px;
  margin-bottom: 2px;
}

/* Details Toggle */
.details-toggle-row {
  display: flex;
  justify-content: flex-end;
}
.details-toggle-btn {
  background: none;
  border: none;
  color: var(--tc-primary);
  font-size: 13px;
  cursor: pointer;
  padding: 0 4px;
  text-decoration: underline;
  transition: color 0.2s;
}
.details-toggle-btn:hover {
  color: var(--tc-primary-dark);
}

/* Unified Details */
.payment-details-unified {
  background: var(--tc-bg-subtle);
  border: 1px solid var(--tc-border-light);
  border-radius: 8px;
  padding: 10px 12px;
  margin-bottom: 2px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
}
.detail-icon {
  font-size: 15px;
  color: var(--tc-primary);
  flex-shrink: 0;
}
.detail-label {
  font-size: 13px;
  color: var(--tc-text-secondary);
  font-weight: 600;
  min-width: fit-content;
}
.detail-value {
  font-size: 13px;
  color: var(--tc-text-primary);
  font-weight: 500;
  word-break: break-word;
}

/* Action Buttons (mobile-first) */
.action-buttons-compact {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 4px;
}
.primary-button-compact {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 0;
  background: var(--tc-success);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
}
.primary-button-compact:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.primary-button-compact.submitting {
  cursor: not-allowed;
}
.submit-icon {
  font-size: 18px;
  color: white;
}
.submit-spinner {
  font-size: 16px;
  margin-right: 4px;
}
.secondary-link-compact {
  background: none;
  border: none;
  color: var(--tc-danger);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
  min-height: 36px;
  text-decoration: underline;
  transition: color 0.2s;
}
.secondary-link-compact:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.secondary-link-compact .icon-flag {
  font-size: 16px;
}

/* Animations */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* Responsive Design */
@media (max-width: 480px) {
  .summary-sentence {
    font-size: 14px;
    padding: 10px 8px 6px 8px;
  }
  .summary-time {
    font-size: 11px;
  }
  .detail-label, .detail-value {
    font-size: 12px;
  }
  .primary-button-compact {
    font-size: 14px;
    min-height: 44px;
  }
  .secondary-link-compact {
    font-size: 13px;
    min-height: 32px;
  }
}

/* RTL Support */
[dir="rtl"] .summary-sentence {
  flex-direction: row-reverse;
}
[dir="rtl"] .summary-time {
  margin-left: 0;
  margin-right: 4px;
}
[dir="rtl"] .details-toggle-row {
  justify-content: flex-start;
}
[dir="rtl"] .payment-details-unified {
  align-items: flex-end;
}
[dir="rtl"] .detail-row {
  flex-direction: row-reverse;
}
[dir="rtl"] .action-buttons-compact {
  flex-direction: column-reverse;
}
</style>


