import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function createAdminUsers() {
  const saltRounds = 10;
  const commonPassword = '11111111'; // Simple password for testing
  const hashedPassword = await bcrypt.hash(commonPassword, saltRounds);

  // Admin emails from environment variable
  const adminEmails = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];

  console.log('Creating admin users...');

  for (const email of adminEmails) {
    try {
      const username = email.split('@')[0]; // Extract username from email
      
      const user = await prisma.user.upsert({
        where: { email },
        update: {
          password: hashedPassword,
          emailVerified: true,
          phoneVerified: true,
          reputationScore: 100, // High reputation for admin users
          reputationLevel: 5,   // Elite level
          updatedAt: new Date(),
        },
        create: {
          email,
          password: hashedPassword,
          username,
          emailVerified: true,
          phoneNumber: `+1000000${Math.floor(Math.random() * 9000) + 1000}`, // Random test phone
          phoneVerified: true,
          reputationScore: 100,
          reputationLevel: 5,
          verificationToken: null,
        },
      });
      
      console.log(`✅ Admin user created/updated: ${user.email} (ID: ${user.id})`);
      
    } catch (error) {
      console.error(`❌ Failed to create admin user ${email}:`, error);
    }
  }

  console.log('Admin user creation complete.');
}

createAdminUsers()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('Admin user creation failed:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
