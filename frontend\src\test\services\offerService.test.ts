import { describe, it, expect, beforeEach, vi } from 'vitest'
import { offerService } from '@/services/offerService'
import type { OfferWithUser } from '@/types/offer'

// Mock the API client
const mockApiClient = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn()
}

vi.mock('@/services/apiClient', () => ({
  default: mockApiClient
}))

describe('OfferService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getOfferById', () => {    const mockOfferResponse: OfferWithUser = {
      id: 'offer-123',
      type: 'SELL',
      amount: 1000,
      baseRate: 150000,
      adjustmentForLowerRep: 2,
      adjustmentForHigherRep: 1,
      status: 'ACTIVE',
      createdAt: '2025-06-01T12:00:00Z',
      currencyPair: 'USD-IRR',
      userId: 'user-456',
      user: {
        username: 'testuser',
        reputationLevel: 3
      },
      userInterest: null,
      isOwner: false
    }

    it('should fetch offer by ID successfully', async () => {
      mockApiClient.get.mockResolvedValue({ data: mockOfferResponse })
      
      const result = await offerService.getOfferById('offer-123')
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/offers/offer-123')
      expect(result).toEqual(mockOfferResponse)
    })

    it('should handle 404 errors appropriately', async () => {
      const error = {
        response: {
          status: 404,
          data: { message: 'Offer not found' }
        }
      }
      mockApiClient.get.mockRejectedValue(error)
      
      await expect(offerService.getOfferById('nonexistent')).rejects.toThrow()
    })

    it('should handle network errors', async () => {
      mockApiClient.get.mockRejectedValue(new Error('Network error'))
      
      await expect(offerService.getOfferById('offer-123')).rejects.toThrow('Network error')
    })

    it('should handle offers with existing user interest', async () => {
      const offerWithInterest = {
        ...mockOfferResponse,
        userInterest: { id: 'interest-123', status: 'PENDING' }
      }
      mockApiClient.get.mockResolvedValue({ data: offerWithInterest })
      
      const result = await offerService.getOfferById('offer-123')
      
      expect(result.userInterest).toEqual({ id: 'interest-123', status: 'PENDING' })
    })

    it('should handle owner vs visitor perspective', async () => {
      const ownerOffer = { ...mockOfferResponse, isOwner: true }
      mockApiClient.get.mockResolvedValue({ data: ownerOffer })
      
      const result = await offerService.getOfferById('offer-123')
      
      expect(result.isOwner).toBe(true)
    })
  })

  describe('expressInterest', () => {
    it('should express interest successfully', async () => {
      const mockResponse = {
        success: true,
        interest: { id: 'interest-123', status: 'PENDING' }
      }
      mockApiClient.post.mockResolvedValue({ data: mockResponse })
      
      const result = await offerService.expressInterest('offer-123')
      
      expect(mockApiClient.post).toHaveBeenCalledWith('/offers/offer-123/interest')
      expect(result).toEqual(mockResponse)
    })

    it('should handle duplicate interest errors', async () => {
      const error = {
        response: {
          status: 400,
          data: { message: 'Interest already exists' }
        }
      }
      mockApiClient.post.mockRejectedValue(error)
      
      await expect(offerService.expressInterest('offer-123')).rejects.toThrow()
    })

    it('should handle offer not found errors', async () => {
      const error = {
        response: {
          status: 404,
          data: { message: 'Offer not found' }
        }
      }
      mockApiClient.post.mockRejectedValue(error)
      
      await expect(offerService.expressInterest('nonexistent')).rejects.toThrow()
    })

    it('should handle own offer errors', async () => {
      const error = {
        response: {
          status: 403,
          data: { message: 'Cannot express interest in own offer' }
        }
      }
      mockApiClient.post.mockRejectedValue(error)
      
      await expect(offerService.expressInterest('own-offer')).rejects.toThrow()
    })
  })
  describe('updateOfferStatus', () => {
    const mockOfferForUpdate: OfferWithUser = {
      id: 'offer-123',
      type: 'SELL',
      amount: 1000,
      baseRate: 150000,
      adjustmentForLowerRep: 2,
      adjustmentForHigherRep: 1,
      status: 'ACTIVE',
      createdAt: '2025-06-01T12:00:00Z',
      currencyPair: 'USD-IRR',
      userId: 'user-456',
      user: {
        username: 'testuser',
        reputationLevel: 3
      },
      userInterest: null,
      isOwner: false
    }

    it('should update offer status successfully', async () => {
      const mockResponse = {
        success: true,
        offer: { ...mockOfferForUpdate, status: 'INACTIVE' }
      }
      mockApiClient.patch.mockResolvedValue({ data: mockResponse })
      
      const result = await offerService.updateOfferStatus('offer-123', 'INACTIVE')
      
      expect(mockApiClient.patch).toHaveBeenCalledWith('/offers/offer-123/status', {
        status: 'INACTIVE'
      })
      expect(result).toEqual(mockResponse)
    })

    it('should handle unauthorized status updates', async () => {
      const error = {
        response: {
          status: 403,
          data: { message: 'Not authorized to update this offer' }
        }
      }
      mockApiClient.patch.mockRejectedValue(error)
      
      await expect(offerService.updateOfferStatus('offer-123', 'INACTIVE')).rejects.toThrow()
    })

    it('should handle invalid status values', async () => {
      const error = {
        response: {
          status: 400,
          data: { message: 'Invalid status value' }
        }
      }
      mockApiClient.patch.mockRejectedValue(error)
      
      await expect(offerService.updateOfferStatus('offer-123', 'INVALID' as any)).rejects.toThrow()
    })

    it('should handle offer not found for status update', async () => {
      const error = {
        response: {
          status: 404,
          data: { message: 'Offer not found' }
        }
      }
      mockApiClient.patch.mockRejectedValue(error)
      
      await expect(offerService.updateOfferStatus('nonexistent', 'INACTIVE')).rejects.toThrow()
    })
  })

  describe('Error Response Formats', () => {
    it('should handle different error response formats', async () => {
      // Test with error message in data
      let error = {
        response: {
          status: 500,
          data: { message: 'Server error' }
        }
      }
      mockApiClient.get.mockRejectedValue(error)
      
      await expect(offerService.getOfferById('offer-123')).rejects.toThrow()
      
      // Test with error message as string
      error = {
        response: {
          status: 500,
          data: 'Server error'
        }
      } as any
      mockApiClient.get.mockRejectedValue(error)
      
      await expect(offerService.getOfferById('offer-123')).rejects.toThrow()
      
      // Test with no response (network error)
      mockApiClient.get.mockRejectedValue(new Error('Network timeout'))
      
      await expect(offerService.getOfferById('offer-123')).rejects.toThrow('Network timeout')
    })
  })

  describe('Type Safety', () => {
    it('should handle response with all required offer fields', async () => {      const completeOffer: OfferWithUser = {
        id: 'offer-123',
        type: 'BUY',
        amount: 2000,
        baseRate: 145000,
        adjustmentForLowerRep: 1.5,
        adjustmentForHigherRep: 0.5,
        status: 'ACTIVE',
        createdAt: '2025-06-01T12:00:00Z',
        currencyPair: 'USD-IRR',
        userId: 'user-789',
        user: {
          username: 'anotheruser',
          reputationLevel: 5
        },
        userInterest: {
          id: 'interest-456',
          status: 'ACCEPTED'
        },
        isOwner: true
      }
      
      mockApiClient.get.mockResolvedValue({ data: completeOffer })
      
      const result = await offerService.getOfferById('offer-123')
      
      expect(result).toEqual(completeOffer)
      expect(result.user.username).toBe('anotheruser')
      expect(result.user.reputationLevel).toBe(5)
      expect(result.userInterest?.status).toBe('ACCEPTED')
      expect(result.isOwner).toBe(true)
    })

    it('should handle minimal valid offer response', async () => {      const minimalOffer: OfferWithUser = {
        id: 'offer-123',
        type: 'SELL',
        amount: 500,
        baseRate: 150000,
        adjustmentForLowerRep: 0,
        adjustmentForHigherRep: 0,
        status: 'ACTIVE',
        createdAt: '2025-06-01T12:00:00Z',
        currencyPair: 'USD-IRR',
        userId: 'user-456',
        user: {
          username: 'minimaluser',
          reputationLevel: 1
        },
        userInterest: null,
        isOwner: false
      }
      
      mockApiClient.get.mockResolvedValue({ data: minimalOffer })
      
      const result = await offerService.getOfferById('offer-123')
      
      expect(result).toEqual(minimalOffer)
      expect(result.userInterest).toBeNull()
      expect(result.adjustmentForLowerRep).toBe(0)
      expect(result.adjustmentForHigherRep).toBe(0)
    })
  })
})
