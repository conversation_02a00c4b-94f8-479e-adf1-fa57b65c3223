### **Project Brief: Transactional Chat Cockpit for P2P Currency Exchange**

**To:** Gemini 2.5 Pro, Implementation Team
**From:** Gemini, Project Architect
**Date:** June 30, 2025
**Subject:** Full implementation of a new feature-rich, user-centric transactional chat interface.

#### **1. Project Vision & Core Objective**

The goal is to design and implement a mobile-first **"Transactional Chat Cockpit."** This is not merely a chat screen; it is an intelligent, unified interface for managing a peer-to-peer currency swap. The primary objective is to reduce user anxiety, build trust, and guide users seamlessly through a multi-step transaction by providing absolute clarity on status, required actions, and communication history at all times.

#### **2. Guiding Principles & Architectural Philosophy**

*   **Clarity Above All:** The user must know the transaction's status, whose turn it is, and what to do next at a single glance. There should be zero ambiguity.
*   **Single Source of Truth:** All transactional state (step, timer, messages, user data) will be managed centrally in a **Pinia store**. Components will be reactive subscribers to this store, not managers of their own state.
*   **Composition & Decoupling:** The UI will be broken down into small, single-responsibility components using Vue 3's Composition API (`<script setup>`). This ensures maintainability and testability.
*   **Seamless Integration of Action & Communication:** The user should never feel trapped or unable to communicate. Chat must always be accessible, even when a primary action is required.

#### **3. High-Level Feature Requirements**

The final implementation must include:
1.  A multi-step progress bar visualizing the entire transaction lifecycle.
2.  A unified, chronological feed displaying user chat, system logs, and interactive action cards.
3.  A dynamic bottom action bar that contextually switches between a chat input and primary action buttons.
4.  Step-by-step guidance for a 7-step transaction process.
5.  Integrated timers for time-sensitive actions.
6.  Full support for both **Light and Dark mode** theming.
7.  Complete **internationalization (i18n)** for all user-facing text.

---

#### **4. Component Architecture & Structure**

Implement the following component hierarchy. All data related to the transaction itself should be read from the central Pinia store, not passed down as props.

*   `TransactionView.vue`: The top-level container. Its role is to orchestrate the layout and initialize the state via the Pinia store on mount.
    *   `TheHeaderBar.vue`: **(Display Only)** Shows other user's info (name, reputation, etc.) and a menu button.
    *   `TheSmartStatusBar.vue`: **(Display Only)** Renders the segmented progress bar and the timer. Its appearance is entirely dictated by the store's state.
    *   `TheUnifiedFeed.vue`: Renders a list of items from the store's `feedItems` array using dynamic `<component :is="...">`.
        *   `ChatMessage.vue`: **(Display Only)** Renders a user's chat message. Receives a single `item` prop.
        *   `SystemLog.vue`: **(Display Only)** Renders a centered system event. Receives a single `item` prop.
        *   `ActionCard.vue`: Renders the user's current task (e.g., "Confirm Receipt"). Receives an `item` prop.
    *   `TheDynamicActionBar.vue`: **(Interactive)** The most complex interactive piece. Manages its own internal UI state (chat vs. action mode) and dispatches actions to the Pinia store.

#### **5. Centralized State Management (Pinia Store: `transactionStore`)**

This is the brain. All logic for state changes must reside here.

| **State Property**       | **Type**               | **Description**                                                                |
| ------------------------ | ---------------------- | ------------------------------------------------------------------------------ |
| `otherUser`              | `Object`               | Contains `{ name, profilePic, reputation }`.                                   |
| `transactionDetails`     | `Object`               | Contains `{ amountToSend, amountToReceive, currencyFrom, currencyTo }`.        |
| `currentStepIndex`       | `Number`               | The zero-based index of the current active step (e.g., `3` for Step 4).        |
| `steps`                  | `Array`                | An array of step configuration objects.                                        |
| `feedItems`              | `Array`                | The array of all items in the chat feed (chat, logs, cards).                   |
| `timer`                  | `Object`               | `{ isActive: boolean, remainingSeconds: number }`.                             |
| `errorState`             | `Object`               | `{ hasError: boolean, message: string }` for displaying API errors.            |

| **Getter**               | **Returns**            | **Description**                                                                |
| ------------------------ | ---------------------- | ------------------------------------------------------------------------------ |
| `currentStep`            | `Object`               | The full configuration object for the current step from the `steps` array.     |
| `isUsersTurn`            | `Boolean`              | **CRUCIAL GETTER.** True if the `currentStep` requires action from the current user. |
| `activeActionCard`       | `Object` or `null`     | The object from `feedItems` that represents the current interactive Action Card. |

| **Action**                 | **Payload**              | **Description**                                                              |
| -------------------------- | ------------------------ | ---------------------------------------------------------------------------- |
| `fetchTransaction(id)`     | `transactionId`          | Fetches all initial data, populates the store, and sets the `currentStepIndex`. |
| `performAction(payload)`   | `{ actionType, data }`   | Handles all primary user actions (e.g., confirming payment, submitting info). Calls the backend, and on success (via API response or WebSocket), commits mutations to advance the state. |
| `sendMessage(text)`        | `messageText`            | Sends chat message to backend. On success, adds message to `feedItems`.      |
| `startTimer()` / `stopTimer()` | `none`                   | Manages the `setInterval` for the `timer` state.                           |

---

#### **6. Detailed UI/UX Flow: A Step-by-Step Visual & Functional Guide**

Implement the UI to match the following descriptions at each stage of the transaction.

**Scenario:** User B (you) is trading with User A (Alex).

| Step                         | Smart Status Bar                                                                    | Unified Feed Contents                                                                                                                                                                                                                                                                   | Dynamic Action Bar                                                                                                                                     |
| ---------------------------- | ----------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **1. Payment Info Setup**    | Step 1 is active. Text: "Provide Payment Information"                               | System log: "Trade started." An **Action Card** is shown, prompting the user to add/select their receiving payment details. A muted status shows "Waiting for Alex..."                                                                                                      | Standard chat input is available for communication.                                                                                                    |
| **2. Negotiation**           | Step 2 is active. Text: "Decide Who Pays First"                                     | System log: "Payment details provided." An **Action Card** appears with the system's recommendation. If the recommendation favors the user, they see a single "I Accept" button. If not, they see "I Agree" and "Request Other Pays First."                                        | Standard chat input is available for discussion.                                                                                                       |
| **3. Waiting for Payer 1**   | Step 3 is active. Text: "Waiting for Alex to send [amount]"                         | System log: "Agreement reached: Alex will pay first." The feed is now clear for chatting. No action cards are present.                                                                                                                                                            | Standard chat input is available.                                                                                                                      |
| **4. Confirm Payer 1 Receipt** | **(Key Screen)** Step 4 is active. Text: "Your Action: Confirm Receipt \| ⏳ Time left: [timer]" | System log: "Alex has marked [amount] as sent." A new **Action Card** appears: "Confirm You Have Received [amount]," with strong warning text and a display of the user's own bank details for reference.                                                                  | **(Key Interaction)** The bar shows a large, primary "Confirm Payment Received" button on the right, and a smaller, secondary chat icon `💬` on the left. Clicking the icon switches the bar to the chat input; sending a message reverts it. |
| **5. Your Turn to Pay**      | Step 5 is active. Text: "Your Action: Send [amount]"                                | System log: "You confirmed receipt." A new **Action Card** appears: "Your Turn: Send [amount] to Alex," which prominently displays *Alex's* payment details with easy "Copy" buttons for each detail.                                                                  | A single, large, primary button that says "I Have Sent The Payment." **No chat icon is needed here**, as the primary action does not require immediate validation from the other user. Clicking it will show a confirmation modal ("Are you sure?"). |
| **6. Waiting for Payer 2**   | Step 6 is active. Text: "Waiting for Alex to confirm receipt \| ⏳ Time left: [timer]" | System log: "You have marked [amount] as sent." The feed is clear for chatting while you wait.                                                                                                                                                                        | Standard chat input is available.                                                                                                                      |
| **7. Finalized & Rate**      | All steps are green. Text: "Trade Successful!"                                      | System log: "🎉 Transaction Complete!" A final **Action Card** appears prompting the user to leave a star rating and review for Alex.                                                                                                                                 | Standard chat input is available for final remarks.                                                                                                    |

---

#### **7. Theming & Internationalization (i18n) Implementation**

*   **Theming:**
    *   Implement a CSS-variable-based system. Define all colors in `:root` for light mode.
    *   Define overrides for all color variables under an `html.dark` selector.
    *   Components must **only** use `var(--variable-name)` for colors, shadows, and backgrounds. No hard-coded hex values.
    *   Create a `themeStore` in Pinia to toggle a class on the `<html>` element and persist the choice in `localStorage`.

*   **Internationalization (i18n):**
    *   Use the `vue-i18n` library.
    *   **No user-facing string should be hard-coded in any component.**
    *   All strings must be sourced from JSON locale files using structured keys (e.g., `t('transaction.actionCard.confirmReceiptTitle')`).
    *   Ensure implementation supports placeholders (`{name}`, `{amount}`) and pluralization (`{count} Trade | {count} Trades`).

#### **8. Final Acceptance Criteria**

The task is complete when:
*   All 7 steps of the transaction flow are fully implemented and visually match the descriptions.
*   The Pinia store acts as the single source of truth for all transactional data.
*   The "Smart" Dynamic Action Bar (chat icon + CTA button) is fully functional in Step 4.
*   The application seamlessly toggles between light and dark themes without visual glitches.
*   All text can be switched between at least two languages (e.g., English and Spanish) to prove the i18n system works.
*   The implementation is responsive and functions flawlessly on a standard mobile viewport (e.g., 375px width).
*   Code is clean, well-commented, and follows Vue 3 Composition API best practices.

Please proceed with the implementation based on this comprehensive brief. I trust your capabilities to build a robust and elegant solution.