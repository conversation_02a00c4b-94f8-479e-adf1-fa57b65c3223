# PowerShell script to update unit.md

# Make a backup of the original file
Copy-Item -Path "unit.md" -Destination "unit.md.bak" -Force

# Read the original file
$content = Get-Content -Path "unit.md" -Raw

# Replace 1: Update Template Slot Limitations to Named Slot Support
$pattern1 = '5. \*\*Template Slot Limitations\*\*: NButton and similar stubs don''t render template slots, so test attributes instead of icon visibility'
$replace1 = '5. **Named Slot Support**: Component stubs support essential named slots like `#icon` for consistent rendering'
$content = $content -replace $pattern1, $replace1

# Replace 2: Update NButton stub description
$pattern2 = '- ``NButton`` → ``<button data-testid="nbutton"><slot /></button>``'
$replace2 = '- ``NButton`` → ``<button data-testid="nbutton"><slot name="icon" /><slot /></button>``'
$content = $content -replace $pattern2, $replace2

# Replace 3: Update Critical Lessons section
$pattern3 = '2. \*\*Stub Limitations\*\*: Global stubs may not render template slots \(like icons inside buttons\), so test attributes rather than visual elements'
$replace3 = '2. **Named Slot Support**: Most stubs (especially NButton) have been updated to support named slots like `#icon` which is crucial for testing components that use them'
$content = $content -replace $pattern3, $replace3

# Write the updated content back to the file
$content | Set-Content -Path "unit.md"

Write-Host "Documentation updated successfully!"
