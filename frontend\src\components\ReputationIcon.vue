<template>
  <n-tooltip trigger="hover">
    <template #trigger>
      <n-icon :color="iconColor" :size="size">
        <component :is="iconComponent" />
      </n-icon>
    </template>
    <span>Level {{ level }}: {{ levelLabel }}</span>
  </n-tooltip>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';
import { NIcon, NTooltip } from 'naive-ui';
import { StarFilled, TrophyFilled, CrownFilled, FireFilled, RocketFilled } from '@vicons/antd';

const props = defineProps<{
  level: number | null | undefined;
  size?: number | string;
}>();

const iconComponent = computed(() => {
  switch (props.level) {
    case 1: return StarFilled;
    case 2: return TrophyFilled;
    case 3: return CrownFilled;
    case 4: return FireFilled;
    case 5: return RocketFilled;
    default: return StarFilled;
  }
});

const iconColor = computed(() => {
  switch (props.level) {
    case 1: return '#b0b0b0'; // gray
    case 2: return '#4caf50'; // green
    case 3: return '#2196f3'; // blue
    case 4: return '#ff9800'; // orange
    case 5: return '#d32f2f'; // red
    default: return '#b0b0b0';
  }
});

const levelLabel = computed(() => {
  switch (props.level) {
    case 1: return 'Newcomer';
    case 2: return 'Verified';
    case 3: return 'Reliable';
    case 4: return 'Trusted';
    case 5: return 'Elite';
    default: return 'Unknown';
  }
});
</script>
