import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import OfferSummaryCard from '../OfferSummaryCard.vue';
import type { BrowseOffer } from '../../types/offer';

// Mock status helpers
vi.mock('../../utils/statusHelpers', () => ({
  getInterestDynamicStatus: vi.fn().mockImplementation((status, transactionStatus, negotiationStatus) => {
    if (!status) return null;
    
    // Mock implementation that matches the actual function behavior
    switch (status) {
      case 'PENDING':
        return { label: '⏳ Pending', type: 'info', pulseEffect: false };
      case 'DECLINED':
        return { label: '❌ Declined', type: 'error', pulseEffect: false };
      case 'ACCEPTED':
        if (transactionStatus === 'COMPLETED') {
          return { label: '✅ Complete', type: 'success', pulseEffect: false };
        }
        if (transactionStatus && transactionStatus.includes('PAYMENT')) {
          return { label: '🔄 In Progress', type: 'warning', pulseEffect: false };
        }
        if (negotiationStatus === 'FINALIZED') {
          return { label: '📋 Terms Agreed', type: 'success', pulseEffect: false };
        }
        if (negotiationStatus && transactionStatus === 'AWAITING_FIRST_PAYER_DESIGNATION') {
          return { label: '💬 Negotiating', type: 'warning', pulseEffect: true };
        }
        return { label: '✅ Accepted', type: 'success', pulseEffect: false };
      default:
        return null;
    }
  })
}));

// Mock Naive UI components
vi.mock('naive-ui', () => ({
  NCard: {
    template: '<div class="n-card"><slot /></div>',
    props: ['title']
  },
  NTag: {
    template: '<span class="n-tag" :class="type"><slot /></span>',
    props: ['type', 'size', 'round']
  },
  NH3: {
    template: '<h3 class="n-h3"><slot /></h3>'
  },
  NButton: {
    template: '<button class="n-button" @click="$emit(\'click\')"><slot /></button>',
    props: ['type', 'size', 'ghost']
  },
  NIcon: {
    template: '<span class="n-icon"><slot /></span>',
    props: ['size']
  },
  NSpace: {
    template: '<div class="n-space"><slot /></div>',
    props: ['vertical', 'align', 'justify']
  }
}));

// Mock router
const mockPush = vi.fn();
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush
  })
}));

// Mock auth store
const mockAuthStore = {
  isAuthenticated: true,
  user: { id: 'test-user-id' }
};

vi.mock('../../stores/auth', () => ({
  useAuthStore: () => mockAuthStore
}));

// Helper function to create mock offers
function createMockOffer(overrides: Partial<BrowseOffer> = {}): BrowseOffer {
  return {
    id: 'test-offer-1',
    type: 'BUY',
    amount: 100,
    baseRate: 1.1,
    adjustmentForLowerRep: 0.05,
    adjustmentForHigherRep: -0.02,
    status: 'ACTIVE',
    createdAt: '2024-01-01T00:00:00Z',
    offerCreatorId: 'user-1',
    offerCreatorUsername: 'testuser',
    offerCreatorReputationLevel: 4.5,
    calculatedApplicableRate: 1.1,
    currentUserHasShownInterest: false,
    currentUserInterestStatus: null,
    transactionStatus: null,
    negotiationStatus: null,
    chatSessionId: null,
    currencyPair: 'USD/EUR',
    ...overrides
  } as BrowseOffer;
}

describe('OfferSummaryCard', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });
  describe('Basic Rendering', () => {
    it('should render offer details correctly', () => {
      const offer = createMockOffer();
      
      const wrapper = mount(OfferSummaryCard, {
        props: { offer }
      });

      expect(wrapper.find('.n-card').exists()).toBe(true);
      expect(wrapper.text()).toContain(offer.type);
      expect(wrapper.text()).toContain(offer.amount.toString());
      expect(wrapper.text()).toContain(offer.currencyPair);
      expect(wrapper.text()).toContain(offer.offerCreatorUsername);
    });

    it('should show reputation correctly', () => {
      const offer = createMockOffer({
        offerCreatorReputationLevel: 4.8
      });
      
      const wrapper = mount(OfferSummaryCard, {
        props: { offer }
      });

      expect(wrapper.text()).toContain('4.8');
    });
  });

  describe('Status Tags', () => {
    it('should not show status tag when user has not shown interest', () => {
      const offer = createMockOffer({
        currentUserHasShownInterest: false,
        currentUserInterestStatus: null
      });
      
      const wrapper = mount(OfferSummaryCard, {
        props: { offer }
      });

      expect(wrapper.find('.status-tag').exists()).toBe(false);
    });

    it('should show Pending status when interest is pending', () => {
      const offer = createMockOffer({
        currentUserHasShownInterest: true,
        currentUserInterestStatus: 'PENDING'
      });
      
      const wrapper = mount(OfferSummaryCard, {
        props: { offer }
      });

      expect(wrapper.find('.status-tag').exists()).toBe(true);
      expect(wrapper.find('.status-tag').text()).toContain('Pending');
    });

    it('should show Declined status when interest is declined', () => {
      const offer = createMockOffer({
        currentUserHasShownInterest: true,
        currentUserInterestStatus: 'DECLINED'
      });
      
      const wrapper = mount(OfferSummaryCard, {
        props: { offer }
      });

      expect(wrapper.find('.status-tag').exists()).toBe(true);
      expect(wrapper.find('.status-tag').text()).toContain('Declined');
    });

    it('should show Accepted status when interest is accepted without transaction', () => {
      const offer = createMockOffer({
        currentUserHasShownInterest: true,
        currentUserInterestStatus: 'ACCEPTED'
      });
      
      const wrapper = mount(OfferSummaryCard, {
        props: { offer }
      });

      expect(wrapper.find('.status-tag').exists()).toBe(true);
      expect(wrapper.find('.status-tag').text()).toContain('Accepted');
    });

    it('should show In Progress status for payment transactions', () => {
      const offer = createMockOffer({
        currentUserHasShownInterest: true,
        currentUserInterestStatus: 'ACCEPTED',
        transactionStatus: 'AWAITING_FIRST_PAYER_PAYMENT'
      });
      
      const wrapper = mount(OfferSummaryCard, {
        props: { offer }
      });

      expect(wrapper.find('.status-tag').exists()).toBe(true);
      expect(wrapper.find('.status-tag').text()).toContain('In Progress');
    });

    it('should show Complete status for completed transactions', () => {
      const offer = createMockOffer({
        currentUserHasShownInterest: true,
        currentUserInterestStatus: 'ACCEPTED',
        transactionStatus: 'COMPLETED'
      });
      
      const wrapper = mount(OfferSummaryCard, {
        props: { offer }
      });

      expect(wrapper.find('.status-tag').exists()).toBe(true);
      expect(wrapper.find('.status-tag').text()).toContain('Complete');
    });
  });

  describe('Button Interactions', () => {
    it('should emit showInterest event when Show Interest button is clicked', () => {
      const offer = createMockOffer({
        currentUserHasShownInterest: false
      });
      
      const wrapper = mount(OfferSummaryCard, {
        props: { offer }
      });

      const showInterestButton = wrapper.find('.show-interest-button');
      showInterestButton.trigger('click');

      expect(wrapper.emitted('showInterest')).toBeTruthy();
      expect(wrapper.emitted('showInterest')?.[0]).toEqual([offer.id]);
    });
  });
});