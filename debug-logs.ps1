#!/usr/bin/env powershell

<#
.SYNOPSIS
    Script to access and manage client debug reports from the containerized backend

.DESCRIPTION
    This script provides easy access to debug reports stored in the Docker volume,
    with options to view recent reports, search logs, and manage log files.

.EXAMPLE
    .\debug-logs.ps1 -Action View -Count 5
    Shows the last 5 debug reports

.EXAMPLE
    .\debug-logs.ps1 -Action Search -Pattern "ERROR"
    Searches for reports containing "ERROR"

.EXAMPLE
    .\debug-logs.ps1 -Action Stats
    Shows log storage statistics
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("View", "Search", "Stats", "Cleanup", "Export")]
    [string]$Action = "View",
    
    [Parameter(Mandatory=$false)]
    [int]$Count = 10,
    
    [Parameter(Mandatory=$false)]
    [string]$Pattern = "",
    
    [Parameter(Mandatory=$false)]
    [string]$ExportPath = ".\debug-reports-export.json"
)

$containerName = "munygo-backend"
$logPath = "/app/logs/client-reports.log"

function Test-ContainerRunning {
    $running = docker ps --filter "name=$containerName" --filter "status=running" --quiet
    return $null -ne $running -and "" -ne $running
}

function Get-DebugReports {
    param([int]$Count)
    
    if (-not (Test-ContainerRunning)) {
        Write-Error "Container '$containerName' is not running. Please start the backend container first."
        return
    }

    Write-Host "📋 Fetching last $Count debug reports..." -ForegroundColor Green
    
    $result = docker exec $containerName sh -c "if [ -f '$logPath' ]; then tail -n $Count '$logPath'; else echo 'No log file found'; fi"
    
    if ($result -eq "No log file found") {
        Write-Host "🔍 No debug reports found yet." -ForegroundColor Yellow
        return
    }    $reports = $result | ForEach-Object { 
        try { 
            $_ | ConvertFrom-Json 
        } catch { 
            Write-Warning "Failed to parse log line: $_"
            $null 
        }
    } | Where-Object { $null -ne $_ }

    Write-Host "`n📊 Found $($reports.Count) reports:" -ForegroundColor Cyan
    
    foreach ($report in $reports) {
        Write-Host "`n🆔 Report ID: $($report.reportId)" -ForegroundColor White
        Write-Host "📅 Timestamp: $($report.timestamp)" -ForegroundColor Gray
        Write-Host "🔗 Session: $($report.sessionId)" -ForegroundColor Gray
        Write-Host "📝 Log Count: $($report.logCount)" -ForegroundColor Gray
        Write-Host "🌐 URL: $($report.currentUrl)" -ForegroundColor Gray
        
        if ($report.userNotes) {
            Write-Host "💬 User Notes: $($report.userNotes)" -ForegroundColor Yellow
        }

        # Show recent errors if any
        $errorLogs = $report.logs | Where-Object { $_.level -eq "ERROR" }
        if ($errorLogs.Count -gt 0) {
            Write-Host "❌ Recent Errors ($($errorLogs.Count)):" -ForegroundColor Red
            $errorLogs | Select-Object -First 3 | ForEach-Object {
                Write-Host "   - $($_.message)" -ForegroundColor Red
            }
        }
        
        Write-Host "─────────────────────────────────────────" -ForegroundColor DarkGray
    }
}

function Search-DebugReports {
    param([string]$Pattern)
    
    if (-not (Test-ContainerRunning)) {
        Write-Error "Container '$containerName' is not running. Please start the backend container first."
        return
    }

    if (-not $Pattern) {
        Write-Error "Please provide a search pattern with -Pattern parameter"
        return
    }

    Write-Host "🔍 Searching for pattern: '$Pattern'..." -ForegroundColor Green
    
    $result = docker exec $containerName sh -c "if [ -f '$logPath' ]; then grep -i '$Pattern' '$logPath'; else echo 'No log file found'; fi"
    
    if ($result -eq "No log file found") {
        Write-Host "🔍 No log file found." -ForegroundColor Yellow
        return
    }

    if (-not $result) {
        Write-Host "🔍 No matches found for pattern: '$Pattern'" -ForegroundColor Yellow
        return
    }    $matches = $result | ForEach-Object { 
        try { 
            $_ | ConvertFrom-Json 
        } catch { 
            $null 
        }
    } | Where-Object { $null -ne $_ }

    Write-Host "`n📊 Found $($matches.Count) matching reports:" -ForegroundColor Cyan
    
    foreach ($match in $matches) {
        Write-Host "`n🆔 Report ID: $($match.reportId)" -ForegroundColor White
        Write-Host "📅 Timestamp: $($match.timestamp)" -ForegroundColor Gray
        Write-Host "🌐 URL: $($match.currentUrl)" -ForegroundColor Gray
        
        # Highlight matching content
        $matchingLogs = $match.logs | Where-Object { $_.message -like "*$Pattern*" -or $_.context -like "*$Pattern*" }
        if ($matchingLogs.Count -gt 0) {
            Write-Host "🎯 Matching log entries:" -ForegroundColor Yellow
            $matchingLogs | ForEach-Object {
                Write-Host "   [$($_.level)] $($_.message)" -ForegroundColor Cyan
            }
        }
        
        Write-Host "─────────────────────────────────────────" -ForegroundColor DarkGray
    }
}

function Get-LogStats {
    if (-not (Test-ContainerRunning)) {
        Write-Error "Container '$containerName' is not running. Please start the backend container first."
        return
    }

    Write-Host "📊 Fetching log statistics..." -ForegroundColor Green
    
    # Get file stats
    $fileStats = docker exec $containerName sh -c "if [ -f '$logPath' ]; then wc -l '$logPath' && ls -lh '$logPath'; else echo 'No log file found'; fi"
    
    if ($fileStats -eq "No log file found") {
        Write-Host "🔍 No log file found yet." -ForegroundColor Yellow
        return
    }

    Write-Host "`n📈 Log File Statistics:" -ForegroundColor Cyan
    Write-Host $fileStats
    
    # Try to get API stats if available
    try {
        $apiStats = Invoke-RestMethod -Uri "http://localhost:3004/api/debug/stats" -Method GET
        if ($apiStats.success) {
            Write-Host "`n📊 API Statistics:" -ForegroundColor Cyan
            Write-Host "Total Reports: $($apiStats.data.reports.totalReports)" -ForegroundColor White
            Write-Host "Log File Size: $($apiStats.data.storage.totalSizeMB) MB" -ForegroundColor White
            Write-Host "Total Files: $($apiStats.data.storage.totalFiles)" -ForegroundColor White
            Write-Host "Log Directory: $($apiStats.data.logDirectory)" -ForegroundColor Gray
        }
    } catch {
        Write-Host "ℹ️ Could not fetch API statistics (backend may not be accessible on localhost:3004)" -ForegroundColor Yellow
    }
}

function Invoke-LogCleanup {
    if (-not (Test-ContainerRunning)) {
        Write-Error "Container '$containerName' is not running. Please start the backend container first."
        return
    }

    Write-Host "🧹 Triggering log cleanup..." -ForegroundColor Green
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:3004/api/debug/cleanup" -Method POST
        if ($response.success) {
            Write-Host "✅ Log cleanup completed successfully" -ForegroundColor Green
        } else {
            Write-Error "❌ Log cleanup failed: $($response.message)"
        }
    } catch {
        Write-Error "❌ Could not trigger log cleanup: $($_.Exception.Message)"
    }
}

function Export-DebugReports {
    param([string]$ExportPath)
    
    if (-not (Test-ContainerRunning)) {
        Write-Error "Container '$containerName' is not running. Please start the backend container first."
        return
    }

    Write-Host "📤 Exporting debug reports to: $ExportPath" -ForegroundColor Green
    
    $result = docker exec $containerName sh -c "if [ -f '$logPath' ]; then cat '$logPath'; else echo 'No log file found'; fi"
    
    if ($result -eq "No log file found") {
        Write-Host "🔍 No log file found to export." -ForegroundColor Yellow
        return
    }    # Parse and re-export as proper JSON array
    $reports = $result | ForEach-Object { 
        try { 
            $_ | ConvertFrom-Json 
        } catch { 
            $null 
        }
    } | Where-Object { $null -ne $_ }

    $reports | ConvertTo-Json -Depth 10 | Out-File -FilePath $ExportPath -Encoding UTF8
    
    Write-Host "✅ Exported $($reports.Count) reports to $ExportPath" -ForegroundColor Green
}

# Main execution
switch ($Action) {
    "View" { Get-DebugReports -Count $Count }
    "Search" { Search-DebugReports -Pattern $Pattern }
    "Stats" { Get-LogStats }
    "Cleanup" { Invoke-LogCleanup }
    "Export" { Export-DebugReports -ExportPath $ExportPath }
}
