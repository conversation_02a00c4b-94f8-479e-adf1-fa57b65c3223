# Design System & Component Library

## 🎨 Mobile-First Component System

### Component Hierarchy
```
Atoms (Basic elements)
├── Buttons
├── Inputs  
├── Icons
├── Typography
└── Colors

Molecules (Simple combinations)
├── Form Fields
├── Card Headers
├── Navigation Items
└── Status Indicators

Organisms (Complex components)
├── Offer Cards
├── Transaction Flows
├── Chat Interface
└── Navigation Bars

Templates (Page layouts)
├── Home Layout
├── Form Layout
├── List Layout
└── Detail Layout
```

## 🧱 Atomic Components

### Button System
```vue
<!-- Primary Button - Main actions -->
<template>
  <button class="btn btn-primary" :class="{ 'btn-loading': loading }">
    <spinner v-if="loading" />
    <slot v-else />
  </button>
</template>

<style scoped>
.btn {
  min-height: 56px;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  width: 100%;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #3B82F6;
  color: white;
}

.btn-primary:active {
  background: #2563EB;
  transform: scale(0.98);
}

.btn-loading {
  pointer-events: none;
  opacity: 0.7;
}
</style>
```

### Input System
```vue
<!-- Mobile-optimized input -->
<template>
  <div class="input-group">
    <label class="input-label">{{ label }}</label>
    <input 
      class="input-field"
      :type="type"
      :placeholder="placeholder"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
    />
    <div v-if="error" class="input-error">{{ error }}</div>
  </div>
</template>

<style scoped>
.input-group {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.input-field {
  width: 100%;
  min-height: 56px;
  padding: 16px;
  font-size: 16px; /* Prevents iOS zoom */
  border: 2px solid #E5E7EB;
  border-radius: 8px;
  background: white;
  transition: border-color 0.2s ease;
}

.input-field:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-error {
  color: #EF4444;
  font-size: 14px;
  margin-top: 8px;
}
</style>
```

## 🏷️ Molecular Components

### Offer Card Component
```vue
<template>
  <div class="offer-card" @click="handleClick">
    <div class="offer-header">
      <div class="user-info">
        <div class="avatar">{{ user.name[0] }}</div>
        <div class="user-details">
          <div class="user-name">{{ user.name }}</div>
          <div class="user-rating">
            <star-rating :rating="user.rating" />
            <span class="rating-count">({{ user.reviews }})</span>
          </div>
        </div>
      </div>
      <div class="offer-status" :class="statusClass">
        {{ statusText }}
      </div>
    </div>
    
    <div class="offer-details">
      <div class="amount-info">
        <div class="primary-amount">${{ offer.amount }}</div>
        <div class="secondary-amount">{{ formatToman(offer.tomanAmount) }}﷼</div>
      </div>
      <div class="offer-meta">
        <div class="distance">📍 {{ offer.distance }}km</div>
        <div class="time">🕐 {{ formatTime(offer.updatedAt) }}</div>
      </div>
    </div>
    
    <div class="offer-actions">
      <button class="connect-btn" @click.stop="connect">
        CONNECT
      </button>
    </div>
  </div>
</template>

<style scoped>
.offer-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.offer-card:active {
  transform: scale(0.98);
}

.offer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #3B82F6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
}

.user-name {
  font-weight: 600;
  font-size: 16px;
  color: #111827;
}

.user-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6B7280;
}

.offer-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-available {
  background: #D1FAE5;
  color: #065F46;
}

.offer-details {
  margin-bottom: 16px;
}

.amount-info {
  margin-bottom: 12px;
}

.primary-amount {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

.secondary-amount {
  font-size: 16px;
  color: #6B7280;
}

.offer-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #6B7280;
}

.connect-btn {
  width: 100%;
  min-height: 48px;
  background: #3B82F6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
}

.connect-btn:active {
  background: #2563EB;
}
</style>
```

### Transaction Status Card
```vue
<template>
  <div class="transaction-card" :class="statusClass">
    <div class="transaction-header">
      <div class="status-indicator" :class="indicatorClass">
        <component :is="statusIcon" />
      </div>
      <div class="transaction-title">{{ statusTitle }}</div>
    </div>
    
    <div class="transaction-details">
      <div class="amount-display">
        <div class="amount">${{ transaction.amount }}</div>
        <div class="exchange-rate">@ {{ transaction.rate }}﷼</div>
      </div>
      
      <div class="participants">
        <div class="participant">{{ transaction.buyer }}</div>
        <div class="exchange-arrow">⇄</div>
        <div class="participant">{{ transaction.seller }}</div>
      </div>
    </div>
    
    <div class="transaction-actions">
      <button 
        v-if="primaryAction" 
        class="primary-action-btn"
        @click="handlePrimaryAction"
      >
        {{ primaryAction.text }}
      </button>
      
      <div v-if="timer" class="timer-display">
        {{ formatTimer(timer) }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.transaction-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin: 16px;
  border-left: 4px solid #E5E7EB;
}

.transaction-card.pending {
  border-left-color: #F59E0B;
}

.transaction-card.active {
  border-left-color: #3B82F6;
}

.transaction-card.completed {
  border-left-color: #10B981;
}

.transaction-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.status-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.indicator-pending {
  background: #FEF3C7;
  color: #D97706;
}

.indicator-active {
  background: #DBEAFE;
  color: #2563EB;
}

.indicator-completed {
  background: #D1FAE5;
  color: #059669;
}

.transaction-title {
  font-weight: 600;
  font-size: 16px;
  color: #111827;
}

.amount-display {
  text-align: center;
  margin-bottom: 16px;
}

.amount {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
}

.exchange-rate {
  font-size: 14px;
  color: #6B7280;
}

.participants {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.participant {
  padding: 8px 16px;
  background: #F3F4F6;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.exchange-arrow {
  font-size: 20px;
  color: #6B7280;
}

.primary-action-btn {
  width: 100%;
  min-height: 52px;
  background: #3B82F6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
}

.timer-display {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #F59E0B;
}
</style>
```

## 🗂️ Organism Components

### Mobile Navigation Bar
```vue
<template>
  <nav class="mobile-nav">
    <div class="nav-container">
      <div class="nav-header">
        <button class="back-btn" v-if="showBack" @click="goBack">
          <ChevronLeftIcon />
        </button>
        <h1 class="nav-title">{{ title }}</h1>
        <div class="nav-actions">
          <slot name="actions" />
        </div>
      </div>
    </div>
    
    <!-- Bottom Tab Navigation -->
    <div class="bottom-tabs" v-if="showTabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.id"
        class="tab-item"
        :class="{ active: currentTab === tab.id }"
        @click="selectTab(tab.id)"
      >
        <component :is="tab.icon" class="tab-icon" />
        <span class="tab-label">{{ tab.label }}</span>
      </button>
    </div>
  </nav>
</template>

<style scoped>
.mobile-nav {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  border-bottom: 1px solid #E5E7EB;
}

.nav-container {
  padding: 12px 16px;
}

.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 44px;
}

.back-btn {
  width: 44px;
  height: 44px;
  border: none;
  background: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-btn:active {
  background: #F3F4F6;
}

.nav-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  flex: 1;
  text-align: center;
}

.bottom-tabs {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #E5E7EB;
  display: flex;
  padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 4px;
  border: none;
  background: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

.tab-item.active {
  color: #3B82F6;
}

.tab-item:not(.active) {
  color: #6B7280;
}

.tab-icon {
  width: 24px;
  height: 24px;
}

.tab-label {
  font-size: 12px;
  font-weight: 500;
}
</style>
```

### Chat Interface Component
```vue
<template>
  <div class="chat-interface">
    <div class="chat-header">
      <div class="chat-user-info">
        <div class="user-avatar">{{ chatUser.name[0] }}</div>
        <div class="user-details">
          <div class="user-name">{{ chatUser.name }}</div>
          <div class="user-status" :class="{ online: chatUser.online }">
            {{ chatUser.online ? 'Online' : 'Offline' }}
          </div>
        </div>
      </div>
      <button class="chat-menu-btn" @click="showMenu">
        <DotsVerticalIcon />
      </button>
    </div>
    
    <div class="messages-container" ref="messagesContainer">
      <div 
        v-for="message in messages" 
        :key="message.id"
        class="message"
        :class="{ 'message-own': message.isOwn }"
      >
        <div class="message-bubble">
          <div class="message-text">{{ message.text }}</div>
          <div class="message-time">{{ formatTime(message.timestamp) }}</div>
        </div>
      </div>
    </div>
    
    <div class="chat-input-container">
      <div class="input-wrapper">
        <input 
          v-model="newMessage"
          class="message-input"
          placeholder="Type a message..."
          @keyup.enter="sendMessage"
        />
        <button 
          class="send-btn"
          @click="sendMessage"
          :disabled="!newMessage.trim()"
        >
          <SendIcon />
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #F9FAFB;
}

.chat-header {
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #3B82F6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.user-name {
  font-weight: 600;
  color: #111827;
}

.user-status {
  font-size: 12px;
  color: #6B7280;
}

.user-status.online {
  color: #10B981;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message {
  display: flex;
}

.message-own {
  justify-content: flex-end;
}

.message-bubble {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  background: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-own .message-bubble {
  background: #3B82F6;
  color: white;
}

.message-text {
  font-size: 16px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
}

.chat-input-container {
  background: white;
  padding: 12px 16px calc(12px + env(safe-area-inset-bottom));
  border-top: 1px solid #E5E7EB;
}

.input-wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
}

.message-input {
  flex: 1;
  min-height: 44px;
  padding: 12px 16px;
  border: 2px solid #E5E7EB;
  border-radius: 22px;
  font-size: 16px;
  background: #F9FAFB;
}

.message-input:focus {
  outline: none;
  border-color: #3B82F6;
  background: white;
}

.send-btn {
  width: 44px;
  height: 44px;
  background: #3B82F6;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
```

## 📱 Layout Templates

### Mobile Page Layout
```vue
<template>
  <div class="mobile-layout">
    <!-- Fixed Header -->
    <header class="layout-header" v-if="!hideHeader">
      <slot name="header">
        <mobile-nav-bar :title="title" :show-back="showBack" />
      </slot>
    </header>
    
    <!-- Scrollable Content -->
    <main class="layout-content" :class="{ 'with-tabs': showBottomTabs }">
      <slot />
    </main>
    
    <!-- Fixed Bottom Navigation -->
    <footer class="layout-footer" v-if="showBottomTabs">
      <slot name="bottom-tabs">
        <bottom-tab-navigation />
      </slot>
    </footer>
    
    <!-- Floating Action Button -->
    <div class="fab-container" v-if="$slots.fab">
      <slot name="fab" />
    </div>
    
    <!-- Loading Overlay -->
    <div class="loading-overlay" v-if="loading">
      <div class="loading-spinner" />
    </div>
  </div>
</template>

<style scoped>
.mobile-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #F9FAFB;
}

.layout-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
}

.layout-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: env(safe-area-inset-bottom);
}

.layout-content.with-tabs {
  padding-bottom: calc(80px + env(safe-area-inset-bottom));
}

.layout-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 90;
}

.fab-container {
  position: fixed;
  bottom: calc(90px + env(safe-area-inset-bottom));
  right: 16px;
  z-index: 95;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
```

## 🎯 Component Usage Guidelines

### Implementation Priority
1. **Atoms**: Basic buttons, inputs, typography (Week 1)
2. **Molecules**: Form components, cards (Week 2)
3. **Organisms**: Navigation, complex interactions (Week 3)
4. **Templates**: Page layouts, responsive behavior (Week 4)

### Mobile-First Standards
- All components must work on 320px screens
- Touch targets minimum 44px
- Text minimum 16px to prevent iOS zoom
- Support for safe area insets (iPhone notch)
- Dark mode compatibility

### Testing Requirements
- Test on real devices, not just browser dev tools
- Verify touch interactions work properly
- Check performance on slower devices
- Validate accessibility with screen readers

---

*This component system provides the foundation for consistent, mobile-optimized user interfaces throughout MUNygo.*
