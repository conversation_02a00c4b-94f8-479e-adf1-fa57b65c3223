// TransactionInfo type for chat transaction card

// Keep TransactionInfo and TransactionDetails for now if they serve other purposes,
// but the new 'Transaction' interface below is primary for the transaction flow.

export interface TransactionInfo { // This seems like a subset of TransactionDetails, can be merged or kept for specific UI components
  offerType: 'BUY' | 'SELL';
  amount: number;
  exchangeRate: number;
  currencyPair: string;
  cadSellerId?: string; // Added: ID of the user providing CAD
  finalCadAmount?: number | null; // Added: The final CAD amount for the transaction
  finalAssetAmount?: number | null; // Added: The final asset amount for the transaction
  finalAssetCurrency?: string | null; // Added: The currency code of the asset (e.g., "IRR")
  offerCreator: {
    id: string;
    username: string;
    reputationLevel: number | null;
  };
  otherUser: {
    id: string;
    username: string;
    reputationLevel: number | null;
  };
}

// Represents the detailed transaction information fetched from the backend
// Corresponds to the payload from GET /api/chat/:chatSessionId/transaction
export interface TransactionDetails {
  offerId: string;
  chatSessionId: string;
  offerType: 'BUY' | 'SELL';
  amount: number; // Original offer amount
  exchangeRate: number;
  currencyPair: string; // e.g., "BTC/CAD"
  cadSellerId?: string;
  finalCadAmount?: number | null; // Added: The final CAD amount for the transaction
  finalAssetAmount?: number | null; // Added: The final asset amount for the transaction
  finalAssetCurrency?: string | null; // Added: The currency code of the asset (e.g., "BTC")
  offerCreator: {
    id: string;
    username: string;
    reputationLevel: number | null;
  };
  otherUser: {
    id: string;
    username: string;
    reputationLevel: number | null;
  };
  transactionStatus: TransactionStatusEnum; // Changed from string to use the aligned enum
  creatorAgreedTerms: boolean;
  interestedPartyAgreedTerms: boolean;
  creatorDeclaredPayment: boolean;
  interestedPartyConfirmedCreatorPayment: boolean;
  interestedPartyDeclaredPayment: boolean;
  creatorConfirmedInterestedPartyPayment: boolean;
  updatedAt: string; // ISO string for when the transaction entity was last updated
}

// This interface might be redundant if the main Transaction interface (defined below) covers all status updates from sockets and API calls.
// For now, we keep it but ensure it uses the correct enum.
export interface TransactionStatusUpdate { 
  id: string; // chatSessionId
  userOneId?: string; // From backend ChatSession model, useful for determining roles
  userTwoId?: string; // From backend ChatSession model, useful for determining roles
  offerId: string;
  transactionStatus: TransactionStatusEnum; // Uses the aligned enum
  creatorAgreedTerms: boolean;
  interestedPartyAgreedTerms: boolean;
  creatorDeclaredPayment: boolean;
  interestedPartyConfirmedCreatorPayment: boolean;
  interestedPartyDeclaredPayment: boolean;
  creatorConfirmedInterestedPartyPayment: boolean;
  updatedAt: string; // ISO string
}

// Enum for transaction status values, aligned with backend Prisma schema and TransactionFlowCard.vue usage
export enum TransactionStatusEnum {
  PENDING_AGREEMENT = 'PENDING_AGREEMENT', // May be deprecated or represent pre-flow state
  AWAITING_FIRST_PAYER_DESIGNATION = 'AWAITING_FIRST_PAYER_DESIGNATION',
  AWAITING_FIRST_PAYER_PAYMENT = 'AWAITING_FIRST_PAYER_PAYMENT',
  AWAITING_SECOND_PAYER_CONFIRMATION = 'AWAITING_SECOND_PAYER_CONFIRMATION',
  AWAITING_SECOND_PAYER_PAYMENT = 'AWAITING_SECOND_PAYER_PAYMENT',
  AWAITING_FIRST_PAYER_CONFIRMATION = 'AWAITING_FIRST_PAYER_CONFIRMATION',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  DISPUTED = 'DISPUTED',
}

export interface UserProfileBasic {
  id: string;
  username?: string | null;
  displayName?: string | null;
  // Add other relevant basic profile fields if needed
}

/**
 * Comprehensive Transaction interface for frontend state management and UI components.
 * Aligns with the backend Prisma model and Zod schema, including fields populated by services.
 */
export interface Transaction {
  id: string; // Transaction CUID from backend Transaction model
  offerId: string | null;
  chatSessionId: string;
  status: TransactionStatusEnum;
  logs?: TransactionTimelineLogEntry[]; // Added logs property

  // Currency A provider details
  currencyAProviderId: string;
  currencyAProvider?: UserProfileBasic | null; // For displaying username/info
  amountA: number;
  currencyA: string;

  // Currency B provider details
  currencyBProviderId: string;
  currencyBProvider?: UserProfileBasic | null; // For displaying username/info
  amountB: number;
  currencyB: string;

  // Timestamps for terms agreement (backend may still have these, frontend use TBD)
  termsAgreementTimestampPayer1?: string | null; 
  termsAgreementTimestampPayer2?: string | null;

  // First payer designation
  agreedFirstPayerId?: string | null;
  firstPayerDesignationTimestamp?: string | null;

  // Payment tracking for the first payer
  paymentExpectedByPayer1?: string | null; // Deadline for first payer's payment
  paymentDeclaredAtPayer1?: string | null;
  paymentTrackingNumberPayer1?: string | null;

  // Confirmation of the first payment
  firstPaymentConfirmedByPayer2At?: string | null;

  // Payment tracking for the second payer
  paymentExpectedByPayer2?: string | null; // Deadline for second payer's payment
  paymentDeclaredAtPayer2?: string | null;
  paymentTrackingNumberPayer2?: string | null;

  // Confirmation of the second payment
  secondPaymentConfirmedByPayer1At?: string | null;

  // Cancellation and Dispute
  cancellationReason?: string | null;
  cancelledByUserId?: string | null; // Added
  disputeReason?: string | null;
  disputedByUserId?: string | null;   // Added
  disputeResolvedAt?: string | null;
  disputeResolutionNotes?: string | null;

  createdAt: string;
  updatedAt: string;

  // Optional deadline for the current action, potentially set by backend service logic
  actionDeadline?: string | null;

  // First Payer ID
  firstPayerId?: string | null; // Add this property
}

// Interface for transaction timeline log entry
interface TransactionTimelineLogEntry {
  timestamp: string;
  message: string;
  status?: TransactionStatusEnum; // Optional: if the log entry corresponds to a status change
  actor?: string; // Optional: e.g., 'system', 'userA', 'userB'
}

// --- API Payload Types ---

export interface AgreeToTermsPayload {
  // Backend might not need a payload if it derives user from auth context
  // If firstPayerId is decided at this stage by one of the users:
  // agreedFirstPayerId?: string; 
}

export interface DesignateFirstPayerPayload {
  designatedPayerId: string;
}

export interface DeclarePaymentPayload {
  trackingNumber?: string;
}

export interface CancelTransactionPayload {
  reason: string;
}

export interface DisputeTransactionPayload {
  reason: string;
}

/**
 * Defines which actions are available to the current user for a transaction.
 * You might also want a type for the actions a user can take based on the current state
 */
export interface TransactionUserActions {
  canAgreeToTerms: boolean;
  canDeclarePayment: boolean;
  canConfirmPayment: boolean;
  canCancel: boolean; // Added for cancel action
  // Add other actions as needed, e.g., canRaiseDispute
}

// Helper type to combine transaction details with current user's role and available actions
export interface FullTransactionContext extends TransactionDetails {
  currentUserRole: 'offerCreator' | 'otherUser' | 'observer'; // 'observer' if not part of transaction but viewing (e.g. admin)
  actions: TransactionUserActions;
  // Include other relevant frontend-specific state if necessary
}

// Based on backend Prisma schema and TransactionStatusUpdatePayload in backend socketEvents.ts

// If you have a type for the raw API response that includes the nested providers,
// you might define it separately, e.g.:
export interface RawTransactionApiResponse extends Omit<Transaction, 'currencyAProviderUsername' | 'currencyBProviderUsername'> {
  currencyAProvider?: { id: string; username: string | null };
  currencyBProvider?: { id: string; username: string | null };
}
