import { ref, onUnmounted } from 'vue';

export function useIntersectionObserver(
  callback: (isVisible: boolean) => void,
  options: IntersectionObserverInit = {}
) {
  const targetRef = ref<HTMLElement | null>(null);
  const isVisible = ref<boolean>(false);
  let observer: IntersectionObserver | null = null;

  const observe = (element: HTMLElement) => {
    if (observer) {
      observer.disconnect();
    }

    observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        const visible = entry.isIntersecting;
        isVisible.value = visible;
        callback(visible);
      },
      {
        root: null, // viewport
        rootMargin: '0px',
        threshold: 0.1, // Consider visible when 10% is showing
        ...options,
      }
    );

    observer.observe(element);
    targetRef.value = element;
  };

  const unobserve = () => {
    if (observer && targetRef.value) {
      observer.unobserve(targetRef.value);
    }
  };

  const disconnect = () => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
  };

  onUnmounted(() => {
    disconnect();
  });

  return {
    targetRef,
    isVisible,
    observe,
    unobserve,
    disconnect,
  };
}
