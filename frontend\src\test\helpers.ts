import { mount } from '@vue/test-utils';
// --- Remove NP, ensure all used components are imported ---
import { NButton, NInput, NForm, NFormItem, NAlert, NSpin, NCard, NSpace, NIcon, NConfigProvider, NMessageProvider, NTag, NDescriptions, NDescriptionsItem, NH3, NDivider } from 'naive-ui';

// Helper to wrap component with Naive UI providers
export const mountWithNaiveUI = (component: any, options: any = {}) => {
  // --- Simplified Pinia Handling ---
  // Assume Pinia instance is correctly provided in options.global.plugins by the caller
  // No need to create a default one here.

  if (!options.global) {
    options.global = {};
  }

  // Ensure plugins array exists
  if (!options.global.plugins) {
    options.global.plugins = [];
    // Consider throwing an error if Pinia is expected but not provided,
    // or let the test fail naturally if Pinia is required by the component.
    // console.warn('mountWithNaiveUI expects a Pinia instance in options.global.plugins');
  }

  // Add Naive UI components
  if (!options.global.components) {
    options.global.components = {};
  }

  Object.assign(options.global.components, {
    NButton, NInput, NForm, NFormItem, NAlert, NSpin, NCard, NSpace, NIcon,
    NConfigProvider, NMessageProvider, NTag, NDescriptions, NDescriptionsItem, // <-- Ensure NDescriptionsItem is here
    NH3, NDivider // <-- Removed NP
  });

  // Create a MessageProvider wrapper with a config provider
  // The component being mounted will use the Pinia instance provided in options.global.plugins
  return mount(
    {
      template: `
        <n-config-provider>
          <n-message-provider>
            <component :is="component" v-bind="props" />
          </n-message-provider>
        </n-config-provider>
      `,
      props: {
        component: {
          type: Object,
          default: () => component // Use default function for objects/arrays
        },
        props: {
          type: Object,
          default: () => ({})
        }
      },
      // Components needed by the wrapper itself (if any) could go here
      // components: { NConfigProvider, NMessageProvider }
    },
    options // Pass the original options (containing the correct Pinia instance)
  );
};
