// Set environment variable BEFORE any imports that might use it
const TEST_JWT_SECRET = 'test-secret-key-12345';
process.env.JWT_SECRET = TEST_JWT_SECRET;

// Import types and test utilities first
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import type { MockInstance } from 'vitest';
import { sign } from 'hono/jwt';
import type { Context as HonoContext, Next } from 'hono';
import type { AuthVariables, JwtPayload } from '../../middleware/auth';

// Mock the hono/jwt module
vi.mock('hono/jwt', async () => {
  const actual = await vi.importActual<typeof import('hono/jwt')>('hono/jwt');
  return {
    ...actual,
    verify: vi.fn(),
    sign: actual.sign, // Keep the actual sign function for test token generation
  };
});

// Helper to create a mock context and next function
const createMockContext = (headers: Record<string, string> = {}) => {
  const contextData: Record<string, any> = {};

  const mockReq = {
    header: (key: string): string | undefined => headers[key],
  };

  const mockCtx = {
    req: mockReq,
    json: vi.fn((data: any, status: number) => 
      new Response(JSON.stringify(data), { 
        status, 
        headers: { 'Content-Type': 'application/json' } 
      })
    ),
    set: vi.fn((key: string, value: any) => { contextData[key] = value; }),
    get: vi.fn((key: string) => contextData[key]),
  };

  const next = vi.fn() as Next;

  return { 
    c: mockCtx as unknown as HonoContext<{ Variables: AuthVariables }>, 
    next 
  };
};

describe('Auth Middleware', () => {
  let authMiddleware: (c: HonoContext<{ Variables: AuthVariables }>, next: Next) => Promise<Response | void>;
  let validPayload: JwtPayload;
  let validToken: string;
  let expiredToken: string;
  let verifyMock: MockInstance;

  beforeEach(async () => {
    vi.resetModules();
    process.env.JWT_SECRET = TEST_JWT_SECRET;

    const { verify } = await import('hono/jwt');
    verifyMock = vi.mocked(verify);

    const middlewareModule = await import('../../middleware/auth');
    authMiddleware = middlewareModule.authMiddleware;

    const now = Math.floor(Date.now() / 1000);
    validPayload = {
      userId: 'user-123',
      email: '<EMAIL>',
      iat: now,
      exp: now + 3600,
    };

    validToken = await sign(validPayload, TEST_JWT_SECRET);
    const expiredPayload = { ...validPayload, exp: now - 3600 };
    expiredToken = await sign(expiredPayload, TEST_JWT_SECRET);

    // Default mock implementation
    verifyMock.mockImplementation(async (token: string, secret: string) => {
      if (secret !== TEST_JWT_SECRET) {
        throw new Error(`Invalid secret received: ${secret}`);
      }
      if (token === validToken) {
        return validPayload;
      }
      if (token === expiredToken) {
        throw new Error('Token expired');
      }
      throw new Error('Invalid token');
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // Test cases start here
  it('should return 401 if Authorization header is missing', async () => {
    const { c, next } = createMockContext();
    const res = await authMiddleware(c, next);

    expect(res?.status).toBe(401);
    expect(await res?.json()).toEqual({ error: 'Unauthorized: Missing or invalid token' });
    expect(next).not.toHaveBeenCalled();
    expect(c.get('jwtPayload')).toBeUndefined();
  });

  it('should return 401 if Authorization header does not start with Bearer', async () => {
    const { c, next } = createMockContext({ Authorization: 'Basic somecreds' });
    const res = await authMiddleware(c, next);

    expect(res?.status).toBe(401);
    expect(await res?.json()).toEqual({ error: 'Unauthorized: Missing or invalid token' });
    expect(next).not.toHaveBeenCalled();
    expect(c.get('jwtPayload')).toBeUndefined();
  });

  it('should return 401 if Bearer token is missing', async () => {
    const { c, next } = createMockContext({ Authorization: 'Bearer ' });
    verifyMock.mockImplementationOnce(async () => {
      throw new Error('Invalid token format');
    });

    const res = await authMiddleware(c, next);

    expect(res?.status).toBe(401);
    expect(await res?.json()).toEqual({ error: 'Unauthorized: Invalid token' });
    expect(next).not.toHaveBeenCalled();
    expect(c.get('jwtPayload')).toBeUndefined();
  });

  it('should return 401 if token verification fails', async () => {
    const invalidToken = 'invalid.token.string';
    verifyMock.mockImplementationOnce(async () => {
      throw new Error('Invalid signature');
    });

    const { c, next } = createMockContext({ Authorization: `Bearer ${invalidToken}` });
    const res = await authMiddleware(c, next);

    expect(res?.status).toBe(401);
    expect(await res?.json()).toEqual({ error: 'Unauthorized: Invalid token' });
    expect(next).not.toHaveBeenCalled();
    expect(verifyMock).toHaveBeenCalledWith(invalidToken, TEST_JWT_SECRET);
    expect(c.get('jwtPayload')).toBeUndefined();
  });

  it('should return 401 if token is expired (verify throws)', async () => {
    verifyMock.mockImplementationOnce(async (token: string, secret: string) => {
      if (token === expiredToken) throw new Error('Token expired');
      throw new Error('Unexpected token');
    });

    const { c, next } = createMockContext({ Authorization: `Bearer ${expiredToken}` });
    const res = await authMiddleware(c, next);

    expect(res?.status).toBe(401);
    expect(await res?.json()).toEqual({ error: 'Unauthorized: Invalid token' });
    expect(next).not.toHaveBeenCalled();
    expect(verifyMock).toHaveBeenCalledWith(expiredToken, TEST_JWT_SECRET);
    expect(c.get('jwtPayload')).toBeUndefined();
  });

  it('should return 401 if token is expired (manual check)', async () => {
    verifyMock.mockImplementationOnce(async () => ({
      ...validPayload,
      exp: Math.floor(Date.now() / 1000) - 3600
    }));

    const { c, next } = createMockContext({ Authorization: `Bearer ${expiredToken}` });
    const res = await authMiddleware(c, next);

    expect(res?.status).toBe(401);
    expect(await res?.json()).toEqual({ error: 'Unauthorized: Token expired' });
    expect(next).not.toHaveBeenCalled();
    expect(verifyMock).toHaveBeenCalledWith(expiredToken, TEST_JWT_SECRET);
    expect(c.get('jwtPayload')).toBeUndefined();
  });

  it('should return 401 if verified payload is missing userId', async () => {
    verifyMock.mockImplementationOnce(async () => ({
      email: '<EMAIL>'
    }));

    const { c, next } = createMockContext({ Authorization: `Bearer ${validToken}` });
    const res = await authMiddleware(c, next);

    expect(res?.status).toBe(401);
    expect(await res?.json()).toEqual({ error: 'Unauthorized: Invalid token payload' });
    expect(next).not.toHaveBeenCalled();
    expect(verifyMock).toHaveBeenCalledWith(validToken, TEST_JWT_SECRET);
    expect(c.get('jwtPayload')).toBeUndefined();
  });

  it('should return 401 if verified payload is missing email', async () => {
    verifyMock.mockImplementationOnce(async () => ({
      userId: 'user-123'
    }));

    const { c, next } = createMockContext({ Authorization: `Bearer ${validToken}` });
    const res = await authMiddleware(c, next);

    expect(res?.status).toBe(401);
    expect(await res?.json()).toEqual({ error: 'Unauthorized: Invalid token payload' });
    expect(next).not.toHaveBeenCalled();
    expect(verifyMock).toHaveBeenCalledWith(validToken, TEST_JWT_SECRET);
    expect(c.get('jwtPayload')).toBeUndefined();
  });

  it('should call next() and set jwtPayload if token is valid', async () => {
    const { c, next } = createMockContext({ Authorization: `Bearer ${validToken}` });
    const res = await authMiddleware(c, next);

    expect(res).toBeUndefined();
    expect(next).toHaveBeenCalledTimes(1);
    expect(verifyMock).toHaveBeenCalledWith(validToken, TEST_JWT_SECRET);
    expect(c.get('jwtPayload')).toEqual(validPayload);
  });
});
