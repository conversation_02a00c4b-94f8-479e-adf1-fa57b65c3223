# MUNygo Development Startup Script
# Run this script at the beginning of each development session

Write-Host "Starting MUNygo Development Environment..." -ForegroundColor Green
Write-Host ""

# Step 1: Start PostgreSQL Container
Write-Host "Starting PostgreSQL Database..." -ForegroundColor Yellow
 if (Get-Command 'docker-compose' -ErrorAction SilentlyContinue) {
     docker-compose -f docker-compose.dev.yml up -d postgres-dev
 } else {
     docker compose -f docker-compose.dev.yml up -d postgres-dev
 }

if ($LASTEXITCODE -eq 0) {
    Write-Host "PostgreSQL started successfully" -ForegroundColor Green
} else {
    Write-Host "Failed to start PostgreSQL. Make sure Docker Desktop is running." -ForegroundColor Red
    Write-Host "Please start Docker Desktop and try again." -ForegroundColor Yellow
    exit 1
}

# Step 2: Wait for PostgreSQL to be ready
Write-Host "Waiting for PostgreSQL to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# Check if PostgreSQL is responding
$retries = 0
$maxRetries = 10
$ready = $false

while (-not $ready -and $retries -lt $maxRetries) {
    try {
        $container = docker compose -f docker-compose.dev.yml ps -q postgres-dev
 if ($container) {
     $null = docker exec $container pg_isready -U munygo_user -d munygo_dev 2>$null
 } else {
     Write-Host "Unable to find postgres-dev container" -ForegroundColor Red
     break
 }
        if ($LASTEXITCODE -eq 0) {
            $ready = $true
            Write-Host "PostgreSQL is ready!" -ForegroundColor Green
        } else {
            $retries++
            Start-Sleep -Seconds 2
        }
    } catch {
        $retries++
        Start-Sleep -Seconds 2
    }
}

if (-not $ready) {
    Write-Host "PostgreSQL is not responding. Please check Docker logs." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Development Environment Ready!" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Open a new terminal and run: cd backend; npm run dev" -ForegroundColor White
Write-Host "2. Open another terminal and run: cd frontend; npm run dev" -ForegroundColor White
Write-Host ""
Write-Host "Your services will be available at:" -ForegroundColor Cyan
Write-Host "- Backend API: http://localhost:3000" -ForegroundColor White
Write-Host "- Frontend:    http://localhost:5173" -ForegroundColor White
Write-Host "- PostgreSQL:  localhost:5433" -ForegroundColor White
Write-Host ""
Write-Host "Useful Commands:" -ForegroundColor Cyan
Write-Host "- Stop PostgreSQL: docker-compose -f docker-compose.dev.yml down" -ForegroundColor White
Write-Host "- View DB: docker exec -it munygo-postgres-dev psql -U munygo_user -d munygo_dev" -ForegroundColor White
Write-Host ""
