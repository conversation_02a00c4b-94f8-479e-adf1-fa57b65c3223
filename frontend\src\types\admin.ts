/**
 * TypeScript type definitions for admin debug dashboard
 */

// Log entry structure
export interface LogEntry {
  timestamp: string;
  level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';
  message: string;
  context?: Record<string, any>;
  url?: string;
  stackTrace?: string;
}

// Diagnostic data structure for enhanced debugging
export interface DiagnosticData {
  connectionStatus: {
    isConnected: boolean;
    connectionQuality: string;
    connectionStatus: string;
    transportType: string;
    reconnectAttempts: number;
    isReconnecting: boolean;
    lastDisconnectReason: string | null;
    socketId?: string;
    socketConnected?: boolean;
  };
  piniaStoreSnapshot: Record<string, any>;
  captureTimestamp: string;
}

// Debug report status types
export type DebugReportStatus = 'NOT_REVIEWED' | 'IN_PROGRESS' | 'COMPLETED' | 'ARCHIVED' | 'DUPLICATE' | 'WONT_FIX';
export type SimplifiedStatus = 'new' | 'todo' | 'done';

// Status mapping and display utilities
export const STATUS_MAPPING: Record<DebugReportStatus, SimplifiedStatus> = {
  NOT_REVIEWED: 'new',
  IN_PROGRESS: 'todo',
  COMPLETED: 'done',
  ARCHIVED: 'done',
  DUPLICATE: 'done',
  WONT_FIX: 'done'
};

export const SIMPLIFIED_STATUS_LABELS: Record<SimplifiedStatus, string> = {
  new: 'New/Unread',
  todo: 'To Do',
  done: 'Done'
};

export const SIMPLIFIED_STATUS_COLORS: Record<SimplifiedStatus, string> = {
  new: '#ef4444', // Red for new/unread
  todo: '#f59e0b', // Orange for to do
  done: '#10b981'  // Green for done
};

// Parsed report structure (matches backend ParsedReport)
export interface ParsedReport {
  reportId: string;
  timestamp: string;
  serverReceivedAt: string;
  clientTimestamp: string;
  sessionId: string;
  userAgent?: string;
  currentUrl?: string;
  userNotes?: string | null;
  reportType?: string;
  reportSeverity?: string;
  reportTitle?: string;
  reportDescription?: string;
  stepsToReproduce?: string;
  expectedBehavior?: string;
  actualBehavior?: string;
  status?: DebugReportStatus;  tags?: Array<{
    tagId?: string;
    tagName?: string;
    origin: 'PREDEFINED' | 'AI_SUGGESTED' | 'USER_DEFINED';
  }>;
  hasTags?: boolean;
  logCount: number;
  logs: LogEntry[];
  // User identification fields
  userId?: string;
  userEmail?: string;
  username?: string;
  // Enhanced diagnostic data
  diagnosticData?: DiagnosticData;
}

// API query parameters
export interface GetReportsQuery {
  page?: number;
  limit?: number;
  sortBy?: 'serverReceivedAt' | 'clientTimestamp' | 'reportType' | 'reportSeverity' | 'reportTitle' | 'logCount';
  sortOrder?: 'asc' | 'desc';
  filterByType?: 'bug' | 'feature-request' | 'performance' | 'ui-ux' | 'improvement' | 'question' | 'other';
  filterBySeverity?: 'low' | 'medium' | 'high' | 'critical';
  filterByStatus?: SimplifiedStatus;
  filterByDateStart?: string;
  filterByDateEnd?: string;
  searchQuery?: string;
}

// API response structures
export interface GetReportsResponse {
  reports: ParsedReport[];
  total: number;
  totalPages: number;
  currentPage: number;
}

// Filter options for UI components
export interface FilterOptions {
  type?: string;
  severity?: string;
  status?: SimplifiedStatus;
  dateStart?: string;
  dateEnd?: string;
  searchQuery?: string;
}

// Sort options for UI components
export interface SortOptions {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// Report type and severity enums for consistent display
export const REPORT_TYPES = {
  bug: 'Bug',
  'feature-request': 'Feature Request',
  performance: 'Performance',
  'ui-ux': 'UI/UX',
  improvement: 'Improvement',
  question: 'Question',
  other: 'Other'
} as const;

export const REPORT_SEVERITIES = {
  low: 'Low',
  medium: 'Medium',
  high: 'High',
  critical: 'Critical'
} as const;

export const LOG_LEVELS = {
  INFO: 'Info',
  WARN: 'Warning',
  ERROR: 'Error',
  DEBUG: 'Debug'
} as const;

// Color mappings for severity indicators
export const SEVERITY_COLORS = {
  low: 'default',
  medium: 'warning',
  high: 'error',
  critical: 'error'
} as const;

// Color mappings for log level indicators
export const LOG_LEVEL_COLORS = {
  INFO: 'info',
  WARN: 'warning',
  ERROR: 'error',
  DEBUG: 'default'
} as const;

// For backward compatibility with dashboard component
export interface DebugReport {
  id: string;
  timestamp: string;
  level: string;
  userId?: string;
  message: string;
  url?: string;
  userAgent?: string;
  stackTrace?: string;
  additionalData?: any;
}

// Status utility functions
export function getSimplifiedStatus(dbStatus?: DebugReportStatus): SimplifiedStatus {
  if (!dbStatus) return 'new';
  return STATUS_MAPPING[dbStatus] || 'new';
}

export function getStatusDisplayName(status: SimplifiedStatus | DebugReportStatus): string {
  if (status === 'new' || status === 'todo' || status === 'done') {
    return SIMPLIFIED_STATUS_LABELS[status];
  }
  // For DB status values, map to simplified and get display name
  const simplified = getSimplifiedStatus(status as DebugReportStatus);
  return SIMPLIFIED_STATUS_LABELS[simplified];
}

export function getStatusColor(status: SimplifiedStatus | DebugReportStatus): string {
  if (status === 'new' || status === 'todo' || status === 'done') {
    return SIMPLIFIED_STATUS_COLORS[status];
  }
  // For DB status values, map to simplified and get color
  const simplified = getSimplifiedStatus(status as DebugReportStatus);
  return SIMPLIFIED_STATUS_COLORS[simplified];
}

export function getDatabaseStatusFromSimplified(simplified: SimplifiedStatus): DebugReportStatus {
  // Default mappings for status changes
  switch (simplified) {
    case 'new': return 'NOT_REVIEWED';
    case 'todo': return 'IN_PROGRESS';
    case 'done': return 'COMPLETED';
    default: return 'NOT_REVIEWED';
  }
}

export function getApiStatusFromSimplified(simplified: SimplifiedStatus): string {
  // Map to API format (lowercase with underscores)
  switch (simplified) {
    case 'new': return 'not_reviewed';
    case 'todo': return 'in_progress';
    case 'done': return 'completed';
    default: return 'not_reviewed';
  }
}

export function getDatabaseStatusFromApi(apiStatus: string): DebugReportStatus {
  // Convert API format back to database format
  switch (apiStatus) {
    case 'not_reviewed': return 'NOT_REVIEWED';
    case 'in_progress': return 'IN_PROGRESS';
    case 'completed': return 'COMPLETED';
    case 'archived': return 'ARCHIVED';
    case 'duplicate': return 'DUPLICATE';
    case 'wont_fix': return 'WONT_FIX';
    default: return 'NOT_REVIEWED';
  }
}

// Additional utility functions for the table component
export function mapToSimplifiedStatus(dbStatus?: DebugReportStatus): SimplifiedStatus {
  return getSimplifiedStatus(dbStatus);
}

export function mapFromSimplifiedStatus(simplified: SimplifiedStatus): DebugReportStatus {
  return getDatabaseStatusFromSimplified(simplified);
}

export function getStatusDisplayText(status: SimplifiedStatus | DebugReportStatus): string {
  return getStatusDisplayName(status);
}

export function getStatusBadgeColor(status: SimplifiedStatus | DebugReportStatus): string {
  return getStatusColor(status);
}
