**MUNygo MVP - User Stories Document**

**Version:** 1.0
**Date:** October 26, 2023
**Based on PRD:** Version 1.0

*(Note: Status for all is initially 'To Do'. Priority is generally MVP-Essential unless noted otherwise.)*

---
**Feature Area: User Authentication & Profile**

---
**User Story**

**ID:** MUN-001
**Feature:** User Authentication
**Priority:** MVP-Essential
**Status:** Done

**As a:** New User
**I want to:** Register for an account using my email address and a chosen password
**So that:** I can begin the process of accessing the MUNygo platform.

**Acceptance Criteria (AC):**

1.  Verify that a registration form exists with fields for Email, Password, and Password Confirmation.
2.  Verify that input validation is performed (email format, password minimum length/complexity, passwords match).
3.  Verify that upon successful submission, a new user record is created in the database with an 'unverified email' status.
4.  Verify that the password stored in the database is securely hashed (e.g., using bcrypt).
5.  Verify that an email verification process is initiated (see MUN-002).
6.  Verify that appropriate error messages are shown for invalid input or if the email is already registered.
7.  Verify that the user must accept Terms of Service and Privacy Policy to register (see MUN-030). - *Implemented*

**Notes/Assumptions:**

*   Depends on MUN-030 (Legal Acceptance) - *Checkbox implemented, requires ToS/PP pages.*
*   Initiates flow for MUN-002 (Email Verification).

---
**User Story**

**ID:** MUN-002
**Feature:** User Authentication
**Priority:** MVP-Essential
**Status:** Done

**As a:** Newly Registered User
**I want to:** Receive an email with a verification link and click it
**So that:** I can verify my email address and proceed with using the platform.

**Acceptance Criteria (AC):**

1.  Verify that after successful registration (MUN-001), an email containing a unique verification link/token is sent to the registered email address.
2.  Verify that clicking the valid verification link updates the user's status in the database to 'email verified'.
3.  Verify that the user is shown a success message upon successful verification (e.g., on a dedicated verification page or after redirecting to login/dashboard).
4.  Verify that clicking an invalid, expired, or already used token shows an appropriate error message.
5.  Verify that users with unverified emails have limited access (e.g., cannot access core features beyond profile/verification steps).

**Notes/Assumptions:**

*   Requires integration with an email sending service (e.g., SendGrid, Mailgun). - *Implemented using nodemailer (Ethereal for dev).*
*   Token generation and validation logic is needed. - *Implemented.*

---
**User Story**

**ID:** MUN-003
**Feature:** User Authentication
**Priority:** MVP-Essential
**Status:** Done

**As a:** Registered User with a verified email
**I want to:** Verify my phone number using an SMS OTP
**So that:** I can gain full access to core features like creating offers and initiating chats.

**Acceptance Criteria (AC):**

1.  Verify that there is a section in the profile or an onboarding step to input a phone number (likely Canadian format initially).
2.  Verify that submitting the phone number triggers an API call to request an OTP via SMS.
3.  Verify that an input field appears for the user to enter the received OTP.
4.  Verify that submitting the correct OTP updates the user's status in the database to 'phone verified'.
5.  Verify that a success message is shown.
6.  Verify that appropriate error messages are shown for invalid OTP, expired OTP, or too many attempts.
7.  Verify that a 'Resend OTP' option is available (with rate limiting).
8.  Verify that core features (Offer Creation MUN-006, Show Interest MUN-011) are blocked until the phone is verified.

**Notes/Assumptions:**

*   Requires integration with an SMS OTP provider (e.g., Twilio). - *Implemented*
*   Focus on Canadian phone number format for MVP. - *Implemented.*
*   Rate limiting for OTP requests is needed to prevent abuse. - *Implemented*
*   OTP and phone verification implemented in ProfileView.vue component.

---
**User Story**

**ID:** MUN-004
**Feature:** User Authentication
**Priority:** MVP-Essential
**Status:** Done

**As a:** Registered and Verified User
**I want to:** Log in to the platform using my email and password
**So that:** I can access my account and the platform's features.

**Acceptance Criteria (AC):**

1.  Verify that a login form exists with fields for Email and Password.
2.  Verify that upon submission, the system checks the credentials against the stored hashed password.
3.  Verify that upon successful authentication, a user session is created (or JWT token issued) and the user is redirected to the main application view (e.g., Offer List).
4.  Verify that if authentication fails (incorrect email/password), an appropriate error message is displayed, and the user remains on the login page.
5.  Verify that logged-in users can access protected pages/features.

**Notes/Assumptions:**

*   Session management or JWT handling is required.

---
**User Story**

**ID:** MUN-005
**Feature:** User Profile
**Priority:** MVP-Essential
**Status:** Done

**As a:** Logged-in User
**I want to:** View my basic profile information
**So that:** I can see my username, join date, verification status, and current reputation level.

**Acceptance Criteria (AC):**

1.  Verify that a dedicated Profile page/section is accessible.
2.  Verify that the page correctly displays the logged-in user's Username.
3.  Verify that the page correctly displays the user's account Join Date.
4.  Verify that the page clearly indicates Email Verification Status (e.g., Verified / Not Verified).
5.  Verify that the page clearly indicates Phone Verification Status (e.g., Verified / Not Verified).
6.  Verify that the page correctly displays the user's current **Reputation Level** (e.g., Level 3: Reliable).
7.  Verify that this information is display-only and not editable by the user in the MVP.

**Notes/Assumptions:**

*   Relies on Reputation System (MUN-016, MUN-018) to calculate and provide the level.

---
**Feature Area: Offer Management**

---
**User Story**

**ID:** MUN-006
**Feature:** Offer Creation
**Priority:** MVP-Essential
**Status:** Done

**As a:** Logged-in, Phone-Verified User
**I want to:** Create a new Buy or Sell offer for CAD-IRR currency exchange
**So that:** My exchange need is listed and visible to other potential partners.

**Acceptance Criteria (AC):**

1.  Verify that an Offer Creation form is accessible only to phone-verified users.
2.  Verify that the form includes fields/options to select Offer Type (Sell CAD / Buy CAD).
3.  Verify that the Currency Pair is fixed/displayed as CAD-IRR.
4.  Verify that the form includes an input field for the Amount (of CAD). Input validation for positive numbers.
5.  Verify that the form includes an input field for the Base Exchange Rate (IRR per CAD). Input validation for positive numbers.
6.  Verify that the form includes input fields for **Simplified Tiered Adjustments**:
    *   Rate/Percentage Adjustment for trade with Lower Reputation Users (Levels 1 & 2).
    *   Rate/Percentage Adjustment for trade with Higher Reputation Users (Levels 4 & 5).
    *   (Input validation for numeric values).
7.  Verify that upon successful submission, a new Offer record is created in the database, linked to the user, with all details saved and status set to 'Active'.
8.  Verify that the user is redirected to their "My Offers" list or shown a success message.
9.  Verify that appropriate error messages are shown for invalid inputs.

**Notes/Assumptions:**

*   Tiered adjustment fields need clear labels explaining they are relative to the Base Rate or absolute rates.
*   User must be phone-verified (dependency on MUN-003).

---
**User Story**

**ID:** MUN-007
**Feature:** Offer Management
**Priority:** MVP-Essential
**Status:** Done

**As a:** Logged-in User
**I want to:** View a list of all the offers I have created
**So that:** I can track my active and inactive exchange requests.

**Acceptance Criteria (AC):**

1.  Verify that a "My Offers" page/section is accessible.
2.  Verify that the list displays *only* the offers created by the logged-in user.
3.  Verify that each offer in the list displays key details: Type (Buy/Sell), Amount (CAD), Base Rate, Status (Active/Inactive), Creation Date.
4.  Verify that offers are distinguishable by status (e.g., visual difference or explicit label).

---
**User Story**

**ID:** MUN-008
**Feature:** Offer Management
**Priority:** MVP-Essential
**Status:** Done

**As a:** Logged-in User viewing my own offers
**I want to:** Toggle the status of an offer between 'Active' and 'Inactive'
**So that:** I can control whether my offer is visible to others in the public listings.

**Acceptance Criteria (AC):**

1.  Verify that each offer listed in "My Offers" (MUN-007) has a button or toggle control to change its status.
2.  Verify that clicking the control triggers an API call to update the offer's status in the database.
3.  Verify that the UI immediately (or upon refresh) reflects the new status of the offer.
4.  Verify that only 'Active' offers appear in the public browse list (MUN-009).

---
**Feature Area: Offer Discovery & Matching**

---
**User Story**

**ID:** MUN-009
**Feature:** Offer Discovery
**Priority:** MVP-Essential
**Status:** Done

**As a:** Logged-in User
**I want to:** Browse a list of active CAD-IRR offers created by *other* users
**So that:** I can find potential exchange partners.

**Acceptance Criteria (AC):**

1.  Verify that a "Browse Offers" or similar page exists.
2.  Verify that the list displays only 'Active' offers.
3.  Verify that the list *excludes* offers created by the logged-in user viewing the page.
4.  Verify that each offer card clearly displays:
    *   Offer Type (Sell CAD / Buy CAD).
    *   Amount (CAD).
    *   Offer Creator's Username.
    *   Offer Creator's **Reputation Level** (e.g., Level 3).
    *   The **Calculated Exchange Rate Applicable** to the *viewing user* (see MUN-010).
    *   A button/link to "Show Interest" (see MUN-011).

**Notes/Assumptions:**

*   Depends on MUN-010 for correct rate display.

---
**User Story**

**ID:** MUN-010
**Feature:** Offer Discovery (Backend Logic)
**Priority:** MVP-Essential
**Status:** Done

**As a:** System/Backend
**I want to:** Calculate the specific, applicable exchange rate for each offer being listed based on the viewing user's reputation level and the offer creator's tiered settings
**So that:** The browsing user sees the correct, personalized rate they would get if they initiated a trade.

**Acceptance Criteria (AC):**

1.  Verify that when the API endpoint for listing offers (for MUN-009) is called, it identifies the requesting/viewing user's reputation level.
2.  Verify that for each offer retrieved, the system accesses the offer's Base Rate and its simplified tiered adjustments (Rate/Adjustment for Low Rep Levels and Rate/Adjustment for High Rep Levels.
3.  Verify that the logic correctly compares the viewing user's level to the offer creator's level (or just uses the viewer's level against the fixed tiers).
4.  Verify that the correct rate is calculated:
    *   If viewer's level matches offer creator's level (or is Level 3), use Base Rate.
    *   If viewer's level is lower than offer creator's level, apply the "Low Rep" adjustment/rate.
    *   If viewer's level is higher than offer creator's level, apply the "High Rep" adjustment/rate.
5.  Verify that this calculated `applicable_rate` is included in the data returned by the API for each offer.
6.  Verify logic works correctly for all level combinations (e.g., Lvl 1 viewer seeing Lvl 5 offer, Lvl 5 viewer seeing Lvl 1 offer, Lvl 3 viewing Lvl 3).

---
**User Story**

**ID:** MUN-011
**Feature:** Connection Flow
**Priority:** MVP-Essential
**Status:** Done

**As a:** Logged-in, Phone-Verified User browsing offers
**I want to:** Click a "Show Interest" button on an offer created by another user
**So that:** I can notify the offer creator that I am potentially interested in their offer.

**Acceptance Criteria (AC):**

1.  Verify that a "Show Interest" button exists on each offer card in the browse list (MUN-009), except for the user's own offers.
2.  Verify that the button is only active/clickable for phone-verified users.
3.  Verify that clicking the button triggers an API call, recording the interest (linking viewer to the offer/creator).
4.  Verify that the user receives visual feedback (e.g., success message, button state change) indicating interest was sent.
5.  Verify that a user cannot show interest multiple times in the same active offer.

**Notes/Assumptions:**

*   Requires user to be phone-verified (dependency on MUN-003).
*   Triggers notification for MUN-012.

---
**User Story**

**ID:** MUN-012
**Feature:** Connection Flow
**Priority:** MVP-Essential
**Status:** Done

**As a:** Offer Creator
**I want to:** Receive an in-app notification when another user shows interest in one of my active offers
**So that:** I am aware of potential trading partners.

**Acceptance Criteria (AC):**

1.  Verify that when User B shows interest (MUN-011) in User A's offer, User A receives a clear in-app notification.
2.  Verify that the notification includes the Username and Reputation Level of the interested user (User B).
3.  Verify that the notification clearly indicates which offer the interest pertains to (e.g., "100 CAD Sell Offer").
4.  Verify that the notification provides clear actions to "Accept" or "Decline" the interest (leading to MUN-013).

**Notes/Assumptions:**

*   Requires a basic in-app notification system (UI element + backend logic).

---
**User Story**

**ID:** MUN-013
**Feature:** Connection Flow
**Priority:** MVP-Essential
**Status:** Done

**As a:** Offer Creator who received an interest notification
**I want to:** Accept or Decline the interest request
**So that:** I can control who I start a conversation with and initiate the chat.

**Acceptance Criteria (AC):**

1.  Verify that "Accept" and "Decline" actions are available for pending interest requests (from MUN-012).
2.  Verify that clicking "Decline":
    *   Triggers an API call.
    *   Removes/updates the pending interest request.
    *   (Optional MVP) Sends a notification back to the interested user that their interest was declined.
3.  Verify that clicking "Accept":
    *   Triggers an API call.
    *   Creates a new private Chat session/room between the two users in the database.
    *   Updates the status of the interest request (e.g., to 'accepted').
    *   Triggers notifications (WebSocket/API) to *both* users indicating the chat is ready.
    *   Enables access to the chat interface (MUN-014) for both users for this specific interaction.

---
**User Story**

**ID:** MUN-014
**Feature:** Communication (Chat)
**Priority:** MVP-Essential
**Status:** Done

**As a:** User who has mutually accepted interest with another user
**I want to:** Send and receive text messages in a private, real-time chat window
**So that:** We can confirm details, manually exchange payment information, and coordinate the external transfer.

**Acceptance Criteria (AC):**

1.  Verify that upon mutual acceptance (MUN-013), a dedicated chat interface becomes accessible for the two participants.
2.  Verify that the chat interface displays the other participant's Username and Reputation Level.
3.  Verify that a text input field and a 'Send' button are present.
4.  Verify that sending a message transmits it via WebSocket and it appears instantly in the chat history for *both* participants.
5.  Verify that incoming messages are displayed instantly.
6.  Verify that the chat history for the session is loaded when the chat window is opened.
7.  Verify that only text messages can be sent (no file uploads, images, etc.).
8.  Verify that chat messages are stored persistently in the database linked to the specific chat session.

**Notes/Assumptions:**

*   Requires WebSocket implementation (e.g., Socket.IO).

---
**Feature Area: Reputation System**

---
**User Story**

**ID:** MUN-015
**Feature:** Reputation System (Display)
**Priority:** MVP-Essential
**Status:** Done

**As a:** Logged-in User
**I want to:** See the Reputation Level (1-5) clearly displayed for myself and other users
**So that:** I can gauge the trustworthiness of potential partners and understand my own standing.

**Acceptance Criteria (AC):**

1.  Verify that the user's own Level is displayed on their Profile page (MUN-005).
2.  Verify that the Offer Creator's Level is displayed on Offer Cards in the browse list (MUN-009).
3.  Verify that the participants' Levels are displayed within the Chat interface (MUN-014).
4.  Verify that the levels are displayed consistently (e.g., "Level 3: Reliable" or a clear visual badge).

---
**User Story**

**ID:** MUN-016
**Feature:** Reputation System (Backend Logic)
**Priority:** MVP-Essential
**Status:** In Progress

**As a:** System/Backend
**I want to:** Calculate and store a user's reputation score based on MVP factors (Email/Phone Verification, Ratings received/given)
**So that:** An accurate score is available to determine the user's level.

**Acceptance Criteria (AC):**

1.  Verify that a `reputation_score` field exists on the User/Profile model.
2.  Verify that functions exist to increment the score based on defined events:
    *   `+X` points upon successful Email Verification completion (call from MUN-002 logic).
    *   `+Y` points upon successful Phone Verification completion (call from MUN-003 logic).
    *   `+Z` points when a rating is received (call from MUN-017 logic, potentially varying points based on star count).
    *   (If implemented) `+W` points when a rating is given by the user.
3.  Verify that score updates are saved correctly to the database.

---
**User Story**

**ID:** MUN-017
**Feature:** Reputation System (Rating Input)
**Priority:** MVP-Essential
**Status:** To Do

**As a:** User who participated in a chat interaction
**I want to:** Be prompted to rate the other user (1-5 stars, optional comment) after concluding the interaction
**So that:** I can provide feedback that contributes to their reputation score.

**Acceptance Criteria (AC):**

1.  Verify that a mechanism exists within the chat interface to mark an interaction as "Concluded" or "Completed" (user-triggered).
2.  Verify that upon conclusion, a rating prompt/modal appears, allowing the user to select 1-5 stars and enter optional text feedback specifically for the *other* participant.
3.  Verify that submitting the rating triggers an API call to save the rating (linked to the interaction/target user) and update the target user's score (calling logic from MUN-016).
4.  Verify that the rating prompt is dismissed after successful submission.
5.  Verify that a user can only rate a specific interaction once.

---
**User Story**

**ID:** MUN-018
**Feature:** Reputation System (Backend Logic)
**Priority:** MVP-Essential
**Status:** To Do

**As a:** System/Backend
**I want to:** Calculate and store the user's Reputation Level (1-5) based on their current reputation score
**So that:** The correct level can be displayed throughout the application.

**Acceptance Criteria (AC):**

1.  Verify that predefined score ranges map to Levels 1 through 5 (e.g., 0-20=L1, 21-50=L2, 51-100=L3, 101-175=L4, 176+=L5 - *Adjust ranges based on MVP scoring potential*).
2.  Verify that a `reputation_level` field exists on the User/Profile model.
3.  Verify that whenever the `reputation_score` is updated (by MUN-016), this function is called to recalculate and update the `reputation_level` field in the database.
4.  Verify the mapping logic correctly assigns levels based on the defined score ranges.

---
**Feature Area: Safety & Support**

---
**User Story**

**ID:** MUN-020
**Feature:** Safety & Support
**Priority:** MVP-High
**Status:** To Do

**As a:** Logged-in User
**I want to:** Report another user or a specific offer for suspicious or inappropriate activity
**So that:** Admins can review and take necessary action to maintain platform safety.

**Acceptance Criteria (AC):**

1.  Verify that a "Report User" button/link is visible on user profiles and/or within the chat interface.
2.  (Optional MVP) Verify a "Report Offer" button/link exists on offer cards.
3.  Verify that clicking the button opens a simple form/modal allowing the user to select a basic reason (e.g., Spam, Scam Attempt, Harassment, Misleading Offer).
4.  Verify that submitting the report triggers an API call that saves the report details (reporter, reported user/offer, reason, timestamp) to the database.
5.  Verify that the user receives confirmation that their report has been submitted.

**Notes/Assumptions:**

*   Requires manual admin review process for MVP (no automated actions).

---
**User Story**

**ID:** MUN-030
**Feature:** Legal
**Priority:** MVP-Essential
**Status:** In Progress

**As a:** New User during registration
**I want to:** Be required to view and accept the Terms of Service and Privacy Policy
**So that:** I understand the rules of the platform and how my data is used before creating an account.

**Acceptance Criteria (AC):**

1.  Verify that links to the Terms of Service (ToS) and Privacy Policy (PP) documents are clearly visible on the registration form. - *Implemented (links added)*
2.  Verify that a checkbox labeled "I agree to the Terms of Service and Privacy Policy" (or similar) is present. - *Implemented*
3.  Verify that this checkbox must be checked to enable the registration submission button. - *Implemented*
4.  Verify that links to the ToS and PP are also accessible from within the application after login (e.g., in the footer or settings). - *To Do*

**Notes/Assumptions:**

*   Requires the actual ToS and PP text to be written and hosted. - *Placeholder links used.*

---

This set of user stories provides a detailed breakdown of the MVP requirements. You can now use these to guide your prompts for the LLM, track progress, and ensure all necessary functionality is built and tested. Remember to create corresponding technical tasks for backend setup, database migrations, API definitions, WebSocket configurations, etc., which might not be direct user stories but are necessary implementation steps.