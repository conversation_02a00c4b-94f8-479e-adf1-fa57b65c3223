<template>
  <section class="hero-section" :class="{ 'enhanced': true }">
    <!-- Enhanced animated background -->
    <div class="hero-background">
      <div class="gradient-animation"></div>
      <div class="floating-elements">
        <div class="floating-element" v-for="n in 6" :key="n" :style="getFloatingStyle(n)"></div>
      </div>
    </div>

    <!-- Content with entrance animation -->
    <div class="hero-content" :class="{ 'loaded': contentLoaded }">
      <!-- Enhanced loading skeleton -->
      <div v-if="!contentLoaded" class="skeleton-content">
        <div class="skeleton-title"></div>
        <div class="skeleton-subtitle"></div>
        <div class="skeleton-buttons">
          <div class="skeleton-button"></div>
          <div class="skeleton-button"></div>
        </div>
      </div>

      <!-- Actual content with staggered animation -->
      <div v-else class="content-animation">
        <h1 class="hero-title animate-fade-up" :style="{ animationDelay: '0.2s' }">
          {{ $t('homeView.heroTitle') }}
        </h1>
        <p class="hero-subtitle animate-fade-up" :style="{ animationDelay: '0.4s' }">
          {{ $t('homeView.heroSubtitle') }}
        </p>
        <div class="hero-actions animate-fade-up" :style="{ animationDelay: '0.6s' }">
          <n-button 
            type="primary" 
            size="large" 
            class="primary-cta enhanced-button"
            @click="goToCreateOffer"
            @mousedown="handleButtonPress"
            @touchstart="handleButtonPress"
          >
            <template #icon>
              <n-icon class="button-icon"><PlusOutlined /></n-icon>
            </template>
            {{ $t('homeView.createOffer') }}
          </n-button>
          <n-button 
            size="large" 
            class="secondary-cta enhanced-button"
            @click="goToBrowseOffers"
            @mousedown="handleButtonPress"
            @touchstart="handleButtonPress"
          >
            <template #icon>
              <n-icon class="button-icon"><SearchOutlined /></n-icon>
            </template>
            {{ $t('homeView.browseOffers') }}
          </n-button>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { NButton, NIcon } from 'naive-ui'
import { PlusOutlined, SearchOutlined } from '@vicons/antd'

const router = useRouter();

// Enhanced loading and animation state
const contentLoaded = ref(false);

// Simulate realistic loading time for skeleton
onMounted(() => {
  // Shorter delay for perceived performance on mobile
  setTimeout(() => {
    contentLoaded.value = true;
  }, 300);
});

// Navigation functions with enhanced feedback
function goToCreateOffer() {
  router.push({ name: 'CreateOffer' });
}

function goToBrowseOffers() {
  router.push({ name: 'BrowseOffers' });
}

// Enhanced button interaction with haptic-like feedback
function handleButtonPress(event: Event) {
  const button = event.currentTarget as HTMLElement;
  button.style.transform = 'scale(0.98)';
  
  // Reset after short delay (haptic-like feedback)
  setTimeout(() => {
    button.style.transform = '';
  }, 150);
}

// Floating elements animation
function getFloatingStyle(index: number) {
  const delay = index * 0.5;
  const duration = 3 + (index * 0.3);
  const size = 4 + (index * 2);
  
  return {
    '--animation-delay': `${delay}s`,
    '--animation-duration': `${duration}s`,
    '--size': `${size}px`,
    '--start-x': `${Math.random() * 100}%`,
    '--start-y': `${Math.random() * 100}%`,
  };
}
</script>

<style scoped>
/* Enhanced Hero Section - Mobile-First with Animations */
.hero-section {
  position: relative;
  overflow: hidden;
  padding: 60px 16px 48px;
  text-align: center;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Enhanced animated background */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.gradient-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  animation: gradientShift 8s ease-in-out infinite;
}

/* Floating elements for visual interest */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  width: var(--size);
  height: var(--size);
  left: var(--start-x);
  top: var(--start-y);
  animation: float var(--animation-duration) ease-in-out infinite;
  animation-delay: var(--animation-delay);
}

/* Content container */
.hero-content {
  position: relative;
  z-index: 1;
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
}

/* Loading skeleton styles */
.skeleton-content {
  opacity: 0.7;
}

.skeleton-title {
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  margin: 0 auto 20px;
  width: 80%;
  animation: shimmer 1.5s infinite;
}

.skeleton-subtitle {
  height: 24px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  margin: 0 auto 32px;
  width: 60%;
  animation: shimmer 1.5s infinite 0.2s;
}

.skeleton-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.skeleton-button {
  height: 44px;
  width: 100%;
  max-width: 280px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  animation: shimmer 1.5s infinite 0.4s;
}

/* Enhanced content with animations */
.content-animation {
  color: white;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
  opacity: 0;
}

.hero-subtitle {
  font-size: 1.125rem;
  margin-bottom: 32px;
  opacity: 0.95;
  line-height: 1.5;
  opacity: 0;
}

.hero-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  opacity: 0;
}

/* Enhanced button styles */
.enhanced-button {
  width: 100%;
  max-width: 280px;
  min-height: 44px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enhanced-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.enhanced-button:hover::before,
.enhanced-button:focus::before {
  left: 100%;
}

.primary-cta {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.primary-cta:hover,
.primary-cta:focus {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.secondary-cta {
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.4);
  color: white;
}

.secondary-cta:hover,
.secondary-cta:focus {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.button-icon {
  transition: transform 0.2s ease;
}

.enhanced-button:hover .button-icon,
.enhanced-button:focus .button-icon {
  transform: scale(1.1);
}

/* Entrance animations */
.animate-fade-up {
  animation: fadeUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.loaded .animate-fade-up {
  opacity: 1;
}

/* Keyframe animations */
@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  }
  50% {
    background: linear-gradient(135deg, #535bf2 0%, #646cff 100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.1;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.3;
  }
}

@keyframes shimmer {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.5;
  }
}

/* Tablet enhancements */
@media (min-width: 768px) {
  .hero-section {
    padding: 80px 24px 60px;
    min-height: 70vh;
  }
  
  .hero-title {
    font-size: 3.5rem;
    margin-bottom: 24px;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .hero-actions {
    flex-direction: row;
    gap: 20px;
    justify-content: center;
  }
  
  .enhanced-button {
    max-width: 200px;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  .hero-content {
    max-width: 800px;
  }
  
  .hero-title {
    font-size: 4rem;
  }
  
  .hero-subtitle {
    font-size: 1.375rem;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .gradient-animation,
  .floating-element,
  .animate-fade-up {
    animation: none;
  }
  
  .animate-fade-up {
    opacity: 1;
    transform: none;
  }
  
  .enhanced-button {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .hero-section {
    background: #000;
    color: #fff;
  }
  
  .primary-cta,
  .secondary-cta {
    border: 2px solid #fff;
    background: #000;
    color: #fff;
  }
}
</style>
