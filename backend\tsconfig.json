{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "declaration": true, "resolveJsonModule": true, "types": ["vitest/globals"]}, "include": ["src/**/*", "src/types/**/*.d.ts", "src/test-env.d.ts"], "exclude": ["node_modules", "dist", "src/scripts/createTimerTestTransaction.ts", "src/scripts/setupTimerTest.ts"]}