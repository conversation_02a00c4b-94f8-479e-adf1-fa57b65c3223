# Multi-stage build for production
FROM node:18-alpine AS build

# Declare the build arguments that will be passed from docker-compose.yml
ARG VITE_BACKEND_URL_FOR_CLIENT
ARG VITE_API_BASE_URL
ARG VITE_ENABLE_DEBUG_REPORT
ARG VITE_ADMIN_EMAILS

# Set them as environment variables so Vite can access them during the build process
# Vite specifically looks for environment variables prefixed with VITE_
ENV VITE_BACKEND_URL=${VITE_BACKEND_URL_FOR_CLIENT}
ENV VITE_API_BASE_URL=${VITE_API_BASE_URL}
ENV VITE_ENABLE_DEBUG_REPORT=${VITE_ENABLE_DEBUG_REPORT}
ENV VITE_ADMIN_EMAILS=${VITE_ADMIN_EMAILS}

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./
COPY vite.config.ts ./

# Install dependencies
RUN npm ci

# Copy source code
COPY src ./src
COPY public ./public
COPY index.html ./

# Build the application (skip TypeScript type checking for Docker build)
RUN npm run build:docker

# Production stage with Nginx
FROM nginx:1.27.0-alpine AS production

# Copy custom nginx configuration into the conf.d directory
# This is the standard way to add site-specific configurations
# without overwriting the main nginx.conf.
# Nginx will automatically include this default.conf.
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built application from build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Set proper permissions (nginx user already exists in the nginx image)
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:80 || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
