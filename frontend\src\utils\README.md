# Debug Logging Strategy

## Overview

This directory contains a standardized approach for debug logging that ensures verbose debug information is only shown in development environments and is automatically excluded from production builds.

## Files

### `debugLogger.ts`

A centralized debug logging utility that:

- Only logs messages when `import.meta.env.DEV` is `true` (development mode)
- Provides consistent logging methods (`log`, `error`, `warn`, `info`)
- Automatically suppresses all debug output in production builds
- Maintains the same API as `console.*` methods for easy adoption

## Usage

```typescript
import { debugLogger } from '@/utils/debugLogger';

// These will only log in development mode
debugLogger.log('Debug information:', data);
debugLogger.error('Debug error:', error);
debugLogger.warn('Debug warning:', warning);
debugLogger.info('Debug info:', info);
```

## Benefits

1. **Production Clean**: No debug clutter in production logs
2. **Performance**: No overhead in production builds (tree-shaking removes the calls)
3. **Consistency**: Standardized approach across the application
4. **Easy Migration**: Drop-in replacement for existing `console.*` calls
5. **Environment-Aware**: Automatically adapts to build environment

## Environment Variable

The logging is controlled by `import.meta.env.DEV`, which is:
- `true` in development mode (`npm run dev`)
- `false` in production builds (`npm run build`)

## Migration Example

**Before:**
```typescript
if (import.meta.env.DEV) {
  console.log('Debug message:', data);
}
```

**After:**
```typescript
debugLogger.log('Debug message:', data);
```

## Implementation Notes

- Uses Vite's `import.meta.env.DEV` for environment detection
- Tree-shaking ensures zero production bundle impact
- Maintains same signature as native console methods
- Can be extended with additional logging features as needed
