// Notification System Test Script - Browser Console Compatible
// Copy and paste this entire script into your browser console

console.log("🔍 Starting notification system debug...");

// Test 1: Check if socket manager is connected
function checkSocketConnection() {
  console.log("📡 Checking socket connection...");
  
  try {
    // Get socket manager from window (if exposed) or Vue app
    var app = document.querySelector('#app');
    if (!app || !app.__vue_app__) {
      console.error("❌ Cannot find Vue app instance");
      return false;
    }
    
    // Check if we can access the stores
    console.log("✅ Vue app found, checking stores...");
    return true;
  } catch (error) {
    console.error("❌ Error checking socket connection:", error);
    return false;
  }
}

// Test 2: Check notification store state
function checkNotificationStore() {
  console.log("🔔 Checking notification store...");
  
  try {
    // Try to access notification store state via Vue DevTools or global
    console.log("Check your Vue DevTools > Pinia tab for notification store state");
    console.log("Look for:");
    console.log("- notifications array");
    console.log("- unreadNotificationsCount");
    console.log("- isLoading state");
    
    return true;
  } catch (error) {
    console.error("❌ Error checking notification store:", error);
    return false;
  }
}

// Test 3: Check authentication state
function checkAuthState() {
  console.log("🔐 Checking authentication state...");
  
  var token = localStorage.getItem('authToken');
  var userInfo = localStorage.getItem('userInfo');
  
  if (token && userInfo) {
    console.log("✅ User is authenticated");
    console.log("Token exists:", !!token);
    console.log("User info:", JSON.parse(userInfo));
    return true;
  } else {
    console.log("❌ User is not authenticated");
    console.log("Please log in to test notifications");
    return false;
  }
}

// Test 4: Monitor socket events
function monitorSocketEvents() {
  console.log("👂 Setting up socket event monitoring...");
  console.log("Watch the console for these messages when someone expresses interest:");
  console.log("- '🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION:'");
  console.log("- '[NotificationStore] Received NEW_NOTIFICATION via centralized manager:'");
  console.log("- '[NotificationStore] Added new notification'");
  
  // Add a console listener for NEW_NOTIFICATION
  var originalLog = console.log;
  console.log = function() {
    var args = Array.prototype.slice.call(arguments);
    if (args[0] && args[0].toString().indexOf('NEW_NOTIFICATION') !== -1) {
      console.warn("🔔🔔🔔 NOTIFICATION EVENT DETECTED:", args);
    }
    originalLog.apply(console, args);
  };
  
  return true;
}

// Test 5: Manual notification test guide
function showManualTestGuide() {
  console.log("");
  console.log("🎯 MANUAL TEST PROCEDURE:");
  console.log("========================");
  console.log("1. Keep this console open");
  console.log("2. Open a second incognito browser window");
  console.log("3. Go to http://localhost:5173 in both windows");
  console.log("4. Log in as different users in each window");
  console.log("5. In window 1: Create an offer");
  console.log("6. In window 2: Browse offers and express interest");
  console.log("7. Check window 1 for notification bell badge");
  console.log("8. Watch this console for socket events");
  console.log("");
}

// Run all tests
console.log("🚀 Running notification system tests...");
console.log("========================================");

var tests = [
  { name: "Socket Connection", test: checkSocketConnection },
  { name: "Notification Store", test: checkNotificationStore },
  { name: "Authentication State", test: checkAuthState },
  { name: "Socket Event Monitoring", test: monitorSocketEvents }
];

for (var i = 0; i < tests.length; i++) {
  var testItem = tests[i];
  console.log("\n📋 Test: " + testItem.name);
  try {
    var result = testItem.test();
    console.log(result ? "✅ PASSED" : "❌ FAILED");
  } catch (error) {
    console.error("❌ ERROR:", error);
  }
}

showManualTestGuide();

console.log("🏁 Notification system debug complete!");
console.log("Keep this console open and follow the manual test procedure above.");
