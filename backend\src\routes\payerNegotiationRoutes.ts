import { Hono } from 'hono';
import { z } from 'zod';
import { authMiddleware } from '../middleware/auth';
import { PayerNegotiationService } from '../services/payerNegotiationService'; // Import the CLASS, not the instance
import { validateRequest } from '../utils/validateRequest';
import { getPrismaClient } from '../utils/database';

// Export a factory function instead of a direct router
export default function createPayerNegotiationRoutes(
  payerNegotiationService: PayerNegotiationService // Accept the service as a parameter
) {
  const router = new Hono();
  const prisma = getPrismaClient(); // Use shared PrismaClient singleton

  // Apply auth middleware to all routes
  router.use('*', authMiddleware);
  const receiveInfoSchema = z.object({
    bankName: z.string(),
    accountNumber: z.string(),
    accountHolderName: z.string(),
    saveToProfile: z.boolean().optional(),
    isDefaultForUser: z.boolean().optional()
  });

  const proposePayerSchema = z.object({
    proposedPayerId: z.string()
  });

  const proposeFirstPayerSchema = z.object({
    proposedPayerId: z.string().cuid(),
    proposalMessage: z.string().optional()
  });

  const cancelNegotiationSchema = z.object({
    reason: z.string().min(1, 'Cancellation reason is required')
  });

  // Initialize or get negotiation state - SAME EXACT PATH
  router.get('/payer-negotiation', async (c) => {
    const transactionId = c.req.param('transactionId');
    const { userId } = c.get('jwtPayload');

    try {
      if (!transactionId) {
        return c.json({ error: 'Transaction ID is missing' }, 400);
      }

      // Before accessing negotiation state, ensure the current user is part of the transaction
      const transaction = await prisma.transaction.findUnique({
        where: { id: transactionId },
        select: { currencyAProviderId: true, currencyBProviderId: true }
      });

      if (!transaction || (transaction.currencyAProviderId !== userId && transaction.currencyBProviderId !== userId)) {
        return c.json({ error: 'User not part of this transaction or transaction not found' }, 403);
      }

      // Use the service method that properly formats the data
      const negotiationPayload = await payerNegotiationService.getOrInitializeNegotiationState(transactionId, userId);
      
      return c.json({ data: negotiationPayload });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  });

  // Submit receiving info - SAME EXACT PATH
  router.post('/payer-negotiation/receiving-info', async (c) => {
    const { userId } = c.get('jwtPayload');
    const transactionId = c.req.param('transactionId');
    const validation = await validateRequest(c, receiveInfoSchema);
    if (!validation.success) return validation.response;

    try {
      const negotiation = await prisma.payerNegotiation.findUnique({
        where: { transactionId }
      });
      if (!negotiation) throw new Error('Negotiation not found');

      if (userId !== negotiation.partyA_Id && userId !== negotiation.partyB_Id) {
        return c.json({ error: 'User not part of this negotiation' }, 403);
      }

      // Use the injected service and return properly formatted payload
      const updatedPayload = await payerNegotiationService.submitReceivingInfo(
        negotiation.negotiationId,
        userId,
        validation.data
      );

      return c.json({ data: updatedPayload });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  });

  // Propose first payer - SAME EXACT PATH
  router.post('/payer-negotiation/propose', async (c) => {
    const { userId } = c.get('jwtPayload');
    const transactionId = c.req.param('transactionId');
    const validation = await validateRequest(c, proposeFirstPayerSchema);
    if (!validation.success) return validation.response;

    try {
      const negotiation = await prisma.payerNegotiation.findUnique({
        where: { transactionId }
      });
      if (!negotiation) throw new Error('Negotiation not found');

      if (userId !== negotiation.partyA_Id && userId !== negotiation.partyB_Id) {
        return c.json({ error: 'User not part of this negotiation' }, 403);
      }

      // Use the injected service and return properly formatted payload
      const updatedPayload = await payerNegotiationService.proposeFirstPayer(
        negotiation.negotiationId,
        userId,
        validation.data.proposedPayerId,
        validation.data.proposalMessage
      );

      return c.json({ data: updatedPayload });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  });

  // Agree to current proposal - SAME EXACT PATH
  router.post('/payer-negotiation/agree', async (c) => {
    const { userId } = c.get('jwtPayload');
    const transactionId = c.req.param('transactionId');

    try {
      const negotiation = await prisma.payerNegotiation.findUnique({
        where: { transactionId }
      });
      if (!negotiation) throw new Error('Negotiation not found');

      if (userId !== negotiation.partyA_Id && userId !== negotiation.partyB_Id) {
        return c.json({ error: 'User not part of this negotiation' }, 403);
      }

      // Use the injected service and return properly formatted payload
      const updatedPayload = await payerNegotiationService.agreeToProposal(
        negotiation.negotiationId,
        userId
      );

      return c.json({ data: updatedPayload });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  });

  // Cancel negotiation
  router.post('/payer-negotiation/cancel', async (c) => {
    const { userId } = c.get('jwtPayload');
    const transactionId = c.req.param('transactionId');
    const validation = await validateRequest(c, cancelNegotiationSchema);
    if (!validation.success) return validation.response;

    try {
      const negotiation = await prisma.payerNegotiation.findUnique({
        where: { transactionId }
      });
      if (!negotiation) throw new Error('Negotiation not found');

      if (userId !== negotiation.partyA_Id && userId !== negotiation.partyB_Id) {
        return c.json({ error: 'User not part of this negotiation' }, 403);
      }

      // Use the injected service and return properly formatted payload
      const updatedPayload = await payerNegotiationService.cancelNegotiation(
        negotiation.negotiationId,
        validation.data.reason
      );

      return c.json({ data: updatedPayload });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  });

  return router; // Return the configured router
}
