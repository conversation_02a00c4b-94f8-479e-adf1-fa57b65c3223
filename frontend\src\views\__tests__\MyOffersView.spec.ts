import { mount, flushPromises } from '@vue/test-utils';
import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import { NMessageProvider, NSwitch, useMessage } from 'naive-ui';
import { h, defineComponent } from 'vue';
import MyOffersView from '../MyOffersView.vue';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as offerStatusService from '@/services/offerStatusService';
import * as offerService from '@/services/offerService';

vi.mock('@/services/offerStatusService');
vi.mock('@/services/offerService');

const mockOffers = [
  {
    id: '1',
    type: 'BUY',
    amount: 100,
    baseRate: 50000,
    adjustmentForLowerRep: 0,
    adjustmentForHigherRep: 0,
    status: 'ACTIVE',
    createdAt: '2024-05-01T00:00:00.000Z',
  },
  {
    id: '2',
    type: 'SELL',
    amount: 200,
    baseRate: 51000,
    adjustmentForLowerRep: 1,
    adjustmentForHigherRep: 2,
    status: 'INACTIVE',
    createdAt: '2024-05-02T00:00:00.000Z',
  },
];

describe('MyOffersView', () => {
  let messageErrorSpy: ReturnType<typeof vi.fn>;  beforeEach(() => {
    vi.resetAllMocks();
    vi.mocked(offerService.getMyOffers).mockResolvedValue([...mockOffers]);
    vi.mocked(offerStatusService.updateOfferStatus).mockResolvedValue({ status: 'INACTIVE' });
    // Spy on Naive UI message.error
    messageErrorSpy = vi.fn();
    vi.spyOn(require('naive-ui'), 'useMessage').mockReturnValue({ error: messageErrorSpy } as any);
  });

  // Wrapper component to provide NMessageProvider context
  const TestWrapper = defineComponent({
    setup() {
      return () => h(NMessageProvider, null, { default: () => h(MyOffersView) });
    },
  });

  function mountWithPinia() {
    return mount(TestWrapper, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            auth: { user: { reputationLevel: 3 } },
          },
        })],
        stubs: ['router-link', 'router-view'],
      },
    });
  }

  it('renders offers and toggles status', async () => {
    const wrapper = mountWithPinia();
    await flushPromises();
    // Check that both offers are rendered
    expect(wrapper.text()).toContain('BUY');
    expect(wrapper.text()).toContain('SELL');
    // Find all switches
    const switches = wrapper.findAllComponents(NSwitch);
    expect(switches.length).toBe(2);
    // Directly call the onUpdateValue handler for the first switch
    const switchVm = switches[0].vm as any;
    await switchVm.$props.onUpdateValue(false);
    expect(offerStatusService.updateOfferStatus).toHaveBeenCalledWith('1', 'INACTIVE');
  });

  it('shows error if status update fails', async () => {
    (offerStatusService.updateOfferStatus as any).mockRejectedValueOnce(new Error('fail'));
    const wrapper = mountWithPinia();
    await flushPromises();
    const switches = wrapper.findAllComponents(NSwitch);
    const switchVm = switches[0].vm as any;
    await switchVm.$props.onUpdateValue(false);
    expect(messageErrorSpy).toHaveBeenCalledWith('Failed to update offer status.');
  });
});
