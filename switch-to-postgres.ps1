# Switch to PostgreSQL Development Environment
# This script starts PostgreSQL in Docker and switches the backend to use it

Write-Host "=== Switching to PostgreSQL Development Environment ===" -ForegroundColor Green

# Start PostgreSQL container
Write-Host "Starting PostgreSQL container..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml up -d postgres-dev

# Wait for PostgreSQL to be ready
Write-Host "Waiting for PostgreSQL to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Copy the PostgreSQL environment file
Write-Host "Switching to PostgreSQL environment..." -ForegroundColor Yellow
Copy-Item ".\backend\.env.local.postgres" ".\backend\.env" -Force

# Navigate to backend and run migrations
Write-Host "Running database migrations..." -ForegroundColor Yellow
Set-Location ".\backend"
npx prisma migrate dev --name "init_postgres_dev"
npx prisma generate

Write-Host "=== PostgreSQL Development Environment Ready! ===" -ForegroundColor Green
Write-Host "Database URL: postgresql://munygo_user:munygo_password@localhost:5432/munygo_dev" -ForegroundColor Cyan
Write-Host "You can now run: npm run dev" -ForegroundColor Cyan

Set-Location ".."
