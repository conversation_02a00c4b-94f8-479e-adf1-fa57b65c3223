<template>
  <n-card title="Email Verification" style="max-width: 400px; margin: 50px auto;">
    <div v-if="loading">
      <n-spin size="medium" />
      <p>Verifying your email...</p>
    </div>
    
    <div v-else>
      <n-result
        v-if="success"
        status="success"
        title="Email Verified!"
        description="Your email has been successfully verified. You can now log in to your account."
      >
        <template #footer>
          <n-button type="primary" @click="router.push('/login')">
            Go to Login
          </n-button>
        </template>
      </n-result>      <n-result
        v-else
        status="error"
        :title="error || 'Verification Failed'"
        :description="error ? 'Please try again or contact support.' : ''"
      >
        <template #footer>
          <div class="verification-actions">
            <n-button @click="verifyEmail">
              Try Again
            </n-button>
            <n-button 
              type="primary" 
              @click="showResendModal = true"
              style="margin-left: 8px;"
            >
              Resend Verification Email
            </n-button>
          </div>
        </template>      </n-result>
    </div>

    <!-- Resend Verification Modal -->
    <n-modal v-model:show="showResendModal" preset="dialog" title="Resend Verification Email">
      <template #header>
        <div>Resend Verification Email</div>
      </template>
      <div>
        <p>Enter your email address to receive a new verification email:</p>        <n-input
          v-model:value="resendEmail"
          placeholder="Enter your email"
          style="margin-top: 12px;"
        />
      </div>
      <template #action>
        <n-button @click="showResendModal = false">Cancel</n-button>
        <n-button 
          type="primary" 
          @click="handleResendVerification" 
          :loading="resendingVerification"
          style="margin-left: 8px;"
        >
          Send Email
        </n-button>
      </template>
    </n-modal>
  </n-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import { authService } from '@/services/authService';
import {
  NCard,
  NSpin,
  NButton,
  NResult,
  NModal,
  NInput,
  useMessage,
} from 'naive-ui';

const route = useRoute();
const router = useRouter();
const message = useMessage();

const loading = ref(true);
const success = ref(false);
const error = ref<string | null>(null);
const showResendModal = ref(false);
const resendEmail = ref('');
const resendingVerification = ref(false);

const verifyEmail = async () => {
  loading.value = true;
  error.value = null;
  
  const token = route.query.token as string;
  
  if (!token) {
    error.value = 'No verification token found';
    loading.value = false;
    return;
  }

  try {
    await axios.get(`/api/auth/verify-email?token=${token}`);
    success.value = true;
  } catch (err: any) {
    if (axios.isAxiosError(err) && err.response) {
      error.value = err.response.data.error || 'Verification failed';
    } else {
      error.value = 'An unknown error occurred';
    }
    console.error('Email verification failed:', err);
  } finally {
    loading.value = false;
  }
};

const handleResendVerification = async () => {
  if (!resendEmail.value) {
    message.error('Please enter your email address');
    return;
  }

  resendingVerification.value = true;

  try {
    await authService.resendVerificationEmail(resendEmail.value);
    message.success('Verification email sent! Please check your inbox.');
    showResendModal.value = false;
    resendEmail.value = '';
  } catch (err: any) {
    console.error('Resend verification failed:', err);
    if (axios.isAxiosError(err)) {
      message.error(err.response?.data?.error || 'Failed to send verification email');
    } else {
      message.error('Failed to send verification email');
    }
  } finally {
    resendingVerification.value = false;
  }
};

onMounted(() => {
  verifyEmail();
});
</script>
