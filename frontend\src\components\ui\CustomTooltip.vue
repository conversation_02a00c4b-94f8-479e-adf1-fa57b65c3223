<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'

// Types
interface PaymentInfo {
  currency: string
  bankName: string
  accountNumber: string
  accountHolder?: string
  iban?: string
  swiftCode?: string
  routingNumber?: string
  isComplete: boolean
  validationStatus?: 'pending' | 'complete' | 'failed'
}

interface Props {
  content?: string
  paymentInfo?: PaymentInfo
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  trigger?: 'hover' | 'click' | 'manual'
  disabled?: boolean
  delay?: number
  maxWidth?: string
  offset?: number
  theme?: 'light' | 'dark' | 'auto'
}

const props = withDefaults(defineProps<Props>(), {
  placement: 'auto',
  trigger: 'hover',
  disabled: false,
  delay: 300,
  maxWidth: '280px',
  offset: 8,
  theme: 'auto'
})

const { t, locale } = useI18n()

// Reactive state
const visible = ref(false)
const triggerRef = ref<HTMLElement>()
const tooltipRef = ref<HTMLElement>()
const timeoutId = ref<number>()
const finalPlacement = ref<string>('top')
const repositionTrigger = ref(0) // Reactive flag to trigger position recalculation

// Computed properties
const isRTL = computed(() => locale.value === 'fa')

const triggerAttributes = computed(() => {
  const attributes: Record<string, any> = {
    'data-testid': 'tooltip-trigger'
  }

  if (props.trigger === 'click') {
    // Interactive button for click triggers
    attributes.role = 'button'
    attributes.tabindex = 0
    attributes['aria-haspopup'] = 'dialog'
    attributes['aria-expanded'] = visible.value
    attributes['aria-controls'] = visible.value ? 'tooltip-content' : undefined
  } else if (props.trigger === 'hover') {
    // Focusable element for hover triggers to support keyboard navigation
    attributes.tabindex = 0
    attributes['aria-describedby'] = visible.value ? 'tooltip-content' : undefined
    attributes['aria-label'] = props.content || 'More information available'
  } else {
    // Manual trigger - minimal accessibility
    attributes.tabindex = -1
    attributes['aria-describedby'] = visible.value ? 'tooltip-content' : undefined
  }

  return attributes
})

const containerClasses = computed(() => {
  return {
    'tooltip-container': true,
    'tooltip-container--dark': props.theme === 'dark' || (props.theme === 'auto' && window.matchMedia?.('(prefers-color-scheme: dark)').matches)
  }
})

// Computed for optimal placement calculation
const optimalPlacement = computed(() => {
  // React to repositionTrigger to force recalculation
  repositionTrigger.value
  
  if (!visible.value || !triggerRef.value || !tooltipRef.value) {
    return props.placement
  }

  if (props.placement !== 'auto') {
    return props.placement
  }

  const triggerRect = triggerRef.value.getBoundingClientRect()
  const tooltipRect = tooltipRef.value.getBoundingClientRect()
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  const spaceTop = triggerRect.top
  const spaceBottom = viewport.height - triggerRect.bottom
  const spaceLeft = triggerRect.left
  const spaceRight = viewport.width - triggerRect.right

  // Determine best placement based on available space
  if (spaceBottom >= tooltipRect.height + props.offset) {
    return 'bottom'
  } else if (spaceTop >= tooltipRect.height + props.offset) {
    return 'top'
  } else if (spaceRight >= tooltipRect.width + props.offset) {
    return isRTL.value ? 'left' : 'right'
  } else if (spaceLeft >= tooltipRect.width + props.offset) {
    return isRTL.value ? 'right' : 'left'
  } else {
    // Default to bottom if no ideal space
    return 'bottom'
  }
})

// Computed for position coordinates
const tooltipPosition = computed(() => {
  if (!visible.value || !triggerRef.value || !tooltipRef.value) {
    return { top: 0, left: 0 }
  }

  const triggerRect = triggerRef.value.getBoundingClientRect()
  const tooltipRect = tooltipRef.value.getBoundingClientRect()
  const placement = optimalPlacement.value
  
  let top = 0
  let left = 0

  // Calculate position based on placement
  switch (placement) {
    case 'top':
      top = triggerRect.top - tooltipRect.height - props.offset
      left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
      break
    case 'bottom':
      top = triggerRect.bottom + props.offset
      left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
      break
    case 'left':
      top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
      left = triggerRect.left - tooltipRect.width - props.offset
      break
    case 'right':
      top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
      left = triggerRect.right + props.offset
      break
  }

  return { top, left }
})

// Computed for viewport-constrained position
const constrainedPosition = computed(() => {
  if (!visible.value || !tooltipRef.value) {
    return { top: 0, left: 0 }
  }

  const { top, left } = tooltipPosition.value
  const tooltipRect = tooltipRef.value.getBoundingClientRect()
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  // Ensure tooltip stays within viewport bounds
  const padding = 8
  const constrainedLeft = Math.max(padding, Math.min(left, viewport.width - tooltipRect.width - padding))
  const constrainedTop = Math.max(padding, Math.min(top, viewport.height - tooltipRect.height - padding))

  return { top: constrainedTop, left: constrainedLeft }
})

// Main tooltip style computed property
const tooltipStyle = computed(() => {
  if (!visible.value || !triggerRef.value || !tooltipRef.value) {
    return { opacity: '0', visibility: 'hidden' }
  }

  const { top, left } = constrainedPosition.value
  
  // Update final placement for arrow positioning
  finalPlacement.value = optimalPlacement.value

  return {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    maxWidth: props.maxWidth,
    opacity: '1',
    visibility: 'visible',
    zIndex: '9999'
  }
})

const arrowStyle = computed(() => {
  if (!visible.value || !triggerRef.value || !tooltipRef.value) return {}

  const placement = optimalPlacement.value
  const arrowSize = 6

  switch (placement) {
    case 'top':
      return {
        bottom: `-${arrowSize}px`,
        left: '50%',
        transform: 'translateX(-50%)',
        borderTopColor: 'var(--tooltip-bg)',
        borderBottomWidth: '0'
      }
    case 'bottom':
      return {
        top: `-${arrowSize}px`,
        left: '50%',
        transform: 'translateX(-50%)',
        borderBottomColor: 'var(--tooltip-bg)',
        borderTopWidth: '0'
      }
    case 'left':
      return {
        right: `-${arrowSize}px`,
        top: '50%',
        transform: 'translateY(-50%)',
        borderLeftColor: 'var(--tooltip-bg)',
        borderRightWidth: '0'
      }
    case 'right':
      return {
        left: `-${arrowSize}px`,
        top: '50%',
        transform: 'translateY(-50%)',
        borderRightColor: 'var(--tooltip-bg)',
        borderLeftWidth: '0'
      }
    default:
      return {}
  }
})

// Methods
const showTooltip = async () => {
  if (props.disabled) return
  
  if (timeoutId.value) {
    clearTimeout(timeoutId.value)
  }

  timeoutId.value = window.setTimeout(async () => {
    visible.value = true
    await nextTick()
    // Trigger position recalculation reactively
    repositionTrigger.value += 1
  }, props.delay)
}

const hideTooltip = () => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value)
  }
  visible.value = false
}

const handleTriggerMouseEnter = () => {
  if (props.trigger === 'hover') {
    showTooltip()
  }
}

const handleTriggerMouseLeave = () => {
  if (props.trigger === 'hover') {
    hideTooltip()
  }
}

const handleTriggerClick = () => {
  if (props.trigger === 'click') {
    if (visible.value) {
      hideTooltip()
    } else {
      showTooltip()
    }
  }
}

const handleTriggerFocus = () => {
  if (props.trigger === 'hover') {
    showTooltip()
  }
}

const handleTriggerBlur = () => {
  if (props.trigger === 'hover') {
    hideTooltip()
  }
}

const handleTriggerKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
    case ' ': // Space key
      if (props.trigger === 'click') {
        event.preventDefault()
        handleTriggerClick()
      } else if (props.trigger === 'hover') {
        event.preventDefault()
        showTooltip()
      }
      break
    case 'Escape':
      if (visible.value) {
        event.preventDefault()
        hideTooltip()
        // Return focus to trigger element
        triggerRef.value?.focus()
      }
      break
  }
}

// Handle clicks outside to close tooltip
const handleClickOutside = (event: Event) => {
  if (
    props.trigger === 'click' &&
    visible.value &&
    triggerRef.value &&
    tooltipRef.value &&
    !triggerRef.value.contains(event.target as Node) &&
    !tooltipRef.value.contains(event.target as Node)
  ) {
    hideTooltip()
  }
}

// Handle escape key
const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && visible.value) {
    hideTooltip()
  }
}

// Handle window resize
const handleResize = () => {
  if (visible.value) {
    // Trigger position recalculation on resize
    repositionTrigger.value += 1
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleEscapeKey)
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value)
  }
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleEscapeKey)
  window.removeEventListener('resize', handleResize)
})

// Expose methods for manual control
defineExpose({
  show: showTooltip,
  hide: hideTooltip,
  toggle: () => visible.value ? hideTooltip() : showTooltip()
})
</script>

<template>
  <div 
    :class="containerClasses" 
    data-testid="tooltip-container"
  >
    <!-- Trigger Element -->
    <div
      ref="triggerRef"
      class="tooltip-trigger"
      v-bind="triggerAttributes"
      @mouseenter="handleTriggerMouseEnter"
      @mouseleave="handleTriggerMouseLeave"
      @click="handleTriggerClick"
      @focus="handleTriggerFocus"
      @blur="handleTriggerBlur"
      @keydown="handleTriggerKeydown"
    >
      <slot name="trigger" />
    </div>

    <!-- Tooltip Portal -->
    <Teleport to="body">
      <Transition
        name="tooltip-fade"
        appear
      >
        <div
          v-if="visible"
          ref="tooltipRef"
          class="tooltip"
          :class="[
            `tooltip--${finalPlacement}`,
            `tooltip--${theme}`,
            { 'tooltip--rtl': isRTL }
          ]"
          :style="tooltipStyle"
          data-testid="tooltip-content"
          id="tooltip-content"
          :role="trigger === 'click' ? 'dialog' : 'tooltip'"
          :aria-live="trigger === 'hover' ? 'polite' : undefined"
          :aria-atomic="trigger === 'hover' ? 'true' : undefined"
          :aria-label="trigger === 'click' ? 'Tooltip dialog' : undefined"
          @mouseenter="() => {}"
          @mouseleave="trigger === 'hover' ? hideTooltip() : undefined"
        >
          <!-- Arrow -->
          <div 
            class="tooltip-arrow"
            :style="arrowStyle"
            data-testid="tooltip-arrow"
          />
          
          <!-- Content -->
          <div class="tooltip-content">
            <!-- Payment Info Display -->
            <div 
              v-if="paymentInfo"
              class="payment-info"
              data-testid="payment-info-display"
            >
              <div class="payment-header">
                <div class="payment-currency">
                  <span class="currency-icon">💳</span>
                  <span class="currency-code">{{ paymentInfo.currency }}</span>
                </div>
                <div 
                  class="payment-status"
                  :class="`status--${paymentInfo.validationStatus || 'pending'}`"
                >
                  <span class="status-indicator" />
                  <span class="status-text">
                    {{ t(`transactionalChat.payment.status.${paymentInfo.validationStatus || 'pending'}`) }}
                  </span>
                </div>
              </div>

              <div class="payment-details">
                <div class="detail-row">
                  <span class="detail-label">{{ t('transactionalChat.payment.bankName') }}</span>
                  <span class="detail-value">{{ paymentInfo.bankName }}</span>
                </div>
                
                <div class="detail-row">
                  <span class="detail-label">{{ t('transactionalChat.payment.accountNumber') }}</span>
                  <span class="detail-value account-number">{{ paymentInfo.accountNumber }}</span>
                </div>
                
                <div v-if="paymentInfo.accountHolder" class="detail-row">
                  <span class="detail-label">{{ t('transactionalChat.payment.accountHolder') }}</span>
                  <span class="detail-value">{{ paymentInfo.accountHolder }}</span>
                </div>
                
                <div v-if="paymentInfo.iban" class="detail-row">
                  <span class="detail-label">{{ t('transactionalChat.payment.iban') }}</span>
                  <span class="detail-value account-number">{{ paymentInfo.iban }}</span>
                </div>
                
                <div v-if="paymentInfo.swiftCode" class="detail-row">
                  <span class="detail-label">{{ t('transactionalChat.payment.swiftCode') }}</span>
                  <span class="detail-value">{{ paymentInfo.swiftCode }}</span>
                </div>
                
                <div v-if="paymentInfo.routingNumber" class="detail-row">
                  <span class="detail-label">{{ t('transactionalChat.payment.routingNumber') }}</span>
                  <span class="detail-value">{{ paymentInfo.routingNumber }}</span>
                </div>
              </div>

              <div v-if="!paymentInfo.isComplete" class="payment-warning">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">
                  {{ t('transactionalChat.payment.incompleteWarning') }}
                </span>
              </div>
            </div>

            <!-- Text Content -->
            <div 
              v-else-if="content"
              class="text-content"
              data-testid="text-content"
            >
              {{ content }}
            </div>

            <!-- Slot Content -->
            <div v-else data-testid="slot-content">
              <slot name="content" />
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<style scoped>
/* CSS Variables for theming */
.tooltip-container {
  --tooltip-bg: var(--color-surface-elevated, #ffffff);
  --tooltip-text: var(--color-text-primary, #1a1a1a);
  --tooltip-border: var(--color-border-subtle, #e5e5e5);
  --tooltip-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --tooltip-radius: 8px;
  --tooltip-padding: 12px;
  --tooltip-max-width: 280px;
  
  /* Status colors */
  --status-complete: #22c55e;
  --status-pending: #f59e0b;
  --status-failed: #ef4444;
}

/* Dark theme overrides */
.tooltip-container--dark {
  --tooltip-bg: var(--color-surface-elevated-dark, #2a2a2a);
  --tooltip-text: var(--color-text-primary-dark, #ffffff);
  --tooltip-border: var(--color-border-subtle-dark, #404040);
  --tooltip-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.tooltip-trigger {
  display: inline-block;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.tooltip-trigger:focus {
  outline: 2px solid var(--color-primary, #3b82f6);
  outline-offset: 2px;
  border-radius: 4px;
}

.tooltip {
  position: fixed;
  background: var(--tooltip-bg);
  color: var(--tooltip-text);
  border: 1px solid var(--tooltip-border);
  border-radius: var(--tooltip-radius);
  box-shadow: var(--tooltip-shadow);
  padding: var(--tooltip-padding);
  font-size: 14px;
  line-height: 1.4;
  z-index: 9999;
  max-width: var(--tooltip-max-width);
  min-width: 200px;
  word-wrap: break-word;
  backdrop-filter: blur(8px);
}

/* RTL Support */
.tooltip--rtl {
  direction: rtl;
  text-align: right;
}

.tooltip--rtl .detail-row {
  flex-direction: row-reverse;
}

/* Arrow */
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  pointer-events: none;
}

/* Payment Info Styles */
.payment-info {
  min-width: 240px;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--tooltip-border);
}

.payment-currency {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  font-size: 16px;
}

.currency-icon {
  font-size: 18px;
}

.payment-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status--complete .status-indicator {
  background-color: var(--status-complete);
}

.status--complete .status-text {
  color: var(--status-complete);
}

.status--pending .status-indicator {
  background-color: var(--status-pending);
}

.status--pending .status-text {
  color: var(--status-pending);
}

.status--failed .status-indicator {
  background-color: var(--status-failed);
}

.status--failed .status-text {
  color: var(--status-failed);
}

.payment-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
  min-height: 20px;
}

.detail-label {
  font-size: 12px;
  color: var(--color-text-secondary, #666);
  font-weight: 500;
  flex-shrink: 0;
  min-width: 80px;
}

.detail-value {
  font-size: 13px;
  font-weight: 600;
  text-align: right;
  word-break: break-all;
  flex: 1;
}

.account-number {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  letter-spacing: 0.5px;
}

.payment-warning {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 12px;
  padding: 8px;
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 6px;
  font-size: 12px;
  color: var(--status-pending);
}

.warning-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.warning-text {
  line-height: 1.3;
}

/* Text content */
.text-content {
  line-height: 1.5;
}

/* Transitions */
.tooltip-fade-enter-active,
.tooltip-fade-leave-active {
  transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.tooltip-fade-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(-4px);
}

.tooltip-fade-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-4px);
}

/* Mobile optimizations */
@media (max-width: 480px) {
  .tooltip {
    max-width: calc(100vw - 24px);
    min-width: 200px;
    font-size: 13px;
  }
  
  .payment-info {
    min-width: 200px;
  }
  
  .detail-label {
    min-width: 70px;
    font-size: 11px;
  }
  
  .detail-value {
    font-size: 12px;
  }
  
  .payment-currency {
    font-size: 15px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tooltip {
    border-width: 2px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  }
  
  .status-indicator {
    border: 1px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .tooltip-fade-enter-active,
  .tooltip-fade-leave-active {
    transition: opacity 0.1s linear;
  }
  
  .tooltip-fade-enter-from,
  .tooltip-fade-leave-to {
    transform: none;
  }
  
  .tooltip-trigger {
    transition: none;
  }
}
</style>
