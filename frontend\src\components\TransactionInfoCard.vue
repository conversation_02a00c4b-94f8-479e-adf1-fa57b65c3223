<template>
  <n-card class="transaction-info-card" :bordered="false" size="small">    <div class="transaction-title">
      <n-tag :type="offerTagType" size="medium" class="offer-type-tag">
        {{ offerTypeLabel }} {{ t('transactionInfo.at') }} {{ formattedExchangeRate }} IRR/CAD
      </n-tag>
    </div>
    <div class="transaction-details">      <!-- Row for the user SELLING CAD -->
      <div class="transaction-row">
        <div class="user-info">
          <n-avatar round size="small" />
          <span class="username">{{ sellerUser?.username }}</span>
          <ReputationIcon v-if="sellerUser?.reputationLevel" :level="sellerUser.reputationLevel" size="small" />
        </div>
        <span class="transaction-action">{{ sellerActionText }}</span>
      </div>
      <!-- Row for the user BUYING CAD -->
      <div class="transaction-row">
        <div class="user-info">
          <n-avatar round size="small" />
          <span class="username">{{ buyerUser?.username }}</span>
          <ReputationIcon v-if="buyerUser?.reputationLevel" :level="buyerUser.reputationLevel" size="small" />
        </div>
        <span class="transaction-action">{{ buyerActionText }}</span>
      </div>
    </div>
  </n-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NCard, NTag, NAvatar } from 'naive-ui';
import { useTranslation } from '@/composables/useTranslation';
import ReputationIcon from './ReputationIcon.vue';
import type { TransactionInfo } from '@/types/transaction';
import { formatAmount } from '@/utils/currencyUtils';

const { t } = useTranslation();

const props = defineProps<{
  transactionDetails: TransactionInfo | null;
}>();

const offerTypeLabel = computed(() => {
  if (!props.transactionDetails) return '';
  return props.transactionDetails.offerType === 'SELL' ? t('transactionInfo.sellCAD') : t('transactionInfo.buyCAD');
});

const offerTagType = computed(() => {
  if (!props.transactionDetails) return 'default';
  return props.transactionDetails.offerType === 'SELL' ? 'error' : 'success';
});

// Computed property for formatted exchange rate to prevent recalculation on every render
const formattedExchangeRate = computed(() => {
  return props.transactionDetails?.exchangeRate.toLocaleString() || '0';
});

// sellerUser: The participant who is selling CAD in this transaction
const sellerUser = computed(() => {
  if (!props.transactionDetails) return null;
  // If the original offer was 'SELL', the offerCreator is the seller.
  // If the original offer was 'BUY', the otherUser (taker) is the seller.
  return props.transactionDetails.offerType === 'SELL'
    ? props.transactionDetails.offerCreator
    : props.transactionDetails.otherUser;
});

// buyerUser: The participant who is buying CAD in this transaction
const buyerUser = computed(() => {
  if (!props.transactionDetails) return null;
  // If the original offer was 'SELL', the otherUser (taker) is the buyer.
  // If the original offer was 'BUY', the offerCreator is the buyer.
  return props.transactionDetails.offerType === 'SELL'
    ? props.transactionDetails.otherUser
    : props.transactionDetails.offerCreator;
});

const totalIRRAmount = computed(() => {
  if (!props.transactionDetails) return '0';
  const total = props.transactionDetails.amount * props.transactionDetails.exchangeRate;
  return formatAmount(total, 'IRR');
});

// Action text for the sellerUser row (the one selling CAD)
const sellerActionText = computed(() => {
  if (!props.transactionDetails || !sellerUser.value) return '';
  // The user selling CAD receives IRR.
  // Or, if you prefer to state what they do with CAD: `sells ${formatAmount(props.transactionDetails.amount, 'CAD')} CAD`
  // Based on user request: "hosami : recives 40000 IRR" for the seller in a BUY offer.
  if (props.transactionDetails.offerType === 'BUY') { // Offer creator buys, otherUser (sellerUser) sells
     return t('transactionInfo.receives', { amount: totalIRRAmount.value });
  } else { // Offer creator (sellerUser) sells
     return t('transactionInfo.sells', { amount: formatAmount(props.transactionDetails.amount, 'CAD') });
  }
});

// Action text for the buyerUser row (the one buying CAD)
const buyerActionText = computed(() => {
  if (!props.transactionDetails || !buyerUser.value) return '';
  const amount = formatAmount(props.transactionDetails.amount, 'CAD');
  // The user buying CAD.
  // Based on user request: "hosami 1 Buys 200 CAD" for the buyer in a BUY offer.
   if (props.transactionDetails.offerType === 'BUY') { // Offer creator (buyerUser) buys
    return t('transactionInfo.buys', { amount });
  } else { // Offer creator sells, otherUser (buyerUser) buys
    // The buyer of CAD pays IRR.
    return t('transactionInfo.pays', { amount: totalIRRAmount.value });
  }
});

</script>

<style scoped>
.transaction-info-card {
  margin: 0;
  padding: 10px 16px;
  background: var(--n-card-color);
  border-bottom: 1px solid var(--n-border-color);
  border-radius: 0;
  transition: box-shadow 0.3s ease;
}

.transaction-title {
  margin-bottom: 8px;
}

.transaction-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.transaction-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.offer-type-tag {
  flex-shrink: 0;
}

.transaction-action {
  color: var(--n-text-color);
  font-size: 0.9em;
  white-space: nowrap;
  font-weight: 500;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.username {
  font-size: 0.9em;
  max-width: 100px; /* Adjust as needed */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
