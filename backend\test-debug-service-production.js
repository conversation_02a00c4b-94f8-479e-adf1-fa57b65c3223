const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Import the service (adjust path as needed)
async function testDebugReportService() {
  console.log('🧪 Testing DebugReportService in production...\n');
  
  try {
    // Simulate the service logic
    const options = {
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    console.log('1. Testing database query...');
    
    const where = {};
    const allowedSortFields = ['createdAt', 'updatedAt', 'severity', 'priority', 'status', 'type', 'title'];
    const validatedSortBy = allowedSortFields.includes(options.sortBy) ? options.sortBy : 'createdAt';
    
    const [reports, total] = await Promise.all([
      prisma.debugReport.findMany({
        where,
        skip: (options.page - 1) * options.limit,
        take: options.limit,
        orderBy: { [validatedSortBy]: options.sortOrder },
        include: {
          user: { select: { id: true, email: true, username: true } },
          assignedTo: { select: { id: true, email: true, username: true } },
          tags: true,
          _count: { select: { comments: true } }
        }
      }),
      prisma.debugReport.count({ where })
    ]);

    console.log(`✅ Query successful! Found ${total} total reports, returned ${reports.length}`);

    console.log('\n2. Testing report formatting...');
    
    const formattedReports = reports.map(report => {
      try {
        return {
          id: report.id,
          reportId: report.reportId,
          reportType: report.type.toLowerCase().replace('_', '-'),
          reportSeverity: report.severity.toLowerCase(),
          reportTitle: report.title,
          reportDescription: report.description,
          type: report.type.toLowerCase().replace('_', '-'), // Keep for backward compatibility
          severity: report.severity.toLowerCase(), // Keep for backward compatibility
          status: report.status.toLowerCase().replace('_', '-'),
          priority: report.priority,
          title: report.title,
          description: report.description,
          stepsToReproduce: report.stepsToReproduce,
          expectedBehavior: report.expectedBehavior,
          actualBehavior: report.actualBehavior,
          additionalNotes: report.additionalNotes,
          user: report.user,
          // Add user fields that frontend expects
          userId: report.userId,
          userEmail: report.user?.email,
          username: report.user?.username,
          assignedTo: report.assignedTo,
          assignedAt: report.assignedAt,
          tags: report.tags?.map((t) => t.tag) || [],
          hasTags: (report.tags?.length || 0) > 0,
          logCount: Array.isArray(report.logs) ? report.logs.length : 0,
          commentCount: report._count?.comments || 0,
          statusHistory: report.statusHistory,
          comments: report.comments,
          sessionId: report.sessionId,
          currentUrl: report.currentUrl,
          userAgent: report.userAgent,
          viewportWidth: report.viewportWidth,
          viewportHeight: report.viewportHeight,
          diagnosticData: report.diagnosticData,
          logs: report.logs || [],
          userActions: report.userActions,
          timestamp: report.clientTimestamp, // Add timestamp field frontend expects
          clientTimestamp: report.clientTimestamp,
          serverReceivedAt: report.serverReceivedAt,
          createdAt: report.createdAt,
          updatedAt: report.updatedAt
        };
      } catch (formatError) {
        console.log(`❌ Error formatting report ${report.reportId}:`, formatError.message);
        return null;
      }
    }).filter(Boolean);

    console.log(`✅ Formatting successful! Formatted ${formattedReports.length} reports`);

    console.log('\n3. Testing response structure...');
    const totalPages = Math.ceil(total / options.limit);
    const response = {
      reports: formattedReports,
      total,
      totalPages,
      currentPage: options.page
    };

    console.log('✅ Response structure created successfully');
    console.log(`   Reports: ${response.reports.length}`);
    console.log(`   Total: ${response.total}`);
    console.log(`   Total Pages: ${response.totalPages}`);
    console.log(`   Current Page: ${response.currentPage}`);

    if (formattedReports.length > 0) {
      console.log('\n4. Sample report data:');
      const sample = formattedReports[0];
      console.log(`   ID: ${sample.reportId}`);
      console.log(`   Type: ${sample.reportType}`);
      console.log(`   Severity: ${sample.reportSeverity}`);
      console.log(`   Title: ${sample.reportTitle}`);
      console.log(`   User: ${sample.userEmail || 'Anonymous'}`);
    }

    console.log('\n🎉 Service test completed successfully!');

  } catch (error) {
    console.error('❌ Service test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

testDebugReportService();
