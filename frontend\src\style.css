/* Import RTL utilities */
@import './utils/rtl.css';

:root {
  font-family: 'Inter', 'Vazirmatn', '<PERSON>azi<PERSON>', '<PERSON>hom<PERSON>', 'Iran Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Persian/RTL font optimization */
[dir="rtl"],
body.rtl {
  font-family: 'Vazirmatn', 'Vazir', 'Tahoma', 'Iran Sans', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6; /* Slightly increased line height for Persian text readability */
  font-feature-settings: "liga" 1, "kern" 1; /* Enable ligatures and kerning for better Persian text */
  text-rendering: optimizeLegibility;
}

/* Persian text elements */
[dir="rtl"] *,
[lang="fa"] *,
body.rtl *,
.persian-text {
  font-family: 'Vazirmatn', 'Vazir', '<PERSON>homa', 'Iran Sans', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* RTL Text Alignment and Layout */
[dir="rtl"],
body.rtl {
  text-align: right;
}

[dir="rtl"] .n-card,
[dir="rtl"] .n-modal,
[dir="rtl"] .n-drawer,
[dir="rtl"] .n-message,
[dir="rtl"] .n-notification,
body.rtl .n-card,
body.rtl .n-modal,
body.rtl .n-drawer,
body.rtl .n-message,
body.rtl .n-notification {
  text-align: right;
  direction: rtl;
}

/* RTL Input and Form Elements */
[dir="rtl"] .n-input,
[dir="rtl"] .n-input-group,
[dir="rtl"] .n-select,
[dir="rtl"] .n-textarea,
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select,
body.rtl .n-input,
body.rtl .n-input-group,
body.rtl .n-select,
body.rtl .n-textarea,
body.rtl input,
body.rtl textarea,
body.rtl select {
  text-align: right;
  direction: rtl;
}

/* RTL Button Content */
[dir="rtl"] .n-button,
[dir="rtl"] button,
body.rtl .n-button,
body.rtl button {
  direction: rtl;
}

/* RTL Flex Direction Adjustments */
[dir="rtl"] .flex-row,
body.rtl .flex-row {
  flex-direction: row-reverse;
}

/* RTL Margin and Padding Adjustments */
[dir="rtl"] .ml-auto,
body.rtl .ml-auto {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .mr-auto,
body.rtl .mr-auto {
  margin-right: 0;
  margin-left: auto;
}

/* RTL Border Radius */
[dir="rtl"] .rounded-l,
body.rtl .rounded-l {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

[dir="rtl"] .rounded-r,
body.rtl .rounded-r {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

/* RTL Navigation and Menu Items */
[dir="rtl"] .n-menu-item,
[dir="rtl"] .n-dropdown-option,
[dir="rtl"] .n-tabs-tab,
body.rtl .n-menu-item,
body.rtl .n-dropdown-option,
body.rtl .n-tabs-tab {
  text-align: right;
  direction: rtl;
}

/* RTL List Items */
[dir="rtl"] .n-list-item,
[dir="rtl"] .n-descriptions-item,
body.rtl .n-list-item,
body.rtl .n-descriptions-item {
  text-align: right;
  direction: rtl;
}

/* RTL Table Content */
[dir="rtl"] .n-data-table,
[dir="rtl"] .n-table,
body.rtl .n-data-table,
body.rtl .n-table {
  direction: rtl;
}

[dir="rtl"] .n-data-table th,
[dir="rtl"] .n-data-table td,
[dir="rtl"] .n-table th,
[dir="rtl"] .n-table td,
body.rtl .n-data-table th,
body.rtl .n-data-table td,
body.rtl .n-table th,
body.rtl .n-table td {
  text-align: right;
}

/* RTL Typography */
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6,
[dir="rtl"] p,
[dir="rtl"] span,
[dir="rtl"] div,
[dir="rtl"] .n-text,
body.rtl h1,
body.rtl h2,
body.rtl h3,
body.rtl h4,
body.rtl h5,
body.rtl h6,
body.rtl p,
body.rtl span,
body.rtl div,
body.rtl .n-text {
  text-align: right;
  direction: rtl;
}

/* Dark theme variables */
[data-theme="dark"] {
  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
}

/* Light theme variables */
[data-theme="light"] {
  color-scheme: light;
  color: #213547;
  background-color: #ffffff;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: border-color 0.25s;
}

/* Persian font optimization for buttons and form elements */
[dir="rtl"] button,
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select,
[lang="fa"] button,
[lang="fa"] input,
[lang="fa"] textarea,
[lang="fa"] select,
body.rtl button,
body.rtl input,
body.rtl textarea,
body.rtl select {
  font-family: 'Vazirmatn', 'Vazir', 'Tahoma', 'Iran Sans', -apple-system, BlinkMacSystemFont, sans-serif;
  text-align: right;
  direction: rtl;
}

/* Dark theme button styles */
[data-theme="dark"] button {
  background-color: #1a1a1a;
}
[data-theme="dark"] button:hover {
  border-color: #646cff;
}

/* Light theme button styles */
[data-theme="light"] button {
  background-color: #f9f9f9;
}
[data-theme="light"] button:hover {
  border-color: #747bff;
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* Dark theme input styles */
[data-theme="dark"] .n-input .n-input__input-el,
[data-theme="dark"] .n-input-group .n-input .n-input__input-el,
[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="email"],
[data-theme="dark"] input[type="password"],
[data-theme="dark"] input[type="tel"],
[data-theme="dark"] input[type="number"],
[data-theme="dark"] textarea {
  color: #ffffff !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  -webkit-text-fill-color: #ffffff !important;
  -webkit-appearance: none !important;
}

[data-theme="dark"] .n-input .n-input__input-el::placeholder,
[data-theme="dark"] .n-input-group .n-input .n-input__input-el::placeholder,
[data-theme="dark"] input::placeholder,
[data-theme="dark"] textarea::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.5) !important;
}

[data-theme="dark"] .n-input,
[data-theme="dark"] .n-input-group .n-input,
[data-theme="dark"] .n-input--focus {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

[data-theme="dark"] .n-input:hover,
[data-theme="dark"] .n-input-group .n-input:hover {
  border-color: rgba(255, 255, 255, 0.3) !important;
}

[data-theme="dark"] .n-input--focus,
[data-theme="dark"] .n-input-group .n-input--focus {
  border-color: #646cff !important;
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.2) !important;
}

[data-theme="dark"] .n-input .n-input__input-el:-webkit-autofill,
[data-theme="dark"] .n-input .n-input__input-el:-webkit-autofill:hover,
[data-theme="dark"] .n-input .n-input__input-el:-webkit-autofill:focus,
[data-theme="dark"] .n-input .n-input__input-el:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px rgba(0, 0, 0, 0.3) inset !important;
  -webkit-text-fill-color: #ffffff !important;
  color: #ffffff !important;
}

/* Light theme input styles */
[data-theme="light"] .n-input .n-input__input-el,
[data-theme="light"] .n-input-group .n-input .n-input__input-el,
[data-theme="light"] input[type="text"],
[data-theme="light"] input[type="email"],
[data-theme="light"] input[type="password"],
[data-theme="light"] input[type="tel"],
[data-theme="light"] input[type="number"],
[data-theme="light"] textarea {
  color: #213547 !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
  -webkit-text-fill-color: #213547 !important;
  -webkit-appearance: none !important;
}

[data-theme="light"] .n-input .n-input__input-el::placeholder,
[data-theme="light"] .n-input-group .n-input .n-input__input-el::placeholder,
[data-theme="light"] input::placeholder,
[data-theme="light"] textarea::placeholder {
  color: rgba(33, 53, 71, 0.5) !important;
  -webkit-text-fill-color: rgba(33, 53, 71, 0.5) !important;
}

[data-theme="light"] .n-input,
[data-theme="light"] .n-input-group .n-input,
[data-theme="light"] .n-input--focus {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(33, 53, 71, 0.2) !important;
}

[data-theme="light"] .n-input:hover,
[data-theme="light"] .n-input-group .n-input:hover {
  border-color: rgba(33, 53, 71, 0.3) !important;
}

[data-theme="light"] .n-input--focus,
[data-theme="light"] .n-input-group .n-input--focus {
  border-color: #646cff !important;
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.2) !important;
}

[data-theme="light"] .n-input .n-input__input-el:-webkit-autofill,
[data-theme="light"] .n-input .n-input__input-el:-webkit-autofill:hover,
[data-theme="light"] .n-input .n-input__input-el:-webkit-autofill:focus,
[data-theme="light"] .n-input .n-input__input-el:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px rgba(255, 255, 255, 0.9) inset !important;
  -webkit-text-fill-color: #213547 !important;
  color: #213547 !important;
}

/* Enhanced autofill overlay prevention and stabilization */
.autofill-stable {
  position: relative !important;
  isolation: isolate !important;
}

.autofill-stable input,
.autofill-stable .n-input,
.autofill-stable .n-input-wrapper {
  position: relative !important;
  z-index: 1 !important;
}

/* Prevent autofill overlay positioning conflicts */
.autofill-stable::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  pointer-events: none;
}

/* Form stabilization for autofill compatibility */
form {
  position: relative;
  isolation: isolate;
}

/* Prevent autofill overlay DOM manipulation issues */
input[type="email"],
input[type="password"],
input[type="text"],
input[type="tel"] {
  position: relative !important;
  z-index: 1 !important;
}

/* Naive UI specific autofill stabilization */
.n-form-item {
  position: relative;
  isolation: isolate;
}

.n-input {
  position: relative !important;
  z-index: 1 !important;
}

.n-input-wrapper {
  position: relative !important;
  z-index: 1 !important;
}
