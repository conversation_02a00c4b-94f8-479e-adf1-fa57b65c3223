-- Comprehensive Production Tag System Migration
-- This script drops all existing tag-related tables and recreates them to match local dev exactly

-- Drop all tag-related tables and their dependencies
DROP TABLE IF EXISTS tag_report_type_associations CASCADE;
DROP TABLE IF EXISTS debug_report_tags CASCADE;
DROP TABLE IF EXISTS tags CASCADE;
DROP TABLE IF EXISTS tag_categories CASCADE;
DROP TABLE IF EXISTS predefined_tags CASCADE;
DROP TABLE IF EXISTS tag_metadata CASCADE;

-- Create tag_categories table (exactly matching local dev)
CREATE TABLE tag_categories (
    id text NOT NULL,
    name character varying(50) NOT NULL,
    description text,
    color character varying(7),
    "order" integer NOT NULL DEFAULT 0,
    "isActive" boolean NOT NULL DEFAULT true,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL
);

-- Create indexes for tag_categories
ALTER TABLE ONLY tag_categories ADD CONSTRAINT tag_categories_pkey PRIMARY KEY (id);
CREATE INDEX tag_categories_name_idx ON tag_categories USING btree (name);
ALTER TABLE ONLY tag_categories ADD CONSTRAINT tag_categories_name_key UNIQUE (name);
CREATE INDEX tag_categories_order_idx ON tag_categories USING btree ("order");

-- Create tags table (exactly matching local dev)
CREATE TABLE tags (
    id text NOT NULL,
    name character varying(50) NOT NULL,
    "displayName" jsonb NOT NULL,
    description jsonb,
    category_id text,
    color character varying(7),
    icon character varying(50),
    weight integer NOT NULL DEFAULT 0,
    is_active boolean NOT NULL DEFAULT true,
    is_system boolean NOT NULL DEFAULT false,
    usage_count integer NOT NULL DEFAULT 0,
    last_used_at timestamp(3) without time zone,
    ai_relevance double precision DEFAULT 0.0,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL
);

-- Create indexes for tags
ALTER TABLE ONLY tags ADD CONSTRAINT tags_pkey PRIMARY KEY (id);
CREATE INDEX tags_ai_relevance_idx ON tags USING btree (ai_relevance);
CREATE INDEX tags_category_id_idx ON tags USING btree (category_id);
CREATE INDEX tags_is_active_idx ON tags USING btree (is_active);
CREATE INDEX tags_name_idx ON tags USING btree (name);
ALTER TABLE ONLY tags ADD CONSTRAINT tags_name_key UNIQUE (name);
CREATE INDEX tags_usage_count_idx ON tags USING btree (usage_count);

-- Create foreign key constraint for tags
ALTER TABLE ONLY tags ADD CONSTRAINT tags_category_id_fkey FOREIGN KEY (category_id) REFERENCES tag_categories(id) ON UPDATE CASCADE ON DELETE SET NULL;

-- Create tag_report_type_associations table (exactly matching local dev)
CREATE TABLE tag_report_type_associations (
    id text NOT NULL,
    tag_id text NOT NULL,
    report_type character varying(50) NOT NULL,
    weight integer NOT NULL DEFAULT 0,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for tag_report_type_associations
ALTER TABLE ONLY tag_report_type_associations ADD CONSTRAINT tag_report_type_associations_pkey PRIMARY KEY (id);
CREATE INDEX tag_report_type_associations_report_type_idx ON tag_report_type_associations USING btree (report_type);
ALTER TABLE ONLY tag_report_type_associations ADD CONSTRAINT tag_report_type_associations_tag_id_report_type_key UNIQUE (tag_id, report_type);
CREATE INDEX tag_report_type_associations_weight_idx ON tag_report_type_associations USING btree (weight);

-- Create foreign key constraint for tag_report_type_associations
ALTER TABLE ONLY tag_report_type_associations ADD CONSTRAINT tag_report_type_associations_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES tags(id) ON UPDATE CASCADE ON DELETE CASCADE;

-- Insert tag categories (matching local dev data)
INSERT INTO tag_categories (id, name, description, color, "order", "isActive", created_at, updated_at) VALUES
('10956015-6103-4ff9-9c34-7aea2170a51a', 'Priority', 'Tags indicating priority and urgency', '#ef4444', 1, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('fe3b3792-04e0-49ba-99de-16b5964f76d8', 'Technical', 'Technical issue related tags', '#dc2626', 2, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('c1369ac0-d249-471c-9067-76351c2fd3fb', 'Feature', 'Feature requests and ideas', '#3b82f6', 3, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('936d5844-ec6b-43bf-88bf-7a41ec765201', 'Improvement', 'Enhancement and improvement tags', '#10b981', 4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('894962be-3074-4e3b-ba28-e4690c5c0a3c', 'Performance', 'Performance related tags', '#f59e0b', 5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert predefined tags (matching local dev data)
INSERT INTO tags (id, name, "displayName", description, category_id, color, icon, weight, is_active, is_system, usage_count, last_used_at, ai_relevance, created_at, updated_at) VALUES
('4c9c3f1a-b605-4037-b060-a037efa18d75', 'urgent', '{"en": "Urgent", "fa": "فوری"}', NULL, '10956015-6103-4ff9-9c34-7aea2170a51a', '#ef4444', 'alert-triangle', 100, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('d7ef7039-8ce0-43f5-87be-5ffa5d1b8313', 'fix-needed', '{"en": "Fix Needed", "fa": "نیاز به رفع"}', NULL, '10956015-6103-4ff9-9c34-7aea2170a51a', '#ef4444', 'tool', 90, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('3c42a34b-87c7-410c-8e16-c617d03a3aa4', 'error', '{"en": "Error", "fa": "خطا"}', NULL, 'fe3b3792-04e0-49ba-99de-16b5964f76d8', '#dc2626', 'x-circle', 95, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('35b80061-2d8d-41bd-a722-483e5dbbe48f', 'enhancement', '{"en": "Enhancement", "fa": "بهبود"}', NULL, '936d5844-ec6b-43bf-88bf-7a41ec765201', '#10b981', 'trending-up', 60, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('2919ad83-dd8f-488a-ad28-1debcaf08ff9', 'new-feature', '{"en": "New Feature", "fa": "ویژگی جدید"}', NULL, 'c1369ac0-d249-471c-9067-76351c2fd3fb', '#3b82f6', 'plus-circle', 70, true, true, 0, NULL, 0.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert tag-report type associations (matching local dev data)
INSERT INTO tag_report_type_associations (id, tag_id, report_type, weight, created_at) VALUES
('20448195-3e67-4e47-a84b-66a02fda8e93', '4c9c3f1a-b605-4037-b060-a037efa18d75', 'BUG', 100, CURRENT_TIMESTAMP),
('51986856-be3d-4941-94d7-3917725465ce', 'd7ef7039-8ce0-43f5-87be-5ffa5d1b8313', 'BUG', 100, CURRENT_TIMESTAMP),
('47435d8d-067f-4263-bfc1-8d7e713257ca', '3c42a34b-87c7-410c-8e16-c617d03a3aa4', 'BUG', 100, CURRENT_TIMESTAMP),
('4ce61626-f1f5-43eb-b413-8fcc49ca7443', '35b80061-2d8d-41bd-a722-483e5dbbe48f', 'FEATURE_REQUEST', 100, CURRENT_TIMESTAMP),
('dca07fcc-b2ce-4053-ac8c-47caaefa3013', '2919ad83-dd8f-488a-ad28-1debcaf08ff9', 'FEATURE_REQUEST', 100, CURRENT_TIMESTAMP);

-- Verify the migration worked correctly
SELECT 'Tag Categories Count:' as info, COUNT(*) as count FROM tag_categories;
SELECT 'Tags Count:' as info, COUNT(*) as count FROM tags;
SELECT 'Tag Associations Count:' as info, COUNT(*) as count FROM tag_report_type_associations;
