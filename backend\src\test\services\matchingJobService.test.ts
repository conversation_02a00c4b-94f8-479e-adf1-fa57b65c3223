import { describe, it, expect, vi, beforeEach, afterEach, type Mocked } from 'vitest';
import { MatchingJobService } from '../../services/matchingJobService';
import { MatchingService } from '../../services/matchingService';

// Mock dependencies
vi.mock('../../services/matchingService');
vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn(),
  OfferStatus: {
    ACTIVE: 'ACTIVE',
    INACTIVE: 'INACTIVE',
    PAUSED: 'PAUSED',
    COMPLETED: 'COMPLETED'
  }
}));

describe('MatchingJobService', () => {
  let matchingJobService: MatchingJobService;
  let mockMatchingService: Mocked<MatchingService>;
  let mockPrisma: any;
  let mockLogger: any;

  beforeEach(() => {
    // Create mock PrismaClient
    mockPrisma = {
      offer: {
        findMany: vi.fn(),
        update: vi.fn(),
      },
      offerMatch: {
        updateMany: vi.fn(),
      },
    };

    // Create mock MatchingService
    mockMatchingService = {
      findPotentialMatches: vi.fn(),
    } as any;

    // Create mock logger
    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    };

    matchingJobService = new MatchingJobService(mockPrisma, mockMatchingService, mockLogger);
  });
  afterEach(() => {
    vi.clearAllMocks();
    matchingJobService.stop();
  });

  describe('start', () => {
    it('should start the background job with correct interval', () => {
      const setIntervalSpy = vi.spyOn(global, 'setInterval');
      
      matchingJobService.start();
      
      expect(setIntervalSpy).toHaveBeenCalledWith(
        expect.any(Function),
        30000 // 30 seconds (actual interval from implementation)
      );
      expect(matchingJobService.getStatus().isRunning).toBe(true);
    });

    it('should not start multiple intervals if already running', () => {
      const setIntervalSpy = vi.spyOn(global, 'setInterval');
      
      matchingJobService.start();
      matchingJobService.start(); // Second call
      
      expect(setIntervalSpy).toHaveBeenCalledTimes(1);
      expect(mockLogger.warn).toHaveBeenCalledWith('[MatchingJobService] Already running');
    });

    it('should execute matching job immediately when started', async () => {
      mockPrisma.offer.findMany.mockResolvedValue([]);
      
      matchingJobService.start();
      
      // Wait a bit for async processing
      await new Promise(resolve => setTimeout(resolve, 10));
      
      expect(mockPrisma.offer.findMany).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith('[MatchingJobService] Starting match processing cycle');
    });
  });
  describe('stop', () => {
    it('should stop the background job', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      matchingJobService.start();
      const wasRunning = matchingJobService.getStatus().isRunning;
      matchingJobService.stop();
      
      expect(wasRunning).toBe(true);
      expect(matchingJobService.getStatus().isRunning).toBe(false);
      expect(clearIntervalSpy).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith('[MatchingJobService] Stopped background matching service');
    });

    it('should handle stop when not running', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      matchingJobService.stop(); // Stop without starting
      
      expect(clearIntervalSpy).not.toHaveBeenCalled();
      expect(matchingJobService.getStatus().isRunning).toBe(false);
      expect(mockLogger.warn).toHaveBeenCalledWith('[MatchingJobService] Not running');
    });
  });
  describe('processMatches', () => {
    beforeEach(() => {
      // Setup common mocks for processMatches tests
      mockPrisma.offer.findMany.mockResolvedValue([]);
      mockPrisma.offer.update.mockResolvedValue({} as any);
      mockMatchingService.findPotentialMatches.mockResolvedValue([]);
    });    it('should process active offers successfully', async () => {
      const mockOffers = [
        {
          id: 'offer1',
          status: 'ACTIVE',
          user: { id: 'user1', username: 'test', email: '<EMAIL>', reputationScore: 100, reputationLevel: 1 }
        },
        {
          id: 'offer2', 
          status: 'ACTIVE',
          user: { id: 'user2', username: 'test2', email: '<EMAIL>', reputationScore: 200, reputationLevel: 2 }
        }
      ];

      mockPrisma.offer.findMany.mockResolvedValue(mockOffers as any);
      mockMatchingService.findPotentialMatches.mockResolvedValue([]);

      matchingJobService.start(); // Start the service first
      await matchingJobService.triggerMatching();expect(mockPrisma.offer.findMany).toHaveBeenCalledWith({
        where: {
          status: 'ACTIVE', // Use string literal instead of enum
          OR: [
            { lastMatchedAt: null },
            { lastMatchedAt: { lt: expect.any(Date) } }
          ]
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
              reputationScore: true,
              reputationLevel: true
            }
          }
        },
        take: 50
      });      expect(mockMatchingService.findPotentialMatches).toHaveBeenCalledTimes(4); // 2 offers × 2 cycles
      expect(mockPrisma.offer.update).toHaveBeenCalledTimes(4); // 2 offers × 2 cycles
      expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('Completed cycle'));
    });    it('should handle individual offer processing errors gracefully', async () => {
      const mockOffers = [
        { id: 'offer1', status: 'ACTIVE', user: { id: 'user1', username: 'test', email: '<EMAIL>', reputationScore: 100, reputationLevel: 1 } }
      ];

      mockPrisma.offer.findMany.mockResolvedValue(mockOffers as any);
      mockMatchingService.findPotentialMatches.mockRejectedValue(new Error('Match error'));

      matchingJobService.start(); // Start the service first
      await matchingJobService.triggerMatching();

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error processing offer offer1'),
        expect.any(Error)
      );
      // Should still complete the cycle despite errors
      expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('Completed cycle'));
    });

    it('should handle database errors and stop after max consecutive errors', async () => {
      matchingJobService.start();
      
      // Mock database error
      mockPrisma.offer.findMany.mockRejectedValue(new Error('Database error'));

      // Trigger multiple errors by calling processMatches multiple times
      const processMatchesSpy = vi.spyOn(matchingJobService as any, 'processMatches');
      
      // Process 5 times to hit max consecutive errors
      for (let i = 0; i < 5; i++) {
        await (matchingJobService as any).processMatches();
      }

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Too many consecutive errors, stopping service'),
      );
      expect(matchingJobService.getStatus().isRunning).toBe(false);
    });
  });
  describe('getStatus', () => {
    it('should return correct status initially', () => {
      const status = matchingJobService.getStatus();
      
      expect(status.isRunning).toBe(false);
      expect(status.consecutiveErrors).toBe(0);
      expect(status.maxConsecutiveErrors).toBe(5);
      expect(status.intervalMs).toBe(30000);
      expect(status.lastProcessedTime).toBeInstanceOf(Date);
    });

    it('should return true when started', () => {
      matchingJobService.start();
      const status = matchingJobService.getStatus();
      
      expect(status.isRunning).toBe(true);
    });

    it('should return false after stopped', () => {
      matchingJobService.start();
      matchingJobService.stop();
      const status = matchingJobService.getStatus();
      
      expect(status.isRunning).toBe(false);
    });
  });

  describe('triggerMatching', () => {
    it('should manually trigger matching when running', async () => {
      mockPrisma.offer.findMany.mockResolvedValue([]);
      
      matchingJobService.start();
      await matchingJobService.triggerMatching();

      expect(mockPrisma.offer.findMany).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith('[MatchingJobService] Manual matching trigger');
    });

    it('should throw error when not running', async () => {
      await expect(matchingJobService.triggerMatching()).rejects.toThrow(
        'MatchingJobService is not running'
      );
    });
  });

  describe('cleanupExpiredMatches', () => {
    it('should clean up expired matches successfully', async () => {
      mockPrisma.offerMatch.updateMany.mockResolvedValue({ count: 5 });

      await matchingJobService.cleanupExpiredMatches();

      expect(mockPrisma.offerMatch.updateMany).toHaveBeenCalledWith({
        where: {
          status: 'PENDING',
          createdAt: { lt: expect.any(Date) }
        },
        data: {
          status: 'EXPIRED',
          updatedAt: expect.any(Date)
        }
      });

      expect(mockLogger.info).toHaveBeenCalledWith('[MatchingJobService] Expired 5 matches');
    });

    it('should handle cleanup errors gracefully', async () => {
      const cleanupError = new Error('Database error');
      mockPrisma.offerMatch.updateMany.mockRejectedValue(cleanupError);

      await matchingJobService.cleanupExpiredMatches();

      expect(mockLogger.error).toHaveBeenCalledWith(
        '[MatchingJobService] Error cleaning up expired matches:',
        cleanupError
      );
    });
  });
  describe('integration behavior', () => {
    it('should handle rapid start/stop cycles', () => {
      const setIntervalSpy = vi.spyOn(global, 'setInterval');
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');

      // Rapid start/stop
      matchingJobService.start();
      matchingJobService.stop();
      matchingJobService.start();
      matchingJobService.stop();

      expect(setIntervalSpy).toHaveBeenCalledTimes(2);
      expect(clearIntervalSpy).toHaveBeenCalledTimes(2);
      expect(matchingJobService.getStatus().isRunning).toBe(false);
    });

    it('should maintain state consistency during async operations', async () => {
      // Setup slow async operations
      mockPrisma.offer.findMany.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve([]), 100))
      );

      matchingJobService.start();
      expect(matchingJobService.getStatus().isRunning).toBe(true);

      // Stop while async operations might be running
      matchingJobService.stop();
      expect(matchingJobService.getStatus().isRunning).toBe(false);

      // Wait for any pending operations
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(matchingJobService.getStatus().isRunning).toBe(false);
    });

    it('should process offers with matches and log results', async () => {
      const mockOffers = [
        { id: 'offer1', status: 'ACTIVE', user: { id: 'user1', username: 'test', email: '<EMAIL>', reputationScore: 100, reputationLevel: 1 } }
      ];
      const mockMatches = [{ id: 'match1' }, { id: 'match2' }];

      mockPrisma.offer.findMany.mockResolvedValue(mockOffers as any);
      mockMatchingService.findPotentialMatches.mockResolvedValue(mockMatches as any);

      matchingJobService.start();
      await matchingJobService.triggerMatching();

      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('Found 2 matches for offer offer1')
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('1 offers processed, 2 matches found')
      );
    });
  });
  describe('lifecycle management', () => {
    it('should cleanup properly on stop', () => {
      const setIntervalSpy = vi.spyOn(global, 'setInterval');
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');

      matchingJobService.start();
      const intervalId = setIntervalSpy.mock.results[0].value;
      
      matchingJobService.stop();
      
      expect(clearIntervalSpy).toHaveBeenCalledWith(intervalId);
      expect(mockLogger.info).toHaveBeenCalledWith('[MatchingJobService] Stopped background matching service');
    });

    it('should handle process termination gracefully', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      matchingJobService.start();
      
      // Simulate process termination
      matchingJobService.stop();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
      expect(matchingJobService.getStatus().isRunning).toBe(false);
    });

    it('should reset consecutive errors on successful cycle', async () => {
      mockPrisma.offer.findMany.mockResolvedValue([]);
      
      // Simulate some errors first
      const status = matchingJobService.getStatus();
      (matchingJobService as any).consecutiveErrors = 3;

      matchingJobService.start();
      await matchingJobService.triggerMatching();

      // After successful cycle, consecutive errors should be reset
      expect(matchingJobService.getStatus().consecutiveErrors).toBe(0);
    });
  });
});
