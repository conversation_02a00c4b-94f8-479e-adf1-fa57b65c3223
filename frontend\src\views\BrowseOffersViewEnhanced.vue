<template>
  <!-- Enhanced Browse Offers View -->
  <div class="browse-offers-view" :class="{ 'enhanced': useEnhancedBrowse }">
    
    <!-- Enhanced Design -->
    <div v-if="useEnhancedBrowse" class="enhanced-browse-container">
      
      <!-- Hero Search Section -->
      <div class="search-hero-section animate-fade-up">
        <div class="hero-content">
          <h1 class="hero-title">{{ $t('browseOffers.title') }}</h1>
          <p class="hero-subtitle">{{ $t('browseOffers.subtitle') }}</p>
        </div>
        
        <!-- Enhanced Search Bar -->
        <div class="search-section">
          <div class="search-bar-container">
            <n-input
              v-model:value="searchQuery"
              :placeholder="$t('browseOffers.heroPlaceholder')"
              size="large"
              round
              clearable
              class="search-input"
              @input="handleSearch"
            >
              <template #prefix>
                <n-icon size="20" color="#666">
                  <SearchOutlined />
                </n-icon>
              </template>
            </n-input>
          </div>
          
          <!-- Filter Chips -->
          <div class="filter-chips" v-if="activeFilters.length > 0">
            <n-tag
              v-for="filter in activeFilters"
              :key="filter.key"
              :type="filter.type"
              round
              closable
              size="small"
              @close="removeFilter(filter.key)"
              class="filter-chip"
            >
              {{ filter.label }}
            </n-tag>
          </div>
          
          <!-- Quick Filters -->
          <div class="quick-filters">
            <n-button-group size="small">
              <n-button 
                :type="selectedOfferType === 'ALL' ? 'primary' : 'default'"
                @click="setOfferTypeFilter('ALL')"
              >
                {{ $t('browseOffers.filters.all') }}
              </n-button>
              <n-button 
                :type="selectedOfferType === 'BUY' ? 'primary' : 'default'"
                @click="setOfferTypeFilter('BUY')"
              >
                {{ $t('browseOffers.filters.buying') }}
              </n-button>
              <n-button 
                :type="selectedOfferType === 'SELL' ? 'primary' : 'default'"
                @click="setOfferTypeFilter('SELL')"
              >
                {{ $t('browseOffers.filters.selling') }}
              </n-button>
            </n-button-group>
            
            <n-button 
              quaternary
              @click="showAdvancedFilters = true"
              class="advanced-filter-btn"
            >
              <template #icon>
                <n-icon><FilterOutlined /></n-icon>
              </template>
              {{ $t('browseOffers.advancedFilters') }}
            </n-button>
          </div>
        </div>
      </div>

      <!-- Loading Skeleton -->
      <div v-if="isLoading" class="offers-skeleton animate-fade-up">
        <div v-for="i in 6" :key="i" class="skeleton-card">
          <div class="skeleton-header"></div>
          <div class="skeleton-content">
            <div class="skeleton-line"></div>
            <div class="skeleton-line short"></div>
            <div class="skeleton-line medium"></div>
          </div>
          <div class="skeleton-footer"></div>
        </div>
      </div>

      <!-- Enhanced Offers Grid -->
      <div v-else-if="filteredOffers.length > 0" class="offers-grid animate-fade-up">
        <div 
          v-for="(offer, index) in filteredOffers" 
          :key="offer.id"
          class="offer-card-container"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <EnhancedOfferCard 
            :offer="offer" 
            @click="handleOfferClick(offer)"
            @show-interest="handleShowInterest"
          />
        </div>
      </div>

      <!-- Enhanced Empty State -->
      <div v-else class="enhanced-empty-state animate-fade-up">
        <div class="empty-icon">🔍</div>
        <h3 class="empty-title">{{ $t('browseOffers.emptyState.title') }}</h3>
        <p class="empty-description">{{ $t('browseOffers.emptyState.description') }}</p>
        <n-button 
          type="primary" 
          round 
          @click="clearAllFilters"
          class="clear-filters-btn"
        >
          {{ $t('browseOffers.emptyState.clearFilters') }}
        </n-button>
      </div>

      <!-- Stats Summary -->
      <div v-if="!isLoading && filteredOffers.length > 0" class="stats-summary">
        <div class="stats-item">
          <span class="stats-number">{{ filteredOffers.length }}</span>
          <span class="stats-label">{{ $t('browseOffers.stats.totalOffers') }}</span>
        </div>
        <div class="stats-item">
          <span class="stats-number">{{ uniqueUsers }}</span>
          <span class="stats-label">{{ $t('offers.users') }}</span>
        </div>
      </div>
    </div>

    <!-- Original Design Fallback -->
    <div v-else class="original-browse-container">
      <n-page-header :title="t('navigation.browse')" />
      <n-space vertical size="large" style="padding-top: 16px;">
        <n-spin :show="isLoading">
          <n-alert v-if="error" type="error">{{ error }}</n-alert>
          <n-empty v-else-if="activeBrowsableOffers.length === 0 && !isLoading" :description="t('offers.noOffersFound')" />
          <n-grid v-else :x-gap="12" :y-gap="16" :responsive="'screen'" cols="1 s:2 m:3 l:4 xl:4">
            <n-gi v-for="offer in activeBrowsableOffers" :key="offer.id">
              <OfferSummaryCard :offer="offer" @click="handleOfferClick(offer)" @showInterest="handleShowInterest" />
            </n-gi>
          </n-grid>
        </n-spin>
      </n-space>
    </div>

    <!-- Advanced Filters Modal -->
    <n-modal v-model:show="showAdvancedFilters" preset="card" style="width: 90%; max-width: 500px;">
      <template #header>
        <span>{{ $t('browseOffers.advancedFilters') }}</span>
      </template>
      
      <div class="advanced-filters-content">
        <!-- Amount Range -->
        <div class="filter-section">
          <n-text strong>{{ $t('browseOffers.filters.minAmount') }}</n-text>
          <div class="amount-inputs">
            <n-input-number
              v-model:value="filters.minAmount"
              :min="0"
              :placeholder="$t('browseOffers.filters.minAmount')"
              style="flex: 1"
            />
            <span style="margin: 0 8px;">-</span>
            <n-input-number
              v-model:value="filters.maxAmount"
              :min="0"
              :placeholder="$t('browseOffers.filters.maxAmount')"
              style="flex: 1"
            />
          </div>
        </div>

        <!-- Reputation Level -->
        <div class="filter-section">
          <n-text strong>{{ $t('browseOffers.filters.reputation') }}</n-text>
          <n-slider
            v-model:value="filters.minReputation"
            :min="1"
            :max="5"
            :step="1"
            :marks="reputationMarks"
          />
        </div>

        <!-- Rate Range -->
        <div class="filter-section">
          <n-text strong>{{ $t('browseOffers.filters.minRate') }}</n-text>
          <div class="amount-inputs">
            <n-input-number
              v-model:value="filters.minRate"
              :min="0"
              :placeholder="$t('browseOffers.filters.minRate')"
              style="flex: 1"
            />
            <span style="margin: 0 8px;">-</span>
            <n-input-number
              v-model:value="filters.maxRate"
              :min="0"
              :placeholder="$t('browseOffers.filters.maxRate')"
              style="flex: 1"
            />
          </div>
        </div>
      </div>

      <template #action>
        <n-space>
          <n-button @click="clearAdvancedFilters">{{ $t('browseOffers.filters.clearFilters') }}</n-button>
          <n-button type="primary" @click="applyAdvancedFilters">{{ $t('browseOffers.filters.applyFilters') }}</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- Modal for Detailed Offer View -->
    <OfferDetailsModal v-model:show="showOfferDetailModal" />
    
    <!-- Development Toggle Button -->
    <div v-if="isDevelopment" class="feature-toggle-btn">
      <n-button 
        circle 
        type="primary" 
        size="large"
        @click="uiStore.toggleEnhancedBrowse()"
        class="toggle-button"
        :title="useEnhancedBrowse ? 'Switch to Legacy View' : 'Switch to Enhanced View'"
      >
        <template #icon>
          <n-icon>{{ useEnhancedBrowse ? '📱' : '🚀' }}</n-icon>
        </template>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { 
  NPageHeader, 
  NSpin, 
  NAlert, 
  NSpace, 
  NEmpty, 
  NGrid, 
  NGi,
  NInput,
  NIcon,
  NTag,
  NButton,
  NButtonGroup,
  NModal,
  NText,
  NInputNumber,
  NSlider
} from 'naive-ui';
import { SearchOutlined, FilterOutlined } from '@vicons/antd';
import OfferSummaryCard from '@/components/OfferSummaryCard.vue';
import EnhancedOfferCard from '@/components/EnhancedOfferCard.vue';
import OfferDetailsModal from '@/components/OfferDetailsModal.vue';
import { useOfferStore } from '@/stores/offerStore';
import { useInterestStore } from '@/stores/interestStore';
import { useUiPreferencesStore } from '@/stores/uiPreferences';
import { useTranslation } from '@/composables/useTranslation';
import { storeToRefs } from 'pinia';
import type { BrowseOffer } from '@/types/offer';
import { offerService } from '@/services/offerService';

// Stores and composables
const { t } = useTranslation();
const offerStore = useOfferStore();
const interestStore = useInterestStore();
const uiStore = useUiPreferencesStore();
const { activeBrowsableOffers, isLoading, error } = storeToRefs(offerStore);
const { useEnhancedBrowse } = storeToRefs(uiStore);

// Modal and UI state
const showOfferDetailModal = ref(false);
const showAdvancedFilters = ref(false);

// Search and filtering
const searchQuery = ref('');
const selectedOfferType = ref<'ALL' | 'BUY' | 'SELL'>('ALL');
const activeFilters = ref<Array<{key: string, label: string, type: 'info' | 'success' | 'warning'}>>([]);

// Advanced filters
const filters = ref({
  minAmount: null as number | null,
  maxAmount: null as number | null,
  minReputation: 1,
  maxRate: null as number | null,
  minRate: null as number | null,
});

const reputationMarks = {
  1: 'Newcomer',
  2: 'Verified', 
  3: 'Reliable',
  4: 'Trusted',
  5: 'Elite'
};

// Computed properties
const filteredOffers = computed(() => {
  let offers = [...activeBrowsableOffers.value];
  
  // Text search
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase();
    offers = offers.filter(offer => 
      offer.offerCreatorUsername?.toLowerCase().includes(query) ||
      offer.amount.toString().includes(query) ||
      offer.baseRate.toString().includes(query)
    );
  }
  
  // Offer type filter
  if (selectedOfferType.value !== 'ALL') {
    offers = offers.filter(offer => offer.type === selectedOfferType.value);
  }
  
  // Advanced filters
  if (filters.value.minAmount) {
    offers = offers.filter(offer => 
      offer.amount != null && offer.amount >= filters.value.minAmount!
    );
  }
  if (filters.value.maxAmount) {
    offers = offers.filter(offer => 
      offer.amount != null && offer.amount <= filters.value.maxAmount!
    );
  }
  if (filters.value.minReputation > 1) {
    offers = offers.filter(offer => 
      offer.offerCreatorReputationLevel != null && 
      offer.offerCreatorReputationLevel >= filters.value.minReputation
    );
  }
  if (filters.value.minRate) {
    offers = offers.filter(offer => 
      offer.baseRate != null && offer.baseRate >= filters.value.minRate!
    );
  }
  if (filters.value.maxRate) {
    offers = offers.filter(offer => 
      offer.baseRate != null && offer.baseRate <= filters.value.maxRate!
    );
  }
  
  return offers;
});

const uniqueUsers = computed(() => {
  const userIds = new Set(filteredOffers.value.map(offer => offer.offerCreatorId));
  return userIds.size;
});

// Development helper
const isDevelopment = computed(() => {
  return import.meta.env.DEV || window.location.hostname === 'localhost';
});

// Methods
async function handleOfferClick(offer: BrowseOffer) {
  await offerStore.fetchAndDisplayOfferForModal(offer.id);
  showOfferDetailModal.value = true;
}

async function handleShowInterest(offerId: string) {
  await offerService.showInterest(offerId);
}

function handleSearch() {
  // Real-time search is handled by computed property
}

function setOfferTypeFilter(type: 'ALL' | 'BUY' | 'SELL') {
  selectedOfferType.value = type;
  updateActiveFilters();
}

function removeFilter(key: string) {
  if (key === 'offerType') {
    selectedOfferType.value = 'ALL';
  } else if (key.startsWith('amount')) {
    filters.value.minAmount = null;
    filters.value.maxAmount = null;
  } else if (key === 'reputation') {
    filters.value.minReputation = 1;
  } else if (key.startsWith('rate')) {
    filters.value.minRate = null;
    filters.value.maxRate = null;
  }
  updateActiveFilters();
}

function updateActiveFilters() {
  const newFilters = [];
  
  if (selectedOfferType.value !== 'ALL') {
    newFilters.push({
      key: 'offerType',
      label: selectedOfferType.value === 'BUY' ? t('browseOffers.filters.buying') : t('browseOffers.filters.selling'),
      type: 'info' as const
    });
  }
  
  if (filters.value.minAmount || filters.value.maxAmount) {
    const min = filters.value.minAmount || 0;
    const max = filters.value.maxAmount || '∞';
    newFilters.push({
      key: 'amount',
      label: `${t('browseOffers.filters.minAmount')}: $${min} - $${max}`,
      type: 'success' as const
    });
  }
  
  if (filters.value.minReputation > 1) {
    newFilters.push({
      key: 'reputation',
      label: `${t('browseOffers.filters.reputation')}: ${t('offers.level')} ${filters.value.minReputation}`,
      type: 'warning' as const
    });
  }
  
  if (filters.value.minRate || filters.value.maxRate) {
    const min = filters.value.minRate || 0;
    const max = filters.value.maxRate || '∞';
    newFilters.push({
      key: 'rate',
      label: `${t('browseOffers.filters.minRate')}: ${min} - ${max}`,
      type: 'info' as const
    });
  }
  
  activeFilters.value = newFilters;
}

function clearAllFilters() {
  searchQuery.value = '';
  selectedOfferType.value = 'ALL';
  clearAdvancedFilters();
}

function clearAdvancedFilters() {
  filters.value = {
    minAmount: null,
    maxAmount: null,
    minReputation: 1,
    maxRate: null,
    minRate: null,
  };
  updateActiveFilters();
}

function applyAdvancedFilters() {
  updateActiveFilters();
  showAdvancedFilters.value = false;
}

// Lifecycle
onMounted(async () => {
  offerStore.initializeSocketListeners();
  interestStore.initializeSocketListeners();
  await offerStore.loadOffers();
});

onUnmounted(() => {
  offerStore.cleanup();
  offerStore.clearViewedOfferDetails();
});

// Watch for filter changes
watch([selectedOfferType, filters], () => {
  updateActiveFilters();
}, { deep: true });
</script>

<style scoped>
/* Mobile-first Enhanced Browse Offers styles */
.browse-offers-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 1rem;
  margin: 0;
  box-sizing: border-box;
}

/* Enhanced container */
.enhanced-browse-container {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  padding: 0;
  box-sizing: border-box;
}

/* Hero Search Section */
.search-hero-section {
  text-align: center;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.5);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease forwards;
  box-sizing: border-box;
}

.hero-content {
  margin-bottom: 1.5rem;
  padding: 0.5rem 0;
}

.hero-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  line-height: 1.3;
}

.hero-subtitle {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

/* Search Section */
.search-section {
  max-width: 100%;
  margin: 0 auto;
}

.search-bar-container {
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.search-input {
  width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 25px !important;
}

/* Filter Chips */
.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
  justify-content: center;
}

.filter-chip {
  animation: fadeInScale 0.3s ease;
}

/* Quick Filters */
.quick-filters {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: center;
  padding: 0 0.5rem;
}

.advanced-filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

/* Loading Skeleton */
.offers-skeleton {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin: 0;
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.2s forwards;
}

.skeleton-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.skeleton-header {
  height: 20px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-line.short {
  width: 60%;
}

.skeleton-line.medium {
  width: 80%;
}

.skeleton-footer {
  height: 32px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 16px;
  width: 120px;
}

/* Offers Grid */
.offers-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin: 0;
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.3s forwards;
}

.offer-card-container {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

/* Enhanced Empty State */
.enhanced-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.4s forwards;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
}

.empty-description {
  color: #666;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

.clear-filters-btn {
  margin-top: 1rem;
}

/* Stats Summary */
.stats-summary {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 1.5rem 0 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2080f0;
}

.stats-label {
  font-size: 0.875rem;
  color: #666;
}

/* Advanced Filters */
.advanced-filters-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.amount-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Original design container */
.original-browse-container {
  padding: 1rem;
}

.original-browse-container .n-page-header {
  margin-bottom: 16px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Tablet enhancements */
@media (min-width: 768px) {
  .enhanced-browse-container {
    padding: 0;
  }
  
  .search-hero-section {
    padding: 2rem;
    margin-bottom: 2rem;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .search-section {
    max-width: 600px;
  }
  
  .offers-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin: 0;
  }
  
  .offers-skeleton {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin: 0;
  }
  
  .quick-filters {
    flex-direction: row;
    justify-content: space-between;
    padding: 0 1rem;
  }
  
  .stats-summary {
    margin: 2rem 0 1rem;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  .browse-offers-view {
    padding: 2rem;
  }
  
  .enhanced-browse-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
  }
  
  .search-hero-section {
    padding: 3rem 2rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .offers-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 0;
  }
  
  .offers-skeleton {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 0;
  }
  
  .stats-summary {
    margin: 2rem 0 1rem;
  }
}

@media (min-width: 1440px) {
  .offers-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .offers-skeleton {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .search-hero-section,
  .offers-skeleton,
  .offers-grid,
  .offer-card-container,
  .enhanced-empty-state,
  .filter-chip {
    animation: none !important;
  }
}

/* Development Toggle Button */
.feature-toggle-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.toggle-button {
  box-shadow: 0 4px 12px rgba(32, 128, 240, 0.3);
  backdrop-filter: blur(10px);
}

/* Dark theme support */
[data-theme="dark"] .browse-offers-view {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

[data-theme="dark"] .search-hero-section {
  background: rgba(30, 32, 48, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .hero-title {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .hero-subtitle {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .skeleton-card {
  background: rgba(30, 32, 48, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .stats-summary {
  background: rgba(30, 32, 48, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .stats-number {
  color: #63a4ff;
}

[data-theme="dark"] .stats-label {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .empty-title {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .empty-description {
  color: rgba(255, 255, 255, 0.7);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .search-hero-section {
    border: 2px solid #333;
  }
  
  .hero-title {
    color: #000;
    font-weight: 800;
  }
  
  .skeleton-card {
    border: 1px solid #333;
  }
  
  .stats-summary {
    border: 1px solid #333;
  }
}

@media (prefers-contrast: high) {
  [data-theme="dark"] .search-hero-section {
    border: 2px solid #fff;
  }
  
  [data-theme="dark"] .hero-title {
    color: #fff;
    font-weight: 800;
  }
  
  [data-theme="dark"] .skeleton-card {
    border: 1px solid #fff;
  }
  
  [data-theme="dark"] .stats-summary {
    border: 1px solid #fff;
  }
}
</style>
