import { GoogleGenAI, Type } from "@google/genai";
import {
  AiGeneratedReport,
  AudioTranscriptionRequest,
  VoiceToReportResponse,
} from "../types/schemas/aiSchemas";

// Simplified voice analysis failure reasons - only 3 categories as requested
type VoiceAnalysisFailureReason = "POOR_QUALITY_OR_SHORT" | "TOO_NOISY" | "IRRELEVANT_CONTENT";

interface VoiceAnalysisFailure {
  reason: VoiceAnalysisFailureReason;
  userMessageKey: string;
  suggestionKeys: string[];
  transcription?: string;
  confidence: number;
}

/**
 * AI Service for processing bug reports using Google Gemini 2.5 Flash Preview (05-20)
 * Handles both transcription analysis and direct audio processing using the latest Google Gen AI SDK
 */
export class AiService {
  private genAI: GoogleGenAI | null;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || "";

    if (!apiKey) {
      console.warn(
        "[AiService] Gemini API key not found. AI features will be disabled."
      );
      this.genAI = null;
    } else {
      this.genAI = new GoogleGenAI({ apiKey });
    }
  }

  /**
   * Check if AI service is available
   */
  isAvailable(): boolean {
    return !!this.genAI;
  }

  /**
   * Process audio directly with Gemini to generate bug report (eliminates speech-to-text step)
   */
  async processAudioToReport(
    audioBuffer: Buffer,
    mimeType: string,
    userContext?: {
      currentPage?: string;
      userAgent?: string;
      viewport?: { width?: number; height?: number };
    },
    predefinedTags?: { [reportType: string]: string[] },
    abortSignal?: AbortSignal
  ): Promise<VoiceToReportResponse> {
    const startTime = Date.now();
    console.log(`[AiService] 🎵 Starting processAudioToReport at ${new Date().toISOString()}`);
    
    // Check for cancellation at the start
    if (abortSignal?.aborted) {
      console.log(`[AiService] ❌ Request was cancelled before processing started`);
      return {
        success: false,
        error: 'Request was cancelled',
        processingTime: Date.now() - startTime
      };
    }
    
    try {
      if (!this.isAvailable()) {
        console.warn("[AiService] Service unavailable at processAudioToReport entry");
        return {
          success: false,
          error: "AI service is not available. Please check configuration.",
          processingTime: Date.now() - startTime,
        };
      }

      console.log(`[AiService] ✅ Service available, processing audio...`);

      // Convert buffer to base64
      const conversionStart = Date.now();
      const audioData = audioBuffer.toString("base64");
      const conversionDuration = Date.now() - conversionStart;
      
      // Debug: log audio buffer info (not raw data)
      const crypto = require("crypto");
      const audioHash = crypto.createHash("sha256").update(audioBuffer).digest("hex");
      console.log(`[AiService] Received audio: size=${audioBuffer.length} bytes, mimeType=${mimeType}, sha256=${audioHash}`);
      console.log(`[AiService] Base64 conversion completed in ${conversionDuration}ms, output size=${audioData.length} chars`);

      // Validate audio data
      const validationStart = Date.now();
      if (!this.isValidAudioData(audioData)) {
        console.warn("[AiService] Invalid audio data format");
        return {
          success: false,
          error: "Invalid audio data format.",
          processingTime: Date.now() - startTime,
        };
      }
      const validationDuration = Date.now() - validationStart;
      console.log(`[AiService] ✅ Audio validation completed in ${validationDuration}ms`);

      const promptStart = Date.now();
      const prompt = this.buildDirectAudioAnalysisPrompt(
        userContext,
        predefinedTags
      );
      const promptDuration = Date.now() - promptStart;
      console.log(`[AiService] ✅ Prompt generation completed in ${promptDuration}ms, prompt length=${prompt.length} chars`);

      // Use Gemini's native audio processing with structured output
      const config = this.getOptimizedConfig(audioBuffer.length, mimeType);

      const contents = [
        {
          role: "user",
          parts: [
            {
              inlineData: {
                data: audioData,
                mimeType: mimeType,
              },
            },
            {
              text: prompt,
            },
          ],
        },
      ];

      console.log("[AiService] Sending audio to Gemini API...");
      console.log(`[AiService] Gemini request details: model=gemini-2.5-flash-preview-05-20, audio_size=${audioBuffer.length}, mime_type=${mimeType}`);
      
      // Check if audio might be too large or complex for processing
      if (audioBuffer.length > 5000000) { // 5MB
        console.warn(`[AiService] ⚠️ Large audio file detected (${audioBuffer.length} bytes) - this may cause timeouts`);
      }
      
      const geminiStart = Date.now();
      let result;
      
      // Set up progress logging for long requests
      const progressInterval = setInterval(() => {
        const elapsed = Date.now() - geminiStart;
        if (elapsed > 10000) { // Log every 10 seconds after first 10 seconds
          console.log(`[AiService] ⏳ Gemini API call still in progress... ${elapsed}ms elapsed`);
          if (elapsed > 45000) {
            console.warn(`[AiService] ⚠️ Gemini API call taking unusually long (${elapsed}ms) - potential timeout risk`);
          }
        }
        
        // Check for cancellation during progress logging
        if (abortSignal?.aborted) {
          console.log(`[AiService] ❌ Request cancelled during Gemini API call at ${elapsed}ms`);
          clearInterval(progressInterval);
        }
      }, 10000);
      
      try {
        // Race between API call, timeout, and cancellation
        const apiCallPromise = this.genAI!.models.generateContent({
          model: "gemini-2.5-flash-preview-05-20",
          config,
          contents,
        });
        
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error(`TIMEOUT: Gemini API call exceeded 60 seconds`));
          }, 60000); // 60 second hard timeout
        });
        
        const cancellationPromise = new Promise((_, reject) => {
          if (abortSignal) {
            abortSignal.addEventListener('abort', () => {
              reject(new Error('CANCELLED: Request was cancelled by client'));
            });
          }
        });
        
        // Race between API call, timeout, and cancellation
        result = await Promise.race([
          apiCallPromise,
          timeoutPromise,
          ...(abortSignal ? [cancellationPromise] : [])
        ]);
        
        clearInterval(progressInterval);
        const geminiDuration = Date.now() - geminiStart;
        console.log(`[AiService] ✅ Gemini API call completed successfully in ${geminiDuration}ms`);
        
        // Safety check for result
        if (!result) {
          throw new Error('No result received from Gemini API');
        }
        
        const content = (result as any).text;
        if (content && content.length > 1000) {
          console.log(`[AiService] Gemini response (truncated): ${content.slice(0, 1000)}... [${content.length} chars total]`);
        } else {
          console.log(`[AiService] Gemini response: ${content}`);
        }

        // Process the successful result
        if (!content) {
          console.warn("[AiService] No response generated from AI model");
          return {
            success: false,
            error: "No response generated from AI model",
            processingTime: Date.now() - startTime,
          };
        }

        console.log(`[AiService] ✅ Response received, starting parsing...`);
        const parseStart = Date.now();
        let parsedResponse;
        try {
          parsedResponse = this.parseAudioGeminiResponse(
            content,
            predefinedTags
          );
        } catch (parseError: any) {
          console.error(`[AiService] JSON parsing error:`, parseError);
          return {
            success: false,
            error: `Error processing AI response format: ${parseError.message}`,
            transcription: content || '',
            processingTime: Date.now() - startTime,
          };
        }
        const parseDuration = Date.now() - parseStart;
        console.log(`[AiService] ✅ Response parsing completed in ${parseDuration}ms, status=${parsedResponse.analysisStatus}`);

        // Handle analysis failure cases
        if (parsedResponse.analysisStatus === "FAILED") {
          let validReason: VoiceAnalysisFailureReason = "POOR_QUALITY_OR_SHORT";
          
          if (
            parsedResponse.failureReason &&
            [
              "POOR_QUALITY_OR_SHORT",
              "TOO_NOISY",
              "IRRELEVANT_CONTENT",
            ].includes(parsedResponse.failureReason)
          ) {
            validReason = parsedResponse.failureReason as VoiceAnalysisFailureReason;
          }
          // Return properly formatted failure response
          return {
            success: false,
            error: `Audio analysis failed: ${validReason.toLowerCase()}`,
            transcription: parsedResponse.transcription,
            analysisFailure: this.handleVoiceAnalysisFailure(validReason, parsedResponse.transcription),
            processingTime: Date.now() - startTime,
          };
        }

        // Success case - return the result
        console.log(`[AiService] ✅ Audio processing completed successfully in ${Date.now() - startTime}ms`);
        return {
          success: true,
          generatedReport: parsedResponse.report,
          transcription: parsedResponse.transcription,
          processingTime: Date.now() - startTime,
        };
        
      } catch (geminiError: any) {
        clearInterval(progressInterval);
        const geminiDuration = Date.now() - geminiStart;
        
        // Handle specific cancellation and timeout scenarios
        if (geminiError.message?.includes('CANCELLED')) {
          console.log(`[AiService] ❌ Gemini API call cancelled by client after ${geminiDuration}ms`);
          return {
            success: false,
            error: 'Request was cancelled by client',
            processingTime: Date.now() - startTime
          };
        } else if (geminiError.message?.includes('TIMEOUT')) {
          console.warn(`[AiService] ⏰ Gemini API call timed out after ${geminiDuration}ms`);
          console.log(`[AiService] 🔄 Attempting fallback processing due to timeout...`);
          
          // Attempt fallback processing
          try {
            const fallbackResult = await this.processAudioToReportFallback(
              audioBuffer,
              mimeType,
              userContext,
              predefinedTags,
              abortSignal
            );
            return fallbackResult;
          } catch (fallbackError: any) {
            console.error(`[AiService] ❌ Fallback processing also failed:`, fallbackError.message);
            return {
              success: false,
              error: `Main processing timed out after ${geminiDuration}ms and fallback also failed: ${fallbackError.message}`,
              processingTime: Date.now() - startTime
            };
          }
        }
        
        console.error(`[AiService] Gemini API call FAILED after ${geminiDuration}ms`);
        console.error(`[AiService] Gemini error type: ${geminiError.name || 'Unknown'}`);
        console.error(`[AiService] Gemini error message: ${geminiError.message || 'No message'}`);
        console.error(`[AiService] Gemini error code: ${geminiError.code || 'No code'}`);
        console.error(`[AiService] Gemini error status: ${geminiError.status || 'No status'}`);
        console.error(`[AiService] Gemini error details:`, {
          name: geminiError.name,
          message: geminiError.message,
          code: geminiError.code,
          status: geminiError.status,
          stack: geminiError.stack?.slice(0, 500) // First 500 chars of stack trace
        });
        
        // Re-throw the error to be handled by the outer catch block
        throw geminiError;
      }
      
    } catch (error: any) {
      const totalDuration = Date.now() - startTime;
      console.error(`[AiService] CRITICAL ERROR processing audio directly after ${totalDuration}ms`);
      console.error(`[AiService] Error type: ${error.name || 'Unknown'}`);
      console.error(`[AiService] Error message: ${error.message || 'No message'}`);
      console.error(`[AiService] Error code: ${error.code || 'No code'}`);
      
      // Check for specific error types
      let errorCategory = 'UNKNOWN';
      let userFriendlyMessage = error.message || 'Unknown error occurred';
      
      if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        errorCategory = 'TIMEOUT';
        console.error(`[AiService] TIMEOUT ERROR - Request exceeded time limit. Duration: ${totalDuration}ms`);
        
        // Try fallback processing for timeout errors
        if (totalDuration > 30000) { // Only try fallback if we've been trying for more than 30 seconds
          console.log(`[AiService] 🔄 Attempting fallback processing due to timeout...`);
          try {
            const fallbackResult = await this.processAudioToReportFallback(
              audioBuffer, 
              mimeType, 
              userContext,
              predefinedTags,
              abortSignal
            );
            
            if (fallbackResult.success) {
              console.log(`[AiService] ✅ Fallback processing succeeded after main timeout`);
              return fallbackResult;
            } else {
              console.warn(`[AiService] ❌ Fallback processing also failed: ${fallbackResult.error}`);
            }
          } catch (fallbackError: any) {
            console.error(`[AiService] ❌ Fallback processing threw error:`, fallbackError);
          }
        }
        
        userFriendlyMessage = `Request timed out after ${totalDuration}ms. The AI service may be experiencing high load. Please try again with a shorter audio clip.`;
      } else if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND') {
        errorCategory = 'NETWORK';
        userFriendlyMessage = 'Network connection error while contacting AI service.';
        console.error(`[AiService] NETWORK ERROR - Connection issue with Gemini API`);
      } else if (error.status && error.status >= 400 && error.status < 500) {
        errorCategory = 'CLIENT_ERROR';
        userFriendlyMessage = `Client error (${error.status}): ${error.message}`;
        console.error(`[AiService] CLIENT ERROR - HTTP ${error.status}: ${error.message}`);
      } else if (error.status && error.status >= 500) {
        errorCategory = 'SERVER_ERROR';
        userFriendlyMessage = `Server error (${error.status}): AI service is temporarily unavailable.`;
        console.error(`[AiService] SERVER ERROR - HTTP ${error.status}: Gemini API server error`);
      } else if (error.name === 'TypeError' || error.name === 'SyntaxError') {
        errorCategory = 'PARSING_ERROR';
        userFriendlyMessage = 'Error processing AI response format.';
        console.error(`[AiService] PARSING ERROR - ${error.name}: ${error.message}`);
      }
      
      console.error(`[AiService] Error category: ${errorCategory}`);
      console.error(`[AiService] Full error details:`, {
        name: error.name,
        message: error.message,
        code: error.code,
        status: error.status,
        stack: error.stack?.slice(0, 1000), // First 1000 chars of stack trace
        category: errorCategory,
        duration: totalDuration
      });
      
      return {
        success: false,
        error: `Direct audio processing failed: ${userFriendlyMessage}`,
        processingTime: totalDuration,
      };
    }
  }

  /**
   * Fallback audio processing using the same prompt and output schema as main processing
   * Uses the faster gemini-2.0-flash-lite model when the main processing times out
   * Ensures consistent response format and structure
   */
  async processAudioToReportFallback(
    audioBuffer: Buffer,
    mimeType: string,
    userContext?: {
      currentPage?: string;
      userAgent?: string;
      viewport?: { width?: number; height?: number };
    },
    predefinedTags?: { [reportType: string]: string[] },
    abortSignal?: AbortSignal
  ): Promise<VoiceToReportResponse> {
    const startTime = Date.now();
    console.log(`[AiService] 🔄 Starting FALLBACK audio processing with simplified approach`);
    
    // Check for cancellation at the start of fallback
    if (abortSignal?.aborted) {
      console.log(`[AiService] ❌ Fallback request was cancelled before processing started`);
      return {
        success: false,
        error: 'Fallback request was cancelled',
        processingTime: Date.now() - startTime,
        isFallback: true
      };
    }
    
    try {
      if (!this.isAvailable()) {
        console.warn("[AiService] Service unavailable for fallback processing");
        return {
          success: false,
          error: "AI service is not available for fallback processing.",
          processingTime: Date.now() - startTime,
        };
      }

      // Convert buffer to base64
      const audioData = audioBuffer.toString("base64");
      console.log(`[AiService] Fallback: Audio size=${audioBuffer.length} bytes, base64 size=${audioData.length} chars`);

      // Use the same comprehensive prompt as main processing for consistency
      const prompt = this.buildDirectAudioAnalysisPrompt(
        userContext,
        predefinedTags
      );
      console.log(`[AiService] Fallback: Using full prompt, length=${prompt.length} chars`);

      // Use the same configuration as main processing but optimized for lite model
      const config = this.getOptimizedConfig(audioBuffer.length, mimeType);
      // Remove thinking_config as it's not supported by gemini-2.0-flash-lite
      const fallbackConfig = { ...config };
      if ('thinkingConfig' in fallbackConfig) {
        delete (fallbackConfig as any).thinkingConfig;
      }
      // Override temperature for faster fallback processing
      fallbackConfig.temperature = 0.3;

      const contents = [
        {
          role: "user",
          parts: [
            {
              inlineData: {
                data: audioData,
                mimeType: mimeType,
              },
            },
            {
              text: prompt,
            },
          ],
        },
      ];

      console.log("[AiService] Fallback: Sending request to Gemini API with full prompt and schema...");
      const geminiStart = Date.now();
      
      // Check for cancellation before making API call
      if (abortSignal?.aborted) {
        console.log(`[AiService] ❌ Fallback request cancelled before Gemini API call`);
        return {
          success: false,
          error: 'Fallback request was cancelled before API call',
          processingTime: Date.now() - startTime,
          isFallback: true
        };
      }
      
      let content: string; // Declare content at function scope
      
      try {
        // Race between API call, timeout, and cancellation for fallback
        const apiCallPromise = this.genAI!.models.generateContent({
          model: "gemini-2.0-flash-lite", // Use the lite version of 2.0 for faster fallback processing
          config: fallbackConfig,
          contents,
        });
        
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error(`FALLBACK_TIMEOUT: Fallback Gemini API call exceeded 30 seconds`));
          }, 30000); // 30 second timeout for fallback
        });
        
        const cancellationPromise = new Promise((_, reject) => {
          if (abortSignal) {
            abortSignal.addEventListener('abort', () => {
              reject(new Error('FALLBACK_CANCELLED: Fallback request was cancelled by client'));
            });
          }
        });
        
        // Race between API call, timeout, and cancellation
        const result = await Promise.race([
          apiCallPromise,
          timeoutPromise,
          ...(abortSignal ? [cancellationPromise] : [])
        ]);
        
        const geminiDuration = Date.now() - geminiStart;
        console.log(`[AiService] ✅ Fallback Gemini API call completed in ${geminiDuration}ms`);
        
        // Safety check for result
        if (!result) {
          throw new Error('No result received from fallback Gemini API');
        }
        
        content = (result as any).text;
        console.log(`[AiService] Fallback response: ${content}`);

        if (!content) {
          console.warn("[AiService] No fallback response generated from AI model");
          return {
            success: false,
            error: "No response generated from fallback AI processing",
            processingTime: Date.now() - startTime,
            isFallback: true
          };
        }

        // Parse the simplified response
        let parsedResponse;
        
      } catch (fallbackError: any) {
        const geminiDuration = Date.now() - geminiStart;
        
        // Handle specific cancellation and timeout scenarios for fallback
        if (fallbackError.message?.includes('FALLBACK_CANCELLED')) {
          console.log(`[AiService] ❌ Fallback Gemini API call cancelled by client after ${geminiDuration}ms`);
          return {
            success: false,
            error: 'Fallback request was cancelled by client',
            processingTime: Date.now() - startTime,
            isFallback: true
          };
        } else if (fallbackError.message?.includes('FALLBACK_TIMEOUT')) {
          console.warn(`[AiService] ⏰ Fallback Gemini API call timed out after ${geminiDuration}ms`);
          return {
            success: false,
            error: `Fallback processing timed out after ${geminiDuration}ms`,
            processingTime: Date.now() - startTime,
            isFallback: true
          };
        }
        
        console.error(`[AiService] Fallback Gemini API call FAILED after ${geminiDuration}ms:`, fallbackError.message);
        return {
          success: false,
          error: `Fallback processing failed: ${fallbackError.message}`,
          processingTime: Date.now() - startTime,
          isFallback: true
        };
      }
      
      // Parse the response using the same format as main processing
      let parsedResponse;
      try {
        // Use the same parsing method as main processing
        parsedResponse = this.parseAudioGeminiResponse(
          content,
          predefinedTags
        );
        console.log(`[AiService] Fallback: Successfully parsed JSON response`);
      } catch (parseError) {
        console.error("[AiService] Failed to parse fallback response:", parseError);
        console.error("[AiService] Raw fallback content:", content.slice(0, 500));
        return {
          success: false,
          error: "Failed to parse fallback AI response - invalid JSON format",
          processingTime: Date.now() - startTime,
          isFallback: true
        };
      }

      // Validate response structure - same as main processing
      if (!parsedResponse || typeof parsedResponse !== 'object') {
        console.error("[AiService] Fallback response is not a valid object");
        return {
          success: false,
          error: "Invalid fallback response format from AI",
          processingTime: Date.now() - startTime,
          isFallback: true
        };
      }

      // Check for analysis failure - same as main processing
      if (parsedResponse.analysisStatus === "FAILED") {
        console.warn(
          `[AiService] Fallback audio analysis failed - Reason: ${parsedResponse.failureReason}`
        );
        // Map failure reason to valid enum value
        let validReason:
          | "POOR_QUALITY_OR_SHORT"
          | "TOO_NOISY"
          | "IRRELEVANT_CONTENT" = "POOR_QUALITY_OR_SHORT";
        if (
          parsedResponse.failureReason &&
          [
            "POOR_QUALITY_OR_SHORT",
            "TOO_NOISY",
            "IRRELEVANT_CONTENT",
          ].includes(parsedResponse.failureReason)
        ) {
          validReason = parsedResponse.failureReason as
            | "POOR_QUALITY_OR_SHORT"
            | "TOO_NOISY"
            | "IRRELEVANT_CONTENT";
        }
        // Return properly formatted failure response
        return {
          success: false,
          error: `Fallback audio analysis failed: ${validReason.toLowerCase()}`,
          transcription: parsedResponse.transcription,
          analysisFailure: this.handleVoiceAnalysisFailure(validReason, parsedResponse.transcription),
          processingTime: Date.now() - startTime,
          isFallback: true
        };
      }

      // Successful analysis - same validation as main processing
      if (!parsedResponse.report) {
        console.warn("[AiService] No report generated despite successful fallback analysis");
        return {
          success: false,
          error: "No report generated despite successful fallback analysis",
          processingTime: Date.now() - startTime,
          isFallback: true
        };
      }

      console.log(
        `[AiService] ✅ Fallback audio processing successful - Processing time: ${
          Date.now() - startTime
        }ms`
      );

      return {
        success: true,
        transcription: parsedResponse.transcription,
        generatedReport: parsedResponse.report,
        processingTime: Date.now() - startTime,
        isFallback: true // Add flag to indicate this was fallback processing
      };

    } catch (fallbackError: any) {
      const totalDuration = Date.now() - startTime;
      console.error(`[AiService] FALLBACK ERROR after ${totalDuration}ms:`, fallbackError);
      return {
        success: false,
        error: `Fallback processing also failed: ${fallbackError.message}`,
        processingTime: totalDuration,
      };
    }
  }

  /**
   * Validate base64 audio data format
   */
  private isValidAudioData(audioData: string): boolean {
    try {
      // Check if it's valid base64
      const decoded = Buffer.from(audioData, "base64");
      return decoded.length > 0;
    } catch {
      return false;
    }
  }

  /**
   * Build the direct audio analysis prompt for Gemini (optimized for direct audio processing)
   */
  private buildDirectAudioAnalysisPrompt(
    userContext?: {
      currentPage?: string;
      userAgent?: string;
      viewport?: { width?: number; height?: number };
    },
    predefinedTags?: { [reportType: string]: string[] }
  ): string {
    const contextInfo = userContext
      ? `

User Context:
- Current Page: ${userContext.currentPage || "Unknown"}
- Browser: ${userContext.userAgent || "Unknown"}
- Viewport: ${
          userContext.viewport
            ? `${userContext.viewport.width}x${userContext.viewport.height}`
            : "Unknown"
        }`
      : "";

    const predefinedTagsInfo = predefinedTags
      ? `

🎯 PREDEFINED TAGS LIST (ALWAYS PREFER THESE - TRY TO USE THEM FIRST):
${Object.entries(predefinedTags)
  .map(([type, tags]) => `- ${type.toUpperCase()}: ${tags.join(", ")}`)
  .join("\n")}

🎯 TAG SELECTION STRATEGY (FOLLOW THIS EXACT ORDER):
1. **STEP 1**: Scan ALL predefined tags above and find EVERY tag that applies to the user's issue
2. **STEP 2**: Think creatively - consider synonyms, related concepts, broader categories
3. **STEP 3**: Be generous with predefined tags - if remotely relevant, include it
4. **STEP 4**: ONLY after exhausting predefined options, add 1-3 specific AI-suggested tags

🚨 TECHNICAL ORIGIN RULES:
- Use origin: "PREDEFINED" ONLY if the tag appears EXACTLY in the above list
- Use origin: "AI_SUGGESTED" for ALL other tags (even if they seem predefined)
- VERIFY each tag against the exact list above before assigning origin

🎯 TARGET: 3-5 predefined tags + 1-3 AI-suggested tags (max 8 total)`
      : `

⚠️ NO PREDEFINED TAGS PROVIDED - USE AI-SUGGESTED ONLY
- Create descriptive, relevant tags for the issue
- Maximum 8 tags total, all with origin: "AI_SUGGESTED"`;

    return `🤖 **PERSONA**: You are an expert bug report analyzer for MUNygo, a peer-to-peer currency exchange platform. You specialize in converting user voice reports into structured, actionable technical documentation.

🌍 **LANGUAGE HANDLING**: You automatically detect and process audio in any language (Persian/Farsi, English, mixed content). Technical terms and UI components can be in any language. Never reject based on language - focus on extracting the technical issue content.

📱 **COMPREHENSIVE MUNYGO DOMAIN CONTEXT**:

**🏦 CORE BUSINESS DOMAIN - P2P Currency Exchange:**
MUNygo is specifically a peer-to-peer currency exchange marketplace where users directly trade currencies (USD, EUR, IRR, etc.) with each other, NOT a general financial app, trading platform, or generic marketplace.

**💰 UNIQUE MUNYGO TERMINOLOGY & CONCEPTS:**
- **Offers**: Users create currency exchange offers (e.g., "Sell 100 USD for 42,000 IRR")
- **Interest Expression**: Users express interest in others' offers to start negotiations
- **Exchange Rates**: Dynamic pricing based on reputation, market rates, and user preferences
- **Reputation System**: 1-5 star ratings that affect exchange rates and trustworthiness
- **Payer Designation**: Complex negotiation system for who pays first in P2P transactions
- **Payment Gates**: Multi-step validation process ensuring both parties are ready to exchange
- **Transaction Tracking**: Real-time status updates for P2P currency exchanges
- **Currency Pairs**: Specific combinations like USD/IRR, EUR/USD with custom rates

**🎯 SPECIFIC USER WORKFLOWS (What Users Actually Do):**
1. **Offer Creation**: Set currency amounts, exchange rates, reputation-based pricing
2. **Offer Browsing**: Filter by currency, amount, rate, user reputation, location
3. **Interest Management**: Express interest, accept/decline incoming interests
4. **Transaction Negotiation**: Chat-based discussion about exchange details
5. **Payment Coordination**: Declare payment readiness, confirm payments, track status
6. **Fund Exchange**: Actual currency transfer between peer users
7. **Reputation Building**: Rate completed exchanges, build trust scores

**🖥️ SPECIFIC UI COMPONENTS & FEATURES:**
- **OfferCard**: Displays currency amounts, rates, user reputation
- **OfferForm**: Complex form for creating exchange offers with rate calculations
- **TransactionFlowCard**: Multi-step guided P2P exchange process
- **PaymentReadinessGate**: Collects payment info before allowing transactions
- **NotificationBell**: Real-time updates for interests, payments, confirmations
- **ReputationIcon**: Visual trust indicators affecting exchange decisions
- **ChatSession**: Transaction-specific messaging with system status updates

**💸 COMMON USER PAIN POINTS (What Users Actually Complain About):**
- Exchange rate calculations being wrong or confusing
- Can't find offers for specific currency pairs (USD/IRR, EUR/USD)
- Interest notifications not arriving or being delayed
- Transaction stuck in "waiting for payment" status
- Chat messages not syncing during negotiations
- Payment confirmation buttons not working
- Reputation scores not updating after successful exchanges
- Phone verification failing during offer creation
- Real-time updates not showing (Socket.IO issues)
- Mobile interface problems during currency exchanges

**🚨 TYPICAL MUNYGO ERROR SCENARIOS:**
- "My USD to IRR offer isn't showing up in browse"
- "Interest notification didn't arrive when someone wanted my EUR"
- "Transaction timer expired before I could confirm payment"
- "Chat history disappeared during my currency exchange"
- "Can't declare payment readiness for my USD sale"
- "Reputation didn't increase after completing exchange"
- "Exchange rate calculation seems incorrect"

**🔄 REAL-TIME FEATURES (Socket.IO Dependent):**
- Live offer updates when new currency exchanges are posted
- Instant interest notifications when users want to exchange
- Real-time chat during P2P transaction negotiations
- Live transaction status updates (payment declared, confirmed, completed)
- Dynamic offer filtering as new currency pairs become available

**❌ WHAT MUNYGO IS NOT (Common Confusion Sources):**
- NOT a general marketplace (only currency exchange)
- NOT a trading platform with charts/analytics
- NOT a banking app with accounts/balances
- NOT a generic report system or data dashboard
- NOT a general chat application
- NOT a generic user management system
- NOT a general notification system

**TECHNICAL STACK**:
- Frontend: Vue 3 + TypeScript + Pinia + Naive UI + Vite (mobile-responsive PWA)
- Backend: Hono + TypeScript + Prisma + PostgreSQL + Socket.IO
- Real-time: Centralized Socket.IO manager for live updates across all features
- Authentication: JWT with phone/email verification and OTP systems
- Key Components: OfferCard, OfferForm, TransactionFlowCard, NotificationBell, PaymentReadinessGate

**CORE USER WORKFLOWS**:
1. **Offer Management**: Create/edit currency exchange offers with dynamic pricing algorithms
2. **Interest Expression**: Browse offers with advanced filtering and express interest with reputation matching
3. **Transaction Flow**: Multi-stage guided process with payment gates, declarations, confirmations, timers
4. **Real-time Chat**: Integrated transaction-context chat with system messages and status updates
5. **Notification System**: Comprehensive real-time updates for all platform activities
6. **Reputation Building**: Trust system through successful exchange completion affecting future rates

🎯 **YOUR PRIMARY TASK**: Generate comprehensive, structured bug reports from audio recordings of user-reported issues.

🔍 **CRITICAL MUNYGO RELEVANCE CHECK**:
Before analyzing content, you MUST verify this is specifically about the MUNygo P2P currency exchange platform:

✅ **PROCEED** with bug report analysis ONLY if audio contains:
- Clear enough speech to understand the main points (slight background noise acceptable)
- At least 3+ seconds of meaningful speech content
- **EXPLICIT MUNYGO-SPECIFIC CONTENT** such as:
  * Currency exchange operations (USD, EUR, IRR rates, exchange offers)
  * P2P transaction flows (payment declarations, confirmations, tracking)
  * MUNygo UI components (OfferCard, TransactionFlow, PaymentGate, NotificationBell)
  * Authentication/verification issues (phone OTP, email verification)
  * Real-time features (Socket.IO chat, notifications, live updates)
  * Reputation system (star ratings, exchange rate impacts)
  * Offer management (create/edit offers, interest expression, browsing)

❌ **MANDATORY FAILURE RESPONSE** for these scenarios:
1. **POOR_QUALITY_OR_SHORT**: Audio too short (<3 sec), too quiet, severely distorted, or completely unclear
2. **TOO_NOISY**: Excessive background noise/music making speech incomprehensible  
3. **IRRELEVANT_CONTENT**: Content about generic software, other apps, general technical discussions, or anything NOT specifically about MUNygo's P2P currency exchange features

🚨 **CRITICAL RELEVANCE EXAMPLES** (Based on MUNygo's P2P Currency Exchange Domain):
- ✅ **DEFINITELY RELEVANT**: 
  * "My USD to IRR offer disappeared from browse"
  * "Interest notification didn't come when someone wanted my EUR" 
  * "Transaction stuck on payment confirmation step"
  * "Chat not syncing during currency exchange"
  * "Exchange rate calculation wrong for my reputation level"
  * "Can't create offer - phone verification failing"
  * "Payment readiness gate not accepting my bank info"
  * "Reputation score didn't update after completing exchange"

- ❌ **DEFINITELY IRRELEVANT**:
  * Generic "report generation" or "data filtering" without currency context
  * General "login issues" without MUNygo authentication context  
  * Abstract "system performance" without P2P exchange specifics
  * Generic "database problems" or "API issues" 
  * General "UI bugs" without currency exchange workflow context
  * Non-financial app discussions (gaming, social media, productivity tools)
  * Generic business reporting or analytics discussions

- 🤔 **BORDERLINE CASES** (Require Careful Analysis):
  * "Notification not working" → RELEVANT if about interests/transactions, IRRELEVANT if generic
  * "Can't save settings" → RELEVANT if about offer preferences, IRRELEVANT if generic app settings
  * "Mobile app slow" → RELEVANT if during currency exchange flows, IRRELEVANT if general performance

**ZERO TOLERANCE POLICY**: If you cannot identify specific MUNygo P2P currency exchange features, workflows, or terminology being discussed, you MUST return IRRELEVANT_CONTENT failure. Do NOT try to force-fit generic technical content into MUNygo context.

**FAILURE RESPONSE FORMAT** (only use if audio fails above criteria):
{
  "analysis_status": "FAILED",
  "failure_reason": "POOR_QUALITY_OR_SHORT" | "TOO_NOISY" | "IRRELEVANT_CONTENT",
  "transcription": "Best effort transcription of what was heard (always provide for user feedback)",
  "confidence": 0.0
}

🎯 **MAIN ANALYSIS WORKFLOW** (ONLY if audio passes MUNygo relevance check):

**STEP 1: VERIFY MUNYGO CONTEXT**
- Confirm user is discussing MUNygo P2P currency exchange platform specifically
- Look for explicit mentions of MUNygo features, UI components, or workflows
- If no clear MUNygo context found, return IRRELEVANT_CONTENT failure immediately

**STEP 2: TRANSCRIBE ACCURATELY**
- Capture every word spoken, including technical terms in any language
- Preserve user's exact language choices and MUNygo-specific terminology
- Include UI component names, error messages, and specific feature references

**STEP 3: CONTEXTUALIZE WITHIN MUNYGO**
- Map user descriptions to MUNygo's specific P2P exchange platform features
- Identify which MUNygo workflow is affected (offers, transactions, chat, notifications)
- Connect issues to specific MUNygo components (OfferCard, TransactionFlow, PaymentGate, etc.)

**STEP 4: EXTRACT ACTIONABLE TECHNICAL DETAILS**
- Identify specific MUNygo problems, UI components, and user workflows affected
- Note browser/device context, specific MUNygo pages, and interaction patterns
- Extract error conditions, timing issues, and reproducible scenarios within MUNygo

**STEP 5: ASSESS SEVERITY AND IMPACT**
Determine severity based on how the issue affects MUNygo's core exchange functionality:
- **critical**: Core exchange/transaction features completely unusable (payment failures, data loss)
- **high**: Major MUNygo workflow disrupted (can't create offers, complete transactions, access funds)
- **medium**: Minor MUNygo workflow issues (UI glitches, non-blocking errors, usability problems)
- **low**: Cosmetic issues, minor inconveniences, MUNygo enhancement suggestions

**STEP 5: CATEGORIZE AND TYPE**
Choose the most fitting type based on user's description:
- **bug**: Functional issues, errors, unexpected behavior
- **feature-request**: New functionality requests, workflow improvements
- **performance**: Speed, loading, responsiveness issues
- **ui-ux**: Interface design, usability, accessibility concerns
- **improvement**: Enhancement of existing features
- **question**: User inquiries about functionality
- **other**: Issues that don't fit standard categories

🏷️ **INTELLIGENT TAGGING STRATEGY**:
${predefinedTagsInfo}

✅ **SUCCESS RESPONSE FORMAT** (primary expected outcome):
{
  "analysis_status": "SUCCESS",
  "transcription": "Complete audio transcription with preserved language and technical terms",
  "report": {
    "title": "Concise bug report title (max 200 chars, include MUNygo feature context)",
    "description": "Comprehensive description (max 2000 chars, reference specific workflows and components)",
    "steps_to_reproduce": "Step-by-step reproduction guide within MUNygo context (optional)",
    "expected_behavior": "What should happen in MUNygo's P2P exchange context (optional)",
    "actual_behavior": "What actually happens in MUNygo platform (optional)", 
    "additional_notes": "Extra context relevant to P2P exchange platform and technical stack (optional)",
    "severity": "low|medium|high|critical",
    "type": "bug|feature-request|performance|ui-ux|improvement|question|other",
    "tags": [{"tag": "tag-name", "origin": "PREDEFINED|AI_SUGGESTED"}],
    "confidence": 0.0-1.0
  }
}

⚠️ **STRICT RELEVANCE ENFORCEMENT**: 
- You MUST return IRRELEVANT_CONTENT failure if the audio doesn't explicitly discuss MUNygo P2P currency exchange features
- Do NOT attempt to map generic technical discussions to MUNygo context
- Do NOT create bug reports for issues that could apply to any software application
- Only proceed if you can confidently identify specific MUNygo features, workflows, or components being discussed
- When in doubt about relevance, always choose IRRELEVANT_CONTENT failure over creating false positive reports

⚠️ **ERROR HANDLING**: Only use the failure format if audio has critical quality issues OR lacks specific MUNygo platform relevance. Prioritize platform-specific content relevance over audio perfection.${contextInfo}`;
  }

  /**
   * Parse Gemini response from direct audio processing
   */
  private parseAudioGeminiResponse(
    content: string,
    predefinedTags?: { [reportType: string]: string[] }
  ): {
    transcription: string;
    report?: AiGeneratedReport;
    analysisStatus: "SUCCESS" | "FAILED";
    failureReason?:
      | "POOR_QUALITY_OR_SHORT"
      | "TOO_NOISY"
      | "IRRELEVANT_CONTENT";
    userMessage?: string;
    suggestions?: string[];
  } {
    try {
      const parsed = JSON.parse(content);
      console.log(`[AiService] 🔍 Parsed AI response structure:`, {
        hasReport: !!parsed.report,
        hasTitle: !!parsed.title,
        hasTags: !!parsed.tags,
        reportHasTags: !!parsed.report?.tags,
        topLevelKeys: Object.keys(parsed),
        reportKeys: parsed.report ? Object.keys(parsed.report) : null
      });

      const transcription = this.sanitizeString(
        parsed.transcription || "Transcription not available",
        5000
      );

      // Check if analysis failed
      if (parsed.analysis_status === "FAILED") {
        // Validate failure reason to ensure it matches our enum
        const validFailureReasons = [
          "POOR_QUALITY_OR_SHORT",
          "TOO_NOISY",
          "IRRELEVANT_CONTENT",
        ];
        const failureReason = validFailureReasons.includes(
          parsed.failure_reason
        )
          ? (parsed.failure_reason as
              | "POOR_QUALITY_OR_SHORT"
              | "TOO_NOISY"
              | "IRRELEVANT_CONTENT")
          : "POOR_QUALITY_OR_SHORT";

        return {
          transcription,
          analysisStatus: "FAILED",
          failureReason,
          userMessage: parsed.user_message,
          suggestions: parsed.suggestions || [],
        };
      }

      // Successful analysis
      const reportData = parsed.report || parsed; // Handle both nested and flat structures for backward compatibility
      
      const report: AiGeneratedReport = {
        title: this.sanitizeString(
          reportData.title || "AI-Generated Bug Report",
          200
        ),
        description: this.sanitizeString(
          reportData.description || "No description provided",
          2000
        ),
        stepsToReproduce: reportData.steps_to_reproduce
          ? this.sanitizeString(reportData.steps_to_reproduce, 2000)
          : undefined,
        expectedBehavior: reportData.expected_behavior
          ? this.sanitizeString(reportData.expected_behavior, 1000)
          : undefined,
        actualBehavior: reportData.actual_behavior
          ? this.sanitizeString(reportData.actual_behavior, 1000)
          : undefined,
        additionalNotes: reportData.additional_notes
          ? this.sanitizeString(reportData.additional_notes, 1000)
          : undefined,
        suggestedSeverity: this.validateSeverity(reportData.suggestedSeverity || reportData.severity),
        suggestedType: this.validateType(reportData.suggestedType || reportData.type),
        suggestedTags: this.validateTags(reportData.suggestedTags || reportData.tags, predefinedTags),
        confidence: this.validateConfidence(reportData.confidence),
      };

      return {
        transcription,
        report,
        analysisStatus: "SUCCESS",
      };
    } catch (error: any) {
      console.error(
        "[AiService] Failed to parse direct audio Gemini response:",
        error
      );
      console.error("[AiService] Raw response content:", content);

      // Re-throw JSON parsing errors so they can be handled appropriately upstream
      if (error instanceof SyntaxError && error.message.includes('JSON')) {
        throw error;
      }

      // Return a fallback response for other parsing errors
      return {
        transcription: "Audio transcription failed",
        analysisStatus: "FAILED",
        failureReason: "POOR_QUALITY_OR_SHORT",
        userMessage:
          "The AI service encountered an error while processing your audio. Please try recording again with clearer audio quality.",
        suggestions: [
          "Ensure you are in a quiet environment",
          "Speak clearly and at a normal pace",
          "Hold the microphone closer to your mouth",
          "Try recording for at least 5-10 seconds",
        ],
      };
    }
  }

  /**
   * Parse Gemini response and validate the generated report
   */
  private parseGeminiResponse(
    content: string,
    predefinedTags?: { [reportType: string]: string[] }
  ): AiGeneratedReport {
    try {
      // With structured output, the response should already be valid JSON
      const parsed = JSON.parse(content);

      // Validate and sanitize the parsed response (extra safety)
      const report: AiGeneratedReport = {
        title: this.sanitizeString(
          parsed.title || "AI-Generated Bug Report",
          200
        ),
        description: this.sanitizeString(
          parsed.description || "No description provided",
          2000
        ),
        stepsToReproduce: parsed.stepsToReproduce
          ? this.sanitizeString(parsed.stepsToReproduce, 2000)
          : undefined,
        expectedBehavior: parsed.expectedBehavior
          ? this.sanitizeString(parsed.expectedBehavior, 1000)
          : undefined,
        actualBehavior: parsed.actualBehavior
          ? this.sanitizeString(parsed.actualBehavior, 1000)
          : undefined,
        additionalNotes: parsed.additionalNotes
          ? this.sanitizeString(parsed.additionalNotes, 1000)
          : undefined,
        suggestedSeverity: this.validateSeverity(parsed.suggestedSeverity),
        suggestedType: this.validateType(parsed.suggestedType),
        suggestedTags: this.validateTags(parsed.suggestedTags, predefinedTags),
        confidence: this.validateConfidence(parsed.confidence),
      };

      return report;
    } catch (error: any) {
      console.error("[AiService] Failed to parse Gemini response:", error);
      console.error("[AiService] Raw response content:", content);

      // Return a fallback report
      return {
        title: "AI Analysis Failed",
        description:
          "The AI service was unable to properly analyze the voice input. Please fill out the form manually.",
        suggestedSeverity: "medium",
        suggestedType: "other",
        confidence: 0.1,
      };
    }
  }

  /**
   * Utility methods for validation and sanitization
   */
  private sanitizeString(str: string, maxLength: number): string {
    return str.trim().substring(0, maxLength);
  }

  private validateSeverity(
    severity: any
  ): "low" | "medium" | "high" | "critical" {
    const validSeverities = ["low", "medium", "high", "critical"];
    return validSeverities.includes(severity) ? severity : "medium";
  }

  private validateType(
    type: any
  ):
    | "bug"
    | "feature-request"
    | "performance"
    | "ui-ux"
    | "improvement"
    | "question"
    | "other" {
    const validTypes = [
      "bug",
      "feature-request",
      "performance",
      "ui-ux",
      "improvement",
      "question",
      "other",
    ];
    return validTypes.includes(type) ? type : "bug";
  }

  private validateTags(
    tags: any,
    predefinedTags?: { [reportType: string]: string[] }
  ): Array<{ tag: string; origin: "PREDEFINED" | "AI_SUGGESTED" }> | undefined {
    if (!Array.isArray(tags)) return undefined;

    // Create a flat list of all predefined tags for validation
    const allPredefinedTags: string[] = [];
    if (predefinedTags) {
      Object.values(predefinedTags).forEach((tagList) => {
        allPredefinedTags.push(...tagList);
      });
    }

    return tags
      .filter(
        (tagItem) =>
          tagItem &&
          typeof tagItem.tag === "string" &&
          tagItem.tag.length <= 50 &&
          ["PREDEFINED", "AI_SUGGESTED"].includes(tagItem.origin)
      )
      .map((tagItem) => {
        // Validate that tags marked as PREDEFINED actually exist in the predefined list
        if (tagItem.origin === "PREDEFINED") {
          const isActuallyPredefined = allPredefinedTags.includes(tagItem.tag);
          if (!isActuallyPredefined) {
            console.warn(
              `[AiService] Tag "${tagItem.tag}" marked as PREDEFINED but not found in predefined tags list. Converting to AI_SUGGESTED.`
            );
            return {
              tag: tagItem.tag,
              origin: "AI_SUGGESTED" as const,
            };
          }
        }

        return {
          tag: tagItem.tag,
          origin: tagItem.origin as "PREDEFINED" | "AI_SUGGESTED",
        };
      })
      .slice(0, 10);
  }

  private validateConfidence(confidence: any): number {
    const num = parseFloat(confidence);
    if (isNaN(num)) return 0.5;
    return Math.max(0, Math.min(1, num));
  }

  private readonly FAILURE_REASON_KEYS = {
    POOR_QUALITY_OR_SHORT: "voice.analysisFailures.audioUnclear",
    TOO_NOISY: "voice.analysisFailures.backgroundNoise",
    IRRELEVANT_CONTENT: "voice.analysisFailures.irrelevantContent"
  } as const;

  private readonly SUGGESTION_KEYS = {
    POOR_QUALITY_OR_SHORT: [
      "voice.suggestions.speakClearly",
      "voice.suggestions.quietEnvironment",
      "voice.suggestions.recordLonger"
    ],
    TOO_NOISY: [
      "voice.suggestions.quietEnvironment",
      "voice.suggestions.avoidBackgroundNoise"
    ],
    IRRELEVANT_CONTENT: [
      "voice.suggestions.focusOnTechnicalDetails",
      "voice.suggestions.describeIssueClearly"
    ]
  } as const;

  /**
   * Get default user message key for analysis failures
   */
  private getDefaultUserMessageKey(failureReason: VoiceAnalysisFailureReason): string {
    return this.FAILURE_REASON_KEYS[failureReason] || this.FAILURE_REASON_KEYS.POOR_QUALITY_OR_SHORT;
  }

  /**
   * Get suggestion keys for a failure reason
   */
  private getSuggestionKeys(failureReason: VoiceAnalysisFailureReason): string[] {
    return [...(this.SUGGESTION_KEYS[failureReason] || this.SUGGESTION_KEYS.POOR_QUALITY_OR_SHORT)];
  }

  /**
   * Handle voice analysis failure with proper translation keys
   */
  private handleVoiceAnalysisFailure(reason: VoiceAnalysisFailureReason, transcription?: string): VoiceAnalysisFailure {
    return {
      reason,
      userMessageKey: this.getDefaultUserMessageKey(reason),
      suggestionKeys: this.getSuggestionKeys(reason),
      transcription,
      confidence: 0.0
    };
  }

  /**
   * Get optimized configuration based on audio characteristics
   */
  private getOptimizedConfig(audioSize: number, mimeType: string) {
    console.log(`[AiService] Optimizing config for audio size: ${audioSize} bytes, type: ${mimeType}`);
    
    // Base configuration
    let config = {
      temperature: 0.1,
      thinkingConfig: {
        thinkingBudget: 0,
      },
      responseMimeType: "application/json",
      responseSchema: {
        type: Type.OBJECT,
        properties: {
          analysis_status: {
            type: Type.STRING,
            enum: ["SUCCESS", "FAILED"],
          },
          failure_reason: {
            type: Type.STRING,
            enum: [
              "POOR_QUALITY_OR_SHORT",
              "TOO_NOISY", 
              "IRRELEVANT_CONTENT",
            ],
          },
          user_message: {
            type: Type.STRING,
          },
          suggestions: {
            type: Type.ARRAY,
            items: {
              type: Type.STRING,
            },
          },
          transcription: {
            type: Type.STRING,
          },
          report: {
            type: Type.OBJECT,
            properties: {
              title: {
                type: Type.STRING,
              },
              description: {
                type: Type.STRING,
              },
              steps_to_reproduce: {
                type: Type.STRING,
              },
              expected_behavior: {
                type: Type.STRING,
              },
              actual_behavior: {
                type: Type.STRING,
              },
              additional_notes: {
                type: Type.STRING,
              },
              severity: {
                type: Type.STRING,
                enum: ["low", "medium", "high", "critical"],
              },
              type: {
                type: Type.STRING,
                enum: [
                  "bug",
                  "feature-request",
                  "performance",
                  "ui-ux",
                  "improvement",
                  "question",
                  "other",
                ],
              },
              tags: {
                type: Type.ARRAY,
                items: {
                  type: Type.OBJECT,
                  properties: {
                    tag: {
                      type: Type.STRING,
                    },
                    origin: {
                      type: Type.STRING,
                      enum: ["PREDEFINED", "AI_SUGGESTED"],
                    },
                  },
                  required: ["tag", "origin"],
                },
              },
              confidence: {
                type: Type.NUMBER,
              },
            },
            required: ["title", "description", "severity", "type", "tags", "confidence"],
          },
        },
        required: ["analysis_status", "transcription"],
      },
    };
    
    // Optimize based on audio size
    if (audioSize > 2000000) { // 2MB+
      console.log(`[AiService] Large audio detected - using optimized settings`);
      config.temperature = 0.2; // Slightly higher temperature for faster processing
      // Remove some optional fields from the report object for faster processing
      const optimizedReportProperties = { ...config.responseSchema.properties.report.properties };
      delete (optimizedReportProperties as any).steps_to_reproduce;
      delete (optimizedReportProperties as any).expected_behavior;
      delete (optimizedReportProperties as any).actual_behavior;
      delete (optimizedReportProperties as any).additional_notes;
      
      // Update the report schema with optimized properties
      config.responseSchema.properties.report.properties = optimizedReportProperties;
      config.responseSchema.properties.report.required = ["title", "description", "severity", "type", "tags", "confidence"];
    }
    
    return config;
  }
}
