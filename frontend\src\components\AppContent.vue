<template>  <router-view v-slot="{ Component, route }">
    <template v-if="route.name === 'landing' || route.name === 'login' || route.name === 'register'">
      <component :is="Component" />
    </template>
    <template v-else-if="route.name === 'NavBarDemo'">
      <!-- Demo view handles its own navbar, don't render DreamNavBar to avoid conflicts -->
      <component :is="Component" />
    </template>
    <template v-else-if="route.name === 'ChatSession' || route.name === 'TransactionalChat'">
      <!-- Chat and transactional chat views use full screen layout without navbar and padding -->
      <div class="chat-fullscreen-container">
        <component :is="Component" />
      </div>
    </template>
    <template v-else>      
      <div class="app-container">        <!-- Use the new DreamNavBar component -->
        <DreamNavBar />
        
        <!-- Main Content -->
        <div class="main-content">
          <component :is="Component" />
        </div>
      </div>
    </template>
  </router-view>
  <DeclineInterestModal v-if="isAuthenticated" />
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue';
import { useAuthStore } from '@/stores/auth';
import DeclineInterestModal from '@/components/DeclineInterestModal.vue';
import DreamNavBar from '@/components/DreamNavBar.vue';

const authStore = useAuthStore();

const isAuthenticated = computed(() => authStore.isAuthenticated);

// Note: Socket initialization is handled automatically by auth store
// when authentication state changes, so no manual initialization needed here

onMounted(() => {
  console.log('🔥 [AppContent] Component mounted');
  // Note: Offers fetching is handled automatically by myOffersStore 
  // when authentication state changes, so no manual fetching needed here
});
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  min-height: 0;
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 12px;
  }
}
</style>
