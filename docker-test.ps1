# PowerShell Docker Local Testing Script for MUNygo
# Usage: .\docker-test.ps1 [start|stop|cleanup|logs|health]

param(
    [Parameter(Position=0)]
    [string]$Action = "start"
)

Write-Host "🚀 MUNygo Docker Local Testing Script" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop and try again." -ForegroundColor Red
    exit 1
}

# Function to cleanup previous containers
function Cleanup {
    Write-Host "🧹 Cleaning up previous containers and volumes..." -ForegroundColor Yellow
    docker-compose down -v
    docker system prune -f
}

# Function to build and start services
function Start-Services {
    Write-Host "🔨 Building and starting MUNygo services..." -ForegroundColor Yellow
    docker-compose up --build -d
}

# Function to check service health
function Check-Health {
    Write-Host "🏥 Checking service health..." -ForegroundColor Yellow
    
    Write-Host "Waiting for services to start..." -ForegroundColor Gray
    Start-Sleep -Seconds 30
    
    # Check PostgreSQL
    try {
        docker-compose exec postgres pg_isready -U munygo | Out-Null
        Write-Host "✅ PostgreSQL is healthy" -ForegroundColor Green
    } catch {
        Write-Host "❌ PostgreSQL is not healthy" -ForegroundColor Red
    }
    
    # Check Backend
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/health" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Backend is healthy" -ForegroundColor Green
        } else {
            Write-Host "❌ Backend is not healthy" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Backend is not healthy" -ForegroundColor Red
    }
    
    # Check Frontend
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:80" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Frontend is healthy" -ForegroundColor Green
        } else {
            Write-Host "❌ Frontend is not healthy" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Frontend is not healthy" -ForegroundColor Red
    }
}

# Function to show logs
function Show-Logs {
    Write-Host "📋 Service logs:" -ForegroundColor Yellow
    docker-compose logs --tail=50
}

# Function to open browser
function Open-App {
    Write-Host "🌐 Opening MUNygo in your browser..." -ForegroundColor Yellow
    Start-Process "http://localhost"
}

# Main execution
switch ($Action.ToLower()) {
    "cleanup" {
        Cleanup
    }
    "start" {
        Cleanup
        Start-Services
        Check-Health
        Show-Logs
        Open-App
    }
    "stop" {
        Write-Host "🛑 Stopping all services..." -ForegroundColor Yellow
        docker-compose down
    }
    "logs" {
        Show-Logs
    }
    "health" {
        Check-Health
    }
    default {
        Write-Host "Usage: .\docker-test.ps1 [start|stop|cleanup|logs|health]" -ForegroundColor White
        Write-Host "  start   - Clean, build and start all services (default)" -ForegroundColor Gray
        Write-Host "  stop    - Stop all services" -ForegroundColor Gray
        Write-Host "  cleanup - Remove all containers and volumes" -ForegroundColor Gray
        Write-Host "  logs    - Show service logs" -ForegroundColor Gray
        Write-Host "  health  - Check service health" -ForegroundColor Gray
    }
}
