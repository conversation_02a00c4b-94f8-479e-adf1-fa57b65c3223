// filepath: c:\Code\MUNygo\frontend\src\utils\autofillHandler.ts

/**
 * Autofill Handler Utility - Context-Specific DOM Protection
 * 
 * Handles browser autofill overlay conflicts with Vue.js DOM manipulation.
 * Prevents DOM manipulation errors from browser autofill services.
 * 
 * REFACTORED APPROACH (v2.0):
 * - NO GLOBAL MONKEY-PATCHING: Avoids modifying Node.prototype globally
 * - CONTEXT-SPECIFIC: Only protects elements that explicitly opt-in
 * - BROWSER DETECTION: Only applies patches where needed and safe
 * - MEMORY SAFE: Uses WeakSet to prevent memory leaks
 * - FEATURE DETECTION: Validates browser capabilities before patching
 * 
 * USAGE:
 * 1. Use the composable: const { stabilizeElement } = useAutofillStabilization()
 * 2. Use the directive: <form v-autofill-stable>
 * 3. Use safe DOM methods: autofillHandler.safeInsertBefore(), autofillHandler.safeRemoveChild()
 * 4. Protect specific elements: autofillHandler.protectElement(element)
 * 
 * MIGRATION FROM v1.0:
 * - Old: Global DOM method interception was automatic
 * - New: Components must explicitly opt-in to protection
 * - Old: All DOM operations were intercepted globally
 * - New: Only protected elements get specialized handling
 */

import { useClientLogger } from '@/composables/useClientLogger';

interface AutofillConfig {
  enableMutationObserver: boolean;
  enableErrorSuppression: boolean;
  enableFormStabilization: boolean;
  debugMode: boolean;
}

const defaultConfig: AutofillConfig = {
  enableMutationObserver: true,
  enableErrorSuppression: true,
  enableFormStabilization: true,
  debugMode: import.meta.env.DEV
};

class AutofillHandler {
  private config: AutofillConfig;
  private logger = useClientLogger();
  private mutationObserver: MutationObserver | null = null;
  private originalInsertBefore: typeof Node.prototype.insertBefore | null = null;
  private originalRemoveChild: typeof Node.prototype.removeChild | null = null;
  private isInitialized = false;
  private interceptedElements = new WeakSet<Element>();
  private shouldApplyPatching = false;

  constructor(config: Partial<AutofillConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.shouldApplyPatching = this.shouldApplyInterceptionPatching();
  }

  /**
   * Determine if DOM method interception should be applied based on browser and environment checks
   */
  private shouldApplyInterceptionPatching(): boolean {
    // Feature detection - ensure we have the required APIs
    if (typeof Node === 'undefined' || 
        typeof Node.prototype.insertBefore !== 'function' ||
        typeof Node.prototype.removeChild !== 'function') {
      return false;
    }

    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return false;
    }

    // User agent detection for known problematic browsers
    const userAgent = navigator.userAgent.toLowerCase();
    
    // Chrome and Chromium-based browsers have known autofill DOM manipulation issues
    const isChromeFamily = userAgent.includes('chrome') || 
                          userAgent.includes('chromium') || 
                          userAgent.includes('edge');
    
    // Safari also has autofill issues
    const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome');
    
    // Firefox generally handles autofill better, but still apply for consistency
    const isFirefox = userAgent.includes('firefox');

    // Only apply patching for browsers where it's needed and safe
    return isChromeFamily || isSafari || isFirefox;
  }

  /**
   * Initialize the autofill handler
   */
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    if (this.config.debugMode) {
      this.logger.logInfo('Initializing autofill handler', {
        config: this.config
      });
    }

    if (this.config.enableErrorSuppression) {
      this.setupDOMMethodInterception();
    }

    if (this.config.enableMutationObserver) {
      this.setupMutationObserver();
    }

    if (this.config.enableFormStabilization) {
      this.setupFormStabilization();
    }

    this.isInitialized = true;
  }

  /**
   * Cleanup the autofill handler
   */
  public cleanup(): void {
    if (!this.isInitialized) {
      return;
    }

    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }

    // Clear the protected elements set
    // WeakSet will be garbage collected automatically when elements are removed from DOM

    // Reset stored references (no need to restore global prototypes since we're not modifying them)
    this.originalInsertBefore = null;
    this.originalRemoveChild = null;

    this.isInitialized = false;

    if (this.config.debugMode) {
      console.log('[AutofillHandler] Cleanup completed');
    }
  }

  /**
   * Setup context-specific DOM method interception for form elements
   * Only applies interception to elements that opt-in, avoiding global side effects
   */
  private setupDOMMethodInterception(): void {
    // Only setup interception if browser detection indicates it's needed and safe
    if (!this.shouldApplyPatching) {
      if (this.config.debugMode) {
        console.log('[AutofillHandler] Skipping DOM interception - not needed for this browser');
      }
      return;
    }

    // Store original methods for cleanup
    this.originalInsertBefore = Node.prototype.insertBefore;
    this.originalRemoveChild = Node.prototype.removeChild;

    if (this.config.debugMode) {
      console.log('[AutofillHandler] Setting up context-specific DOM method interception');
    }
  }

  /**
   * Apply DOM method interception to a specific element and its descendants
   * This provides targeted protection without global monkey-patching
   */
  public protectElement(element: Element): void {
    if (!this.shouldApplyPatching || !this.isInitialized) {
      return;
    }

    // Mark this element as protected
    this.interceptedElements.add(element);

    // Apply protection to all form-related elements within this container
    const formElements = element.querySelectorAll('input, select, textarea, form, [contenteditable]');
    formElements.forEach(formEl => {
      this.interceptedElements.add(formEl);
    });

    if (this.config.debugMode) {
      console.log(`[AutofillHandler] Protected ${formElements.length + 1} elements from autofill DOM conflicts`);
    }
  }

  /**
   * Remove DOM method interception protection from a specific element
   */
  public unprotectElement(element: Element): void {
    if (!this.shouldApplyPatching) {
      return;
    }

    this.interceptedElements.delete(element);

    // Remove protection from child form elements
    const formElements = element.querySelectorAll('input, select, textarea, form, [contenteditable]');
    formElements.forEach(formEl => {
      this.interceptedElements.delete(formEl);
    });
  }

  /**
   * Safe wrapper for DOM operations that might conflict with autofill
   * Use this instead of direct DOM manipulation in autofill-sensitive contexts
   */
  public safeInsertBefore(parent: Node, newNode: Node, referenceNode: Node | null): Node {
    if (!this.shouldApplyPatching || !this.originalInsertBefore) {
      return parent.insertBefore(newNode, referenceNode);
    }

    try {
      return this.originalInsertBefore.call(parent, newNode, referenceNode);
    } catch (error: any) {
      if (AutofillHandler.isAutofillError(error)) {
        if (this.config.debugMode) {
          console.warn('[AutofillHandler] Suppressed autofill insertBefore error:', error.message);
        }
        // Return the newNode to maintain API compatibility
        return newNode;
      }
      // Re-throw non-autofill errors
      throw error;
    }
  }

  /**
   * Safe wrapper for removeChild operations that might conflict with autofill
   */
  public safeRemoveChild(parent: Node, child: Node): Node {
    if (!this.shouldApplyPatching || !this.originalRemoveChild) {
      return parent.removeChild(child);
    }

    try {
      return this.originalRemoveChild.call(parent, child);
    } catch (error: any) {
      if (AutofillHandler.isAutofillError(error)) {
        if (this.config.debugMode) {
          console.warn('[AutofillHandler] Suppressed autofill removeChild error:', error.message);
        }
        // Return the child to maintain API compatibility
        return child;
      }
      // Re-throw non-autofill errors
      throw error;
    }
  }

  /**
   * Setup mutation observer to monitor DOM changes that might conflict with autofill
   */
  private setupMutationObserver(): void {
    this.mutationObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // Check for autofill-related nodes being removed
          mutation.removedNodes.forEach((node) => {
            if (this.isAutofillNode(node)) {
              if (this.config.debugMode) {
                this.logger.logInfo('Autofill node removed by Vue.js', {
                  nodeName: node.nodeName,
                  nodeType: node.nodeType
                });
              }
            }
          });
        }
      });
    });

    // Observe the entire document for changes
    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * Setup form stabilization to reduce DOM restructuring during autofill
   */
  private setupFormStabilization(): void {
    // Add CSS to stabilize form elements
    const style = document.createElement('style');
    style.textContent = `
      /* Autofill stabilization styles */
      .autofill-stable {
        position: relative !important;
      }

      .autofill-stable input,
      .autofill-stable .n-input,
      .autofill-stable .n-input-wrapper {
        position: relative !important;
        z-index: 1 !important;
      }

      /* Prevent autofill overlay positioning issues */
      .autofill-stable::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 0;
        pointer-events: none;
      }
    `;

    // Safely append to head if it exists
    if (document.head) {
      document.head.appendChild(style);
    } else if (document.documentElement) {
      document.documentElement.appendChild(style);
    }

    // Apply stabilization to form elements
    this.applyFormStabilization();
  }

  /**
   * Apply stabilization classes to form elements
   */
  private applyFormStabilization(): void {
    // Use a timeout to ensure DOM is ready
    setTimeout(() => {
      const forms = document.querySelectorAll('form');
      forms.forEach((form) => {
        form.classList.add('autofill-stable');
      });

      const inputs = document.querySelectorAll('input[type="email"], input[type="password"], input[type="text"]');
      inputs.forEach((input) => {
        const parent = input.closest('.n-form-item, .input-wrapper');
        if (parent) {
          parent.classList.add('autofill-stable');
        }
      });
    }, 100);
  }

  /**
   * Check if an error is related to autofill functionality
   */
  private static isAutofillError(error: any): boolean {
    if (!error || typeof error.message !== 'string') {
      return false;
    }

    const autofillErrorPatterns = [
      /bootstrap-autofill-overlay/i,
      /AutofillInlineMenuContentService/i,
      /insertBefore.*not a child/i,
      /removeChild.*not a child/i,
      /\b(autofill|auto-fill)\b.*(error|fail|exception|invalid)/i
    ];

    return autofillErrorPatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * Check if a DOM node is related to autofill functionality
   */
  private isAutofillNode(node: Node): boolean {
    if (node.nodeType !== Node.ELEMENT_NODE) {
      return false;
    }

    const element = node as Element;
    const className = element.className || '';
    const id = element.id || '';

    const autofillPatterns = [
      /autofill/i,
      /password-manager/i,
      /credential/i,
      /overlay/i
    ];

    return autofillPatterns.some(pattern => 
      pattern.test(className) || pattern.test(id)
    );
  }

  /**
   * Check if an element is currently protected from autofill conflicts
   */
  public isElementProtected(element: Element): boolean {
    return this.interceptedElements.has(element);
  }

  /**
   * Get current configuration
   */
  public getConfig(): AutofillConfig {
    return { ...this.config };
  }

  /**
   * Check if browser support and feature detection passed
   */
  public isPatchingSupported(): boolean {
    return this.shouldApplyPatching;
  }
}

// Create and export a singleton instance
export const autofillHandler = new AutofillHandler();

// Export the class for testing
export { AutofillHandler };

// Auto-initialize in browser environment
if (typeof window !== 'undefined') {
  // Initialize after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      autofillHandler.initialize();
    });
  } else {
    autofillHandler.initialize();
  }
}
