/**
 * Utility functions for handling tag origin styling and display
 */

/**
 * Get CSS class name based on tag origin
 * @param origin - The tag origin (PREDEFINED, AI_SUGGESTED, USER_DEFINED, etc.)
 * @returns CSS class name for styling the tag
 */
export function getTagOriginClass(origin: string): string {
  switch (origin) {
    case 'PREDEFINED':
      return 'tag-predefined';
    case 'AI_SUGGESTED':
      return 'tag-ai-suggested';
    case 'USER_DEFINED':
      return 'tag-user-defined';
    default:
      return 'tag-unknown';
  }
}

/**
 * Get human-readable title based on tag origin
 * @param origin - The tag origin (PREDEFINED, AI_SUGGESTED, USER_DEFINED, etc.)
 * @returns Human-readable title for display/tooltip
 */
export function getTagOriginTitle(origin: string): string {
  switch (origin) {
    case 'PREDEFINED':
      return 'Predefined tag';
    case 'AI_SUGGESTED':
      return 'AI-suggested tag';
    case 'USER_DEFINED':
      return 'User-defined tag';
    default:
      return 'Unknown origin';
  }
}

/**
 * Get the display name for a tag based on its structure
 * @param tag - The tag object with either tagId, tagName, or tag property
 * @returns The display name for the tag
 */
export function getTagDisplayName(tag: any): string {
  // Handle the current backend format where tags are formatted as:
  // { tag: "Tag Name", origin: "PREDEFINED" }
  if (tag.tag && typeof tag.tag === 'string') {
    return tag.tag;
  }
  
  // Handle legacy formats
  if (tag.tagName && typeof tag.tagName === 'string') {
    return tag.tagName;
  }
  
  // Handle direct tag ID (fallback case)
  if (tag.tagId && typeof tag.tagId === 'string') {
    return tag.tagId;
  }
  
  // Handle if the tag itself is just a string
  if (typeof tag === 'string') {
    return tag;
  }
  
  console.warn('[tagOriginUtils] Tag with no display name:', tag);
  return 'Unknown Tag';
}
