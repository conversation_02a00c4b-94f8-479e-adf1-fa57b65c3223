/**
 * Translation migration utility
 * This file helps identify and migrate text that needs translation
 */

// Common English text patterns that should be translated
export const TRANSLATION_PATTERNS = [
  // Buttons and actions
  'Save', 'Cancel', 'Submit', 'Edit', 'Delete', 'Add', 'Create', 'Update',
  'Confirm', 'Close', 'Back', 'Next', 'Previous', 'Refresh', 'Search',
  'Filter', 'Sort', 'Upload', 'Download', 'Export', 'Import',
  
  // Status and states
  'Active', 'Inactive', 'Pending', 'Completed', 'Failed', 'Success',
  'Error', 'Warning', 'Loading', 'Processing', 'Available', 'Unavailable',
  'Online', 'Offline', 'Connected', 'Disconnected', 'Verified', 'Unverified',
  
  // Common phrases
  'No data available', 'No results found', 'Loading...', 'Please wait...',
  'Something went wrong', 'Try again', 'Are you sure?', 'This action cannot be undone',
  
  // Form labels
  'Email', 'Password', 'Username', 'First Name', 'Last Name', 'Phone Number',
  'Address', 'City', 'Country', 'Description', 'Title', 'Name', 'Amount',
  'Rate', 'Currency', 'Date', 'Time', 'Status', 'Type', 'Category',
  
  // Navigation
  'Home', 'Profile', 'Settings', 'Help', 'About', 'Contact', 'Dashboard',
  'Notifications', 'Messages', 'Chat', 'History', 'Reports',
  
  // Offer-related
  'Browse Offers', 'My Offers', 'Create Offer', 'Edit Offer', 'Delete Offer',
  'Show Interest', 'Accept', 'Decline', 'Buy', 'Sell', 'Exchange Rate',
  'Total Amount', 'Offer Details', 'Created By', 'Created At',
  
  // Authentication
  'Sign In', 'Sign Up', 'Log Out', 'Register', 'Login', 'Forgot Password',
  'Reset Password', 'Change Password', 'Current Password', 'New Password',
  'Confirm Password', 'Remember Me', 'Terms of Service', 'Privacy Policy',
  
  // Profile and verification
  'Account Status', 'Email Verification', 'Phone Verification', 'Verified',
  'Not Verified', 'Pending Verification', 'Resend Email', 'Change Photo',
  'Member since', 'Last login', 'Reputation', 'Transactions',
  
  // Currencies
  'Iranian Rial', 'US Dollar', 'Euro', 'British Pound', 'Canadian Dollar',
  'Australian Dollar', 'UAE Dirham', 'Swiss Franc', 'Japanese Yen',
  
  // Time and dates
  'Today', 'Yesterday', 'Tomorrow', 'Now', 'Recently', 'Last week',
  'Last month', 'Last year', 'Never', 'Always', 'Sometimes',
  
  // Chat and messaging
  'Send Message', 'Type your message', 'Online', 'Offline', 'Last seen',
  'Message delivered', 'Message read', 'Clear chat', 'Delete message',
  'Chat history', 'No messages', 'Connecting...', 'Connected', 'Reconnecting',
  
  // Notifications
  'Mark as read', 'Clear all', 'No notifications', 'New notification',
  'Interest accepted', 'Interest declined', 'Payment received', 'Payment sent',
  
  // Errors and validation
  'This field is required', 'Invalid email', 'Password too short',
  'Passwords don\'t match', 'Network error', 'Server error', 'Invalid format',
  'File too large', 'Upload failed', 'Connection lost', 'Session expired'
];

// Helper function to suggest translation keys for text
export function suggestTranslationKey(text: string): string {
  const normalized = text.trim();
  
  // Convert to camelCase key
  const key = normalized
    .toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove special characters
    .replace(/\s+/g, ' ') // Normalize spaces
    .split(' ')
    .map((word, index) => index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
    
  return key;
}

// Helper function to determine the appropriate translation namespace
export function suggestNamespace(text: string, context?: string): string {
  const normalized = text.toLowerCase();
  
  if (context?.includes('auth') || 
      ['login', 'register', 'password', 'email', 'sign'].some(term => normalized.includes(term))) {
    return 'auth';
  }
  
  if (context?.includes('offer') || 
      ['offer', 'buy', 'sell', 'exchange', 'rate', 'currency'].some(term => normalized.includes(term))) {
    return 'offers';
  }
  
  if (context?.includes('profile') || 
      ['profile', 'verification', 'verified', 'account', 'reputation'].some(term => normalized.includes(term))) {
    return 'profile';
  }
  
  if (context?.includes('chat') || 
      ['chat', 'message', 'send', 'online', 'offline'].some(term => normalized.includes(term))) {
    return 'chat';
  }
  
  if (context?.includes('notification') || 
      ['notification', 'alert', 'mark as read'].some(term => normalized.includes(term))) {
    return 'notifications';
  }
  
  if (['error', 'warning', 'failed', 'invalid'].some(term => normalized.includes(term))) {
    return 'errors';
  }
  
  if (['home', 'browse', 'profile', 'settings', 'help'].some(term => normalized.includes(term))) {
    return 'navigation';
  }
  
  if (['save', 'cancel', 'submit', 'edit', 'delete', 'confirm', 'close'].some(term => normalized.includes(term))) {
    return 'app';
  }
  
  return 'common';
}

// Function to generate translation replacement suggestion
export function generateTranslationReplacement(text: string, context?: string): string {
  const namespace = suggestNamespace(text, context);
  const key = suggestTranslationKey(text);
  return `{{ t('${namespace}.${key}') }}`;
}

export default {
  TRANSLATION_PATTERNS,
  suggestTranslationKey,
  suggestNamespace,
  generateTranslationReplacement
};
