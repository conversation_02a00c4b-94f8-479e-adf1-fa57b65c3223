import { defineStore } from 'pinia'
import { ref } from 'vue'

const useUiPreferencesStore = defineStore('uiPreferences', () => {
  // Initialize from localStorage immediately
  const savedHomeValue = localStorage.getItem('useNewHomeDesign')
  const useNewHomeDesign = ref(savedHomeValue === 'true')
  
  const savedOfferValue = localStorage.getItem('useSimpleOfferForm')
  const useSimpleOfferForm = ref(savedOfferValue === 'true' || savedOfferValue === null) // Enable by default
  
  const savedBrowseValue = localStorage.getItem('useEnhancedBrowse')
  const useEnhancedBrowse = ref(savedBrowseValue === 'true' || savedBrowseValue === null) // Enable by default
  
  // Home Design Controls
  const toggleNewHomeDesign = () => {
    useNewHomeDesign.value = !useNewHomeDesign.value
    localStorage.setItem('useNewHomeDesign', String(useNewHomeDesign.value))
  }
  
  const enableNewHomeDesign = () => {
    useNewHomeDesign.value = true
    localStorage.setItem('useNewHomeDesign', 'true')
  }
  
  const disableNewHomeDesign = () => {
    useNewHomeDesign.value = false
    localStorage.setItem('useNewHomeDesign', 'false')
  }
  
  // Simple Offer Form Controls
  const toggleSimpleOfferForm = () => {
    useSimpleOfferForm.value = !useSimpleOfferForm.value
    localStorage.setItem('useSimpleOfferForm', String(useSimpleOfferForm.value))
  }
  
  const enableSimpleOfferForm = () => {
    useSimpleOfferForm.value = true
    localStorage.setItem('useSimpleOfferForm', 'true')
  }
  
  const disableSimpleOfferForm = () => {
    useSimpleOfferForm.value = false
    localStorage.setItem('useSimpleOfferForm', 'false')
  }
  
  // Enhanced Browse Controls
  const toggleEnhancedBrowse = () => {
    useEnhancedBrowse.value = !useEnhancedBrowse.value
    localStorage.setItem('useEnhancedBrowse', String(useEnhancedBrowse.value))
  }
  
  const enableEnhancedBrowse = () => {
    useEnhancedBrowse.value = true
    localStorage.setItem('useEnhancedBrowse', 'true')
  }
  
  const disableEnhancedBrowse = () => {
    useEnhancedBrowse.value = false
    localStorage.setItem('useEnhancedBrowse', 'false')
  }
  
  // Watch for manual localStorage changes and sync the store
  const syncFromLocalStorage = () => {
    const savedHome = localStorage.getItem('useNewHomeDesign')
    if (savedHome !== null) {
      useNewHomeDesign.value = savedHome === 'true'
    }
    
    const savedOffer = localStorage.getItem('useSimpleOfferForm')
    useSimpleOfferForm.value = savedOffer === 'true' || savedOffer === null // Enable by default
    
    const savedBrowse = localStorage.getItem('useEnhancedBrowse')
    useEnhancedBrowse.value = savedBrowse === 'true' || savedBrowse === null // Enable by default
  }
  
  return {
    // Home Design
    useNewHomeDesign,
    toggleNewHomeDesign,
    enableNewHomeDesign,
    disableNewHomeDesign,
    // Simple Offer Form
    useSimpleOfferForm,
    toggleSimpleOfferForm,
    enableSimpleOfferForm,
    disableSimpleOfferForm,
    // Enhanced Browse
    useEnhancedBrowse,
    toggleEnhancedBrowse,
    enableEnhancedBrowse,
    disableEnhancedBrowse,
    // Utilities
    syncFromLocalStorage
  }
})

export { useUiPreferencesStore }
