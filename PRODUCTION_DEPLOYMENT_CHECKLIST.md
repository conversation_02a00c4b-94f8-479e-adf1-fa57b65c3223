# 🚀 Production Deployment Checklist - Debug Report System

## Overview
This checklist will safely deploy the new debug reporting feature to your CentOS 9 production environment without losing any existing data.

## ⚠️ IMPORTANT: Pre-Deployment Requirements

### 1. Environment Variables Required
Ensure your production `.env` file contains these variables:

```bash
# Required for debug reports
GEMINI_API_KEY=your_gemini_api_key_here
VITE_ADMIN_EMAILS=<EMAIL>,<EMAIL>
VITE_ENABLE_DEBUG_REPORT=true
CLIENT_LOG_DIRECTORY=/app/logs

# Your existing production variables
DATABASE_URL=postgresql://username:password@host:port/database
NODE_ENV=production
# ... other existing variables
```

## 🛡️ Step-by-Step Safe Deployment Process

### Phase 1: Database Migration (CRITICAL - Do this first!)

1. **Upload migration script to your CentOS 9 server:**
   ```bash
   # Copy migrate-production-safe.sh to your server
   scp migrate-production-safe.sh user@your-server:/path/to/munygo/
   ```

2. **SSH into your production server:**
   ```bash
   ssh user@your-production-server
   cd /path/to/munygo
   ```

3. **Make script executable:**
   ```bash
   chmod +x migrate-production-safe.sh
   ```

4. **Run the safe migration:**
   ```bash
   ./migrate-production-safe.sh
   ```
   
   This script will:
   - ✅ Create a timestamped backup of your database
   - ✅ Only add new debug report tables (won't touch existing data)
   - ✅ Check if tables already exist (safe to run multiple times)
   - ✅ Show clear success/failure messages

### Phase 2: Docker Deployment

1. **Upload deployment script:**
   ```bash
   scp deploy-production-centos9.sh user@your-server:/path/to/munygo/
   ```

2. **Make script executable:**
   ```bash
   chmod +x deploy-production-centos9.sh
   ```

3. **Run the deployment:**
   ```bash
   ./deploy-production-centos9.sh
   ```
   
   This script will:
   - ✅ Force rebuild Docker images (no cache)
   - ✅ Ensure all environment variables are passed correctly
   - ✅ Stop and restart containers safely
   - ✅ Run health checks

### Phase 3: Verification

1. **Check if debug button appears:**
   - Log in as an admin user (using email from `VITE_ADMIN_EMAILS`)
   - Look for the "Debug Report" button in the UI
   
2. **Test debug report creation:**
   - Click the debug report button
   - Fill out and submit a test report
   - Verify it appears in the admin panel

3. **Check logs:**
   ```bash
   docker-compose logs -f backend
   docker-compose logs -f frontend
   ```

## 🔧 Troubleshooting Common Issues

### Issue: Debug button not visible
**Solution:** Check browser cache and environment variables
```bash
# On your server, verify env vars are set correctly:
docker exec munygo-backend env | grep -E "(VITE_|GEMINI_|CLIENT_)"
docker exec munygo-frontend env | grep VITE_
```

### Issue: Database connection errors
**Solution:** Verify DATABASE_URL format and database accessibility
```bash
# Test database connection:
docker exec munygo-backend npx prisma db pull
```

### Issue: Gemini API errors
**Solution:** Verify API key is correctly set
```bash
# Check if Gemini key is available in backend:
docker exec munygo-backend env | grep GEMINI_API_KEY
```

## 🛟 Emergency Rollback Procedure

If something goes wrong, you can quickly rollback:

1. **Database Rollback:**
   ```bash
   # The migration script creates backups named: backup_YYYYMMDD_HHMMSS.sql
   # Find your backup:
   ls -la backup_*.sql
   
   # Restore (replace with your actual backup filename):
   psql $DATABASE_URL < backup_20241204_143022.sql
   ```

2. **Docker Rollback:**
   ```bash
   # Stop current containers:
   docker-compose down
   
   # Pull your previous working image or rebuild from last known good commit:
   git checkout previous-working-commit
   docker-compose up -d --build
   ```

## ✅ Success Criteria

Your deployment is successful when:
- [ ] Database migration completed without errors
- [ ] Docker containers are running and healthy
- [ ] Debug report button is visible to admin users
- [ ] Test debug report can be created and viewed
- [ ] All existing functionality still works
- [ ] No database data was lost

## 📞 Support

If you encounter issues:
1. Check the logs first: `docker-compose logs`
2. Verify environment variables are set correctly
3. Ensure database migration completed successfully
4. Test with a fresh browser session (clear cache)

---

**Remember:** The migration script is designed to be safe and can be run multiple times. It will only add new tables if they don't exist and will always create a backup first.
