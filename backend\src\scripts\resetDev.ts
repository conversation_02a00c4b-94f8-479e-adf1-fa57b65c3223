import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function runScript(command: string, description: string) {
  console.log(`\n🚀 ${description}...`);
  console.log(`Running: ${command}`);
  
  try {
    const { stdout, stderr } = await execAsync(command);
    console.log(stdout);
    if (stderr) {
      console.warn('Warnings:', stderr);
    }
    console.log(`✅ ${description} completed successfully`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error);
    throw error;
  }
}

async function main() {
  console.log('🔄 Starting complete development database reset...');
  console.log('⚠️  This will DELETE ALL DATA and recreate with test data!');
  
  // Wait 3 seconds to let user cancel if needed
  console.log('Starting in 3 seconds... (Ctrl+C to cancel)');
  await new Promise(resolve => setTimeout(resolve, 3000));

  try {
    // Step 1: Reset the database
    await runScript('npm run prisma:reset', 'Resetting database and running migrations');

    // Step 2: Generate Prisma client
    await runScript('npm run prisma:generate', 'Generating Prisma client');

    // Step 3: Seed all test data
    await runScript('npm run seed:all', 'Seeding all test data');

    console.log('\n🎉 Development database reset completed successfully!');
    console.log('\n🔗 Next steps:');
    console.log('  - Start the backend: npm run dev');
    console.log('  - View database: npm run prisma:studio');
    console.log('  - Login with test users (password: 11111111):');
    console.log('    • <EMAIL> (Elite user)');
    console.log('    • <EMAIL> (New user)'); 
    console.log('    • <EMAIL> (Reliable user)');
    
  } catch (error) {
    console.error('\n💥 Database reset failed:', error);
    console.log('\nTroubleshooting:');
    console.log('  - Ensure PostgreSQL is running');
    console.log('  - Check .env database connection');
    console.log('  - Verify Prisma schema is valid');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { main };
