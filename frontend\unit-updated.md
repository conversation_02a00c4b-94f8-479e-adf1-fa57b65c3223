# Comprehensive Unit Testing Guidelines for MUNygo Frontend

This document provides authoritative testing guidelines for the MUNygo Vue 3 + TypeScript + Pinia + Naive UI application using Vitest and Vue Test Utils. These guidelines have been validated through practical implementation and reflect the actual working patterns in the project infrastructure.

> **⚠️ IMPORTANT**: These guidelines are based on the existing test infrastructure in `src/test/setup.ts` and proven working patterns. Always use the established patterns described here rather than experimental approaches.

## Table of Contents
- [Core Principles](#core-principles)
- [Essential Setup](#essential-setup)
- [Component Testing](#component-testing)
- [Pinia Store Testing](#pinia-store-testing)
- [Service Testing](#service-testing)
- [Naive UI Testing](#naive-ui-testing)
- [Socket.IO Testing](#socketio-testing)
- [Advanced Patterns](#advanced-patterns)
- [Performance & Debugging](#performance--debugging)

## Core Principles

### Testing Philosophy
1. **Test User Behavior, Not Implementation**: Focus on what users can see and do
2. **Test in Isolation**: Each test should be independent and not rely on others
3. **Avoid Testing Third-Party Code**: Don't test Vue.js, Naive UI, or Pinia internals
4. **Test Edge Cases**: Empty states, error conditions, boundary values
5. **Maintainable Tests**: Write tests that survive refactoring

### What to Test ✅
- Component props and emitted events
- User interactions (clicks, form submissions)
- Conditional rendering based on state
- Store actions and computed properties
- API service calls and error handling
- Route navigation and guards

### What NOT to Test ❌
- Internal component methods (private implementation)
- CSS styling unless it affects functionality
- Third-party library internals
- Complex component library behaviors
- Implementation details that users can't observe

## Essential Setup

### Existing Test Infrastructure

**⚠️ CRITICAL**: The project already has a comprehensive test infrastructure in `src/test/setup.ts` that provides:

1. **Global Naive UI Component Stubs**: All Naive UI components are pre-stubbed with `data-testid` attributes
2. **Browser API Mocks**: matchMedia, ResizeObserver, localStorage are already mocked
3. **Global Plugin Configuration**: Includes necessary plugins and configurations

**DO NOT** create custom component stubs or mock configurations. Use the existing infrastructure.

### Working Test Pattern Structure
```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, type VueWrapper } from '@vue/test-utils'
import type { ComponentPublicInstance } from 'vue'
import MyComponent from '@/components/MyComponent.vue'

// Mock stores at module level (DO NOT use createTestingPinia)
vi.mock('@/stores/myStore', () => ({
  useMyStore: vi.fn(() => ({
    someProperty: true,
    someMethod: vi.fn()
  }))
}))

// Mock Vue I18n at module level
const mockT = vi.fn((key: string) => key)
vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: mockT,
    locale: { value: 'en' }
  })
}))

// Mock icon components if needed
vi.mock('@vicons/ionicons5', () => ({
  SunnySharp: { template: '<div>sun-icon</div>' },
  MoonSharp: { template: '<div>moon-icon</div>' }
}))

describe('MyComponent', () => {
  let wrapper: VueWrapper<ComponentPublicInstance>
  
  const createComponent = (props = {}) => {
    return mount(MyComponent, {…})
  }

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()
  })

  afterEach(() => {
    wrapper?.unmount()
  })
  
  it('should render without errors', () => {
    wrapper = createComponent()
    expect(wrapper.exists()).toBe(true)
  })
})
```

### Key Differences from Standard Patterns

1. **Module-Level Store Mocking**: Use `vi.mock('@/stores/storeName')` instead of `createTestingPinia`
2. **Global Stubs**: Use existing stubs from `setup.ts` instead of custom stubs
3. **No Custom Plugin Configuration**: Global plugins are already configured
4. **Data TestID Usage**: All stubs provide `data-testid="component-name"` for reliable element selection
5. **Named Slot Support**: Component stubs support essential named slots like `#icon` for consistent rendering

### Browser API Mocks (Already Available)

These are already mocked globally in `setup.ts`:
- `window.matchMedia`
- `ResizeObserver`
- `localStorage`
- Naive UI composables (`useMessage`, `useDialog`, etc.)

### Component Stub Reference

All Naive UI components are pre-stubbed with simple templates and `data-testid` attributes:
- `NButton` → `<button data-testid="nbutton"><slot name="icon" /><slot /></button>`
- `NIcon` → `<i data-testid="nicon"><slot /></i>`
- `NCard` → `<div data-testid="ncard"><slot /></div>`
- And many more...

### Practical Testing Example

Here's a real working example from the ThemeToggle component test:

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, type VueWrapper } from '@vue/test-utils'
import type { ComponentPublicInstance } from 'vue'
import ThemeToggle from '@/components/ThemeToggle.vue'

// Mock the theme store
const mockThemeStore = {
  isDark: false,
  toggleTheme: vi.fn()
}

vi.mock('@/stores/theme', () => ({
  useThemeStore: vi.fn(() => mockThemeStore)
}))

// Mock Vue I18n
const mockT = vi.fn((key: string) => key)
vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: mockT,
    locale: { value: 'en' }
  })
}))

// Mock icon components
vi.mock('@vicons/ionicons5', () => ({
  SunnySharp: { template: '<div>sun-icon</div>' },
  MoonSharp: { template: '<div>moon-icon</div>' }
}))

describe('ThemeToggle', () => {
  let wrapper: VueWrapper<ComponentPublicInstance>
  
  const createComponent = () => {
    return mount(ThemeToggle)
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockThemeStore.isDark = false
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('should render NButton component', () => {
    wrapper = createComponent()
    expect(button.exists()).toBe(true)
  })

  it('should call toggleTheme when button is clicked', async () => {
    wrapper = createComponent()
    expect(mockThemeStore.toggleTheme).toHaveBeenCalledOnce()
  })

  it('should show correct title for dark mode', () => {
    mockT.mockImplementation((key: string) => {
    expect(button.attributes('title')).toBe('Switch to light mode')
  })
})
```

### Critical Lessons Learned

1. **Reactivity with Mocks**: When testing store reactivity, re-mount the component instead of relying on Vue's reactivity with mocked stores
2. **Named Slot Support**: Most stubs (especially NButton) have been updated to support named slots like `#icon` which is crucial for testing components that use them
3. **Translation Setup**: Mock `useI18n` consistently and provide realistic return values
4. **Element Selection**: Always use `data-testid` attributes for reliable element selection
5. **Mock Reset**: Always reset mocks in `beforeEach` to ensure test isolation

## Common Issues and Troubleshooting

### Empty Component Text Content

If your component renders but `wrapper.text()` is empty when it should contain text:

1. **Check Named Slots**: Ensure the component isn't using named slots that aren't supported in the stub
2. **Examine HTML Output**: Use `console.log(wrapper.html())` to debug the actual rendered structure
3. **Verify Icon Slot Rendering**: For buttons with icon text, make sure the NButton stub includes `<slot name="icon" />`

### Reactive State Changes Not Reflected

When testing components that respond to reactive state:

1. **Re-mount the Component**: Create a new instance after changing mock state values
2. **Use nextTick**: Wait for Vue's reactivity to update with `await wrapper.vm.$nextTick()`
3. **Set Mocked Values Before Mounting**: Initialize mock state before creating the component wrapper

### Test-Specific Stubs

If you need to extend or override the global stubs for a specific test:

```typescript
const createComponent = (props = {}) => {
  return mount(MyComponent, {
    props,
    global: {
      stubs: {
        // Override specific stubs
        NButton: {
          template: `<button data-testid="custom-button"><slot name="icon" /><slot /></button>`,
          props: ['loading', 'disabled', 'size', 'type', 'htmlType', 'title'],
          emits: ['click']
        }
      }
    }
  })
}
```

### Testing Slots and Custom Content

When testing components that provide slots to their children:

```typescript
const createComponent = () => {
  return mount(ParentComponent, {
    slots: {
      default: '<div class="custom-content">Slot content</div>',
      header: '<h2>Custom Header</h2>'
    }
  })
}

it('renders slot content', () => {
  wrapper = createComponent()
  expect(wrapper.find('.custom-content').exists()).toBe(true)
  expect(wrapper.find('h2').exists()).toBe(true)
})
```
