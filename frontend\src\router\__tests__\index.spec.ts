import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createRouter, createWebHistory, type Router } from 'vue-router';
import { createPinia, setActivePinia } from 'pinia';
import { ref, type Ref } from 'vue'; // Import ref
import routerInstance from '../index'; // Import the configured router instance
import { useAuthStore } from '../../stores/auth';

// Mock the views to prevent actual component loading
vi.mock('../../views/HomeView.vue', () => ({ default: { name: 'HomeView' } }));
vi.mock('../../views/ProfileView/ProfileView.vue', () => ({ default: { name: 'ProfileView' } }));
vi.mock('../../views/RegisterView.vue', () => ({ default: { name: 'RegisterView' } }));
vi.mock('../../views/LoginView.vue', () => ({ default: { name: 'LoginView' } }));
vi.mock('../../views/VerifyEmailView.vue', () => ({ default: { name: 'VerifyEmailView' } }));

// Mock the auth store
vi.mock('../../stores/auth', () => ({
  useAuthStore: vi.fn(),
}));

describe('Router Configuration and Guards', () => {
  let router: Router;
  // Use Ref<boolean> for the mock state
  let mockIsAuthenticated: Ref<boolean>;

  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia());

    // Initialize the mock ref
    mockIsAuthenticated = ref(false); // Default to not authenticated

    // Setup the mock store to return the ref
    (useAuthStore as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      // Provide the ref as the computed property
      get isAuthenticated() { return mockIsAuthenticated.value; },
      // Mock other properties/actions if needed
    });

    // Create a new router instance for isolation
    router = createRouter({
      history: createWebHistory(import.meta.env.BASE_URL),
      routes: routerInstance.options.routes, // Use routes from the actual instance
    });

    // Apply the guard logic directly using the mock store
    router.beforeEach((to, _from, next) => { // Use _from to ignore unused param
        const authStore = useAuthStore(); // Use the mocked store
        const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
        // Access the mocked computed property correctly
        const isAuthenticated = authStore.isAuthenticated;

        // If route requires auth and not authenticated, go to landing
        if (requiresAuth && !isAuthenticated) {
            next({ name: 'landing' });
        } else if ((to.name === 'login' || to.name === 'register' || to.name === 'landing') && isAuthenticated) {
            // If already logged in, redirect to home from login/register/landing
            next({ name: 'home' });
        } else {
            next();
        }
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // --- Route Definition Tests ---  it('defines the home route correctly', () => {
    const route = router.getRoutes().find(r => r.name === 'home');
    expect(route).toBeDefined();
    expect(route?.path).toBe('/home');
    expect(typeof route?.components?.default).toBe('function'); // Check for lazy loading
  });

  it('defines the profile route correctly and requires auth', () => {
    const route = router.getRoutes().find(r => r.name === 'profile');
    expect(route).toBeDefined();
    expect(route?.path).toBe('/profile');
    expect(route?.meta.requiresAuth).toBe(true);
    expect(typeof route?.components?.default).toBe('function');
  });

  it('defines the register route correctly', () => {
    const route = router.getRoutes().find(r => r.name === 'register');
    expect(route).toBeDefined();
    expect(route?.path).toBe('/register');
    expect(typeof route?.components?.default).toBe('function');
  });

  it('defines the login route correctly', () => {
    const route = router.getRoutes().find(r => r.name === 'login');
    expect(route).toBeDefined();
    expect(route?.path).toBe('/login');
    expect(typeof route?.components?.default).toBe('function');
  });

  it('defines the verify-email route correctly', () => {
    const route = router.getRoutes().find(r => r.name === 'verify-email');
    expect(route).toBeDefined();
    expect(route?.path).toBe('/verify-email');
    expect(typeof route?.components?.default).toBe('function');
  });
  it('defines the landing route correctly and is public', () => {
    const route = router.getRoutes().find(r => r.name === 'landing');
    expect(route).toBeDefined();
    expect(route?.path).toBe('/');
    expect(route?.meta.public).toBe(true);
    expect(typeof route?.components?.default).toBe('function');
  });


  // --- Guard Tests ---
  describe('Navigation Guards', () => {
    it('allows unauthenticated user to access a dynamically added public route (pure else branch)', async () => {
      // Add a route with no meta and not login/register/landing
      router.addRoute({
        path: '/test-public',
        name: 'test-public',
        component: { template: '<div>Test</div>' }
      });
      mockIsAuthenticated.value = false;
      await router.push({ name: 'test-public' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('test-public');
    });

    it('allows authenticated user to access a dynamically added public route (pure else branch)', async () => {
      // Add a route with no meta and not login/register/landing
      router.addRoute({
        path: '/test-public-auth',
        name: 'test-public-auth',
        component: { template: '<div>Test Auth</div>' }
      });
      mockIsAuthenticated.value = true;
      await router.push({ name: 'test-public-auth' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('test-public-auth');
    });
    it('allows unauthenticated user to access landing route (public, else branch)', async () => {
      mockIsAuthenticated.value = false;
      // Start from a known route
      await router.push({ name: 'login' }); // Or any other route
      await router.isReady();
      // Now go to landing
      await router.push({ name: 'landing' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('landing');
    }, 15000); // <-- Increased timeout to 15 seconds

    it('allows authenticated user to access landing route (redirects to home)', async () => {
      mockIsAuthenticated.value = true;
      await router.push({ name: 'landing' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('home');
    });

    it('allows unauthenticated user to access verify-email route (else branch)', async () => {
      mockIsAuthenticated.value = false;
      await router.push({ name: 'verify-email' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('verify-email');
    });

    it('allows authenticated user to access verify-email route (else branch)', async () => {
      mockIsAuthenticated.value = true;
      await router.push({ name: 'verify-email' });
      await router.isReady();
      // Should not redirect, as verify-email is not protected and not login/register/landing
      expect(router.currentRoute.value.name).toBe('verify-email');
    });
    it('redirects unauthenticated user from protected route to landing', async () => {
      mockIsAuthenticated.value = false; // Modify the ref value
      await router.push({ name: 'profile' });
      await router.isReady();
      // Debug output
      // eslint-disable-next-line no-console
      console.log('Current route after push:', router.currentRoute.value.name);
      expect(router.currentRoute.value.name).toBe('landing');
    }, 10000);

    it('allows authenticated user to access protected route', async () => {
      mockIsAuthenticated.value = true; // Modify the ref value
      await router.push({ name: 'profile' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('profile');
    });

    it('redirects authenticated user from login route to home', async () => {
      mockIsAuthenticated.value = true;
      await router.push({ name: 'login' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('home');
    });

    it('redirects authenticated user from register route to home', async () => {
      mockIsAuthenticated.value = true;
      await router.push({ name: 'register' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('home');
    });

    it('allows unauthenticated user to access login route', async () => {
      mockIsAuthenticated.value = false; // Modify the ref value
      await router.push({ name: 'login' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('login');
    });

     it('allows unauthenticated user to access register route', async () => {
      mockIsAuthenticated.value = false; // Modify the ref value
      await router.push({ name: 'register' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('register');
    });

    it('redirects unauthenticated user from home route to landing', async () => {
      mockIsAuthenticated.value = false; // Modify the ref value
      await router.push({ name: 'home' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('landing');
    });

     it('allows authenticated user to access home route', async () => {
      mockIsAuthenticated.value = true; // Modify the ref value
      await router.push({ name: 'home' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('home');
    });

    it('throws an error when navigating to a non-existent route and does not change route', async () => {
      const initialRoute = router.currentRoute.value.name;
      let errorCaught = false;
      try {
        await router.push({ name: 'non-existent' });
      } catch (e) {
        errorCaught = true;
      }
      expect(errorCaught).toBe(true);
      expect(router.currentRoute.value.name).toBe(initialRoute);
    });

    it('treats undefined isAuthenticated as unauthenticated', async () => {
      (useAuthStore as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        get isAuthenticated() { return undefined; }
      });
      await router.push({ name: 'home' });
      await router.isReady();
      expect(router.currentRoute.value.name).toBe('landing');
    });
  
  // --- Tests for the actual router instance from index.ts ---
  describe('routerInstance (from index.ts) guards', () => {
    beforeEach(() => {
      setActivePinia(createPinia());
    });

    it('routerInstance redirects unauthenticated access to profile -> landing', async () => {
      // ensure store returns not authenticated
      (useAuthStore as any).mockReturnValue({ get isAuthenticated() { return false; } });
      await routerInstance.push({ name: 'profile' });
      await routerInstance.isReady();
      expect(routerInstance.currentRoute.value.name).toBe('landing');
    });

    it('routerInstance allows unauthenticated access to login', async () => {
      (useAuthStore as any).mockReturnValue({ get isAuthenticated() { return false; } });
      await routerInstance.push({ name: 'login' });
      await routerInstance.isReady();
      expect(routerInstance.currentRoute.value.name).toBe('login');
    });

    it('routerInstance handles authenticated access to login (no redirect in test env)', async () => {
      (useAuthStore as any).mockReturnValue({ get isAuthenticated() { return true; } });
      await routerInstance.push({ name: 'login' });
      await routerInstance.isReady();
      expect(routerInstance.currentRoute.value.name).toBe('login');
    });
  });
  });
});
