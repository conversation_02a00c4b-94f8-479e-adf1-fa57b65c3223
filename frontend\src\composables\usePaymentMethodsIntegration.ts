import { ref, computed } from 'vue';
import { usePaymentMethodsStore } from '@/stores/paymentMethodsStore';
import type { 
  PaymentMethodWithValidation, 
  CreatePaymentMethodPayload,
  PaymentMethodSelectionForTransaction 
} from '@/types/paymentMethods';

export interface PaymentDetailsFormState {
  isVisible: boolean;
  currency: string;
  transactionId: string;
  mode: 'select' | 'create' | 'edit';
  selectedMethod?: PaymentMethodWithValidation;
  availableMethods: PaymentMethodWithValidation[];
}

export function usePaymentMethodsIntegration() {
  const paymentMethodsStore = usePaymentMethodsStore();
  
  // Form state
  const formState = ref<PaymentDetailsFormState>({
    isVisible: false,
    currency: '',
    transactionId: '',
    mode: 'select',
    availableMethods: []
  });

  const isLoading = computed(() => paymentMethodsStore.isLoading);
  const hasExistingMethods = computed(() => formState.value.availableMethods.length > 0);

  /**
   * Open payment details form for a transaction
   */
  const openPaymentDetailsForm = async (
    transactionId: string, 
    currency: string
  ): Promise<void> => {
    console.log('🔍 [PaymentIntegration] Opening payment details form for:', { transactionId, currency });
    
    // Reset form state to avoid stale data
    formState.value = {
      isVisible: true,
      currency,
      transactionId,
      mode: 'select',
      availableMethods: []
    };

    try {
      // Load existing payment methods for the currency
      console.log('🔍 [PaymentIntegration] Fetching methods for currency:', currency);
      await paymentMethodsStore.fetchMethodsByCurrency(currency);
      
      console.log('🔍 [PaymentIntegration] All payment methods in store:', paymentMethodsStore.paymentMethods);
      console.log('🔍 [PaymentIntegration] Methods by currency for', currency, ':', paymentMethodsStore.getMethodsByCurrency(currency));
      
      const methods = paymentMethodsStore.getMethodsForTransaction(currency);
      console.log('🔍 [PaymentIntegration] Methods for transaction (currency:', currency, '):', methods);
      
      formState.value.availableMethods = methods;
      
      // If no methods exist, go directly to create mode
      if (methods.length === 0) {
        console.log('🔍 [PaymentIntegration] No methods found for', currency, ', switching to create mode');
        formState.value.mode = 'create';
      } else {
        console.log('🔍 [PaymentIntegration] Found', methods.length, 'methods for', currency, ', staying in select mode');
      }
    } catch (error) {
      console.error('🔴 [PaymentIntegration] Failed to load payment methods for', currency, ':', error);
      // Still show the form in create mode
      formState.value.mode = 'create';
    }
  };

  /**
   * Close the payment details form
   */
  const closePaymentDetailsForm = (): void => {
    formState.value.isVisible = false;
    formState.value.selectedMethod = undefined;
  };

  /**
   * Switch to create new method mode
   */
  const switchToCreateMode = (): void => {
    formState.value.mode = 'create';
    formState.value.selectedMethod = undefined;
  };

  /**
   * Switch to edit existing method mode
   */
  const switchToEditMode = (method: PaymentMethodWithValidation): void => {
    formState.value.mode = 'edit';
    formState.value.selectedMethod = method;
  };

  /**
   * Switch back to selection mode
   */
  const switchToSelectMode = (): void => {
    formState.value.mode = 'select';
    formState.value.selectedMethod = undefined;
  };

  /**
   * Select an existing payment method for the transaction
   */
  const selectExistingMethod = async (method: PaymentMethodWithValidation): Promise<PaymentMethodSelectionForTransaction> => {
    return {
      methodId: method.id,
      currency: method.currency,
      useExisting: true,
      method
    };
  };

  /**
   * Create a new payment method and use it for the transaction
   */
  const createAndSelectMethod = async (
    data: CreatePaymentMethodPayload
  ): Promise<PaymentMethodSelectionForTransaction> => {
    try {
      const newMethod = await paymentMethodsStore.createMethod(data);
      
      // Refresh the available methods list
      const methods = paymentMethodsStore.getMethodsForTransaction(data.currency);
      formState.value.availableMethods = methods;
      
      return {
        methodId: newMethod.id,
        currency: newMethod.currency,
        useExisting: true,
        method: newMethod,
        newMethodData: data
      };
    } catch (error) {
      console.error('Failed to create payment method:', error);
      throw error;
    }
  };

  /**
   * Update an existing payment method
   */
  const updateMethod = async (
    methodId: string,
    data: Partial<CreatePaymentMethodPayload>
  ): Promise<PaymentMethodWithValidation> => {
    try {
      const updatedMethod = await paymentMethodsStore.updateMethod(methodId, data);
      
      // Refresh the available methods list
      const methods = paymentMethodsStore.getMethodsForTransaction(formState.value.currency);
      formState.value.availableMethods = methods;
      
      return updatedMethod;
    } catch (error) {
      console.error('Failed to update payment method:', error);
      throw error;
    }
  };

  /**
   * Delete a payment method
   */
  const deleteMethod = async (methodId: string): Promise<void> => {
    try {
      await paymentMethodsStore.deactivateMethod(methodId);
      
      // Refresh the available methods list
      const methods = paymentMethodsStore.getMethodsForTransaction(formState.value.currency);
      formState.value.availableMethods = methods;
    } catch (error) {
      console.error('Failed to delete payment method:', error);
      throw error;
    }
  };

  /**
   * Get the default payment method for a currency
   */
  const getDefaultMethod = async (currency: string): Promise<PaymentMethodWithValidation | null> => {
    try {
      return await paymentMethodsStore.getDefaultMethod(currency);
    } catch (error) {
      console.error('Failed to get default payment method:', error);
      return null;
    }
  };

  /**
   * Quick setup: use default method or create new one
   */
  const quickSetupPaymentMethod = async (
    currency: string
  ): Promise<PaymentMethodSelectionForTransaction | null> => {
    try {
      console.log('🔍 [PaymentIntegration] Quick setup for currency:', currency);
      
      // Try to get default method first
      const defaultMethod = await getDefaultMethod(currency);
      console.log('🔍 [PaymentIntegration] Default method found:', defaultMethod);
      
      if (defaultMethod && defaultMethod.validationStatus === 'complete') {
        console.log('🔍 [PaymentIntegration] Using complete default method:', defaultMethod.id);
        return {
          methodId: defaultMethod.id,
          currency: defaultMethod.currency,
          useExisting: true,
          method: defaultMethod
        };
      }
      
      console.log('🔍 [PaymentIntegration] No suitable default method found');
      // No suitable default method found
      return null;
    } catch (error) {
      console.error('Failed to quick setup payment method:', error);
      return null;
    }
  };

  /**
   * Validate payment method for transaction
   */
  const validateMethodForTransaction = (method: PaymentMethodWithValidation): boolean => {
    return method.isActive && method.validationStatus === 'complete';
  };

  /**
   * Get payment method summary for display
   */
  const getMethodSummary = (method: PaymentMethodWithValidation): string => {
    const maskedAccount = method.accountNumber.length > 4 
      ? '*'.repeat(method.accountNumber.length - 4) + method.accountNumber.slice(-4)
      : method.accountNumber;
    
    return `${method.bankName} - ${maskedAccount}`;
  };

  return {
    // State
    formState,
    isLoading,
    hasExistingMethods,
    
    // Actions
    openPaymentDetailsForm,
    closePaymentDetailsForm,
    switchToCreateMode,
    switchToEditMode,
    switchToSelectMode,
    selectExistingMethod,
    createAndSelectMethod,
    updateMethod,
    deleteMethod,
    getDefaultMethod,
    quickSetupPaymentMethod,
    validateMethodForTransaction,
    getMethodSummary
  };
}
