#!/bin/bash

# Update Production Database Schema from schema.prisma
# Run this script on the CentOS 9 host machine where Docker containers are running

echo "🚀 Updating production database schema from schema.prisma..."

# Check if Docker containers are running
echo "🔍 Checking if production containers are running..."
if ! docker-compose ps | grep -q "backend.*Up"; then
    echo "❌ Backend container is not running"
    echo "💡 Start production environment first with: docker-compose up -d"
    exit 1
fi

echo "✅ Backend container is running"

# Copy the latest schema.prisma to the container
echo "📋 Copying latest schema.prisma to production container..."
docker cp ./backend/prisma/schema.prisma $(docker-compose ps -q backend):/app/prisma/schema.prisma

if [ $? -ne 0 ]; then
    echo "❌ Failed to copy schema.prisma to container"
    exit 1
fi

echo "✅ Schema file copied successfully"

# Push schema changes directly to database (bypasses migrations)
echo "🔄 Applying schema changes to production database..."
echo "⏳ This will update the database structure to match schema.prisma..."

docker-compose exec backend npx prisma db push --accept-data-loss

if [ $? -ne 0 ]; then
    echo "❌ Failed to apply schema changes"
    echo "💡 Check the output above for error details"
    exit 1
fi

echo "✅ Schema changes applied successfully!"

# Generate Prisma client to match new schema
echo "🔄 Regenerating Prisma client..."
docker-compose exec backend npx prisma generate

if [ $? -ne 0 ]; then
    echo "⚠️  Warning: Failed to regenerate Prisma client"
else
    echo "✅ Prisma client regenerated successfully"
fi

# Test the updated database
echo "🧪 Testing updated database schema..."
docker-compose exec backend node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testUpdatedSchema() {
    try {
        console.log('Testing debug_reports table...');
        
        // Test that we can query the table with new columns
        const result = await prisma.debugReport.findMany({
            take: 1,
            select: {
                id: true,
                priority: true,
                title: true,
                severity: true,
                status: true,
                createdAt: true
            }
        });
        
        console.log('✅ debug_reports table updated successfully');
        console.log('Sample query result:', result.length, 'records found');
        
        // Check table schema
        const schema = await prisma.\$queryRaw\`
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'debug_reports' 
            AND column_name IN ('priority', 'assigned_at', 'session_id', 'viewport_width', 'viewport_height')
            ORDER BY column_name;
        \`;
        
        console.log('✅ New columns verified:');
        console.table(schema);
        
        await prisma.\$disconnect();
        console.log('✅ Database schema update completed successfully!');
        
    } catch (error) {
        console.error('❌ Database test failed:', error.message);
        process.exit(1);
    }
}

testUpdatedSchema();
"

echo ""
echo "🎉 Production database schema update completed!"
echo ""
echo "📊 Summary:"
echo "✅ Schema.prisma copied to container"
echo "✅ Database structure updated with prisma db push"
echo "✅ Prisma client regenerated"
echo "✅ Database schema tested and verified"
echo ""
echo "🔗 Next Steps:"
echo "1. Test the Debug Dashboard at your domain/admin/debug-dashboard"
echo "2. Verify that admin users can view debug reports"
echo "3. Test debug report submission and display"
echo ""
echo "⚠️  Note: If you encounter issues, check container logs:"
echo "   docker-compose logs backend"
