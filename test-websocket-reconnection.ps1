# WebSocket Reconnection Test Script
# This script tests the WebSocket reconnection functionality after authentication

Write-Host "🧪 WebSocket Reconnection Test Script" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

# Check if servers are running
Write-Host "`n1. Checking if servers are running..." -ForegroundColor Yellow

try {
    $backendResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/health" -Method GET -TimeoutSec 5
    Write-Host "✅ Backend server is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend server is not running. Please start it with 'cd backend && npm run dev'" -ForegroundColor Red
    exit 1
}

try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:5173" -Method GET -TimeoutSec 5
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend server is running" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Frontend server is not running. Please start it with 'cd frontend && npm run dev'" -ForegroundColor Red
    exit 1
}

Write-Host "`n2. Testing authentication and WebSocket connection..." -ForegroundColor Yellow

# Test login endpoint
$loginData = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "✅ Login successful" -ForegroundColor Green
    $token = $loginResponse.token
    Write-Host "   Token: $($token.Substring(0, 20))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Login failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Make sure you have a test user with email '<EMAIL>' and password 'password123'" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n3. Testing WebSocket connection with valid token..." -ForegroundColor Yellow

# Test WebSocket connection by checking if we can access protected endpoints
$headers = @{
    "Authorization" = "Bearer $token"
}

try {
    $userResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/auth/me" -Method GET -Headers $headers
    Write-Host "✅ Authenticated API call successful" -ForegroundColor Green
    Write-Host "   User: $($userResponse.email)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Authenticated API call failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n4. Testing token expiration simulation..." -ForegroundColor Yellow

# Test with invalid token to simulate expired token
$invalidHeaders = @{
    "Authorization" = "Bearer invalid-token-12345"
}

try {
    $invalidResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/auth/me" -Method GET -Headers $invalidHeaders
    Write-Host "❌ Invalid token was accepted (this shouldn't happen)" -ForegroundColor Red
} catch {
    if ($_.Exception.Response -and $_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ Invalid token correctly rejected with 401" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Unexpected error: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

Write-Host "`n5. Manual Testing Instructions:" -ForegroundColor Yellow
Write-Host "==============================" -ForegroundColor Yellow
Write-Host "To fully test the WebSocket reconnection fixes:" -ForegroundColor White
Write-Host ""
Write-Host "1. Open http://localhost:5173 in your browser" -ForegroundColor Cyan
Write-Host "2. Login with any valid credentials" -ForegroundColor Cyan
Write-Host "3. Check the connection indicator in the top navigation bar" -ForegroundColor Cyan
Write-Host "4. Open browser DevTools and check the console for WebSocket logs" -ForegroundColor Cyan
Write-Host "5. Look for these log messages:" -ForegroundColor Cyan
Write-Host "   - '🚀 [CentralizedSocketManager] initializeSocket called'" -ForegroundColor Gray
Write-Host "   - '✅ [CentralizedSocketManager] Connected with ID: ...'" -ForegroundColor Gray
Write-Host "   - '[AuthStore] Socket connection initialized successfully'" -ForegroundColor Gray
Write-Host ""
Write-Host "6. To test reconnection after token expiration:" -ForegroundColor Cyan
Write-Host "   - Wait for token to expire (1 hour) OR" -ForegroundColor Gray
Write-Host "   - Manually clear localStorage and refresh the page" -ForegroundColor Gray
Write-Host "   - Login again and verify WebSocket reconnects automatically" -ForegroundColor Gray
Write-Host ""
Write-Host "7. Expected behavior after fixes:" -ForegroundColor Cyan
Write-Host "   - WebSocket should connect automatically after login" -ForegroundColor Green
Write-Host "   - Connection indicator should show 'Connected'" -ForegroundColor Green
Write-Host "   - No manual reconnection should be required" -ForegroundColor Green
Write-Host "   - Real-time features should work immediately" -ForegroundColor Green

Write-Host "`n✅ Basic connectivity tests completed!" -ForegroundColor Green
Write-Host "Please follow the manual testing instructions above to verify the WebSocket reconnection fixes." -ForegroundColor White
