{"reportIssue": "Report Issue", "reportType": "Report Type", "selectReportType": "Select report type", "orSelectFromCards": "Or choose from the options below", "severityText": "Severity", "selectSeverity": "Select severity level", "tagsText": "Tags", "tagsDescription": "Add custom tags to help categorize this report", "title": "Title", "titlePlaceholder": "Brief summary of the issue", "description": "This system collects comprehensive diagnostic information including logs, user actions, and page content for analysis and app improvement.", "descriptionPlaceholder": "Detailed description of what happened", "stepsToReproduce": "Steps to Reproduce", "stepsPlaceholder": "1. Click on...\n2. Navigate to...\n3. Expected result...", "expectedBehavior": "Expected Behavior", "expectedPlaceholder": "What you expected to happen", "actualBehavior": "Actual Behavior", "actualPlaceholder": "What actually happened", "additionalNotes": "Additional Notes", "additionalNotesPlaceholder": "Any additional context or information", "contextInfo": "Context Information", "currentPage": "Current Page", "logEntries": "Log Entries", "userActions": "User Actions", "viewport": "Viewport", "recentActions": "Recent Actions", "sendReport": "Send Report", "formValidationError": "Please fill in the required fields", "reportSentSuccess": "Report sent successfully! Report ID: {reportId}", "reportSentError": "Failed to send report. Please try again.", "tags": {"selectTags": "Select Tags", "selectTagsDescription": "Choose tags that best describe your issue to help us categorize and resolve it faster", "loading": "Loading tags...", "search": "Search tags...", "searchPlaceholder": "Search for tags...", "selectedTags": "Selected Tags", "recommended": "Recommended", "browseAllTags": "Browse All Tags", "customTags": "Custom Tags", "addCustomTag": "Add your own tag...", "customTagHelp": "Add custom tags to better describe your specific issue", "aiSuggestions": "AI Suggestions", "autoSuggested": "Auto-suggested", "aiNotAvailable": "AI suggestions not available", "confidence": "Confidence", "selected": "Selected", "manualSelection": "Manual Tag Selection", "manualSelectionDesc": "Choose tags that best describe your issue from the categories below", "customHelp": "Add custom tags to better describe your issue", "clickHint": "Tip: Click on the colored tags above to quickly add them to your report", "clickToAdd": "Click to add this tag to your report", "clickToRemove": "Click to remove this tag from your report", "label": "Tags", "placeholder": "Add custom tags", "addCustom": "Add Custom Tag", "predefined": "Predefined", "predefinedTags": "Available Tags", "selectedCount": "{count} selected", "loadError": "Failed to load tags", "maxTagsReached": "Maximum of {max} tags allowed", "customTagAdded": "Custom tag '{name}' added", "customTagAddError": "Failed to add custom tag", "customTagRemoved": "Tag '{name}' removed", "allTagsCleared": "All tags cleared", "customTagsCount": "{current} of {max} custom tags", "approachingLimit": "Approaching tag limit: {current}/{max}", "add": "Add", "total": "Total", "custom": "Custom", "categories": {"general": "General", "technical": "Technical", "uiux": "UI/UX", "performance": "Performance", "uncategorized": "Uncategorized"}}, "titleText": "Enhanced Debug Report Title", "subtitle": "Submit comprehensive diagnostic report to improve the application", "reportTypes": {"bug": "Bug Report", "bugDescription": "Something is broken or not working as expected", "feature": "Feature Request", "featureDescription": "Suggest a new feature or enhancement", "performance": "Performance Issue", "performanceDescription": "App is slow or unresponsive", "uiux": "UI/UX Issue", "uiuxDescription": "Interface design or usability problem", "improvement": "General Improvement", "improvementDescription": "Suggestion for making something better", "question": "Question/Help", "questionDescription": "Need help or clarification", "other": "Other", "otherDescription": "Something else not covered above", "idea": "Idea/Suggestion", "ui": "User Interface", "help": "Help"}, "reportTypeDescriptions": {"bug": "Something is broken or not working as expected", "bugDescription": "Something is broken or not working as expected", "idea": "Suggestion for improvement or new feature", "ui": "Issue with appearance, layout or user experience", "uiux": "Issue with appearance, layout or user experience", "uiuxDescription": "Issue with appearance, layout or user experience", "performance": "Page is slow or has poor performance", "performanceDescription": "Page is slow or has poor performance", "feature": "Request for new feature or capability", "featureDescription": "Request for new feature or capability", "improvement": "Suggestion for improving performance or experience", "improvementDescription": "Suggestion for improving performance or experience", "question": "Confusion or need for guidance", "questionDescription": "Confusion or need for guidance", "other": "Other items not covered above", "otherDescription": "Other items not covered above", "help": "Confusion or need for guidance", "helpDescription": "Confusion or need for guidance"}, "severity": {"low": "Low", "medium": "Medium", "high": "High", "critical": "Critical"}, "form": {"title": "Title", "titlePlaceholder": "Brief summary of the issue or suggestion", "description": "Description", "descriptionPlaceholder": "Complete description of what happened or your suggestion", "stepsToReproduce": "Steps to Reproduce", "stepsToReproducePlaceholder": "Exact steps to reproduce this issue", "expectedBehavior": "Expected Behavior", "expectedBehaviorPlaceholder": "What you expected to happen", "actualBehavior": "Actual Behavior", "actualBehaviorPlaceholder": "What actually happened", "additionalNotes": "Additional Notes", "additionalNotesPlaceholder": "Any additional information that might be helpful"}, "context": {"title": "Environment Information", "totalLogs": "Total Logs", "userActions": "User Actions", "currentPage": "Current Page", "viewport": "Viewport Dimensions", "browser": "Browser", "timestamp": "Timestamp", "sessionId": "Session ID", "route": "Route", "recentActions": "Recent Actions", "correlatedData": "Correlated Data", "showDetails": "Show Details", "hideDetails": "Hide Details"}, "actions": {"cancel": "Cancel", "send": "Send Report", "sending": "Sending..."}, "messages": {"success": "Your report has been sent successfully! Thank you for helping us improve the application.", "error": "Error sending report. Please try again.", "noLogsWarning": "No logs available to send.", "fillRequired": "Please fill in the required fields.", "titleRequired": "Title is required", "descriptionRequired": "Description is required"}, "help": {"reportTypeHelp": "Choose the appropriate report type so our team can better assist you.", "severityHelp": "Severity level helps us prioritize correctly.", "tagsHelp": "Tags help with categorization and faster searching of reports.", "contextHelp": "This information is collected automatically and helps us better understand the issue."}, "resetForm": "Reset Form", "resetFormConfirm": "Are you sure you want to clear all form data? This action cannot be undone.", "resetFormSuccess": "Form has been reset successfully.", "offlineReportStored": "You're offline. Report saved locally and will be submitted when connection is restored.", "offlineReportsSubmitted": "{count} offline report(s) submitted successfully.", "offlineReportsFailed": "{count} offline report(s) failed to submit after multiple attempts.", "draftSaved": "Draft saved automatically.", "draftSavedManually": "Draft saved successfully.", "draftSaveError": "Failed to save draft. Your changes may be lost.", "storageQuotaExceeded": "Storage space full. Unable to save draft. Please clear browser data.", "storageSecurityError": "Cannot save draft due to browser security restrictions.", "draftLoaded": "Previous draft restored.", "draftCleared": "Draft cleared.", "foundSavedDraft": "Saved Draft Found", "foundSavedDrafts": "Saved Drafts Found", "continueOrStartFresh": "You have unsaved changes from your previous session. Would you like to continue editing or start fresh?", "continueEditing": "Continue Editing", "hasDrafts": "You have {count} saved draft(s). Would you like to restore the latest one?", "multipleDraftsFound": "You have {count} saved drafts. Would you like to restore the most recent one?", "selectDraft": "Select Draft", "restoreDraft": "Restore Draft", "restoreLatest": "<PERSON><PERSON>st", "discardDraft": "Start Fresh", "startFresh": "Start Fresh", "autoSaveEnabled": "Auto-save is enabled. Your progress is automatically saved.", "saveNow": "Save Now", "connectionRestored": "Connection restored. Processing offline reports...", "offlineMode": "You're currently offline. Reports will be saved locally.", "offlineReportsCount": "{count} offline report(s) pending submission", "processingOfflineReports": "Processing offline reports..."}