import axios from 'axios';

async function testConfirmReceiptAPI() {
  console.log('🧪 Testing Confirm Receipt API Endpoint...');
  
  try {
    // First, get a transaction that should be in confirmation state
    // Using user cmav458yl0000vl50932ofx2h as shown in the error logs
    const userId = 'cmav458yl0000vl50932ofx2h';
    const transactionId = 'cmb1ckwie0006vlr4c42tu79m'; // From the error logs
    
    // Mock JWT token for testing (you might need to get a real one)
    const mockToken = 'your-jwt-token-here';
    
    console.log(`📋 Testing transaction ${transactionId} with user ${userId}`);
    
    // Make the API call
    const response = await axios.post(
      `http://localhost:3000/api/transactions/${transactionId}/confirm-receipt`,
      {},
      {
        headers: {
          'Authorization': `Bearer ${mockToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ API call successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    
  } catch (error: any) {
    if (error.response) {
      console.log('❌ API Error Response:');
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
      
      if (error.response.status === 401) {
        console.log('ℹ️  This is expected - we need a valid JWT token');
      } else if (error.response.status === 500 && 
                error.response.data?.error?.includes('not your turn')) {
        console.log('❌ The original error is still occurring!');
      } else {
        console.log('✅ Different error - our fix might be working!');
      }
    } else {
      console.log('❌ Network error:', error.message);
    }
  }
}

// Run the test
testConfirmReceiptAPI().catch(console.error);
