# Playwright MCP Server Usage Guide for MUNygo

## 🚀 Getting Started with Microsoft Playwright MCP

The Microsoft Playwright MCP server is now installed and configured for your VS Code environment. Here's how to use it effectively with your MUNygo currency exchange platform.

## 🛠️ Available MCP Commands

### Basic Browser Automation

```typescript
// Navigate to your local development server
browser_navigate: { url: "http://localhost:5173" }

// Take a screenshot of your mobile-first design
browser_take_screenshot: { filename: "munygo-mobile.png" }

// Capture accessibility snapshot (better for interactions)
browser_snapshot: {}
```

### Testing Your SmartNegotiationSection Component

```typescript
// Click elements with data-testid attributes
browser_click: { 
  element: "Accept recommendation button", 
  ref: "[data-testid='accept-recommendation-btn']" 
}

// Type in the custom message textarea
browser_type: { 
  element: "Custom message input", 
  ref: "[data-testid='custom-message-input']",
  text: "I prefer to pay first due to higher reputation"
}

// Test counter proposal button
browser_click: { 
  element: "Propose counter button", 
  ref: "[data-testid='propose-counter-btn']" 
}
```

### Mobile-First Testing Commands

```typescript
// Resize to mobile viewport (your primary target)
browser_resize: { width: 375, height: 667 }

// Test touch interactions on small screens
browser_resize: { width: 320, height: 568 }

// Test tablet progressive enhancement
browser_resize: { width: 768, height: 1024 }
```

### Real-Time Feature Testing

```typescript
// Wait for Socket.IO updates
browser_wait_for: { text: "Negotiation updated" }

// Test notification system
browser_wait_for: { text: "Counter proposal received" }

// Wait for skeleton screens to load
browser_wait_for: { textGone: "Loading..." }
```

### Form Testing for Your Authentication

```typescript
// Fill login form
browser_type: { 
  element: "Email input", 
  ref: "[data-testid='email-input']",
  text: "<EMAIL>"
}

browser_type: { 
  element: "Password input", 
  ref: "[data-testid='password-input']",
  text: "password123"
}

// Submit with Enter key
browser_type: { 
  element: "Password input", 
  ref: "[data-testid='password-input']",
  submit: true
}
```

### Currency Exchange Flow Testing

```typescript
// Test offer creation
browser_click: { 
  element: "Create offer button", 
  ref: "[data-testid='create-offer-btn']" 
}

browser_type: { 
  element: "Amount input", 
  ref: "[data-testid='amount-input']",
  text: "100000"
}

browser_select_option: { 
  element: "Currency dropdown", 
  ref: "[data-testid='currency-select']",
  values: ["IRR"]
}
```

## 🎯 Specific Tests for Your Components

### 1. SmartNegotiationSection Mobile Testing

```bash
# Start your dev server first
cd frontend && npm run dev

# Then use MCP commands to test:
```

```typescript
// Navigate to negotiation page
browser_navigate: { url: "http://localhost:5173/transaction/123/negotiation" }

// Set mobile viewport
browser_resize: { width: 375, height: 667 }

// Test loading states
browser_snapshot: {} // Capture initial state

// Wait for content to load
browser_wait_for: { text: "System Recommendation" }

// Test AI recommendation banner
browser_click: { 
  element: "Recommendation banner", 
  ref: ".recommendation-banner" 
}

// Test action buttons for recommended user
browser_click: { 
  element: "Accept recommendation", 
  ref: "[data-testid='accept-recommendation-btn']" 
}

// Test counter proposal flow
browser_click: { 
  element: "Propose counter", 
  ref: "[data-testid='propose-counter-btn']" 
}

// Fill custom message
browser_type: { 
  element: "Custom message", 
  ref: "[data-testid='custom-message-input']",
  text: "I can pay first if additional verification is provided"
}

// Submit counter proposal
browser_click: { 
  element: "Submit counter", 
  ref: ".submit-counter-btn" 
}
```

### 2. Authentication Flow Testing

```typescript
// Test login page
browser_navigate: { url: "http://localhost:5173/login" }

// Test mobile-friendly form
browser_click: { 
  element: "Email input", 
  ref: "[data-testid='email-input']" 
}

browser_type: { 
  element: "Email input", 
  ref: "[data-testid='email-input']",
  text: "<EMAIL>"
}

browser_type: { 
  element: "Password input", 
  ref: "[data-testid='password-input']",
  text: "password123"
}

// Test submit button touch target (should be ≥44px)
browser_click: { 
  element: "Submit button", 
  ref: "[data-testid='submit-btn']" 
}

// Wait for success redirect
browser_wait_for: { text: "Dashboard" }
```

### 3. Multi-language (Persian) Testing

```typescript
// Test Persian language support
browser_navigate: { url: "http://localhost:5173?lang=fa" }

// Verify RTL layout
browser_snapshot: {} // Should show RTL layout

// Test Persian text rendering
browser_wait_for: { text: "ایمیل" } // Email in Persian

// Test RTL negotiation section
browser_navigate: { url: "http://localhost:5173/transaction/123/negotiation?lang=fa" }

browser_click: { 
  element: "Persian accept button", 
  ref: "[data-testid='accept-recommendation-btn']" 
}
```

## 📱 Mobile-First Testing Workflow

### Step 1: Start Your Development Environment
```powershell
# Terminal 1: Start backend
cd backend && npm run dev

# Terminal 2: Start frontend  
cd frontend && npm run dev
```

### Step 2: Use MCP for Mobile Testing
```typescript
// Always start with mobile viewport
browser_resize: { width: 375, height: 667 }

// Navigate to your app
browser_navigate: { url: "http://localhost:5173" }

// Test skeleton screens
browser_snapshot: {} // Capture loading state

// Wait for content
browser_wait_for: { text: "MUNygo" }

// Test thumb-friendly navigation (bottom 1/3 of screen)
browser_click: { 
  element: "Main navigation", 
  ref: "[data-testid='bottom-nav']" 
}
```

### Step 3: Progressive Enhancement Testing
```typescript
// Test tablet view (768px+)
browser_resize: { width: 768, height: 1024 }
browser_snapshot: {} // Capture enhanced layout

// Test desktop view (1024px+)
browser_resize: { width: 1280, height: 720 }
browser_snapshot: {} // Capture full enhancement
```

## 🔧 Advanced MCP Features for Your Platform

### PDF Generation for Transaction Records
```typescript
browser_pdf_save: { filename: "transaction-receipt.pdf" }
```

### Network Monitoring for API Testing
```typescript
browser_network_requests: {} // Monitor Socket.IO connections
```

### Console Debugging
```typescript
browser_console_messages: {} // Check for JavaScript errors
```

### File Upload Testing (for document verification)
```typescript
browser_file_upload: { 
  paths: ["/path/to/test-id-document.jpg"] 
}
```

## 🚨 Common Issues and Solutions

### 1. Browser Not Installed
```typescript
browser_install: {} // Install required browsers
```

### 2. Element Not Found
- Always use your `data-testid` attributes
- Use `browser_snapshot: {}` first to see the current page state
- Check if elements are in viewport for mobile

### 3. Network Errors
- Ensure your dev servers are running
- Check if Socket.IO connections are working
- Use `browser_network_requests: {}` to debug

## 📊 Performance Testing with MCP

```typescript
// Test initial load performance
browser_navigate: { url: "http://localhost:5173" }
// Time should be < 3 seconds for mobile

// Test interaction responsiveness  
browser_click: { element: "Button", ref: "[data-testid='btn']" }
// Response should be < 100ms

// Test scroll performance
browser_press_key: { key: "PageDown" }
// Should be smooth on mobile devices
```

## 🔄 Continuous Testing Integration

You can integrate these MCP commands into your development workflow:

1. **Before commits**: Test critical user flows
2. **During development**: Quick mobile viewport testing
3. **Before deployment**: Full cross-device testing
4. **Post-deployment**: Verify production functionality

## 🎓 Next Steps

1. Start your dev servers: `cd frontend && npm run dev`
2. Use the MCP commands above to test your SmartNegotiationSection
3. Create automated test scripts using the Playwright configuration
4. Integrate with your CI/CD pipeline for continuous testing

The Playwright MCP server is now ready to help you test your mobile-first P2P currency exchange platform! 🚀
