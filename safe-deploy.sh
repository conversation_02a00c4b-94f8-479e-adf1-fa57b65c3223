#!/bin/bash

# Safe Production Deployment Script with Database Migrations
# Usage: ./safe-deploy.sh
# Run this on your CentOS host machine

set -e  # Exit on any error

echo "🚀 Safe Production Deployment with Database Migrations"
echo "📅 Started at: $(date)"
echo ""

# 1. Pull latest changes
echo "📥 Step 1: Pulling latest changes from git..."
git pull origin main
echo "✅ Git pull completed"
echo ""

# 2. Check for pending migrations BEFORE rebuilding
echo "🔍 Step 2: Checking for database migrations..."
MIGRATION_CHECK=$(docker-compose exec -T backend npx prisma migrate status 2>&1 || echo "MIGRATION_CHECK_FAILED")

if echo "$MIGRATION_CHECK" | grep -q "following migration"; then
    echo "📋 New migrations detected!"
    
    # 3. Create backup before migration
    echo "💾 Step 3: Creating database backup..."
    BACKUP_FILE="db_backup_$(date +%Y%m%d_%H%M%S).sql"
    docker-compose exec -T postgres pg_dump -U munygo_user munygo_db > "$BACKUP_FILE"
    echo "✅ Backup created: $BACKUP_FILE"
    
    # 4. Apply migrations
    echo "🔄 Step 4: Applying database migrations..."
    docker-compose exec -T backend npx prisma migrate deploy
    echo "✅ Migrations applied successfully"
    
elif echo "$MIGRATION_CHECK" | grep -q "No pending migrations"; then
    echo "✅ No new migrations to apply"
else
    echo "⚠️  Migration status unclear. Proceeding with caution..."
fi

echo ""

# 5. Rebuild and restart containers
echo "🔨 Step 5: Rebuilding containers..."
docker-compose build
echo "✅ Build completed"

echo "🔄 Step 6: Restarting containers..."
docker-compose up -d
echo "✅ Containers restarted"

# 6. Generate Prisma client
echo "🔄 Step 7: Regenerating Prisma client..."
sleep 10  # Wait for container to be ready
docker-compose exec -T backend npx prisma generate
echo "✅ Prisma client updated"

# 7. Health check
echo "🏥 Step 8: Running health check..."
sleep 5
HEALTH_RESULT=$(docker-compose exec -T backend node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.\$connect()
  .then(() => prisma.user.count())
  .then(count => {
    console.log('✅ Database connection OK - Users: ' + count);
    return prisma.\$disconnect();
  })
  .catch(err => {
    console.log('❌ Health check failed: ' + err.message);
    process.exit(1);
  });
" 2>&1)

echo "$HEALTH_RESULT"

if echo "$HEALTH_RESULT" | grep -q "Database connection OK"; then
    echo ""
    echo "🎉 Deployment completed successfully!"
    echo "📅 Finished at: $(date)"
    
    echo ""
    echo "📋 Container Status:"
    docker-compose ps
else
    echo "❌ Deployment failed health check!"
    exit 1
fi
