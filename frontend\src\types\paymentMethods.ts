// Payment method types for the frontend
export type PaymentMethodType = 
  | 'BANK_TRANSFER' 
  | 'DIGITAL_WALLET' 
  | 'CRYPTO_WALLET' 
  | 'MOBILE_MONEY' 
  | 'CASH_PICKUP';

export interface PaymentMethodBase {
  id: string;
  userId: string;
  currency: string;
  paymentMethodType: PaymentMethodType;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  swiftCode?: string | null;
  iban?: string | null;
  routingNumber?: string | null;
  sortCode?: string | null;
  bsb?: string | null;
  notes?: string | null;
  isDefaultForUser: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethodWithValidation extends PaymentMethodBase {
  validationStatus: 'complete' | 'incomplete';
  missingFields: string[];
}

export interface CreatePaymentMethodPayload {
  currency: string;
  paymentMethodType: PaymentMethodType;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  swiftCode?: string;
  iban?: string;
  routingNumber?: string;
  sortCode?: string;
  bsb?: string;
  notes?: string;
}

export interface UpdatePaymentMethodPayload {
  currency?: string;
  paymentMethodType?: PaymentMethodType;
  bankName?: string;
  accountNumber?: string;
  accountHolderName?: string;
  swiftCode?: string;
  iban?: string;
  routingNumber?: string;
  sortCode?: string;
  bsb?: string;
  notes?: string;
  isActive?: boolean;
}

export interface PaymentMethodsApiResponse {
  success: boolean;
  data: PaymentMethodWithValidation[];
  count?: number;
  currency?: string;
  message?: string;
}

export interface PaymentMethodApiResponse {
  success: boolean;
  data: PaymentMethodWithValidation;
  message?: string;
}

export interface PaymentMethodFilters {
  currency?: string;
  paymentMethodType?: PaymentMethodType;
  isActive?: boolean;
  isDefaultOnly?: boolean;
}

// For stats and summary views
export interface PaymentMethodStats {
  totalMethods: number;
  methodsByCurrency: Record<string, number>;
  methodsByType: Partial<Record<PaymentMethodType, number>>;
  completeMethods: number;
  incompleteMethods: number;
  defaultMethods: Record<string, PaymentMethodWithValidation>;
}

// API error response type
export interface ApiErrorResponse {
  error: string;
  details?: any;
}

// Currency configuration
export interface CurrencyConfig {
  code: string;
  label: string;
  symbol?: string;
  isSupported: boolean;
  requiredFields?: string[];
  optionalFields?: string[];
  preferredMethods?: PaymentMethodType[];
}

// For form validation
export interface PaymentMethodFormValidation {
  isValid: boolean;
  errors: Record<string, string[]>;
  warnings: Record<string, string[]>;
}

// Integration with existing transaction system
export interface PaymentMethodSelectionForTransaction {
  methodId?: string;
  currency: string;
  useExisting: boolean;
  method?: PaymentMethodWithValidation;
  newMethodData?: CreatePaymentMethodPayload;
}
