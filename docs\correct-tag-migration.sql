-- Correct Tag System Migration
-- Based on the current working Prisma schema

-- Create the tags table according to current Prisma schema
CREATE TABLE IF NOT EXISTS tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL UNIQUE,
  display_name JSONB NOT NULL, -- Multilingual display names
  description JSONB, -- Multilingual descriptions
  category_id UUID REFERENCES tag_categories(id) ON DELETE SET NULL,
  color VARCHAR(7), -- Hex color
  icon VARCHAR(50),
  weight INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  is_system BOOLEAN DEFAULT false,
  usage_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMPTZ,
  ai_relevance FLOAT DEFAULT 0.0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create tag_report_type_associations table
CREATE TABLE IF NOT EXISTS tag_report_type_associations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
  report_type VARCHAR(50) NOT NULL,
  weight INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(tag_id, report_type)
);

-- Create indexes for tags table
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
CREATE INDEX IF NOT EXISTS idx_tags_category_id ON tags(category_id);
CREATE INDEX IF NOT EXISTS idx_tags_is_active ON tags(is_active);
CREATE INDEX IF NOT EXISTS idx_tags_usage_count ON tags(usage_count);
CREATE INDEX IF NOT EXISTS idx_tags_ai_relevance ON tags(ai_relevance);

-- Create indexes for tag_report_type_associations
CREATE INDEX IF NOT EXISTS idx_tag_associations_report_type ON tag_report_type_associations(report_type);
CREATE INDEX IF NOT EXISTS idx_tag_associations_weight ON tag_report_type_associations(weight);

-- Create trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for tags (drop if exists first)
DROP TRIGGER IF EXISTS update_tags_updated_at ON tags;
CREATE TRIGGER update_tags_updated_at
    BEFORE UPDATE ON tags
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert predefined tags with proper structure
INSERT INTO tags (name, display_name, category_id, is_system, weight, is_active) VALUES
-- Severity tags
('urgent', '{"en": "Urgent", "fa": "فوری"}', (SELECT id FROM tag_categories WHERE name = 'severity'), true, 10, true),
('critical', '{"en": "Critical", "fa": "بحرانی"}', (SELECT id FROM tag_categories WHERE name = 'severity'), true, 9, true),
('high', '{"en": "High Priority", "fa": "اولویت بالا"}', (SELECT id FROM tag_categories WHERE name = 'severity'), true, 8, true),

-- Type tags
('bug-fix', '{"en": "Bug Fix", "fa": "رفع اشکال"}', (SELECT id FROM tag_categories WHERE name = 'type'), true, 7, true),
('enhancement', '{"en": "Enhancement", "fa": "بهبود"}', (SELECT id FROM tag_categories WHERE name = 'type'), true, 6, true),
('new-feature', '{"en": "New Feature", "fa": "ویژگی جدید"}', (SELECT id FROM tag_categories WHERE name = 'type'), true, 6, true),
('performance', '{"en": "Performance Issue", "fa": "مشکل عملکرد"}', (SELECT id FROM tag_categories WHERE name = 'type'), true, 7, true),

-- Area tags
('user-interface', '{"en": "User Interface", "fa": "رابط کاربری"}', (SELECT id FROM tag_categories WHERE name = 'area'), true, 5, true),
('user-experience', '{"en": "User Experience", "fa": "تجربه کاربری"}', (SELECT id FROM tag_categories WHERE name = 'area'), true, 5, true),
('backend', '{"en": "Backend", "fa": "بک‌اند"}', (SELECT id FROM tag_categories WHERE name = 'area'), true, 5, true),
('frontend', '{"en": "Frontend", "fa": "فرانت‌اند"}', (SELECT id FROM tag_categories WHERE name = 'area'), true, 5, true),
('database', '{"en": "Database", "fa": "پایگاه داده"}', (SELECT id FROM tag_categories WHERE name = 'area'), true, 5, true),
('security', '{"en": "Security", "fa": "امنیت"}', (SELECT id FROM tag_categories WHERE name = 'area'), true, 8, true),

-- Priority tags
('high-priority', '{"en": "High Priority", "fa": "اولویت بالا"}', (SELECT id FROM tag_categories WHERE name = 'priority'), true, 8, true),
('medium-priority', '{"en": "Medium Priority", "fa": "اولویت متوسط"}', (SELECT id FROM tag_categories WHERE name = 'priority'), true, 5, true),
('low-priority', '{"en": "Low Priority", "fa": "اولویت پایین"}', (SELECT id FROM tag_categories WHERE name = 'priority'), true, 3, true),

-- Status tags
('needs-review', '{"en": "Needs Review", "fa": "نیاز به بررسی"}', (SELECT id FROM tag_categories WHERE name = 'status'), true, 6, true),
('in-progress', '{"en": "In Progress", "fa": "در حال انجام"}', (SELECT id FROM tag_categories WHERE name = 'status'), true, 5, true),
('ready-to-test', '{"en": "Ready to Test", "fa": "آماده تست"}', (SELECT id FROM tag_categories WHERE name = 'status'), true, 4, true);

-- Insert tag-report type associations for better AI suggestions
INSERT INTO tag_report_type_associations (tag_id, report_type, weight) VALUES
-- Bug-related associations
((SELECT id FROM tags WHERE name = 'bug-fix'), 'BUG', 10),
((SELECT id FROM tags WHERE name = 'critical'), 'BUG', 9),
((SELECT id FROM tags WHERE name = 'urgent'), 'BUG', 8),

-- Performance associations
((SELECT id FROM tags WHERE name = 'performance'), 'PERFORMANCE', 10),
((SELECT id FROM tags WHERE name = 'backend'), 'PERFORMANCE', 7),

-- UI/UX associations
((SELECT id FROM tags WHERE name = 'user-interface'), 'UI_UX', 10),
((SELECT id FROM tags WHERE name = 'user-experience'), 'UI_UX', 10),
((SELECT id FROM tags WHERE name = 'frontend'), 'UI_UX', 8),

-- Feature request associations
((SELECT id FROM tags WHERE name = 'new-feature'), 'FEATURE_REQUEST', 10),
((SELECT id FROM tags WHERE name = 'enhancement'), 'FEATURE_REQUEST', 8),
((SELECT id FROM tags WHERE name = 'enhancement'), 'IMPROVEMENT', 9);
