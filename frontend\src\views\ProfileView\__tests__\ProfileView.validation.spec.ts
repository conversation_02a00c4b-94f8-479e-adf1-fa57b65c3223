import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { createTestingP<PERSON> } from '@pinia/testing';
import { useAuthStore } from '@/stores/auth';
import ProfileView from '../ProfileView.vue';
import {
  NConfigProvider, NMessageProvider, NCard, NSpin, NDescriptions, NDescriptionsItem,
  NTag, NDivider, NH3, NAlert, NForm, NFormItem, NInput, NButton, NIcon, NTooltip
} from 'naive-ui';
import type { UserInfo } from '@/types/auth';
import { ref, computed, nextTick } from 'vue';
import apiClient from '@/services/apiClient';
import type { Mock } from 'vitest';

// --- Mock apiClient ---
vi.mock('@/services/apiClient', () => ({
  default: { post: vi.fn() }
}));
const apiClientPostMock = apiClient.post as Mock;

// --- Mock naive-ui useMessage ---
const messageSuccessSpy = vi.fn();
const messageErrorSpy = vi.fn();
vi.mock('naive-ui', async (importOriginal) => {
  const originalNaive = await importOriginal<typeof import('naive-ui')>();
  return {
    ...originalNaive,
    useMessage: () => ({ success: messageSuccessSpy, error: messageErrorSpy }),
  };
});

// --- Mock the auth store module ---
const mockUserRef = ref<UserInfo | null>(null);
const fetchUserProfileMock = vi.fn();
const updatePhoneVerificationStatusMock = vi.fn((status: boolean, phoneNumber: string | null) => {
  if (mockUserRef.value) {
    mockUserRef.value.phoneVerified = status;
    mockUserRef.value.phoneNumber = phoneNumber;
  }
});

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    user: mockUserRef,
    fetchUserProfile: fetchUserProfileMock,
    isLoggedIn: computed(() => !!mockUserRef.value),
    updatePhoneVerificationStatus: updatePhoneVerificationStatusMock,
  }),
}));
// -----------------------------------------

// Required wrapper to provide Naive UI context
const TestWrapper = {
  template: `
    <n-config-provider>
      <n-message-provider>
        <profile-view />
      </n-message-provider>
    </n-config-provider>
  `,
  components: {
    ProfileView, NConfigProvider, NMessageProvider, NCard, NSpin, NDescriptions,
    NDescriptionsItem, NTag, NDivider, NH3, NAlert, NInput, NButton, NIcon, NTooltip
    // Note: NForm and NFormItem are stubbed below
  },
};

describe('ProfileView.vue - Form Validation', () => {
  let wrapper: ReturnType<typeof mount>;

  // Function to setup mocks and mount component for each test
  const setupTest = (initialUser: UserInfo) => {
    vi.resetAllMocks();
    mockUserRef.value = initialUser;
    fetchUserProfileMock.mockResolvedValue(undefined);
    apiClientPostMock.mockResolvedValue({ data: {} }); // Default success

    wrapper = mount(TestWrapper, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
        stubs: {
          // ---> Simplify NForm stub - REMOVE validate method <---
          NForm: {
            name: 'StubbedNForm',
            template: '<form @submit.prevent="$emit(\'submit\')"><slot></slot></form>',
            // No validate method needed here, component uses its own ref
            restoreValidation: vi.fn(), // Keep if needed elsewhere
          },
          NFormItem: {
            template: '<div><slot></slot><div class="validation-message"><slot name="feedback"></slot></div></div>',
          }
        }
      },
    });
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  // --- Phone Input Validation Tests ---

  it('does not call requestOtp if phone number is empty', async () => {
    // Arrange
    const initialUser: UserInfo = { id: 'val-user-1', email: '<EMAIL>', emailVerified: true, phoneVerified: false, phoneNumber: null };
    // ---> REMOVE validation options <---
    setupTest(initialUser);

    await flushPromises();
    await nextTick();

    const phoneInput = wrapper.find('[data-testid="phone-input"] input');
    const phoneForm = wrapper.find('form');

    // Act
    await phoneInput.setValue('');
    await phoneForm.trigger('submit');
    await flushPromises(); // Allow submit handler to run (and fail validation internally)

    // Assert
    // ---> REMOVE assertion for validate mock <---
    // expect(phoneFormValidateMock).toHaveBeenCalled();
    expect(apiClientPostMock).not.toHaveBeenCalled(); // Core assertion
    expect(messageErrorSpy).not.toHaveBeenCalled();
  });

  it('does not call requestOtp if phone number format is invalid', async () => {
    // Arrange
    const initialUser: UserInfo = { id: 'val-user-2', email: '<EMAIL>', emailVerified: true, phoneVerified: false, phoneNumber: null };
    // ---> REMOVE validation options <---
    setupTest(initialUser);

    await flushPromises();
    await nextTick();

    const phoneInput = wrapper.find('[data-testid="phone-input"] input');
    const phoneForm = wrapper.find('form');

    // Act
    await phoneInput.setValue('12345');
    await phoneForm.trigger('submit');
    await flushPromises();

    // Assert
    // ---> REMOVE assertion for validate mock <---
    // expect(phoneFormValidateMock).toHaveBeenCalled();
    expect(apiClientPostMock).not.toHaveBeenCalled(); // Core assertion
    expect(messageErrorSpy).not.toHaveBeenCalled();
  });

  // --- OTP Input Validation Tests ---

  async function showOtpFormForTest() {
    const vm = wrapper.findComponent(ProfileView).vm;
    vm.showOtpForm = true;
    await nextTick();
  }

  it('does not call verifyOtp if OTP is empty', async () => {
    // Arrange
    const initialUser: UserInfo = { id: 'val-user-3', email: '<EMAIL>', emailVerified: true, phoneVerified: false, phoneNumber: null };
    // ---> REMOVE validation options <---
    setupTest(initialUser);
    await flushPromises();
    await showOtpFormForTest();

    const otpInput = wrapper.find('[data-testid="otp-input"] input');
    const otpForm = wrapper.find('form');

    // Act
    await otpInput.setValue('');
    await otpForm.trigger('submit');
    await flushPromises();

    // Assert
    // ---> REMOVE assertion for validate mock <---
    // expect(otpFormValidateMock).toHaveBeenCalled();
    expect(apiClientPostMock).not.toHaveBeenCalled(); // Core assertion
    expect(messageErrorSpy).not.toHaveBeenCalled();
  });

  it('does not call verifyOtp if OTP has less than 6 digits', async () => {
    // Arrange
    const initialUser: UserInfo = { id: 'val-user-4', email: '<EMAIL>', emailVerified: true, phoneVerified: false, phoneNumber: null };
    // ---> REMOVE validation options <---
    setupTest(initialUser);
    await flushPromises();
    await showOtpFormForTest();

    const otpInput = wrapper.find('[data-testid="otp-input"] input');
    const otpForm = wrapper.find('form');

    // Act
    await otpInput.setValue('12345');
    await otpForm.trigger('submit');
    await flushPromises();

    // Assert
    // ---> REMOVE assertion for validate mock <---
    // expect(otpFormValidateMock).toHaveBeenCalled();
    expect(apiClientPostMock).not.toHaveBeenCalled(); // Core assertion
    expect(messageErrorSpy).not.toHaveBeenCalled();
  });

   it('does not call verifyOtp if OTP contains non-digits', async () => {
    // Arrange
    const initialUser: UserInfo = { id: 'val-user-5', email: '<EMAIL>', emailVerified: true, phoneVerified: false, phoneNumber: null };
    // ---> REMOVE validation options <---
    setupTest(initialUser);
    await flushPromises();
    await showOtpFormForTest();

    const otpInput = wrapper.find('[data-testid="otp-input"] input');
    const otpForm = wrapper.find('form');

    // Act
    await otpInput.setValue('12345a');
    await otpForm.trigger('submit');
    await flushPromises();

    // Assert
    // ---> REMOVE assertion for validate mock <---
    // expect(otpFormValidateMock).toHaveBeenCalled();
    expect(apiClientPostMock).not.toHaveBeenCalled(); // Core assertion
    expect(messageErrorSpy).not.toHaveBeenCalled();
  });

});