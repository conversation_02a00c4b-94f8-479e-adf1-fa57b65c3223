<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Offer Form Test - Mobile-First UX</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 2rem;
            color: white;
        }

        .hero-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .hero-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            line-height: 1.5;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 2rem;
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #333;
        }

        .form-section {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .radio-group {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .radio-card {
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            text-align: center;
        }

        .radio-card:hover {
            border-color: #2080f0;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(32, 128, 240, 0.2);
        }

        .radio-card.active {
            border-color: #2080f0;
            background: rgba(32, 128, 240, 0.1);
            transform: scale(1.02);
        }

        .radio-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .radio-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: #333;
        }

        .radio-description {
            font-size: 0.875rem;
            color: #666;
            line-height: 1.4;
        }

        .input-container {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #2080f0;
            box-shadow: 0 0 0 3px rgba(32, 128, 240, 0.1);
        }

        .input-prefix {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            font-weight: 600;
            color: #18a058;
            pointer-events: none;
        }

        .input-with-prefix {
            padding-left: 4rem;
        }

        .quick-amounts {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .quick-amount-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .quick-amount-btn:hover {
            border-color: #2080f0;
            color: #2080f0;
        }

        .rate-helper {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(32, 128, 240, 0.1);
            border: 1px solid rgba(32, 128, 240, 0.3);
            border-radius: 8px;
        }

        .calculation-line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .total-amount {
            color: #18a058;
            font-weight: 600;
            font-size: 1.125rem;
        }

        .submit-button {
            width: 100%;
            padding: 0.75rem 1.5rem;
            background: #2080f0;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-button:hover {
            background: #1968d2;
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(32, 128, 240, 0.3);
        }

        .submit-button:active {
            transform: scale(0.98);
        }

        .submit-helper {
            text-align: center;
            margin-top: 1rem;
            font-size: 0.875rem;
            color: #666;
        }

        /* Tablet */
        @media (min-width: 768px) {
            .radio-group {
                grid-template-columns: 1fr 1fr;
            }
            
            .hero-title {
                font-size: 2rem;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .hero-title {
                font-size: 2.25rem;
            }
        }

        .comparison-section {
            margin-top: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: white;
        }

        .comparison-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }

        .comparison-list {
            list-style: none;
            line-height: 1.8;
        }

        .comparison-list li {
            padding: 0.25rem 0;
        }

        .comparison-list li:before {
            content: "✅ ";
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="hero-icon">➕</div>
            <h1 class="hero-title">Create New Offer</h1>
            <p class="hero-subtitle">Set up your currency exchange offer with a simple, streamlined process</p>
        </div>

        <!-- Simple Form -->
        <div class="form-card">
            <div class="card-header">
                <span style="font-size: 1.5rem;">💰</span>
                <span>Offer Details</span>
            </div>

            <!-- Offer Type -->
            <div class="form-section">
                <label class="form-label">What would you like to do?</label>
                <div class="radio-group">
                    <div class="radio-card" id="sell-option">
                        <div class="radio-icon" style="color: #e74c3c;">📤</div>
                        <h3 class="radio-title">Sell CAD</h3>
                        <p class="radio-description">I have Canadian Dollars and want Iranian Rials</p>
                    </div>
                    <div class="radio-card" id="buy-option">
                        <div class="radio-icon" style="color: #27ae60;">📥</div>
                        <h3 class="radio-title">Buy CAD</h3>
                        <p class="radio-description">I have Iranian Rials and want Canadian Dollars</p>
                    </div>
                </div>
            </div>

            <!-- Amount -->
            <div class="form-section">
                <label class="form-label">Amount (CAD)</label>
                <div class="input-container">
                    <span class="input-prefix">CAD $</span>
                    <input type="number" class="form-input input-with-prefix" id="amount-input" placeholder="Enter amount in Canadian Dollars">
                </div>
                <div class="quick-amounts">
                    <button class="quick-amount-btn" onclick="setAmount(100)">$100</button>
                    <button class="quick-amount-btn" onclick="setAmount(500)">$500</button>
                    <button class="quick-amount-btn" onclick="setAmount(1000)">$1,000</button>
                    <button class="quick-amount-btn" onclick="setAmount(2000)">$2,000</button>
                    <button class="quick-amount-btn" onclick="setAmount(5000)">$5,000</button>
                </div>
            </div>

            <!-- Exchange Rate -->
            <div class="form-section">
                <label class="form-label">Exchange Rate</label>
                <div class="input-container">
                    <span class="input-prefix">IRR</span>
                    <input type="number" class="form-input input-with-prefix" id="rate-input" placeholder="Rate in IRR per CAD">
                </div>
                
                <div class="rate-helper" id="rate-helper" style="display: none;">
                    <div class="calculation-line">
                        <span>Total Value:</span>
                        <strong id="total-value">0 IRR</strong>
                    </div>
                    <div class="calculation-line">
                        <span>You will <span id="action-text">receive</span>:</span>
                        <strong class="total-amount" id="final-amount">0 IRR</strong>
                    </div>
                </div>
            </div>

            <!-- Submit -->
            <div class="form-section" style="margin-top: 2rem;">
                <button class="submit-button" id="submit-btn" onclick="handleSubmit()">
                    ✅ Create Offer
                </button>
                <p class="submit-helper">Your offer will be visible to other users once created</p>
            </div>
        </div>

        <!-- Comparison Section -->
        <div class="comparison-section">
            <h3 class="comparison-title">📱 Simplified vs Complex Form Benefits</h3>
            <ul class="comparison-list">
                <li><strong>Mobile-First Design:</strong> Single column, touch-friendly layout</li>
                <li><strong>Simplified Pricing:</strong> No complex tier adjustments to confuse users</li>
                <li><strong>Visual Card Selection:</strong> Clear, icon-based offer type selection</li>
                <li><strong>Quick Amount Shortcuts:</strong> Common amounts for faster input</li>
                <li><strong>Real-time Calculation:</strong> Instant total value feedback</li>
                <li><strong>Professional Animations:</strong> Smooth interactions and feedback</li>
                <li><strong>Reduced Cognitive Load:</strong> Only essential fields visible</li>
                <li><strong>Better Conversion Rate:</strong> Simpler forms increase completion</li>
            </ul>
        </div>
    </div>

    <script>
        let selectedType = null;

        // Handle offer type selection
        document.getElementById('sell-option').addEventListener('click', function() {
            selectOfferType('sell');
        });

        document.getElementById('buy-option').addEventListener('click', function() {
            selectOfferType('buy');
        });

        function selectOfferType(type) {
            // Remove active class from all
            document.querySelectorAll('.radio-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // Add active class to selected
            document.getElementById(type + '-option').classList.add('active');
            selectedType = type;
            
            // Update action text
            document.getElementById('action-text').textContent = type === 'sell' ? 'receive' : 'pay';
            
            // Recalculate
            calculateTotal();
            
            // Visual feedback
            const activeCard = document.getElementById(type + '-option');
            activeCard.style.transform = 'scale(0.98)';
            setTimeout(() => {
                activeCard.style.transform = '';
            }, 150);
        }

        // Set amount from quick buttons
        function setAmount(amount) {
            document.getElementById('amount-input').value = amount;
            calculateTotal();
        }

        // Calculate total on input changes
        document.getElementById('amount-input').addEventListener('input', calculateTotal);
        document.getElementById('rate-input').addEventListener('input', calculateTotal);

        function calculateTotal() {
            const amount = parseFloat(document.getElementById('amount-input').value) || 0;
            const rate = parseFloat(document.getElementById('rate-input').value) || 0;
            
            if (amount > 0 && rate > 0) {
                const total = amount * rate;
                document.getElementById('total-value').textContent = formatCurrency(total) + ' IRR';
                document.getElementById('final-amount').textContent = formatCurrency(total) + ' IRR';
                document.getElementById('rate-helper').style.display = 'block';
            } else {
                document.getElementById('rate-helper').style.display = 'none';
            }
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-US').format(amount);
        }

        function handleSubmit() {
            if (!selectedType) {
                alert('Please select an offer type');
                return;
            }
            
            const amount = parseFloat(document.getElementById('amount-input').value);
            const rate = parseFloat(document.getElementById('rate-input').value);
            
            if (!amount || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }
            
            if (!rate || rate <= 0) {
                alert('Please enter a valid exchange rate');
                return;
            }
            
            // Visual feedback
            const btn = document.getElementById('submit-btn');
            btn.style.transform = 'scale(0.98)';
            btn.textContent = '⏳ Creating Offer...';
            
            setTimeout(() => {
                btn.textContent = '🎉 Offer Created Successfully!';
                btn.style.background = '#18a058';
                setTimeout(() => {
                    btn.textContent = '✅ Create Offer';
                    btn.style.background = '#2080f0';
                    btn.style.transform = '';
                }, 2000);
            }, 1000);
        }

        // Add some initial interaction
        selectOfferType('sell');
        setAmount(1000);
        document.getElementById('rate-input').value = 55000;
        calculateTotal();
    </script>
</body>
</html>
