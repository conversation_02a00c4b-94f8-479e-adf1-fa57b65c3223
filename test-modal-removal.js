/**
 * Verify VoiceRecorder modal removal and clean error handling
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Verifying VoiceRecorder Modal Removal...\n');

const voiceRecorderPath = path.join(__dirname, 'frontend', 'src', 'components', 'VoiceRecorder.vue');
const voiceRecorderContent = fs.readFileSync(voiceRecorderPath, 'utf8');

// Test 1: Check modal is completely removed
const hasModal = voiceRecorderContent.includes('<n-modal') || voiceRecorderContent.includes('Analysis Failure Modal');
console.log(`✅ Modal completely removed: ${!hasModal ? 'PASS' : 'FAIL'}`);

// Test 2: Check modal-related refs are removed
const hasShowAnalysisFailure = voiceRecorderContent.includes('showAnalysisFailure');
console.log(`✅ showAnalysisFailure refs removed: ${!hasShowAnalysisFailure ? 'PASS' : 'FAIL'}`);

// Test 3: Check modal methods are removed
const hasCloseFailureModal = voiceRecorderContent.includes('closeFailureModal');
const hasHandleResendFromModal = voiceRecorderContent.includes('handleResendFromModal');
console.log(`✅ Modal methods removed: ${!hasCloseFailureModal && !hasHandleResendFromModal ? 'PASS' : 'FAIL'}`);

// Test 4: Check disable prop is removed
const hasDisableProp = voiceRecorderContent.includes('disableInternalErrorModal');
console.log(`✅ Disable prop removed: ${!hasDisableProp ? 'PASS' : 'FAIL'}`);

// Test 5: Check emit still works for parent handling
const hasProcessingErrorEmit = voiceRecorderContent.includes("emit('processing-error'");
console.log(`✅ Still emits processing-error: ${hasProcessingErrorEmit ? 'PASS' : 'FAIL'}`);

// Test 6: Check DebugReportButtonEnhanced prop is removed
const debugReportPath = path.join(__dirname, 'frontend', 'src', 'components', 'DebugReportButtonEnhanced.vue');
const debugReportContent = fs.readFileSync(debugReportPath, 'utf8');
const hasDisablePropInParent = debugReportContent.includes('disable-internal-error-modal');
console.log(`✅ Disable prop removed from parent: ${!hasDisablePropInParent ? 'PASS' : 'FAIL'}`);

// Test 7: Check DebugReportButtonEnhanced still handles errors
const hasParentErrorHandling = debugReportContent.includes('handleVoiceError') && 
                                debugReportContent.includes('dialog.create');
console.log(`✅ Parent still handles errors: ${hasParentErrorHandling ? 'PASS' : 'FAIL'}`);

console.log('\n📊 SUMMARY:');
const tests = [
  !hasModal,
  !hasShowAnalysisFailure, 
  !hasCloseFailureModal && !hasHandleResendFromModal,
  !hasDisableProp,
  hasProcessingErrorEmit,
  !hasDisablePropInParent,
  hasParentErrorHandling
];

const passCount = tests.filter(Boolean).length;
const totalCount = tests.length;

if (passCount === totalCount) {
  console.log(`🎉 ALL TESTS PASSED (${passCount}/${totalCount})`);
  console.log('✅ VoiceRecorder modal completely removed');
  console.log('✅ DebugReportButtonEnhanced handles all error display');
  console.log('✅ Clean separation of concerns achieved');
} else {
  console.log(`⚠️  SOME TESTS FAILED (${passCount}/${totalCount})`);
  process.exit(1);
}
