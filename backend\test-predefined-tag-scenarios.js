const axios = require('axios');

// Test different scenarios with predefined tags
async function testPredefinedTagScenarios() {
  console.log('🧪 Testing Predefined Tag Scenarios...\n');

  const baseUrl = 'http://localhost:3000';
  
  const predefinedTags = {
    bug: ['ui-layout', 'performance', 'authentication', 'navigation', 'data-validation'],
    feature: ['enhancement', 'new-functionality', 'user-experience', 'accessibility'],
    question: ['how-to', 'configuration', 'troubleshooting', 'documentation'],
    performance: ['slow-loading', 'memory-usage', 'api-response', 'database-query']
  };

  const testCases = [
    {
      name: 'Performance Issue',
      transcription: 'The page is loading very slowly, especially the offer list. It takes about 10 seconds to load all the offers and the API seems to be responding slowly.',
      expectedType: 'performance',
      expectedPredefinedTags: ['slow-loading', 'api-response']
    },
    {
      name: 'UI Bug',
      transcription: 'The navigation menu is overlapping with the main content when I resize the browser window. The layout looks broken on mobile devices.',
      expectedType: 'bug',
      expectedPredefinedTags: ['ui-layout', 'navigation']
    },
    {
      name: 'Feature Request',
      transcription: 'It would be great if we could have a dark mode theme option. The current bright theme is hard on the eyes during night time usage.',
      expectedType: 'feature',
      expectedPredefinedTags: ['enhancement', 'user-experience']
    },
    {
      name: 'Authentication Problem',
      transcription: 'I cannot log in to my account. The login form keeps showing validation errors even when I enter correct credentials.',
      expectedType: 'bug',
      expectedPredefinedTags: ['authentication', 'data-validation']
    }
  ];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`📝 Test ${i + 1}: ${testCase.name}`);
    
    try {
      const response = await axios.post(`${baseUrl}/api/ai/analyze`, {
        transcription: testCase.transcription,
        language: 'en',
        userContext: {
          currentPage: '/test',
          userAgent: 'test-browser'
        },
        predefinedTags: predefinedTags
      });

      if (response.data.success) {
        const report = response.data.generatedReport;
        console.log(`   ✅ Success`);
        console.log(`   📊 Report Type: ${report.suggestedType}`);
        console.log(`   📝 Title: ${report.title}`);
        console.log(`   🏷️  Tags: ${report.suggestedTags?.map(t => `"${t.tag}" (${t.origin})`).join(', ') || 'None'}`);
        
        if (report.suggestedTags) {
          const predefinedUsed = report.suggestedTags.filter(t => t.origin === 'PREDEFINED');
          const aiUsed = report.suggestedTags.filter(t => t.origin === 'AI_SUGGESTED');
          console.log(`   📌 Predefined: ${predefinedUsed.length}, AI-suggested: ${aiUsed.length}`);
          
          // Check if expected predefined tags were used
          const expectedFound = testCase.expectedPredefinedTags.filter(expectedTag => 
            predefinedUsed.some(usedTag => usedTag.tag === expectedTag)
          );
          console.log(`   🎯 Expected tags found: ${expectedFound.join(', ') || 'None'}`);
        }
        
        console.log(`   🎯 Confidence: ${(report.confidence * 100).toFixed(1)}%`);
      } else {
        console.log(`   ❌ Failed: ${response.data.error}`);
      }
    } catch (error) {
      console.log(`   ❌ Request failed: ${error.response?.data?.error || error.message}`);
    }
    
    console.log('\n' + '-'.repeat(50) + '\n');
  }

  console.log('✨ Predefined tag scenario testing completed!');
}

// Run the test
testPredefinedTagScenarios().catch(console.error);
