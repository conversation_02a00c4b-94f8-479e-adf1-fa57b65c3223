// Quick notification test utility
// Add this to your browser console to test notification system

console.log('🔍 MUNygo Notification Debug Utility');
console.log('===================================');

// Function to check notification store state
function checkNotificationStore() {
    // Check if we can access the Pinia store
    try {
        const app = document.querySelector('#app').__vueapp__;
        const stores = app._context.provides[Symbol.for('pinia')];
        
        if (stores) {
            console.log('✅ Pinia stores accessible');
            
            // Try to access notification store
            const notificationStore = stores._s.get('notificationStore');
            if (notificationStore) {
                console.log('✅ Notification store found');
                console.log('📊 Notification store state:', {
                    notifications: notificationStore.notifications,
                    unreadCount: notificationStore.unreadNotificationsCount,
                    isLoading: notificationStore.isLoading,
                    error: notificationStore.error,
                    lastFetched: notificationStore.lastFetchedTimestamp
                });
                return notificationStore;
            } else {
                console.log('❌ Notification store not found');
            }
        } else {
            console.log('❌ Pinia stores not accessible');
        }
    } catch (error) {
        console.log('❌ Error accessing stores:', error);
    }
    
    return null;
}

// Function to check socket connection
function checkSocketConnection() {
    console.log('🔌 Checking socket connection...');
    
    // Check if socket manager is accessible via window
    if (window.socketManager) {
        console.log('✅ Socket manager accessible');
        console.log('📊 Socket state:', {
            connected: window.socketManager.isConnected(),
            socket: window.socketManager.socket
        });
    } else {
        console.log('❌ Socket manager not accessible via window');
    }
    
    // Check global socket instances
    if (window.io) {
        console.log('✅ Socket.IO available globally');
    }
}

// Function to manually trigger notification fetch
function fetchNotifications() {
    console.log('📥 Manually triggering notification fetch...');
    
    const store = checkNotificationStore();
    if (store) {
        try {
            store.fetchNotifications({ unreadOnly: false, limit: 20 });
            console.log('✅ Notification fetch triggered');
        } catch (error) {
            console.log('❌ Error triggering fetch:', error);
        }
    }
}

// Function to simulate a notification
function simulateNotification() {
    console.log('🧪 Simulating notification...');
    
    const store = checkNotificationStore();
    if (store) {
        const testNotification = {
            id: 'test-' + Date.now(),
            userId: 'current-user',
            type: 'NEW_INTEREST_ON_YOUR_OFFER',
            message: 'Test notification - someone showed interest in your offer',
            isRead: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            relatedEntityType: 'OFFER',
            relatedEntityId: 'test-offer-id',
            actorId: 'test-actor',
            actorUsername: 'TestUser',
            data: {
                interestId: 'test-interest-id',
                offerId: 'test-offer-id',
                offerTitle: 'TEST-USD 100',
                offerType: 'SELL',
                offerAmount: 100
            }
        };
        
        try {
            store.addOrUpdateNotification(testNotification);
            console.log('✅ Test notification added:', testNotification);
        } catch (error) {
            console.log('❌ Error adding test notification:', error);
        }
    }
}

// Function to check recent browser logs
function checkRecentLogs() {
    console.log('📋 Recent notification-related logs:');
    console.log('Look for these patterns in console:');
    console.log('- 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION');
    console.log('- [NotificationStore] Received NEW_NOTIFICATION via centralized manager');
    console.log('- [NotificationStore] Added new notification');
    console.log('- [DreamNavBar] notification-related logs');
}

// Run initial checks
console.log('🚀 Starting notification system check...');
checkNotificationStore();
checkSocketConnection();
checkRecentLogs();

console.log('');
console.log('🔧 Available functions:');
console.log('- checkNotificationStore() - Check notification store state');
console.log('- checkSocketConnection() - Check socket connection');
console.log('- fetchNotifications() - Manually fetch notifications');
console.log('- simulateNotification() - Add a test notification');
console.log('- checkRecentLogs() - Show what to look for in logs');
console.log('');
