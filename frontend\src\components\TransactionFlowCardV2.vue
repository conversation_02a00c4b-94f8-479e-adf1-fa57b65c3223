<template>
  <n-card :title="cardHeaderV2" bordered class="transaction-flow-card-v2" v-if="currentTransaction">
    <template #header-extra>
      <n-space align="center">
        <span :style="{ width: '10px', height: '10px', borderRadius: '50%', backgroundColor: statusDotColor, display: 'inline-block', marginRight: '8px' }"></span>
        <n-text depth="3" style="font-size: 0.9em;">{{ transactionDisplayId }}</n-text>
      </n-space>
    </template>

    <n-spin :show="isLoading && !isActionLoading">
      <div v-if="!currentTransaction && !isLoading">
        <n-alert title="No Transaction Data" type="info">
          There is no active transaction associated with this chat, or it has not loaded yet.
        </n-alert>
      </div>
      <div v-else-if="currentTransaction">
        <!-- User and Timer Info -->
        <n-space justify="space-between" align="center" style="margin-bottom: 16px;">
          <n-text depth="2">{{ userRoleDisplay }} with {{ otherPartyUsername }}</n-text>
          <n-text :type="isTimerCritical ? 'error' : (isTimerExpired ? 'warning' : 'default')" style="font-weight: bold;">
            Time Left: {{ timeLeft }}
          </n-text>
        </n-space>

        <!-- Main Stepper for Visual Flow -->
        <n-steps :current="currentVisualStepIndex" :status="overallStepperStatus" vertical style="margin-bottom: 20px;">
          <n-step
            v-for="step in visualSteps"
            :key="step.key"
            :title="step.title"
            :description="step.description"
            :status="step.status"
          >
            <template #icon><n-icon :component="step.icon || InfoIcon" /></template>
            <!-- Sub-steps or details within a step, shown in a timeline -->
            <template v-if="step.subSteps && step.subSteps.length > 0">
              <n-collapse :default-expanded-names="step.subSteps.some(ss => ss.active) || step.isActive ? [step.key + '-sub'] : []" style="margin-top: 8px;">
                <n-collapse-item :title="step.subStepsTitle || 'Details'" :name="step.key + '-sub'">
                  <n-timeline size="medium" horizontal style="margin-top:5px;">
                    <n-timeline-item
                      v-for="subStep in step.subSteps"
                      :key="subStep.key"
                      :type="subStep.status === 'finish' ? 'success' : subStep.status === 'process' ? 'info' : subStep.status === 'error' ? 'error' : 'default'"
                      :title="subStep.title"
                      :time="subStep.timestamp ? new Date(subStep.timestamp).toLocaleString() : 'Pending'"
                    >
                      <template #icon><n-icon :component="subStep.icon || PendingIcon" /></template>
                      <n-text v-if="subStep.description" depth="3" style="font-size:0.9em">{{ subStep.description }}</n-text>
                    </n-timeline-item>
                  </n-timeline>
                </n-collapse-item>
              </n-collapse>
            </template>
          </n-step>
        </n-steps>

        <!-- Terminal State Display -->
        <n-alert v-if="isTerminalState && terminalStateDetails" :title="terminalStateDetails.title" :type="terminalStateDetails.type" :show-icon="true" style="margin-bottom: 16px;">
            <template #icon><n-icon :component="terminalStateDetails.icon" /></template>
            {{ terminalStateDetails.message }}
        </n-alert>

        <!-- Action Buttons Area -->
        <n-divider v-if="!isTerminalState">Your Action</n-divider>
        <n-space vertical v-if="!isTerminalState">
          <n-alert v-if="storeError" title="Error" type="error" closable @close="clearStoreError">
            {{ storeError }}
          </n-alert>

          <n-button v-if="isUserTurnToAgree" @click="handleAgreeToTerms" type="primary" :loading="isActionLoading === 'agree'" :disabled="isCurrentActionBlockedByTimer || (isActionLoading !== null && isActionLoading !== 'agree')">
            Agree to Terms
          </n-button>
          
          <n-button v-if="canDesignateFirstPayer && isUserTurn" @click="openDesignateModal" type="primary" :loading="isActionLoading === 'designate'" :disabled="isCurrentActionBlockedByTimer || (isActionLoading !== null && isActionLoading !== 'designate')">
            Designate First Payer
          </n-button>

          <n-button v-if="isUserTurnForPayment" @click="openDeclarePaymentModal" type="primary" :loading="isActionLoading === 'declarePayment'" :disabled="isCurrentActionBlockedByTimer || (isActionLoading !== null && isActionLoading !== 'declarePayment')">
            Declare Payment Made
          </n-button>

          <n-button v-if="isUserTurnForConfirmation" @click="handleConfirmReceipt" type="primary" :loading="isActionLoading === 'confirmReceipt'" :disabled="isCurrentActionBlockedByTimer || (isActionLoading !== null && isActionLoading !== 'confirmReceipt')">
            Confirm Receipt
          </n-button>
          
          <n-button v-if="canCancel" @click="openCancelModal" type="warning" ghost :loading="isActionLoading === 'cancel'" :disabled="(isActionLoading !== null && isActionLoading !== 'cancel')">
            Cancel Transaction
          </n-button>
          <n-button v-if="canDispute" @click="openDisputeModal" type="error" ghost :loading="isActionLoading === 'dispute'" :disabled="(isActionLoading !== null && isActionLoading !== 'dispute')">
            Dispute Transaction
          </n-button>
        </n-space>

        <!-- Modals -->
        <n-modal v-model:show="showDesignateModal" preset="dialog" title="Designate First Payer">
          <p>Who will make their payment first?</p>
          <n-radio-group v-model:value="selectedFirstPayerId" name="firstPayer">
            <n-space vertical>            <n-radio :value="currentTransaction.currencyAProviderId">{{ currentTransaction.currencyAProvider?.username }} (pays {{currentTransaction.currencyA}})</n-radio>
            <n-radio :value="currentTransaction.currencyBProviderId">{{ currentTransaction.currencyBProvider?.username }} (pays {{currentTransaction.currencyB}})</n-radio>
            </n-space>
          </n-radio-group>
          <template #action>
            <n-button @click="showDesignateModal = false">Cancel</n-button>
            <n-button type="primary" @click="handleDesignateFirstPayer" :disabled="!selectedFirstPayerId" :loading="isActionLoading === 'designate'">Confirm</n-button>
          </template>
        </n-modal>

        <n-modal v-model:show="showDeclarePaymentModal" preset="dialog" title="Declare Payment Made">
          <n-input v-model:value="paymentTrackingNumber" placeholder="Optional: Tracking / Reference #" />
          <template #action>
            <n-button @click="showDeclarePaymentModal = false">Cancel</n-button>
            <n-button type="primary" @click="handleDeclarePayment" :loading="isActionLoading === 'declarePayment'">Declare</n-button>
          </template>
        </n-modal>

        <n-modal v-model:show="showCancelModal" preset="dialog" title="Cancel Transaction">
          <n-input type="textarea" v-model:value="cancelReason" placeholder="Reason for cancellation (required)" />
          <template #action>
            <n-button @click="showCancelModal = false">Back</n-button>
            <n-button type="error" @click="handleCancelTransaction" :disabled="!cancelReason.trim()" :loading="isActionLoading === 'cancel'">Confirm Cancellation</n-button>
          </template>
        </n-modal>

        <n-modal v-model:show="showDisputeModal" preset="dialog" title="Dispute Transaction">
          <n-input type="textarea" v-model:value="disputeReason" placeholder="Reason for dispute (required)" />
          <template #action>
            <n-button @click="showDisputeModal = false">Back</n-button>
            <n-button type="error" @click="handleDisputeTransaction" :disabled="!disputeReason.trim()" :loading="isActionLoading === 'dispute'">Confirm Dispute</n-button>
          </template>
        </n-modal>

        <!-- Transaction Log -->
        <n-divider>Transaction History</n-divider>
        <TransactionTimelineLog v-if="currentTransaction && currentTransaction.logs" :transaction="currentTransaction" />
        <n-text v-else depth="3">No transaction logs available.</n-text>

      </div>
      <template #description>
        Processing transaction...
      </template>
    </n-spin>
  </n-card>
  <div v-else-if="isLoading">
     <n-spin size="large" />
  </div>
   <div v-else>
    <n-alert title="Transaction Not Found" type="warning">
      Could not load transaction details for this chat session.
    </n-alert>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs, watch } from 'vue'; // Keep necessary Vue imports
import type { Component, Ref } from 'vue'; // Keep necessary Vue type imports
import {
  NSteps, NStep, NIcon, NCollapse, NCollapseItem, NTimeline, NTimelineItem,
  NCard, NSpin, NButton, NModal, NInput, NAlert, NText, useMessage, NSpace, NDivider, NRadioGroup, NRadio
} from 'naive-ui'; // Consolidate Naive UI imports
import {
  PersonCircleOutline as UserIcon,
  CheckmarkCircleOutline as CheckmarkIcon,
  CloseCircleOutline as ErrorIcon, // Keep for terminal states if needed
  HourglassOutline as PendingIcon,
  InformationCircleOutline as InfoIcon,
  WarningOutline as WarningIcon, // Keep for terminal states if needed
  BanOutline as CancelIcon, // Keep for terminal states
  ShieldCheckmarkOutline as ShieldIcon, // Keep for terminal states
  DocumentTextOutline as TermsIcon,
  SwapHorizontalOutline as DesignateIcon,
  WalletOutline as PaymentIcon,
  ReceiptOutline as ReceiptIcon,
  AlertCircleOutline as DisputeIcon // Keep for terminal states
} from '@vicons/ionicons5'; // Consolidate icon imports

import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic';
import TransactionTimelineLog from './TransactionTimelineLog.vue';
import { TransactionStatusEnum } from '@/types/transaction';
import type { Transaction } from '@/types/transaction'; // Keep if used directly in template/script
import { useTransactionStore } from '@/stores/transactionStore'; // Keep for direct store usage if any

// Local type definitions (can be moved to a shared types file or within composable if only used there)
type StepStatus = 'wait' | 'process' | 'finish' | 'error';

interface SubStep {
  key: string;
  title: string;
  status: StepStatus;
  icon?: Component;
  description?: string;
  timestamp?: string | null;
  active?: boolean;
}

interface VisualStep {
  key: string;
  title: string;
  status: StepStatus;
  icon?: Component;
  description?: string;
  subSteps?: SubStep[];
  subStepsTitle?: string;
  isActive: boolean; 
}

const props = defineProps<{
  chatSessionId: string | null;
}>();

const { chatSessionId: propChatSessionId } = toRefs(props);
const naiveMessage = useMessage(); 

const transactionStore = useTransactionStore();
// Notification instance is already set globally by AppContent.vue
// Removed redundant setNotificationInstance call to prevent conflicts

const {
  currentTransaction,
  isLoading,
  storeError,
  isUserTurn,
  currentTxStatus, 
  userId,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isActionLoading,
  showDesignateModal,
  selectedFirstPayerId,
  showDeclarePaymentModal,
  paymentTrackingNumber,
  showCancelModal,
  cancelReason,
  showDisputeModal,
  disputeReason,
  isTerminalState,
  isUserTurnToAgree,
  canDesignateFirstPayer,
  isUserTurnForPayment,
  isUserTurnForConfirmation,
  canCancel,
  canDispute,
  isCurrentActionBlockedByTimer,
  handleAgreeToTerms,
  handleDesignateFirstPayer,
  handleDeclarePayment,
  handleConfirmReceipt,
  handleCancelTransaction,
  handleDisputeTransaction,
  clearStoreError,
  openDesignateModal,
  openDeclarePaymentModal,
  openCancelModal,
  openDisputeModal,
} = useTransactionFlowLogic(propChatSessionId, naiveMessage);

// --- UI Display Computeds (specific to this component's presentation) ---
const transactionDisplayId = computed(() => {
  if (!currentTransaction.value) return 'TXN-N/A';
  return `TXN-${currentTransaction.value.id.substring(0, 8).toUpperCase()}`;
});

const cardHeaderV2 = computed(() => {
  if (!currentTransaction.value || !currentTransaction.value.status) return 'Transaction Details';
  const s = currentTransaction.value.status;
  return s.charAt(0).toUpperCase() + s.slice(1).toLowerCase().replace(/_/g, ' ');
});

const statusDotColor = computed(() => {
  if (!currentTransaction.value) return 'grey';
  const status = currentTransaction.value.status;
  if (status === TransactionStatusEnum.COMPLETED) return '#63e2b7'; // success
  if (status === TransactionStatusEnum.CANCELLED || status === TransactionStatusEnum.DISPUTED) return '#f2c97d'; // warning
  if (isTimerExpired.value && isUserTurn.value) return '#e88080'; // error, if expired on user's turn
  if (isUserTurn.value) return '#5ea5ff'; // info, if user's turn and not not expired
  return '#d9d9d9'; // default grey for other states or not user's turn
});

const userRoleDisplay = computed(() => {
  if (!currentTransaction.value || !userId.value) return 'Observer';
  const tx = currentTransaction.value;
  if (userId.value === tx.currencyAProviderId) return `Role: ${tx.currencyA} Provider`;
  if (userId.value === tx.currencyBProviderId) return `Role: ${tx.currencyB} Provider`;
  return 'Observer';
});

const otherPartyUsername = computed(() => {
  if (!currentTransaction.value || !userId.value) return 'Other Party';
  const tx = currentTransaction.value;  if (userId.value === tx.currencyAProviderId) return tx.currencyBProvider?.username || 'Other User';
  if (userId.value === tx.currencyBProviderId) return tx.currencyAProvider?.username || 'Other User';
  return 'N/A';
});

// --- Stepper Logic (Visual Steps) ---
const getStepStatus = (targetStatus: TransactionStatusEnum[], currentStatus: TransactionStatusEnum | null | undefined, isCompleted: boolean = false): StepStatus => {
  if (!currentStatus) return 'wait';
  if (isCompleted || currentStatus === TransactionStatusEnum.COMPLETED) return 'finish';
  if (targetStatus.includes(currentStatus)) return 'process';
  
  const order = Object.values(TransactionStatusEnum);
  const currentIndex = order.indexOf(currentStatus);
  const targetMaxIndex = Math.max(...targetStatus.map(ts => order.indexOf(ts)));

  if (currentIndex > targetMaxIndex && targetMaxIndex !== -1) return 'finish'; // Current status is past this step
  return 'wait';
};

const visualSteps = computed<VisualStep[]>(() => {
  if (!currentTransaction.value) return [];
  const tx = currentTransaction.value;
  const status = tx.status;

  // Helper to create sub-steps for payment/confirmation
  const createPaymentSubSteps = (payerType: 'first' | 'second'): SubStep[] => {
    if (!tx.agreedFirstPayerId) return []; // Should not happen if we are in payment stages
    const isFirstPayer = payerType === 'first';
    // Determine which currency and user details to use based on who the 'firstPayer' agreed in the transaction is

    let paymentCurrency: string;    let paymentDeclarerId: string;
    let paymentConfirmerId: string;
    let paymentDeclarerUsername: string | null;
    let paymentConfirmerUsername: string | null;

    if (isFirstPayer) {
      // This is the first payment cycle, driven by agreedFirstPayerId
      paymentDeclarerId = tx.agreedFirstPayerId!;      if (tx.agreedFirstPayerId === tx.currencyAProviderId) {
        paymentCurrency = tx.currencyA;
        paymentDeclarerUsername = tx.currencyAProvider?.username || null;
        paymentConfirmerId = tx.currencyBProviderId;
        paymentConfirmerUsername = tx.currencyBProvider?.username || null;
      } else { // agreedFirstPayerId is currencyBProviderId
        paymentCurrency = tx.currencyB;
        paymentDeclarerUsername = tx.currencyBProvider?.username || null;
        paymentConfirmerId = tx.currencyAProviderId;
        paymentConfirmerUsername = tx.currencyAProvider?.username || null;
      }
    } else {
      // This is the second payment cycle, roles are swapped from the first payment
      paymentConfirmerId = tx.agreedFirstPayerId!;
      if (tx.agreedFirstPayerId === tx.currencyAProviderId) {
        paymentDeclarerId = tx.currencyBProviderId;
        paymentCurrency = tx.currencyB;        paymentDeclarerUsername = tx.currencyBProvider?.username || null;
        paymentConfirmerUsername = tx.currencyAProvider?.username || null;
      } else { // agreedFirstPayerId is currencyBProviderId
        paymentDeclarerId = tx.currencyAProviderId;
        paymentCurrency = tx.currencyA;        paymentDeclarerUsername = tx.currencyAProvider?.username || null;
        paymentConfirmerUsername = tx.currencyBProvider?.username || null;
      }
    }

    const paymentDeclaredTimestamp = isFirstPayer ? tx.paymentDeclaredAtPayer1 : tx.paymentDeclaredAtPayer2;
    const paymentConfirmedTimestamp = isFirstPayer ? tx.firstPaymentConfirmedByPayer2At : tx.secondPaymentConfirmedByPayer1At;
    
    const awaitingPaymentStatus = isFirstPayer ? TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT : TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT;
    const awaitingConfirmationStatus = isFirstPayer ? TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION : TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION;

    return [
      {
        key: `${payerType}-declare`,
        title: `Declare ${paymentCurrency} Sent by ${paymentDeclarerUsername}`,
        status: paymentDeclaredTimestamp ? 'finish' : (status === awaitingPaymentStatus && userId.value === paymentDeclarerId ? 'process' : 'wait'),
        icon: PaymentIcon,
        timestamp: paymentDeclaredTimestamp,
        active: status === awaitingPaymentStatus && userId.value === paymentDeclarerId && !paymentDeclaredTimestamp
      },
      {
        key: `${payerType}-confirm`,
        title: `Confirm ${paymentCurrency} Received by ${paymentConfirmerUsername}`,
        status: paymentConfirmedTimestamp ? 'finish' : (status === awaitingConfirmationStatus && userId.value === paymentConfirmerId ? 'process' : 'wait'),
        icon: ReceiptIcon,
        timestamp: paymentConfirmedTimestamp,
        active: status === awaitingConfirmationStatus && userId.value === paymentConfirmerId && !paymentConfirmedTimestamp
      }
    ];
  };
  
  const steps: VisualStep[] = [
    {
      key: 'agreement',
      title: 'Terms Agreement',
      icon: TermsIcon,
      status: getStepStatus([TransactionStatusEnum.PENDING_AGREEMENT], status, !!tx.termsAgreementTimestampPayer1 && !!tx.termsAgreementTimestampPayer2),
      description: `Both parties must agree to the transaction terms. Your currency: ${userId.value === tx.currencyAProviderId ? tx.currencyA : tx.currencyB}.`,
      isActive: status === TransactionStatusEnum.PENDING_AGREEMENT,
      subSteps: [
        {
          key: 'payer1-agree',
          title: `${tx.currencyAProvider?.username} Agrees (${tx.currencyA})`,
          status: tx.termsAgreementTimestampPayer1 ? 'finish' : (userId.value === tx.currencyAProviderId && status === TransactionStatusEnum.PENDING_AGREEMENT ? 'process' : 'wait'),
          timestamp: tx.termsAgreementTimestampPayer1,
          active: userId.value === tx.currencyAProviderId && status === TransactionStatusEnum.PENDING_AGREEMENT && !tx.termsAgreementTimestampPayer1
        },
        {
          key: 'payer2-agree',
          title: `${tx.currencyBProvider?.username} Agrees (${tx.currencyB})`,
          status: tx.termsAgreementTimestampPayer2 ? 'finish' : (userId.value === tx.currencyBProviderId && status === TransactionStatusEnum.PENDING_AGREEMENT ? 'process' : 'wait'),
          timestamp: tx.termsAgreementTimestampPayer2,
          active: userId.value === tx.currencyBProviderId && status === TransactionStatusEnum.PENDING_AGREEMENT && !tx.termsAgreementTimestampPayer2
        },
      ],
      subStepsTitle: 'Agreement Status'
    },
    {
      key: 'designation',
      title: 'Designate First Payer',
      icon: DesignateIcon,
      status: getStepStatus([TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION], status, !!tx.agreedFirstPayerId),
      description: `Parties decide who makes their payment first.`,
      isActive: status === TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION,
      subSteps: tx.agreedFirstPayerId ? [
        {
            key: 'designated',
            title: `Designated: ${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username}`,
            status: 'finish',
            icon: CheckmarkIcon,
            timestamp: tx.firstPayerDesignationTimestamp // Corrected: Use firstPayerDesignationTimestamp
        }
      ] : [],
      subStepsTitle: 'Designation Outcome'
    },
    {
      key: 'first-payment-cycle',
      title: `First Payment: ${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username} (pays ${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyA : tx.currencyB})`,
      icon: PaymentIcon,
      status: getStepStatus([
        TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT,
        TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION
      ], status, !!tx.paymentDeclaredAtPayer1 && !!tx.firstPaymentConfirmedByPayer2At),
      description: `${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username} sends ${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyA : tx.currencyB}, then ${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyBProvider?.username : tx.currencyAProvider?.username} confirms receipt.`,
      isActive: status === TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT || status === TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION,
      subSteps: createPaymentSubSteps('first'), 
      subStepsTitle: 'Payment & Confirmation'
    },
    {
      key: 'second-payment-cycle',
      title: `Second Payment: ${tx.agreedFirstPayerId !== tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username} (pays ${tx.agreedFirstPayerId !== tx.currencyAProviderId ? tx.currencyA : tx.currencyB})`,
      icon: PaymentIcon,
      status: getStepStatus([
        TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT,
        TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION
      ], status, !!tx.paymentDeclaredAtPayer2 && !!tx.secondPaymentConfirmedByPayer1At),
      description: `${tx.agreedFirstPayerId !== tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username} sends ${tx.agreedFirstPayerId !== tx.currencyAProviderId ? tx.currencyA : tx.currencyB}, then ${tx.agreedFirstPayerId === tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username} confirms receipt.`,
      isActive: status === TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT || status === TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION,
      subSteps: createPaymentSubSteps('second'),
      subStepsTitle: 'Payment & Confirmation'
    },
    {
      key: 'completion',
      title: 'Transaction Complete',
      icon: ShieldIcon,
      status: getStepStatus([TransactionStatusEnum.COMPLETED], status, status === TransactionStatusEnum.COMPLETED),
      description: 'Transaction successfully completed.',
      isActive: status === TransactionStatusEnum.COMPLETED
    }
  ];

  if (status === TransactionStatusEnum.CANCELLED) {
    return [{
      key: 'cancelled',
      title: 'Transaction Cancelled',
      icon: CancelIcon,
      status: 'error',
      description: `Cancelled by ${tx.cancelledByUserId === tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username || 'a user'}. Reason: ${tx.cancellationReason}`,
      isActive: true
    }];
  }
  if (status === TransactionStatusEnum.DISPUTED) {
    return [{
      key: 'disputed',
      title: 'Transaction Disputed',
      icon: DisputeIcon,
      status: 'error',
      description: `Disputed by ${tx.disputedByUserId === tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username || 'a user'}. Reason: ${tx.disputeReason || 'Not specified'}. Admin will review.`,
      isActive: true
    }];
  }

  return steps;
});

const currentVisualStepIndex = computed(() => {
  const activeStep = visualSteps.value.findIndex(step => step.isActive);
  return activeStep >= 0 ? activeStep + 1 : // NSteps is 1-indexed for current prop
    (isTerminalState.value ? visualSteps.value.length : 1); // Default to last if terminal, else first
});

const overallStepperStatus = computed<StepStatus>(() => {
  if (!currentTransaction.value) return 'wait';
  if (storeError.value) return 'error';
  const status = currentTransaction.value.status;
  if (status === TransactionStatusEnum.CANCELLED || status === TransactionStatusEnum.DISPUTED) return 'error';
  if (status === TransactionStatusEnum.COMPLETED) return 'finish';
  if (visualSteps.value.some(step => step.status === 'process')) return 'process';
  return 'wait';
});

const terminalStateDetails = computed(() => {
  if (!currentTransaction.value || !isTerminalState.value) return null;
  const tx = currentTransaction.value;
  switch (tx.status) {
    case TransactionStatusEnum.COMPLETED:
      return { title: 'Completed', message: 'This transaction has been successfully completed.', icon: CheckmarkIcon, type: 'success' as const };
    case TransactionStatusEnum.CANCELLED:
      return { title: 'Cancelled', message: `Cancelled by ${tx.cancelledByUserId === tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username || 'a user'}. Reason: ${tx.cancellationReason || 'Not specified'}.`, icon: CancelIcon, type: 'warning' as const };
    case TransactionStatusEnum.DISPUTED:
      return { title: 'Disputed', message: `Disputed by ${tx.disputedByUserId === tx.currencyAProviderId ? tx.currencyAProvider?.username : tx.currencyBProvider?.username || 'a user'}. Reason: ${tx.disputeReason || 'Not specified'}. Admin will review.`, icon: DisputeIcon, type: 'error' as const };
    default: return null;
  }
});

// Watch for errors from the store to display them
watch(storeError, (newError) => {
  if (newError) {
    // Notification is handled by the store's watcher or composable's direct use of naiveMessage
    // No need to call naiveMessage.error here if it's already handled.
  }
});

</script>

<style scoped>
.transaction-flow-card-v2 {
  margin-bottom: 20px;
}

/* Add any additional V2 specific styles here */
.n-step-content {
  font-size: 0.95em;
}
.n-step-description {
  font-size: 0.9em;
}

</style>
