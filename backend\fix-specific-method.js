const { PrismaClient } = require('@prisma/client');

async function fixSpecificInactiveDefault() {
  const prisma = new PrismaClient();
  
  try {
    console.log('=== Fixing Specific Inactive Default Method ===');
    
    const problemMethodId = 'cmck91tmw000dvl48co9fwf3h';
    
    // Update the specific problematic method
    const result = await prisma.paymentReceivingInfo.update({
      where: {
        id: problemMethodId
      },
      data: {
        isDefaultForUser: false
      }
    });
    
    console.log('✅ Successfully updated problematic method:');
    console.log(`- ID: ${result.id}`);
    console.log(`- Active: ${result.isActive}`);
    console.log(`- Default: ${result.isDefaultForUser}`);
    
    // Verify the fix
    const verification = await prisma.paymentReceivingInfo.findMany({
      where: {
        userId: 'cmbqrmwcv0001vl48wlfffva4',
        currency: 'CAD'
      }
    });
    
    console.log('\n=== Verification ===');
    verification.forEach(method => {
      console.log(`- ID: ${method.id.slice(-8)}, Active: ${method.isActive}, Default: ${method.isDefaultForUser}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixSpecificInactiveDefault();
