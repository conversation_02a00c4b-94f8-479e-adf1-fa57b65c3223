import apiClient from './apiClient';
import { useClientLogger } from '@/composables/useClientLogger';
import type { 
  MatchesResponse, 
  MatchActionResponse, 
  MatchesQuery, 
  DeclineMatchRequest
} from '@/types/api';
import { MatchStatus } from '@/types/api';

const logger = useClientLogger();

export const matchService = {
  /**
   * Fetch all matches for the current user
   */
  async fetchMatches(query?: MatchesQuery): Promise<MatchesResponse> {
    logger.logInfo('Fetching user matches', { query });
    
    try {
      const params = new URLSearchParams();
      if (query?.status) params.append('status', query.status);
      if (query?.limit) params.append('limit', query.limit.toString());
      if (query?.offset) params.append('offset', query.offset.toString());
      
      const queryString = params.toString();
      const url = queryString ? `/matches?${queryString}` : '/matches';
      
      const response = await apiClient.get<MatchesResponse>(url);
      logger.logInfo('Matches fetched successfully', { 
        count: response.data.matches.length,
        total: response.data.total 
      });
      
      return response.data;
    } catch (error) {
      logger.logError('Failed to fetch matches', error, { query });
      throw error;
    }
  },
  /**
   * Accept a match
   */
  async acceptMatch(matchId: string): Promise<MatchActionResponse> {
    logger.logInfo('Accepting match', { matchId });
    
    try {
      const response = await apiClient.post<MatchActionResponse>(`/matches/${matchId}/accept`, {});
      logger.logInfo('Match accepted successfully', {
        matchId,
        success: response.data.success,
        chatSessionId: response.data.chatSessionId,
        transactionId: response.data.transactionId
      });
      
      return response.data;
    } catch (error) {
      logger.logError('Failed to accept match', error, { matchId });
      throw error;
    }
  },
  /**
   * Decline a match
   */
  async declineMatch(matchId: string, request?: DeclineMatchRequest): Promise<MatchActionResponse> {
    logger.logInfo('Declining match', { matchId, reason: request?.reason });
    
    try {
      const response = await apiClient.post<MatchActionResponse>(`/matches/${matchId}/decline`, request || {});
      logger.logInfo('Match declined successfully', { 
        matchId,
        success: response.data.success 
      });
      
      return response.data;
    } catch (error) {
      logger.logError('Failed to decline match', error, { matchId, request });
      throw error;
    }
  },
  /**
   * Get matches filtered by status
   */
  getMatchesByStatus: async (status: MatchStatus): Promise<MatchesResponse> => {
    return matchService.fetchMatches({ status });
  },

  /**
   * Get pending matches that need user response
   */
  getPendingMatches: async (): Promise<MatchesResponse> => {
    return matchService.fetchMatches({ status: MatchStatus.PENDING });
  }
};
