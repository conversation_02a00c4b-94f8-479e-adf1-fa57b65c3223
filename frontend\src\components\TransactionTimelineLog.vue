<template>
  <div v-if="transaction" class="transaction-timeline-log">
    <n-timeline>
      <n-timeline-item
        v-if="transaction.createdAt"
        type="info"
        :title="t('transactionTimeline.transactionInitiated')"
        :time="formatDate(transaction.createdAt)"
      />
      <n-timeline-item
        v-if="transaction.termsAgreementTimestampPayer1"
        type="success"
        :title="t('transactionTimeline.agreedToTerms', { username: transaction.currencyAProvider?.username })"
        :time="formatDate(transaction.termsAgreementTimestampPayer1)"
      />
      <n-timeline-item
        v-if="transaction.termsAgreementTimestampPayer2"
        type="success"
        :title="t('transactionTimeline.agreedToTerms', { username: transaction.currencyBProvider?.username })"
        :time="formatDate(transaction.termsAgreementTimestampPayer2)"
      />
      <n-timeline-item
        v-if="transaction.agreedFirstPayerId && transaction.firstPayerDesignationTimestamp"
        type="info"
        :title="t('transactionTimeline.firstPayerDesignated', { username: firstPayerUsername })"
        :content="t('transactionTimeline.firstPayerDesignatedContent', { username: firstPayerUsername })"
        :time="formatDate(transaction.firstPayerDesignationTimestamp)"
      />
      <n-timeline-item
        v-if="transaction.paymentDeclaredAtPayer1"
        type="success"
        :title="t('transactionTimeline.declaredPayment', { username: firstPayerUsername })"
        :content="transaction.paymentTrackingNumberPayer1 ? t('transactionTimeline.trackingInfo', { trackingInfo: transaction.paymentTrackingNumberPayer1 }) : t('transactionTimeline.noTrackingInfo')"
        :time="formatDate(transaction.paymentDeclaredAtPayer1)"
      />
      <n-timeline-item
        v-if="transaction.firstPaymentConfirmedByPayer2At" 
        type="success"
        :title="t('transactionTimeline.confirmedReceipt', { username: secondPayerUsername })"
        :time="formatDate(transaction.firstPaymentConfirmedByPayer2At)"
      />
      <n-timeline-item
        v-if="transaction.paymentDeclaredAtPayer2"
        type="success"
        :title="t('transactionTimeline.declaredPayment', { username: secondPayerUsername })"
        :content="transaction.paymentTrackingNumberPayer2 ? t('transactionTimeline.trackingInfo', { trackingInfo: transaction.paymentTrackingNumberPayer2 }) : t('transactionTimeline.noTrackingInfo')"
        :time="formatDate(transaction.paymentDeclaredAtPayer2)"
      />      <n-timeline-item
        v-if="transaction.secondPaymentConfirmedByPayer1At"
        type="success"
        :title="t('transactionTimeline.confirmedFinalReceipt', { username: firstPayerUsername })"
        :time="formatDate(transaction.secondPaymentConfirmedByPayer1At)"
      />
      <n-timeline-item
        v-if="transaction.status === TransactionStatusEnum.COMPLETED"
        type="success"
        :title="t('transactionTimeline.transactionCompleted')"
        :time="formatDate(transaction.updatedAt)" 
      />
      <n-timeline-item
        v-if="transaction.status === TransactionStatusEnum.CANCELLED"
        type="error"
        :title="t('transactionTimeline.transactionCancelled', { username: cancelledOrDisputedByUsername(transaction.cancelledByUserId) })"
        :content="transaction.cancellationReason ? t('transactionTimeline.reason', { reason: transaction.cancellationReason }) : t('transactionTimeline.noReason')"
        :time="formatDate(transaction.updatedAt)"
      />
      <n-timeline-item
        v-if="transaction.status === TransactionStatusEnum.DISPUTED"
        type="warning"
        :title="t('transactionTimeline.transactionDisputed', { username: cancelledOrDisputedByUsername(transaction.disputedByUserId) })"
        :content="transaction.disputeReason ? t('transactionTimeline.reason', { reason: transaction.disputeReason }) : t('transactionTimeline.noReason')"
        :time="formatDate(transaction.updatedAt)"
      />
    </n-timeline>
  </div>
  <div v-else>
    <n-empty :description="t('transactionTimeline.noTransactionData')" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NTimeline, NTimelineItem, NEmpty } from 'naive-ui';
import { useTranslation } from '@/composables/useTranslation';
import type { Transaction } from '@/types/transaction';
import { TransactionStatusEnum } from '@/types/transaction';

const { t } = useTranslation();

const props = defineProps<{
  transaction: Transaction | null;
}>();

function formatDate(dateString: string | null | undefined): string {
  if (!dateString) return t('transactionTimeline.notAvailable');
  return new Date(dateString).toLocaleString();
}

const firstPayerUsername = computed(() => {
  if (!props.transaction || !props.transaction.agreedFirstPayerId) return t('transactionTimeline.notAvailable');
  const tx = props.transaction;
  if (tx.agreedFirstPayerId === tx.currencyAProviderId) return tx.currencyAProvider?.username || t('transactionTimeline.notAvailable');
  if (tx.agreedFirstPayerId === tx.currencyBProviderId) return tx.currencyBProvider?.username || t('transactionTimeline.notAvailable');
  return t('transactionTimeline.notAvailable');
});

const secondPayerUsername = computed(() => {
  if (!props.transaction || !props.transaction.agreedFirstPayerId) return t('transactionTimeline.notAvailable');
  const tx = props.transaction;
  if (tx.agreedFirstPayerId === tx.currencyAProviderId) return tx.currencyBProvider?.username || t('transactionTimeline.notAvailable');
  if (tx.agreedFirstPayerId === tx.currencyBProviderId) return tx.currencyAProvider?.username || t('transactionTimeline.notAvailable');
  return t('transactionTimeline.notAvailable');
});

const cancelledOrDisputedByUsername = (userId?: string | null) => {
  if (!userId || !props.transaction) return t('transactionTimeline.unknownUser');
  if (userId === props.transaction.currencyAProviderId) return props.transaction.currencyAProvider?.username || t('transactionTimeline.unknownUser');
  if (userId === props.transaction.currencyBProviderId) return props.transaction.currencyBProvider?.username || t('transactionTimeline.unknownUser');
  return t('transactionTimeline.unknownUser');
};
</script>

<style scoped>
.transaction-timeline-log {
  margin-top: 20px;
}
</style>
