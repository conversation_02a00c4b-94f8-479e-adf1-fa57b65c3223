const { PrismaClient } = require('./backend/node_modules/@prisma/client');

async function checkConstraintIssues() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Checking constraint issues for user: cmbqrmwcv0001vl48wlfffva4');
    
    // Check all records for this user
    const allRecords = await prisma.paymentReceivingInfo.findMany({
      where: { userId: 'cmbqrmwcv0001vl48wlfffva4' },
      select: {
        id: true,
        currency: true,
        isDefaultForUser: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log('\nAll records for user:');
    console.table(allRecords);
    
    // Check specifically for CAD records
    const cadRecords = await prisma.paymentReceivingInfo.findMany({
      where: { 
        userId: 'cmbqrmwcv0001vl48wlfffva4',
        currency: 'CAD'
      },
      select: {
        id: true,
        currency: true,
        isDefaultForUser: true,
        isActive: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log('\nCAD records for user:');
    console.table(cadRecords);
    
    // Check for constraint violations - multiple defaults
    const constraintViolations = await prisma.paymentReceivingInfo.groupBy({
      by: ['userId', 'currency', 'isDefaultForUser'],
      where: {
        isDefaultForUser: true
      },
      _count: {
        id: true
      },
      having: {
        id: {
          _count: {
            gt: 1
          }
        }
      }
    });
    
    console.log('\nConstraint violations (multiple defaults):');
    console.table(constraintViolations);
    
  } catch (error) {
    console.error('Error checking constraint issues:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkConstraintIssues();
