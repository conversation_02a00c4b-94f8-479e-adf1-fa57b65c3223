Okay, I will update the Feature Requirements Document (FRD) to incorporate this "Payment Readiness Gate" approach. The key changes will be in sections related to UI flow, backend state, and the prerequisites for engaging in the negotiation.

---

**Feature Requirements Document: First Payer Designation (Version 2)**

**1. Introduction & Goals** (No changes from previous FRD)

*   **1.1. Feature Name:** First Payer Designation
*   **1.2. Goal:** To provide a clear, intuitive, and robust system allowing two parties in a transaction (Party A and Party B) to asynchronously agree on which of them will make the first payment. The system must ensure necessary payment *receiving* information is on file from both parties before negotiation, clearly track the negotiation state, handle user actions, and seamlessly transition to the payment phase once an agreement is reached.
*   **1.3. User Problem:** (No changes)
*   **1.4. Scope:** (No changes)

**2. Key Terminology** (No changes from previous FRD)

**3. User Roles & Personas** (No changes from previous FRD)

**4. Functional Requirements**

*   **4.1. State Management (Backend)**
    *   **4.1.1. Negotiation Record:**
        *   A dedicated data entity (e.g., `PayerNegotiation`) must be created for each transaction requiring first payer designation.
        *   It must store:
            *   `negotiationId` (PK)
            *   `transactionId` (FK)
            *   `partyA_Id`
            *   `partyB_Id`
            *   `partyA_receivingInfoStatus` (Enum: `PENDING_INPUT`, `PROVIDED`, `CONFIRMED_FROM_PROFILE`)
            *   `partyB_receivingInfoStatus` (Enum: `PENDING_INPUT`, `PROVIDED`, `CONFIRMED_FROM_PROFILE`)
            *   *(Secure storage for actual receiving info, potentially in a separate related table or linked to user profiles, with appropriate encryption and access controls. This FRD assumes such a mechanism exists.)*
            *   `systemRecommendedPayerId` (User ID: Party A or Party B)
            *   `currentProposal_PayerId` (User ID: Party A or Party B)
            *   `currentProposal_ById` (User ID: Party A, Party B, or 'system')
            *   `partyA_agreedToProposal_PayerId` (User ID, nullable)
            *   `partyA_agreedToProposal_ById` (User ID or 'system', nullable)
            *   `partyB_agreedToProposal_PayerId` (User ID, nullable)
            *   `partyB_agreedToProposal_ById` (User ID or 'system', nullable)
            *   `finalizedPayerId` (User ID: Party A or Party B, nullable)
            *   `negotiationStatus` (Enum: `AWAITING_PARTY_A_RECEIVING_INFO`, `AWAITING_PARTY_B_RECEIVING_INFO`, `READY_TO_NEGOTIATE`, `PENDING_RESPONSE`, `FINALIZED`, `EXPIRED` (if applicable))
            *   `paymentTimerDueDate` (Timestamp, nullable) - Set when agreement is finalized.
            *   `createdAt`, `updatedAt` timestamps.
    *   **4.1.2. Payment Receiving Information Prerequisite:**
        *   A party can access and interact with the "First Payer Designation" negotiation UI as soon as *they* have provided or confirmed their payment receiving information (`partyX_receivingInfoStatus` is `PROVIDED` or `CONFIRMED_FROM_PROFILE`), regardless of the other party's status.
        *   The backend must store/link the provided payment receiving details securely.
        *   Actions that require the other party's payment receiving info (e.g., finalizing the payer and revealing recipient details) remain gated until both parties have provided their info.
    *   **4.1.3. Initialization:**
        *   Upon creation of the `PayerNegotiation` record, `negotiationStatus` is set based on whether `partyA_receivingInfoStatus` and `partyB_receivingInfoStatus` are already `PROVIDED` (e.g., from profile defaults for this transaction type). If Party A needs to provide, status is `AWAITING_PARTY_A_RECEIVING_INFO`.
        *   Once both parties have `PROVIDED` or `CONFIRMED_FROM_PROFILE` their receiving info, the system determines `systemRecommendedPayerId`.
        *   The initial `currentProposal_PayerId` and `currentProposal_ById` must be set to reflect this system recommendation.
        *   `negotiationStatus` transitions to `READY_TO_NEGOTIATE` (or directly to `PENDING_RESPONSE` if the system recommendation is active).
    *   **4.1.4. Proposal Logic:** (Largely unchanged, but assumes `READY_TO_NEGOTIATE` status met)
        *   When a user makes a proposal or counter-proposal:
            *   `currentProposal_PayerId` is updated.
            *   `currentProposal_ById` is updated.
            *   Any existing `agreedToProposal_` entries for *both* users that do not match the new `currentProposal` are considered outdated.
            *   `negotiationStatus` set to `PENDING_RESPONSE`.
            *   Trigger notification to the other party.
    *   **4.1.5. Agreement Logic:** (Largely unchanged, but assumes `READY_TO_NEGOTIATE` status met)
        *   When a user agrees to the `currentProposal`:
            *   Their respective `agreedToProposal_` fields are updated.
            *   The system checks if *both* `partyA_agreedToProposal_` and `partyB_agreedToProposal_` now match the `currentProposal`.
                *   If YES:
                    *   `finalizedPayerId` is set.
                    *   `negotiationStatus` set to `FINALIZED`.
                    *   `paymentTimerDueDate` is calculated.
                    *   Trigger notifications.
                *   If NO:
                    *   `negotiationStatus` remains `PENDING_RESPONSE`.
                    *   Trigger notification.
    *   **4.1.6. Atomicity:** (No changes)

*   **4.2. User Interface (UI) & User Experience (UX)**
    *   **4.2.1. "Payment Readiness Gate" UI (Initial View for Each Party):**
        *   When a user first accesses the "First Payer Designation" component for a transaction, if their `partyX_receivingInfoStatus` is `PENDING_INPUT`:
            *   Display a prominent message: "To proceed, please provide/confirm your payment *receiving* details. This information will be shared with the other party if they are designated to pay you."
            *   **Input Section for Receiving Details:**
                *   If details exist in user's profile: "Use saved details: [Masked details]?" `[ Use these ]` `[ Provide new/different details for this transaction ]`
                *   If no profile details: Form to input necessary payment receiving information (e.g., bank name, account number, recipient name).
                *   Checkbox: `[ ] Save these details to my profile for future use.`
                *   Button: `[ Save and Continue to Negotiation ]` or `[ Confirm Details and Continue ]`
            *   The main negotiation UI (4.2.2 onwards) is disabled or hidden for this user until this step is completed.
        *   Once a user has provided their info, they can see and interact with the negotiation UI, regardless of whether the other party has provided theirs.
        *   If the other party has not yet provided their info, certain actions (such as finalizing the payer or revealing recipient details) will remain unavailable or display a message indicating the other party's readiness is pending.
    *   **4.2.2. General Layout (Post-Readiness Gate):** (No changes)
    *   **4.2.3. Current Proposal Display:** (No changes)
    *   **4.2.4. Action Buttons Area:** (No changes, assumes readiness gate passed)
    *   **4.2.5. Agreement Status & Next Steps Display:** (No changes)
    *   **4.2.6. Payment Due Display (Post-Agreement):**
        *   If `finalizedPayerId` is set (e.g., Party A is to pay Party B):
            *   **For the Finalized Payer (Party A):**
                *   "AGREEMENT REACHED! You will pay first."
                *   "Please use the following details to pay **\[Party B's Name]**: **\[Party B's full, unmasked receiving details, retrieved from backend]**."
                *   "PAYMENT DUE: Make payment by **\[Date/Time (derived from `paymentTimerDueDate`)]**." Show a countdown timer.
            *   **For the Other Party (Party B - Recipient):**
                *   "AGREEMENT REACHED! **\[Party A's Name]** will pay first."
                *   "They have been provided with your payment details: **\[Party B's masked receiving details, for confirmation]**."
                *   "Waiting for **\[Party A's Name]** to make payment (due by **\[Date/Time]**)."
    *   **4.2.7. Counter-Offer Mechanism:** (No changes)
    *   **4.2.8. UI Updates:** (No changes)
    *   **4.2.9. Editing Receiving Details (Mid-Negotiation):**
        *   Users must have a way to view and edit their provided *receiving* details for the current transaction even after the initial "Readiness Gate" if no final agreement has been reached.
        *   If details are edited, the system should note this. If the other party is designated to pay them, they will receive the updated details upon finalization.

*   **4.3. Notifications (Asynchronous Support)**
    *   **4.3.1. Payment Details Required (Initial):** If a user needs to provide payment details to proceed.
        *   Content: "Action Required for Transaction #XYZ: Please provide your payment receiving details to participate in deciding who pays first."
    *   **4.3.2. Other Party Provided Details:** When User A provides their details, and User B still needs to.
        *   Content: "[User A] is ready for the 'First Payer Designation' step in Transaction #XYZ. Please provide your payment receiving details to proceed."
    *   **4.3.3. New Proposal Made:** (No changes)
    *   **4.3.4. One Party Agrees:** (No changes)
    *   **4.3.5. Agreement Finalized (Notification to Payer):**
        *   Content: "ACTION REQUIRED: Payment Due! You've been designated to pay first for Transaction #XYZ. Payment is due by \[Date/Time]. [Other Party's Name]'s payment details are available in the transaction view."
    *   **4.3.6. Agreement Finalized (Notification to Non-Payer):** (No changes)
    *   **4.3.7. Notification Delivery:** (No changes)

*   **4.4. Payment Timer Initiation**
    *   **4.4.1. Start Trigger:** The `paymentTimerDueDate` is calculated and stored on the backend immediately when `finalizedPayerId` is set. This occurs only *after* both parties have successfully passed the "Payment Readiness Gate" and then reached mutual agreement on the payer.
    *   **4.4.2. Duration:** (No changes)

**5. API Endpoints (Illustrative - Backend Team to Finalize)**

*   `POST /api/transactions/{transactionId}/payer-negotiation/receiving-info`:
    *   Authenticated user.
    *   Request Body: `{ paymentDetails: { ... }, saveToProfile: true/false }`
    *   Updates `partyX_receivingInfoStatus` and stores details. Checks if both parties are now ready and updates `negotiationStatus`.
*   `GET /api/transactions/{transactionId}/payer-negotiation`: (No changes)
*   `POST /api/transactions/{transactionId}/payer-negotiation/propose`: (No changes, but precondition that user's `receivingInfoStatus` is `PROVIDED`)
*   `POST /api/transactions/{transactionId}/payer-negotiation/agree`: (No changes, but precondition that user's `receivingInfoStatus` is `PROVIDED`)

**6. Non-Functional Requirements** (No changes from previous FRD, but emphasize secure storage and handling of payment receiving information)

*   **6.1. Performance**
*   **6.2. Scalability**
*   **6.3. Reliability**
*   **6.4. Security:**
    *   Strict security measures for storing and transmitting payment receiving information (encryption at rest and in transit, access controls, consider tokenization if integrating with payment gateways). Adherence to relevant standards (e.g., PCI DSS if card data were involved, though this example focuses on bank details).
    *   Only authenticated users involved in the transaction can view or modify its negotiation state.
    *   Input validation on all API endpoints.
*   **6.5. Usability**

**7. Edge Cases & Considerations**

*   **7.1. User Refuses to Provide Receiving Info:** The negotiation cannot proceed for that transaction. The UI should clearly state this. The transaction might stall or follow an alternative path defined by business rules.
*   **7.2. Simultaneous Actions:** (No changes)
*   **7.3. User Abandons Transaction:** (No changes)
*   **7.4. Changing Mind After Proposing (Pre-Agreement):** (No changes)
*   **7.5. Notification Failures:** (No changes)

**8. Future Considerations (Out of Scope for MVP)** (No changes from previous FRD)

**9. Acceptance Criteria (Summary)**

*   **A user must provide/confirm their payment *receiving* details before they can actively participate in the payer designation negotiation.**
*   As soon as a user has provided their info, they can see and interact with the negotiation UI, regardless of the other party's readiness.
*   Users can see the system's initial recommendation for the first payer (once both have provided info).
*   Users can propose themselves or the other party to be the first payer.
*   Users can agree to the current proposal.
*   The UI clearly reflects the current proposal, who made it, and the agreement status of both parties.
*   Once both parties agree to the same proposal and both have provided their payment info, the first payer is finalized.
*   **The finalized payer is immediately shown the recipient's payment *receiving* details (once both have provided).**
*   The finalized payer is clearly notified and sees a payment timer.
*   The other party is notified of the final agreement and sees who is due to pay.
*   Notifications are sent for key actions.
*   The system correctly handles asynchronous interactions.

---

This updated FRD now centralizes the payment information handling as a prerequisite ("Payment Readiness Gate"), which should lead to a smoother and fairer process once the actual negotiation and payment timer begin.

The plan:
Okay, I will generate a detailed implementation plan for the "First Payer Designation" feature, including the "Payment Readiness Gate," based on the provided `first-payer-gate.md` document and considering its integration into the existing `TransactionFlowCardV3.vue` component or a similar structure.

## Implementation Plan: First Payer Designation & Payment Readiness Gate

**1. Understanding the Requirement**

*   **Summary:** Implement a system where two parties in a transaction can agree on who makes the first payment. This involves a "Payment Readiness Gate" ensuring both parties have provided necessary payment *receiving* information before negotiation can begin. The system will track negotiation states, handle proposals and agreements, and integrate with the overall transaction flow.
*   **Key Objectives & Acceptance Criteria (from `first-payer-gate.md`):**
    *   Create a `PayerNegotiation` entity to track the state.
    *   Parties must provide/confirm payment receiving information before negotiation (`Payment Readiness Gate`).
    *   The system can recommend a first payer.
    *   Users can propose and agree on the first payer.
    *   The negotiation status must be clearly tracked (e.g., `AWAITING_RECEIVING_INFO`, `READY_TO_NEGOTIATE`, `PENDING_RESPONSE`, `FINALIZED`).
    *   Real-time updates for negotiation progress.
    *   Seamless transition to the payment phase once finalized.

**2. Impact Analysis & Affected Components**

*   **Backend (backend)**
    *   **Prisma Schema (`prisma/schema.prisma`):**
        *   New model: `PayerNegotiation` (as per FRD 4.1.1), including fields like `transactionId`, party IDs, receiving info statuses, proposals, `finalizedPayerId`, `negotiationStatus`, timestamps.
        *   New enums: `ReceivingInfoStatus` (e.g., `PENDING_INPUT`, `PROVIDED`, `CONFIRMED_FROM_PROFILE`), `NegotiationStatus` (e.g., `AWAITING_PARTY_A_RECEIVING_INFO`, `READY_TO_NEGOTIATE`, `PENDING_RESPONSE`, `FINALIZED`, `EXPIRED`).
        *   Possible modifications to `Transaction` model: Add a relation to `PayerNegotiation` or fields to track this phase.
        *   Possible new model for `UserPaymentReceivingInfo` if not already part of user profiles, to securely store this information.
    *   **Routes (`src/routes/`):**
        *   New route file (e.g., `negotiationRoutes.ts` or integrate into `transactionRoutes.ts`).
        *   Endpoints for:
            *   Fetching/initializing `PayerNegotiation` state for a transaction.
            *   Submitting/confirming payment receiving information.
            *   Making a proposal for the first payer.
            *   Agreeing to a proposal.
            *   (Potentially) Getting system recommendation.
    *   **Services (`src/services/`):**
        *   New service (e.g., `payerNegotiationService.ts`).
        *   Logic for:
            *   Initializing `PayerNegotiation` records.
            *   Validating and updating `partyX_receivingInfoStatus`.
            *   Determining `systemRecommendedPayerId` (logic needs to be defined).
            *   Processing proposals and counter-proposals.
            *   Processing agreements and finalizing `finalizedPayerId`.
            *   Updating `negotiationStatus`.
            *   Managing `paymentTimerDueDate` upon finalization.
    *   **Middleware (`src/middleware/auth.ts`):**
        *   Ensure `authMiddleware` is applied to all new negotiation-related endpoints.
    *   **Socket.IO (`src/index.ts`, `src/services/`):**
        *   Define new socket events (in `src/types/socketEvents.ts`).
        *   Emit events from `payerNegotiationService.ts` for:
            *   `PAYER_NEGOTIATION_UPDATED` (generic update with current state).
            *   `RECEIVING_INFO_REQUIRED`.
            *   `RECEIVING_INFO_SUBMITTED`.
            *   `PROPOSAL_MADE`.
            *   `AGREEMENT_REACHED`.
    *   **Types (`src/types/`):**
        *   Zod schemas for API request bodies and responses related to negotiation.
        *   TypeScript interfaces for `PayerNegotiation` and related data.

*   **Frontend (frontend)**
    *   **Components (`src/components/`):**
        *   Likely modifications to `TransactionFlowCardV3.vue` to include:
            *   A "Payment Readiness Gate" section (conditional display).
            *   A negotiation UI section (proposals, agreement buttons, status display).
        *   Or, create new sub-components managed by `TransactionFlowCardV3.vue`:
            *   `PaymentReadinessGate.vue`
            *   `FirstPayerNegotiation.vue`
    *   **Stores (`src/stores/`):**
        *   New Pinia store (e.g., `payerNegotiationStore.ts`).
        *   State: Current `PayerNegotiation` details, loading states, error states.
        *   Actions:
            *   Fetch/initialize negotiation state.
            *   Submit payment receiving information.
            *   Make a proposal.
            *   Agree to a proposal.
        *   Getters: Computed properties for UI display (e.g., `isReadyToNegotiate`, `isUserTurn`).
    *   **Services (`src/services/`):**
        *   New service (e.g., `negotiationService.ts`).
        *   Functions to call the new backend API endpoints.
    *   **Composables (`src/composables/`):**
        *   The existing `useTransactionFlowLogic.ts` will need significant updates to manage the state and actions for this new negotiation phase, or a new composable `usePayerNegotiationLogic.ts` could be created and integrated.
    *   **Router (`src/router/index.ts`):**
        *   No direct changes expected unless this flow is accessed via a new dedicated route.
    *   **Types (`src/types/`):**
        *   Interfaces for `PayerNegotiation` state, API responses, socket event payloads.
        *   Update `TransactionStatusEnum` if new overall transaction statuses are introduced due to this phase.
    *   **Socket.IO (`src/services/socketService.ts`):**
        *   Add listeners for the new backend socket events.
        *   Update `payerNegotiationStore.ts` based on received events.

**3. Detailed Step-by-Step Implementation Strategy**

*   **Phase 1: Backend Development**
    1.  **Database Schema (Prisma):**
        *   Define and add the `PayerNegotiation` model to `schema.prisma`. Include all fields from FRD 4.1.1 (`negotiationId`, `transactionId`, `partyA_Id`, `partyB_Id`, `partyA_receivingInfoStatus`, `partyB_receivingInfoStatus`, `systemRecommendedPayerId`, `currentProposal_PayerId`, `currentProposal_ById`, agreement fields, `finalizedPayerId`, `negotiationStatus`, `paymentTimerDueDate`, `createdAt`, `updatedAt`).
        *   Define `ReceivingInfoStatus` and `NegotiationStatus` enums in `schema.prisma`.
        *   Consider if/how `UserPaymentReceivingInfo` should be modeled if not already available. For MVP, this might be simplified or linked to existing user profile fields if suitable.
        *   Update the `Transaction` model to link to `PayerNegotiation` (e.g., a one-to-one or one-to-many if a transaction could have multiple negotiation attempts, though FRD implies one).
        *   Run `npx prisma migrate dev --name add_payer_negotiation_table` to create migrations.
    2.  **API Design & Zod Schemas (Types & Routes):**
        *   Define Zod schemas for:
            *   Request to submit/confirm receiving info.
            *   Request to make a proposal (e.g., `{ proposedPayerId: string }`).
            *   Request to agree to the current proposal.
            *   Response for `PayerNegotiation` state.
        *   In `src/types/`, create TypeScript interfaces based on these Zod schemas and the Prisma model.
    3.  **API Endpoints (Hono Routes):**
        *   Create `negotiationRoutes.ts` or add to `transactionRoutes.ts`.
        *   Protect all routes with `authMiddleware`.
        *   Implement endpoints:
            *   `POST /api/transactions/:transactionId/negotiation/init` (or GET to fetch/create if not exists).
            *   `POST /api/transactions/:transactionId/negotiation/receiving-info` (body: receiving info details or confirmation).
            *   `POST /api/transactions/:transactionId/negotiation/propose` (body: Zod schema for proposal).
            *   `POST /api/transactions/:transactionId/negotiation/agree`
            *   `GET /api/transactions/:transactionId/negotiation` (to get current state).
    4.  **Business Logic (Services):**
        *   Create `payerNegotiationService.ts`.
        *   Implement functions:
            *   `initializeOrGetNegotiation(transactionId, userId)`: Creates/fetches `PayerNegotiation`. Sets initial `negotiationStatus` based on `partyX_receivingInfoStatus` (FRD 4.1.3). Determines and sets `systemRecommendedPayerId` and initial proposal.
            *   `submitReceivingInfo(negotiationId, userId, info)`: Updates `partyX_receivingInfoStatus`. If both parties provided info, transitions `negotiationStatus` to `READY_TO_NEGOTIATE` (or `PENDING_RESPONSE` if system proposal is active).
            *   `makeProposal(negotiationId, userId, proposedPayerId)`: Validates user's ability to propose. Updates `currentProposal_PayerId`, `currentProposal_ById`. Resets agreement fields. Sets `negotiationStatus` to `PENDING_RESPONSE`. (FRD 4.1.4)
            *   `agreeToProposal(negotiationId, userId)`: Updates user's `agreedToProposal_` fields. Checks if both parties agree to the `currentProposal`. If yes, sets `finalizedPayerId`, updates `negotiationStatus` to `FINALIZED`, sets `paymentTimerDueDate`. (FRD 4.1.5)
            *   Helper to determine `systemRecommendedPayerId` (e.g., based on offer creator, transaction type, or other business rule - this rule needs to be defined).
    5.  **Socket.IO Integration (Backend):**
        *   In `src/types/socketEvents.ts`, define event names (e.g., `NEGOTIATION_STATE_CHANGE`, `PROPOSAL_RECEIVED`, `NEGOTIATION_FINALIZED`) and their payload types.
        *   In `payerNegotiationService.ts`, after key state changes (info submission, proposal, agreement, finalization), emit socket events to relevant users (e.g., both parties in the transaction). The payload should contain the updated `PayerNegotiation` state or specific event details.

*   **Phase 2: Frontend Development**
    1.  **API Service (`src/services/negotiationService.ts`):**
        *   Create functions to call the backend endpoints defined in Phase 1 (e.g., `getNegotiationState`, `submitReceivingInfo`, `makeProposal`, `agreeToProposal`). Use `apiClient`.
    2.  **Pinia Store (`src/stores/payerNegotiationStore.ts`):**
        *   Define state: `currentNegotiation: PayerNegotiation | null`, `isLoading: boolean`, `error: string | null`.
        *   Define actions:
            *   `fetchNegotiation(transactionId)`: Calls API service, updates state.
            *   `provideReceivingInfo(transactionId, info)`: Calls API, updates state.
            *   `proposeFirstPayer(transactionId, proposedPayerId)`: Calls API, updates state.
            *   `acceptCurrentProposal(transactionId)`: Calls API, updates state.
            *   Actions to update state based on socket events.
        *   Define getters: `isPaymentGatePassedForCurrentUser`, `canCurrentUserPropose`, `canCurrentUserAgree`, `currentSystemProposal`, `finalizedPayer`, etc.
    3.  **Socket.IO Integration (Frontend - `src/services/socketService.ts` & Store):**
        *   In `socketService.ts`, add listeners for the new negotiation-related socket events.
        *   When events are received, dispatch actions in `payerNegotiationStore.ts` to update the state.
    4.  **UI Components (`TransactionFlowCardV3.vue` or new sub-components):**
        *   **Payment Readiness Gate UI (FRD 4.2.1):**
            *   Conditionally display if `currentNegotiation.partyX_receivingInfoStatus` is `PENDING_INPUT` for the current user.
            *   Show message and a form/button to provide/confirm receiving details.
            *   Disable/hide main negotiation UI until this is done.
        *   **Negotiation UI (FRD 4.2.2 - Post-Readiness Gate):**
            *   Display current negotiation status (e.g., "Awaiting your proposal", "Waiting for other party's response", "System recommends X to pay first").
            *   Display `systemRecommendedPayerId` and `currentProposal_PayerId`.
            *   If it's the user's turn to propose or they can counter-propose: show options/buttons.
            *   If there's a pending proposal for the user to respond to: show "Agree" / "Make Counter-Proposal" buttons.
            *   Display who made the current proposal.
            *   Clearly indicate when agreement is reached and who the `finalizedPayerId` is.
            *   Integrate with `useTransactionFlowLogic.ts` or a new `usePayerNegotiationLogic.ts` composable. This composable will use the Pinia store actions and getters.
        *   Update `TransactionFlowCardV3.vue`'s `visualSteps` and `currentStepIndex` logic to reflect this new initial step. The "Designate Payer" step in `visualSteps` seems to align with this feature.
    5.  **Composables (`src/composables/useTransactionFlowLogic.ts` or new):**
        *   Add state and methods to manage the UI logic for payer designation.
        *   Interact with the `payerNegotiationStore`.
        *   Handle user actions (submitting forms, clicking buttons) and call store actions.
        *   Provide computed properties for the UI (e.g., `isDesignationStepActive`, `currentUserPromptText`).

**4. Data Management**

*   **Backend:**
    *   Prisma for `PayerNegotiation` model and enums.
    *   Secure handling of payment receiving information (details depend on whether it's new or uses existing profile data; encryption at rest for sensitive details is critical if stored directly).
*   **Frontend:**
    *   Pinia store (`payerNegotiationStore`) to manage client-side state.
    *   Zod (already in use) for frontend input validation if any forms are complex before sending to backend.

**5. API Design (Recap)**

*   `POST /api/transactions/:transactionId/negotiation/init`
*   `GET /api/transactions/:transactionId/negotiation`
*   `POST /api/transactions/:transactionId/negotiation/receiving-info` (Payload: e.g., `{ infoType: string, details: object }` or `{ useProfileInfo: boolean }`)
*   `POST /api/transactions/:transactionId/negotiation/propose` (Payload: e.g., `{ proposedPayerId: string }`)
*   `POST /api/transactions/:transactionId/negotiation/agree`
*   All responses should include the updated `PayerNegotiation` state or a relevant part of it.
*   Authentication: `authMiddleware` on all.

**6. Frontend UI/UX Considerations**

*   Clear visual distinction for the "Payment Readiness Gate" step.
*   Intuitive UI for making proposals and agreeing:
    *   Clearly show who the current proposal is from and who it nominates.
    *   Disable actions that are not currently available to the user (e.g., "Agree" if no proposal is pending for them).
*   Real-time feedback: Update UI instantly via Socket.IO events (e.g., when the other party makes a proposal or agrees).
*   Loading states and error messages (using Naive UI components like `NSpin`, `NAlert`, `useMessage`).
*   The `TransactionFlowCardV3.vue`'s `headerTitle`, `currentActionTitle`, `currentActionInfoText`, and step indicators need to be updated to reflect the negotiation phase accurately.

**7. Real-time Aspects (Recap)**

*   **Socket Events (Backend to Frontend):**
    *   `NEGOTIATION_STATE_CHANGE`: Sent when `PayerNegotiation` status or key fields change. Payload: `PayerNegotiation` object.
    *   `PROPOSAL_RECEIVED`: Specific event when a new proposal is made. Payload: `{ negotiationId: string, proposedPayerId: string, proposedById: string }`.
    *   `NEGOTIATION_FINALIZED`: When `finalizedPayerId` is set. Payload: `{ negotiationId: string, finalizedPayerId: string, paymentTimerDueDate: Date }`.
    *   `RECEIVING_INFO_STATUS_UPDATED`: When a party updates their receiving info status. Payload: `{ negotiationId: string, userId: string, status: ReceivingInfoStatus }`.
*   **Frontend Handling:** Listen in `socketService.ts`, update `payerNegotiationStore.ts`.

**8. Error Handling and Validation**

*   **Backend:**
    *   Zod validation for all API request bodies.
    *   Service-level validation:
        *   Ensure user is part of the transaction.
        *   Check `negotiationStatus` before allowing actions (e.g., cannot propose if status is `AWAITING_RECEIVING_INFO`).
        *   Validate that `proposedPayerId` is one of the two parties.
*   **Frontend:**
    *   Use `errorHandler.ts` utility.
    *   Display user-friendly messages for API errors or validation failures.
    *   Disable buttons/inputs based on current state to prevent invalid actions.

**9. Testing Considerations**

*   **Backend (Vitest):**
    *   Unit tests for `payerNegotiationService.ts`:
        *   Initialization logic (system recommendation, initial status).
        *   Receiving info submission and status transitions.
        *   Proposal logic (valid proposals, status changes).
        *   Agreement logic (correct finalization, status changes).
        *   Edge cases (e.g., user not part of transaction, invalid proposals).
    *   API endpoint tests (integration tests): Test each endpoint with valid and invalid inputs, check responses and database state.
    *   Socket event emission (mock Socket.IO server or check emitted events).
*   **Frontend (Vitest):**
    *   Unit tests for `payerNegotiationStore.ts`: Actions, mutations, getters. Mock API calls.
    *   Component tests for `TransactionFlowCardV3.vue` (or new sub-components):
        *   Rendering based on negotiation state.
        *   User interactions (button clicks, form submissions).
        *   Correct dispatch of store actions.
    *   Composable tests (`useTransactionFlowLogic.ts` or new composable).
    *   Socket event handling in the store.
*   **End-to-End:**
    *   Manually test the full flow: two users, payment readiness gate, proposal, counter-proposal (if implemented), agreement, and transition to next phase.

**10. Potential Challenges & Questions**

*   **Payment Receiving Information Security:** The FRD states, "*(Secure storage for actual receiving info... This FRD assumes such a mechanism exists.)*".
    *   **Question:** Does a secure mechanism for storing/managing user payment receiving details (e.g., bank account info, crypto addresses) already exist? If not, this is a significant prerequisite. For MVP, this might be simplified to "user confirms they have means to receive X currency" without storing actual details, but this needs clarification.
*   **System Recommended Payer Logic:**
    *   **Question:** What is the business logic for `systemRecommendedPayerId`? (e.g., offer creator, random, based on transaction type, etc.). This needs to be defined for backend implementation.
*   **Definition of "Payment Receiving Information":**
    *   **Question:** What specific information is needed for "payment receiving"? This will affect the UI and backend storage/validation. Is it per currency type?
*   **Expiration (`negotiationStatus: EXPIRED`):**
    *   **Question:** What are the conditions for a negotiation to expire? Is there a timer on the negotiation itself before finalization? The FRD mentions `paymentTimerDueDate` after finalization, but not for the negotiation process itself.
*   **User Experience for Counter-Proposals:** The FRD focuses on proposing and agreeing.
    *   **Question:** How are counter-proposals handled? Does a "counter-proposal" simply become the new `currentProposal`? The plan assumes this.
*   **Integration with `Transaction` Model:** The exact relationship and status updates between `PayerNegotiation` and the main `Transaction` model need to be carefully managed.
*   **Complexity of `TransactionFlowCardV3.vue`:** This component is already complex. Integrating this new, stateful step requires careful planning to keep it manageable. Breaking it into smaller, focused sub-components is advisable.

This plan provides a comprehensive roadmap. The developer should clarify the open questions before diving deep into implementation, especially regarding payment receiving information and system recommendation logic.

Report of what has been done:
Summarized conversation historyI've already provided a detailed summary of the work completed for the "First Payer Designation" feature. Here's the conceptual diagram description from that summary, which explains the components and their interactions visually. You can use this description to create a diagram.

**Conceptual Diagram Description:**

This describes the flow and components:

1.  **User Interaction (Frontend - `TransactionFlowCardV3.vue`):**
    *   A user involved in a transaction (linked to a `Transaction` record) sees an interface related to "First Payer Designation." This is managed within the `TransactionFlowCardV3.vue` component.
    *   The state of the negotiation is primarily drawn from the `payerNegotiationStore.ts`.
    *   If the `PayerNegotiation` state (e.g., `negotiationStatus`, `partyA_receivingInfoStatus`) indicates that payment information is needed from the current user, the `PaymentReadinessGate.vue` component is displayed.

2.  **Payment Readiness Gate (Frontend - `PaymentReadinessGate.vue`):**
    *   The user inputs their bank name, account number, and account holder name into the form provided by `PaymentReadinessGate.vue`.
    *   The user has an option (checkbox) to save this information as their default payment receiving method (which would update/create a `PaymentReceivingInfo` record with `isDefaultForUser: true`).
    *   On submission, the component calls an action in `payerNegotiationStore.ts`.

3.  **Store Action (Frontend - `payerNegotiationStore.ts`):**
    *   The relevant action in `payerNegotiationStore.ts` (e.g., `submitReceivingInfoAction`) makes an API call to the backend.
    *   The API call is typically: `POST /api/transactions/:transactionId/payer-negotiation/receiving-info`.
    *   It sends the payment details and the "save to profile" preference.

4.  **API Route (Backend - `payerNegotiationRoutes.ts`):**
    *   The `POST /transactions/:transactionId/payer-negotiation/receiving-info` route in `payerNegotiationRoutes.ts` receives the request.
    *   `authMiddleware` verifies the user's JWT.
    *   The `validateRequest` utility (using Zod schemas) validates the input data (bank details, etc.).
    *   Authorization logic within the route handler checks if the authenticated user is indeed `partyA` or `partyB` of the `PayerNegotiation` record associated with the `transactionId`.
    *   If valid, it calls the `payerNegotiationService.submitReceivingInfo()` method.

5.  **Service Logic (Backend - `payerNegotiationService.ts`):**
    *   `submitReceivingInfo(userId, transactionId, details, saveToProfile)`:
        *   Uses `PrismaClient` to find the `PayerNegotiation` record by `transactionId`.
        *   Verifies the `userId` matches either `partyA_Id` or `partyB_Id`.
        *   If `saveToProfile` is true:
            *   It queries for an existing `PaymentReceivingInfo` for the `userId` where `isDefaultForUser: true`. If one exists, it sets `isDefaultForUser: false`.
            *   Creates a new `PaymentReceivingInfo` record for the `userId` with the provided details and `isDefaultForUser: true`.
        *   Updates the corresponding status field in the `PayerNegotiation` record (e.g., `partyA_receivingInfoStatus = 'PROVIDED'`).
        *   Determines the new overall `negotiationStatus`. For example, if both `partyA_receivingInfoStatus` and `partyB_receivingInfoStatus` are `'PROVIDED'`, the `negotiationStatus` might change to `READY_TO_NEGOTIATE`.
        *   Saves changes to the `PayerNegotiation` (and potentially `PaymentReceivingInfo`) tables in the database.
        *   **Emits Socket.IO Events** (using the `io` instance from index.ts):
            *   `io.to(transactionId).emit(SocketEvents.PAYMENT_INFO_SUBMITTED, payload)` - `payload` includes which party submitted and their new status.
            *   `io.to(transactionId).emit(SocketEvents.NEGOTIATION_STATE_UPDATED, updatedPayerNegotiationRecord)` - `payload` is the full updated negotiation state.
            *   (The `transactionId` serves as the Socket.IO room name).

6.  **Database (Backend - Prisma/SQLite):**
    *   The `PayerNegotiation` table is updated with new `partyX_receivingInfoStatus` and `negotiationStatus`.
    *   If "save to profile" was chosen, the `PaymentReceivingInfo` table is updated or a new record is created.

7.  **Socket.IO Communication (Real-time Update):**
    *   The backend (`payerNegotiationService.ts`) emits events to all clients connected to the `transactionId` room.
    *   **Frontend (`payerNegotiationStore.ts`):**
        *   The store has listeners (initialized via `socketService.ts`) for `SocketEvents.PAYMENT_INFO_SUBMITTED` and `SocketEvents.NEGOTIATION_STATE_UPDATED`.
        *   Upon receiving an event, the store updates its local state (e.g., the `PayerNegotiation` object, individual status flags).

8.  **UI Update (Frontend - `TransactionFlowCardV3.vue` / `PaymentReadinessGate.vue`):**
    *   Vue components (`TransactionFlowCardV3.vue`, `PaymentReadinessGate.vue`) are reactive to changes in the `payerNegotiationStore`.
    *   The UI updates automatically:
        *   The `PaymentReadinessGate.vue` might disappear for the user who just submitted.
        *   The overall status display in `TransactionFlowCardV3.vue` updates.
        *   If `negotiationStatus` becomes `READY_TO_NEGOTIATE`, UI elements for proposing the first payer might become active.

**Negotiation Flow (Proposal & Agreement - Similar Pattern):**

*   **Proposing First Payer:**
    1.  **User Action (Frontend - `TransactionFlowCardV3.vue`):** User (e.g., Party A) clicks a button to propose themselves or Party B as the first payer.
    2.  **Store Action (`payerNegotiationStore.ts`):** Calls an action like `proposeFirstPayerAction`.
    3.  **API Call:** `POST /api/transactions/:transactionId/payer-negotiation/propose` with `{ proposedPayerId: 'userIdOfProposedPayer' }`.
    4.  **API Route (`payerNegotiationRoutes.ts`):** Validates, authorizes, then calls `payerNegotiationService.proposeFirstPayer()`.
    5.  **Service Logic (`payerNegotiationService.ts`):**
        *   Updates `PayerNegotiation` fields: `currentProposal_PayerId` (who is proposed), `currentProposal_ById` (who made the proposal), and sets `negotiationStatus` to something like `PENDING_AGREEMENT_ON_PROPOSAL`.
        *   Emits `SocketEvents.PROPOSAL_MADE` and `SocketEvents.NEGOTIATION_STATE_UPDATED`.
    6.  **DB Update:** `PayerNegotiation` record is updated.
    7.  **Socket & UI Update:** Frontend store receives events, UI in `TransactionFlowCardV3.vue` updates to show the active proposal and who needs to respond.

*   **Agreeing to Proposal:**
    1.  **User Action (Frontend - `TransactionFlowCardV3.vue`):** The other user (e.g., Party B) sees the proposal and clicks "Agree."
    2.  **Store Action (`payerNegotiationStore.ts`):** Calls `agreeToProposalAction`.
    3.  **API Call:** `POST /api/transactions/:transactionId/payer-negotiation/agree`.
    4.  **API Route (`payerNegotiationRoutes.ts`):** Validates, authorizes, then calls `payerNegotiationService.agreeToProposal()`.
    5.  **Service Logic (`payerNegotiationService.ts`):**
        *   Updates `PayerNegotiation` fields: `partyX_agreedToProposal_PayerId` and `partyX_agreedToProposal_ById` for the agreeing user.
        *   Checks if both parties have now agreed to the *same* `currentProposal_PayerId`.
        *   If agreement is reached:
            *   Sets `finalizedPayerId` to the `currentProposal_PayerId`.
            *   Sets `negotiationStatus` to `FINALIZED`.
            *   Optionally sets `paymentTimerDueDate`.
            *   Emits `SocketEvents.NEGOTIATION_FINALIZED` and `SocketEvents.NEGOTIATION_STATE_UPDATED`.
        *   If only one party has agreed so far (or if they agreed to different things, though the logic should prevent this if a single proposal is active): Emits `SocketEvents.NEGOTIATION_STATE_UPDATED`.
    6.  **DB Update:** `PayerNegotiation` record is updated.
    7.  **Socket & UI Update:** Frontend store receives events. If finalized, UI in `TransactionFlowCardV3.vue` shows who pays first, and the transaction flow might move to the next stage (e.g., awaiting payment from `finalizedPayerId`).

This detailed flow should help in visualizing the interactions between the frontend components, backend services, the database models (`PayerNegotiation`, `PaymentReceivingInfo`, `Transaction`, `User`), and the real-time updates via Socket.IO.


sequenceDiagram
    actor UserA
    participant FE_UI_A as Frontend UI (User A)<br>(TransactionFlowCardV3, PaymentReadinessGate)
    participant FE_Store_A as Frontend Store (User A)<br>(payerNegotiationStore)
    participant BE_API as Backend API<br>(payerNegotiationRoutes)
    participant BE_Service as Backend Service<br>(payerNegotiationService)
    participant DB as Database<br>(Prisma: PayerNegotiation, PaymentReceivingInfo)
    participant SocketIO_Server as Socket.IO Server
    participant FE_Socket_Client_A as Frontend Socket Client (User A)
    actor UserB
    participant FE_UI_B as Frontend UI (User B)<br>(TransactionFlowCardV3, PaymentReadinessGate)
    participant FE_Store_B as Frontend Store (User B)<br>(payerNegotiationStore)
    participant FE_Socket_Client_B as Frontend Socket Client (User B)

    %% --- Initial Load & Payment Readiness Gate ---
    UserA->>FE_UI_A: Views Transaction
    FE_UI_A->>FE_Store_A: Get negotiation state
    FE_Store_A->>BE_API: GET /transactions/{id}/payer-negotiation
    BE_API->>BE_Service: initializeOrGetNegotiation(txId, userAId)
    BE_Service->>DB: Query/Create PayerNegotiation
    DB-->>BE_Service: PayerNegotiation data
    BE_Service-->>BE_API: PayerNegotiation data
    BE_API-->>FE_Store_A: PayerNegotiation data
    FE_Store_A-->>FE_UI_A: Displays state (e.g., User A needs to provide info)

    alt User A needs to provide payment info
        FE_UI_A->>UserA: Shows PaymentReadinessGate
        UserA->>FE_UI_A: Submits payment receiving info
        FE_UI_A->>FE_Store_A: submitReceivingInfoAction(info)
        FE_Store_A->>BE_API: POST /transactions/{id}/payer-negotiation/receiving-info
        BE_API->>BE_Service: submitReceivingInfo(userAId, txId, info)
        BE_Service->>DB: Update PayerNegotiation (partyA_receivingInfoStatus), Create/Update PaymentReceivingInfo
        DB-->>BE_Service: Success
        BE_Service->>SocketIO_Server: emit PAYMENT_INFO_SUBMITTED (to txId room)
        BE_Service->>SocketIO_Server: emit NEGOTIATION_STATE_UPDATED (to txId room)
        SocketIO_Server-->>FE_Socket_Client_A: Receives PAYMENT_INFO_SUBMITTED
        FE_Socket_Client_A->>FE_Store_A: Update state
        SocketIO_Server-->>FE_Socket_Client_A: Receives NEGOTIATION_STATE_UPDATED
        FE_Socket_Client_A->>FE_Store_A: Update state (full negotiation object)
        FE_Store_A-->>FE_UI_A: UI updates (Gate disappears for User A)

        %% Notify User B if they also need to provide info or that User A is ready
        SocketIO_Server-->>FE_Socket_Client_B: Receives NEGOTIATION_STATE_UPDATED
        FE_Socket_Client_B->>FE_Store_B: Update state
        FE_Store_B-->>FE_UI_B: UI updates for User B (e.g. "User A is ready")
    end

    %% Assume User B also provides info (simplified for brevity)
    Note over UserB, FE_Socket_Client_B: User B provides their payment info (similar flow as User A)
    BE_Service->>SocketIO_Server: emit NEGOTIATION_STATE_UPDATED (status: READY_TO_NEGOTIATE)
    SocketIO_Server-->>FE_Socket_Client_A: Receives NEGOTIATION_STATE_UPDATED
    FE_Socket_Client_A->>FE_Store_A: Update state
    FE_Store_A-->>FE_UI_A: UI updates (Negotiation UI enabled)
    SocketIO_Server-->>FE_Socket_Client_B: Receives NEGOTIATION_STATE_UPDATED
    FE_Socket_Client_B->>FE_Store_B: Update state
    FE_Store_B-->>FE_UI_B: UI updates (Negotiation UI enabled)

    %% --- Negotiation: Proposal ---
    UserA->>FE_UI_A: Proposes User B to pay first
    FE_UI_A->>FE_Store_A: proposeFirstPayerAction(userBId)
    FE_Store_A->>BE_API: POST /transactions/{id}/payer-negotiation/propose
    BE_API->>BE_Service: makeProposal(userAId, txId, userBId)
    BE_Service->>DB: Update PayerNegotiation (currentProposal_*, negotiationStatus)
    DB-->>BE_Service: Success
    BE_Service->>SocketIO_Server: emit PROPOSAL_MADE (to txId room)
    BE_Service->>SocketIO_Server: emit NEGOTIATION_STATE_UPDATED (to txId room)

    SocketIO_Server-->>FE_Socket_Client_A: Receives PROPOSAL_MADE / NEGOTIATION_STATE_UPDATED
    FE_Socket_Client_A->>FE_Store_A: Update state
    FE_Store_A-->>FE_UI_A: UI updates (shows User A's proposal, waiting for User B)

    SocketIO_Server-->>FE_Socket_Client_B: Receives PROPOSAL_MADE / NEGOTIATION_STATE_UPDATED
    FE_Socket_Client_B->>FE_Store_B: Update state
    FE_Store_B-->>FE_UI_B: UI updates (shows User A's proposal, prompts User B to respond)

    %% --- Negotiation: Agreement ---
    UserB->>FE_UI_B: Agrees to User A's proposal
    FE_UI_B->>FE_Store_B: agreeToProposalAction()
    FE_Store_B->>BE_API: POST /transactions/{id}/payer-negotiation/agree
    BE_API->>BE_Service: agreeToProposal(userBId, txId)
    BE_Service->>DB: Update PayerNegotiation (partyB_agreedToProposal_*, finalizedPayerId, negotiationStatus: FINALIZED, paymentTimerDueDate)
    DB-->>BE_Service: Success
    BE_Service->>SocketIO_Server: emit NEGOTIATION_FINALIZED (to txId room)
    BE_Service->>SocketIO_Server: emit NEGOTIATION_STATE_UPDATED (to txId room)

    SocketIO_Server-->>FE_Socket_Client_A: Receives NEGOTIATION_FINALIZED / NEGOTIATION_STATE_UPDATED
    FE_Socket_Client_A->>FE_Store_A: Update state
    FE_Store_A-->>FE_UI_A: UI updates (shows negotiation finalized, User B pays first, payment timer starts for User B)

    SocketIO_Server-->>FE_Socket_Client_B: Receives NEGOTIATION_FINALIZED / NEGOTIATION_STATE_UPDATED
    FE_Socket_Client_B->>FE_Store_B: Update state
    FE_Store_B-->>FE_UI_B: UI updates (shows negotiation finalized, User B pays first, payment timer starts for User B)
