// composables/useUserResolver.ts
import { computed } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useChatStore } from '@/stores/chatStore';

/**
 * Composable for resolving user information (like usernames) from user IDs
 * using cached data from various stores instead of relying on API responses.
 * 
 * This provides a more efficient and consistent way to get user data
 * that doesn't depend on the backend always sending complete user objects.
 */
export function useUserResolver() {
  const authStore = useAuthStore();
  const chatStore = useChatStore();

  /**
   * Resolves a username from a user ID using available cached data
   * @param userId - The user ID to resolve
   * @returns The username or a fallback if not found
   */
  const resolveUsername = (userId: string): string => {
    // Check if it's the current user
    if (authStore.user?.id === userId) {
      return authStore.user.username || 'You';
    }

    // Check chat participants
    if (chatStore.participants) {
      if (chatStore.participants.currentUser.id === userId) {
        return chatStore.participants.currentUser.username;
      }
      if (chatStore.participants.otherUser.id === userId) {
        return chatStore.participants.otherUser.username;
      }
    }

    // Fallback - could be extended to check other stores in the future
    return 'Unknown User';
  };

  /**
   * Reactive computed for resolving usernames that updates when store data changes
   */
  const createUsernameResolver = (userId: string) => {
    return computed(() => resolveUsername(userId));
  };

  /**
   * Resolves both currency provider usernames for a transaction
   * @param currencyAProviderId - ID of currency A provider
   * @param currencyBProviderId - ID of currency B provider
   * @returns Object with both usernames
   */
  const resolveTransactionUsernames = (
    currencyAProviderId: string,
    currencyBProviderId: string
  ) => {
    return {
      currencyAProviderUsername: resolveUsername(currencyAProviderId),
      currencyBProviderUsername: resolveUsername(currencyBProviderId)
    };
  };

  return {
    resolveUsername,
    createUsernameResolver,
    resolveTransactionUsernames
  };
}
