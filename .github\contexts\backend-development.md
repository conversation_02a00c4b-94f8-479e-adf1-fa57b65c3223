# Backend Development Guidelines for MUNygo

## Architecture Principles

### Dependency Injection Best Practices

**Avoid Circular Dependencies**: Never import service instances from the main entry point (`index.ts`) within service modules.

```typescript
// ❌ Bad Pattern - Creates circular imports
import { io, someServiceInstance } from '../index';
export const myService = new MyService();

// ✅ Good Pattern - Constructor injection
export class MyService {
  constructor(
    private dependency1: SomeService,
    private dependency2: AnotherService
  ) {
    // Dependencies injected via constructor
  }
}
```

### Centralized Service Creation

Create all service instances in `index.ts` in proper dependency order:

```typescript
// Create services in dependency order
const serviceA = new ServiceA();
const serviceB = new ServiceB(serviceA); // ServiceB depends on ServiceA
const serviceC = new ServiceC(serviceA, serviceB); // ServiceC depends on both
```

### Route Factory Pattern

Routes that need service instances should be factory functions:

```typescript
// routes/myRoutes.ts
export default function createMyRoutes(myService: MyService) {
  const router = new Hono();
  // Use myService in route handlers
  return router;
}

// index.ts
app.route('/api/my-routes', createMyRoutes(myServiceInstance));
```

## Hono API Development

### Route Structure

```typescript
import { Hono } from 'hono';
import { authMiddleware } from '../middleware/auth';

const router = new Hono();

router.get('/endpoint', authMiddleware, async (c) => {
  try {
    // Route logic here
    return c.json({ success: true, data: result });
  } catch (error) {
    return c.json({ error: 'Error message' }, 500);
  }
});

export default router;
```

### Authentication Middleware

Use `authMiddleware` to protect routes by verifying JWTs:

```typescript
import { authMiddleware } from '../middleware/auth';

router.post('/protected-route', authMiddleware, async (c) => {
  const user = c.get('user'); // User from JWT payload
  // Route logic with authenticated user
});
```

### Error Handling Patterns

- Use structured error responses with consistent format
- Include proper HTTP status codes
- Log errors with context information
- Return user-friendly error messages

## Service Layer Architecture

### Service Classes

```typescript
export class MyService {
  constructor(
    private prisma: PrismaClient,
    private notificationService: NotificationService
  ) {}

  async performOperation(data: InputData): Promise<Result> {
    try {
      // Business logic here
      const result = await this.prisma.model.create({ data });
      
      // Emit notifications if needed
      await this.notificationService.notify({
        type: 'OPERATION_COMPLETE',
        data: result
      });
      
      return result;
    } catch (error) {
      // Handle and log errors
      throw new Error(`Operation failed: ${error.message}`);
    }
  }
}
```

### Socket.IO Integration

Use centralized Socket.IO management for real-time updates:

```typescript
// In service classes
export class TransactionService {
  constructor(
    private prisma: PrismaClient,
    private io: Server
  ) {}

  async updateTransactionStatus(id: string, status: string) {
    const transaction = await this.prisma.transaction.update({
      where: { id },
      data: { status }
    });

    // Emit real-time updates
    this.io.to(`transaction-${id}`).emit('transaction_updated', {
      transactionId: id,
      status,
      timestamp: new Date()
    });

    return transaction;
  }
}
```

## Database Operations (Prisma)

### Model Relationships

Define all foreign key relationships explicitly in schema:

```prisma
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  offers      Offer[]
  interests   Interest[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Offer {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  interests   Interest[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

### Migration Best Practices

- Use descriptive migration names: `add_user_profile_fields`
- Always commit schema.prisma + migration files together
- Test migrations on development database first
- Use `npx prisma migrate dev` for development
- Use `npx prisma migrate deploy` for production

### Database Queries

```typescript
// Efficient queries with proper relations
const offers = await prisma.offer.findMany({
  include: {
    user: {
      select: { id: true, name: true, reputation: true }
    },
    interests: {
      where: { status: 'PENDING' },
      include: { user: true }
    }
  },
  orderBy: { createdAt: 'desc' }
});
```

## Input Validation

Use Zod schemas for runtime validation at API boundaries:

```typescript
import { z } from 'zod';

const CreateOfferSchema = z.object({
  title: z.string().min(1).max(100),
  amount: z.number().positive(),
  currency: z.enum(['USD', 'EUR', 'IRR']),
  rate: z.number().positive()
});

router.post('/offers', authMiddleware, async (c) => {
  const body = await c.req.json();
  const validatedData = CreateOfferSchema.parse(body);
  // Use validatedData safely
});
```

## External Service Integration

### Email Service

```typescript
// services/email.ts
export class EmailService {
  async sendVerificationEmail(email: string, token: string) {
    // Email sending logic with nodemailer
    // Support both development (Ethereal) and production SMTP
  }
}
```

### Twilio Integration

```typescript
// services/twilio.ts
export class TwilioService {
  async sendOTP(phoneNumber: string): Promise<string> {
    // Phone verification with OTP
    // Support both production Twilio and development mock modes
  }
}
```

## Rate Limiting

Implement rate limiting for sensitive operations:

```typescript
// utils/rateLimiter.ts
export class RateLimiter {
  async checkLimit(identifier: string, operation: string): Promise<boolean> {
    // Advanced rate limiting with phone number normalization
    // Attempt tracking and blocking mechanisms
  }
}
```

## Testing Backend Services

### Service Testing

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { MyService } from '../services/MyService';

describe('MyService', () => {
  let service: MyService;
  
  beforeEach(() => {
    const mockPrisma = createMockPrisma();
    service = new MyService(mockPrisma);
  });

  it('should perform operation correctly', async () => {
    const result = await service.performOperation(testData);
    expect(result).toEqual(expectedResult);
  });
});
```

### API Route Testing

```typescript
import { testClient } from 'hono/testing';
import app from '../index';

describe('API Routes', () => {
  it('should return offers', async () => {
    const client = testClient(app);
    const response = await client.api.offers.$get();
    
    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
  });
});
```

## Environment Configuration

- Use environment variables for configuration
- Separate development and production settings
- Never commit sensitive data to version control
- Use proper typing for environment variables
