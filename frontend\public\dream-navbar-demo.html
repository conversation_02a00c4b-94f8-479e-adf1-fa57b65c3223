<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DreamNavBar - Custom Modern Navigation</title>
  <style>
    /* Reset and base styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
      overflow-x: hidden;
    }

    /* CSS Custom Properties for theming */
    :root {
      --navbar-height: 80px;
      --navbar-bg: rgba(255, 255, 255, 0.95);
      --navbar-border: rgba(0, 0, 0, 0.1);
      --navbar-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      --navbar-glow: rgba(103, 126, 234, 0.1);
      --text-primary: #1a1a1a;
      --text-secondary: #666666;
      --text-hover: #667eea;
      --accent-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --error-color: #ef4444;
      --border-radius: 16px;
      --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    /* Dark theme variables */
    [data-theme="dark"] {
      --navbar-bg: rgba(30, 30, 30, 0.95);
      --navbar-border: rgba(255, 255, 255, 0.1);
      --navbar-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      --navbar-glow: rgba(103, 126, 234, 0.2);
      --text-primary: #ffffff;
      --text-secondary: #a1a1aa;
      --text-hover: #8b94ff;
    }

    /* Main navbar */
    .dream-navbar {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      height: var(--navbar-height);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
    }

    /* Background layers */
    .navbar-bg {
      position: absolute;
      inset: 0;
      background: var(--navbar-bg);
      border-bottom: 1px solid var(--navbar-border);
    }

    .navbar-glow {
      position: absolute;
      inset: 0;
      background: var(--navbar-glow);
      opacity: 0;
      transition: var(--transition-smooth);
    }

    .dream-navbar:hover .navbar-glow {
      opacity: 1;
    }

    /* Container */
    .navbar-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    /* Logo section */
    .logo-section {
      flex-shrink: 0;
    }

    .logo-container {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      cursor: pointer;
      transition: var(--transition-smooth);
    }

    .logo-container:hover {
      transform: translateY(-1px);
    }

    .logo-icon {
      position: relative;
      transform: perspective(1000px) rotateY(0deg);
      transition: var(--transition-spring);
    }

    .logo-container:hover .logo-icon {
      transform: perspective(1000px) rotateY(180deg);
    }

    .logo-text {
      font-size: 1.5rem;
      font-weight: 700;
      background: var(--accent-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* Desktop navigation */
    .desktop-nav {
      flex: 1;
      display: flex;
      justify-content: center;
    }

    .nav-links {
      display: flex;
      gap: 0.5rem;
    }

    .nav-link {
      position: relative;
      text-decoration: none;
      border-radius: var(--border-radius);
      transition: var(--transition-smooth);
    }

    .nav-link-content {
      position: relative;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.25rem;
      color: var(--text-secondary);
      font-weight: 500;
      transition: var(--transition-smooth);
    }

    .nav-link:hover .nav-link-content,
    .nav-link.active .nav-link-content {
      color: var(--text-hover);
      transform: translateY(-1px);
    }

    .nav-link-indicator {
      position: absolute;
      bottom: -2px;
      left: 50%;
      width: 0;
      height: 3px;
      background: var(--accent-gradient);
      border-radius: 2px;
      transform: translateX(-50%);
      transition: var(--transition-spring);
    }

    .nav-link:hover .nav-link-indicator,
    .nav-link.active .nav-link-indicator {
      width: 80%;
    }

    /* Action section */
    .action-section {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex-shrink: 0;
    }

    /* Connection status */
    .connection-status {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border-radius: 50px;
      cursor: pointer;
      transition: var(--transition-smooth);
      font-size: 0.875rem;
      font-weight: 500;
    }

    .status-indicator {
      position: relative;
      width: 8px;
      height: 8px;
    }

    .status-dot {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      transition: var(--transition-smooth);
    }

    .status-pulse {
      position: absolute;
      inset: -4px;
      border-radius: 50%;
      opacity: 0.3;
      animation: pulse 2s infinite;
    }

    .status-excellent .status-dot,
    .status-excellent .status-pulse {
      background: var(--success-color);
    }

    .status-good .status-dot,
    .status-good .status-pulse {
      background: var(--success-color);
    }

    .status-poor .status-dot,
    .status-poor .status-pulse {
      background: var(--warning-color);
    }

    .status-disconnected .status-dot,
    .status-disconnected .status-pulse {
      background: var(--error-color);
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); opacity: 0.3; }
      50% { transform: scale(1.5); opacity: 0; }
    }

    /* Notification button */
    .notification-container {
      position: relative;
    }

    .notification-btn {
      position: relative;
      padding: 0.75rem;
      border: none;
      background: none;
      border-radius: 50%;
      cursor: pointer;
      color: var(--text-secondary);
      transition: var(--transition-smooth);
    }

    .notification-btn:hover {
      background: rgba(103, 126, 234, 0.1);
      color: var(--text-hover);
      transform: translateY(-1px);
    }

    .notification-icon {
      position: relative;
      font-size: 1.25rem;
    }

    .notification-badge {
      position: absolute;
      top: -8px;
      right: -8px;
      min-width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--accent-gradient);
      color: white;
      border-radius: 10px;
      font-size: 0.75rem;
      font-weight: 600;
      border: 2px solid var(--navbar-bg);
      animation: bounce 0.5s ease-in-out;
    }

    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
      40% { transform: translateY(-3px); }
      60% { transform: translateY(-1px); }
    }

    /* Theme toggle */
    .theme-toggle {
      padding: 0.75rem;
      border: none;
      background: none;
      border-radius: 50%;
      cursor: pointer;
      color: var(--text-secondary);
      transition: var(--transition-smooth);
    }

    .theme-toggle:hover {
      background: rgba(103, 126, 234, 0.1);
      color: var(--text-hover);
      transform: translateY(-1px) rotate(180deg);
    }

    .theme-icon {
      font-size: 1.25rem;
    }

    /* Language selector */
    .language-selector {
      position: relative;
    }

    .language-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border: 1px solid var(--navbar-border);
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50px;
      cursor: pointer;
      color: var(--text-secondary);
      font-weight: 500;
      transition: var(--transition-smooth);
    }

    .language-btn:hover {
      border-color: var(--text-hover);
      color: var(--text-hover);
      transform: translateY(-1px);
    }

    .language-text {
      font-size: 0.875rem;
      font-weight: 600;
    }

    /* User profile */
    .user-profile {
      position: relative;
    }

    .user-btn {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.5rem;
      border: none;
      background: none;
      border-radius: var(--border-radius);
      cursor: pointer;
      transition: var(--transition-smooth);
    }

    .user-btn:hover {
      background: rgba(103, 126, 234, 0.1);
      transform: translateY(-1px);
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      position: relative;
      border: 2px solid transparent;
      background: var(--accent-gradient);
      padding: 2px;
    }

    .avatar-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
    }

    .avatar-fallback {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--navbar-bg);
      color: var(--text-secondary);
      border-radius: 50%;
    }

    .user-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      text-align: left;
    }

    .user-name {
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.875rem;
    }

    .user-status {
      font-size: 0.75rem;
      color: var(--text-secondary);
    }

    .user-chevron {
      font-size: 0.75rem;
      color: var(--text-secondary);
      transition: var(--transition-smooth);
    }

    .user-chevron.rotate {
      transform: rotate(180deg);
    }

    /* User dropdown */
    .user-dropdown {
      position: absolute;
      top: calc(100% + 0.5rem);
      right: 0;
      width: 280px;
      background: var(--navbar-bg);
      border: 1px solid var(--navbar-border);
      border-radius: var(--border-radius);
      box-shadow: var(--navbar-shadow);
      backdrop-filter: blur(20px);
      overflow: hidden;
      z-index: 1001;
      opacity: 0;
      transform: translateY(-10px) scale(0.95);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      pointer-events: none;
    }

    .user-dropdown.show {
      opacity: 1;
      transform: translateY(0) scale(1);
      pointer-events: all;
    }

    .dropdown-header {
      padding: 1.5rem;
      background: var(--accent-gradient);
      color: white;
    }

    .user-avatar-large {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      margin-bottom: 1rem;
      border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .user-details h4 {
      margin: 0 0 0.25rem 0;
      font-size: 1.125rem;
      font-weight: 700;
    }

    .user-details p {
      margin: 0 0 0.5rem 0;
      font-size: 0.875rem;
      opacity: 0.9;
    }

    .reputation-badge {
      display: inline-flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50px;
      font-size: 0.75rem;
      font-weight: 600;
    }

    .dropdown-divider {
      height: 1px;
      background: var(--navbar-border);
      margin: 0.5rem 0;
    }

    .dropdown-menu {
      padding: 0.5rem;
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      width: 100%;
      padding: 0.75rem;
      border: none;
      background: none;
      color: var(--text-primary);
      text-decoration: none;
      border-radius: 8px;
      cursor: pointer;
      transition: var(--transition-smooth);
      font-size: 0.875rem;
    }

    .dropdown-item:hover {
      background: rgba(103, 126, 234, 0.1);
      color: var(--text-hover);
    }

    .dropdown-item.logout-btn:hover {
      background: rgba(239, 68, 68, 0.1);
      color: var(--error-color);
    }

    /* Mobile menu toggle */
    .mobile-menu-toggle {
      display: none;
      padding: 0.75rem;
      border: none;
      background: none;
      cursor: pointer;
    }

    .hamburger {
      width: 24px;
      height: 18px;
      position: relative;
      transform: rotate(0deg);
      transition: var(--transition-smooth);
    }

    .hamburger span {
      display: block;
      position: absolute;
      height: 3px;
      width: 100%;
      background: var(--text-primary);
      border-radius: 2px;
      opacity: 1;
      left: 0;
      transform: rotate(0deg);
      transition: var(--transition-smooth);
    }

    .hamburger span:nth-child(1) {
      top: 0px;
    }

    .hamburger span:nth-child(2) {
      top: 7px;
    }

    .hamburger span:nth-child(3) {
      top: 14px;
    }

    .hamburger.active span:nth-child(1) {
      top: 7px;
      transform: rotate(135deg);
    }

    .hamburger.active span:nth-child(2) {
      opacity: 0;
      left: -60px;
    }

    .hamburger.active span:nth-child(3) {
      top: 7px;
      transform: rotate(-135deg);
    }

    /* Mobile styles */
    @media (max-width: 1024px) {
      .desktop-nav {
        display: none;
      }
      
      .action-section .connection-status,
      .action-section .language-selector,
      .action-section .user-profile {
        display: none;
      }
      
      .mobile-menu-toggle {
        display: block;
      }
    }

    /* Mobile overlay */
    .mobile-overlay {
      position: fixed;
      inset: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 999;
      backdrop-filter: blur(4px);
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .mobile-overlay.show {
      opacity: 1;
      visibility: visible;
    }

    /* Mobile menu */
    .mobile-menu {
      position: fixed;
      top: 0;
      right: 0;
      width: 320px;
      height: 100vh;
      background: var(--navbar-bg);
      border-left: 1px solid var(--navbar-border);
      box-shadow: -8px 0 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(20px);
      z-index: 1000;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      transform: translateX(100%);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .mobile-menu.show {
      transform: translateX(0);
    }

    .mobile-menu-header {
      padding: 2rem 1.5rem 1.5rem;
      background: var(--accent-gradient);
      color: white;
    }

    .mobile-user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .user-avatar-mobile {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      overflow: hidden;
      margin-bottom: 1rem;
      border: 4px solid rgba(255, 255, 255, 0.3);
    }

    .user-details-mobile h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.25rem;
      font-weight: 700;
    }

    .user-details-mobile p {
      margin: 0 0 1rem 0;
      font-size: 0.875rem;
      opacity: 0.9;
    }

    .reputation-badge-mobile {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50px;
      font-size: 0.875rem;
      font-weight: 600;
    }

    .mobile-nav-links {
      flex: 1;
      padding: 1rem;
    }

    .mobile-nav-link {
      display: block;
      text-decoration: none;
      margin-bottom: 0.5rem;
      border-radius: var(--border-radius);
      overflow: hidden;
      transition: var(--transition-smooth);
    }

    .mobile-link-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem 1.5rem;
      color: var(--text-primary);
      background: rgba(255, 255, 255, 0.05);
      transition: var(--transition-smooth);
    }

    .mobile-nav-link:hover .mobile-link-content,
    .mobile-nav-link.active .mobile-link-content {
      background: rgba(103, 126, 234, 0.1);
      color: var(--text-hover);
      transform: translateX(8px);
    }

    .mobile-menu-footer {
      padding: 1rem;
      border-top: 1px solid var(--navbar-border);
    }

    .mobile-actions {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .mobile-action-btn {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem 1.5rem;
      border: none;
      background: rgba(255, 255, 255, 0.05);
      color: var(--text-primary);
      border-radius: var(--border-radius);
      cursor: pointer;
      transition: var(--transition-smooth);
      font-size: 0.875rem;
    }

    .mobile-action-btn:hover {
      background: rgba(103, 126, 234, 0.1);
      color: var(--text-hover);
    }

    .mobile-action-btn.logout-btn:hover {
      background: rgba(239, 68, 68, 0.1);
      color: var(--error-color);
    }

    /* Responsive adjustments */
    @media (max-width: 480px) {
      .navbar-container {
        padding: 0 1rem;
      }
      
      .mobile-menu {
        width: 100vw;
        right: 0;
      }
      
      .logo-text {
        font-size: 1.25rem;
      }
    }

    /* Demo content */
    .demo-content {
      padding-top: var(--navbar-height);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 4rem 2rem;
      color: white;
    }

    .demo-title {
      font-size: 3rem;
      font-weight: 800;
      margin-bottom: 1rem;
      background: linear-gradient(45deg, #fff, #e0e7ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .demo-subtitle {
      font-size: 1.25rem;
      opacity: 0.9;
      margin-bottom: 2rem;
      max-width: 600px;
    }

    .demo-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      max-width: 1000px;
      margin-top: 3rem;
    }

    .feature-card {
      background: rgba(255, 255, 255, 0.1);
      padding: 2rem;
      border-radius: 20px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: var(--transition-smooth);
    }

    .feature-card:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.15);
    }

    .feature-icon {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: #fbbf24;
    }

    .feature-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .feature-desc {
      opacity: 0.8;
      line-height: 1.6;
    }

    .theme-controls {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      display: flex;
      gap: 1rem;
      z-index: 999;
    }

    .demo-btn {
      padding: 0.75rem 1.5rem;
      border: none;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border-radius: 50px;
      cursor: pointer;
      font-weight: 500;
      transition: var(--transition-smooth);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .demo-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  </style>
</head>
<body>
  <!-- Dream Navbar -->
  <nav class="dream-navbar" id="navbar">
    <!-- Background layers -->
    <div class="navbar-bg"></div>
    <div class="navbar-glow"></div>
    
    <!-- Main navbar content -->
    <div class="navbar-container">
      <!-- Logo section -->
      <div class="logo-section">
        <div class="logo-container">
          <div class="logo-icon">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <defs>
                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stop-color="#667eea" />
                  <stop offset="100%" stop-color="#764ba2" />
                </linearGradient>
              </defs>
              <path d="M16 2L29 16L16 30L3 16L16 2Z" fill="url(#logoGradient)" />
              <circle cx="16" cy="16" r="6" fill="white" fill-opacity="0.3" />
            </svg>
          </div>
          <span class="logo-text">DreamNavBar</span>
        </div>
      </div>

      <!-- Desktop navigation -->
      <div class="desktop-nav">
        <div class="nav-links">
          <a href="#" class="nav-link active">
            <div class="nav-link-content">
              <i class="fas fa-search"></i>
              <span>Browse</span>
              <div class="nav-link-indicator"></div>
            </div>
          </a>
          <a href="#" class="nav-link">
            <div class="nav-link-content">
              <i class="fas fa-list"></i>
              <span>My Offers</span>
              <div class="nav-link-indicator"></div>
            </div>
          </a>
          <a href="#" class="nav-link">
            <div class="nav-link-content">
              <i class="fas fa-plus-circle"></i>
              <span>Create</span>
              <div class="nav-link-indicator"></div>
            </div>
          </a>
          <a href="#" class="nav-link">
            <div class="nav-link-content">
              <i class="fas fa-comments"></i>
              <span>Chat</span>
              <div class="nav-link-indicator"></div>
            </div>
          </a>
        </div>
      </div>

      <!-- Action section -->
      <div class="action-section">
        <!-- Connection status -->
        <div class="connection-status status-excellent" onclick="toggleConnection()">
          <div class="status-indicator">
            <div class="status-dot"></div>
            <div class="status-pulse"></div>
          </div>
          <span class="status-text" id="connectionText">Excellent</span>
        </div>

        <!-- Notifications -->
        <div class="notification-container">
          <button class="notification-btn" onclick="toggleNotifications()">
            <div class="notification-icon">
              <i class="fas fa-bell"></i>
              <div class="notification-badge" id="notificationBadge">
                <span>3</span>
              </div>
            </div>
          </button>
        </div>

        <!-- Theme toggle -->
        <button class="theme-toggle" onclick="toggleTheme()">
          <div class="theme-icon">
            <i class="fas fa-moon" id="themeIcon"></i>
          </div>
        </button>

        <!-- Language selector -->
        <div class="language-selector">
          <button class="language-btn" onclick="toggleLanguage()">
            <span class="language-text" id="languageText">EN</span>
            <i class="fas fa-globe"></i>
          </button>
        </div>

        <!-- User profile -->
        <div class="user-profile">
          <button class="user-btn" onclick="toggleUserMenu()">
            <div class="user-avatar">
              <div class="avatar-fallback">
                <i class="fas fa-user"></i>
              </div>
            </div>
            <div class="user-info">
              <span class="user-name">John Doe</span>
              <span class="user-status">Online</span>
            </div>
            <i class="fas fa-chevron-down user-chevron" id="userChevron"></i>
          </button>

          <!-- User dropdown menu -->
          <div class="user-dropdown" id="userDropdown">
            <div class="dropdown-header">
              <div class="user-avatar-large">
                <div class="avatar-fallback">
                  <i class="fas fa-user"></i>
                </div>
              </div>
              <div class="user-details">
                <h4>John Doe</h4>
                <p><EMAIL></p>
                <div class="reputation-badge">
                  <i class="fas fa-star"></i>
                  <span>4.8</span>
                </div>
              </div>
            </div>
            
            <div class="dropdown-divider"></div>
            
            <div class="dropdown-menu">
              <a href="#" class="dropdown-item">
                <i class="fas fa-user-circle"></i>
                <span>Profile</span>
              </a>
              <a href="#" class="dropdown-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
              </a>
              <a href="#" class="dropdown-item">
                <i class="fas fa-question-circle"></i>
                <span>Help</span>
              </a>
              <div class="dropdown-divider"></div>
              <button class="dropdown-item logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile menu toggle -->
        <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
          <div class="hamburger" id="hamburger">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
      </div>
    </div>

    <!-- Mobile menu overlay -->
    <div class="mobile-overlay" id="mobileOverlay" onclick="closeMobileMenu()"></div>

    <!-- Mobile menu -->
    <div class="mobile-menu" id="mobileMenu">
      <div class="mobile-menu-header">
        <div class="mobile-user-info">
          <div class="user-avatar-mobile">
            <div class="avatar-fallback">
              <i class="fas fa-user"></i>
            </div>
          </div>
          <div class="user-details-mobile">
            <h3>John Doe</h3>
            <p><EMAIL></p>
            <div class="reputation-badge-mobile">
              <i class="fas fa-star"></i>
              <span>4.8</span>
            </div>
          </div>
        </div>
      </div>

      <div class="mobile-nav-links">
        <a href="#" class="mobile-nav-link active">
          <div class="mobile-link-content">
            <i class="fas fa-search"></i>
            <span>Browse</span>
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>
        <a href="#" class="mobile-nav-link">
          <div class="mobile-link-content">
            <i class="fas fa-list"></i>
            <span>My Offers</span>
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>
        <a href="#" class="mobile-nav-link">
          <div class="mobile-link-content">
            <i class="fas fa-plus-circle"></i>
            <span>Create</span>
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>
        <a href="#" class="mobile-nav-link">
          <div class="mobile-link-content">
            <i class="fas fa-comments"></i>
            <span>Chat</span>
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>
      </div>

      <div class="mobile-menu-footer">
        <div class="mobile-actions">
          <button class="mobile-action-btn" onclick="toggleTheme()">
            <i class="fas fa-moon"></i>
            <span>Toggle Theme</span>
          </button>
          <button class="mobile-action-btn" onclick="toggleLanguage()">
            <i class="fas fa-globe"></i>
            <span>Language</span>
          </button>
          <button class="mobile-action-btn logout-btn">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Demo content -->
  <div class="demo-content">
    <h1 class="demo-title">DreamNavBar</h1>
    <p class="demo-subtitle">A stunning, modern navbar built with pure HTML, CSS, and JavaScript. Features glassmorphism effects, smooth animations, and full responsive design.</p>
    
    <div class="demo-features">
      <div class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-magic"></i>
        </div>
        <h3 class="feature-title">Glassmorphism Design</h3>
        <p class="feature-desc">Beautiful glassmorphism effects with backdrop blur and translucent backgrounds that adapt to any content.</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-mobile-alt"></i>
        </div>
        <h3 class="feature-title">Fully Responsive</h3>
        <p class="feature-desc">Seamless transition between desktop and mobile with touch-friendly hamburger menu and drawer navigation.</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-palette"></i>
        </div>
        <h3 class="feature-title">Dark/Light Themes</h3>
        <p class="feature-desc">Instant theme switching with smooth transitions and proper color schemes for both light and dark modes.</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-rocket"></i>
        </div>
        <h3 class="feature-title">Smooth Animations</h3>
        <p class="feature-desc">Spring-based animations and micro-interactions that bring the interface to life with delightful user experience.</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-wifi"></i>
        </div>
        <h3 class="feature-title">Real-time Status</h3>
        <p class="feature-desc">Live connection status indicators with animated pulse effects and notification badges for real-time updates.</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-user-circle"></i>
        </div>
        <h3 class="feature-title">Rich User Menu</h3>
        <p class="feature-desc">Beautiful user profile dropdown with avatar, status, reputation, and comprehensive navigation options.</p>
      </div>
    </div>
  </div>

  <!-- Theme controls -->
  <div class="theme-controls">
    <button class="demo-btn" onclick="simulateNotification()">
      <i class="fas fa-bell"></i> Add Notification
    </button>
    <button class="demo-btn" onclick="cycleConnectionStatus()">
      <i class="fas fa-wifi"></i> Cycle Connection
    </button>
  </div>

  <script>
    // State management
    let isDark = false;
    let isEnglish = true;
    let userMenuOpen = false;
    let mobileMenuOpen = false;
    let notificationCount = 3;
    let connectionStatuses = ['excellent', 'good', 'poor', 'disconnected'];
    let connectionTexts = ['Excellent', 'Good', 'Poor', 'Disconnected'];
    let currentConnectionIndex = 0;

    // Theme toggle
    function toggleTheme() {
      isDark = !isDark;
      document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
      document.getElementById('themeIcon').className = isDark ? 'fas fa-sun' : 'fas fa-moon';
    }

    // Language toggle
    function toggleLanguage() {
      isEnglish = !isEnglish;
      document.getElementById('languageText').textContent = isEnglish ? 'EN' : 'FA';
      if (!isEnglish) {
        document.body.style.direction = 'rtl';
      } else {
        document.body.style.direction = 'ltr';
      }
    }

    // User menu toggle
    function toggleUserMenu() {
      userMenuOpen = !userMenuOpen;
      const dropdown = document.getElementById('userDropdown');
      const chevron = document.getElementById('userChevron');
      
      if (userMenuOpen) {
        dropdown.classList.add('show');
        chevron.classList.add('rotate');
      } else {
        dropdown.classList.remove('show');
        chevron.classList.remove('rotate');
      }
    }

    // Mobile menu toggle
    function toggleMobileMenu() {
      mobileMenuOpen = !mobileMenuOpen;
      const overlay = document.getElementById('mobileOverlay');
      const menu = document.getElementById('mobileMenu');
      const hamburger = document.getElementById('hamburger');
      
      if (mobileMenuOpen) {
        overlay.classList.add('show');
        menu.classList.add('show');
        hamburger.classList.add('active');
        document.body.style.overflow = 'hidden';
      } else {
        overlay.classList.remove('show');
        menu.classList.remove('show');
        hamburger.classList.remove('active');
        document.body.style.overflow = '';
      }
    }

    // Close mobile menu
    function closeMobileMenu() {
      if (mobileMenuOpen) {
        toggleMobileMenu();
      }
    }

    // Connection status cycle
    function cycleConnectionStatus() {
      currentConnectionIndex = (currentConnectionIndex + 1) % connectionStatuses.length;
      const status = connectionStatuses[currentConnectionIndex];
      const text = connectionTexts[currentConnectionIndex];
      
      const connectionEl = document.querySelector('.connection-status');
      connectionEl.className = `connection-status status-${status}`;
      document.getElementById('connectionText').textContent = text;
    }

    // Toggle connection (for demo)
    function toggleConnection() {
      cycleConnectionStatus();
    }

    // Simulate notification
    function simulateNotification() {
      notificationCount++;
      const badge = document.getElementById('notificationBadge');
      badge.innerHTML = `<span>${notificationCount > 99 ? '99+' : notificationCount}</span>`;
      
      // Add bounce animation
      badge.style.animation = 'none';
      setTimeout(() => {
        badge.style.animation = 'bounce 0.5s ease-in-out';
      }, 10);
    }

    // Toggle notifications (for demo)
    function toggleNotifications() {
      if (notificationCount > 0) {
        notificationCount = 0;
        document.getElementById('notificationBadge').style.display = 'none';
      } else {
        simulateNotification();
        document.getElementById('notificationBadge').style.display = 'flex';
      }
    }

    // Click outside handler
    document.addEventListener('click', function(event) {
      const userProfile = document.querySelector('.user-profile');
      if (userMenuOpen && !userProfile.contains(event.target)) {
        toggleUserMenu();
      }
    });

    // Navigation link handling
    document.querySelectorAll('.nav-link, .mobile-nav-link').forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Remove active class from all links
        document.querySelectorAll('.nav-link, .mobile-nav-link').forEach(l => {
          l.classList.remove('active');
        });
        
        // Add active class to clicked link
        this.classList.add('active');
        
        // Close mobile menu if open
        if (mobileMenuOpen) {
          closeMobileMenu();
        }
      });
    });

    // Initialize theme
    document.documentElement.setAttribute('data-theme', 'light');

    // Add some dynamic effects
    function addParticleEffect() {
      const navbar = document.querySelector('.dream-navbar');
      const particle = document.createElement('div');
      particle.style.cssText = `
        position: absolute;
        width: 4px;
        height: 4px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 50%;
        pointer-events: none;
        animation: float 3s ease-in-out infinite;
        left: ${Math.random() * 100}%;
        top: 50%;
        opacity: 0;
      `;
      
      navbar.appendChild(particle);
      
      // Remove particle after animation
      setTimeout(() => {
        if (particle.parentNode) {
          particle.parentNode.removeChild(particle);
        }
      }, 3000);
    }

    // Add floating particles occasionally
    setInterval(addParticleEffect, 2000);

    // Add CSS for floating animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes float {
        0% { transform: translateY(0) scale(0); opacity: 0; }
        50% { transform: translateY(-20px) scale(1); opacity: 0.8; }
        100% { transform: translateY(-40px) scale(0); opacity: 0; }
      }
    `;
    document.head.appendChild(style);

    console.log('🎨 DreamNavBar initialized with amazing features!');
  </script>
</body>
</html>
