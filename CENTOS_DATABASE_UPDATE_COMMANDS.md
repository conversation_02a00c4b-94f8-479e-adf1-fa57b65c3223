# Commands to Update Production Database Schema on CentOS 9 Host

## Prerequisites
- You must be on the CentOS 9 host machine where Docker containers are running
- Docker containers should be running (docker-compose up -d)
- You need the latest schema.prisma file from your Windows development environment

## Step-by-Step Commands

### 1. Navigate to your project directory
```bash
cd /path/to/your/munygo/project
```

### 2. Check if containers are running
```bash
docker-compose ps
```
(Should show backend container as "Up")

### 3. Copy latest schema.prisma to the backend container
```bash
docker cp ./backend/prisma/schema.prisma $(docker-compose ps -q backend):/app/prisma/schema.prisma
```

### 4. Apply schema changes to database (THIS IS THE KEY COMMAND)
```bash
docker-compose exec backend npx prisma db push --accept-data-loss
```
Note: The --accept-data-loss flag is needed because we're adding NOT NULL columns

### 5. Regenerate Prisma client
```bash
docker-compose exec backend npx prisma generate
```

### 6. Test the updated database
```bash
docker-compose exec backend node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function test() {
    try {
        const result = await prisma.debugReport.findMany({ take: 1 });
        console.log('✅ Database updated successfully!');
        await prisma.\$disconnect();
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}
test();
"
```

### 7. Verify new columns exist
```bash
docker-compose exec backend node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkSchema() {
    try {
        const columns = await prisma.\$queryRaw\`
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'debug_reports' 
            AND column_name IN ('priority', 'assigned_at', 'session_id', 'viewport_width', 'viewport_height')
            ORDER BY column_name;
        \`;
        console.log('New columns added:');
        console.table(columns);
        await prisma.\$disconnect();
    } catch (error) {
        console.error('Error:', error.message);
    }
}
checkSchema();
"
```

## Alternative: One-liner command
If you want to run everything in one command:
```bash
docker cp ./backend/prisma/schema.prisma $(docker-compose ps -q backend):/app/prisma/schema.prisma && docker-compose exec backend npx prisma db push --accept-data-loss && docker-compose exec backend npx prisma generate
```

## Important Notes:
- `prisma db push` directly applies schema changes without creating migration files
- It's perfect for development/staging environments
- The `--accept-data-loss` flag handles adding NOT NULL columns to existing tables
- This will update your production database to match your latest schema.prisma exactly

## Expected Output:
You should see:
- "Environment variables loaded from .env"
- "Prisma schema loaded from prisma/schema.prisma"  
- "🚀 Your database is now in sync with your Prisma schema."
- No errors about missing columns

After running these commands, your production database will have all the missing columns and the Debug Dashboard should work correctly!
