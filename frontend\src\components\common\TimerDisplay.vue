<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  timerDisplayValue: string | null
  timerColorClass: string
  timerLabel: string
  testId?: string
}

const props = withDefaults(defineProps<Props>(), {
  testId: 'timer-display'
})

// Show the timer only if we have a valid display value
const shouldShowTimer = computed(() => {
  return props.timerDisplayValue !== null && props.timerDisplayValue !== undefined
})
</script>

<template>
  <div v-if="shouldShowTimer"
       class="timer-display"
       :class="timerColorClass"
       :data-testid="testId">
    <span class="timer-value">{{ timerDisplayValue }}</span>
    <span class="timer-label">{{ timerLabel }}</span>
  </div>
</template>

<style scoped>
/* Timer Display Styles (from timer.md guide - consistent across components) */
.timer-display {
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  margin: 12px 0;
  border: 2px solid #10b981; /* Default green for plenty of time */
  background-color: rgba(16, 185, 129, 0.1);
  color: #065f46;
  transition: all 0.3s ease;
  font-weight: 600;
}

/* Plenty of time - Green (< 30 minutes elapsed) */
.timer-display.timer-plenty {
  background-color: rgba(16, 185, 129, 0.1);
  color: #065f46;
  border-color: #10b981;
}

/* Moderate time - Light Green (30-60 minutes elapsed) */
.timer-display.timer-moderate {
  background-color: rgba(34, 197, 94, 0.1);
  color: #166534;
  border-color: #22c55e;
}

/* Warning - Orange (1-2 hours elapsed) */
.timer-display.timer-warning {
  background-color: rgba(251, 146, 60, 0.1);
  color: #c2410c;
  border-color: #fb923c;
}

/* Critical - Red with pulse (>2 hours elapsed) */
.timer-display.timer-critical {
  background-color: rgba(239, 68, 68, 0.15);
  color: #dc2626;
  border-color: #ef4444;
  animation: pulse 2s infinite;
}

/* Expired - Dark Red (timer expired) */
.timer-display.timer-expired {
  background-color: rgba(245, 101, 101, 0.2);
  color: #991b1b;
  border-color: #f56565;
  border-style: dashed;
}

/* Elapsed - Gray (counting up after expiry) */
.timer-display.timer-elapsed {
  background-color: rgba(156, 163, 175, 0.1);
  color: #4b5563;
  border-color: #9ca3af;
  border-style: dotted;
}

/* Default fallback */
.timer-display.timer-default {
  background-color: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border-color: #3b82f6;
}

.timer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.timer-label {
  font-size: 0.85em;
  font-weight: 500;
}

.timer-value {
  font-size: 1.3em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}
</style>
