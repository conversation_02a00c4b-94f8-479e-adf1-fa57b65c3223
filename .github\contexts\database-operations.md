# Database Operations for MUNygo

## Prisma Schema Management

### Core Models

The database includes these main models with complex relationships:

- **User**: Authentication, profile, reputation
- **Offer**: Currency exchange offers with dynamic pricing
- **Interest**: User interest in offers with status tracking
- **ChatSession**: Real-time messaging between users
- **ChatMessage**: Individual messages with transaction integration
- **Transaction**: Multi-stage payment flow management
- **PayerNegotiation**: First-payer designation system
- **PaymentReceivingInfo**: User payment details
- **Notification**: Persistent notification system

### Schema Best Practices

```prisma
model User {
  id                    String   @id @default(cuid())
  email                 String   @unique
  phoneNumber           String?  @unique
  isEmailVerified       Boolean  @default(false)
  isPhoneVerified       Boolean  @default(false)
  reputation            Float    @default(0.0)
  
  // Relations
  offers                Offer[]
  interests             Interest[]
  chatSessions          ChatSession[]
  sentMessages          ChatMessage[]
  transactions          Transaction[]
  
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  
  @@map("users")
}
```

### Relationship Definitions

Always define explicit foreign key relationships:

```prisma
model Offer {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Bidirectional relations
  interests   Interest[]
  
  @@index([userId])
  @@index([createdAt])
}

model Interest {
  id          String   @id @default(cuid())
  offerId     String
  userId      String
  offer       Offer    @relation(fields: [offerId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([offerId, userId]) // Prevent duplicate interests
}
```

## Migration Workflow

### Development Migrations

```powershell
# Create new migration
cd backend
npm run prisma:migrate

# Reset database (development only)
npm run prisma:reset

# Generate Prisma client
npm run prisma:generate
```

### Production Deployment

```powershell
# Deploy migrations to production
npm run prisma:deploy

# Always commit schema.prisma + migration files together
git add prisma/schema.prisma prisma/migrations/
git commit -m "Add user profile fields migration"
```

### Migration Naming

Use descriptive migration names:

- `add_user_profile_fields`
- `create_transaction_flow_system`
- `update_offer_status_enum`
- `add_notification_types`

## Database Queries

### Efficient Query Patterns

```typescript
// ✅ Good - Selective includes with proper indexing
const offers = await prisma.offer.findMany({
  where: {
    status: 'ACTIVE',
    userId: { not: currentUserId }
  },
  include: {
    user: {
      select: {
        id: true,
        name: true,
        reputation: true,
        avatar: true
      }
    },
    interests: {
      where: { status: 'PENDING' },
      select: {
        id: true,
        status: true,
        user: {
          select: { id: true, name: true }
        }
      }
    }
  },
  orderBy: [
    { isPinned: 'desc' },
    { createdAt: 'desc' }
  ],
  take: 20,
  skip: offset
});
```

### Transaction Handling

Use Prisma transactions for atomic operations:

```typescript
const result = await prisma.$transaction(async (tx) => {
  // Update offer status
  const offer = await tx.offer.update({
    where: { id: offerId },
    data: { status: 'MATCHED' }
  });

  // Create chat session
  const chatSession = await tx.chatSession.create({
    data: {
      offerId: offerId,
      participants: {
        connect: [
          { id: offer.userId },
          { id: interestedUserId }
        ]
      }
    }
  });

  // Create notification
  await tx.notification.create({
    data: {
      userId: offer.userId,
      type: 'INTEREST_ACCEPTED',
      data: { offerId, chatSessionId: chatSession.id }
    }
  });

  return { offer, chatSession };
});
```

### Complex Queries with Dynamic Filtering

```typescript
// Dynamic offer filtering with reputation-based pricing
const offers = await prisma.offer.findMany({
  where: {
    AND: [
      { status: 'ACTIVE' },
      { userId: { not: currentUserId } },
      filters.currency ? { currency: filters.currency } : {},
      filters.minAmount ? { amount: { gte: filters.minAmount } } : {},
      filters.maxAmount ? { amount: { lte: filters.maxAmount } } : {}
    ]
  },
  include: {
    user: {
      select: {
        id: true,
        name: true,
        reputation: true,
        _count: {
          select: {
            offers: { where: { status: 'COMPLETED' } }
          }
        }
      }
    }
  }
});

// Calculate dynamic rates based on reputation
const offersWithDynamicRates = offers.map(offer => ({
  ...offer,
  dynamicRate: calculateDynamicRate(offer.baseRate, offer.user.reputation)
}));
```

## Data Seeding

### Development Seed Data

```typescript
// prisma/seed.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Create test users
  const users = await Promise.all([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User 1',
        isEmailVerified: true,
        reputation: 4.5
      }
    }),
    // More test data...
  ]);

  // Create test offers
  await Promise.all([
    prisma.offer.create({
      data: {
        title: 'USD to EUR Exchange',
        amount: 1000,
        currency: 'USD',
        targetCurrency: 'EUR',
        baseRate: 0.85,
        userId: users[0].id
      }
    })
    // More test offers...
  ]);
}

main()
  .catch(console.error)
  .finally(() => prisma.$disconnect());
```

## Database Monitoring

### Performance Optimization

```typescript
// Use database indexes for frequently queried fields
model Offer {
  // ... fields ...
  
  @@index([status, createdAt])
  @@index([currency, targetCurrency])
  @@index([userId, status])
}

// Monitor slow queries
const offers = await prisma.offer.findMany({
  // ... query ...
}).catch(error => {
  console.error('Database query failed:', {
    query: 'findManyOffers',
    error: error.message,
    timestamp: new Date()
  });
  throw error;
});
```

### Connection Management

```typescript
// client.ts - Proper Prisma client configuration
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error']
});

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}
```

## Backup and Recovery

### Database Backup Strategy

```powershell
# PostgreSQL backup (production)
pg_dump $DATABASE_URL > backup-$(date +%Y%m%d).sql

# Restore from backup
psql $DATABASE_URL < backup-20241230.sql
```

### Migration Rollback

```powershell
# Reset to specific migration (development only)
npx prisma migrate reset --force

# Deploy specific migration
npx prisma migrate deploy --schema=./prisma/schema.prisma
```

## Common Patterns

### Soft Deletes

```prisma
model Offer {
  id          String    @id @default(cuid())
  deletedAt   DateTime?
  
  @@map("offers")
}
```

```typescript
// Query excluding soft-deleted records
const activeOffers = await prisma.offer.findMany({
  where: { deletedAt: null }
});
```

### Audit Trails

```prisma
model AuditLog {
  id          String   @id @default(cuid())
  entityType  String   // 'offer', 'user', 'transaction'
  entityId    String
  action      String   // 'create', 'update', 'delete'
  changes     Json?    // Store old/new values
  userId      String?
  timestamp   DateTime @default(now())
  
  @@index([entityType, entityId])
  @@index([timestamp])
}
```
