import { test, expect } from '@playwright/test'

/**
 * E2E Tests for MUNygo Currency Exchange Platform
 * 
 * Tests the complete user journey from authentication to currency exchange,
 * focusing on mobile-first design and real-time features.
 */

test.describe('MUNygo Currency Exchange Platform', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set mobile-first viewport (375px - your primary target)
    await page.setViewportSize({ width: 375, height: 667 })
  })

  test.describe('Authentication Flow', () => {
    
    test('should complete mobile-first login flow', async ({ page }) => {
      await page.goto('/')
      
      // Test skeleton loading states
      await expect(page.locator('[data-testid="content-skeleton"]')).toBeVisible()
      await expect(page.locator('[data-testid="content-loaded"]')).toBeVisible({ timeout: 5000 })
      
      // Navigate to login
      await page.click('[data-testid="login-btn"]')
      
      // Test mobile-optimized form
      const emailInput = page.locator('[data-testid="email-input"]')
      const passwordInput = page.locator('[data-testid="password-input"]')
      const submitButton = page.locator('[data-testid="submit-btn"]')
      
      // Verify touch-friendly input sizes (minimum 44px height)
      const emailBox = await emailInput.boundingBox()
      const passwordBox = await passwordInput.boundingBox()
      const submitBox = await submitButton.boundingBox()
      
      expect(emailBox?.height).toBeGreaterThanOrEqual(44)
      expect(passwordBox?.height).toBeGreaterThanOrEqual(44)
      expect(submitBox?.height).toBeGreaterThanOrEqual(44)
      
      // Fill login form
      await emailInput.fill('<EMAIL>')
      await passwordInput.fill('password123')
      await submitButton.click()
      
      // Should show loading state
      await expect(submitButton.locator('.button-spinner')).toBeVisible()
      
      // Should redirect to dashboard on success
      await expect(page).toHaveURL(/\/dashboard|\/home/<USER>
      
      // Should show success notification
      await expect(page.locator('.n-message--success')).toBeVisible()
    })

    test('should handle email verification flow', async ({ page }) => {
      await page.goto('/verify-email')
      
      // Test verification code input
      const codeInput = page.locator('[data-testid="verification-code-input"]')
      await expect(codeInput).toBeVisible()
      
      // Should accept 6-digit verification code
      await codeInput.fill('123456')
      await page.click('[data-testid="verify-btn"]')
      
      // Should show verification success
      await expect(page.locator('[data-testid="verification-success"]')).toBeVisible()
    })

    test('should support Persian language authentication', async ({ page }) => {
      // Set Persian locale
      await page.goto('/?lang=fa')
      
      // Check RTL layout
      await expect(page.locator('html')).toHaveAttribute('dir', 'rtl')
      
      // Login form should display in Persian
      await page.click('[data-testid="login-btn"]')
      await expect(page.locator('[data-testid="email-label"]')).toContainText('ایمیل')
      await expect(page.locator('[data-testid="password-label"]')).toContainText('رمز عبور')
    })
  })

  test.describe('Currency Exchange Flow', () => {
    
    test.beforeEach(async ({ page }) => {
      // Login before each test
      await page.goto('/login')
      await page.fill('[data-testid="email-input"]', '<EMAIL>')
      await page.fill('[data-testid="password-input"]', 'password123')
      await page.click('[data-testid="submit-btn"]')
      await expect(page).toHaveURL(/\/dashboard|\/home/<USER>
    })

    test('should create currency exchange offer', async ({ page }) => {
      // Navigate to create offer
      await page.click('[data-testid="create-offer-btn"]')
      
      // Test mobile-optimized offer creation form
      const amountInput = page.locator('[data-testid="amount-input"]')
      const currencySelect = page.locator('[data-testid="currency-select"]')
      const targetCurrencySelect = page.locator('[data-testid="target-currency-select"]')
      const submitOfferBtn = page.locator('[data-testid="submit-offer-btn"]')
      
      // Fill offer details
      await amountInput.fill('100000') // 100,000 IRR
      await currencySelect.selectOption('IRR')
      await targetCurrencySelect.selectOption('CAD')
      
      // Test calculation display
      const calculatedAmount = page.locator('[data-testid="calculated-amount"]')
      await expect(calculatedAmount).toBeVisible()
      await expect(calculatedAmount).toContainText('CAD') // Should show CAD equivalent
      
      // Submit offer
      await submitOfferBtn.click()
      
      // Should show success and redirect to offer page
      await expect(page.locator('.n-message--success')).toBeVisible()
      await expect(page).toHaveURL(/\/offer\/\d+/)
    })

    test('should browse and filter offers', async ({ page }) => {
      await page.goto('/browse-offers')
      
      // Test mobile-first offer cards
      const offerCards = page.locator('[data-testid="offer-card"]')
      await expect(offerCards.first()).toBeVisible()
      
      // Test filtering
      await page.click('[data-testid="filter-btn"]')
      
      // Filter by currency
      await page.selectOption('[data-testid="currency-filter"]', 'CAD')
      await page.click('[data-testid="apply-filters-btn"]')
      
      // Should show filtered results
      await expect(page.locator('[data-testid="offer-card"]')).toContainText('CAD')
      
      // Test infinite scroll or pagination
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
      await expect(page.locator('[data-testid="loading-more"]')).toBeVisible()
    })

    test('should handle offer matching and notifications', async ({ page }) => {
      await page.goto('/offer/123')
      
      // Express interest in offer
      await page.click('[data-testid="express-interest-btn"]')
      
      // Should open negotiation
      await expect(page.locator('[data-testid="smart-negotiation-section"]')).toBeVisible()
      
      // Test real-time notifications
      // Simulate incoming notification via Socket.IO
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('offer-matched', {
          detail: {
            offerId: '123',
            matchedUserId: 'user456',
            message: 'New match found!'
          }
        }))
      })
      
      // Should show notification
      await expect(page.locator('[data-testid="notification-popup"]')).toBeVisible()
      await expect(page.locator('[data-testid="notification-popup"]')).toContainText('New match found!')
    })
  })

  test.describe('Transaction Chat & Negotiation', () => {
    
    test.beforeEach(async ({ page }) => {
      // Setup authenticated user in transaction
      await page.goto('/login')
      await page.fill('[data-testid="email-input"]', '<EMAIL>')
      await page.fill('[data-testid="password-input"]', 'password123')
      await page.click('[data-testid="submit-btn"]')
      await page.goto('/transaction/123')
    })

    test('should handle complete negotiation flow', async ({ page }) => {
      // Wait for negotiation section to load
      await expect(page.locator('[data-testid="smart-negotiation-section"]')).toBeVisible()
      
      // Test AI recommendation display
      await expect(page.locator('.recommendation-banner')).toBeVisible()
      await expect(page.locator('.recommendation-icon')).toContainText('🤖')
      
      // Test counter proposal
      await page.click('[data-testid="propose-counter-btn"]')
      await page.fill('[data-testid="custom-message-input"]', 'I can pay first if you provide additional verification.')
      await page.click('.submit-counter-btn')
      
      // Should show loading and success
      await expect(page.locator('.button-spinner')).toBeVisible()
      await expect(page.locator('.n-message--success')).toBeVisible()
    })

    test('should support real-time chat messages', async ({ page }) => {
      // Navigate to chat section
      const chatInput = page.locator('[data-testid="chat-input"]')
      const sendBtn = page.locator('[data-testid="send-message-btn"]')
      
      await chatInput.fill('Hello, when can we complete the exchange?')
      await sendBtn.click()
      
      // Should show message in chat
      await expect(page.locator('[data-testid="chat-message"]').last()).toContainText('Hello, when can we complete')
      
      // Simulate incoming message
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('chat-message', {
          detail: {
            message: 'I can meet tomorrow at 2 PM',
            senderId: 'other-user',
            timestamp: new Date().toISOString()
          }
        }))
      })
      
      // Should show incoming message
      await expect(page.locator('[data-testid="chat-message"]').last()).toContainText('I can meet tomorrow')
    })
  })

  test.describe('Mobile Performance & Accessibility', () => {
    
    test('should meet mobile performance budgets', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto('/')
      await expect(page.locator('[data-testid="content-loaded"]')).toBeVisible()
      
      const loadTime = Date.now() - startTime
      
      // Should load within 3 seconds on mobile
      expect(loadTime).toBeLessThan(3000)
      
      // Test JavaScript bundle size impact
      const performanceMetrics = await page.evaluate(() => ({
        navigationStart: performance.timing.navigationStart,
        loadEventEnd: performance.timing.loadEventEnd,
        domContentLoadedEventEnd: performance.timing.domContentLoadedEventEnd
      }))
      
      const totalLoadTime = performanceMetrics.loadEventEnd - performanceMetrics.navigationStart
      expect(totalLoadTime).toBeLessThan(5000) // 5 seconds total
    })

    test('should be accessible with screen readers', async ({ page }) => {
      await page.goto('/')
      
      // Check for proper heading structure
      const h1 = page.locator('h1')
      await expect(h1).toBeVisible()
      
      // Check for alt text on images
      const images = page.locator('img')
      const imageCount = await images.count()
      
      for (let i = 0; i < imageCount; i++) {
        const img = images.nth(i)
        const alt = await img.getAttribute('alt')
        expect(alt).toBeTruthy()
      }
      
      // Check for proper form labels
      const inputs = page.locator('input')
      const inputCount = await inputs.count()
      
      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i)
        const id = await input.getAttribute('id')
        if (id) {
          const label = page.locator(`label[for="${id}"]`)
          await expect(label).toBeVisible()
        }
      }
    })

    test('should work with reduced motion preferences', async ({ page }) => {
      // Set reduced motion preference
      await page.emulateMedia({ reducedMotion: 'reduce' })
      
      await page.goto('/')
      
      // Animations should be disabled or reduced
      const animatedElements = page.locator('.animate, [class*="animate"]')
      
      if (await animatedElements.count() > 0) {
        const firstElement = animatedElements.first()
        const animationDuration = await firstElement.evaluate(el => 
          getComputedStyle(el).animationDuration
        )
        
        // Should be either 0s or very short for reduced motion
        expect(animationDuration === '0s' || parseFloat(animationDuration) < 0.1).toBeTruthy()
      }
    })

    test('should handle offline scenarios gracefully', async ({ page }) => {
      await page.goto('/')
      
      // Go offline
      await page.context().setOffline(true)
      
      // Try to perform an action
      await page.click('[data-testid="create-offer-btn"]').catch(() => {
        // Expected to fail
      })
      
      // Should show offline indicator or error message
      await expect(page.locator('[data-testid="offline-indicator"], .network-error')).toBeVisible()
      
      // Go back online
      await page.context().setOffline(false)
      
      // Should recover functionality
      await page.reload()
      await expect(page.locator('[data-testid="content-loaded"]')).toBeVisible()
    })
  })

  test.describe('Cross-browser Mobile Testing', () => {
    
    test('should work consistently across mobile browsers', async ({ page, browserName }) => {
      await page.goto('/')
      
      // Test core functionality works in all browsers
      await expect(page.locator('[data-testid="content-loaded"]')).toBeVisible()
      
      // Test touch interactions
      const button = page.locator('[data-testid="login-btn"]')
      await button.tap()
      
      // Should navigate consistently
      await expect(page).toHaveURL(/\/login/)
      
      // Test form interactions
      const emailInput = page.locator('[data-testid="email-input"]')
      await emailInput.tap()
      await emailInput.fill('<EMAIL>')
      
      const emailValue = await emailInput.inputValue()
      expect(emailValue).toBe('<EMAIL>')
    })
  })
})
