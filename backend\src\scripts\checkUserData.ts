import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkUserData() {
  try {
    const email = '<EMAIL>';
    
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        phoneNumber: true,
        phoneVerified: true,
        createdAt: true,
        username: true,
        reputationScore: true,
        reputationLevel: true,
      },
    });

    if (!user) {
      console.log(`User ${email} not found`);
      return;
    }

    console.log('\n=== USER DATA FROM DATABASE ===');
    console.log(JSON.stringify(user, null, 2));

    // Calculate what the reputation should be based on the logic in auth.ts
    const accountAgeInDays = Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24));
    
    let calculatedScore = 0;
    if (user.emailVerified) {
      calculatedScore += 10;
    }
    if (user.phoneVerified) {
      calculatedScore += 15;
    }
    calculatedScore += Math.min(accountAgeInDays, 30);

    let calculatedLevel = 1;
    if (calculatedScore < 10) calculatedLevel = 1; // Newcomer
    else if (calculatedScore < 25) calculatedLevel = 2; // Verified
    else if (calculatedScore < 40) calculatedLevel = 3; // Reliable
    else if (calculatedScore < 60) calculatedLevel = 4; // Trusted
    else calculatedLevel = 5; // Elite

    console.log('\n=== CALCULATED VALUES (AUTH.TS LOGIC) ===');
    console.log(`Account age in days: ${accountAgeInDays}`);
    console.log(`Email verified bonus: ${user.emailVerified ? 10 : 0}`);
    console.log(`Phone verified bonus: ${user.phoneVerified ? 15 : 0}`);
    console.log(`Age bonus (max 30): ${Math.min(accountAgeInDays, 30)}`);
    console.log(`Total calculated score: ${calculatedScore}`);
    console.log(`Calculated level: ${calculatedLevel}`);
    
    console.log('\n=== COMPARISON ===');
    console.log(`DB Score: ${user.reputationScore} vs Calculated: ${calculatedScore}`);
    console.log(`DB Level: ${user.reputationLevel} vs Calculated: ${calculatedLevel}`);

  } catch (error) {
    console.error('Error checking user data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUserData();
