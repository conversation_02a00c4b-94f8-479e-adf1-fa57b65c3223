import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting to seed test payment methods...');

  // Get test users
  const testUsers = await prisma.user.findMany({
    where: {
      email: {
        in: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
      }
    }
  });

  if (testUsers.length === 0) {
    console.error('No test users found. Please run seed:test-users first.');
    return;
  }

  const paymentMethods = [
    // Iranian payment methods
    {
      currency: 'IRR',
      paymentMethodType: 'BANK_TRANSFER' as const,
      bankName: 'Melli Bank',
      accountNumber: '****************',
      accountHolderName: 'حسین احمدی',
      isDefaultForUser: true,
      isVerified: true
    },
    {
      currency: 'IRR', 
      paymentMethodType: 'BANK_TRANSFER' as const,
      bankName: 'Saderat Bank',
      accountNumber: '****************',
      accountHolderName: 'حسین احمدی',
      isDefaultForUser: false,
      isVerified: true
    },
    // Turkish payment methods
    {
      currency: 'TRY',
      paymentMethodType: 'BANK_TRANSFER' as const,
      bankName: 'Ziraat Bankası',
      accountNumber: '**************************',
      accountHolderName: 'Hasan Ahmadi',
      isDefaultForUser: true,
      isVerified: true
    },
    // USD payment methods
    {
      currency: 'USD',
      paymentMethodType: 'BANK_TRANSFER' as const,
      bankName: 'Wells Fargo',
      accountNumber: '****************',
      accountHolderName: 'Hassan Ahmadi',
      isDefaultForUser: true,
      isVerified: true
    },
    // EUR payment methods
    {
      currency: 'EUR',
      paymentMethodType: 'BANK_TRANSFER' as const,
      bankName: 'Deutsche Bank',
      accountNumber: '**********************',
      accountHolderName: 'Hassan Ahmadi',
      isDefaultForUser: true,
      isVerified: false // Mix of verified/unverified
    }
  ];

  // Create payment methods for each user
  for (const user of testUsers) {
    for (const paymentMethodData of paymentMethods) {
      try {
        // Check if this payment method already exists for this user/currency
        const existing = await prisma.paymentReceivingInfo.findFirst({
          where: {
            userId: user.id,
            currency: paymentMethodData.currency,
            bankName: paymentMethodData.bankName
          }
        });

        if (!existing) {
          const paymentMethod = await prisma.paymentReceivingInfo.create({
            data: {
              userId: user.id,
              currency: paymentMethodData.currency,
              paymentMethodType: paymentMethodData.paymentMethodType,
              bankName: paymentMethodData.bankName,
              accountNumber: paymentMethodData.accountNumber,
              accountHolderName: paymentMethodData.accountHolderName,
              isDefaultForUser: paymentMethodData.isDefaultForUser
            }
          });

          console.log(`Created payment method for ${user.email}: ${paymentMethodData.currency} - ${paymentMethodData.bankName}`);
        } else {
          console.log(`Payment method already exists for ${user.email}: ${paymentMethodData.currency} - ${paymentMethodData.bankName}`);
        }
      } catch (error) {
        console.error(`Failed to create payment method for ${user.email}:`, error);
      }
    }
  }

  console.log('Test payment methods seeding complete.');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('Payment methods seeding script failed:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
