const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testMultipleMatchesScenario() {
  console.log('=== TESTING MULTIPLE MATCHES SCENARIO ===\n');
  
  try {
    // First, let's see what the current matching logic does
    console.log('SCENARIO: One offer matches multiple users\n');
    
    console.log('Setup:');
    console.log('- User A: SELL 100 CAD-IRR @ 200');
    console.log('- User B: SELL 100 CAD-IRR @ 200');  
    console.log('- User C: SELL 100 CAD-IRR @ 200');
    console.log('- User D creates: BUY 100 CAD-IRR @ 200');
    console.log('');
    console.log('Question: Does User D get matched with A, B, C, or all three?');
    
    // Let's trace through the findPotentialMatches logic
    console.log('\n=== CURRENT SYSTEM BEHAVIOR ===');
    
    // Simulate what happens when User D creates BUY 100@200
    const potentialMatches = await prisma.offer.findMany({
      where: {
        type: 'SELL',
        currencyPair: 'CAD-IRR',
        status: 'ACTIVE',
        amount: 100,
        baseRate: {
          gte: 200 * 0.99, // 198
          lte: 200 * 1.01  // 202
        }
      },
      include: {
        user: { select: { username: true, id: true } }
      }
    });
    
    console.log(`System finds ${potentialMatches.length} compatible SELL offers:`);
    potentialMatches.forEach((offer, index) => {
      console.log(`${index + 1}. ${offer.user.username}: SELL ${offer.amount}@${offer.baseRate}`);
    });
    
    if (potentialMatches.length > 1) {
      console.log('\n🤔 MULTIPLE MATCHES FOUND!');
      console.log('Current system behavior:');
      console.log('├─ Creates match with User A ✅');
      console.log('├─ Creates match with User B ✅');  
      console.log('├─ Creates match with User C ✅');
      console.log('└─ User D gets 3 separate match notifications!');
      
      console.log('\nImplications:');
      console.log('✅ Pro: User D has choice of trading partners');
      console.log('✅ Pro: Better matching rate - higher chance of completion');
      console.log('❓ Question: What happens when User D accepts one match?');
      console.log('❓ Question: Should other matches be auto-cancelled?');
    }
    
    // Let's examine what happens in a real scenario
    console.log('\n=== REAL SCENARIO ANALYSIS ===');
    
    // Find offers that could create multiple matches
    const allOffers = await prisma.offer.findMany({
      where: { status: 'ACTIVE' },
      include: { user: { select: { username: true } } }
    });
    
    // Group by type, amount, and rate to find potential multi-matches
    const potentialMultiMatches = new Map();
    
    allOffers.forEach(offer => {
      const key = `${offer.type}-${offer.amount}-${offer.baseRate}-${offer.currencyPair}`;
      if (!potentialMultiMatches.has(key)) {
        potentialMultiMatches.set(key, []);
      }
      potentialMultiMatches.get(key).push(offer);
    });
    
    console.log('Analyzing current offers for potential multi-matches:');
    let foundMultiMatch = false;
    
    for (const [key, offers] of potentialMultiMatches) {
      if (offers.length > 1) {
        foundMultiMatch = true;
        const [type, amount, rate, pair] = key.split('-');
        console.log(`\n🎯 POTENTIAL MULTI-MATCH: ${type} ${amount} ${pair} @ ${rate}`);
        console.log('   Users:');
        offers.forEach((offer, index) => {
          console.log(`   ${index + 1}. ${offer.user.username}`);
        });
        
        // If someone created the opposite offer, they'd match with ALL of these
        const oppositeType = type === 'BUY' ? 'SELL' : 'BUY';
        console.log(`   📝 If someone creates ${oppositeType} ${amount} @ ${rate}, they'd get ${offers.length} matches!`);
      }
    }
    
    if (!foundMultiMatch) {
      console.log('No current multi-match scenarios in the database.');
      console.log('(All current offers are different amounts/rates/types)');
    }
    
    console.log('\n=== DESIGN CONSIDERATIONS ===');
    console.log('');
    console.log('Option 1: Multiple Matches (Current System)');
    console.log('  ✅ User gets maximum choice');
    console.log('  ✅ Higher probability of successful trade');
    console.log('  ✅ Market-like behavior');
    console.log('  ❌ Could overwhelm users with notifications');
    console.log('  ❌ Need to handle what happens when one is accepted');
    console.log('');
    console.log('Option 2: Single "Best" Match');
    console.log('  ✅ Simpler user experience');
    console.log('  ✅ No notification spam');
    console.log('  ❌ User misses potentially better partners');
    console.log('  ❌ Need to define "best" (first? highest rep? random?)');
    console.log('');
    console.log('Option 3: Hybrid Approach');
    console.log('  ✅ Show top 3 matches');
    console.log('  ✅ Balanced choice vs simplicity');
    console.log('  ✅ Can prioritize by reputation/history');
    console.log('');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMultipleMatchesScenario();
