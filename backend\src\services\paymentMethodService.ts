import { PrismaClient, PaymentReceivingInfo, PaymentMethodType, Prisma } from '@prisma/client';
import { ILogger } from '../utils/logger';

export interface CreatePaymentMethodPayload {
  currency: string;
  paymentMethodType: PaymentMethodType;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  swiftCode?: string;
  iban?: string;
  routingNumber?: string;
  sortCode?: string;
  bsb?: string;
  notes?: string;
}

export interface UpdatePaymentMethodPayload {
  currency?: string;
  paymentMethodType?: PaymentMethodType;
  bankName?: string;
  accountNumber?: string;
  accountHolderName?: string;
  swiftCode?: string;
  iban?: string;
  routingNumber?: string;
  sortCode?: string;
  bsb?: string;
  notes?: string;
  isActive?: boolean;
}

export interface PaymentMethodWithValidation extends PaymentReceivingInfo {
  validationStatus: 'complete' | 'incomplete';
  missingFields: string[];
}

// Type for Prisma transaction client
type PrismaTransaction = Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>;

export class PaymentMethodService {
  constructor(
    private prisma: PrismaClient | PrismaTransaction,
    private logger: ILogger
  ) {}

  /**
   * Get all payment methods for a user
   */
  async getUserPaymentMethods(userId: string): Promise<PaymentMethodWithValidation[]> {
    this.logger.info('Getting payment methods for user', { userId });
    
    try {
      const methods = await this.prisma.paymentReceivingInfo.findMany({
        where: { 
          userId,
          isActive: true
        },
        orderBy: [
          { isDefaultForUser: 'desc' },
          { currency: 'asc' },
          { createdAt: 'desc' }
        ]
      });

      return methods.map(method => this.addValidationStatus(method));
    } catch (error) {
      this.logger.error('Failed to get user payment methods', { userId, error });
      throw new Error('Failed to retrieve payment methods');
    }
  }

  /**
   * Get payment methods for a user by currency
   */
  async getUserPaymentMethodsByCurrency(userId: string, currency: string): Promise<PaymentMethodWithValidation[]> {
    this.logger.info('Getting payment methods for user by currency', { userId, currency });
    
    try {
      const methods = await this.prisma.paymentReceivingInfo.findMany({
        where: { 
          userId,
          currency,
          isActive: true
        },
        orderBy: [
          { isDefaultForUser: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      return methods.map(method => this.addValidationStatus(method));
    } catch (error) {
      this.logger.error('Failed to get user payment methods by currency', { userId, currency, error });
      throw new Error('Failed to retrieve payment methods for currency');
    }
  }

  /**
   * Create a new payment method
   */
  async createPaymentMethod(userId: string, data: CreatePaymentMethodPayload): Promise<PaymentMethodWithValidation> {
    this.logger.info('Creating payment method', { userId, currency: data.currency, type: data.paymentMethodType });
    
    try {
      // If this.prisma is already a transaction client, use it directly
      // Otherwise, wrap in a transaction
      const executeInTransaction = async (tx: PrismaTransaction) => {
        // Check if this is the first active payment method for this currency
        const existingActiveMethods = await tx.paymentReceivingInfo.count({
          where: {
            userId,
            currency: data.currency,
            isActive: true
          }
        });

        const isFirstMethod = existingActiveMethods === 0;

        // If this will be the default method, clear existing default flags for this currency
        // This handles both active and inactive methods to avoid constraint violations
        if (isFirstMethod) {
          // First, find all existing default methods for this currency (active or inactive)
          const existingDefaults = await tx.paymentReceivingInfo.findMany({
            where: {
              userId,
              currency: data.currency,
              isDefaultForUser: true
            },
            select: { id: true }
          });

          // Update each existing default method individually to avoid constraint issues
          for (const existing of existingDefaults) {
            await tx.paymentReceivingInfo.update({
              where: { id: existing.id },
              data: { isDefaultForUser: false }
            });
          }
        }

        // Create the new payment method
        return await tx.paymentReceivingInfo.create({
          data: {
            userId,
            currency: data.currency,
            paymentMethodType: data.paymentMethodType,
            bankName: data.bankName,
            accountNumber: data.accountNumber,
            accountHolderName: data.accountHolderName,
            swiftCode: data.swiftCode,
            iban: data.iban,
            routingNumber: data.routingNumber,
            sortCode: data.sortCode,
            bsb: data.bsb,
            notes: data.notes,
            isDefaultForUser: isFirstMethod, // First method becomes default
            isActive: true
          }
        });
      };

      // Check if we already have a transaction client or need to create one
      const method = '$transaction' in this.prisma 
        ? await (this.prisma as PrismaClient).$transaction(executeInTransaction)
        : await executeInTransaction(this.prisma as PrismaTransaction);

      this.logger.info('Payment method created successfully', { methodId: method.id, userId });
      return this.addValidationStatus(method);
    } catch (error) {
      this.logger.error('Failed to create payment method', { userId, error });
      throw new Error('Failed to create payment method');
    }
  }

  /**
   * Update an existing payment method
   */
  async updatePaymentMethod(methodId: string, userId: string, data: UpdatePaymentMethodPayload): Promise<PaymentMethodWithValidation> {
    this.logger.info('Updating payment method', { methodId, userId });
    
    try {
      // Verify ownership
      const existingMethod = await this.prisma.paymentReceivingInfo.findFirst({
        where: { id: methodId, userId }
      });

      if (!existingMethod) {
        throw new Error('Payment method not found or access denied');
      }

      const method = await this.prisma.paymentReceivingInfo.update({
        where: { id: methodId },
        data: {
          ...data,
          updatedAt: new Date()
        }
      });

      this.logger.info('Payment method updated successfully', { methodId, userId });
      return this.addValidationStatus(method);
    } catch (error) {
      this.logger.error('Failed to update payment method', { methodId, userId, error });
      throw new Error('Failed to update payment method');
    }
  }

  /**
   * Set a payment method as default for its currency
   */
  async setAsDefault(methodId: string, userId: string): Promise<PaymentMethodWithValidation> {
    this.logger.info('Setting payment method as default', { methodId, userId });
    
    try {
      // Get the method to check currency
      const method = await this.prisma.paymentReceivingInfo.findFirst({
        where: { id: methodId, userId }
      });

      if (!method) {
        throw new Error('Payment method not found or access denied');
      }

      // Transaction logic
      const executeInTransaction = async (tx: PrismaTransaction) => {
        // Remove default flag from other methods of the same currency
        await tx.paymentReceivingInfo.updateMany({
          where: {
            userId,
            currency: method.currency,
            isDefaultForUser: true,
            id: { not: methodId }
          },
          data: { isDefaultForUser: false }
        });

        // Set this method as default
        return await tx.paymentReceivingInfo.update({
          where: { id: methodId },
          data: { isDefaultForUser: true }
        });
      };

      // Check if we already have a transaction client or need to create one
      const updatedMethod = '$transaction' in this.prisma 
        ? await (this.prisma as PrismaClient).$transaction(executeInTransaction)
        : await executeInTransaction(this.prisma as PrismaTransaction);

      this.logger.info('Payment method set as default successfully', { methodId, userId });
      return this.addValidationStatus(updatedMethod);
    } catch (error) {
      this.logger.error('Failed to set payment method as default', { methodId, userId, error });
      throw new Error('Failed to set payment method as default');
    }
  }

  /**
   * Deactivate a payment method (soft delete) - Updated July 1, 2025
   */
  async deactivatePaymentMethod(methodId: string, userId: string): Promise<boolean> {
    this.logger.info('Deactivating payment method', { methodId, userId });
    
    try {
      // Verify ownership
      const existingMethod = await this.prisma.paymentReceivingInfo.findFirst({
        where: { id: methodId, userId, isActive: true }
      });

      if (!existingMethod) {
        throw new Error('Payment method not found or access denied');
      }

      await this.prisma.paymentReceivingInfo.update({
        where: { id: methodId },
        data: { 
          isActive: false,
          isDefaultForUser: false // Clear default flag when deactivating to avoid constraint issues
        }
      });

      this.logger.info('Payment method deactivated successfully', { methodId, userId });
      return true;
    } catch (error) {
      this.logger.error('Failed to deactivate payment method', { methodId, userId, error });
      throw new Error('Failed to deactivate payment method');
    }
  }

  /**
   * Get the default payment method for a user and currency
   */
  async getDefaultPaymentMethod(userId: string, currency: string): Promise<PaymentMethodWithValidation | null> {
    this.logger.info('Getting default payment method', { userId, currency });
    
    try {
      const method = await this.prisma.paymentReceivingInfo.findFirst({
        where: {
          userId,
          currency,
          isDefaultForUser: true,
          isActive: true
        }
      });

      return method ? this.addValidationStatus(method) : null;
    } catch (error) {
      this.logger.error('Failed to get default payment method', { userId, currency, error });
      throw new Error('Failed to retrieve default payment method');
    }
  }

  /**
   * Private helper to add validation status to payment method
   */
  private addValidationStatus(method: PaymentReceivingInfo): PaymentMethodWithValidation {
    const missingFields: string[] = [];
    
    // Check required fields
    if (!method.bankName) missingFields.push('bankName');
    if (!method.accountNumber) missingFields.push('accountNumber');
    if (!method.accountHolderName) missingFields.push('accountHolderName');

    const validationStatus = missingFields.length === 0 ? 'complete' : 'incomplete';

    return {
      ...method,
      validationStatus,
      missingFields
    };
  }
}
