<template>
  <n-button
    quaternary
    circle
    size="medium"
    class="theme-toggle"
    @click="toggleTheme"
    :title="isDark ? t('common.switchToLight') : t('common.switchToDark')"
  >
    <template #icon>
      <n-icon size="18">
        <SunnySharp v-if="isDark" />
        <MoonSharp v-else />
      </n-icon>
    </template>
  </n-button>
</template>

<script setup lang="ts">
import { NButton, NIcon } from 'naive-ui';
import { SunnySharp, MoonSharp } from '@vicons/ionicons5';
import { useThemeStore } from '@/stores/theme';
import { useI18n } from 'vue-i18n';

const themeStore = useThemeStore();
const { t } = useI18n();

const { isDark, toggleTheme } = themeStore;
</script>

<style scoped>
.theme-toggle {
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
}

.theme-toggle:hover {
  color: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

[data-theme="light"] .theme-toggle {
  color: rgba(33, 53, 71, 0.8);
}

[data-theme="light"] .theme-toggle:hover {
  color: rgba(33, 53, 71, 1);
}
</style>
