# MUNygo Development Stop Script
# Run this script when you're done with development

Write-Host "🛑 Stopping MUNygo Development Environment..." -ForegroundColor Yellow
Write-Host ""

# Stop PostgreSQL Container
Write-Host "📊 Stopping PostgreSQL Database..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml down

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ PostgreSQL stopped successfully" -ForegroundColor Green
} else {
    Write-Host "⚠️  Some containers may still be running" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔧 If you need to kill any remaining Node.js processes:" -ForegroundColor Cyan
Write-Host "Run: taskkill /F /IM node.exe" -ForegroundColor White
Write-Host ""
Write-Host "💾 Your data is preserved in Docker volumes." -ForegroundColor Green
Write-Host "🚀 Next time, just run: .\dev-start.ps1" -ForegroundColor Cyan
Write-Host ""
