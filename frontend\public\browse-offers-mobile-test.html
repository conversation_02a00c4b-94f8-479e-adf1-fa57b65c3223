<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse Offers - Mobile UI Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .browse-offers-view {
            min-height: 100vh;
            padding-bottom: 2rem;
        }
        
        /* Hero Search Section */
        .search-hero-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 1rem;
            text-align: center;
        }
        
        .hero-content {
            margin-bottom: 2rem;
        }
        
        .hero-title {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .hero-subtitle {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0;
        }
        
        /* Search Section */
        .search-section {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .search-bar-container {
            margin-bottom: 1rem;
        }
        
        .search-input {
            width: 100%;
            height: 56px;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 28px;
            padding: 0 1.5rem 0 3rem;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .search-input:focus {
            border-color: #2080f0;
            box-shadow: 0 4px 20px rgba(32, 128, 240, 0.2);
        }
        
        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        /* Quick Filters */
        .quick-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
            margin-bottom: 1rem;
        }
        
        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .filter-btn.active {
            background: #2080f0;
            border-color: #2080f0;
            box-shadow: 0 4px 15px rgba(32, 128, 240, 0.3);
        }
        
        .filter-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }
        
        /* Filter Chips */
        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
            margin-bottom: 1rem;
        }
        
        .filter-chip {
            background: rgba(32, 128, 240, 0.9);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .chip-close {
            cursor: pointer;
            margin-left: 0.25rem;
        }
        
        /* Offers Grid */
        .offers-grid {
            padding: 1rem;
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        @media (min-width: 640px) {
            .offers-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (min-width: 1024px) {
            .offers-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        /* Enhanced Offer Card */
        .enhanced-offer-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 1.25rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            min-height: 220px;
            display: flex;
            flex-direction: column;
            cursor: pointer;
        }
        
        .enhanced-offer-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        /* Card Header */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .offer-type-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .offer-type-badge.buy {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border: 1px solid rgba(39, 174, 96, 0.2);
        }
        
        .offer-type-badge.sell {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.2);
        }
        
        /* Amount Section */
        .amount-section {
            text-align: center;
            padding: 0.75rem 0;
            flex: 1;
        }
        
        .primary-amount {
            display: flex;
            align-items: baseline;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .currency {
            font-size: 0.875rem;
            font-weight: 600;
            color: #18a058;
            background: rgba(24, 160, 88, 0.1);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }
        
        .amount {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1e293b;
            line-height: 1;
        }
        
        .exchange-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            align-items: center;
        }
        
        .rate {
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
        }
        
        .total-value {
            display: flex;
            gap: 0.375rem;
            align-items: center;
            font-size: 0.8rem;
        }
        
        .total-label {
            color: #94a3b8;
        }
        
        .total-amount {
            color: #2080f0;
            font-weight: 600;
        }
        
        /* User Section */
        .user-section {
            border-top: 1px solid rgba(0, 0, 0, 0.06);
            padding-top: 0.75rem;
            text-align: center;
        }
        
        .username {
            font-size: 0.9rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.375rem;
        }
        
        .user-stats {
            display: flex;
            justify-content: center;
            gap: 1rem;
            font-size: 0.75rem;
            color: #64748b;
        }
        
        .completion-rate {
            color: #18a058;
            font-weight: 500;
        }
        
        .response-time {
            color: #2080f0;
            font-weight: 500;
        }
        
        /* Card Actions */
        .card-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;
            padding-top: 1rem;
            border-top: 1px solid rgba(0, 0, 0, 0.06);
        }
        
        .action-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .created-time {
            font-size: 0.7rem;
            color: #94a3b8;
        }
        
        .offer-status {
            font-size: 0.7rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #18a058;
        }
        
        .interest-button {
            background: #2080f0;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }
        
        .interest-button:hover {
            background: #1c7ed6;
            transform: translateY(-1px);
        }
        
        /* Loading Skeleton */
        .offers-skeleton {
            padding: 1rem;
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        @media (min-width: 640px) {
            .offers-skeleton {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (min-width: 1024px) {
            .offers-skeleton {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        .skeleton-card {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 1.25rem;
            backdrop-filter: blur(10px);
            min-height: 220px;
            display: flex;
            flex-direction: column;
        }
        
        .skeleton-header,
        .skeleton-line,
        .skeleton-footer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 4px;
        }
        
        .skeleton-header {
            height: 20px;
            margin-bottom: 1rem;
        }
        
        .skeleton-line {
            height: 16px;
            margin-bottom: 0.5rem;
        }
        
        .skeleton-line.short {
            width: 60%;
        }
        
        .skeleton-line.medium {
            width: 80%;
        }
        
        .skeleton-footer {
            height: 32px;
            margin-top: auto;
        }
        
        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }
        
        /* Empty State */
        .enhanced-empty-state {
            text-align: center;
            padding: 3rem 1rem;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .empty-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: white;
            margin-bottom: 0.5rem;
        }
        
        .empty-description {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
        }
        
        .clear-filters-btn {
            background: #2080f0;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 24px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .clear-filters-btn:hover {
            background: #1c7ed6;
            transform: translateY(-2px);
        }
        
        /* Stats Summary */
        .stats-summary {
            display: flex;
            justify-content: center;
            gap: 2rem;
            padding: 2rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            margin: 2rem 1rem;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stats-item {
            text-align: center;
        }
        
        .stats-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.25rem;
        }
        
        .stats-label {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.8);
        }
        
        /* Animations */
        .animate-fade-up {
            animation: fadeUp 0.6s ease-out forwards;
        }
        
        @keyframes fadeUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Responsive adjustments */
        @media (min-width: 768px) {
            .search-hero-section {
                padding: 3rem 2rem;
            }
            
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.125rem;
            }
            
            .quick-filters {
                gap: 1rem;
            }
        }
        
        /* Dark mode simulation */
        .dark-mode {
            filter: invert(1) hue-rotate(180deg);
        }
        
        .dark-mode img,
        .dark-mode video {
            filter: invert(1) hue-rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="browse-offers-view">
        <!-- Hero Search Section -->
        <div class="search-hero-section animate-fade-up">
            <div class="hero-content">
                <h1 class="hero-title">Browse Offers</h1>
                <p class="hero-subtitle">Find the perfect currency exchange match</p>
            </div>
            
            <!-- Enhanced Search Bar -->
            <div class="search-section">
                <div class="search-bar-container">
                    <input 
                        type="text" 
                        class="search-input" 
                        placeholder="Search offers by amount, rate, or user..."
                    >
                </div>
                
                <!-- Filter Chips -->
                <div class="filter-chips">
                    <div class="filter-chip">
                        Buying CAD
                        <span class="chip-close">×</span>
                    </div>
                    <div class="filter-chip">
                        Min: $500
                        <span class="chip-close">×</span>
                    </div>
                </div>
                
                <!-- Quick Filters -->
                <div class="quick-filters">
                    <button class="filter-btn active">All</button>
                    <button class="filter-btn">Buying CAD</button>
                    <button class="filter-btn">Selling CAD</button>
                    <button class="filter-btn">🔧 Filters</button>
                </div>
            </div>
        </div>

        <!-- Offers Grid -->
        <div class="offers-grid animate-fade-up">
            <!-- Sample Offer Cards -->
            <div class="enhanced-offer-card" style="animation-delay: 0s;">
                <div class="card-header">
                    <div class="offer-type-badge buy">
                        📈 Buying
                    </div>
                    <div class="user-reputation">⭐</div>
                </div>
                
                <div class="amount-section">
                    <div class="primary-amount">
                        <span class="currency">CAD</span>
                        <span class="amount">$1,500</span>
                    </div>
                    <div class="exchange-info">
                        <span class="rate">32,450 IRR/CAD</span>
                        <div class="total-value">
                            <span class="total-label">Total value:</span>
                            <span class="total-amount">48,675,000 IRR</span>
                        </div>
                    </div>
                </div>
                
                <div class="user-section">
                    <div class="username">Ali_Trader</div>
                    <div class="user-stats">
                        <span class="completion-rate">95% success</span>
                        <span class="response-time">Fast response</span>
                    </div>
                </div>
                
                <div class="card-actions">
                    <div class="action-info">
                        <span class="created-time">2h ago</span>
                        <span class="offer-status">Active</span>
                    </div>
                    <button class="interest-button">
                        ❤️ Show Interest
                    </button>
                </div>
            </div>
            
            <div class="enhanced-offer-card" style="animation-delay: 0.1s;">
                <div class="card-header">
                    <div class="offer-type-badge sell">
                        📉 Selling
                    </div>
                    <div class="user-reputation">⭐⭐</div>
                </div>
                
                <div class="amount-section">
                    <div class="primary-amount">
                        <span class="currency">CAD</span>
                        <span class="amount">$2,000</span>
                    </div>
                    <div class="exchange-info">
                        <span class="rate">32,800 IRR/CAD</span>
                        <div class="total-value">
                            <span class="total-label">Total value:</span>
                            <span class="total-amount">65,600,000 IRR</span>
                        </div>
                    </div>
                </div>
                
                <div class="user-section">
                    <div class="username">Sara_Exchange</div>
                    <div class="user-stats">
                        <span class="completion-rate">98% success</span>
                        <span class="response-time">Fast response</span>
                    </div>
                </div>
                
                <div class="card-actions">
                    <div class="action-info">
                        <span class="created-time">4h ago</span>
                        <span class="offer-status">Active</span>
                    </div>
                    <button class="interest-button">
                        ❤️ Show Interest
                    </button>
                </div>
            </div>
            
            <div class="enhanced-offer-card" style="animation-delay: 0.2s;">
                <div class="card-header">
                    <div class="offer-type-badge buy">
                        📈 Buying
                    </div>
                    <div class="user-reputation">⭐⭐⭐</div>
                </div>
                
                <div class="amount-section">
                    <div class="primary-amount">
                        <span class="currency">CAD</span>
                        <span class="amount">$500</span>
                    </div>
                    <div class="exchange-info">
                        <span class="rate">32,200 IRR/CAD</span>
                        <div class="total-value">
                            <span class="total-label">Total value:</span>
                            <span class="total-amount">16,100,000 IRR</span>
                        </div>
                    </div>
                </div>
                
                <div class="user-section">
                    <div class="username">Mohammad_Pro</div>
                    <div class="user-stats">
                        <span class="completion-rate">100% success</span>
                        <span class="response-time">Fast response</span>
                    </div>
                </div>
                
                <div class="card-actions">
                    <div class="action-info">
                        <span class="created-time">1h ago</span>
                        <span class="offer-status">Active</span>
                    </div>
                    <button class="interest-button">
                        ❤️ Show Interest
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Summary -->
        <div class="stats-summary">
            <div class="stats-item">
                <span class="stats-number">24</span>
                <span class="stats-label">Active Offers</span>
            </div>
            <div class="stats-item">
                <span class="stats-number">18</span>
                <span class="stats-label">Users</span>
            </div>
        </div>
    </div>

    <script>
        // Simple interactivity for testing
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        document.querySelectorAll('.chip-close').forEach(close => {
            close.addEventListener('click', (e) => {
                e.target.parentElement.remove();
            });
        });

        document.querySelectorAll('.interest-button').forEach(btn => {
            btn.addEventListener('click', () => {
                btn.textContent = '✅ Interest Shown';
                btn.style.background = '#18a058';
                setTimeout(() => {
                    btn.textContent = '❤️ Show Interest';
                    btn.style.background = '#2080f0';
                }, 2000);
            });
        });

        // Simulate loading state
        function showLoadingState() {
            const grid = document.querySelector('.offers-grid');
            grid.innerHTML = '';
            for (let i = 0; i < 6; i++) {
                const skeleton = document.createElement('div');
                skeleton.className = 'skeleton-card';
                skeleton.innerHTML = `
                    <div class="skeleton-header"></div>
                    <div class="skeleton-content">
                        <div class="skeleton-line"></div>
                        <div class="skeleton-line short"></div>
                        <div class="skeleton-line medium"></div>
                    </div>
                    <div class="skeleton-footer"></div>
                `;
                grid.appendChild(skeleton);
            }
        }

        // Test loading state
        document.addEventListener('keydown', (e) => {
            if (e.key === 'l' || e.key === 'L') {
                showLoadingState();
                setTimeout(() => {
                    location.reload();
                }, 3000);
            }
        });

        console.log('📱 Browse Offers Mobile UI Test');
        console.log('Press "L" to test loading state');
        console.log('✨ Interactive elements ready for testing');
    </script>
</body>
</html>
