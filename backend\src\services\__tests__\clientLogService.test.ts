import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { promises as fs } from 'fs'
import path from 'path'
import { tmpdir } from 'os'
import { ClientLogService } from '../clientLogService'
import type { ClientReportPayload } from '../../types/schemas/debugSchemas'

describe('ClientLogService', () => {
  let tempDir: string
  let clientLogService: ClientLogService
  let originalEnv: string | undefined

  beforeEach(async () => {
    // Create a temporary directory for testing
    tempDir = path.join(tmpdir(), `client-log-test-${Date.now()}`)
    await fs.mkdir(tempDir, { recursive: true })
    
    // Create service with test directory
    clientLogService = new ClientLogService(tempDir)
    
    // Save original NODE_ENV
    originalEnv = process.env.NODE_ENV
  })

  afterEach(async () => {
    // Clean up test directory
    try {
      await fs.rmdir(tempDir, { recursive: true })
    } catch (error) {
      // Ignore cleanup errors
    }
    
    // Restore original NODE_ENV
    process.env.NODE_ENV = originalEnv
  })

  describe('Service Initialization', () => {
    it('should use provided log directory', () => {
      const customDir = '/custom/log/path'
      const service = new ClientLogService(customDir)
      
      // Access the private property via type assertion for testing
      const logDirectory = (service as any).logDirectory
      expect(logDirectory).toBe(customDir)
    })

    it('should use environment variable when no directory provided', () => {
      const envDir = '/env/log/path'
      process.env.CLIENT_LOG_DIRECTORY = envDir
      
      const service = new ClientLogService()
      const logDirectory = (service as any).logDirectory
      expect(logDirectory).toBe(envDir)
      
      delete process.env.CLIENT_LOG_DIRECTORY
    })

    it('should use default development directory', () => {
      process.env.NODE_ENV = 'development'
      delete process.env.CLIENT_LOG_DIRECTORY
      
      const service = new ClientLogService()
      const logDirectory = (service as any).logDirectory
      expect(logDirectory).toBe(path.join(process.cwd(), 'logs'))
    })

    it('should use default production directory on Unix', () => {
      process.env.NODE_ENV = 'production'
      delete process.env.CLIENT_LOG_DIRECTORY
      
      // Mock platform
      const originalPlatform = process.platform
      Object.defineProperty(process, 'platform', { value: 'linux' })
      
      const service = new ClientLogService()
      const logDirectory = (service as any).logDirectory
      expect(logDirectory).toBe('/var/log/munygo')
      
      // Restore platform
      Object.defineProperty(process, 'platform', { value: originalPlatform })
    })
  })

  describe('Report Saving', () => {    it('should save a complete bug report with all fields', async () => {
      const reportData: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: 'Test Bug Report',
          description: 'This is a detailed bug description',
          stepsToReproduce: '1. Open page\n2. Click button\n3. Observe error',
          expectedBehavior: 'Button should work correctly',
          actualBehavior: 'Button throws an error',
          severity: 'high',
          additionalNotes: 'This happens only on Firefox',
          reportTags: [
            { tag: 'urgent', origin: 'USER_DEFINED' },
            { tag: 'browser-specific', origin: 'USER_DEFINED' }
          ]
        },
        logs: [
          {
            level: 'ERROR',
            message: 'Button click failed',
            timestamp: new Date().toISOString(),
            context: { component: 'TestButton' }
          }
        ],
        timestamp: new Date().toISOString()
      }

      const reportId = await clientLogService.saveReport(reportData)
      
      expect(reportId).toBeDefined()
      expect(typeof reportId).toBe('string')
      expect(reportId.length).toBeGreaterThan(0)

      // Verify file was created
      const logFilePath = path.join(tempDir, 'client-reports.log')
      const fileExists = await fs.access(logFilePath).then(() => true).catch(() => false)
      expect(fileExists).toBe(true)

      // Read and verify content
      const logContent = await fs.readFile(logFilePath, 'utf-8')
      const logEntry = JSON.parse(logContent.trim())

      expect(logEntry).toMatchObject({
        reportId,
        timestamp: expect.any(String),
        reportType: 'bug',
        reportSeverity: 'high',        userNotes: 'This happens only on Firefox',        hasTags: true,
        reportTags: [
          { tag: 'urgent', origin: 'USER_DEFINED' },
          { tag: 'browser-specific', origin: 'USER_DEFINED' }
        ]
      })

      // Verify complete reportDetails are included
      expect(logEntry.reportDetails).toMatchObject({
        type: 'bug',
        title: 'Test Bug Report',
        description: 'This is a detailed bug description',
        stepsToReproduce: '1. Open page\n2. Click button\n3. Observe error',
        expectedBehavior: 'Button should work correctly',
        actualBehavior: 'Button throws an error',
        severity: 'high',        additionalNotes: 'This happens only on Firefox',
        reportTags: [
          { tag: 'urgent', origin: 'USER_DEFINED' },
          { tag: 'browser-specific', origin: 'USER_DEFINED' }
        ]
      })      // Verify logs are included
      expect(logEntry.logs).toHaveLength(1)
      expect(logEntry.logs[0]).toMatchObject({
        level: 'ERROR',
        message: 'Button click failed'
      })
    })

    it('should save a feature request report', async () => {
      const reportData: ClientReportPayload = {
        reportDetails: {
          type: 'feature-request',
          title: 'New Feature Request',
          description: 'Please add dark mode support',
          severity: 'low',
          additionalNotes: 'This would improve user experience',
          reportTags: [
            { tag: 'enhancement', origin: 'USER_DEFINED' },
            { tag: 'ui', origin: 'USER_DEFINED' }
          ]
        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      const reportId = await clientLogService.saveReport(reportData)
      
      expect(reportId).toBeDefined()

      // Read and verify content
      const logFilePath = path.join(tempDir, 'client-reports.log')
      const logContent = await fs.readFile(logFilePath, 'utf-8')
      const logEntry = JSON.parse(logContent.trim())

      expect(logEntry).toMatchObject({
        reportId,
        reportType: 'feature-request',
        reportSeverity: 'low',        userNotes: 'This would improve user experience',
        hasTags: true,
        reportTags: [
          { tag: 'enhancement', origin: 'USER_DEFINED' },
          { tag: 'ui', origin: 'USER_DEFINED' }
        ]
      })      // Bug-specific fields should not be present for feature requests
      expect(logEntry.reportDetails.stepsToReproduce).toBeUndefined()
      expect(logEntry.reportDetails.expectedBehavior).toBeUndefined()
      expect(logEntry.reportDetails.actualBehavior).toBeUndefined()
    })

    it('should save feedback report', async () => {
      const reportData: ClientReportPayload = {
        reportDetails: {
          type: 'other',
          title: 'General Feedback',
          description: 'The app is great but could be faster',
          severity: 'medium',
          additionalNotes: 'Overall positive experience',
          reportTags: [
            { tag: 'performance', origin: 'USER_DEFINED' }
          ]
        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      const reportId = await clientLogService.saveReport(reportData)
      expect(reportId).toBeDefined()

      // Verify content
      const logFilePath = path.join(tempDir, 'client-reports.log')
      const logContent = await fs.readFile(logFilePath, 'utf-8')
      const logEntry = JSON.parse(logContent.trim())

      expect(logEntry.reportType).toBe('other')
    })

    it('should handle reports without tags', async () => {
      const reportData: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: 'Bug without tags',
          description: 'Simple bug report',
          severity: 'medium'
        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      const reportId = await clientLogService.saveReport(reportData)
      
      // Verify content
      const logFilePath = path.join(tempDir, 'client-reports.log')
      const logContent = await fs.readFile(logFilePath, 'utf-8')
      const logEntry = JSON.parse(logContent.trim())

      expect(logEntry.hasTags).toBe(false)
      expect(logEntry.reportTags).toEqual([])
    })

    it('should handle reports without additionalNotes', async () => {
      const reportData: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: 'Bug without notes',
          description: 'Simple bug report',
          severity: 'medium'        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      const reportId = await clientLogService.saveReport(reportData)
      
      // Verify content
      const logFilePath = path.join(tempDir, 'client-reports.log')
      const logContent = await fs.readFile(logFilePath, 'utf-8')
      const logEntry = JSON.parse(logContent.trim())

      expect(logEntry.userNotes).toBe('')
    })

    it('should generate unique report IDs', async () => {
      const reportData1: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: 'First Report',
          description: 'First description',
          severity: 'low'
        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      const reportData2: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: 'Second Report',
          description: 'Second description',
          severity: 'high'
        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      const reportId1 = await clientLogService.saveReport(reportData1)
      const reportId2 = await clientLogService.saveReport(reportData2)

      expect(reportId1).not.toBe(reportId2)
    })

    it('should append multiple reports to the same file', async () => {
      const reportData1: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: 'First Report',
          description: 'First description',
          severity: 'low'
        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      const reportData2: ClientReportPayload = {
        reportDetails: {
          type: 'feature-request',
          title: 'Second Report',
          description: 'Second description',
          severity: 'medium'
        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      await clientLogService.saveReport(reportData1)
      await clientLogService.saveReport(reportData2)

      // Read entire file
      const logFilePath = path.join(tempDir, 'client-reports.log')
      const logContent = await fs.readFile(logFilePath, 'utf-8')
      const lines = logContent.trim().split('\n')

      expect(lines).toHaveLength(2)

      const entry1 = JSON.parse(lines[0])
      const entry2 = JSON.parse(lines[1])

      expect(entry1.reportDetails.title).toBe('First Report')
      expect(entry2.reportDetails.title).toBe('Second Report')
    })

    it('should handle reports with complex log data', async () => {
      const reportData: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: 'Bug with complex logs',
          description: 'Bug with detailed logging',
          severity: 'high'
        },
        logs: [          {
            level: 'ERROR',
            message: 'First error occurred',
            timestamp: new Date().toISOString(),
            context: { 
              component: 'Header',
              user: 'test-user',
              action: 'click'
            }
          },
          {
            level: 'WARN',
            message: 'Warning message',
            timestamp: new Date().toISOString(),
            context: {
              performance: true,
              loadTime: 3000
            }
          },
          {
            level: 'INFO',
            message: 'Info message',
            timestamp: new Date().toISOString()
          }        ],
        timestamp: new Date().toISOString()
      }

      const reportId = await clientLogService.saveReport(reportData)
      
      // Verify content
      const logFilePath = path.join(tempDir, 'client-reports.log')
      const logContent = await fs.readFile(logFilePath, 'utf-8')
      const logEntry = JSON.parse(logContent.trim())

      expect(logEntry.logs).toHaveLength(3)
      expect(logEntry.logs[0]).toMatchObject({
        level: 'ERROR',
        message: 'First error occurred',
        context: {
          component: 'Header',
          user: 'test-user',
          action: 'click'
        }
      })
    })
  })

  describe('Error Handling', () => {
    it('should create log directory if it does not exist', async () => {
      // Use a non-existent directory
      const nonExistentDir = path.join(tempDir, 'new-subdir')
      const service = new ClientLogService(nonExistentDir)
      
      const reportData: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: 'Test Report',
          description: 'Test description',
          severity: 'low'
        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      const reportId = await service.saveReport(reportData)
      expect(reportId).toBeDefined()

      // Verify directory was created
      const dirExists = await fs.access(nonExistentDir).then(() => true).catch(() => false)
      expect(dirExists).toBe(true)

      // Clean up
      await fs.rmdir(nonExistentDir, { recursive: true })
    })

    it('should handle file write errors gracefully', async () => {
      // Create a service with a directory that contains a file with the same name as our log file
      // This will cause a write error when trying to create the directory
      const conflictDir = path.join(tempDir, 'conflict-test')
      await fs.mkdir(conflictDir, { recursive: true })

      // Create a file with the same name as the log file name
      const conflictFile = path.join(conflictDir, 'client-reports.log')
      await fs.writeFile(conflictFile, 'existing file content')

      // Now make the file read-only to cause write errors
      await fs.chmod(conflictFile, 0o444) // Read-only

      const service = new ClientLogService(conflictDir)

      const reportData: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: 'Test Report',
          description: 'Test description',
          severity: 'low'
        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      // This should throw an error when trying to append to the read-only file
      await expect(service.saveReport(reportData)).rejects.toThrow()

      // Clean up - restore write permissions so cleanup can work
      await fs.chmod(conflictFile, 0o644)
    })
  })

  describe('Report Statistics', () => {
    it('should return correct stats for existing reports', async () => {
      // Save a few reports
      const reportData: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: 'Test Report',
          description: 'Test description',
          severity: 'low'        },
        logs: [],
        timestamp: new Date().toISOString()
      }

      await clientLogService.saveReport(reportData)
      await clientLogService.saveReport(reportData)
      await clientLogService.saveReport(reportData)

      const stats = await clientLogService.getReportStats()
      
      expect(stats.totalReports).toBe(3)
      expect(stats.logFileSize).toBeGreaterThan(0)
    })

    it('should return zero stats for non-existent file', async () => {
      // Don't save any reports
      const stats = await clientLogService.getReportStats()
      
      expect(stats.totalReports).toBe(0)
      expect(stats.logFileSize).toBe(0)
    })
  })

  describe('Data Integrity', () => {
    it('should preserve all field data including special characters', async () => {
      const specialText = 'Special chars: émojis 🐛🔧 unicode ñáéíóú quotes "test" \'test\' backslashes \\ newlines\nand\ttabs'
        const reportData: ClientReportPayload = {
        reportDetails: {
          type: 'bug',
          title: specialText,
          description: specialText,
          stepsToReproduce: specialText,
          expectedBehavior: specialText,
          actualBehavior: specialText,
          severity: 'medium',
          additionalNotes: specialText,
          reportTags: [
            { tag: 'special-chars', origin: 'USER_DEFINED' },
            { tag: 'unicode-test', origin: 'USER_DEFINED' }
          ]
        },
        logs: [
          {
            level: 'ERROR',
            message: specialText,
            timestamp: new Date().toISOString(),
            context: { data: specialText }
          }
        ],
        timestamp: new Date().toISOString()
      }

      const reportId = await clientLogService.saveReport(reportData)
      
      // Read and verify all data is preserved
      const logFilePath = path.join(tempDir, 'client-reports.log')
      const logContent = await fs.readFile(logFilePath, 'utf-8')
      const logEntry = JSON.parse(logContent.trim())

      expect(logEntry.reportDetails.title).toBe(specialText)
      expect(logEntry.reportDetails.description).toBe(specialText)
      expect(logEntry.reportDetails.stepsToReproduce).toBe(specialText)
      expect(logEntry.reportDetails.expectedBehavior).toBe(specialText)
      expect(logEntry.reportDetails.actualBehavior).toBe(specialText)
      expect(logEntry.reportDetails.additionalNotes).toBe(specialText)
      expect(logEntry.logs[0].message).toBe(specialText)
      expect(logEntry.logs[0].context.data).toBe(specialText)
    })

    it('should handle very large report data', async () => {
      const largeText = 'A'.repeat(10000) // 10KB of text
      
      const reportData: ClientReportPayload = {        reportDetails: {
          type: 'bug',
          title: 'Large Report',
          description: largeText,
          stepsToReproduce: largeText,
          severity: 'low'
        },
        logs: Array.from({ length: 100 }, (_, i) => ({
          level: 'INFO' as const,
          message: `Log entry ${i}: ${largeText.substring(0, 100)}`,
          timestamp: new Date().toISOString(),
          context: { index: i, data: largeText.substring(0, 50) }
        })),
        timestamp: new Date().toISOString()
      }

      const reportId = await clientLogService.saveReport(reportData)
      expect(reportId).toBeDefined()

      // Verify data was saved correctly
      const logFilePath = path.join(tempDir, 'client-reports.log')
      const logContent = await fs.readFile(logFilePath, 'utf-8')
      const logEntry = JSON.parse(logContent.trim())

      expect(logEntry.reportDetails.description).toBe(largeText)
      expect(logEntry.logs).toHaveLength(100)
    })
  })
})
