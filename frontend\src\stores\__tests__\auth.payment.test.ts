import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '../auth'
import * as apiClient from '@/services/apiClient'

// Mock the API client
vi.mock('@/services/apiClient', () => ({
  apiClient: {
    get: vi.fn()
  }
}))

// Mock socket service
vi.mock('@/services/socketService', () => ({
  initSocket: vi.fn(),
  disconnectSocket: vi.fn()
}))

describe('Auth Store Payment Info Tests', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    localStorage.clear()
  })

  describe('Payment Info Persistence', () => {
    it('should load user with payment info from API', async () => {
      const mockUserWithPayment = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        isPhoneVerified: true,
        reputation: 5.0,
        defaultPaymentReceivingInfo: {
          bankName: 'Test Bank',
          accountNumber: '*********',
          accountHolderName: 'John Doe'
        }
      }

      vi.mocked(apiClient.apiClient.get).mockResolvedValue({
        data: { success: true, user: mockUserWithPayment }
      })

      const authStore = useAuthStore()
      await authStore.fetchUserProfile()

      expect(authStore.user).toEqual(mockUserWithPayment)
      expect(authStore.user?.defaultPaymentReceivingInfo).toBeDefined()
      expect(authStore.user?.defaultPaymentReceivingInfo?.bankName).toBe('Test Bank')
    })

    it('should handle user without payment info', async () => {
      const mockUserWithoutPayment = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        isPhoneVerified: true,
        reputation: 5.0,
        defaultPaymentReceivingInfo: null
      }

      vi.mocked(apiClient.apiClient.get).mockResolvedValue({
        data: { success: true, user: mockUserWithoutPayment }
      })

      const authStore = useAuthStore()
      await authStore.fetchUserProfile()

      expect(authStore.user).toEqual(mockUserWithoutPayment)
      expect(authStore.user?.defaultPaymentReceivingInfo).toBeNull()
    })

    it('should update user payment info after successful payment save', async () => {
      const initialUser = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        isPhoneVerified: true,
        reputation: 5.0,
        defaultPaymentReceivingInfo: null
      }

      const updatedUser = {
        ...initialUser,
        defaultPaymentReceivingInfo: {
          bankName: 'New Bank',
          accountNumber: '*********',
          accountHolderName: 'Jane Doe'
        }
      }

      // Mock initial load
      vi.mocked(apiClient.apiClient.get).mockResolvedValueOnce({
        data: { success: true, user: initialUser }
      })

      const authStore = useAuthStore()
      await authStore.fetchUserProfile()

      expect(authStore.user?.defaultPaymentReceivingInfo).toBeNull()

      // Mock updated load after payment save
      vi.mocked(apiClient.apiClient.get).mockResolvedValueOnce({
        data: { success: true, user: updatedUser }
      })

      // Simulate fetching updated profile
      await authStore.fetchUserProfile()

      expect(authStore.user?.defaultPaymentReceivingInfo).toBeDefined()
      expect(authStore.user?.defaultPaymentReceivingInfo?.bankName).toBe('New Bank')
    })

    it('should persist user data to localStorage', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        isPhoneVerified: true,
        reputation: 5.0,
        defaultPaymentReceivingInfo: {
          bankName: 'Test Bank',
          accountNumber: '*********',
          accountHolderName: 'John Doe'
        }
      }

      vi.mocked(apiClient.apiClient.get).mockResolvedValue({
        data: { success: true, user: mockUser }
      })

      const authStore = useAuthStore()
      await authStore.fetchUserProfile()

      // Check that user data is saved to localStorage
      const savedUser = JSON.parse(localStorage.getItem('user') || '{}')
      expect(savedUser).toEqual(mockUser)
      expect(savedUser.defaultPaymentReceivingInfo).toBeDefined()
    })

    it('should load persisted user data from localStorage on initialization', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        isPhoneVerified: true,
        reputation: 5.0,
        defaultPaymentReceivingInfo: {
          bankName: 'Saved Bank',
          accountNumber: '*********',
          accountHolderName: 'Saved User'
        }
      }

      // Pre-populate localStorage
      localStorage.setItem('user', JSON.stringify(mockUser))
      localStorage.setItem('token', 'test-token')

      // Create new store instance (simulates app initialization)
      const authStore = useAuthStore()
      authStore.initializeAuth()

      expect(authStore.user).toEqual(mockUser)
      expect(authStore.user?.defaultPaymentReceivingInfo?.bankName).toBe('Saved Bank')
      expect(authStore.isAuthenticated).toBe(true)
    })
  })

  describe('Authentication Flow', () => {
    it('should clear payment info on logout', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        isPhoneVerified: true,
        reputation: 5.0,
        defaultPaymentReceivingInfo: {
          bankName: 'Test Bank',
          accountNumber: '*********',
          accountHolderName: 'John Doe'
        }
      }

      localStorage.setItem('user', JSON.stringify(mockUser))
      localStorage.setItem('token', 'test-token')

      const authStore = useAuthStore()
      authStore.initializeAuth()

      expect(authStore.user?.defaultPaymentReceivingInfo).toBeDefined()

      authStore.logout()

      expect(authStore.user).toBeNull()
      expect(localStorage.getItem('user')).toBeNull()
      expect(localStorage.getItem('token')).toBeNull()
    })
  })
})
