# Debug Report System - Production Configuration

## Overview
The debug report system allows clients to submit bug reports, feature requests, and other feedback directly from the application. This system is designed to work reliably in both development and containerized production environments.

## Architecture

### Client Side
- **Component**: `DebugReportButtonEnhanced.vue`
- **Service**: `useClientLogger` composable
- **Endpoint**: `POST /api/debug/report-issue`

### Backend Side
- **Routes**: `debugRoutes.ts`
- **Service**: `ClientLogService`
- **Storage**: JSON-line format log files with rotation
- **Log Rotation**: `LogRotationService`

## Production Configuration

### Docker Container Setup
```yaml
# In docker-compose.yml
backend:
  environment:
    CLIENT_LOG_DIRECTORY: /app/logs  # Writable directory for nodejs user
  volumes:
    - client_logs:/app/logs  # Persistent volume mount
```

### Log Storage Location
- **Development**: `./logs/client-reports.log`
- **Production Container**: `/app/logs/client-reports.log`
- **Volume Mount**: `client_logs` Docker volume for persistence

### Log Rotation
- **Max File Size**: 10MB per log file
- **Max Backup Files**: 5 rotated files kept
- **Naming Pattern**: `client-reports.log`, `client-reports.1.log`, etc.
- **Auto-rotation**: Triggered before each new report if size exceeded

## Log Management

### PowerShell Management Script
Use `debug-logs.ps1` for easy log access:

```powershell
# View last 10 reports
.\debug-logs.ps1 -Action View

# Search for specific issues
.\debug-logs.ps1 -Action Search -Pattern "ERROR"

# Get storage statistics
.\debug-logs.ps1 -Action Stats

# Trigger manual cleanup
.\debug-logs.ps1 -Action Cleanup

# Export all reports to JSON file
.\debug-logs.ps1 -Action Export -ExportPath "reports.json"
```

### API Endpoints

#### Get Statistics
```
GET /api/debug/stats
```
Returns:
```json
{
  "success": true,
  "data": {
    "reports": {
      "totalReports": 42,
      "logFileSize": 1048576
    },
    "storage": {
      "totalFiles": 3,
      "totalSizeMB": 12.5,
      "oldestFile": "client-reports.3.log",
      "newestFile": "client-reports.log"
    },
    "logDirectory": "/app/logs"
  }
}
```

#### Trigger Cleanup
```
POST /api/debug/cleanup
```
Manually triggers log rotation and cleanup.

## Security Considerations

### File Permissions
- Container runs as `nodejs` user (uid 1001)
- Log directory `/app/logs` owned by `nodejs:nodejs`
- No root access required for log operations

### Data Privacy
- Reports may contain sensitive user context
- Logs are stored locally in container, not transmitted to external services
- Consider implementing data retention policies

### Rate Limiting
- Client-side: Only available in development mode by default
- Server-side: Consider implementing rate limiting for report endpoints

## Troubleshooting

### Common Issues

1. **"No log file found"**
   - Normal for new installations
   - First report will create the log file

2. **Permission denied errors**
   - Check log directory ownership in container
   - Ensure `/app/logs` is writable by nodejs user

3. **Container restart loses logs**
   - Verify volume mount is configured correctly
   - Check `client_logs` volume exists

4. **Log files growing too large**
   - Check rotation service is working
   - Manually trigger cleanup via API

### Manual Container Debugging
```bash
# Access container
docker exec -it munygo-backend sh

# Check log directory
ls -la /app/logs/

# Check log file permissions
ls -la /app/logs/client-reports.log

# View recent reports
tail -n 5 /app/logs/client-reports.log
```

## Monitoring

### Health Checks
- Monitor `/api/debug/stats` endpoint
- Alert on excessive log file sizes
- Track report frequency for abuse detection

### Log Analysis
- Parse JSON-line format for automated analysis
- Monitor error patterns and frequencies
- Track feature request themes

## Future Enhancements

1. **Database Integration**: Store reports in PostgreSQL instead of files
2. **Admin Dashboard**: Web interface for browsing and managing reports
3. **Automated Notifications**: Alert developers on critical reports
4. **Advanced Analytics**: Trend analysis and report categorization
5. **Data Export**: Export to external ticketing systems
