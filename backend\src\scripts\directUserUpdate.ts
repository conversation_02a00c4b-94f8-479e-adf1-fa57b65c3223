import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting direct database update...');
    
    // Our list of users and the values we want to update them with
    const usersToUpdate = [
      {
        email: '<EMAIL>',
        username: 'hosami',
        reputationScore: 25,
        reputationLevel: 3
      },
      {
        email: '<EMAIL>',
        username: 'hosami1',
        reputationScore: 25,
        reputationLevel: 3
      },
      {
        email: '<EMAIL>',
        username: 'hosami2',
        reputationScore: 25,
        reputationLevel: 3
      },
      {
        email: '<EMAIL>',
        username: 'hosami3',
        reputationScore: 5,
        reputationLevel: 1
      },
      {
        email: '<EMAIL>',
        username: 'hosami4',
        reputationScore: 40,
        reputationLevel: 4
      },
      {
        email: '<EMAIL>',
        username: 'hosami5',
        reputationScore: 60,
        reputationLevel: 5
      }
    ];
    
    // Update each user by email
    for (const userUpdate of usersToUpdate) {
      console.log(`Updating ${userUpdate.email} with username=${userUpdate.username}, score=${userUpdate.reputationScore}, level=${userUpdate.reputationLevel}`);
      
      try {
        const result = await prisma.user.updateMany({
          where: {
            email: userUpdate.email
          },
          data: {
            username: userUpdate.username,
            reputationScore: userUpdate.reputationScore,
            reputationLevel: userUpdate.reputationLevel
          }
        });
        
        console.log(`Updated ${result.count} records for ${userUpdate.email}`);
      } catch (err) {
        console.error(`Failed to update ${userUpdate.email}:`, err);
      }
    }
    
    console.log('All users processed.');
  } catch (error) {
    console.error('Script error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('Update completed successfully.'))
  .catch(e => {
    console.error('Script failed:', e);
    process.exit(1);
  });
