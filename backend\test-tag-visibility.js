const debugReportService = require('./src/services/debugReportService');

async function testTagVisibility() {
    console.log('Testing debug report tags visibility...');
    
    try {
        // Get recent debug reports
        const reports = await debugReportService.getReports(1, 5);
        
        if (reports.data.length === 0) {
            console.log('❌ No debug reports found');
            return;
        }
        
        console.log(`✅ Found ${reports.data.length} debug reports`);
        
        // Check each report for tags
        for (let i = 0; i < Math.min(3, reports.data.length); i++) {
            const report = reports.data[i];
            console.log(`\n--- Report ${i + 1} ---`);
            console.log(`Report ID: ${report.reportId}`);
            console.log(`Report Type: ${report.reportType}`);
            console.log(`Title: ${report.reportTitle || 'None'}`);
            
            if (report.tags && report.tags.length > 0) {
                console.log(`✅ Tags found: ${report.tags.length}`);
                report.tags.forEach(tag => {
                    if (typeof tag === 'object' && tag.tag && tag.origin) {
                        console.log(`  - Tag: '${tag.tag}' (Origin: ${tag.origin})`);
                    } else {
                        console.log(`  - Tag structure: ${JSON.stringify(tag)}`);
                    }
                });
            } else {
                console.log('❌ No tags found');
            }
        }
        
        // Get detailed report
        const firstReportId = reports.data[0].reportId;
        console.log(`\nFetching detailed report: ${firstReportId}`);
        
        const detailedReport = await debugReportService.getReportById(firstReportId);
        
        if (detailedReport.tags && detailedReport.tags.length > 0) {
            console.log('\n✅ Tags in detailed report:');
            console.log(JSON.stringify(detailedReport.tags, null, 2));
        } else {
            console.log('\n❌ No tags in detailed report');
        }
        
        // Log sample of report structure
        console.log('\n--- Sample Report Structure ---');
        const sampleReport = {
            reportId: detailedReport.reportId,
            reportType: detailedReport.reportType,
            reportTitle: detailedReport.reportTitle,
            tags: detailedReport.tags,
            hasLogs: detailedReport.logs ? detailedReport.logs.length : 0
        };
        console.log(JSON.stringify(sampleReport, null, 2));
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

testTagVisibility();
