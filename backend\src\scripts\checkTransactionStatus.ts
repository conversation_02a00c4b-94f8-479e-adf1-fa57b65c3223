import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkTransactionStatus() {
  console.log('🔍 Checking Transaction Status...');
  
  try {
    const transactionId = 'cmb1ckwie0006vlr4c42tu79m'; // From the error logs
    
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        currencyAProvider: { select: { id: true, username: true } },
        currencyBProvider: { select: { id: true, username: true } }
      }
    });
    
    if (transaction) {
      console.log('📋 Transaction Details:');
      console.log(`   ID: ${transaction.id}`);
      console.log(`   Status: ${transaction.status}`);
      console.log(`   Currency A Provider: ${transaction.currencyAProvider?.username} (${transaction.currencyAProvider?.id})`);
      console.log(`   Currency B Provider: ${transaction.currencyBProvider?.username} (${transaction.currencyBProvider?.id})`);
      console.log(`   Agreed First Payer: ${transaction.agreedFirstPayerId}`);
      
      const payer1Id = transaction.agreedFirstPayerId;
      const payer2Id = payer1Id === transaction.currencyAProviderId 
        ? transaction.currencyBProviderId 
        : transaction.currencyAProviderId;
      
      console.log(`   Payer 1 (first payer): ${payer1Id}`);
      console.log(`   Payer 2 (second payer): ${payer2Id}`);
      
      // Check what should happen next
      const userFromErrorLogs = 'cmav458yl0000vl50932ofx2h';
      console.log(`\n🎯 User from error logs: ${userFromErrorLogs}`);
      
      if (userFromErrorLogs === payer1Id) {
        console.log('   → This user is Payer 1 (first payer)');
      } else if (userFromErrorLogs === payer2Id) {
        console.log('   → This user is Payer 2 (second payer)');
      } else {
        console.log('   → This user is neither payer (unexpected!)');
      }
      
      console.log(`\n📊 Current Status Analysis:`);
      if (transaction.status === 'AWAITING_SECOND_PAYER_CONFIRMATION') {
        console.log('   → Payer 2 should confirm receipt of first payment');
        if (userFromErrorLogs === payer2Id) {
          console.log('   ✅ User should be able to confirm!');
        } else {
          console.log('   ❌ Wrong user trying to confirm');
        }
      } else if (transaction.status === 'AWAITING_FIRST_PAYER_CONFIRMATION') {
        console.log('   → Payer 1 should confirm receipt of second payment');
        if (userFromErrorLogs === payer1Id) {
          console.log('   ✅ User should be able to confirm!');
        } else {
          console.log('   ❌ Wrong user trying to confirm');
        }
      } else {
        console.log(`   → Status: ${transaction.status} (not a confirmation state)`);
      }
      
    } else {
      console.log('❌ Transaction not found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkTransactionStatus().catch(console.error);
