// filepath: c:\Code\MUNygo\frontend\src\types\chat.ts
// Represents a user in the context of a chat
export interface ChatParticipant {
  id: string;
  username: string;
  reputationLevel?: number; // Made optional
  profile?: { // Added profile
    avatarUrl?: string | null;
  } | null;
}

// Represents a single chat message
export interface ChatMessage {
  messageId: string;
  chatSessionId: string;
  sender?: ChatParticipant; // Optional for system messages
  content: string;
  createdAt: string; // ISO string
  isSystemMessage?: boolean; // Added for system messages
}

// For the API response when fetching chat participants
export interface ChatParticipantsInfo {
  chatSessionId: string;
  currentUser: ChatParticipant;
  otherUser: ChatParticipant;
  // offerId?: string; // Optional: if you decide to include it
}

// For the API response when fetching chat history (already an array of ChatMessage)
export type ChatHistoryResponse = ChatMessage[];
