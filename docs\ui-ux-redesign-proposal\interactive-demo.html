<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MUNygo - Innovative Transaction Chat UI Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Light theme colors */
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --info-color: #3b82f6;
            
            --bg-color: #ffffff;
            --card-color: #f8fafc;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
        }

        [data-theme="dark"] {
            --bg-color: #0f172a;
            --card-color: #1e293b;
            --border-color: #334155;
            --text-primary: #f1f5f9;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* Mobile-first layout */
        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            max-width: 400px;
            margin: 0 auto;
            border: 1px solid var(--border-color);
            background: var(--bg-color);
            position: relative;
        }

        /* Header */        .app-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: var(--card-color);
            border-bottom: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            position: sticky;
            top: 0;
            z-index: 100;
            gap: 0.75rem;
        }

        .back-button {
            background: none;
            border: none;
            padding: 0.5rem;
            cursor: pointer;
            color: var(--text-primary);
            font-size: 1.25rem;
            border-radius: var(--radius-md);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            min-height: 44px;
        }

        .back-button:hover {
            background: var(--border-color);
        }

        .back-arrow {
            font-weight: bold;
        }

        .header-menu {
            background: none;
            border: none;
            padding: 0.5rem;
            cursor: pointer;
            color: var(--text-primary);
            font-size: 1.25rem;
            border-radius: var(--radius-md);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            min-height: 44px;
        }

        .header-menu:hover {
            background: var(--border-color);
        }

        .menu-dots {
            font-weight: bold;
            transform: rotate(90deg);
        }

        .header-user {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .user-info h3 {
            font-size: 0.95rem;
            font-weight: 600;
            margin: 0;
        }

        .user-status {
            font-size: 0.8rem;
            color: var(--success-color);
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--success-color);
        }

        .theme-toggle {
            background: none;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 0.5rem;
            cursor: pointer;
            color: var(--text-primary);
            transition: all 0.2s ease;
        }

        .theme-toggle:hover {
            background: var(--card-color);
        }

        /* Message Stream */
        .message-stream {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        /* Chat Messages */
        .chat-message {
            display: flex;
            gap: 0.75rem;
            max-width: 85%;
        }

        .chat-message.own {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-content {
            background: var(--card-color);
            padding: 0.75rem 1rem;
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
            position: relative;
        }

        .chat-message.own .message-content {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .message-text {
            margin: 0;
            font-size: 0.95rem;
        }

        .message-time {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        .chat-message.own .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        /* System Messages */
        .system-message {
            align-self: center;
            max-width: 100%;
            margin: 0.5rem 0;
        }

        .system-message .message-content {
            background: linear-gradient(135deg, var(--info-color), var(--primary-color));
            color: white;
            text-align: center;
            border-radius: var(--radius-xl);
            padding: 1rem 1.25rem;
            box-shadow: var(--shadow-md);
        }

        /* Transaction System Messages */
        .transaction-message {
            background: var(--card-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: 1.25rem;
            margin: 0.75rem 0;
            border-left: 4px solid var(--info-color);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
        }

        .transaction-message.payment-declared {
            border-left-color: var(--warning-color);
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), var(--card-color));
        }

        .transaction-message.payment-confirmed {
            border-left-color: var(--success-color);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), var(--card-color));
        }

        .transaction-message.payment-gate {
            border-left-color: var(--info-color);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), var(--card-color));
        }

        .transaction-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .transaction-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 600;
            font-size: 1rem;
        }

        .transaction-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .status-tag {
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-tag.pending {
            background: rgba(245, 158, 11, 0.2);
            color: var(--warning-color);
        }

        .status-tag.confirmed {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
        }        .status-tag.ready {
            background: rgba(59, 130, 246, 0.2);
            color: var(--info-color);
        }

        .status-tag.completed {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
        }

        .status-tag.partial {
            background: rgba(245, 158, 11, 0.2);
            color: var(--warning-color);
        }

        .status-tag.negotiating {
            background: rgba(139, 92, 246, 0.2);
            color: #8b5cf6;
        }

        /* Payment Details */
        .payment-details {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin: 1rem 0;
        }

        .amount-display {
            text-align: center;
            padding: 1rem;
            background: rgba(59, 130, 246, 0.1);
            border-radius: var(--radius-lg);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .amount-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
        }

        .amount-label {
            font-size: 0.85rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .payer-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: var(--bg-color);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }

        .payer-text {
            flex: 1;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        /* Payment Readiness Gate */
        .payment-gate {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .gate-explanation {
            padding: 1rem;
            background: rgba(59, 130, 246, 0.1);
            border-radius: var(--radius-md);
            border-left: 3px solid var(--info-color);
        }

        .gate-explanation p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .users-status {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
        }

        .user-status-card {
            padding: 1rem;
            background: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            text-align: center;
            transition: all 0.3s ease;
        }

        .user-status-card.ready {
            border-color: var(--success-color);
            background: rgba(16, 185, 129, 0.1);
        }

        .user-status-card.pending {
            border-color: var(--warning-color);
            background: rgba(245, 158, 11, 0.1);
        }

        .user-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .user-status-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .user-status-indicator.ready {
            color: var(--success-color);
        }

        .user-status-indicator.pending {
            color: var(--warning-color);
        }

        .progress-bar {
            height: 6px;
            background: var(--border-color);
            border-radius: 3px;
            overflow: hidden;
            margin: 1rem 0 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--info-color), var(--success-color));
            transition: width 0.5s ease;
        }

        .progress-text {
            text-align: center;
            font-size: 0.85rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Inline Actions */
        .inline-actions {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.25rem;
            border: none;
            border-radius: var(--radius-md);
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            min-width: 120px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-secondary {
            background: var(--card-color);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Smart Action Bar */
        .smart-action-bar {
            background: var(--card-color);
            border-top: 1px solid var(--border-color);
            padding: 1rem;
            backdrop-filter: blur(10px);
            position: sticky;
            bottom: 0;
            z-index: 50;
        }

        .action-bar-payment-due {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), var(--card-color));
            border-top-color: var(--error-color);
            animation: urgentPulse 2s infinite;
        }

        .action-bar-confirmation {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), var(--card-color));
            border-top-color: var(--success-color);
        }

        .action-bar-waiting {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), var(--card-color));
            border-top-color: var(--info-color);
        }

        .action-context {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .context-text {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .context-main {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.95rem;
        }

        .context-sub {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }        .timer-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-family: 'Monaco', 'Consolas', monospace;
            padding: 0.5rem 0.75rem;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: var(--radius-md);
            min-width: 90px;
        }

        .timer-display .timer-label {
            font-size: 0.65rem;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 0.125rem;
            font-family: inherit;
        }

        .timer-display .timer-value {
            font-size: 1rem;
            font-weight: 700;
            color: var(--error-color);
        }

        .timer-display.timer-warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: rgba(245, 158, 11, 0.3);
        }

        .timer-display.timer-warning .timer-value {
            color: var(--warning-color);
        }

        .timer-display.timer-critical {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
            animation: urgentPulse 2s infinite;
        }

        .timer-display.timer-critical .timer-value {
            color: var(--error-color);
        }

        .timer-display.timer-info {
            background: rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .timer-display.timer-info .timer-value {
            color: var(--info-color);
        }

        .timer-display.normal {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            color: var(--success-color);
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
        }

        .btn-large {
            flex: 1;
            min-height: 48px;
            font-size: 1rem;
            font-weight: 600;
        }

        /* Message Input */
        .message-input-container {
            padding: 1rem;
            background: var(--bg-color);
            border-top: 1px solid var(--border-color);
        }

        .message-input-row {
            display: flex;
            gap: 0.75rem;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            min-height: 44px;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--card-color);
            color: var(--text-primary);
            font-family: inherit;
            font-size: 0.95rem;
            resize: none;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .message-input:focus {
            border-color: var(--primary-color);
        }

        .send-btn {
            min-width: 44px;
            min-height: 44px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .send-btn:hover {
            background: var(--primary-hover);
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: var(--border-color);
            cursor: not-allowed;
            transform: none;
        }

        /* Animations */
        @keyframes urgentPulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }
            50% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-item {
            animation: slideIn 0.3s ease;
        }

        /* Loading states */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Transaction Summary Bar */
        .transaction-summary-bar {
            background: var(--card-color);
            border-bottom: 1px solid var(--border-color);
            padding: 0.75rem 1rem;
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: var(--shadow-sm);
        }

        .transaction-summary-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 0.5rem;
            max-width: 100%;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex: 0 0 auto;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.85rem;
            box-shadow: var(--shadow-sm);
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: 0.125rem;
        }

        .user-name {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-primary);
            line-height: 1;
        }

        .user-level {
            font-size: 0.7rem;
            color: var(--text-secondary);
            line-height: 1;
        }

        .transaction-flow {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            flex: 1;
            justify-content: center;
            min-width: 0;
        }

        .payment-info {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .amount {
            color: var(--primary-color);
            white-space: nowrap;
        }

        .arrow {
            color: var(--text-muted);
            font-size: 0.8rem;
        }

        .exchange-rate {
            background: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            padding: 0.25rem 0.5rem;
            font-size: 0.7rem;
            font-weight: 600;
            color: var(--text-primary);
            white-space: nowrap;
        }

        /* Step Progress Bar */
        .step-progress-bar {
            background: var(--bg-color);
            border-bottom: 1px solid var(--border-color);
            padding: 0.75rem 1rem;
            position: sticky;
            top: 84px; /* Height of transaction summary bar */
            z-index: 9;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .step-progress-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: max-content;
            padding: 0 0.5rem;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
            flex: 0 0 auto;
            transition: all 0.2s ease;
        }

        .step-number {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            transition: all 0.2s ease;
            border: 2px solid transparent;
        }

        .step-label {
            font-size: 0.65rem;
            color: var(--text-muted);
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            transition: color 0.2s ease;
        }

        .step-connector {
            width: 24px;
            height: 2px;
            background: var(--border-color);
            margin: 0 0.25rem;
            border-radius: 1px;
            transition: background-color 0.2s ease;
        }

        .step.active .step-number {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-sm);
        }

        .step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        .step.completed .step-number {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .step.completed .step-label {
            color: var(--success-color);
        }

        .step.completed + .step-connector {            background: var(--success-color);
        }

        /* Timer Section Styling */
        .timer-section {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background: rgba(245, 158, 11, 0.1);
            border-radius: var(--radius-md);
            border-left: 3px solid var(--warning-color);
        }

        .timer-icon {
            font-size: 1.1rem;
        }

        .timer-text {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--warning-color);
        }

        /* Transaction Completion Styling */
        .completion-summary {
            text-align: center;
            padding: 1rem;
            background: rgba(16, 185, 129, 0.05);
            border-radius: var(--radius-lg);
            border: 2px solid rgba(16, 185, 129, 0.2);
        }

        .completion-amounts {
            margin-bottom: 1rem;
        }

        .completion-amount {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--success-color);
        }

        .amount-arrow {
            font-size: 1.2rem;
            color: var(--text-muted);
        }

        .completion-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .completion-time {
            font-weight: 500;
        }        .completion-duration {
            opacity: 0.8;
        }

        /* Payer Negotiation Styling */
        .current-proposal {
            background: rgba(139, 92, 246, 0.1);
            border-radius: var(--radius-md);
            padding: 0.75rem;
            margin-top: 0.5rem;
            border-left: 3px solid #8b5cf6;
        }

        .proposal-header {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .proposal-payer {
            font-size: 0.9rem;
            font-weight: 600;
            color: #8b5cf6;
        }        .payer-text.reason {
            font-style: italic;
            color: var(--text-secondary);
            font-size: 0.85rem;
        }

        /* Exit Menu Styling */
        .exit-menu {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.2s ease;
        }

        .exit-menu-content {
            background: var(--card-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow-xl);
            min-width: 280px;
            max-width: 90vw;
            animation: slideUp 0.3s ease;
        }

        .exit-menu-header {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.75rem;
        }

        .exit-option {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            width: 100%;
            padding: 0.75rem;
            background: none;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.5rem;
            text-align: left;
        }

        .exit-option:hover {
            background: var(--border-color);
        }

        .exit-option.danger {
            color: var(--error-color);
        }

        .exit-option.danger:hover {
            background: rgba(239, 68, 68, 0.1);
        }

        .exit-icon {
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
        }

        .exit-text {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .exit-option.danger .exit-text {
            color: var(--error-color);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to { 
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Mobile optimizations for progress bar */
        @media (max-width: 480px) {
            .transaction-summary-bar {
                padding: 0.5rem 0.75rem;
            }

            .user-avatar {
                width: 28px;
                height: 28px;
                font-size: 0.75rem;
            }

            .user-name {
                font-size: 0.7rem;
            }

            .user-level {
                font-size: 0.65rem;
            }

            .payment-info {
                font-size: 0.7rem;
            }

            .exchange-rate {
                font-size: 0.65rem;
                padding: 0.2rem 0.4rem;
            }

            .step-progress-bar {
                padding: 0.5rem 0.75rem;
                top: 76px; /* Adjusted for smaller summary bar */
            }

            .step-number {
                width: 24px;
                height: 24px;
                font-size: 0.7rem;
            }

            .step-label {
                font-size: 0.6rem;
            }

            .step-connector {
                width: 16px;
            }
        }

        /* Responsive Design */
        @media (min-width: 768px) {
            .app-container {
                max-width: 500px;
                border-radius: var(--radius-xl);
                margin-top: 2rem;
                margin-bottom: 2rem;
                height: calc(100vh - 4rem);
                box-shadow: var(--shadow-xl);
            }

            .users-status {
                grid-template-columns: 1fr 1fr;
            }

            .inline-actions {
                flex-wrap: nowrap;
            }

            .action-buttons {
                flex-direction: row;
            }
        }

        @media (max-width: 480px) {
            .users-status {
                grid-template-columns: 1fr;
            }

            .inline-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }

            .action-buttons {
                flex-direction: column;
            }

            .action-context {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }

        /* Demo Controls */
        .demo-controls {
            position: fixed;
            top: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
            z-index: 1000;
        }

        .demo-btn {
            padding: 0.5rem 1rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .demo-btn:hover {
            background: var(--primary-hover);
        }

        /* Empty state */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 3rem 2rem;
            text-align: center;
            color: var(--text-muted);
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* High contrast mode */
        @media (prefers-contrast: high) {
            :root {
                --border-color: #000000;
                --text-muted: #333333;
            }
            
            [data-theme="dark"] {
                --border-color: #ffffff;
                --text-muted: #cccccc;
            }
        }

        /* Reduced motion */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body data-theme="light">
    <div class="demo-controls">
        <button class="demo-btn" onclick="toggleTheme()">🌙 Toggle Theme</button>
        <button class="demo-btn" onclick="nextDemoStep()">▶️ Next Step</button>
        <button class="demo-btn" onclick="resetDemo()">🔄 Reset</button>
    </div>

    <div class="app-container">        <!-- Header -->
        <div class="app-header">
            <button class="back-button" onclick="handleBackNavigation()">
                <span class="back-arrow">←</span>
            </button>
            <div class="header-user">
                <div class="avatar">S</div>
                <div class="user-info">
                    <h3>Sarah Johnson</h3>
                    <div class="user-status">
                        <div class="status-dot"></div>
                        <span>Online</span>
                    </div>
                </div>
            </div>
            <button class="header-menu" onclick="showExitMenu()">
                <span class="menu-dots">⋮</span>
            </button>
            <button class="theme-toggle" onclick="toggleTheme()">
                <span id="theme-icon">🌙</span>
            </button>
        </div>

        <!-- Fixed Transaction Summary Bar -->
        <div class="transaction-summary-bar">
            <div class="transaction-summary-content">
                <div class="user-section">
                    <div class="user-avatar">S</div>
                    <div class="user-details">
                        <div class="user-name">Sarah</div>
                        <div class="user-level">⭐ Level 3</div>
                    </div>
                </div>
                
                <div class="transaction-flow">
                    <div class="payment-info">
                        <span class="amount">$1,000</span>
                        <span class="arrow">→</span>
                    </div>
                    <div class="exchange-rate">1.08</div>
                    <div class="payment-info">
                        <span class="arrow">→</span>
                        <span class="amount">€925</span>
                    </div>
                </div>
                
                <div class="user-section">
                    <div class="user-avatar">A</div>
                    <div class="user-details">
                        <div class="user-name">Alex</div>
                        <div class="user-level">🛡️ Level 5</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fixed Step Progress Indicator -->
        <div class="step-progress-bar">
            <div class="step-progress-content">
                <div class="step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-label">Payment Info</div>
                </div>
                <div class="step-connector"></div>
                <div class="step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-label">Payer Selection</div>
                </div>
                <div class="step-connector"></div>
                <div class="step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-label">First Payment</div>
                </div>
                <div class="step-connector"></div>
                <div class="step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-label">Confirmation</div>
                </div>
                <div class="step-connector"></div>
                <div class="step" data-step="5">
                    <div class="step-number">5</div>
                    <div class="step-label">Second Payment</div>
                </div>
                <div class="step-connector"></div>
                <div class="step" data-step="6">
                    <div class="step-number">6</div>
                    <div class="step-label">Complete</div>
                </div>
            </div>
        </div>

        <!-- Message Stream -->
        <div class="message-stream" id="messageStream">
            <!-- Messages will be populated by JavaScript -->
        </div>

        <!-- Smart Action Bar -->
        <div class="smart-action-bar" id="actionBar" style="display: none;">
            <div class="action-context" id="actionContext">
                <!-- Context will be populated by JavaScript -->
            </div>
            <div class="action-buttons" id="actionButtons">
                <!-- Buttons will be populated by JavaScript -->
            </div>
        </div>

        <!-- Message Input -->
        <div class="message-input-container">
            <div class="message-input-row">
                <textarea 
                    class="message-input" 
                    id="messageInput"
                    placeholder="Type your message..."
                    rows="1"
                    onkeydown="handleKeyPress(event)"
                ></textarea>
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                    <span>➤</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Demo state
        let currentStep = 0;
        let messages = [];
        let isDarkMode = false;
        let isTyping = false;        // Demo steps configuration - Updated to match real transaction flow
        const demoSteps = [
            {
                type: 'system',
                content: '💼 Transaction Started<br>$1,000 USD → €925 EUR<br>Rate: 0.925 EUR/USD',
                delay: 1000,
                stepUpdate: 1
            },
            {
                type: 'chat',
                sender: 'other',
                content: 'Perfect! Let\'s proceed with this exchange. The rate looks good.',
                delay: 1500
            },
            {
                type: 'transaction',
                subType: 'payment-readiness-gate',
                content: {
                    title: 'Payment Information Required',
                    status: 'pending',
                    description: 'Both parties must provide payment details before proceeding',
                    yourStatus: 'pending',
                    otherStatus: 'pending',
                    progress: 0,
                    actions: ['Provide Payment Info']
                },
                delay: 1000
            },
            {
                type: 'chat',
                sender: 'user',
                content: 'I\'ll enter my bank details now.',
                delay: 2000
            },
            {
                type: 'system',
                content: '🏦 Your payment details have been securely stored',
                delay: 1500
            },
            {
                type: 'transaction',
                subType: 'payment-readiness-update',
                content: {
                    title: 'Payment Information Status',
                    status: 'partial',
                    description: 'Waiting for Alex to provide payment details',
                    yourStatus: 'ready',
                    otherStatus: 'pending',
                    progress: 50
                },
                delay: 1000
            },
            {
                type: 'chat',
                sender: 'other',
                content: 'Adding my payment info as well...',
                delay: 2000
            },
            {
                type: 'system',
                content: '✅ Both parties payment details collected<br>Ready for payer designation',
                delay: 1500,
                stepUpdate: 2
            },
            {
                type: 'transaction',
                subType: 'payer-negotiation',
                content: {
                    title: 'First Payer Designation',
                    status: 'negotiating',
                    recommendation: 'System recommends: Alex pays first',
                    reason: 'Higher reputation level (Shield Level 5)',
                    currentProposal: 'Alex',
                    actions: ['Agree to Proposal', 'I\'ll Pay First Instead']
                },
                delay: 1500
            },
            {
                type: 'chat',
                sender: 'other',
                content: 'I\'m happy to pay first. Let\'s proceed.',
                delay: 2000
            },
            {
                type: 'system',
                content: '✅ Alex will pay first<br>Starting payment phase...',
                delay: 1500,
                stepUpdate: 3
            },
            {
                type: 'transaction',
                subType: 'awaiting-first-payment',
                content: {
                    title: 'Awaiting First Payment',
                    status: 'pending',
                    amount: '$1,000',
                    payer: 'Alex',
                    recipient: 'Sarah',
                    recipientDetails: 'Bank: Chase ****1234',
                    timeRemaining: '02:45:30',
                    timer: true
                },
                delay: 1000,                actionBar: {
                    type: 'waiting',
                    context: {
                        main: 'Alex is making the payment',
                        sub: '$1,000 to your Chase account ****1234'
                    },
                    timer: {
                        value: '02:45:30',
                        class: 'timer-warning',
                        label: 'Payment window:'
                    },
                    actions: []
                }
            },
            {
                type: 'chat',
                sender: 'other',
                content: 'Payment sent! $1,000 transferred to your Chase account. Reference: TXN-240611-001',
                delay: 3000
            },
            {
                type: 'system',
                content: '💸 Payment declared by Alex<br>Ref: TXN-240611-001',
                delay: 1000,
                stepUpdate: 4
            },
            {
                type: 'transaction',
                subType: 'payment-confirmation-needed',
                content: {
                    title: 'Confirm Payment Receipt',
                    status: 'pending',
                    amount: '$1,000',
                    reference: 'TXN-240611-001',
                    declaredAt: new Date().toLocaleTimeString(),
                    timeRemaining: '01:30:00',
                    actions: ['Confirm Receipt', 'Not Received']
                },
                delay: 1500,                actionBar: {
                    type: 'confirmation',
                    context: {
                        main: 'Alex declared payment',
                        sub: '$1,000 • Ref: TXN-240611-001'
                    },
                    timer: {
                        value: '01:30:00',
                        class: 'timer-critical',
                        label: 'Confirm within:'
                    },
                    actions: [
                        { text: 'Confirm Receipt', type: 'success', primary: true },
                        { text: 'Not Received', type: 'warning' }
                    ]
                }
            },
            {
                type: 'chat',
                sender: 'user',
                content: 'Received! Checking my account now...',
                delay: 2000
            },
            {
                type: 'chat',
                sender: 'user',
                content: 'Confirmed! $1,000 received. Now sending €925 to your account.',
                delay: 2000
            },
            {
                type: 'system',
                content: '✅ First payment confirmed<br>Starting second payment phase...',
                delay: 1500,
                stepUpdate: 5
            },
            {
                type: 'transaction',
                subType: 'awaiting-second-payment',
                content: {
                    title: 'Your Turn to Pay',
                    status: 'pending',
                    amount: '€925',
                    payer: 'Sarah',
                    recipient: 'Alex',
                    recipientDetails: 'Bank: IBAN DE89****5678',
                    timeRemaining: '02:45:30',
                    timer: true,
                    actions: ['Declare Payment Sent']
                },
                delay: 1000,                actionBar: {
                    type: 'payment',
                    context: {
                        main: 'Send €925 to Alex',
                        sub: 'IBAN: DE89****5678'
                    },
                    timer: {
                        value: '02:45:30',
                        class: 'timer-warning',
                        label: 'Payment due:'
                    },
                    actions: [
                        { text: 'Payment Sent', type: 'primary', primary: true }
                    ]
                }
            },
            {
                type: 'system',
                content: '💸 Payment declared by you<br>Ref: EUR-240611-002',
                delay: 3000,
                stepUpdate: 6
            },
            {
                type: 'chat',
                sender: 'user',
                content: 'Payment sent! €925 transferred to your IBAN account. Reference: EUR-240611-002',
                delay: 1000
            },
            {
                type: 'transaction',
                subType: 'awaiting-final-confirmation',
                content: {
                    title: 'Awaiting Final Confirmation',
                    status: 'pending',
                    amount: '€925',
                    reference: 'EUR-240611-002',
                    declaredAt: new Date().toLocaleTimeString(),
                    timeRemaining: '01:30:00'
                },
                delay: 1500,                actionBar: {
                    type: 'waiting',
                    context: {
                        main: 'Waiting for Alex to confirm',
                        sub: '€925 • Ref: EUR-240611-002'
                    },
                    timer: {
                        value: '01:30:00',
                        class: 'timer-info',
                        label: 'Confirmation window:'
                    },
                    actions: []
                }
            },
            {
                type: 'chat',
                sender: 'other',
                content: 'Perfect! €925 received in my account. Transaction complete!',
                delay: 3000
            },
            {
                type: 'system',
                content: '🎉 Transaction Completed Successfully!<br>Both payments confirmed',
                delay: 1500,
                stepUpdate: 6
            },
            {
                type: 'transaction',
                subType: 'transaction-complete',
                content: {
                    title: 'Transaction Complete',
                    status: 'completed',
                    finalAmount1: '$1,000 USD',
                    finalAmount2: '€925 EUR',
                    completedAt: new Date().toLocaleTimeString(),
                    duration: '12 minutes'
                },
                delay: 1000
            }        ];

        // Step Progress Functions
        function updateStepProgress(stepNumber) {
            const steps = document.querySelectorAll('.step');
            const connectors = document.querySelectorAll('.step-connector');
            
            steps.forEach((step, index) => {
                const stepNum = index + 1;
                step.classList.remove('active', 'completed');
                
                if (stepNum < stepNumber) {
                    step.classList.add('completed');
                } else if (stepNum === stepNumber) {
                    step.classList.add('active');
                }
            });
            
            connectors.forEach((connector, index) => {
                const stepNum = index + 1;
                connector.classList.remove('completed');
                
                if (stepNum < stepNumber) {
                    connector.classList.add('completed');
                }
            });
        }

        function resetStepProgress() {
            const steps = document.querySelectorAll('.step');
            const connectors = document.querySelectorAll('.step-connector');
            
            steps.forEach(step => {
                step.classList.remove('active', 'completed');
            });
            connectors.forEach(connector => {
                connector.classList.remove('completed');
            });
            
            // Set first step as active
            if (steps.length > 0) {
                steps[0].classList.add('active');
            }
        }

        // Initialize demo
        function initDemo() {
            messages = [];
            currentStep = 0;
            document.getElementById('messageStream').innerHTML = '';
            hideActionBar();
            resetStepProgress();
            addWelcomeMessage();
        }

        function addWelcomeMessage() {
            const welcomeHTML = `
                <div class="message-item">
                    <div class="system-message">
                        <div class="message-content">
                            <div class="transaction-icon">🚀</div>
                            <h3 style="margin: 0.5rem 0;">Welcome to MUNygo</h3>
                            <p style="margin: 0; opacity: 0.9;">Innovative Transaction + Chat UI Demo</p>
                            <p style="margin: 0.5rem 0 0; font-size: 0.85rem; opacity: 0.8;">Click "Next Step" to see the transaction flow in action</p>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('messageStream').innerHTML = welcomeHTML;
        }

        // Demo progression
        function nextDemoStep() {
            if (currentStep < demoSteps.length) {
                const step = demoSteps[currentStep];
                setTimeout(() => {
                    addMessage(step);
                    currentStep++;
                }, step.delay || 0);
            } else {
                // Demo complete
                setTimeout(() => {
                    const completionHTML = `
                        <div class="message-item">
                            <div class="system-message">
                                <div class="message-content">
                                    <div class="transaction-icon">🎉</div>
                                    <h3 style="margin: 0.5rem 0;">Demo Complete!</h3>
                                    <p style="margin: 0; opacity: 0.9;">You've experienced the innovative unified transaction-chat flow</p>
                                    <p style="margin: 0.5rem 0 0; font-size: 0.85rem; opacity: 0.8;">Click "Reset" to start over</p>
                                </div>
                            </div>
                        </div>
                    `;
                    document.getElementById('messageStream').insertAdjacentHTML('beforeend', completionHTML);
                    scrollToBottom();
                    hideActionBar();
                }, 1000);
            }
        }

        function resetDemo() {
            initDemo();
        }        // Message handling
        function addMessage(step) {
            const messageHTML = createMessageHTML(step);
            document.getElementById('messageStream').insertAdjacentHTML('beforeend', messageHTML);
            scrollToBottom();

            // Handle step progress update
            if (step.stepUpdate) {
                setTimeout(() => {
                    updateStepProgress(step.stepUpdate);
                }, 200);
            }

            // Handle action bar
            if (step.actionBar) {
                setTimeout(() => {
                    showActionBar(step.actionBar);
                }, 500);
            } else if (step.type === 'transaction' && step.subType === 'transaction-complete') {
                hideActionBar();
            }
        }

        function createMessageHTML(step) {
            const timestamp = new Date().toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            switch (step.type) {
                case 'chat':
                    return createChatMessageHTML(step, timestamp);
                case 'system':
                    return createSystemMessageHTML(step, timestamp);
                case 'transaction':
                    return createTransactionMessageHTML(step, timestamp);
                default:
                    return '';
            }
        }

        function createChatMessageHTML(step, timestamp) {
            const isOwn = step.sender === 'user';
            const avatar = isOwn ? 'J' : 'S';
            return `
                <div class="message-item">
                    <div class="chat-message ${isOwn ? 'own' : ''}">
                        <div class="avatar">${avatar}</div>
                        <div class="message-content">
                            <p class="message-text">${step.content}</p>
                            <div class="message-time">${timestamp}</div>
                        </div>
                    </div>
                </div>
            `;
        }

        function createSystemMessageHTML(step, timestamp) {
            return `
                <div class="message-item">
                    <div class="system-message">
                        <div class="message-content">
                            ${step.content}
                        </div>
                    </div>
                </div>
            `;
        }

        function createTransactionMessageHTML(step, timestamp) {
            const content = step.content;
            let messageClass = `transaction-message ${step.subType}`;
            let actionsHTML = '';

            // Create actions if present
            if (content.actions) {
                const actionButtons = content.actions.map(action => {
                    const btnClass = action.includes('Confirm') || action.includes('Agree') ? 'btn-success' : 
                                   action.includes('Report') || action.includes('Not Received') ? 'btn-warning' : 'btn-primary';
                    return `<button class="btn ${btnClass}" onclick="handleTransactionAction('${action}')">${action}</button>`;
                }).join('');
                
                actionsHTML = `<div class="inline-actions">${actionButtons}</div>`;
            }

            // Handle different transaction types
            switch (step.subType) {
                case 'rate-agreement':
                    return `
                        <div class="message-item">
                            <div class="${messageClass}">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <div class="transaction-icon">📈</div>
                                        <span>${content.title}</span>
                                    </div>
                                    <div class="status-tag pending">${content.status}</div>
                                </div>
                                <div class="payment-details">
                                    <div class="amount-display">
                                        <div class="amount-value">${content.amount}</div>
                                        <div class="amount-label">at ${content.rate}</div>
                                    </div>
                                    <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">${content.description}</p>
                                </div>
                                ${actionsHTML}
                            </div>
                        </div>
                    `;

                case 'payment-gate':
                    return `
                        <div class="message-item">
                            <div class="${messageClass}">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <div class="transaction-icon">🏦</div>
                                        <span>${content.title}</span>
                                    </div>
                                    <div class="status-tag ready">${content.status}</div>
                                </div>
                                <div class="payment-gate">
                                    <div class="gate-explanation">
                                        <p>${content.description}</p>
                                    </div>
                                    <div class="users-status">
                                        <div class="user-status-card ${content.yourStatus}">
                                            <div class="user-name">You</div>
                                            <div class="user-status-indicator ${content.yourStatus}">
                                                <span>${content.yourStatus === 'ready' ? '✅' : '⏳'}</span>
                                                <span>${content.yourStatus === 'ready' ? 'Ready' : 'Pending'}</span>
                                            </div>
                                        </div>
                                        <div class="user-status-card ${content.otherStatus}">
                                            <div class="user-name">Sarah</div>
                                            <div class="user-status-indicator ${content.otherStatus}">
                                                <span>${content.otherStatus === 'ready' ? '✅' : '⏳'}</span>
                                                <span>${content.otherStatus === 'ready' ? 'Ready' : 'Pending'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: ${content.progress}%"></div>
                                    </div>
                                    <div class="progress-text">${content.progress}% Complete</div>
                                </div>
                            </div>
                        </div>
                    `;                case 'payer-negotiation':
                    return `
                        <div class="message-item">
                            <div class="${messageClass}">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <div class="transaction-icon">🤝</div>
                                        <span>${content.title}</span>
                                    </div>
                                    <div class="status-tag ${content.status}">${content.status}</div>
                                </div>
                                <div class="payment-details">
                                    <div class="payer-info">
                                        <div class="transaction-icon">💡</div>
                                        <div class="payer-text">${content.recommendation}</div>
                                    </div>
                                    ${content.reason ? `
                                    <div class="payer-info">
                                        <div class="transaction-icon">📋</div>
                                        <div class="payer-text reason">${content.reason}</div>
                                    </div>
                                    ` : ''}
                                    ${content.currentProposal ? `
                                    <div class="current-proposal">
                                        <div class="proposal-header">Current Proposal:</div>
                                        <div class="proposal-payer">${content.currentProposal} pays first</div>
                                    </div>
                                    ` : ''}
                                </div>
                                ${actionsHTML}
                            </div>
                        </div>
                    `;

                case 'payment-declared':
                    return `
                        <div class="message-item">
                            <div class="${messageClass}">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <div class="transaction-icon">💰</div>
                                        <span>${content.title}</span>
                                    </div>
                                    <div class="status-tag pending">${content.status}</div>
                                </div>
                                <div class="payment-details">
                                    <div class="amount-display">
                                        <div class="amount-value">${content.amount}</div>
                                        <div class="amount-label">from ${content.payer}</div>
                                    </div>
                                    <div class="payer-info">
                                        <div class="transaction-icon">🔖</div>
                                        <div class="payer-text">Reference: ${content.reference}</div>
                                        <button class="btn btn-secondary" style="min-width: auto; padding: 0.25rem 0.5rem; font-size: 0.75rem;" onclick="copyReference('${content.reference}')">Copy</button>
                                    </div>
                                </div>
                                ${actionsHTML}
                            </div>                        </div>
                    `;

                case 'payment-readiness-gate':
                    return `
                        <div class="message-item">
                            <div class="${messageClass}">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <div class="transaction-icon">🏦</div>
                                        <span>${content.title}</span>
                                    </div>
                                    <div class="status-tag ${content.status}">${content.status}</div>
                                </div>
                                <div class="payment-gate">
                                    <div class="gate-explanation">
                                        <p>${content.description}</p>
                                    </div>
                                    <div class="users-status">
                                        <div class="user-status-card ${content.yourStatus}">
                                            <div class="user-name">You</div>
                                            <div class="user-status-indicator ${content.yourStatus}">
                                                <span>${content.yourStatus === 'ready' ? '✅' : '⏳'}</span>
                                                <span>${content.yourStatus === 'ready' ? 'Ready' : 'Pending'}</span>
                                            </div>
                                        </div>
                                        <div class="user-status-card ${content.otherStatus}">
                                            <div class="user-name">Alex</div>
                                            <div class="user-status-indicator ${content.otherStatus}">
                                                <span>${content.otherStatus === 'ready' ? '✅' : '⏳'}</span>
                                                <span>${content.otherStatus === 'ready' ? 'Ready' : 'Pending'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: ${content.progress}%"></div>
                                    </div>
                                    <div class="progress-text">${content.progress}% Complete</div>
                                </div>
                                ${actionsHTML}
                            </div>
                        </div>
                    `;

                case 'payment-readiness-update':
                    return `
                        <div class="message-item">
                            <div class="${messageClass}">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <div class="transaction-icon">🏦</div>
                                        <span>${content.title}</span>
                                    </div>
                                    <div class="status-tag ${content.status}">${content.status}</div>
                                </div>
                                <div class="payment-gate">
                                    <div class="gate-explanation">
                                        <p>${content.description}</p>
                                    </div>
                                    <div class="users-status">
                                        <div class="user-status-card ${content.yourStatus}">
                                            <div class="user-name">You</div>
                                            <div class="user-status-indicator ${content.yourStatus}">
                                                <span>${content.yourStatus === 'ready' ? '✅' : '⏳'}</span>
                                                <span>${content.yourStatus === 'ready' ? 'Ready' : 'Pending'}</span>
                                            </div>
                                        </div>
                                        <div class="user-status-card ${content.otherStatus}">
                                            <div class="user-name">Alex</div>                                            <div class="user-status-indicator ${content.otherStatus}">
                                                <span>${content.otherStatus === 'ready' ? '✅' : '⏳'}</span>
                                                <span>${content.otherStatus === 'ready' ? 'Ready' : 'Pending'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: ${content.progress}%"></div>
                                    </div>
                                    <div class="progress-text">${content.progress}% Complete</div>
                                </div>
                            </div>
                        </div>
                    `;

                case 'awaiting-first-payment':
                case 'awaiting-second-payment':
                    const isSecondPayment = step.subType === 'awaiting-second-payment';
                    return `
                        <div class="message-item">
                            <div class="${messageClass}">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <div class="transaction-icon">💸</div>
                                        <span>${content.title}</span>
                                    </div>
                                    <div class="status-tag pending">${content.status}</div>
                                </div>
                                <div class="payment-details">
                                    <div class="amount-display">
                                        <div class="amount-value">${content.amount}</div>
                                        <div class="amount-label">${content.payer} → ${content.recipient}</div>
                                    </div>
                                    <div class="payer-info">
                                        <div class="transaction-icon">🏦</div>
                                        <div class="payer-text">${content.recipientDetails}</div>
                                    </div>
                                    ${content.timer ? `
                                    <div class="timer-section">
                                        <div class="timer-icon">⏰</div>
                                        <div class="timer-text">Time remaining: ${content.timeRemaining}</div>
                                    </div>
                                    ` : ''}
                                </div>
                                ${actionsHTML}
                            </div>
                        </div>
                    `;

                case 'payment-confirmation-needed':
                case 'awaiting-final-confirmation':
                    return `
                        <div class="message-item">
                            <div class="${messageClass}">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <div class="transaction-icon">🤝</div>
                                        <span>${content.title}</span>
                                    </div>
                                    <div class="status-tag pending">${content.status}</div>
                                </div>
                                <div class="payment-details">
                                    <div class="amount-display">
                                        <div class="amount-value">${content.amount}</div>
                                        <div class="amount-label">Declared at ${content.declaredAt}</div>
                                    </div>
                                    <div class="payer-info">
                                        <div class="transaction-icon">🔖</div>
                                        <div class="payer-text">Reference: ${content.reference}</div>
                                        <button class="btn btn-secondary" style="min-width: auto; padding: 0.25rem 0.5rem; font-size: 0.75rem;" onclick="copyReference('${content.reference}')">Copy</button>
                                    </div>
                                    ${content.timeRemaining ? `
                                    <div class="timer-section">
                                        <div class="timer-icon">⏰</div>
                                        <div class="timer-text">Time remaining: ${content.timeRemaining}</div>
                                    </div>
                                    ` : ''}
                                </div>
                                ${actionsHTML}
                            </div>
                        </div>
                    `;

                case 'transaction-complete':
                    return `
                        <div class="message-item">
                            <div class="${messageClass}">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <div class="transaction-icon">🎉</div>
                                        <span>${content.title}</span>
                                    </div>
                                    <div class="status-tag completed">${content.status}</div>
                                </div>
                                <div class="payment-details">
                                    <div class="completion-summary">
                                        <div class="completion-amounts">
                                            <div class="completion-amount">
                                                <span class="amount-value">${content.finalAmount1}</span>
                                                <span class="amount-arrow">↔</span>
                                                <span class="amount-value">${content.finalAmount2}</span>
                                            </div>
                                        </div>
                                        <div class="completion-info">
                                            <div class="completion-time">Completed at ${content.completedAt}</div>
                                            <div class="completion-duration">Duration: ${content.duration}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                case 'payment-confirmed':
                    return `
                        <div class="message-item">
                            <div class="${messageClass}">
                                <div class="transaction-header">
                                    <div class="transaction-title">
                                        <div class="transaction-icon">✅</div>
                                        <span>${content.title}</span>
                                    </div>
                                    <div class="status-tag confirmed">${content.status}</div>
                                </div>
                                <div class="payment-details">
                                    <div class="amount-display">
                                        <div class="amount-value">${content.amount}</div>
                                        <div class="amount-label">Completed at ${content.completedAt}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                default:
                    return '';
            }
        }

        // Action bar handling
        function showActionBar(config) {
            const actionBar = document.getElementById('actionBar');
            const actionContext = document.getElementById('actionContext');
            const actionButtons = document.getElementById('actionButtons');

            // Set action bar class
            actionBar.className = `smart-action-bar action-bar-${config.type}`;            // Create context
            const timerHTML = config.timer ? `
                <div class="timer-display ${config.timer.class || 'normal'}">
                    <div class="timer-label">${config.timer.label}</div>
                    <div class="timer-value">${config.timer.value}</div>
                </div>
            ` : '';
            
            const contextHTML = `
                <div class="context-text">
                    <div class="context-main">${config.context.main}</div>
                    <div class="context-sub">${config.context.sub}</div>
                </div>
                ${timerHTML}
            `;
            actionContext.innerHTML = contextHTML;

            // Create buttons
            const buttonsHTML = config.actions.map(action => {
                const btnClass = `btn btn-${action.type} ${action.primary ? 'btn-large' : ''}`;
                return `<button class="${btnClass}" onclick="handleActionBarAction('${action.text}')">${action.text}</button>`;
            }).join('');
            actionButtons.innerHTML = buttonsHTML;

            // Show action bar
            actionBar.style.display = 'block';
        }

        function hideActionBar() {
            document.getElementById('actionBar').style.display = 'none';
        }

        // Event handlers
        function handleTransactionAction(action) {
            const btn = event.target;
            btn.classList.add('loading');
            btn.disabled = true;

            setTimeout(() => {
                btn.classList.remove('loading');
                btn.disabled = false;
                
                // Simulate action result
                if (action.includes('Agree') || action.includes('Confirm')) {
                    btn.textContent = '✅ ' + action;
                    btn.className = 'btn btn-success';
                }
                
                // Continue demo automatically for some actions
                if (action === 'Agree to Rate' || action === 'Agree') {
                    setTimeout(nextDemoStep, 1000);
                }
            }, 1500);
        }

        function handleActionBarAction(action) {
            const btn = event.target;
            btn.classList.add('loading');
            btn.disabled = true;

            setTimeout(() => {
                btn.classList.remove('loading');
                btn.disabled = false;
                
                if (action === 'Confirm Receipt') {
                    hideActionBar();
                    setTimeout(nextDemoStep, 500);
                }
            }, 1500);
        }

        function copyReference(reference) {
            navigator.clipboard.writeText(reference).then(() => {
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.style.background = 'var(--success-color)';
                btn.style.color = 'white';
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '';
                    btn.style.color = '';
                }, 2000);
            });
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message && !isTyping) {
                isTyping = true;
                const timestamp = new Date().toLocaleTimeString('en-US', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                });
                
                const messageHTML = `
                    <div class="message-item">
                        <div class="chat-message own">
                            <div class="avatar">J</div>
                            <div class="message-content">
                                <p class="message-text">${message}</p>
                                <div class="message-time">${timestamp}</div>
                            </div>
                        </div>
                    </div>
                `;
                
                document.getElementById('messageStream').insertAdjacentHTML('beforeend', messageHTML);
                input.value = '';
                scrollToBottom();
                
                // Auto-resize textarea
                input.style.height = 'auto';
                
                setTimeout(() => {
                    isTyping = false;
                }, 1000);
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
            
            // Auto-resize textarea
            const textarea = event.target;
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }        // Theme toggle
        function toggleTheme() {
            isDarkMode = !isDarkMode;
            document.body.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
            document.getElementById('theme-icon').textContent = isDarkMode ? '☀️' : '🌙';
        }

        // Navigation functions
        function handleBackNavigation() {
            // Check transaction state to determine exit behavior
            const transactionState = getTransactionState();
            
            if (transactionState.isActive && transactionState.isCritical) {
                showExitWarning();
            } else if (transactionState.isActive) {
                showExitConfirmation();
            } else {
                exitToOfferDetails();
            }
        }

        function showExitMenu() {
            const menu = `
                <div class="exit-menu" id="exitMenu">
                    <div class="exit-menu-content">
                        <div class="exit-menu-header">Exit Transaction</div>
                        <button class="exit-option" onclick="exitToOfferDetails()">
                            <span class="exit-icon">📄</span>
                            <span class="exit-text">Back to Offer Details</span>
                        </button>
                        <button class="exit-option" onclick="exitToBrowse()">
                            <span class="exit-icon">🔍</span>
                            <span class="exit-text">Browse Other Offers</span>
                        </button>
                        <button class="exit-option" onclick="exitToMyOffers()">
                            <span class="exit-icon">📋</span>
                            <span class="exit-text">My Offers</span>
                        </button>
                        <button class="exit-option danger" onclick="showCancelTransaction()">
                            <span class="exit-icon">⚠️</span>
                            <span class="exit-text">Cancel Transaction</span>
                        </button>
                        <button class="exit-option" onclick="closeExitMenu()">
                            <span class="exit-icon">✕</span>
                            <span class="exit-text">Stay in Chat</span>
                        </button>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', menu);
        }

        function getTransactionState() {
            // Simplified state detection based on current demo step
            const isActive = currentStep > 2 && currentStep < demoSteps.length - 2;
            const isCritical = currentStep >= 10 && currentStep <= 20; // Payment windows
            
            return { isActive, isCritical };
        }

        function showExitWarning() {
            alert('⚠️ Warning: You\'re in an active payment window. Exiting now may cause issues with your transaction. Are you sure you want to leave?');
        }

        function showExitConfirmation() {
            const confirmed = confirm('💼 You have an active transaction. Your progress will be saved. Continue?');
            if (confirmed) {
                exitToOfferDetails();
            }
        }

        function exitToOfferDetails() {
            alert('📄 Returning to Offer Details...\n(In real app: Navigate to previous screen)');
        }

        function exitToBrowse() {
            alert('🔍 Navigating to Browse Offers...\n(In real app: Navigate to browse screen)');
            closeExitMenu();
        }

        function exitToMyOffers() {
            alert('📋 Navigating to My Offers...\n(In real app: Navigate to user\'s offers)');
            closeExitMenu();
        }

        function showCancelTransaction() {
            const confirmed = confirm('⚠️ Cancel Transaction?\n\nThis will permanently cancel the current exchange. This action cannot be undone.');
            if (confirmed) {
                alert('❌ Transaction cancelled.\nReturning to offer details...');
                closeExitMenu();
            }
        }

        function closeExitMenu() {
            const menu = document.getElementById('exitMenu');
            if (menu) {
                menu.remove();
            }
        }

        // Utility functions
        function scrollToBottom() {
            const messageStream = document.getElementById('messageStream');
            messageStream.scrollTop = messageStream.scrollHeight;
        }

        // Initialize demo on load
        document.addEventListener('DOMContentLoaded', initDemo);

        // Handle input focus for mobile
        document.getElementById('messageInput').addEventListener('focus', function() {
            setTimeout(() => {
                scrollToBottom();
            }, 300);
        });
    </script>
</body>
</html>
