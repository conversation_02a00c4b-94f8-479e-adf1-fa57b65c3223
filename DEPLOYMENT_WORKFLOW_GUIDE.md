# Development to Production Workflow with Database Migrations

## The Problem You Asked About

You currently:
1. <PERSON><PERSON><PERSON> on Windows
2. `git commit` and `git push` changes
3. On CentOS: `git pull` → `docker-compose build` → `docker-compose up -d`

**But what happens when your changes include database schema modifications?**

## The Solution: Proper Migration Workflow

### Phase 1: Development (Windows)

When you modify your database schema:

```powershell
# 1. Edit your schema.prisma file
# (make your changes)

# 2. Create a migration (this generates migration files)
cd C:\Code\MUNygo\backend
npx prisma migrate dev --name "add_user_profile_fields"

# 3. Test locally
npm run dev

# 4. Commit BOTH schema.prisma AND migration files
git add .
git commit -m "Add user profile fields with database migration"
git push origin main
```

**Key Point:** The `prisma migrate dev` command creates migration files in `backend/prisma/migrations/`. You MUST commit these files!

### Phase 2: Production Deployment (CentOS)

Instead of just running `docker-compose build up -d`, use the deployment script:

```bash
# On your CentOS host
cd /path/to/munygo
git pull origin main
./safe-deploy.sh
```

## What the Deployment Script Does

1. **Pulls latest code** (including migration files)
2. **Checks for new migrations** before rebuilding containers
3. **Creates database backup** before applying migrations
4. **Applies migrations** using `prisma migrate deploy`
5. **Rebuilds containers** only after migrations succeed
6. **Runs health checks** to verify everything works

## Key Differences

### ❌ What You Were Doing (Risky)
```bash
git pull
docker-compose build
docker-compose up -d
# 💥 Database schema out of sync with code!
```

### ✅ What You Should Do (Safe)
```bash
git pull
./safe-deploy.sh  # Handles migrations automatically
```

## Commands for Different Scenarios

### Scenario 1: Code changes only (no database changes)
```bash
# CentOS host
git pull
docker-compose build
docker-compose up -d
# This still works fine
```

### Scenario 2: Database schema changes
```bash
# CentOS host
git pull
./safe-deploy.sh  # Use the deployment script
```

### Scenario 3: Check if you have pending migrations
```bash
# CentOS host
docker-compose exec backend npx prisma migrate status
```

## Emergency Procedures

### If migration fails in production:
```bash
# Check what went wrong
docker-compose logs backend

# Check migration status
docker-compose exec backend npx prisma migrate status

# Restore from backup (if needed)
docker-compose exec postgres psql -U munygo_user -d munygo_db < db_backup_YYYYMMDD_HHMMSS.sql
```

### If you need to rollback:
```bash
# Go back to previous git commit
git checkout HEAD~1
./safe-deploy.sh
```

## Files You Need on CentOS Host

1. **`safe-deploy.sh`** - Main deployment script
2. **Make it executable:** `chmod +x safe-deploy.sh`

## Summary

**Your new workflow:**

### Development (Windows):
```powershell
# Make database changes
npx prisma migrate dev --name "descriptive_name"
git add .
git commit -m "Feature with database changes"
git push
```

### Production (CentOS):
```bash
git pull
./safe-deploy.sh
```

**The script handles everything automatically:**
- ✅ Checks for migrations
- ✅ Creates backups
- ✅ Applies migrations safely
- ✅ Rebuilds containers
- ✅ Runs health checks

This ensures your production database always stays in sync with your code changes!
