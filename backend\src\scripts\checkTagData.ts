import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkTagData() {
  console.log('🔍 Checking tag data...');
  
  // Check report type associations
  const associations = await prisma.tagReportTypeAssociation.findMany({
    include: {
      tag: true
    }
  });
  
  console.log('📊 Report Type Associations:');
  associations.forEach(assoc => {
    console.log(`  Tag: ${assoc.tag.name} -> Report Type: ${assoc.reportType} (weight: ${assoc.weight})`);
  });
  
  // Check tags specifically for 'bug' type
  console.log('\n🐛 Tags for BUG report type:');
  const bugTags = await prisma.tag.findMany({
    where: {
      reportTypes: {
        some: {
          reportType: 'BUG'
        }
      }
    },
    include: {
      reportTypes: true
    }
  });
  
  console.log(`Found ${bugTags.length} tags for BUG type:`);
  bugTags.forEach(tag => {
    console.log(`  - ${tag.name}: ${tag.displayName}`);
  });
  
  await prisma.$disconnect();
}

checkTagData().catch(console.error);
