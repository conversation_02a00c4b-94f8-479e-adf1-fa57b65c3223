import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import OtpRateLimiter from '../utils/rateLimiter_new'; // Use your new implementation

// Helper function to wait for a specified time
const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

describe('OtpRateLimiter', () => {
  let rateLimiter: OtpRateLimiter;

  beforeEach(() => {
    // Get a fresh instance before each test
    rateLimiter = OtpRateLimiter.getInstance();
    rateLimiter.clearLimits(); // Clear any existing limits
  });

  afterEach(() => {
    // Clean up after each test
    vi.restoreAllMocks();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance when getInstance is called multiple times', () => {
      const instance1 = OtpRateLimiter.getInstance();
      const instance2 = OtpRateLimiter.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('checkSendLimit', () => {
    it('should allow sending OTP initially', () => {
      const phoneNumber = '+15551234567';
      const result = rateLimiter.checkSendLimit(phoneNumber);
      
      expect(result.allowed).toBe(true);
      expect(result.remainingAttempts).toBe(3); // Assuming MAX_SEND_ATTEMPTS is 3
      expect(result.blockedUntil).toBeUndefined();
    });

    it('should normalize phone numbers correctly', () => {
      const phoneNumber1 = '+15551234567';
      const phoneNumber2 = ' +15551234567 '; // With spaces
      
      // Record attempt for first number
      rateLimiter.recordSendAttempt(phoneNumber1);
      
      // Check if the attempt was recorded for both number formats
      const result1 = rateLimiter.checkSendLimit(phoneNumber1);
      const result2 = rateLimiter.checkSendLimit(phoneNumber2);
      
      expect(result1.remainingAttempts).toBe(2);
      expect(result2.remainingAttempts).toBe(2);
    });

    it('should decrease remaining attempts when recordSendAttempt is called', () => {
      const phoneNumber = '+15551234567';
      
      // Initial check
      const initial = rateLimiter.checkSendLimit(phoneNumber);
      expect(initial.remainingAttempts).toBe(3);
      
      // Record one attempt
      rateLimiter.recordSendAttempt(phoneNumber);
      
      // Check after one attempt
      const afterOne = rateLimiter.checkSendLimit(phoneNumber);
      expect(afterOne.remainingAttempts).toBe(2);
      
      // Record another attempt
      rateLimiter.recordSendAttempt(phoneNumber);
      
      // Check after two attempts
      const afterTwo = rateLimiter.checkSendLimit(phoneNumber);
      expect(afterTwo.remainingAttempts).toBe(1);
    });

    it('should block after max attempts are reached', () => {
      const phoneNumber = '+15551234567';
      
      // Record max attempts
      rateLimiter.recordSendAttempt(phoneNumber);
      rateLimiter.recordSendAttempt(phoneNumber);
      rateLimiter.recordSendAttempt(phoneNumber);
      
      // Now should be blocked
      const result = rateLimiter.checkSendLimit(phoneNumber);
      
      expect(result.allowed).toBe(false);
      expect(result.remainingAttempts).toBe(0);
      expect(result.blockedUntil).toBeDefined();
      
      // Verify block time is in the future
      if (result.blockedUntil) {
        expect(result.blockedUntil > Date.now()).toBe(true);
      }
    });

    it('should enforce cooldown between sends', () => {
      const phoneNumber = '+15551234567';
      
      // Record one attempt
      rateLimiter.recordSendAttempt(phoneNumber);
      
      // Immediately try to send again - should be in cooldown
      const result = rateLimiter.checkSendLimit(phoneNumber);
      
      expect(result.allowed).toBe(false);
      expect(result.blockedUntil).toBeDefined();
      
      // Difference should be close to cooldown time
      if (result.blockedUntil) {
        const diff = result.blockedUntil - Date.now();
        // Expect cooldown to be less than the full block time (which is 1 hour)
        // For a 1-minute cooldown, this should be around 60,000 ms
        expect(diff).toBeLessThan(120000); // Allow some buffer
        expect(diff).toBeGreaterThan(0); // But still in the future
      }
    });

    it('should handle rate limit errors gracefully', () => {
      // Mock console.error to prevent test output pollution
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Mock a function that should throw an error
      const errorFn = vi.fn().mockImplementation(() => {
        throw new Error('Test error');
      });
      
      // Replace the getEntry method to cause an error
      const getEntrySpy = vi.spyOn(rateLimiter as any, 'getEntry').mockImplementation(errorFn);
      
      try {
        // Even with an error, the function should return a safe default
        const result = rateLimiter.checkSendLimit('+15551234567');
        
        // Verify error was logged
        expect(consoleErrorSpy).toHaveBeenCalled();
        
        // Verify safe default values
        expect(result.allowed).toBe(true);
        expect(result.remainingAttempts).toBe(3);
      } finally {
        // Restore original methods
        getEntrySpy.mockRestore();
        consoleErrorSpy.mockRestore();
      }
    });
  });

  describe('checkVerifyLimit', () => {
    it('should allow verification initially', () => {
      const phoneNumber = '+15551234567';
      const result = rateLimiter.checkVerifyLimit(phoneNumber);
      
      expect(result.allowed).toBe(true);
      expect(result.remainingAttempts).toBe(5); // Assuming MAX_VERIFY_ATTEMPTS is 5
      expect(result.blockedUntil).toBeUndefined();
    });

    it('should decrease remaining attempts when recordVerifyAttempt is called', () => {
      const phoneNumber = '+15551234567';
      
      // Initial check
      const initial = rateLimiter.checkVerifyLimit(phoneNumber);
      expect(initial.remainingAttempts).toBe(5);
      
      // Record one attempt
      rateLimiter.recordVerifyAttempt(phoneNumber);
      
      // Check after one attempt
      const afterOne = rateLimiter.checkVerifyLimit(phoneNumber);
      expect(afterOne.remainingAttempts).toBe(4);
    });

    it('should block after max verify attempts are reached', () => {
      const phoneNumber = '+15551234567';
      
      // Record max verify attempts
      for (let i = 0; i < 5; i++) {
        rateLimiter.recordVerifyAttempt(phoneNumber);
      }
      
      // Now should be blocked
      const result = rateLimiter.checkVerifyLimit(phoneNumber);
      
      expect(result.allowed).toBe(false);
      expect(result.remainingAttempts).toBe(0);
      expect(result.blockedUntil).toBeDefined();
    });

    it('should reset verify attempts when a new OTP is requested', () => {
      const phoneNumber = '+15551234567';
      
      // Record some verify attempts
      rateLimiter.recordVerifyAttempt(phoneNumber);
      rateLimiter.recordVerifyAttempt(phoneNumber);
      
      // Check attempts count
      const beforeReset = rateLimiter.checkVerifyLimit(phoneNumber);
      expect(beforeReset.remainingAttempts).toBe(3);
      
      // Now send a new OTP (which should reset verify attempts)
      rateLimiter.recordSendAttempt(phoneNumber);
      
      // Verify attempts should be reset
      const afterReset = rateLimiter.checkVerifyLimit(phoneNumber);
      expect(afterReset.remainingAttempts).toBe(5);
    });
  });
  describe('Memory Management', () => {
    it('should update lastAccessed time when accessing entries', () => {
      // Use fake timers for this test
      vi.useFakeTimers();
      
      try {
        const phoneNumber = '+15551234567';
        
        // Get the entry directly for testing
        const getEntryMethod = (rateLimiter as any).getEntry.bind(rateLimiter);
        
        // Initial entry creation
        const entry = getEntryMethod(phoneNumber);
        const initialTime = entry.lastAccessed;
        
        // Wait a small amount
        vi.advanceTimersByTime(10);
        
        // Access again
        const updatedEntry = getEntryMethod(phoneNumber);
        const updatedTime = updatedEntry.lastAccessed;
        
        // Time should be updated
        expect(updatedTime).toBeGreaterThan(initialTime);
      } finally {
        // Restore real timers
        vi.useRealTimers();
      }
    });
  });

  describe('Edge Cases', () => {
    it('should handle invalid phone number formats gracefully', () => {
      // Console spy to prevent pollution of test output
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      try {
        // Invalid format but should not throw
        const result = rateLimiter.checkSendLimit('invalid-phone');
        
        // Should warn about invalid format
        expect(consoleSpy).toHaveBeenCalled();
        
        // Should still return a result
        expect(result.allowed).toBeDefined();
      } finally {
        consoleSpy.mockRestore();
      }
    });
  });
});
