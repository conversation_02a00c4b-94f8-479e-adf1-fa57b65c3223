const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function getFullTransactionId() {
  try {
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: {
          startsWith: 'cmb0xaj5'
        }
      },
      select: {
        id: true,
        status: true,
        paymentDeclaredAtPayer1: true
      }
    });
    
    if (transaction) {
      console.log(`Full Transaction ID: ${transaction.id}`);
      console.log(`Status: ${transaction.status}`);
      console.log(`Payment Declared: ${transaction.paymentDeclaredAtPayer1 ? 'Yes' : 'No'}`);
    } else {
      console.log('No transaction found starting with cmb0xaj5');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getFullTransactionId();
