import { PrismaClient, TransactionStatus } from '@prisma/client';
import { Server } from 'socket.io';
import { TransactionService } from '../services/transactionService';
import { PayerNegotiationService } from '../services/payerNegotiationService';
import { NotificationService } from '../services/notificationService';
import { ChatService } from '../services/chatService';
import { createInitialTransaction } from '../services/transactionService';
import { ConsoleLogger } from '../utils/logger';

const prisma = new PrismaClient();

// Mock Socket.IO server for testing
const mockSocketServer = {
  to: (room: string) => ({
    emit: (event: string, data: any) => {
      console.log(`[SOCKET] Emitting '${event}' to room '${room}':`, JSON.stringify(data, null, 2));
      return mockSocketServer;
    }
  }),
  emit: (event: string, data: any) => {
    console.log(`[SOCKET] Broadcasting '${event}':`, JSON.stringify(data, null, 2));
    return mockSocketServer;
  }
} as any;

async function createTimerTestTransaction() {
  console.log('🚀 Creating Timer Test Transaction...\n');

  try {
    // Initialize services
    const logger = new ConsoleLogger();
    const notificationService = new NotificationService(mockSocketServer);
    const chatService = new ChatService(mockSocketServer);
    const transactionService = new TransactionService(mockSocketServer, notificationService, chatService);
    const payerNegotiationService = new PayerNegotiationService(prisma, chatService, mockSocketServer, logger, transactionService);

    console.log('✅ Services initialized successfully\n');

    // Find existing users or create test users
    let user1 = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    let user2 = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!user1 || !user2) {
      console.log('❌ Test users not found. <NAME_EMAIL> and <EMAIL> users exist');
      return;
    }

    console.log(`✅ Found test users: ${user1.username} (${user1.id}) and ${user2.username} (${user2.id})`);

    // Create offer
    const offer = await prisma.offer.create({
      data: {
        title: 'Timer Test Offer',
        description: 'Test offer for timer functionality',
        currencyOffered: 'USD',
        amountOffered: 100,
        currencyRequested: 'EUR',
        amountRequested: 85,
        creatorId: user1.id,
        status: 'ACTIVE'
      }
    });

    console.log(`✅ Created offer: ${offer.id}`);

    // Create interest and chat session
    const interest = await prisma.interest.create({
      data: {
        offerId: offer.id,
        userId: user2.id,
        status: 'ACCEPTED'
      }
    });

    const chatSession = await prisma.chatSession.create({
      data: {
        offerId: offer.id,
        interestId: interest.id,
        participant1Id: user1.id,
        participant2Id: user2.id
      }
    });

    console.log(`✅ Created chat session: ${chatSession.id}`);

    // Create transaction
    console.log('\n📝 Creating transaction...');
    const transactionData = {
      chatSessionId: chatSession.id,
      offerId: offer.id,
      currencyA: offer.currencyOffered,
      amountA: offer.amountOffered,
      currencyAProviderId: user1.id,
      currencyB: offer.currencyRequested,
      amountB: offer.amountRequested,
      currencyBProviderId: user2.id
    };

    const transaction = await createInitialTransaction(transactionData);
    console.log(`✅ Created transaction: ${transaction.id} with status ${transaction.status}`);

    // Simulate agreeing to terms (both users)
    console.log('\n📝 Simulating terms agreement...');
    const updatedTx1 = await transactionService.agreeToTerms(transaction.id, user1.id);
    const updatedTx2 = await transactionService.agreeToTerms(updatedTx1.id, user2.id);
    console.log(`✅ Both users agreed to terms. Status: ${updatedTx2.status}`);

    // Initialize negotiation if not already done
    if (updatedTx2.status === TransactionStatus.AWAITING_FIRST_PAYER_DESIGNATION) {
      console.log('\n📝 Initializing payer negotiation...');
      await payerNegotiationService.initializeNegotiation(updatedTx2.id);
      console.log('✅ Negotiation initialized');

      // Simulate accepting system recommendation
      console.log('\n📝 Accepting system recommendation...');
      await payerNegotiationService.acceptCurrentProposal(updatedTx2.id, user1.id);
      await payerNegotiationService.acceptCurrentProposal(updatedTx2.id, user2.id);
      console.log('✅ System recommendation accepted');
    }

    // Get updated transaction
    const finalTransaction = await prisma.transaction.findUnique({
      where: { id: transaction.id },
      include: { chatSession: true }
    });

    console.log('\n🎉 Timer Test Transaction Created Successfully!');
    console.log('='.repeat(50));
    console.log(`Transaction ID: ${finalTransaction?.id}`);
    console.log(`Chat Session ID: ${finalTransaction?.chatSession?.id}`);
    console.log(`Status: ${finalTransaction?.status}`);
    console.log(`Payment Expected By Payer 1: ${finalTransaction?.paymentExpectedByPayer1}`);
    console.log('='.repeat(50));
    console.log(`\n🌐 You can test the timer by navigating to:`);
    console.log(`http://localhost:5174/home`);
    console.log(`\nLogin as user: h@g.<NAME_EMAIL>`);
    console.log(`Navigate to the chat with ID: ${finalTransaction?.chatSession?.id}`);
    console.log('\n✨ The timer should be visible in the transaction flow card!');

  } catch (error) {
    console.error('❌ Error creating timer test transaction:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTimerTestTransaction();
