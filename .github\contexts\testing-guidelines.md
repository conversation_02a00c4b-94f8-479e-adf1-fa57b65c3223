# Testing Guidelines for MUNygo Project

## Test Philosophy
Focus on user-observable behavior rather than implementation details. Test what users can see and interact with, not internal component methods or third-party library internals.

## Essential Test Setup
- Use Vitest configuration with happy-dom environment (frontend)
- Use Vitest with Node.js environment (backend)
- Comprehensive Naive UI component stubbing
- Browser API mocks as needed

## Frontend Testing Patterns

### Component Testing
- Test props, events, user interactions, and conditional rendering
- Use `data-testid` attributes for reliable element selection
- Focus on user workflows and visual states

```typescript
// ✅ Good component test pattern
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import MyComponent from '@/components/MyComponent.vue'

describe('MyComponent', () => {
  it('should render correctly with props', () => {
    const wrapper = mount(MyComponent, {
      props: { title: 'Test Title' }
    })
    
    expect(wrapper.find('[data-testid="component-title"]').text()).toBe('Test Title')
  })
})
```

### Store Testing (Pinia)
- Use proper store isolation with createTestingPinia
- Test actions, getters, and state changes independently
- Mock API calls completely

```typescript
// ✅ Good store test pattern
import { createTestingPinia } from '@pinia/testing'
import { useMyStore } from '@/stores/myStore'

describe('MyStore', () => {
  it('should update state correctly', () => {
    const pinia = createTestingPinia()
    const store = useMyStore(pinia)
    
    store.updateData('new value')
    expect(store.data).toBe('new value')
  })
})
```

### Service Testing
- Mock API calls completely with correct response formats
- Test error handling scenarios
- Validate request parameters and response processing

### Async Testing
- Properly handle async operations with await patterns
- Use fake timers for time-dependent code
- Wait for Vue reactivity updates with nextTick

## Backend Testing Patterns

### Route Testing
- Test complete request/response cycles
- Validate authentication middleware
- Test error scenarios and status codes

### Service Testing
- Mock database operations
- Test business logic independently
- Validate service integrations

### Database Testing
- Use test database or transaction rollbacks
- Test Prisma model relationships
- Validate migration scripts

## Data Test ID Standards

### Naming Convention
Use kebab-case with descriptive, semantic names:
- Buttons: `[action]-btn` (e.g., `submit-btn`, `express-interest-btn`)
- Forms: `[form-name]-form` (e.g., `login-form`, `create-offer-form`)
- Inputs: `[field-name]-input` (e.g., `email-input`, `amount-input`)
- Cards: `[content-type]-card` (e.g., `offer-card`, `transaction-card`)
- Modals: `[modal-type]-modal` (e.g., `offer-details-modal`)
- Lists: `[item-type]-list`, `[item-type]-item`

### Required Test IDs
All interactive elements MUST have data-testid attributes:
- Buttons, links, form inputs
- Modals, drawers, overlays
- Key content areas and conditional elements
- Loading states and skeleton placeholders

## Avoid Legacy Patterns
- Don't copy existing test patterns that may be flawed
- Don't test implementation details or internal methods
- Don't test third-party library internals
- Don't create tightly coupled tests that break with UI changes

## Test File Organization
```
src/
├── components/
│   ├── MyComponent.vue
│   └── __tests__/
│       └── MyComponent.test.ts
├── stores/
│   ├── myStore.ts
│   └── __tests__/
│       └── myStore.test.ts
└── services/
    ├── apiService.ts
    └── __tests__/
        └── apiService.test.ts
```

## Commands
- **Run Tests**: `npm test` (both frontend/backend)
- **Watch Mode**: `npm run test:watch`
- **Coverage**: `npm run test:coverage`
- **Specific Tests**: `npm run test:component-name`
