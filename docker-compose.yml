services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: munygo-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: munygo-backend
    environment:
      NODE_ENV: production
      DATABASE_URL: ${DATABASE_URL}
      JWT_SECRET: ${JWT_SECRET}      
      PORT: ${PORT}
      FRONTEND_URL: ${FRONTEND_URL}      # Debug logging configuration
      CLIENT_LOG_DIRECTORY: /app/logs
      # AI Service Configuration
      GEMINI_API_KEY: ${GEMINI_API_KEY}
      # Email settings (using Ethereal for testing)
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}      
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      # Twilio settings (can be mock values for development)
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_PHONE_NUMBER: ${TWILIO_PHONE_NUMBER}
      TWILIO_DEV_VERIFIED_NUMBER: ${TWILIO_DEV_VERIFIED_NUMBER}
      MOCK_TWILIO_OTP: ${MOCK_TWILIO_OTP}
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - client_logs:/app/logs  # Persistent volume for debug reports
    ports:
      - "127.0.0.1:3004:3000" # New, example non-conflicting host port
    restart: unless-stopped
    command: >
      sh -c "npx prisma generate &&
            npx prisma migrate deploy &&
             npm start"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]      
      interval: 30s      
      timeout: 10s
      retries: 3
      start_period: 30s
  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile      
      args:
        VITE_BACKEND_URL_FOR_CLIENT: ${VITE_BACKEND_URL_FOR_CLIENT}
        VITE_API_BASE_URL: ${VITE_API_BASE_URL}
        VITE_ENABLE_DEBUG_REPORT: ${VITE_ENABLE_DEBUG_REPORT:-true}
        VITE_ADMIN_EMAILS: ${VITE_ADMIN_EMAILS}
    container_name: munygo-frontend
    environment:
      VITE_BACKEND_URL: ${VITE_BACKEND_URL_FOR_CLIENT}
      VITE_API_BASE_URL: ${VITE_API_BASE_URL}
      VITE_ENABLE_DEBUG_REPORT: ${VITE_ENABLE_DEBUG_REPORT:-true}
      VITE_ADMIN_EMAILS: ${VITE_ADMIN_EMAILS}
    depends_on:
      backend:
        condition: service_healthy
    ports:
      - "127.0.0.1:8081:80" # Host_IP:Host_Port:Container_Port
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  client_logs:
    driver: local

networks:
  default:
    name: munygo-network
