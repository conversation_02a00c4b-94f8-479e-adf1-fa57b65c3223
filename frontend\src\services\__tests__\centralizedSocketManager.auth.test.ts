import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';

// Mock socket.io-client
const mockSocket = {
  connected: false,
  id: 'test-socket-id',
  on: vi.fn(),
  emit: vi.fn(),
  disconnect: vi.fn(),
  connect: vi.fn(),
  io: {
    engine: {
      transport: {
        name: 'websocket'
      }
    }
  }
};

const mockIo = vi.fn(() => mockSocket);

vi.mock('socket.io-client', () => ({
  io: mockIo
}));

// Mock auth store
const mockAuthStore = {
  token: 'test-token',
  user: { id: 'user-1', email: '<EMAIL>' },
  isAuthenticated: true
};

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => mockAuthStore
}));

// Mock connection store
const mockConnectionStore = {
  setConnected: vi.fn(),
  setDisconnected: vi.fn(),
  setReconnecting: vi.fn(),
  setTransportType: vi.fn(),
  resetReconnectAttempts: vi.fn()
};

vi.mock('@/stores/connection', () => ({
  useConnectionStore: () => mockConnectionStore
}));

describe('CentralizedSocketManager Authentication Handling', () => {
  let centralizedSocketManager: any;

  beforeEach(async () => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
    
    // Reset mock socket state
    mockSocket.connected = false;
    mockAuthStore.token = 'test-token';
    mockAuthStore.isAuthenticated = true;
    
    // Dynamically import the module to get fresh instance
    const module = await import('../centralizedSocketManager');
    centralizedSocketManager = module.default;
  });

  afterEach(() => {
    vi.resetModules();
  });

  describe('Token Change Detection', () => {
    it('should detect token changes and force reconnection', async () => {
      // First connection with initial token
      await centralizedSocketManager.initializeSocket().catch(() => {});
      
      // Change token (simulating re-login)
      mockAuthStore.token = 'new-token';
      
      // Attempt to initialize again
      await centralizedSocketManager.initializeSocket().catch(() => {});
      
      // Should have called disconnect due to token change
      expect(mockSocket.disconnect).toHaveBeenCalled();
    });

    it('should reset auth error state when token changes', async () => {
      // Simulate auth error state
      const connectErrorHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect_error'
      )?.[1];
      
      if (connectErrorHandler) {
        // Trigger auth error
        connectErrorHandler(new Error('unauthorized'));
      }
      
      // Change token
      mockAuthStore.token = 'new-token';
      
      // Should reset auth error state
      expect(centralizedSocketManager.isInAuthErrorState()).toBe(false);
    });
  });

  describe('Authentication Error Handling', () => {
    it('should detect authentication errors', async () => {
      await centralizedSocketManager.initializeSocket().catch(() => {});
      
      const connectErrorHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect_error'
      )?.[1];
      
      expect(connectErrorHandler).toBeDefined();
      
      // Trigger auth error
      if (connectErrorHandler) {
        connectErrorHandler(new Error('unauthorized'));
      }
      
      expect(centralizedSocketManager.isInAuthErrorState()).toBe(true);
    });

    it('should stop reconnection attempts after max auth errors', async () => {
      await centralizedSocketManager.initializeSocket().catch(() => {});
      
      const connectErrorHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect_error'
      )?.[1];
      
      // Trigger multiple auth errors
      if (connectErrorHandler) {
        for (let i = 0; i < 5; i++) {
          connectErrorHandler(new Error('unauthorized'));
        }
      }
      
      // Should be in auth error state and stop retrying
      expect(centralizedSocketManager.isInAuthErrorState()).toBe(true);
    });
  });

  describe('Connection Recovery', () => {
    it('should reset auth error state on successful connection', async () => {
      // Set up auth error state
      await centralizedSocketManager.initializeSocket().catch(() => {});
      
      const connectErrorHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect_error'
      )?.[1];
      
      if (connectErrorHandler) {
        connectErrorHandler(new Error('unauthorized'));
      }
      
      expect(centralizedSocketManager.isInAuthErrorState()).toBe(true);
      
      // Simulate successful connection
      const connectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect'
      )?.[1];
      
      if (connectHandler) {
        mockSocket.connected = true;
        connectHandler();
      }
      
      // Auth error state should be reset
      expect(centralizedSocketManager.isInAuthErrorState()).toBe(false);
    });

    it('should force reconnect and reset auth state', async () => {
      // Set up auth error state
      await centralizedSocketManager.initializeSocket().catch(() => {});
      
      const connectErrorHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect_error'
      )?.[1];
      
      if (connectErrorHandler) {
        connectErrorHandler(new Error('unauthorized'));
      }
      
      expect(centralizedSocketManager.isInAuthErrorState()).toBe(true);
      
      // Force reconnect should reset auth error state
      await centralizedSocketManager.forceReconnect().catch(() => {});
      
      expect(centralizedSocketManager.isInAuthErrorState()).toBe(false);
    });
  });
});
