# Start MUNygo Development Environment
# This script starts PostgreSQL and gives you instructions for backend/frontend

Write-Host "=== Starting MUNygo Development Environment ===" -ForegroundColor Green

# Start PostgreSQL
Write-Host "Starting PostgreSQL container..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml up -d postgres-dev

# Wait a moment for container to be ready
Start-Sleep -Seconds 3

# Check if container is running
$containerStatus = docker ps --filter "name=munygo-postgres-dev" --format "{{.Status}}"
if ($containerStatus -like "*Up*") {
    Write-Host "✅ PostgreSQL is running on localhost:5433" -ForegroundColor Green
} else {
    Write-Host "❌ PostgreSQL failed to start" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== Next Steps ===" -ForegroundColor Cyan
Write-Host "1. Backend: cd backend && npm run dev" -ForegroundColor White
Write-Host "2. Frontend: cd frontend && npm run dev" -ForegroundColor White
Write-Host ""
Write-Host "=== Database Info ===" -ForegroundColor Cyan
Write-Host "Database: postgresql://munygo_user:munygo_password@localhost:5433/munygo_dev" -ForegroundColor White
Write-Host "Container: munygo-postgres-dev" -ForegroundColor White
Write-Host ""
Write-Host "=== To Stop ===" -ForegroundColor Cyan
Write-Host "docker-compose -f docker-compose.dev.yml down" -ForegroundColor White
