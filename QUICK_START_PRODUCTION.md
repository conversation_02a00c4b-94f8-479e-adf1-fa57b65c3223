# 🚀 Quick Start Guide - Production Deployment

## Summary
Deploy the debug reporting feature to your CentOS 9 production server in 3 simple steps.

## Prerequisites
- CentOS 9 server with SSH access
- Docker and Docker Compose installed
- Your production database running

## 📋 Step-by-Step Commands

### 1. Upload Files to Server
```bash
# From your local machine, upload the necessary files:
scp migrate-docker-production.sh deploy-production-centos9.sh validate-production-environment.sh user@your-server:/path/to/munygo/
scp .env user@your-server:/path/to/munygo/
scp docker-compose.yml user@your-server:/path/to/munygo/
```

### 2. SSH into Server and Prepare
```bash
# SSH into your production server
ssh user@your-production-server

# Navigate to your project directory
cd /path/to/munygo

# Make scripts executable
chmod +x *.sh

# Run validation (optional but recommended)
./validate-production-environment.sh
```

### 3. Deploy
```bash
# Step 1: Migrate database (adds debug tables safely using Docker)
./migrate-docker-production.sh

# Step 2: Deploy new Docker containers
./deploy-production-centos9.sh
```

## ✅ Verification
After deployment, check:
1. **Debug button visible**: Log in as admin, look for "Debug Report" button
2. **Containers running**: `docker-compose ps`
3. **Logs clean**: `docker-compose logs -f`

## 🛟 Emergency Rollback
If needed:
```bash
# Database rollback
psql $DATABASE_URL < backup_YYYYMMDD_HHMMSS.sql

# Container rollback
docker-compose down
# Then restore from your previous working state
```

## 🔧 Troubleshooting
- **Debug button not showing**: Clear browser cache, check admin emails
- **Container errors**: Check logs with `docker-compose logs`
- **Database errors**: Verify DATABASE_URL is correct

---

**Total deployment time**: ~10-15 minutes (including backup and verification)

**Data safety**: ✅ Database backup created automatically, no existing data lost
