{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "dotenv -e .env.test -- cross-env NODE_ENV=test vitest", "test:watch": "dotenv -e .env.test -- cross-env NODE_ENV=test vitest --watch", "test:coverage": "dotenv -e .env.test -- cross-env NODE_ENV=test vitest --coverage", "test:notification": "dotenv -e .env.test -- cross-env NODE_ENV=test vitest run src/test/notificationService.test.ts", "test:integration": "dotenv -e .env.test -- cross-env NODE_ENV=test vitest run src/test/integration/", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:reset": "prisma migrate reset --force", "docker:build": "docker build -t munygo-backend .", "docker:run": "docker run -p 3000:3000 munygo-backend", "migrate:debug-reports": "ts-node src/scripts/migrateDebugReports.ts", "seed:test-users": "ts-node src/scripts/seedTestUsers.ts", "seed:payment-methods": "ts-node src/scripts/seedTestPaymentMethods.ts", "seed:test-offers": "ts-node src/scripts/seedTestOffers.ts", "seed:all": "ts-node src/scripts/seedAll.ts", "clear:test-users": "ts-node src/scripts/clearTestUsers.ts", "reset:dev": "ts-node src/scripts/resetDev.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@google/genai": "^1.3.0", "@hono/node-server": "^1.14.1", "@hono/zod-validator": "^0.5.0", "@otplib/preset-default": "^12.0.1", "@prisma/client": "^6.8.2", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/nodemailer": "^6.4.17", "axios": "^1.9.0", "bcrypt": "^5.1.1", "dotenv": "^16.5.0", "hono": "^4.7.8", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.10.1", "socket.io": "^4.8.1", "thirty-two": "^1.0.2", "twilio": "^5.5.2", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.15.3", "@types/supertest": "^6.0.3", "@vitest/coverage-v8": "^3.1.2", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "dotenv-flow": "^4.1.0", "nodemon": "^3.1.10", "prisma": "^6.8.2", "run-script-os": "^1.1.6", "socket.io-client": "^4.8.1", "supertest": "^7.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3", "vitest": "^3.1.2"}}