# Manual Test Guide: Match Notification Logic

## Overview
This guide will help you verify that the match notification logic is working correctly:
- When User A accepts a match, only User B should receive a notification
- When both users accept, both should receive mutual acceptance notifications
- No duplicate notifications should be created

## Prerequisites
- Backend server running on http://localhost:3000
- Frontend server running on http://localhost:5173  
- Two test user accounts

## Test Steps

### Step 1: Prepare Test Users
1. Open two browser windows (or use incognito mode for second user)
2. Register/login with two different test accounts:
   - User A: <EMAIL>
   - User B: <EMAIL>

### Step 2: Create Matching Offers
1. **User A** creates an offer:
   - Type: BUY
   - Currency Pair: USD/EUR
   - Amount: 100
   - Rate: 0.85
   
2. **User B** creates a matching offer:
   - Type: SELL
   - Currency Pair: USD/EUR  
   - Amount: 100
   - Rate: 0.85

### Step 3: Verify Match Discovery
1. Both users should receive "Match Found" notifications
2. Check that both users see the match in their matches section

### Step 4: Test First Acceptance (Critical Test)
1. **User A** accepts the match
2. **Expected Result:**
   - ✅ User A should NOT receive any new notification
   - ✅ User B should receive "Match accepted by other" notification
   - ✅ Match status should update to "Partial Accept" for both users

### Step 5: Test Mutual Acceptance
1. **User B** accepts the match
2. **Expected Result:**
   - ✅ Both users should receive "Match converted to chat" notifications
   - ✅ Match status should update to "Both Accepted"
   - ✅ Chat session should be created
   - ✅ Transaction should be initiated

### Step 6: Verify No Duplicates
1. Check notification lists for both users
2. **Expected Result:**
   - ✅ No duplicate notifications
   - ✅ Each user has appropriate notifications only
   - ✅ Notification counts are correct

## Expected Notification Timeline

### User A Notifications:
1. "Match Found" (when offers matched)
2. "Match converted to chat" (when User B accepts)

### User B Notifications:  
1. "Match Found" (when offers matched)
2. "Match accepted by other" (when User A accepts first)
3. "Match converted to chat" (when User B accepts)

## Debugging Tips

### Check Browser Console
```javascript
// Check notification store state
window.notificationStore = window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps[0].config.globalProperties.$pinia._s.get('notificationStore')
console.log('Notifications:', notificationStore.notifications)
```

### Check Network Tab
- Monitor API calls to `/api/notifications`
- Monitor WebSocket events in browser dev tools

### Check Backend Logs
```bash
# In backend terminal, watch for these log messages:
# [MatchingService] Emitted match accepted event to other user: [userId] for match: [matchId]
# [MatchingService] Emitted match complete event to both users for match: [matchId]
```

## Success Criteria
✅ Only appropriate users receive notifications at each stage  
✅ No duplicate notifications are created  
✅ Notification timing matches expected timeline  
✅ All notification types display correctly in UI  
✅ Socket events are emitted to correct users only

## Failure Scenarios to Watch For
❌ User A receives notification when they accept (should not happen)  
❌ Duplicate notifications appear  
❌ Missing notifications for User B  
❌ Both users receive notifications on first acceptance

## Notes
- Clear browser local storage between tests if needed
- Use browser dev tools to monitor WebSocket connections
- Check that notification bell updates correctly
- Verify notification persistence across page refreshes
