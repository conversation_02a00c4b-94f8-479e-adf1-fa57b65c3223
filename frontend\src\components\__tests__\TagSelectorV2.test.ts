import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import { createTesting<PERSON>inia } from '@pinia/testing';
import { nextTick } from 'vue';
import { NConfigProvider } from 'naive-ui';
import TagSelectorV2 from '@/components/TagSelectorV2.vue';
import { useTagStore } from '@/stores/tagStore';
import type { TagWithRelations } from '@/types/api';

// Mock the icons to avoid import issues in tests
vi.mock('@vicons/tabler', () => ({
  Bulb: { name: 'Bulb' },
  Tag: { name: 'Tag' },
  Plus: { name: 'Plus' },
  Check: { name: 'Check' },
  User: { name: 'User' }
}));

// Mock the useI18n composable
const mockT = vi.fn((key: string, params?: any) => {
  const translations: Record<string, string> = {
    'debug.tags.loading': 'Loading tags...',
    'debug.tags.loadError': 'Failed to load tags',
    'debug.tags.selectTags': 'Select Tags',
    'debug.tags.selectedCount': `${params?.count || 0} selected`,
    'debug.tags.aiSuggestions': 'AI Suggestions',
    'debug.tags.predefinedTags': 'Available Tags',
    'debug.tags.customTags': 'Custom Tags',
    'debug.tags.addCustomTag': 'Add custom tag...',
    'debug.tags.add': 'Add',
    'debug.tags.customTagAdded': `Custom tag '${params?.name}' added`,
    'debug.tags.customTagRemoved': `Tag '${params?.name}' removed`,
    'debug.tags.allTagsCleared': 'All tags cleared',
    'debug.tags.maxTagsReached': `Maximum of ${params?.max} tags allowed`,
    'debug.tags.customTagsCount': `${params?.current} of ${params?.max} custom tags`,
    'debug.tags.approachingLimit': `Approaching tag limit: ${params?.current}/${params?.max}`,
    'debug.tags.categories.general': 'General',
    'debug.tags.categories.technical': 'Technical',
    'debug.tags.categories.uiux': 'UI/UX',
    'debug.tags.categories.performance': 'Performance',
    'debug.tags.categories.uncategorized': 'Uncategorized',
    'debug.tags.predefined': 'Predefined',
    'debug.tags.custom': 'Custom',
    'debug.tags.total': 'Total',
    'common.retry': 'Retry',
    'common.clearAll': 'Clear All'
  };
  return translations[key] || key;
});

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: mockT
  })
}));

// Mock useMessage
const mockMessage = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn()
};

vi.mock('naive-ui', async () => {
  const actual = await vi.importActual('naive-ui');
  return {
    ...actual,
    useMessage: () => mockMessage
  };
});

// Mock tag data
const mockTags: TagWithRelations[] = [
  {
    id: 'tag-1',
    name: 'Bug',
    description: 'Bug related issues',
    isActive: true,
    category: {
      id: 'cat-1',
      name: 'General',
      description: 'General tags',
      isActive: true
    },
    reportCount: 5
  },
  {
    id: 'tag-2',
    name: 'Performance',
    description: 'Performance issues',
    isActive: true,
    category: {
      id: 'cat-2',
      name: 'Technical',
      description: 'Technical tags',
      isActive: true
    },
    reportCount: 3
  },
  {
    id: 'tag-3',
    name: 'UI Issue',
    description: 'User interface problems',
    isActive: true,
    category: {
      id: 'cat-3',
      name: 'UI/UX',
      description: 'UI/UX tags',
      isActive: true
    },
    reportCount: 8
  }
];

describe('TagSelectorV2', () => {
  let wrapper: VueWrapper;
  let tagStore: any;

  const createWrapper = (props = {}) => {
    const pinia = createTestingPinia({
      createSpy: vi.fn,
      stubActions: false
    });

    wrapper = mount(NConfigProvider, {
      slots: {
        default: () => mount(TagSelectorV2, {
          props: {
            selectedTagIds: [],
            allowCustomTags: true,
            enableAiSuggestions: true,
            maxTags: 10,
            maxCustomTags: 5,
            ...props
          },
          global: {
            plugins: [pinia]
          }
        })
      }
    });

    tagStore = useTagStore();
    
    // Setup mock store data
    tagStore.tags = mockTags;
    tagStore.activeTags = mockTags;
    tagStore.tagsByCategory = {
      'General': [mockTags[0]],
      'Technical': [mockTags[1]],
      'UI/UX': [mockTags[2]]
    };

    return wrapper;
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('Initialization', () => {
    it('should render without errors', () => {
      createWrapper();
      expect(wrapper.exists()).toBe(true);
    });

    it('should show loading state initially', async () => {
      createWrapper();
      const component = wrapper.findComponent(TagSelectorV2);
      
      // Set loading state
      await component.vm.$nextTick();
      component.vm.isLoading = true;
      await nextTick();

      expect(wrapper.find('[data-testid="loading-state"]').exists()).toBe(true);
      expect(wrapper.text()).toContain('Loading tags...');
    });

    it('should show error state when loading fails', async () => {
      createWrapper();
      const component = wrapper.findComponent(TagSelectorV2);
      
      component.vm.error = 'Failed to load tags';
      component.vm.isLoading = false;
      await nextTick();

      expect(wrapper.find('.error-state').exists()).toBe(true);
      expect(wrapper.text()).toContain('Failed to load tags');
    });

    it('should load and display predefined tags', async () => {
      createWrapper();
      const component = wrapper.findComponent(TagSelectorV2);
      
      component.vm.isLoading = false;
      component.vm.error = null;
      await nextTick();

      expect(wrapper.find('.tag-interface').exists()).toBe(true);
      expect(wrapper.find('.predefined-section').exists()).toBe(true);
    });
  });

  describe('Props Handling', () => {
    it('should handle selectedTagIds prop correctly', async () => {
      createWrapper({ selectedTagIds: ['tag-1', 'tag-2'] });
      const component = wrapper.findComponent(TagSelectorV2);
      
      expect(component.vm.internalSelectedIds).toEqual(['tag-1', 'tag-2']);
    });

    it('should respect allowCustomTags prop', async () => {
      createWrapper({ allowCustomTags: false });
      const component = wrapper.findComponent(TagSelectorV2);
      
      component.vm.isLoading = false;
      await nextTick();

      const customInput = wrapper.find('.custom-input');
      expect(customInput.attributes('disabled')).toBeDefined();
    });

    it('should respect maxTags limit', async () => {
      createWrapper({ maxTags: 3 });
      const component = wrapper.findComponent(TagSelectorV2);
      
      expect(component.props().maxTags).toBe(3);
    });

    it('should handle compact mode', () => {
      createWrapper({ compact: true });
      
      expect(wrapper.find('.tag-selector-v2.compact').exists()).toBe(true);
    });
  });

  describe('Tag Selection', () => {
    beforeEach(() => {
      createWrapper();
    });

    it('should toggle predefined tag selection', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      await nextTick();

      // Select a tag
      component.vm.handleTagToggle('tag-1', true, 'PREDEFINED');
      
      expect(component.vm.internalSelectedIds).toContain('tag-1');
      expect(component.vm.isTagSelected('tag-1')).toBe(true);

      // Deselect the tag
      component.vm.handleTagToggle('tag-1', false, 'PREDEFINED');
      
      expect(component.vm.internalSelectedIds).not.toContain('tag-1');
      expect(component.vm.isTagSelected('tag-1')).toBe(false);
    });

    it('should emit tagsChanged event when selection changes', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      await nextTick();

      component.vm.handleTagToggle('tag-1', true, 'PREDEFINED');
      
      await nextTick();

      const emitted = component.emitted('tagsChanged');
      expect(emitted).toBeTruthy();
      expect(emitted![0][0]).toEqual([
        {
          id: 'tag-1',
          name: 'Bug',
          isCustom: false,
          origin: 'PREDEFINED'
        }
      ]);
    });

    it('should prevent selection when max tags limit reached', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      
      // Set a low max tags limit
      component.vm.$props.maxTags = 1;
      
      // Select first tag
      component.vm.handleTagToggle('tag-1', true, 'PREDEFINED');
      
      // Try to select second tag (should be prevented)
      component.vm.handleTagToggle('tag-2', true, 'PREDEFINED');
      
      expect(component.vm.internalSelectedIds).toEqual(['tag-1']);
      expect(mockMessage.warning).toHaveBeenCalledWith('Maximum of 1 tags allowed');
    });
  });

  describe('Custom Tags', () => {
    beforeEach(() => {
      createWrapper();
    });

    it('should add custom tags correctly', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      await nextTick();

      component.vm.newCustomTagInput = 'Custom Bug';
      await component.vm.addCustomTag();

      expect(component.vm.customTags).toHaveLength(1);
      expect(component.vm.customTags[0].name).toBe('Custom Bug');
      expect(component.vm.customTags[0].id).toMatch(/^custom-/);
      expect(mockMessage.success).toHaveBeenCalledWith("Custom tag 'Custom Bug' added");
    });

    it('should prevent duplicate custom tags', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      
      // Add first custom tag
      component.vm.newCustomTagInput = 'Custom Tag';
      await component.vm.addCustomTag();

      // Try to add duplicate
      component.vm.newCustomTagInput = 'Custom Tag';
      
      expect(component.vm.canAddCustomTag).toBe(false);
    });

    it('should remove custom tags correctly', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      
      // Add a custom tag first
      component.vm.newCustomTagInput = 'Test Tag';
      await component.vm.addCustomTag();
      
      const customTagId = component.vm.customTags[0].id;
      
      // Remove the custom tag
      component.vm.removeCustomTag(customTagId);
      
      expect(component.vm.customTags).toHaveLength(0);
      expect(mockMessage.info).toHaveBeenCalledWith("Tag 'Test Tag' removed");
    });

    it('should respect maxCustomTags limit', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      component.vm.$props.maxCustomTags = 1;
      
      // Add first custom tag
      component.vm.newCustomTagInput = 'First Tag';
      await component.vm.addCustomTag();

      // Try to add second custom tag
      component.vm.newCustomTagInput = 'Second Tag';
      
      expect(component.vm.canAddCustomTag).toBe(false);
    });

    it('should emit customTagAdded event', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      
      component.vm.newCustomTagInput = 'New Custom Tag';
      await component.vm.addCustomTag();

      const emitted = component.emitted('customTagAdded');
      expect(emitted).toBeTruthy();
      expect(emitted![0][0]).toMatchObject({
        name: 'New Custom Tag',
        id: expect.stringMatching(/^custom-/)
      });
    });
  });

  describe('AI Suggestions', () => {
    beforeEach(() => {
      createWrapper({ enableAiSuggestions: true, reportType: 'bug' });
    });

    it('should display AI suggestions when available', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      
      // Mock AI suggestions
      component.vm.aiSuggestions = [
        { id: 'ai-1', name: 'Frontend Bug' },
        { id: 'ai-2', name: 'JavaScript Error' }
      ];
      
      await nextTick();

      expect(wrapper.find('.suggestions-section').exists()).toBe(true);
      expect(wrapper.text()).toContain('AI Suggestions');
    });

    it('should handle AI suggestion selection', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      
      component.vm.aiSuggestions = [
        { id: 'ai-1', name: 'Frontend Bug' }
      ];
      
      await nextTick();

      component.vm.handleTagToggle('ai-1', true, 'AI_SUGGESTED');
      
      expect(component.vm.internalSelectedIds).toContain('ai-1');
    });
  });

  describe('Category Management', () => {
    beforeEach(() => {
      createWrapper();
    });

    it('should display category tabs when multiple categories exist', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      await nextTick();

      expect(component.vm.categorizedTags.length).toBeGreaterThan(1);
      expect(wrapper.find('.category-tabs').exists()).toBe(true);
    });

    it('should switch between categories correctly', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      component.vm.activeCategory = 'General';
      await nextTick();

      component.vm.handleCategoryChange('Technical');
      
      expect(component.vm.activeCategory).toBe('Technical');
    });

    it('should display correct category display names', () => {
      const component = wrapper.findComponent(TagSelectorV2);
      
      expect(component.vm.getCategoryDisplayName('General')).toBe('General');
      expect(component.vm.getCategoryDisplayName('Technical')).toBe('Technical');
      expect(component.vm.getCategoryDisplayName('UI/UX')).toBe('UI/UX');
    });
  });

  describe('Clear All Functionality', () => {
    beforeEach(() => {
      createWrapper();
    });

    it('should clear all selections', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      
      // Add some selections
      component.vm.internalSelectedIds = ['tag-1', 'tag-2'];
      component.vm.customTags = [{ id: 'custom-1', name: 'Custom Tag' }];
      
      await component.vm.clearAllSelections();
      
      expect(component.vm.internalSelectedIds).toHaveLength(0);
      expect(component.vm.customTags).toHaveLength(0);
      expect(mockMessage.success).toHaveBeenCalledWith('All tags cleared');
    });
  });

  describe('Computed Properties', () => {
    beforeEach(() => {
      createWrapper();
    });

    it('should calculate totalSelectedCount correctly', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      
      component.vm.internalSelectedIds = ['tag-1', 'tag-2'];
      component.vm.customTags = [{ id: 'custom-1', name: 'Custom' }];
      
      expect(component.vm.totalSelectedCount).toBe(3);
    });

    it('should calculate hasSelectedTags correctly', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      
      expect(component.vm.hasSelectedTags).toBe(false);
      
      component.vm.internalSelectedIds = ['tag-1'];
      await nextTick();
      
      expect(component.vm.hasSelectedTags).toBe(true);
    });

    it('should detect when approaching tag limit', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      component.vm.$props.maxTags = 5;
      
      component.vm.internalSelectedIds = ['tag-1', 'tag-2', 'tag-3'];
      await nextTick();
      
      expect(component.vm.isApproachingLimit).toBe(true);
    });
  });

  describe('Props Reactivity', () => {
    it('should react to selectedTagIds prop changes', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      
      await component.setProps({ selectedTagIds: ['tag-1', 'tag-2'] });
      
      expect(component.vm.internalSelectedIds).toEqual(['tag-1', 'tag-2']);
    });

    it('should react to reportType changes and update AI suggestions', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      
      await component.setProps({ reportType: 'performance' });
      
      // AI suggestions should be updated based on new report type
      expect(component.vm.aiSuggestions).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle tag store fetch errors gracefully', async () => {
      // Mock store to throw error
      tagStore.fetchTags = vi.fn().mockRejectedValue(new Error('Network error'));
      
      createWrapper();
      const component = wrapper.findComponent(TagSelectorV2);
      
      await component.vm.loadPredefinedTags();
      
      expect(component.vm.error).toBe('Failed to load tags');
      expect(component.vm.isLoading).toBe(false);
    });

    it('should handle custom tag addition errors gracefully', async () => {
      const component = wrapper.findComponent(TagSelectorV2);
      component.vm.isLoading = false;
      
      // Mock console.error to prevent error output in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Force an error scenario
      const originalPush = Array.prototype.push;
      Array.prototype.push = vi.fn().mockImplementation(() => {
        throw new Error('Array error');
      });
      
      component.vm.newCustomTagInput = 'Test Tag';
      await component.vm.addCustomTag();
      
      expect(mockMessage.error).toHaveBeenCalledWith('Failed to add custom tag');
      
      // Restore original methods
      Array.prototype.push = originalPush;
      consoleSpy.mockRestore();
    });
  });
});
