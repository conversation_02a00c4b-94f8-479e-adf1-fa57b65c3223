import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createTesting<PERSON>inia } from '@pinia/testing'
import DebugReportButtonEnhanced from '../../components/DebugReportButtonEnhanced.vue'
import { createI18n } from 'vue-i18n'
import axios from 'axios'
import type { ComponentPublicInstance } from 'vue'

// Mock axios
vi.mock('axios')
const mockedAxios = vi.mocked(axios)

// Create i18n instance with test translations
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en: {
      debug: {
        reportIssue: 'Report Issue',
        reportType: 'Report Type',
        title: 'Title',
        titlePlaceholder: 'Brief summary of the issue',
        description: 'Description',
        descriptionPlaceholder: 'Detailed description',
        stepsToReproduce: 'Steps to Reproduce',
        stepsPlaceholder: 'Step-by-step instructions',
        expectedBehavior: 'Expected Behavior',
        expectedPlaceholder: 'What should happen',
        actualBehavior: 'Actual Behavior',
        actualPlaceholder: 'What actually happens',
        severity: 'Severity',
        additionalNotes: 'Additional Notes',
        notesPlaceholder: 'Any other relevant information',
        submitReport: 'Submit Report',
        submitting: 'Submitting...',
        reportSubmitted: 'Report submitted successfully',
        reportFailed: 'Failed to submit report'
      }
    }
  }
})

describe('Debug Report Integration Tests', () => {
  let wrapper: VueWrapper<ComponentPublicInstance>
  let originalEnv: string | undefined

  beforeEach(() => {
    // Save original NODE_ENV and set to development
    originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'
    
    // Reset all mocks
    vi.clearAllMocks()
    
    wrapper = mount(DebugReportButtonEnhanced, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false
          }),
          i18n
        ]
      }
    })
  })

  afterEach(() => {
    // Restore original NODE_ENV
    process.env.NODE_ENV = originalEnv
    wrapper?.unmount()
  })

  describe('End-to-End Data Flow', () => {
    it('should collect complete bug report data and send to backend successfully', async () => {
      // Mock successful API response
      const mockReportId = 'integration-test-report-123'
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Debug report received successfully. Thank you for helping us improve!',
          reportId: mockReportId
        }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Fill out the complete form
      const vm = wrapper.vm as any
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Integration Test Bug',
          description: 'This is a comprehensive bug report for integration testing',
          stepsToReproduce: '1. Load the application\n2. Navigate to test page\n3. Click the problematic button\n4. Observe the error',
          expectedBehavior: 'Button should perform the intended action without errors',
          actualBehavior: 'Button throws a JavaScript error and fails to complete the action',
          severity: 'high',
          additionalNotes: 'This issue affects all users on Chrome browsers version 120+',
          tags: ['urgent', 'browser-specific', 'javascript-error']
        }
      })

      // Submit the form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      // Wait for async operations to complete
      await wrapper.vm.$nextTick()

      // Verify the API was called with the correct data structure
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/api/debug/report-issue',
        expect.objectContaining({
          reportDetails: {
            type: 'bug',
            title: 'Integration Test Bug',
            description: 'This is a comprehensive bug report for integration testing',
            stepsToReproduce: '1. Load the application\n2. Navigate to test page\n3. Click the problematic button\n4. Observe the error',
            expectedBehavior: 'Button should perform the intended action without errors',
            actualBehavior: 'Button throws a JavaScript error and fails to complete the action',
            severity: 'high',
            additionalNotes: 'This issue affects all users on Chrome browsers version 120+',
            tags: ['urgent', 'browser-specific', 'javascript-error']
          },
          logs: expect.any(Array)
        })
      )

      // Verify the form was reset after successful submission
      expect(vm.reportForm.title).toBe('')
      expect(vm.reportForm.description).toBe('')
      expect(vm.reportForm.stepsToReproduce).toBe('')
      expect(vm.showModal).toBe(false)
    })

    it('should collect feature request data and send to backend', async () => {
      // Mock successful API response
      const mockReportId = 'feature-request-456'
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Debug report received successfully. Thank you for helping us improve!',
          reportId: mockReportId
        }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Fill out feature request form
      const vm = wrapper.vm as any
      await wrapper.setData({
        reportForm: {
          type: 'feature',
          title: 'Dark Mode Implementation',
          description: 'Add comprehensive dark mode support throughout the application',
          severity: 'medium',
          additionalNotes: 'This would significantly improve user experience, especially for users in low-light environments',
          tags: ['enhancement', 'ui-improvement', 'accessibility']
        }
      })

      // Submit the form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      await wrapper.vm.$nextTick()

      // Verify the API was called with feature request data
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/api/debug/report-issue',
        expect.objectContaining({
          reportDetails: {
            type: 'feature',
            title: 'Dark Mode Implementation',
            description: 'Add comprehensive dark mode support throughout the application',
            severity: 'medium',
            additionalNotes: 'This would significantly improve user experience, especially for users in low-light environments',
            tags: ['enhancement', 'ui-improvement', 'accessibility']
          },
          logs: expect.any(Array)
        })
      )

      // Verify feature request doesn't include bug-specific fields
      const callData = mockedAxios.post.mock.calls[0][1] as any
      expect(callData.reportDetails.stepsToReproduce).toBeUndefined()
      expect(callData.reportDetails.expectedBehavior).toBeUndefined()
      expect(callData.reportDetails.actualBehavior).toBeUndefined()
    })

    it('should collect feedback data and send to backend', async () => {
      // Mock successful API response
      const mockReportId = 'feedback-789'
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Debug report received successfully. Thank you for helping us improve!',
          reportId: mockReportId
        }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Fill out feedback form
      const vm = wrapper.vm as any
      await wrapper.setData({
        reportForm: {
          type: 'feedback',
          title: 'General Application Feedback',
          description: 'The application is intuitive and well-designed. Performance could be improved on slower devices.',
          severity: 'low',
          additionalNotes: 'Overall very positive experience. The real-time features work excellently.',
          tags: ['positive-feedback', 'performance', 'user-experience']
        }
      })

      // Submit the form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      await wrapper.vm.$nextTick()

      // Verify the API was called with feedback data
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/api/debug/report-issue',
        expect.objectContaining({
          reportDetails: {
            type: 'feedback',
            title: 'General Application Feedback',
            description: 'The application is intuitive and well-designed. Performance could be improved on slower devices.',
            severity: 'low',
            additionalNotes: 'Overall very positive experience. The real-time features work excellently.',
            tags: ['positive-feedback', 'performance', 'user-experience']
          },
          logs: expect.any(Array)
        })
      )
    })

    it('should handle network errors gracefully', async () => {
      // Mock network error
      mockedAxios.post.mockRejectedValueOnce(new Error('Network Error'))

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Fill out minimal form
      const vm = wrapper.vm as any
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Test Bug',
          description: 'Test description',
          severity: 'low'
        }
      })

      // Submit the form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      await wrapper.vm.$nextTick()

      // Verify the API was called
      expect(mockedAxios.post).toHaveBeenCalled()

      // Verify the form remains open (since submission failed)
      expect(vm.showModal).toBe(true)
      expect(vm.isLoading).toBe(false)
    })

    it('should handle server errors gracefully', async () => {
      // Mock server error response
      mockedAxios.post.mockRejectedValueOnce({
        response: {
          status: 500,
          data: {
            success: false,
            message: 'Internal server error'
          }
        }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Fill out form
      const vm = wrapper.vm as any
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Server Error Test',
          description: 'Testing server error handling',
          severity: 'medium'
        }
      })

      // Submit the form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      await wrapper.vm.$nextTick()

      // Verify the API was called
      expect(mockedAxios.post).toHaveBeenCalled()

      // Verify error handling
      expect(vm.showModal).toBe(true)
      expect(vm.isLoading).toBe(false)
    })

    it('should include client logs in the request payload', async () => {
      // Mock successful API response
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          reportId: 'logs-test-123'
        }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Fill out form
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Bug with logs',
          description: 'Testing log inclusion',
          severity: 'low'
        }
      })

      // Submit the form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      await wrapper.vm.$nextTick()

      // Verify the API was called with logs array
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/api/debug/report-issue',
        expect.objectContaining({
          reportDetails: expect.any(Object),
          logs: expect.any(Array)
        })
      )

      // Get the actual call data
      const callData = mockedAxios.post.mock.calls[0][1] as any
      expect(callData.logs).toBeDefined()
      expect(Array.isArray(callData.logs)).toBe(true)
    })

    it('should preserve special characters in data transmission', async () => {
      // Mock successful API response
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          reportId: 'special-chars-123'
        }
      })

      const specialText = 'Special chars: émojis 🐛🔧 unicode ñáéíóú quotes "test" \'test\' backslashes \\ newlines\nand\ttabs'

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Fill out form with special characters
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: specialText,
          description: specialText,
          stepsToReproduce: specialText,
          expectedBehavior: specialText,
          actualBehavior: specialText,
          severity: 'medium',
          additionalNotes: specialText,
          tags: ['special-chars', 'unicode-test']
        }
      })

      // Submit the form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      await wrapper.vm.$nextTick()

      // Verify all special characters are preserved in transmission
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/api/debug/report-issue',
        expect.objectContaining({
          reportDetails: {
            type: 'bug',
            title: specialText,
            description: specialText,
            stepsToReproduce: specialText,
            expectedBehavior: specialText,
            actualBehavior: specialText,
            severity: 'medium',
            additionalNotes: specialText,
            tags: ['special-chars', 'unicode-test']
          },
          logs: expect.any(Array)
        })
      )
    })

    it('should handle large data payloads', async () => {
      // Mock successful API response
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          reportId: 'large-data-456'
        }
      })

      const largeText = 'A'.repeat(5000) // 5KB of text

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Fill out form with large data
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Large Data Test',
          description: largeText,
          stepsToReproduce: largeText,
          expectedBehavior: largeText.substring(0, 1000),
          actualBehavior: largeText.substring(0, 1000),
          severity: 'low',
          additionalNotes: largeText.substring(0, 500)
        }
      })

      // Submit the form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      await wrapper.vm.$nextTick()

      // Verify large data is transmitted correctly
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/api/debug/report-issue',
        expect.objectContaining({
          reportDetails: expect.objectContaining({
            description: largeText,
            stepsToReproduce: largeText
          }),
          logs: expect.any(Array)
        })
      )
    })
  })

  describe('Data Validation Integration', () => {
    it('should handle backend validation errors', async () => {
      // Mock validation error response
      mockedAxios.post.mockRejectedValueOnce({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Validation error',
            errors: [
              { field: 'title', message: 'Title is required' }
            ]
          }
        }
      })

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Submit form with potentially invalid data
      const vm = wrapper.vm as any
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: '', // Empty title
          description: 'Description',
          severity: 'low'
        }
      })

      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      await wrapper.vm.$nextTick()

      // Verify the API was called
      expect(mockedAxios.post).toHaveBeenCalled()

      // Verify error handling
      expect(vm.showModal).toBe(true)
      expect(vm.isLoading).toBe(false)
    })
  })

  describe('Performance Integration', () => {
    it('should handle rapid form submissions', async () => {
      // Mock successful API response with delay
      mockedAxios.post.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            data: { success: true, reportId: 'rapid-test-123' }
          }), 100)
        )
      )

      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Fill out form
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Rapid Test',
          description: 'Testing rapid submissions',
          severity: 'low'
        }
      })

      // Attempt rapid submissions
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      
      // First submission
      const firstSubmission = submitButton.trigger('click')
      
      // Second submission immediately (should be prevented by loading state)
      await submitButton.trigger('click')

      // Wait for first submission to complete
      await firstSubmission

      // Verify only one API call was made (due to loading state prevention)
      expect(mockedAxios.post).toHaveBeenCalledTimes(1)
    })
  })
})
