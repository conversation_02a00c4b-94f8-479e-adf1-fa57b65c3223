const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkRecentReports() {
  try {
    console.log('🔍 Fetching recent debug reports...\n');
    
    const reports = await prisma.debugReport.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: { id: true, email: true, username: true }
        },
        tags: true
      }
    });

    if (reports.length === 0) {
      console.log('❌ No debug reports found in database');
      return;
    }

    console.log(`✅ Found ${reports.length} recent reports:\n`);

    reports.forEach((report, index) => {
      console.log(`--- Report ${index + 1} ---`);
      console.log(`Report ID: ${report.reportId}`);
      console.log(`Type: ${report.type}`);
      console.log(`Severity: ${report.severity}`);
      console.log(`Status: ${report.status}`);
      console.log(`Title: ${report.title}`);
      console.log(`Description: ${report.description?.substring(0, 100)}...`);
      console.log(`User: ${report.user ? `${report.user.email} (${report.user.username})` : 'No user'}`);
      console.log(`Tags: ${report.tags.map(t => t.tag).join(', ') || 'None'}`);
      console.log(`Created: ${report.createdAt}`);
      console.log(`Server Received: ${report.serverReceivedAt}`);
      console.log('---\n');
    });

  } catch (error) {
    console.error('❌ Error checking reports:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRecentReports();
