import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export type TransportType = 'websocket' | 'polling';
export type ConnectionQuality = 'excellent' | 'good' | 'poor' | 'disconnected';

export const useConnectionStore = defineStore('connection', () => {
  const isConnected = ref(false);
  const transportType = ref<TransportType>('websocket');
  const reconnectAttempts = ref(0);
  const maxReconnectAttempts = ref(5);
  const lastDisconnectReason = ref<string | null>(null);
  const isReconnecting = ref(false);

  const connectionQuality = computed<ConnectionQuality>(() => {
    if (!isConnected.value) {
      return isReconnecting.value ? 'poor' : 'disconnected';
    }
    return transportType.value === 'websocket' ? 'excellent' : 'good';
  });

  const connectionStatus = computed(() => {
    switch (connectionQuality.value) {
      case 'excellent':
        return 'Connected - Real-time updates';
      case 'good':
        return 'Connected - Limited mode (slight delays)';
      case 'poor':
        return 'Reconnecting...';
      case 'disconnected':
        return 'Disconnected - Attempting to reconnect';
      default:
        return 'Unknown connection status';
    }
  });

  const shouldShowConnectionWarning = computed(() => {
    return connectionQuality.value === 'good' || connectionQuality.value === 'poor';
  });

  const shouldShowConnectionError = computed(() => {
    return connectionQuality.value === 'disconnected';
  });

  function setConnected(connected: boolean) {
    isConnected.value = connected;
    if (connected) {
      reconnectAttempts.value = 0;
      isReconnecting.value = false;
      lastDisconnectReason.value = null;
    }
  }

  function setTransportType(transport: TransportType) {
    transportType.value = transport;
  }

  function setDisconnected(reason?: string) {
    isConnected.value = false;
    lastDisconnectReason.value = reason || null;
    isReconnecting.value = false;
  }

  function setReconnecting() {
    isReconnecting.value = true;
    reconnectAttempts.value += 1;
  }

  function resetReconnectAttempts() {
    reconnectAttempts.value = 0;
    isReconnecting.value = false;
  }

  const isMaxReconnectAttemptsReached = computed(() => {
    return reconnectAttempts.value >= maxReconnectAttempts.value;
  });

  return {
    // State
    isConnected,
    transportType,
    reconnectAttempts,
    maxReconnectAttempts,
    lastDisconnectReason,
    isReconnecting,
    
    // Computed
    connectionQuality,
    connectionStatus,
    shouldShowConnectionWarning,
    shouldShowConnectionError,
    isMaxReconnectAttemptsReached,
    
    // Actions
    setConnected,
    setTransportType,
    setDisconnected,
    setReconnecting,
    resetReconnectAttempts,
  };
});
