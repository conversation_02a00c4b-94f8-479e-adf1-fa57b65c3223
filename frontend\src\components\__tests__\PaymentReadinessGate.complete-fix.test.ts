import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import PaymentReadinessGate from '../PaymentReadinessGate.vue'

describe('Payment Persistence Complete Fix Verification', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(PaymentReadinessGate, {
      props: {
        negotiationId: 'test-negotiation',
        profileDetails: null,
        bankDetailsProvided: false
      },
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })]
      }
    })
  })

  describe('Complete saveToProfile + isDefaultForUser Fix', () => {
    it('should send both saveToProfile and isDefaultForUser when saving new details', async () => {
      const emitSpy = vi.spyOn(wrapper, 'emitEvent')
      
      // Show new details form
      await wrapper.vm.showNewDetailsForm()
      expect(wrapper.vm.formModel.saveToProfile).toBe(true)

      // Fill form
      wrapper.vm.formModel = {
        bankName: 'Test Bank',
        accountNumber: '*********',
        accountHolderName: '<PERSON>',
        saveToProfile: true
      }

      // Mock form validation
      const formRef = { validate: vi.fn().mockResolvedValue(true) }
      wrapper.vm.$refs.formRef = formRef

      // Submit form
      await wrapper.vm.savePaymentInfo()

      // Verify the payload includes both fields
      const emittedEvents = wrapper.emitted('submit')
      expect(emittedEvents).toBeDefined()
      
      if (emittedEvents && emittedEvents.length > 0) {
        const payload = emittedEvents[0][0]
        
        // This is the critical fix verification
        expect(payload.saveToProfile).toBe(true)
        expect(payload.isDefaultForUser).toBe(true) // This was missing before!
        
        console.log('✅ Payload includes both saveToProfile: true AND isDefaultForUser: true')
        console.log('📊 Payload:', JSON.stringify(payload, null, 2))
      }
    })

    it('should send isDefaultForUser: false when saveToProfile is false', async () => {
      const mockProfileDetails = {
        bankName: 'Test Bank',
        accountNumber: '*********',
        accountHolderName: 'John Doe'
      }

      await wrapper.setProps({
        profileDetails: mockProfileDetails,
        bankDetailsProvided: true
      })

      // Show edit form then cancel (sets saveToProfile to false)
      await wrapper.vm.showEditDetails()
      await wrapper.vm.cancelNewDetails()
      
      expect(wrapper.vm.formModel.saveToProfile).toBe(false)

      // Mock form validation
      const formRef = { validate: vi.fn().mockResolvedValue(true) }
      wrapper.vm.$refs.formRef = formRef

      // Submit form
      await wrapper.vm.savePaymentInfo()

      const emittedEvents = wrapper.emitted('submit')
      if (emittedEvents && emittedEvents.length > 0) {
        const payload = emittedEvents[emittedEvents.length - 1][0]
        
        expect(payload.saveToProfile).toBe(false)
        expect(payload.isDefaultForUser).toBe(false)
        
        console.log('✅ When saveToProfile is false, isDefaultForUser is also false')
      }
    })
  })

  describe('Backend Integration Expectation', () => {
    it('should demonstrate the expected backend behavior', () => {
      console.log('\n🔍 Expected Backend Behavior:')
      console.log('1. When isDefaultForUser: true → Sets PaymentReceivingInfo.isDefaultForUser = true')
      console.log('2. When isDefaultForUser: true → Clears isDefaultForUser from other records for this user')
      console.log('3. User profile query: WHERE isDefaultForUser = true → Returns the saved payment info')
      console.log('4. Frontend loads user profile → Shows payment info as "existing profile details"')
      console.log('5. User no longer sees payment form on subsequent visits ✅')
      
      expect(true).toBe(true) // This test documents expected behavior
    })
  })
})
