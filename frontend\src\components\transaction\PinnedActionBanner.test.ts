import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import { createI18n } from 'vue-i18n';
import PinnedActionBanner from '@/components/transaction/PinnedActionBanner.vue';
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore';

// Mock the icons to avoid import issues in tests
vi.mock('@vicons/tabler', () => ({
  CreditCard: 'div',
  Handshake: 'div',
  CheckCircle: 'div',
  Send: 'div',
  Star: 'div',
  ArrowDown: 'div'
}));

// Mock i18n translations
const mockTranslations = {
  en: {
    transactionalChat: {
      steps: {
        paymentInfo: 'Provide Payment Information',
        negotiation: 'Decide Who Pays First',
        confirmReceipt: 'Your Action: Confirm Receipt',
        yourTurnToPay: 'Your Action: Send Payment',
        rateExperience: 'Rate Experience'
      },
      pinnedBanner: {
        viewDetails: 'View Details',
        subtitles: {
          paymentInfo: 'Complete this step to continue',
          negotiation: 'Your decision is required',
          confirmReceipt: 'Action required - check your account',
          yourTurnToPay: 'Send payment using the details below',
          rateExperience: 'Share your experience',
          default: 'Action required'
        }
      }
    }
  }
};

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: mockTranslations
});

describe('PinnedActionBanner', () => {
  let store: ReturnType<typeof useTransactionalChatStore>;
  let wrapper: any;

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useTransactionalChatStore();
    
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  const createWrapper = () => {
    return mount(PinnedActionBanner, {
      global: {
        plugins: [i18n],
        stubs: {
          'n-button': { 
            template: '<button><slot /></button>',
            props: ['type', 'size', 'ghost', 'class']
          },
          'n-icon': { 
            template: '<span class="icon"><slot /></span>',
            props: ['component']
          }
        }
      }
    });
  };

  describe('Visibility Logic', () => {
    it('should not render when no pinned action exists', () => {
      store.pinnedAction = null;
      wrapper = createWrapper();
      
      expect(wrapper.find('[data-testid="pinned-action-banner"]').exists()).toBe(false);
    });

    it('should render when pinned action exists', () => {
      store.pinnedAction = {
        title: 'transactionalChat.steps.paymentInfo',
        cardId: 'action-123',
        actionType: 'paymentInfo',
        stepIndex: 0
      };
      
      wrapper = createWrapper();
      
      expect(wrapper.find('[data-testid="pinned-action-banner"]').exists()).toBe(true);
    });
  });

  describe('Content Display', () => {
    beforeEach(() => {
      store.pinnedAction = {
        title: 'transactionalChat.steps.paymentInfo',
        cardId: 'action-123',
        actionType: 'paymentInfo',
        stepIndex: 0
      };
      wrapper = createWrapper();
    });

    it('should display the correct title', () => {
      const titleElement = wrapper.find('.action-title');
      expect(titleElement.text()).toBe('Provide Payment Information');
    });

    it('should display the correct subtitle', () => {
      const subtitleElement = wrapper.find('.action-subtitle');
      expect(subtitleElement.text()).toBe('Complete this step to continue');
    });

    it('should show the view details button', () => {
      const button = wrapper.find('[data-testid="view-details-btn"]');
      expect(button.exists()).toBe(true);
      expect(button.text()).toContain('View Details');
    });
  });

  describe('Icon Selection', () => {
    it.each([
      ['paymentInfo', 'CreditCard'],
      ['negotiation', 'Handshake'],
      ['confirmReceipt', 'CheckCircle'],
      ['yourTurnToPay', 'Send'],
      ['rateExperience', 'Star'],
      ['unknown', 'CheckCircle'] // default
    ])('should show correct icon for %s action type', (actionType, expectedIcon) => {
      store.pinnedAction = {
        title: 'transactionalChat.steps.test',
        cardId: 'action-123',
        actionType,
        stepIndex: 0
      };
      
      wrapper = createWrapper();
      const iconContainer = wrapper.find('.action-icon');
      expect(iconContainer.exists()).toBe(true);
    });
  });

  describe('Scroll Functionality', () => {
    beforeEach(() => {
      store.pinnedAction = {
        title: 'transactionalChat.steps.paymentInfo',
        cardId: 'action-123',
        actionType: 'paymentInfo',
        stepIndex: 0
      };
      wrapper = createWrapper();
    });

    it('should call store scrollToActionCard when view details is clicked', async () => {
      const scrollSpy = vi.spyOn(store, 'scrollToActionCard');
      
      const button = wrapper.find('[data-testid="view-details-btn"]');
      await button.trigger('click');
      
      expect(scrollSpy).toHaveBeenCalledWith('action-123');
    });
  });

  describe('Subtitle Generation', () => {
    it.each([
      ['paymentInfo', 'Complete this step to continue'],
      ['negotiation', 'Your decision is required'],
      ['confirmReceipt', 'Action required - check your account'],
      ['yourTurnToPay', 'Send payment using the details below'],
      ['rateExperience', 'Share your experience'],
      ['unknown', 'Action required']
    ])('should show correct subtitle for %s action type', (actionType, expectedSubtitle) => {
      store.pinnedAction = {
        title: 'transactionalChat.steps.test',
        cardId: 'action-123',
        actionType,
        stepIndex: 0
      };
      
      wrapper = createWrapper();
      const subtitleElement = wrapper.find('.action-subtitle');
      expect(subtitleElement.text()).toBe(expectedSubtitle);
    });
  });

  describe('Responsive Behavior', () => {
    beforeEach(() => {
      store.pinnedAction = {
        title: 'transactionalChat.steps.paymentInfo',
        cardId: 'action-123',
        actionType: 'paymentInfo',
        stepIndex: 0
      };
      wrapper = createWrapper();
    });

    it('should have responsive CSS classes', () => {
      const banner = wrapper.find('.pinned-action-banner');
      expect(banner.exists()).toBe(true);
      
      const bannerContent = wrapper.find('.banner-content');
      expect(bannerContent.exists()).toBe(true);
      
      const actionInfo = wrapper.find('.action-info');
      expect(actionInfo.exists()).toBe(true);
    });

    it('should have proper ARIA attributes for accessibility', () => {
      const button = wrapper.find('[data-testid="view-details-btn"]');
      expect(button.exists()).toBe(true);
      
      // Check that the button is properly focusable
      expect(button.element.tagName.toLowerCase()).toBe('button');
    });
  });

  describe('Animation and Styling', () => {
    beforeEach(() => {
      store.pinnedAction = {
        title: 'transactionalChat.steps.paymentInfo',
        cardId: 'action-123',
        actionType: 'paymentInfo',
        stepIndex: 0
      };
      wrapper = createWrapper();
    });

    it('should have the correct CSS classes for styling', () => {
      const banner = wrapper.find('.pinned-action-banner');
      expect(banner.classes()).toContain('pinned-action-banner');
    });

    it('should have sticky positioning', () => {
      const banner = wrapper.find('.pinned-action-banner');
      const style = getComputedStyle(banner.element);
      // Note: In test environment, computed styles might not work as expected
      // This is more of a integration test that would need to run in a browser
      expect(banner.exists()).toBe(true);
    });
  });

  describe('Store Integration', () => {
    it('should react to store changes', async () => {
      // Start with no pinned action
      store.pinnedAction = null;
      wrapper = createWrapper();
      expect(wrapper.find('[data-testid="pinned-action-banner"]').exists()).toBe(false);

      // Add a pinned action
      store.pinnedAction = {
        title: 'transactionalChat.steps.negotiation',
        cardId: 'action-456',
        actionType: 'negotiation',
        stepIndex: 1
      };
      
      await wrapper.vm.$nextTick();
      expect(wrapper.find('[data-testid="pinned-action-banner"]').exists()).toBe(true);
      
      // Verify the content updated
      const titleElement = wrapper.find('.action-title');
      expect(titleElement.text()).toBe('Decide Who Pays First');
    });

    it('should clear when pinned action is removed', async () => {
      // Start with a pinned action
      store.pinnedAction = {
        title: 'transactionalChat.steps.paymentInfo',
        cardId: 'action-123',
        actionType: 'paymentInfo',
        stepIndex: 0
      };
      wrapper = createWrapper();
      expect(wrapper.find('[data-testid="pinned-action-banner"]').exists()).toBe(true);

      // Remove the pinned action
      store.pinnedAction = null;
      await wrapper.vm.$nextTick();
      expect(wrapper.find('[data-testid="pinned-action-banner"]').exists()).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing translations gracefully', () => {
      store.pinnedAction = {
        title: 'nonexistent.translation.key',
        cardId: 'action-123',
        actionType: 'paymentInfo',
        stepIndex: 0
      };
      
      // This should not throw an error
      expect(() => {
        wrapper = createWrapper();
      }).not.toThrow();
    });

    it('should handle unknown action types gracefully', () => {
      store.pinnedAction = {
        title: 'transactionalChat.steps.paymentInfo',
        cardId: 'action-123',
        actionType: 'unknownActionType',
        stepIndex: 0
      };
      
      wrapper = createWrapper();
      const subtitleElement = wrapper.find('.action-subtitle');
      expect(subtitleElement.text()).toBe('Action required');
    });
  });
});
