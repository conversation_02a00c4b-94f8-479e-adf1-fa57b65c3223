/**
 * Simple Playwright Test to Demonstrate MCP Integration
 * for MUNygo SmartNegotiationSection Component
 */

const { test, expect } = require('@playwright/test');

test.describe('MUNygo App with Playwright MCP', () => {
  test('should load the application and test basic functionality', async ({ page }) => {
    console.log('🚀 Starting MCP integration test for MUNygo...');
    
    // Navigate to your running application
    await page.goto('http://localhost:5173');
    console.log('✅ Navigated to http://localhost:5173');
    
    // Set mobile viewport (your mobile-first approach)
    await page.setViewportSize({ width: 375, height: 667 });
    console.log('📱 Set mobile viewport: 375x667');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    console.log('⏳ Page loaded completely');
    
    // Take a screenshot
    await page.screenshot({ 
      path: 'test-results/munygo-homepage.png',
      fullPage: true 
    });
    console.log('📸 Screenshot saved: munygo-homepage.png');
    
    // Test for data-testid elements (as per your coding guidelines)
    const testIds = await page.locator('[data-testid]').count();
    console.log(`🔍 Found ${testIds} elements with data-testid attributes`);
    
    // Look for your SmartNegotiationSection if it exists
    const negotiationSection = page.locator('[data-testid="smart-negotiation-section"]');
    const hasNegotiationSection = await negotiationSection.isVisible();
    
    if (hasNegotiationSection) {
      console.log('✅ SmartNegotiationSection found!');
      
      // Test loading state
      const loadingState = page.locator('.loading-state');
      if (await loadingState.isVisible()) {
        console.log('⏳ Loading state detected');
        await page.waitForTimeout(2000); // Wait for loading to complete
      }
      
      // Take screenshot of negotiation section
      await negotiationSection.screenshot({ 
        path: 'test-results/negotiation-section.png' 
      });
      console.log('📸 Negotiation section screenshot saved');
      
      // Test buttons with data-testid
      const buttons = negotiationSection.locator('[data-testid*="btn"]');
      const buttonCount = await buttons.count();
      console.log(`🔘 Found ${buttonCount} buttons in negotiation section`);
      
    } else {
      console.log('ℹ️  SmartNegotiationSection not visible on current page');
    }
    
    // Test i18n elements
    const i18nElements = await page.locator('[class*="i18n"], [data-i18n]').count();
    console.log(`🌐 Found ${i18nElements} i18n elements`);
    
    // Check for Socket.IO connections
    const requests = [];
    page.on('request', request => {
      if (request.url().includes('socket.io') || request.url().includes('websocket')) {
        requests.push(request.url());
      }
    });
    
    await page.waitForTimeout(3000);
    if (requests.length > 0) {
      console.log('🔌 Socket.IO connections detected:', requests);
    } else {
      console.log('ℹ️  No Socket.IO connections found');
    }
    
    // Test responsive design breakpoints
    const breakpoints = [
      { name: 'Small Mobile', width: 320, height: 568 },
      { name: 'Mobile', width: 375, height: 667 },
      { name: 'Tablet', width: 768, height: 1024 }
    ];
    
    for (const bp of breakpoints) {
      await page.setViewportSize({ width: bp.width, height: bp.height });
      await page.screenshot({ 
        path: `test-results/responsive-${bp.width}x${bp.height}.png`,
        fullPage: true 
      });
      console.log(`📱 ${bp.name} screenshot saved (${bp.width}x${bp.height})`);
    }
    
    console.log('✅ MCP integration test completed successfully!');
  });
  
  test('should test accessibility requirements', async ({ page }) => {
    await page.goto('http://localhost:5173');
    
    // Test minimum touch target size (44px as per your guidelines)
    const interactiveElements = page.locator('button, a, [data-testid*="btn"]');
    const count = await interactiveElements.count();
    
    let touchTargetIssues = 0;
    
    for (let i = 0; i < Math.min(count, 10); i++) {
      const element = interactiveElements.nth(i);
      const box = await element.boundingBox();
      
      if (box) {
        if (box.width < 44 || box.height < 44) {
          touchTargetIssues++;
          console.warn(`⚠️  Touch target ${i} too small: ${box.width}x${box.height}px`);
        }
      }
    }
    
    console.log(`🎯 Touch target analysis: ${touchTargetIssues} issues found out of ${Math.min(count, 10)} checked`);
    
    // Test for skeleton loading screens (required by your guidelines)
    const skeletonElements = await page.locator('[data-testid*="skeleton"]').count();
    console.log(`💀 Found ${skeletonElements} skeleton loading elements`);
  });
});
