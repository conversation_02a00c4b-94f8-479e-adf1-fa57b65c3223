// filepath: c:\Code\MUNygo\frontend\src\components\__tests__\TestSetup.spec.ts
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import TestSetup from '../TestSetup.vue'; // Import the component
import { NButton, NSpace } from 'naive-ui'; // Import Naive UI components used

// You might need to configure Naive UI globally for tests or mock components
// For simplicity here, we'll assume basic rendering works or stub them.
// A better approach involves setting up a test environment that handles UI libraries.

describe('TestSetup.vue', () => {
  it('renders the heading', () => {
    const wrapper = mount(TestSetup, {
      global: {
        // Provide Naive UI components to avoid warnings/errors during mounting
        // You can use stubs for simpler tests: components: { NButton: true, NSpace: true }
        components: { NButton, NSpace }
      }
    });
    // Check if an h1 element exists
    expect(wrapper.find('h1').exists()).toBe(true);
    // Check if the h1 element contains the expected text
    expect(wrapper.find('h1').text()).toBe('Setup Test Page');
  });

  it('renders the Naive UI button', () => {
    const wrapper = mount(TestSetup, {
      global: {
        components: { NButton, NSpace }
      }
    });
    // Find the NButton component instance
    const button = wrapper.findComponent(NButton);
    expect(button.exists()).toBe(true);
    // Check props or text if needed (might require deeper knowledge of Naive UI rendering in tests)
    expect(button.text()).toBe('Naive UI Button');
  });
});