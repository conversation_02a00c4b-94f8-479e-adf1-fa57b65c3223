<template>
  <div 
    ref="containerRef"
    class="smart-action-card-container"
    :class="{
      'size-compact': currentSize === 'compact',
      'size-medium': currentSize === 'medium',
      'size-large': currentSize === 'large',
      'is-transitioning': isTransitioning,
      'is-focused': isFocused,
      'viewport-constrained': isViewportConstrained
    }"
    @transitionend="handleTransitionEnd"
  >
    <!-- Action Card Content Slot -->
    <div class="action-content-wrapper">
      <slot />
    </div>
    
    <!-- Dynamic Resize Handler -->
    <div 
      v-if="showResizeHandle && isLarge" 
      class="resize-handle"
      @mousedown="startResize"
    />
    
    <!-- Focus Indicator -->
    <div v-if="isFocused" class="focus-ring" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'

interface Props {
  size?: 'compact' | 'medium' | 'large' | 'auto'
  allowResize?: boolean
  focusOnExpand?: boolean
  maxHeight?: number
  adaptToViewport?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'auto',
  allowResize: false,
  focusOnExpand: true,
  maxHeight: 400,
  adaptToViewport: true
})

const emit = defineEmits<{
  sizeChanged: [size: 'compact' | 'medium' | 'large']
  focused: []
  resized: [height: number]
}>()

// Refs
const containerRef = ref<HTMLElement>()
const isTransitioning = ref(false)
const isFocused = ref(false)
const isViewportConstrained = ref(false)

// Size detection logic
const currentSize = computed(() => {
  if (props.size !== 'auto') return props.size
  
  const height = containerRef.value?.scrollHeight || 0
  if (height <= 80) return 'compact'
  if (height <= 200) return 'medium'
  return 'large'
})

const isLarge = computed(() => currentSize.value === 'large')
const showResizeHandle = computed(() => props.allowResize && isLarge.value)

// Smart positioning based on viewport
const checkViewportConstraints = () => {
  if (!props.adaptToViewport || !containerRef.value) return
  
  const rect = containerRef.value.getBoundingClientRect()
  const viewportHeight = window.innerHeight
  const availableHeight = viewportHeight - rect.top - 100 // 100px buffer for action bar
  
  isViewportConstrained.value = rect.height > availableHeight
  
  if (isViewportConstrained.value) {
    // Trigger smart repositioning
    scrollIntoOptimalView()
  }
}

const scrollIntoOptimalView = () => {
  if (!containerRef.value) return
  
  // Find scrollable parent (unified feed)
  const scrollableParent = findScrollableParent(containerRef.value)
  
  if (scrollableParent) {
    const containerRect = containerRef.value.getBoundingClientRect()
    const parentRect = scrollableParent.getBoundingClientRect()
    
    // Calculate optimal position (top 20% of scrollable parent's visible area)
    const targetTop = scrollableParent.scrollTop + (scrollableParent.clientHeight * 0.2)
    const scrollAdjustment = containerRect.top - parentRect.top + scrollableParent.scrollTop - targetTop
    
    scrollableParent.scrollTo({
      top: scrollableParent.scrollTop + scrollAdjustment,
      behavior: 'smooth'
    })
  }
}

const findScrollableParent = (element: HTMLElement): HTMLElement | null => {
  let parent = element.parentElement
  
  while (parent) {
    const style = window.getComputedStyle(parent)
    if (parent.classList.contains('unified-feed') || 
        style.overflowY === 'auto' || 
        style.overflowY === 'scroll') {
      return parent
    }
    parent = parent.parentElement
  }
  
  return document.documentElement
}

// Transition handling
const handleTransitionEnd = () => {
  isTransitioning.value = false
}

// Focus management
const focusCard = () => {
  if (!props.focusOnExpand) return
  
  isFocused.value = true
  containerRef.value?.focus()
  
  // Auto-unfocus after interaction
  setTimeout(() => {
    isFocused.value = false
  }, 2000)
}

// Resize handling for large cards
const startResize = (event: MouseEvent) => {
  if (!props.allowResize) return
  
  const startY = event.clientY
  const startHeight = containerRef.value?.offsetHeight || 0
  
  const handleMouseMove = (e: MouseEvent) => {
    const deltaY = e.clientY - startY
    const newHeight = Math.max(200, Math.min(props.maxHeight, startHeight + deltaY))
    
    if (containerRef.value) {
      containerRef.value.style.height = `${newHeight}px`
      emit('resized', newHeight)
    }
  }
  
  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// Watchers
watch(() => currentSize.value, (newSize, oldSize) => {
  if (newSize !== oldSize) {
    isTransitioning.value = true
    emit('sizeChanged', newSize)
    
    if (newSize === 'large') {
      nextTick(() => {
        checkViewportConstraints()
        focusCard()
      })
    }
  }
})

// Lifecycle
onMounted(() => {
  window.addEventListener('resize', checkViewportConstraints)
  checkViewportConstraints()
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', checkViewportConstraints)
})
</script>

<style scoped>
.smart-action-card-container {
  /* Base styling */
  position: relative;
  border-radius: 12px;
  background: var(--tc-bg-card);
  border: 1px solid var(--tc-border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  
  /* Focus management */
  outline: none;
  margin-bottom: 16px;
}

/* Size-based styling */
.smart-action-card-container.size-compact {
  min-height: 60px;
  max-height: 120px;
}

.smart-action-card-container.size-medium {
  min-height: 120px;
  max-height: 300px;
}

.smart-action-card-container.size-large {
  min-height: 300px;
  max-height: 500px;
  /* Add extra margin for large cards */
  margin-bottom: 24px;
}

/* State-based styling */
.smart-action-card-container.is-transitioning {
  pointer-events: none;
}

.smart-action-card-container.is-focused {
  border-color: var(--tc-primary);
  box-shadow: 0 0 0 3px var(--tc-primary-light);
  z-index: 10;
}

.smart-action-card-container.viewport-constrained {
  /* Visual indicator for constrained cards */
  border-color: var(--tc-warning);
}

/* Content wrapper */
.action-content-wrapper {
  padding: 16px;
  height: 100%;
  overflow: hidden;
}

.size-large .action-content-wrapper {
  padding: 20px;
  overflow-y: auto;
}

/* Focus ring */
.focus-ring {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid var(--tc-primary);
  border-radius: 14px;
  pointer-events: none;
  animation: focusPulse 2s ease-in-out;
}

@keyframes focusPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Resize handle */
.resize-handle {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 12px;
  background: var(--tc-border-medium);
  border-radius: 6px 6px 0 0;
  cursor: ns-resize;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.resize-handle:hover {
  opacity: 1;
  background: var(--tc-primary);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .smart-action-card-container.size-large {
    max-height: 70vh;
  }
  
  .action-content-wrapper {
    padding: 12px;
  }
  
  .resize-handle {
    display: none; /* No resize on mobile */
  }
}

/* Dark mode */
[data-theme="dark"] .smart-action-card-container {
  background: var(--tc-bg-card-dark);
  border-color: var(--tc-border-dark);
}

/* RTL support */
[dir="rtl"] .resize-handle {
  left: auto;
  right: 50%;
  transform: translateX(50%);
}
</style>
