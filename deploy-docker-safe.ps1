# Safe Docker Database Migration Script (PowerShell)
# This script ensures <PERSON><PERSON> uses the correct database schema without losing data

Write-Host "🔄 Safe Docker Database Migration and Rebuild" -ForegroundColor Cyan
Write-Host "==============================================" -ForegroundColor Cyan

# Step 1: Stop Docker containers
Write-Host "📛 Stopping Docker containers..." -ForegroundColor Yellow
docker-compose down

# Step 2: Clean up Docker images (force rebuild without cache)
Write-Host "🧹 Cleaning up Docker images..." -ForegroundColor Yellow
docker-compose build --no-cache --pull

# Step 3: Start only the PostgreSQL database first
Write-Host "🗄️ Starting PostgreSQL database..." -ForegroundColor Green
docker-compose up -d postgres

# Wait for PostgreSQL to be ready
Write-Host "⏳ Waiting for PostgreSQL to be ready..." -ForegroundColor Yellow
do {
    Start-Sleep -Seconds 2
    $result = docker-compose exec postgres pg_isready -U munygo_user -d munygo_db 2>$null
    Write-Host "Waiting for PostgreSQL..." -ForegroundColor Gray
} while ($LASTEXITCODE -ne 0)

Write-Host "✅ PostgreSQL is ready!" -ForegroundColor Green

# Step 4: Run database migrations in Docker
Write-Host "📊 Running database migrations..." -ForegroundColor Blue
docker-compose run --rm backend npx prisma migrate deploy

# Step 5: Generate Prisma client
Write-Host "🔧 Generating Prisma client..." -ForegroundColor Blue
docker-compose run --rm backend npx prisma generate

# Step 6: Start backend service
Write-Host "🚀 Starting backend service..." -ForegroundColor Green
docker-compose up -d backend

# Wait for backend to be healthy
Write-Host "⏳ Waiting for backend to be healthy..." -ForegroundColor Yellow
do {
    Start-Sleep -Seconds 5
    $status = docker-compose ps backend | Select-String "healthy"
    Write-Host "Waiting for backend to be healthy..." -ForegroundColor Gray
} while (-not $status)

Write-Host "✅ Backend is healthy!" -ForegroundColor Green

# Step 7: Start frontend service
Write-Host "🎨 Starting frontend service..." -ForegroundColor Green
docker-compose up -d frontend

# Step 8: Verify all services are running
Write-Host "🔍 Verifying all services..." -ForegroundColor Blue
docker-compose ps

Write-Host ""
Write-Host "🎉 Docker deployment complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Services Status:" -ForegroundColor Cyan

$postgresStatus = if (docker-compose ps postgres | Select-String "Up") { "✅ Running" } else { "❌ Not running" }
$backendStatus = if (docker-compose ps backend | Select-String "Up") { "✅ Running" } else { "❌ Not running" }
$frontendStatus = if (docker-compose ps frontend | Select-String "Up") { "✅ Running" } else { "❌ Not running" }

Write-Host "- PostgreSQL: $postgresStatus" -ForegroundColor White
Write-Host "- Backend: $backendStatus" -ForegroundColor White
Write-Host "- Frontend: $frontendStatus" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Access the application:" -ForegroundColor Cyan
Write-Host "- Frontend: http://localhost:8080" -ForegroundColor White
Write-Host "- Backend API: http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Useful commands:" -ForegroundColor Cyan
Write-Host "- View logs: docker-compose logs -f" -ForegroundColor White
Write-Host "- Access backend: docker-compose exec backend bash" -ForegroundColor White
Write-Host "- Access database: docker-compose exec postgres psql -U munygo_user -d munygo_db" -ForegroundColor White
