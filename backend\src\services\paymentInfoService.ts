import { PrismaClient, PaymentReceivingInfo } from '@prisma/client';

export class PaymentInfoService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get all payment info for a user
   */
  async getUserPaymentInfo(userId: string): Promise<PaymentReceivingInfo[]> {
    console.log(`[PaymentInfoService] Getting payment info for user ${userId}`);

    return await this.prisma.paymentReceivingInfo.findMany({
      where: { userId },
      orderBy: [
        { isDefaultForUser: 'desc' },
        { createdAt: 'desc' }
      ]
    });
  }

  /**
   * Get default payment info for a user
   */
  async getDefaultPaymentInfo(userId: string): Promise<PaymentReceivingInfo | null> {
    console.log(`[PaymentInfoService] Getting default payment info for user ${userId}`);

    return await this.prisma.paymentReceivingInfo.findFirst({
      where: { 
        userId,
        isDefaultForUser: true
      }
    });
  }

  /**
   * Create new payment info for a user
   */
  async createPaymentInfo(
    userId: string,
    bankName: string,
    accountNumber: string,
    accountHolderName: string,
    isDefault: boolean = false
  ): Promise<PaymentReceivingInfo> {
    console.log(`[PaymentInfoService] Creating payment info for user ${userId}`);

    // Validate input parameters
    this.validatePaymentInfoInputs(bankName, accountNumber, accountHolderName);

    // Use transaction to ensure atomicity when setting default
    return await this.prisma.$transaction(async (prisma) => {
      // If this is set as default, unset all other defaults for this user
      if (isDefault) {
        await prisma.paymentReceivingInfo.updateMany({
          where: { userId },
          data: { isDefaultForUser: false }
        });
      }

      return await prisma.paymentReceivingInfo.create({
        data: {
          userId,
          bankName,
          accountNumber,
          accountHolderName,
          isDefaultForUser: isDefault
        }
      });
    });
  }

  /**
   * Update payment info
   */
  async updatePaymentInfo(
    paymentInfoId: string,
    userId: string,
    updates: {
      bankName?: string;
      accountNumber?: string;
      accountHolderName?: string;
      isDefaultForUser?: boolean;
    }
  ): Promise<PaymentReceivingInfo> {
    console.log(`[PaymentInfoService] Updating payment info ${paymentInfoId} for user ${userId}`);

    // Validate input parameters if they are being updated
    if (updates.bankName !== undefined || updates.accountNumber !== undefined || updates.accountHolderName !== undefined) {
      // Get existing values for validation if partial update
      const existing = await this.prisma.paymentReceivingInfo.findFirst({
        where: { id: paymentInfoId, userId }
      });

      if (!existing) {
        throw new Error('Payment info not found or access denied');
      }

      // Use provided values or fall back to existing ones for validation
      const bankName = updates.bankName !== undefined ? updates.bankName : existing.bankName;
      const accountNumber = updates.accountNumber !== undefined ? updates.accountNumber : existing.accountNumber;
      const accountHolderName = updates.accountHolderName !== undefined ? updates.accountHolderName : existing.accountHolderName;

      this.validatePaymentInfoInputs(bankName, accountNumber, accountHolderName);
    }

    // Verify ownership (if not already done above)
    const existing = await this.prisma.paymentReceivingInfo.findFirst({
      where: { id: paymentInfoId, userId }
    });

    if (!existing) {
      throw new Error('Payment info not found or access denied');
    }

    // If setting as default, unset all other defaults for this user
    if (updates.isDefaultForUser) {
      return await this.prisma.$transaction(async (prisma) => {
        await prisma.paymentReceivingInfo.updateMany({
          where: { userId },
          data: { isDefaultForUser: false }
        });

        return await prisma.paymentReceivingInfo.update({
          where: { id: paymentInfoId },
          data: updates
        });
      });
    }

    return await this.prisma.paymentReceivingInfo.update({
      where: { id: paymentInfoId },
      data: updates
    });
  }

  /**
   * Delete payment info
   */
  async deletePaymentInfo(paymentInfoId: string, userId: string): Promise<void> {
    console.log(`[PaymentInfoService] Deleting payment info ${paymentInfoId} for user ${userId}`);

    // Verify ownership
    const existing = await this.prisma.paymentReceivingInfo.findFirst({
      where: { id: paymentInfoId, userId }
    });

    if (!existing) {
      throw new Error('Payment info not found or access denied');
    }

    await this.prisma.paymentReceivingInfo.delete({
      where: { id: paymentInfoId }
    });
  }

  /**
   * Set default payment info for a user
   */
  async setDefaultPaymentInfo(paymentInfoId: string, userId: string): Promise<PaymentReceivingInfo> {
    console.log(`[PaymentInfoService] Setting default payment info ${paymentInfoId} for user ${userId}`);

    // Verify ownership
    const existing = await this.prisma.paymentReceivingInfo.findFirst({
      where: { id: paymentInfoId, userId }
    });

    if (!existing) {
      throw new Error('Payment info not found or access denied');
    }

    // Use transaction to ensure atomicity when changing default
    return await this.prisma.$transaction(async (prisma) => {
      // Unset all other defaults for this user
      await prisma.paymentReceivingInfo.updateMany({
        where: { userId },
        data: { isDefaultForUser: false }
      });

      // Set this one as default
      return await prisma.paymentReceivingInfo.update({
        where: { id: paymentInfoId },
        data: { isDefaultForUser: true }
      });
    });
  }

  /**
   * Validate payment info input parameters
   */
  private validatePaymentInfoInputs(
    bankName: string,
    accountNumber: string,
    accountHolderName: string
  ): void {
    // Validate bank name
    if (!bankName || typeof bankName !== 'string' || bankName.trim().length === 0) {
      throw new Error('Bank name is required and must be a non-empty string');
    }
    if (bankName.trim().length < 2 || bankName.trim().length > 100) {
      throw new Error('Bank name must be between 2 and 100 characters');
    }

    // Validate account number
    if (!accountNumber || typeof accountNumber !== 'string' || accountNumber.trim().length === 0) {
      throw new Error('Account number is required and must be a non-empty string');
    }
    // Remove spaces and check if it contains only digits
    const cleanAccountNumber = accountNumber.replace(/\s/g, '');
    if (!/^\d+$/.test(cleanAccountNumber)) {
      throw new Error('Account number must contain only digits and spaces');
    }
    if (cleanAccountNumber.length < 8 || cleanAccountNumber.length > 30) {
      throw new Error('Account number must be between 8 and 30 digits');
    }

    // Validate account holder name
    if (!accountHolderName || typeof accountHolderName !== 'string' || accountHolderName.trim().length === 0) {
      throw new Error('Account holder name is required and must be a non-empty string');
    }
    if (accountHolderName.trim().length < 2 || accountHolderName.trim().length > 100) {
      throw new Error('Account holder name must be between 2 and 100 characters');
    }
    // Check for valid characters (letters, spaces, dots, apostrophes, hyphens)
    if (!/^[a-zA-Z\u0600-\u06FF\s.\'-]+$/.test(accountHolderName.trim())) {
      throw new Error('Account holder name must contain only letters, spaces, dots, apostrophes, and hyphens');
    }
  }
}
