import { PrismaClient, TransactionStatus } from '@prisma/client';
import { TransactionService } from '../services/transactionService';
import { NotificationService } from '../services/notificationService';
import { ChatService } from '../services/chatService';
import { Server } from 'socket.io';

const prisma = new PrismaClient();

async function testTransactionConfirmFlow() {
  console.log('🧪 Testing Transaction Confirm Receipt Flow...');
  
  try {
    // Mock Socket.IO server
    const mockIO = {
      to: () => ({ emit: () => {} })    } as any as Server;
    
    // Create notification service
    const notificationService = new NotificationService(mockIO);
    
    // Create chat service  
    const chatService = new ChatService(mockIO);
    
    // Create transaction service
    const transactionService = new TransactionService(mockIO, notificationService, chatService);
    
    // Find an existing transaction in AWAITING_SECOND_PAYER_CONFIRMATION status
    const transactionAwaitingSecondConfirm = await prisma.transaction.findFirst({
      where: {
        status: TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION
      },
      include: {
        currencyAProvider: true,
        currencyBProvider: true
      }
    });
    
    if (transactionAwaitingSecondConfirm) {
      console.log(`📋 Found transaction ${transactionAwaitingSecondConfirm.id} in AWAITING_SECOND_PAYER_CONFIRMATION`);
      console.log(`   - Currency A Provider: ${transactionAwaitingSecondConfirm.currencyAProvider?.username}`);
      console.log(`   - Currency B Provider: ${transactionAwaitingSecondConfirm.currencyBProvider?.username}`);
      console.log(`   - Agreed First Payer: ${transactionAwaitingSecondConfirm.agreedFirstPayerId}`);
      
      const payer1Id = transactionAwaitingSecondConfirm.agreedFirstPayerId!;
      const payer2Id = payer1Id === transactionAwaitingSecondConfirm.currencyAProviderId 
        ? transactionAwaitingSecondConfirm.currencyBProviderId 
        : transactionAwaitingSecondConfirm.currencyAProviderId;
      
      console.log(`   - Payer 1 (first payer): ${payer1Id}`);
      console.log(`   - Payer 2 (second payer): ${payer2Id}`);
      
      try {
        console.log(`\n✅ Testing: Payer 2 (${payer2Id}) confirming receipt of first payment...`);
        const result = await transactionService.confirmReceipt(transactionAwaitingSecondConfirm.id, payer2Id);
        console.log(`✅ SUCCESS! Transaction status changed to: ${result.status}`);
        console.log(`   - Expected: AWAITING_SECOND_PAYER_PAYMENT`);
        console.log(`   - Actual: ${result.status}`);
        
        if (result.status === TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT) {
          console.log('🎉 Status transition is correct!');
        } else {
          console.log('❌ Status transition is incorrect!');
        }
      } catch (error: any) {
        console.log(`❌ FAILED: ${error.message}`);
      }
    }
    
    // Find an existing transaction in AWAITING_FIRST_PAYER_CONFIRMATION status
    const transactionAwaitingFirstConfirm = await prisma.transaction.findFirst({
      where: {
        status: TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION
      },
      include: {
        currencyAProvider: true,
        currencyBProvider: true
      }
    });
    
    if (transactionAwaitingFirstConfirm) {
      console.log(`\n📋 Found transaction ${transactionAwaitingFirstConfirm.id} in AWAITING_FIRST_PAYER_CONFIRMATION`);
      console.log(`   - Currency A Provider: ${transactionAwaitingFirstConfirm.currencyAProvider?.username}`);
      console.log(`   - Currency B Provider: ${transactionAwaitingFirstConfirm.currencyBProvider?.username}`);
      console.log(`   - Agreed First Payer: ${transactionAwaitingFirstConfirm.agreedFirstPayerId}`);
      
      const payer1Id = transactionAwaitingFirstConfirm.agreedFirstPayerId!;
      const payer2Id = payer1Id === transactionAwaitingFirstConfirm.currencyAProviderId 
        ? transactionAwaitingFirstConfirm.currencyBProviderId 
        : transactionAwaitingFirstConfirm.currencyAProviderId;
      
      console.log(`   - Payer 1 (first payer): ${payer1Id}`);
      console.log(`   - Payer 2 (second payer): ${payer2Id}`);
      
      try {
        console.log(`\n✅ Testing: Payer 1 (${payer1Id}) confirming receipt of second payment...`);
        const result = await transactionService.confirmReceipt(transactionAwaitingFirstConfirm.id, payer1Id);
        console.log(`✅ SUCCESS! Transaction status changed to: ${result.status}`);
        console.log(`   - Expected: COMPLETED`);
        console.log(`   - Actual: ${result.status}`);
        
        if (result.status === TransactionStatus.COMPLETED) {
          console.log('🎉 Status transition is correct!');
        } else {
          console.log('❌ Status transition is incorrect!');
        }
      } catch (error: any) {
        console.log(`❌ FAILED: ${error.message}`);
      }
    }
    
    if (!transactionAwaitingSecondConfirm && !transactionAwaitingFirstConfirm) {
      console.log('ℹ️  No transactions found in confirmation states. Creating test scenario...');
      
      // Create a test transaction in AWAITING_SECOND_PAYER_CONFIRMATION
      const testUsers = await prisma.user.findMany({ take: 2 });
      if (testUsers.length >= 2) {
        const testTransaction = await prisma.transaction.create({
          data: {
            chatSessionId: 'test-chat-session',
            offerId: 'test-offer-id',
            currencyA: 'USD',
            amountA: 100,
            currencyAProviderId: testUsers[0].id,
            currencyB: 'EUR',
            amountB: 85,
            currencyBProviderId: testUsers[1].id,
            status: TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION,
            agreedFirstPayerId: testUsers[0].id,
            paymentDeclaredAtPayer1: new Date(),
          }
        });
        
        console.log(`📋 Created test transaction ${testTransaction.id} in AWAITING_SECOND_PAYER_CONFIRMATION`);
        
        try {
          console.log(`\n✅ Testing: Payer 2 (${testUsers[1].id}) confirming receipt of first payment...`);
          const result = await transactionService.confirmReceipt(testTransaction.id, testUsers[1].id);
          console.log(`✅ SUCCESS! Transaction status changed to: ${result.status}`);
          
          if (result.status === TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT) {
            console.log('🎉 Status transition is correct!');
          } else {
            console.log('❌ Status transition is incorrect!');
          }
          
          // Clean up test transaction
          await prisma.transaction.delete({ where: { id: testTransaction.id } });
          console.log('🧹 Cleaned up test transaction');
          
        } catch (error: any) {
          console.log(`❌ FAILED: ${error.message}`);
          // Clean up test transaction even on failure
          await prisma.transaction.delete({ where: { id: testTransaction.id } });
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testTransactionConfirmFlow().catch(console.error);
