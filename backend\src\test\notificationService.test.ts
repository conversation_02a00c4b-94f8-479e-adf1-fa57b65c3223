import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NotificationService, CreateNotificationInput } from '../services/notificationService';

// Use vi.hoisted to ensure these are available when the mock is created
const mockPrismaNotificationCreate = vi.hoisted(() => vi.fn());
const mockPrismaNotificationFindMany = vi.hoisted(() => vi.fn());
const mockPrismaNotificationFindUnique = vi.hoisted(() => vi.fn());
const mockPrismaNotificationUpdate = vi.hoisted(() => vi.fn());
const mockPrismaNotificationUpdateMany = vi.hoisted(() => vi.fn());

vi.mock('@prisma/client', () => {
  return {
    PrismaClient: vi.fn().mockImplementation(() => ({
      notification: {
        create: mockPrismaNotificationCreate,
        findMany: mockPrismaNotificationFindMany,
        findUnique: mockPrismaNotificationFindUnique,
        update: mockPrismaNotificationUpdate,
        updateMany: mockPrismaNotificationUpdateMany,
      },
    })),
    NotificationType: {
      NEW_INTEREST_ON_YOUR_OFFER: 'NEW_INTEREST_ON_YOUR_OFFER',
      YOUR_INTEREST_ACCEPTED: 'YOUR_INTEREST_ACCEPTED',
      YOUR_INTEREST_DECLINED: 'YOUR_INTEREST_DECLINED',
    },
  };
});

// Mock Socket.IO
const mockSocket = {
  to: vi.fn().mockReturnThis(),
  emit: vi.fn(),
};

describe('NotificationService', () => {
  let notificationService: NotificationService;
  const mockUserId = 'user123';
  const mockNotificationId = 'notif123';

  beforeEach(() => {
    vi.clearAllMocks();
    notificationService = new NotificationService(mockSocket);
  });
  describe('createNotification', () => {
    const validInput: CreateNotificationInput = {
      userId: mockUserId,
      type: 'NEW_INTEREST_ON_YOUR_OFFER' as any,
      message: 'Someone showed interest in your offer',
      relatedEntityType: 'OFFER',
      relatedEntityId: 'offer123',
      actorId: 'actor123',
      actorUsername: 'testuser',
      data: JSON.stringify({
        interestId: 'interest123',
        offerTitle: 'SELL 100 USD-CAD',
      }),
    };

    const mockCreatedNotification = {
      id: mockNotificationId,
      userId: validInput.userId,
      type: validInput.type,
      message: validInput.message,
      relatedEntityType: validInput.relatedEntityType,
      relatedEntityId: validInput.relatedEntityId,
      actorId: validInput.actorId,
      actorUsername: validInput.actorUsername,
      data: JSON.stringify(validInput.data),
      isRead: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should create notification successfully with data object', async () => {
      mockPrismaNotificationCreate.mockResolvedValue(mockCreatedNotification);

      const result = await notificationService.createNotification(validInput);      expect(mockPrismaNotificationCreate).toHaveBeenCalledWith({
        data: {
          userId: validInput.userId,
          type: validInput.type,
          message: validInput.message,
          relatedEntityType: validInput.relatedEntityType,
          relatedEntityId: validInput.relatedEntityId,
          actorId: validInput.actorId,
          actorUsername: validInput.actorUsername,
          data: validInput.data, // Pass through as-is (already stringified by caller)
        },
      });

      expect(result).toEqual(mockCreatedNotification);
    });

    it('should create notification with null data when data is not provided', async () => {
      const inputWithoutData = { ...validInput };
      delete inputWithoutData.data;

      const expectedNotification = {
        ...mockCreatedNotification,
        data: null,
      };

      mockPrismaNotificationCreate.mockResolvedValue(expectedNotification);

      await notificationService.createNotification(inputWithoutData);      expect(mockPrismaNotificationCreate).toHaveBeenCalledWith({
        data: {
          userId: validInput.userId,
          type: validInput.type,
          message: validInput.message,
          relatedEntityType: validInput.relatedEntityType,
          relatedEntityId: validInput.relatedEntityId,
          actorId: validInput.actorId,
          actorUsername: validInput.actorUsername,
          data: null,
        },
      });
    });

    it('should emit socket event when io is available', async () => {
      mockPrismaNotificationCreate.mockResolvedValue(mockCreatedNotification);

      await notificationService.createNotification(validInput);

      expect(mockSocket.to).toHaveBeenCalledWith(mockUserId);
      expect(mockSocket.emit).toHaveBeenCalledWith('NEW_NOTIFICATION', mockCreatedNotification);
    });

    it('should not emit socket event when io is not available', async () => {
      const serviceWithoutSocket = new NotificationService();
      mockPrismaNotificationCreate.mockResolvedValue(mockCreatedNotification);

      await serviceWithoutSocket.createNotification(validInput);

      expect(mockSocket.to).not.toHaveBeenCalled();
      expect(mockSocket.emit).not.toHaveBeenCalled();
    });

    it('should throw error when database operation fails', async () => {
      const dbError = new Error('Database connection failed');
      mockPrismaNotificationCreate.mockRejectedValue(dbError);

      await expect(notificationService.createNotification(validInput))
        .rejects.toThrow('Failed to create notification. Details: Database connection failed');
    });    it('should properly stringify complex data objects', async () => {
      const complexData = {
        interestId: 'interest123',
        offer: {
          id: 'offer123',
          type: 'SELL',
          amount: 100.50,
          currency: 'USD',
        },
        user: {
          id: 'user123',
          username: 'testuser',
          reputation: 5,
        },
        metadata: {
          timestamp: '2025-05-27T10:00:00Z',
          source: 'web',
        },
      };      const inputWithComplexData = {
        ...validInput,
        data: JSON.stringify(complexData), // Pass stringified data like real callers do
      };

      mockPrismaNotificationCreate.mockResolvedValue({
        ...mockCreatedNotification,
        data: JSON.stringify(complexData),
      });

      await notificationService.createNotification(inputWithComplexData);

      expect(mockPrismaNotificationCreate).toHaveBeenCalledWith({
        data: {
          actorId: validInput.actorId,
          actorUsername: validInput.actorUsername,
          userId: validInput.userId,
          type: validInput.type,
          message: validInput.message,
          relatedEntityType: validInput.relatedEntityType,
          relatedEntityId: validInput.relatedEntityId,
          data: JSON.stringify(complexData), // Service should pass through the string as-is
        },
      });
    });
  });

  describe('getNotificationsForUser', () => {
    const mockNotifications = [
      {
        id: 'notif1',
        userId: mockUserId,
        type: 'NEW_INTEREST_ON_YOUR_OFFER',
        message: 'Interest received',
        isRead: false,
        createdAt: new Date(),
      },
      {
        id: 'notif2',
        userId: mockUserId,
        type: 'YOUR_INTEREST_ACCEPTED',
        message: 'Interest accepted',
        isRead: true,
        createdAt: new Date(),
      },
    ];

    it('should fetch notifications with default options', async () => {
      mockPrismaNotificationFindMany.mockResolvedValue(mockNotifications);

      const result = await notificationService.getNotificationsForUser(mockUserId);

      expect(mockPrismaNotificationFindMany).toHaveBeenCalledWith({
        where: { userId: mockUserId },
        orderBy: { createdAt: 'desc' },
        take: 20,
        skip: 0,
      });

      expect(result).toEqual(mockNotifications);
    });

    it('should fetch notifications with custom options', async () => {
      const options = { limit: 10, offset: 5, unreadOnly: true };
      mockPrismaNotificationFindMany.mockResolvedValue([mockNotifications[0]]);

      const result = await notificationService.getNotificationsForUser(mockUserId, options);

      expect(mockPrismaNotificationFindMany).toHaveBeenCalledWith({
        where: {
          userId: mockUserId,
          isRead: false,
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
        skip: 5,
      });

      expect(result).toEqual([mockNotifications[0]]);
    });

    it('should throw error when database operation fails', async () => {
      mockPrismaNotificationFindMany.mockRejectedValue(new Error('DB Error'));

      await expect(notificationService.getNotificationsForUser(mockUserId))
        .rejects.toThrow('Failed to fetch notifications.');
    });
  });

  describe('markNotificationAsRead', () => {
    const mockNotification = {
      id: mockNotificationId,
      userId: mockUserId,
      isRead: false,
      type: 'NEW_INTEREST_ON_YOUR_OFFER',
      message: 'Test notification',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should mark notification as read successfully', async () => {
      mockPrismaNotificationFindUnique.mockResolvedValue(mockNotification);
      mockPrismaNotificationUpdate.mockResolvedValue({
        ...mockNotification,
        isRead: true,
      });

      const result = await notificationService.markNotificationAsRead(mockNotificationId, mockUserId);

      expect(mockPrismaNotificationFindUnique).toHaveBeenCalledWith({
        where: { id: mockNotificationId },
      });

      expect(mockPrismaNotificationUpdate).toHaveBeenCalledWith({
        where: { id: mockNotificationId },
        data: { isRead: true },
      });

      expect(result?.isRead).toBe(true);
    });

    it('should return null when notification not found', async () => {
      mockPrismaNotificationFindUnique.mockResolvedValue(null);

      const result = await notificationService.markNotificationAsRead(mockNotificationId, mockUserId);

      expect(result).toBeNull();
      expect(mockPrismaNotificationUpdate).not.toHaveBeenCalled();
    });

    it('should return null when notification belongs to different user', async () => {
      mockPrismaNotificationFindUnique.mockResolvedValue({
        ...mockNotification,
        userId: 'differentUser',
      });

      const result = await notificationService.markNotificationAsRead(mockNotificationId, mockUserId);

      expect(result).toBeNull();
      expect(mockPrismaNotificationUpdate).not.toHaveBeenCalled();
    });

    it('should return notification if already read', async () => {
      const readNotification = { ...mockNotification, isRead: true };
      mockPrismaNotificationFindUnique.mockResolvedValue(readNotification);

      const result = await notificationService.markNotificationAsRead(mockNotificationId, mockUserId);

      expect(result).toEqual(readNotification);
      expect(mockPrismaNotificationUpdate).not.toHaveBeenCalled();
    });
  });

  describe('markAllNotificationsAsRead', () => {
    it('should mark all unread notifications as read', async () => {
      mockPrismaNotificationUpdateMany.mockResolvedValue({ count: 3 });

      const result = await notificationService.markAllNotificationsAsRead(mockUserId);

      expect(mockPrismaNotificationUpdateMany).toHaveBeenCalledWith({
        where: {
          userId: mockUserId,
          isRead: false,
        },
        data: {
          isRead: true,
        },
      });

      expect(result).toEqual({ count: 3 });
    });

    it('should handle case when no notifications to update', async () => {
      mockPrismaNotificationUpdateMany.mockResolvedValue({ count: 0 });

      const result = await notificationService.markAllNotificationsAsRead(mockUserId);

      expect(result).toEqual({ count: 0 });
    });

    it('should throw error when database operation fails', async () => {
      mockPrismaNotificationUpdateMany.mockRejectedValue(new Error('DB Error'));

      await expect(notificationService.markAllNotificationsAsRead(mockUserId))
        .rejects.toThrow('Failed to mark all notifications as read.');
    });
  });
});