# Docker Compose Environment Variables Example
# Copy this file to .env and update the values for your environment

# Database Configuration
POSTGRES_DB=munygo
POSTGRES_USER=munygo
POSTGRES_PASSWORD=your_secure_password_here

# Application Configuration
DATABASE_URL=***********************************************************/munygo
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost

# JWT Secret (change this in production!)
JWT_SECRET=your_super_secret_jwt_key_change_in_production

# Email Configuration (Ethereal for testing)
SMTP_HOST=smtp.ethereal.email
SMTP_PORT=587
SMTP_USER=your_ethereal_user
SMTP_PASS=your_ethereal_pass

# Twilio Configuration (for SMS/Phone verification)
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=+**********
TWILIO_DEV_VERIFIED_NUMBER=+**********
MOCK_TWILIO_OTP=true

# Frontend Configuration
VITE_BACKEND_URL=http://localhost:3000
