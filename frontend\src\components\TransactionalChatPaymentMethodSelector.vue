<template>
  <NModal
    v-model:show="isVisible"
    preset="card"
    :title="t('paymentMethodSelector.modal.title')"
    style="width: 500px; max-width: 95vw; max-height: 90vh;"
    :mask-closable="false"
    :close-on-esc="false"
    class="payment-modal"
    @update:show="handleModalClose"
  >
    <div class="payment-method-selector">
      <!-- Header Info - More compact -->
      <div class="header-info mb-4">
        <NAlert type="info" :bordered="false" class="mb-3" size="small">
          <div>
            <p class="font-medium text-sm">{{ t('paymentMethodSelector.header.infoTitle') }}</p>
            <p class="text-xs mt-1">
              {{ t('paymentMethodSelector.modal.description', { currency: currency || 'payment' }) }}
            </p>
          </div>
        </NAlert>
      </div>

      <!-- Scrollable Content Area -->
      <div class="modal-content" style="max-height: 60vh; overflow-y: auto; padding-right: 8px;">

      <!-- Loading State -->
      <div v-if="isSubmitting" class="loading-state">
        <div class="text-center py-8">
          <NSpin size="large" />
          <p class="text-gray-600 dark:text-gray-400 mt-4">{{ t('paymentMethodSelector.header.loadingMethods') }}</p>
        </div>
      </div>

      <!-- Mode: Select Existing Method -->
      <div v-else-if="currentMode === 'select'" class="select-mode">
        <div class="section-header mb-4">
          <h3 class="text-lg font-semibold">{{ t('paymentMethodSelector.selectMode.title', { currency }) }}</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ t('paymentMethodSelector.selectMode.description') }}
          </p>
        </div>

        <!-- Existing Methods List -->
        <div v-if="availableMethods.length > 0" class="methods-list mb-6">
          <div
            v-for="method in availableMethods"
            :key="method.id"
            class="method-item"
            :class="{ 'selected': selectedMethodId === method.id }"
            @click="selectMethod(method.id)"
          >
            <div class="method-content">
              <div class="method-info">
                <div class="method-primary">
                  <h4 class="method-bank">{{ method.bankName }}</h4>
                  <div class="method-status">
                    <NBadge
                      v-if="method.isDefaultForUser"
                      type="success"
                      :value="t('paymentMethodSelector.methodItem.default')"
                      size="small"
                    />
                    <NBadge
                      :type="method.validationStatus === 'complete' ? 'success' : 'warning'"
                      :value="method.validationStatus === 'complete' ? t('paymentMethodSelector.methodItem.verified') : t('paymentMethodSelector.methodItem.warning')"
                      size="small"
                    />
                  </div>
                </div>
                <div class="method-details">
                  <span class="account-info">{{ formatAccountNumber(method.accountNumber) }}</span>
                  <span class="account-holder">{{ method.accountHolderName }}</span>
                </div>
              </div>

              <!-- Validation Issues -->
              <div v-if="method.missingFields.length > 0" class="validation-warning">
                <Icon icon="mdi:alert-circle" size="12" />
                <span>{{ t('paymentMethodSelector.methodItem.missingFields', { fields: method.missingFields.join(', ') }) }}</span>
              </div>
            </div>

            <!-- Actions and Selection -->
            <div class="method-controls">
              <div class="action-buttons">
                <NButton 
                  size="tiny" 
                  circle
                  quaternary 
                  @click.stop="switchToEditMode(method)"
                  :title="t('paymentMethodSelector.methodItem.edit')"
                >
                  <template #icon>
                    <Icon icon="mdi:pencil" size="14" />
                  </template>
                </NButton>
                <NButton 
                  size="tiny" 
                  circle
                  quaternary 
                  type="error"
                  @click.stop="deleteMethod(method)"
                  :title="t('paymentMethodSelector.methodItem.delete')"
                >
                  <template #icon>
                    <Icon icon="mdi:delete" size="14" />
                  </template>
                </NButton>
              </div>
              
              <div class="method-radio">
                <NRadio :checked="selectedMethodId === method.id" size="small" />
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="actions mt-6">
            <NSpace justify="space-between">
              <NButton @click="switchToCreateMode">
                <template #icon>
                  <Icon icon="mdi:plus" />
                </template>
                {{ t('paymentMethodSelector.selectMode.addNewMethod') }}
              </NButton>
              
              <NSpace>
                <NButton @click="closeModal">{{ t('paymentMethodSelector.selectMode.cancel') }}</NButton>
                <NButton
                  type="primary"
                  @click="confirmSelection"
                  :disabled="!selectedMethodId"
                  :loading="isSubmitting"
                >
                  {{ t('paymentMethodSelector.selectMode.useSelectedMethod') }}
                </NButton>
              </NSpace>
            </NSpace>
          </div>
        </div>

        <!-- No Methods Available -->
        <div v-else class="no-methods">
          <div class="text-center py-8">
            <div class="text-gray-400 mb-4 text-5xl">💳</div>
            <h4 class="text-lg font-medium mb-2" style="color: #6b7280;">
              {{ t('paymentMethodSelector.noMethods.title', { currency }) }}
            </h4>
            <p class="mb-6" style="color: #9ca3af;">
              {{ t('paymentMethodSelector.noMethods.description', { currency }) }}
            </p>
            <NButton type="primary" @click="switchToCreateMode">
              <template #icon>
                <Icon icon="mdi:plus" />
              </template>
              {{ t('paymentMethodSelector.noMethods.addButton') }}
            </NButton>
          </div>
        </div>
      </div>

      <!-- Mode: Create New Method -->
      <div v-else-if="currentMode === 'create'" class="create-mode">
        <div class="section-header mb-4">
          <h3 class="text-lg font-semibold">{{ t('paymentMethodSelector.createMode.title', { currency }) }}</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ t('paymentMethodSelector.createMode.description') }}
          </p>
        </div>

        <PaymentMethodForm
          :currency="currency"
          @submit="handleCreateSubmit"
          @cancel="handleCreateCancel"
          :loading="isSubmitting"
        />
      </div>

      <!-- Mode: Edit Method -->
      <div v-else-if="currentMode === 'edit'" class="edit-mode">
        <div class="section-header mb-4">
          <h3 class="text-lg font-semibold">{{ t('paymentMethodSelector.editMode.title') }}</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ t('paymentMethodSelector.editMode.description') }}
          </p>
        </div>

        <PaymentMethodForm
          :method="editingMethod"
          :currency="currency"
          @submit="handleEditSubmit"
          @cancel="handleEditCancel"
          :loading="isSubmitting"
        />
      </div>
      
      </div> <!-- End scrollable content -->
    </div>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  NModal,
  NAlert,
  NButton,
  NSpace,
  NBadge,
  NRadio,
  NSpin,
  useMessage,
  useNotification,
  useDialog
} from 'naive-ui';
import { Icon } from '@iconify/vue';
import { useI18n } from 'vue-i18n';
import PaymentMethodForm from './PaymentMethodForm.vue';
import { usePaymentMethodsIntegration } from '@/composables/usePaymentMethodsIntegration';
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore';
import type { PaymentMethodWithValidation, CreatePaymentMethodPayload } from '@/types/paymentMethods';

// Store and composables
const { t } = useI18n();
const transactionalChatStore = useTransactionalChatStore();
const paymentIntegration = usePaymentMethodsIntegration();
const message = useMessage();
const notification = useNotification();
const dialog = useDialog();

// Local state
const selectedMethodId = ref<string>('');
const editingMethod = ref<PaymentMethodWithValidation | null>(null);

// Computed properties
const isVisible = computed(() => transactionalChatStore.showPaymentDetailsForm);
const isSubmitting = computed(() => paymentIntegration.isLoading.value);
const formState = computed(() => paymentIntegration.formState.value);
const currentMode = computed(() => formState.value.mode);

// Always prioritize currency from transaction details, with fallback to form state
const currency = computed(() => {
  const transactionCurrency = transactionalChatStore.transactionDetails?.currencyTo;
  const formCurrency = formState.value.currency;
  
  return transactionCurrency || formCurrency || 'IRR';
});

const availableMethods = computed(() => formState.value.availableMethods);

// Watchers for reactive behavior
watch(isVisible, (newValue) => {
  if (newValue) {
    // Ensure the integration state is synced when modal opens
    if (!formState.value.isVisible || formState.value.currency !== currency.value) {
      paymentIntegration.openPaymentDetailsForm('', currency.value);
    }
  }
}, { immediate: true });

watch(currency, (newCurrency, oldCurrency) => {
  if (isVisible.value && newCurrency !== oldCurrency) {
    paymentIntegration.openPaymentDetailsForm('', newCurrency);
  }
}, { immediate: true });

watch(availableMethods, (newMethods) => {
  // Available methods watcher - functionality can be added here if needed
}, { immediate: true });

watch(currentMode, (newMode) => {
  // Current mode watcher - functionality can be added here if needed
}, { immediate: true });

watch(formState, (newFormState) => {
  // Form state watcher - functionality can be added here if needed
}, { immediate: true, deep: true });

watch(isSubmitting, (newIsSubmitting) => {
  // Submitting state watcher - functionality can be added here if needed
}, { immediate: true });

// Methods
const selectMethod = (methodId: string) => {
  selectedMethodId.value = methodId;
};

const formatAccountNumber = (accountNumber: string): string => {
  if (!accountNumber || accountNumber.length <= 4) return accountNumber;
  const lastFour = accountNumber.slice(-4);
  const masked = '*'.repeat(Math.min(accountNumber.length - 4, 8));
  return `${masked}${lastFour}`;
};

const switchToCreateMode = () => {
  paymentIntegration.switchToCreateMode();
  selectedMethodId.value = '';
};

const handleCreateCancel = () => {
  if (availableMethods.value.length > 0) {
    paymentIntegration.switchToSelectMode();
  } else {
    closeModal();
  }
};

const switchToEditMode = (method: PaymentMethodWithValidation) => {
  editingMethod.value = method;
  paymentIntegration.switchToEditMode(method);
};

const handleEditCancel = () => {
  editingMethod.value = null;
  paymentIntegration.switchToSelectMode();
};

const confirmSelection = async () => {
  if (!selectedMethodId.value) return;

  const selectedMethod = availableMethods.value.find(m => m.id === selectedMethodId.value);
  if (!selectedMethod) return;

  try {
    await transactionalChatStore.completePaymentMethodSelection(selectedMethodId.value);
    message.success(t('paymentMethodSelector.messages.selectionSuccess'));
    closeModal();
  } catch (error) {
    notification.error({
      title: 'Error',
      content: t('paymentMethodSelector.messages.selectionError'),
      duration: 3000
    });
  }
};

const deleteMethod = async (method: PaymentMethodWithValidation) => {
  // Show confirmation dialog
  dialog.warning({
    title: t('paymentMethodSelector.actions.deleteConfirm'),
    content: t('paymentMethodSelector.actions.deleteWarning', { 
      bankName: method.bankName, 
      accountNumber: formatAccountNumber(method.accountNumber) 
    }),
    positiveText: t('paymentMethodSelector.actions.deleteButton'),
    negativeText: t('paymentMethodSelector.actions.cancelButton'),
    onPositiveClick: async () => {
      try {
        await paymentIntegration.deleteMethod(method.id);
        message.success(t('paymentMethodSelector.messages.deleteSuccess'));
        
        // If we deleted the selected method, clear selection
        if (selectedMethodId.value === method.id) {
          selectedMethodId.value = '';
        }
      } catch (error) {
        notification.error({
          title: 'Error',
          content: t('paymentMethodSelector.messages.deleteError'),
          duration: 3000
        });
      }
    }
  });
};

const handleCreateSubmit = async (formData: CreatePaymentMethodPayload) => {
  try {
    const selection = await paymentIntegration.createAndSelectMethod(formData);
    await transactionalChatStore.completePaymentMethodSelection(selection.methodId!);
    message.success(t('paymentMethodSelector.messages.createSuccess'));
    closeModal();
  } catch (error) {
    notification.error({
      title: 'Error',
      content: t('paymentMethodSelector.messages.createError'),
      duration: 3000
    });
  }
};

const handleEditSubmit = async (formData: any) => {
  if (!editingMethod.value) return;

  try {
    await paymentIntegration.updateMethod(editingMethod.value.id, formData);
    message.success(t('paymentMethodSelector.messages.updateSuccess'));
    editingMethod.value = null;
    paymentIntegration.switchToSelectMode();
  } catch (error) {
    notification.error({
      title: 'Error',
      content: t('paymentMethodSelector.messages.updateError'),
      duration: 3000
    });
  }
};

const closeModal = () => {
  transactionalChatStore.closePaymentDetailsForm();
  paymentIntegration.closePaymentDetailsForm();
  selectedMethodId.value = '';
  editingMethod.value = null;
};

const handleModalClose = (show: boolean) => {
  // When the modal's X button is clicked, show becomes false
  if (!show) {
    closeModal();
  }
};

// Reset selection when mode changes back to select
watch(currentMode, (newMode) => {
  if (newMode === 'select') {
    selectedMethodId.value = '';
  }
});

// Auto-select method based on transaction state or default
watch(availableMethods, (methods) => {
  if (methods.length > 0 && !selectedMethodId.value) {
    // First, check if there's a payment method already selected for this transaction
    const transactionDetails = transactionalChatStore.transactionDetails;
    const userPaymentDetails = transactionDetails?.userPaymentDetails;
    
    let methodToSelect = null;
    
    if (userPaymentDetails?.id) {
      // Find the method that matches the transaction's selected payment method
      methodToSelect = methods.find(m => m.id === userPaymentDetails.id);
      if (methodToSelect) {
        console.log('🔍 [PaymentMethodSelector] Found previously selected method for transaction:', methodToSelect);
        selectedMethodId.value = methodToSelect.id;
        return;
      }
    }
    
    // Fall back to default method if no transaction-specific method found
    const defaultMethod = methods.find(m => m.isDefaultForUser);
    if (defaultMethod) {
      console.log('🔍 [PaymentMethodSelector] Using default method:', defaultMethod);
      selectedMethodId.value = defaultMethod.id;
    }
  }
}, { immediate: true });
</script>

<style scoped>
.payment-method-selector {
  min-height: 200px;
}

.section-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
}

.methods-list {
  max-height: 400px;
  overflow-y: auto;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 60px;
}

.method-item:hover {
  border-color: #3b82f6;
  background-color: #f1f5f9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.method-item.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 0 0 1px #3b82f6, 0 2px 8px rgba(59, 130, 246, 0.15);
}

.method-item.selected .action-buttons {
  opacity: 1;
}

.method-item.selected .action-buttons .n-button {
  color: #1e40af !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.method-item.selected .action-buttons .n-button:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

.method-content {
  flex: 1;
  min-width: 0;
}

.method-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.method-primary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.method-bank {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin: 0;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.method-status {
  display: flex;
  gap: 4px;
  align-items: center;
  flex-shrink: 0;
}

.method-details {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 12px;
  color: #6b7280;
}

.account-info {
  font-family: 'Monaco', 'Courier New', monospace;
  font-weight: 500;
}

.account-holder {
  color: #9ca3af;
}

.method-details::before {
  content: '';
  width: 2px;
  height: 2px;
  background-color: #d1d5db;
  border-radius: 50%;
  flex-shrink: 0;
}

.validation-warning {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #f59e0b;
  margin-top: 2px;
}

.method-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
}

.action-buttons {
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.method-item:hover .action-buttons {
  opacity: 1;
}

.method-radio {
  display: flex;
  align-items: center;
}

.dark .validation-warning {
  color: #fbbf24;
}

.dark .section-header {
  border-bottom-color: #374151;
}

.no-methods {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background-color: #f9fafb;
  color: #374151;
}

.dark .no-methods {
  border-color: #4b5563;
  background-color: #111827;
  color: #d1d5db;
}

/* Responsive design */
@media (max-width: 768px) {
  .method-item {
    padding: 10px;
    min-height: 70px;
  }

  .method-primary {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .method-controls {
    margin-left: 8px;
    gap: 6px;
  }

  .action-buttons {
    opacity: 1; /* Always show on mobile */
  }

  .method-details {
    flex-direction: column;
    gap: 2px;
    align-items: flex-start;
  }

  .method-details::before {
    display: none;
  }
}
</style>
