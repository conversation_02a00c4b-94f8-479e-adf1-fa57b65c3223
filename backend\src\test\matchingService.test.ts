import { describe, it, expect, beforeEach, vi, beforeAll, afterAll } from 'vitest';
import { PrismaClient, OfferType, OfferStatus, MatchStatus } from '@prisma/client';
import { MatchingService } from '../services/matchingService';
import { NotificationService } from '../services/notificationService';
import { Server } from 'socket.io';

// Mock Socket.IO
const mockIo = {
  to: vi.fn().mockReturnThis(),
  emit: vi.fn()
} as unknown as Server;

// Mock dependencies
vi.mock('../services/notificationService');

describe('MatchingService', () => {
  let prisma: PrismaClient;
  let matchingService: MatchingService;
  let mockNotificationService: NotificationService;
  
  beforeAll(async () => {
    // Require DATABASE_URL environment variable to be explicitly set
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable must be set for tests. No fallback database connection allowed for security.');
    }
    
    // Use test database from DATABASE_URL environment variable
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      }
    });
    
    // Initialize mocked services
    mockNotificationService = {
      createNotification: vi.fn().mockResolvedValue({
        id: 'mock-notification-id',
        userId: 'mock-user-id',
        type: 'MATCH_FOUND',
        message: 'Mock notification',
        isRead: false,
        createdAt: new Date()
      })
    } as any;

    matchingService = new MatchingService(
      prisma,      mockIo,
      mockNotificationService
    );
  });

  beforeEach(async () => {
    // Clean up test data before each test in correct order due to foreign key constraints
    // Only delete test-specific data to avoid affecting development users
    await prisma.chatMessage.deleteMany({
      where: {
        OR: [          { chatSession: { userOne: { email: { contains: '@example.com' } } } },
          { chatSession: { userTwo: { email: { contains: '@example.com' } } } },
          { chatSession: { userOne: { email: { contains: '@test.com' } } } },
          { chatSession: { userTwo: { email: { contains: '@test.com' } } } }
        ]
      }
    });
    await prisma.payerNegotiation.deleteMany({
      where: {
        OR: [          { transaction: { offer: { user: { email: { contains: '@example.com' } } } } },
          { transaction: { offer: { user: { email: { contains: '@test.com' } } } } }
        ]
      }
    });    await prisma.transaction.deleteMany({
      where: {
        OR: [
          { offer: { user: { email: { contains: '@example.com' } } } },
          { offer: { user: { email: { contains: '@test.com' } } } }
        ]
      }
    });await prisma.chatSession.deleteMany({
      where: {
        OR: [          { userOne: { email: { contains: '@example.com' } } },
          { userTwo: { email: { contains: '@example.com' } } },
          { userOne: { email: { contains: '@test.com' } } },
          { userTwo: { email: { contains: '@test.com' } } }
        ]
      }
    });    await prisma.offerMatch.deleteMany({
      where: {
        OR: [          { offerA: { user: { email: { contains: '@example.com' } } } },
          { offerA: { user: { email: { contains: '@test.com' } } } },
          { offerB: { user: { email: { contains: '@example.com' } } } },
          { offerB: { user: { email: { contains: '@test.com' } } } }
        ]
      }
    });
    await prisma.interest.deleteMany({
      where: {
        OR: [          { interestedUser: { email: { contains: '@example.com' } } },
          { interestedUser: { email: { contains: '@test.com' } } },
          { offer: { user: { email: { contains: '@example.com' } } } },
          { offer: { user: { email: { contains: '@test.com' } } } }
        ]
      }
    });    await prisma.paymentReceivingInfo.deleteMany({
      where: {
        user: { 
          OR: [
            { email: { contains: '@example.com' } },
            { email: { contains: '@test.com' } }
          ]
        }
      }
    });    await prisma.offer.deleteMany({
      where: {
        user: { 
          OR: [
            { email: { contains: '@example.com' } },
            { email: { contains: '@test.com' } }
          ]
        }
      }
    });
    await prisma.user.deleteMany({
      where: {        OR: [
          { email: { contains: '@example.com' } },
          { email: { contains: '@test.com' } }
        ]
      }
    });
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('findPotentialMatches', () => {
    it('should find exact rate matches between compatible offers', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });      // Create compatible offers based on the new convention
      // User A wants to BUY 1000 CAD, offering 10 IRR per CAD.
      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'CAD-IRR', // e.g., Canadian Dollar to Iranian Rial
          amount: 1000,           // Amount of CAD
          baseRate: 10,             // Price: 10 IRR per CAD
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // User B wants to SELL 1000 CAD, asking 10 IRR per CAD.
      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'CAD-IRR', // Same currency pair
          amount: 1000,           // Same amount of CAD
          baseRate: 10,             // Same price: 10 IRR per CAD
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Test finding matches
      const matches = await matchingService.findPotentialMatches(offerA.id);      expect(matches).toHaveLength(1);
      // Ensure the match object correctly references offerA and offerB
      // The createMatch method populates offerA and offerB based on the order they are passed.
      // findPotentialMatches calls createMatch(offer, matchOffer)
      // So, matches[0].offerAId should be offerA.id and matches[0].offerBId should be offerB.id
      expect(matches[0]).toMatchObject({
        offerAId: offerA.id,
        offerBId: offerB.id,
        // You can add more specific checks for the match object if needed,
        // e.g., status: MatchStatus.PENDING (assuming createMatch sets it)
      });
    });

    it('should not match offers from the same user', async () => {
      // Create test user
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'user',
          reputationLevel: 3
        }
      });

      // Create two offers from same user
      const offerA = await prisma.offer.create({
        data: {
          userId: user.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: user.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Test finding matches - should not match same user's offers
      const matches = await matchingService.findPotentialMatches(offerA.id);

      expect(matches).toHaveLength(0);
    });

    it('should not match inactive offers', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      // Create offers - one inactive
      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.INACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Test finding matches for inactive offer
      const matches = await matchingService.findPotentialMatches(offerA.id);

      expect(matches).toHaveLength(0);
    });
  });

  describe('createMatch', () => {
    it('should create a new match with correct initial status', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create a match
      const match = await matchingService.createMatch(offerA, offerB);      expect(match).toMatchObject({
        offerAId: offerA.id,
        offerBId: offerB.id,
        status: MatchStatus.PENDING,
        userAResponse: null,
        userBResponse: null
      });
    });

    it('should handle duplicate match creation gracefully', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create a match
      const match1 = await matchingService.createMatch(offerA, offerB);
      
      // Try to create the same match again
      const match2 = await matchingService.createMatch(offerA, offerB);

      // Should return the existing match
      expect(match1.id).toBe(match2.id);
    });
  });

  describe('acceptMatch', () => {
    it('should handle race condition when both users accept simultaneously', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create a match
      const match = await matchingService.createMatch(offerA, offerB);      // Simulate both users accepting simultaneously
      const results = await Promise.allSettled([
        matchingService.acceptMatch(match.id, userA.id),
        matchingService.acceptMatch(match.id, userB.id)
      ]);      // At least one should succeed, one might fail due to race condition
      const successfulResults = results.filter(r => r.status === 'fulfilled');
      expect(successfulResults.length).toBeGreaterThanOrEqual(1);
      
      // Check final match status
      const finalMatch = await prisma.offerMatch.findUnique({
        where: { id: match.id }
      });
      
      // In a race condition, when both users accept simultaneously, the match progresses
      // from BOTH_ACCEPTED to CONVERTED as it creates a transaction
      expect([MatchStatus.BOTH_ACCEPTED, MatchStatus.CONVERTED]).toContain(finalMatch?.status);
    });    it('should update match status correctly for partial acceptance', async () => {
      // Create test users and offers
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'CAD-IRR',
          amount: 1000,
          baseRate: 10,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'CAD-IRR',
          amount: 1000,
          baseRate: 10,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create a match
      const match = await matchingService.createMatch(offerA, offerB);

      // User A accepts first
      await matchingService.acceptMatch(match.id, userA.id);

      // Check partial acceptance status
      const updatedMatch = await prisma.offerMatch.findUnique({
        where: { id: match.id }
      });      expect(updatedMatch?.status).toBe(MatchStatus.PARTIAL_ACCEPT);
      expect(updatedMatch?.userAResponse).toBe('ACCEPTED');
      expect(updatedMatch?.userBResponse).toBeNull(); // Not responded yet
    });

    it('should create transaction when both users accept', async () => {
      // Create test users and offers
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 850,
          baseRate: 1.176,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create a match
      const match = await matchingService.createMatch(offerA, offerB);

      // Both users accept
      await matchingService.acceptMatch(match.id, userA.id);
      const result = await matchingService.acceptMatch(match.id, userB.id);      // Check that transaction was created
      expect(result.transaction).toBeDefined();
      expect(result.transaction.status).toBe('AWAITING_FIRST_PAYER_DESIGNATION');
        // Verify transaction exists in database
      const transaction = await prisma.transaction.findUnique({
        where: { id: result.transaction.id }
      });
      
      expect(transaction).toBeDefined();
      
      // Verify the match is linked to the transaction through the offerMatch table
      const updatedMatch = await prisma.offerMatch.findUnique({
        where: { id: match.id }
      });
      expect(updatedMatch?.transactionId).toBe(transaction?.id);
    });
  });

  describe('decline backoff system', () => {
    it('should block re-matching within 1 hour of decline', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      // Create offers that would normally match
      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create and decline a match between these users
      const initialMatch = await matchingService.createMatch(offerA, offerB);
      await matchingService.declineMatch(initialMatch.id, userA.id, 'Testing backoff');

      // Verify match was declined
      const declinedMatch = await prisma.offerMatch.findUnique({
        where: { id: initialMatch.id }
      });
      expect(declinedMatch?.status).toBe(MatchStatus.DECLINED);

      // Try to find matches again - should be empty due to backoff
      const matches = await matchingService.findPotentialMatches(offerA.id);
      expect(matches).toHaveLength(0);
    });

    it('should allow matching after 1 hour cooldown period', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'GBP-USD',
          amount: 500,
          baseRate: 1.25,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'GBP-USD',
          amount: 500,
          baseRate: 1.25,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create an old declined match (more than 1 hour ago)
      const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);
      await prisma.offerMatch.create({
        data: {
          matchId: `MATCH_${Date.now()}_OLD`,
          offerAId: offerA.id,
          offerBId: offerB.id,
          userAId: userA.id,
          userBId: userB.id,
          currencyA: 'GBP',
          currencyB: 'USD',
          amountA: 500,
          amountB: 625,
          rateAToB: 1.25,
          rateBToA: 0.8,
          status: MatchStatus.DECLINED,
          declinedByUserId: userA.id,
          declineReason: 'Old decline',
          createdAt: twoHoursAgo,
          updatedAt: twoHoursAgo,
          expiresAt: new Date(twoHoursAgo.getTime() + 24 * 60 * 60 * 1000)
        }
      });

      // Now try to find matches - should work since cooldown period passed
      const matches = await matchingService.findPotentialMatches(offerA.id);
      expect(matches).toHaveLength(1);
      expect(matches[0].offerBId).toBe(offerB.id);
    });

    it('should handle multiple decline scenarios correctly', async () => {
      // Create three users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const userC = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userc',
          reputationLevel: 3
        }
      });

      // Create offers
      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'EUR-USD',
          amount: 800,
          baseRate: 1.1,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'EUR-USD',
          amount: 800,
          baseRate: 1.1,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerC = await prisma.offer.create({
        data: {
          userId: userC.id,
          type: OfferType.SELL,
          currencyPair: 'EUR-USD',
          amount: 800,
          baseRate: 1.1,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // User A declines match with User B
      const matchAB = await matchingService.createMatch(offerA, offerB);
      await matchingService.declineMatch(matchAB.id, userA.id, 'Not interested');

      // User A should still be able to match with User C (no decline history)
      const matches = await matchingService.findPotentialMatches(offerA.id);
      expect(matches).toHaveLength(1);
      expect(matches[0].offerBId).toBe(offerC.id);

      // But should not match with User B due to recent decline
      const matchesWithB = matches.filter(m => m.offerBId === offerB.id);
      expect(matchesWithB).toHaveLength(0);
    });
  });

  describe('competing match cancellation', () => {
    it('should cancel pending matches for same offers when one is accepted', async () => {
      // Create three users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const userC = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userc',
          reputationLevel: 3
        }
      });

      // Create offers - A wants to buy, B and C want to sell
      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-CAD',
          amount: 1000,
          baseRate: 1.3,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-CAD',
          amount: 1000,
          baseRate: 1.3,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerC = await prisma.offer.create({
        data: {
          userId: userC.id,
          type: OfferType.SELL,
          currencyPair: 'USD-CAD',
          amount: 1000,
          baseRate: 1.3,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create competing matches
      const matchAB = await matchingService.createMatch(offerA, offerB);
      const matchAC = await matchingService.createMatch(offerA, offerC);

      // Verify both matches are pending
      expect(matchAB.status).toBe(MatchStatus.PENDING);
      expect(matchAC.status).toBe(MatchStatus.PENDING);

      // User A accepts match with User B
      await matchingService.acceptMatch(matchAB.id, userA.id);
      await matchingService.acceptMatch(matchAB.id, userB.id);

      // Check that the accepted match progressed to CONVERTED (transaction created)
      const finalMatchAB = await prisma.offerMatch.findUnique({
        where: { id: matchAB.id }
      });
      expect([MatchStatus.BOTH_ACCEPTED, MatchStatus.CONVERTED]).toContain(finalMatchAB?.status);

      // Check that competing match was cancelled
      const finalMatchAC = await prisma.offerMatch.findUnique({
        where: { id: matchAC.id }
      });
      expect(finalMatchAC?.status).toBe(MatchStatus.CANCELLED);
    });

    it('should handle multiple competing matches correctly', async () => {
      // Create four users for complex scenario
      const users = await Promise.all([
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            password: 'hashedpassword',
            emailVerified: true,
            phoneVerified: true,
            username: 'user1',
            reputationLevel: 3
          }
        }),
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            password: 'hashedpassword',
            emailVerified: true,
            phoneVerified: true,
            username: 'user2',
            reputationLevel: 3
          }
        }),
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            password: 'hashedpassword',
            emailVerified: true,
            phoneVerified: true,
            username: 'user3',
            reputationLevel: 3
          }
        }),
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            password: 'hashedpassword',
            emailVerified: true,
            phoneVerified: true,
            username: 'user4',
            reputationLevel: 3
          }
        })
      ]);

      // Create offers - multiple sellers for same currency pair
      const buyOffer = await prisma.offer.create({
        data: {
          userId: users[0].id,
          type: OfferType.BUY,
          currencyPair: 'JPY-USD',
          amount: 100000,
          baseRate: 0.007,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const sellOffers = await Promise.all([
        prisma.offer.create({
          data: {
            userId: users[1].id,
            type: OfferType.SELL,
            currencyPair: 'JPY-USD',
            amount: 100000,
            baseRate: 0.007,
            adjustmentForLowerRep: 0,
            adjustmentForHigherRep: 0,
            status: OfferStatus.ACTIVE
          }
        }),
        prisma.offer.create({
          data: {
            userId: users[2].id,
            type: OfferType.SELL,
            currencyPair: 'JPY-USD',
            amount: 100000,
            baseRate: 0.007,
            adjustmentForLowerRep: 0,
            adjustmentForHigherRep: 0,
            status: OfferStatus.ACTIVE
          }
        }),
        prisma.offer.create({
          data: {
            userId: users[3].id,
            type: OfferType.SELL,
            currencyPair: 'JPY-USD',
            amount: 100000,
            baseRate: 0.007,
            adjustmentForLowerRep: 0,
            adjustmentForHigherRep: 0,
            status: OfferStatus.ACTIVE
          }
        })
      ]);

      // Create multiple competing matches
      const matches = await Promise.all([
        matchingService.createMatch(buyOffer, sellOffers[0]),
        matchingService.createMatch(buyOffer, sellOffers[1]),
        matchingService.createMatch(buyOffer, sellOffers[2])
      ]);

      // Accept one match fully
      await matchingService.acceptMatch(matches[0].id, users[0].id);
      await matchingService.acceptMatch(matches[0].id, users[1].id);

      // Verify the accepted match succeeded
      const acceptedMatch = await prisma.offerMatch.findUnique({
        where: { id: matches[0].id }
      });
      expect([MatchStatus.BOTH_ACCEPTED, MatchStatus.CONVERTED]).toContain(acceptedMatch?.status);

      // Verify competing matches were cancelled
      const competingMatch1 = await prisma.offerMatch.findUnique({
        where: { id: matches[1].id }
      });
      const competingMatch2 = await prisma.offerMatch.findUnique({
        where: { id: matches[2].id }
      });

      expect(competingMatch1?.status).toBe(MatchStatus.CANCELLED);
      expect(competingMatch2?.status).toBe(MatchStatus.CANCELLED);
    });
  });

  describe('socket event verification', () => {
    it('should emit correct MATCH_FOUND payload structure', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create match and verify socket events
      const match = await matchingService.createMatch(offerA, offerB);

      // Verify MATCH_FOUND events were emitted to both users
      expect(mockIo.to).toHaveBeenCalledWith(`user_${userA.id}`);
      expect(mockIo.to).toHaveBeenCalledWith(`user_${userB.id}`);
      expect(mockIo.emit).toHaveBeenCalledWith('MATCH_FOUND', expect.objectContaining({
        matchId: match.id,
        match: expect.objectContaining({
          id: match.id,
          matchId: expect.any(String),
          offerA: expect.objectContaining({
            id: offerA.id,
            type: 'BUY',
            currencyPair: 'USD-EUR'
          }),
          offerB: expect.objectContaining({
            id: offerB.id,
            type: 'SELL',
            currencyPair: 'USD-EUR'
          }),
          compatibilityScore: expect.any(Number),
          expiresAt: expect.any(String)
        })
      }));
    });

    it('should emit MATCH_ACCEPTED only to correct users based on scenario', async () => {
      // Create test users and match
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'CAD-USD',
          amount: 1000,
          baseRate: 0.75,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'CAD-USD',
          amount: 1000,
          baseRate: 0.75,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const match = await matchingService.createMatch(offerA, offerB);

      // Clear previous mock calls from match creation
      vi.clearAllMocks();

      // User A accepts (partial acceptance)
      await matchingService.acceptMatch(match.id, userA.id);

      // Should emit MATCH_ACCEPTED only to User B (the other user)
      expect(mockIo.to).toHaveBeenCalledWith(`user_${userB.id}`);
      expect(mockIo.to).not.toHaveBeenCalledWith(`user_${userA.id}`);
      expect(mockIo.emit).toHaveBeenCalledWith('MATCH_ACCEPTED', expect.objectContaining({
        matchId: match.id,
        acceptingUserId: userA.id,
        status: 'partial_accept'
      }));

      // Clear mocks again
      vi.clearAllMocks();

      // User B accepts (both accepted)
      await matchingService.acceptMatch(match.id, userB.id);

      // Should emit MATCH_ACCEPTED to both users for mutual acceptance
      expect(mockIo.to).toHaveBeenCalledWith(`user_${userA.id}`);
      expect(mockIo.to).toHaveBeenCalledWith(`user_${userB.id}`);
      expect(mockIo.emit).toHaveBeenCalledWith('MATCH_ACCEPTED', expect.objectContaining({
        matchId: match.id,
        acceptingUserId: userB.id,
        status: 'both_accepted'
      }));
    });

    it('should emit MATCH_DECLINED with proper reason and only to other user', async () => {
      // Create test users and match
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'EUR-GBP',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'EUR-GBP',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const match = await matchingService.createMatch(offerA, offerB);

      // Clear previous mock calls
      vi.clearAllMocks();

      const declineReason = 'Rate not favorable anymore';

      // User A declines the match
      await matchingService.declineMatch(match.id, userA.id, declineReason);

      // Should emit MATCH_DECLINED only to User B (the other user)
      expect(mockIo.to).toHaveBeenCalledWith(`user_${userB.id}`);
      expect(mockIo.to).not.toHaveBeenCalledWith(`user_${userA.id}`);
      expect(mockIo.emit).toHaveBeenCalledWith('MATCH_DECLINED', expect.objectContaining({
        matchId: match.id,
        decliningUserId: userA.id,
        reason: declineReason,
        match: expect.objectContaining({
          id: match.id,
          status: MatchStatus.DECLINED
        })
      }));
    });
  });

  describe('integration workflows', () => {
    it('should handle complete workflow: create -> partial accept -> full accept -> transaction -> competing match cancellation', async () => {
      // Create three users for complex workflow
      const users = await Promise.all([
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            password: 'hashedpassword',
            emailVerified: true,
            phoneVerified: true,
            username: 'buyer',
            reputationLevel: 4
          }
        }),
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            password: 'hashedpassword',
            emailVerified: true,
            phoneVerified: true,
            username: 'seller1',
            reputationLevel: 4
          }
        }),
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            password: 'hashedpassword',
            emailVerified: true,
            phoneVerified: true,
            username: 'seller2',
            reputationLevel: 4
          }
        })
      ]);

      // Create offers
      const buyOffer = await prisma.offer.create({
        data: {
          userId: users[0].id,
          type: OfferType.BUY,
          currencyPair: 'AUD-USD',
          amount: 2000,
          baseRate: 0.65,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const sellOffer1 = await prisma.offer.create({
        data: {
          userId: users[1].id,
          type: OfferType.SELL,
          currencyPair: 'AUD-USD',
          amount: 2000,
          baseRate: 0.65,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const sellOffer2 = await prisma.offer.create({
        data: {
          userId: users[2].id,
          type: OfferType.SELL,
          currencyPair: 'AUD-USD',
          amount: 2000,
          baseRate: 0.65,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Step 1: Create competing matches
      const match1 = await matchingService.createMatch(buyOffer, sellOffer1);
      const match2 = await matchingService.createMatch(buyOffer, sellOffer2);

      expect(match1.status).toBe(MatchStatus.PENDING);
      expect(match2.status).toBe(MatchStatus.PENDING);

      // Step 2: Partial acceptance (buyer accepts first match)
      await matchingService.acceptMatch(match1.id, users[0].id);

      const partialMatch = await prisma.offerMatch.findUnique({
        where: { id: match1.id }
      });
      expect(partialMatch?.status).toBe(MatchStatus.PARTIAL_ACCEPT);
      expect(partialMatch?.userAResponse).toBe('ACCEPTED');
      expect(partialMatch?.userBResponse).toBeNull();

      // Step 3: Full acceptance (seller accepts)
      const result = await matchingService.acceptMatch(match1.id, users[1].id);

      expect(result.status).toBe('both_accepted');
      expect(result.transaction).toBeDefined();
      expect(result.chatSessionId).toBeDefined();

      // Step 4: Verify transaction was created
      const finalMatch = await prisma.offerMatch.findUnique({
        where: { id: match1.id }
      });
      expect([MatchStatus.BOTH_ACCEPTED, MatchStatus.CONVERTED]).toContain(finalMatch?.status);
      expect(finalMatch?.transactionId).toBeDefined();
      expect(finalMatch?.chatSessionId).toBeDefined();

      // Step 5: Verify competing match was cancelled
      const competingMatch = await prisma.offerMatch.findUnique({
        where: { id: match2.id }
      });
      expect(competingMatch?.status).toBe(MatchStatus.CANCELLED);

      // Step 6: Verify transaction exists with correct details
      const transaction = await prisma.transaction.findUnique({
        where: { id: finalMatch?.transactionId! }
      });
      expect(transaction).toBeDefined();
      expect(transaction?.status).toBe('AWAITING_FIRST_PAYER_DESIGNATION');
      expect(transaction?.currencyA).toBe('USD'); // Quote currency from buyer
      expect(transaction?.currencyB).toBe('AUD'); // Base currency from seller
      expect(transaction?.amountA).toBe(1300); // 2000 * 0.65
      expect(transaction?.amountB).toBe(2000);

      // Step 7: Verify chat session exists
      const chatSession = await prisma.chatSession.findUnique({
        where: { id: finalMatch?.chatSessionId! }
      });
      expect(chatSession).toBeDefined();
      expect(chatSession?.userOneId).toBe(users[0].id);
      expect(chatSession?.userTwoId).toBe(users[1].id);
    });

    it('should handle workflow: create -> decline -> backoff -> fresh matching', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const userC = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userc',
          reputationLevel: 3
        }
      });

      // Create offers
      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'CHF-EUR',
          amount: 1500,
          baseRate: 0.92,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'CHF-EUR',
          amount: 1500,
          baseRate: 0.92,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerC = await prisma.offer.create({
        data: {
          userId: userC.id,
          type: OfferType.SELL,
          currencyPair: 'CHF-EUR',
          amount: 1500,
          baseRate: 0.92,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Step 1: Create and decline match between A and B
      const matchAB = await matchingService.createMatch(offerA, offerB);
      await matchingService.declineMatch(matchAB.id, userA.id, 'Changed my mind');

      // Step 2: Verify decline backoff prevents new match with B
      const matchesAfterDecline = await matchingService.findPotentialMatches(offerA.id);
      const matchWithB = matchesAfterDecline.find(m => m.offerBId === offerB.id);
      expect(matchWithB).toBeUndefined();

      // Step 3: But should still be able to match with C
      const matchWithC = matchesAfterDecline.find(m => m.offerBId === offerC.id);
      expect(matchWithC).toBeDefined();

      // Step 4: Accept match with C
      const matchAC = await matchingService.createMatch(offerA, offerC);
      await matchingService.acceptMatch(matchAC.id, userA.id);
      await matchingService.acceptMatch(matchAC.id, userC.id);

      // Step 5: Verify transaction created
      const finalMatch = await prisma.offerMatch.findUnique({
        where: { id: matchAC.id }
      });
      expect([MatchStatus.BOTH_ACCEPTED, MatchStatus.CONVERTED]).toContain(finalMatch?.status);
      expect(finalMatch?.transactionId).toBeDefined();
    });

    it('should handle multiple simultaneous matches for same offer with proper cleanup', async () => {
      // Create one buyer and multiple sellers
      const buyer = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'buyer',
          reputationLevel: 5
        }
      });

      const sellers = await Promise.all([
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            password: 'hashedpassword',
            emailVerified: true,
            phoneVerified: true,
            username: 'seller1',
            reputationLevel: 4
          }
        }),
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            password: 'hashedpassword',
            emailVerified: true,
            phoneVerified: true,
            username: 'seller2',
            reputationLevel: 4
          }
        }),
        prisma.user.create({
          data: {
            email: '<EMAIL>',
            password: 'hashedpassword',
            emailVerified: true,
            phoneVerified: true,
            username: 'seller3',
            reputationLevel: 4
          }
        })
      ]);

      // Create one buy offer and multiple sell offers
      const buyOffer = await prisma.offer.create({
        data: {
          userId: buyer.id,
          type: OfferType.BUY,
          currencyPair: 'NOK-SEK',
          amount: 10000,
          baseRate: 0.95,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const sellOffers = await Promise.all(
        sellers.map(seller => 
          prisma.offer.create({
            data: {
              userId: seller.id,
              type: OfferType.SELL,
              currencyPair: 'NOK-SEK',
              amount: 10000,
              baseRate: 0.95,
              adjustmentForLowerRep: 0,
              adjustmentForHigherRep: 0,
              status: OfferStatus.ACTIVE
            }
          })
        )
      );

      // Create multiple matches
      const matches = await Promise.all(
        sellOffers.map(sellOffer => 
          matchingService.createMatch(buyOffer, sellOffer)
        )
      );

      // Verify all matches are pending
      matches.forEach(match => {
        expect(match.status).toBe(MatchStatus.PENDING);
      });

      // Simulate race condition: buyer accepts all matches simultaneously
      const acceptanceResults = await Promise.allSettled(
        matches.map(match => 
          matchingService.acceptMatch(match.id, buyer.id)
        )
      );

      // All buyer acceptances should succeed (partial accepts)
      const successfulBuyerAccepts = acceptanceResults.filter(r => r.status === 'fulfilled');
      expect(successfulBuyerAccepts.length).toBe(3);

      // One seller accepts to complete a match
      await matchingService.acceptMatch(matches[0].id, sellers[0].id);

      // Verify the completed match
      const completedMatch = await prisma.offerMatch.findUnique({
        where: { id: matches[0].id }
      });
      expect([MatchStatus.BOTH_ACCEPTED, MatchStatus.CONVERTED]).toContain(completedMatch?.status);

      // Verify other matches were cancelled
      const otherMatches = await Promise.all([
        prisma.offerMatch.findUnique({ where: { id: matches[1].id } }),
        prisma.offerMatch.findUnique({ where: { id: matches[2].id } })
      ]);

      otherMatches.forEach(match => {
        expect(match?.status).toBe(MatchStatus.CANCELLED);
      });
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle expired matches', async () => {
      // Create test users and offers
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR', // Keep consistent or change to CAD-IRR if preferred for all tests
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR', // Keep consistent
          amount: 1000, // Amount of base currency
          baseRate: 0.85, // Rate Quote/Base
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });
      
      const matchId = `MATCH_${Date.now()}_TEST`; // Use a test-specific matchId

      // Create an expired match (manually set past expiry)
      const expiredDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago
      const expiredMatch = await prisma.offerMatch.create({
        data: {
          matchId: matchId, // Provide the matchId
          offerAId: offerA.id,
          offerBId: offerB.id,
          userAId: userA.id,
          userBId: userB.id,
          currencyA: offerA.currencyPair.split('-')[0],
          currencyB: offerA.currencyPair.split('-')[1],
          amountA: offerA.amount,
          amountB: offerB.amount,
          rateAToB: offerA.baseRate,
          rateBToA: offerB.baseRate,
          status: MatchStatus.PENDING,
          userAResponse: null,
          userBResponse: null,
          createdAt: expiredDate,
          expiresAt: new Date(expiredDate.getTime() + 24 * 60 * 60 * 1000) // 24 hours from creation
        }
      });

      // Test cleanup of expired matches
      await matchingService.cleanupExpiredMatches();

      // Check that expired match was updated
      const updatedMatch = await prisma.offerMatch.findUnique({
        where: { id: expiredMatch.id }
      });

      expect(updatedMatch?.status).toBe(MatchStatus.EXPIRED);
    });

    it('should handle non-existent match', async () => {
      const fakeMatchId = 'fake-match-id';
      const fakeUserId = 'fake-user-id';

      await expect(
        matchingService.acceptMatch(fakeMatchId, fakeUserId)
      ).rejects.toThrow();
    });

    it('should handle malformed match data gracefully', async () => {
      const fakeMatchId = 'fake-match-id';
      const fakeUserId = 'fake-user-id';

      await expect(
        matchingService.acceptMatch(fakeMatchId, fakeUserId)
      ).rejects.toThrow();
    });

    it('should prevent duplicate match responses', async () => {
      // Create test users and match
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const match = await matchingService.createMatch(offerA, offerB);

      // User A accepts
      await matchingService.acceptMatch(match.id, userA.id);

      // User A tries to accept again - should throw error
      await expect(
        matchingService.acceptMatch(match.id, userA.id)
      ).rejects.toThrow('You have already responded to this match');
    });

    it('should handle unauthorized match operations', async () => {
      // Create test users and match
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const userC = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userc',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const match = await matchingService.createMatch(offerA, offerB);

      // User C (not part of match) tries to accept - should throw error
      await expect(
        matchingService.acceptMatch(match.id, userC.id)
      ).rejects.toThrow('Unauthorized to accept this match');

      // User C tries to decline - should throw error
      await expect(
        matchingService.declineMatch(match.id, userC.id)
      ).rejects.toThrow('Unauthorized to decline this match');
    });

    it('should handle expired match operations', async () => {
      // Create test users
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      // Create an expired match manually
      const expiredDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago
      const expiredMatch = await prisma.offerMatch.create({
        data: {
          matchId: `MATCH_${Date.now()}_EXPIRED`,
          offerAId: offerA.id,
          offerBId: offerB.id,
          userAId: userA.id,
          userBId: userB.id,
          currencyA: 'USD',
          currencyB: 'EUR',
          amountA: 1000,
          amountB: 850,
          rateAToB: 0.85,
          rateBToA: 1.176,
          status: MatchStatus.PENDING,
          createdAt: expiredDate,
          expiresAt: new Date(expiredDate.getTime() + 24 * 60 * 60 * 1000), // 24 hours from creation (so expired)
          updatedAt: expiredDate
        }
      });

      // Trying to accept expired match should throw error
      await expect(
        matchingService.acceptMatch(expiredMatch.id, userA.id)
      ).rejects.toThrow('Match has expired');
    });

    it('should handle match state validation correctly', async () => {
      // Create test users and match
      const userA = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'usera',
          reputationLevel: 3
        }
      });

      const userB = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          emailVerified: true,
          phoneVerified: true,
          username: 'userb',
          reputationLevel: 3
        }
      });

      const offerA = await prisma.offer.create({
        data: {
          userId: userA.id,
          type: OfferType.BUY,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const offerB = await prisma.offer.create({
        data: {
          userId: userB.id,
          type: OfferType.SELL,
          currencyPair: 'USD-EUR',
          amount: 1000,
          baseRate: 0.85,
          adjustmentForLowerRep: 0,
          adjustmentForHigherRep: 0,
          status: OfferStatus.ACTIVE
        }
      });

      const match = await matchingService.createMatch(offerA, offerB);

      // Decline the match
      await matchingService.declineMatch(match.id, userA.id, 'Testing state validation');

      // Trying to accept already declined match should throw error
      await expect(
        matchingService.acceptMatch(match.id, userB.id)
      ).rejects.toThrow('Match is no longer available');

      // Trying to decline already declined match should throw error
      await expect(
        matchingService.declineMatch(match.id, userB.id, 'Second decline')
      ).rejects.toThrow('Match is no longer available');
    });
  });
});
