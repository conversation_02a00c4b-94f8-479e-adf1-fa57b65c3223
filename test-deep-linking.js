#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Deep Linking Notifications Feature
 * 
 * This script runs all tests related to the deep linking functionality
 * and provides a detailed report of the test results.
 */

import { spawn } from 'child_process'
import { existsSync } from 'fs'
import path from 'path'

const FRONTEND_DIR = 'C:\\Code\\MUNygo\\frontend'
const BACKEND_DIR = 'C:\\Code\\MUNygo\\backend'

interface TestResult {
  name: string
  passed: boolean
  output: string
  duration: number
}

class TestRunner {
  private results: TestResult[] = []

  async runTest(name: string, command: string, args: string[], cwd: string): Promise<TestResult> {
    console.log(`\n🧪 Running ${name}...`)
    const startTime = Date.now()

    return new Promise((resolve) => {
      const child = spawn(command, args, {
        cwd,
        shell: true,
        stdio: ['pipe', 'pipe', 'pipe']
      })

      let output = ''
      let errorOutput = ''

      child.stdout?.on('data', (data) => {
        output += data.toString()
      })

      child.stderr?.on('data', (data) => {
        errorOutput += data.toString()
      })

      child.on('close', (code) => {
        const duration = Date.now() - startTime
        const passed = code === 0
        const fullOutput = output + errorOutput

        console.log(passed ? '✅ PASSED' : '❌ FAILED')
        if (!passed) {
          console.log('Error output:', errorOutput)
        }

        const result: TestResult = {
          name,
          passed,
          output: fullOutput,
          duration
        }

        this.results.push(result)
        resolve(result)
      })

      child.on('error', (error) => {
        const duration = Date.now() - startTime
        const result: TestResult = {
          name,
          passed: false,
          output: error.message,
          duration
        }

        this.results.push(result)
        resolve(result)
      })
    })
  }

  async runAllTests() {
    console.log('🚀 Starting Deep Linking Notification Tests')
    console.log('='.repeat(50))

    // Check if directories exist
    if (!existsSync(FRONTEND_DIR)) {
      console.error(`❌ Frontend directory not found: ${FRONTEND_DIR}`)
      return
    }

    if (!existsSync(BACKEND_DIR)) {
      console.error(`❌ Backend directory not found: ${BACKEND_DIR}`)
      return
    }

    // Frontend Tests
    console.log('\n📱 FRONTEND TESTS')
    console.log('-'.repeat(30))

    await this.runTest(
      'NotificationBell Component Tests',
      'npm',
      ['test', 'src/test/components/NotificationBell.test.ts'],
      FRONTEND_DIR
    )

    await this.runTest(
      'OfferDetailsView Component Tests',
      'npm',
      ['test', 'src/test/views/OfferDetailsView.test.ts'],
      FRONTEND_DIR
    )

    await this.runTest(
      'OfferService Tests',
      'npm',
      ['test', 'src/test/services/offerService.test.ts'],
      FRONTEND_DIR
    )

    await this.runTest(
      'Deep Linking Integration Tests',
      'npm',
      ['test', 'src/test/integration/deepLinking.test.ts'],
      FRONTEND_DIR
    )

    // Backend Tests
    console.log('\n🔧 BACKEND TESTS')
    console.log('-'.repeat(30))

    await this.runTest(
      'Offer Details Endpoint Tests',
      'npm',
      ['test', 'src/test/routes/offerDetails.test.ts'],
      BACKEND_DIR
    )

    // Optional: Run type checking
    console.log('\n🔍 TYPE CHECKING')
    console.log('-'.repeat(30))

    await this.runTest(
      'Frontend Type Check',
      'npx',
      ['tsc', '--noEmit'],
      FRONTEND_DIR
    )

    await this.runTest(
      'Backend Type Check',
      'npx',
      ['tsc', '--noEmit'],
      BACKEND_DIR
    )

    this.generateReport()
  }

  generateReport() {
    console.log('\n📊 TEST RESULTS SUMMARY')
    console.log('='.repeat(50))

    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0)

    console.log(`Total Tests: ${totalTests}`)
    console.log(`✅ Passed: ${passedTests}`)
    console.log(`❌ Failed: ${failedTests}`)
    console.log(`⏱️  Total Duration: ${totalDuration}ms`)
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

    if (failedTests > 0) {
      console.log('\n❌ FAILED TESTS:')
      console.log('-'.repeat(30))
      this.results
        .filter(r => !r.passed)
        .forEach(result => {
          console.log(`• ${result.name} (${result.duration}ms)`)
          if (result.output.length < 500) {
            console.log(`  ${result.output.slice(0, 200)}...`)
          }
        })
    }

    console.log('\n✅ PASSED TESTS:')
    console.log('-'.repeat(30))
    this.results
      .filter(r => r.passed)
      .forEach(result => {
        console.log(`• ${result.name} (${result.duration}ms)`)
      })

    console.log('\n🎯 DEEP LINKING FEATURES TESTED:')
    console.log('-'.repeat(40))
    console.log('• ✅ Notification click handling')
    console.log('• ✅ Entity-based navigation (OFFER, CHAT_SESSION, TRANSACTION)')
    console.log('• ✅ Fallback type-based navigation')
    console.log('• ✅ Legacy notification support (data.offerId, data.chatSessionId)')
    console.log('• ✅ Error handling (navigation failures, missing entities)')
    console.log('• ✅ Offer details loading and display')
    console.log('• ✅ Interest management in offer details')
    console.log('• ✅ Owner vs visitor permissions')
    console.log('• ✅ Backend API endpoint security and data integrity')
    console.log('• ✅ Multiple notification types and statuses')

    console.log('\n🚀 NEXT STEPS:')
    console.log('-'.repeat(20))
    
    if (failedTests === 0) {
      console.log('• ✅ All tests passing! Ready for manual testing')
      console.log('• 🔄 Test the deep linking flow end-to-end:')
      console.log('  1. Start backend: cd backend && npm run dev')
      console.log('  2. Start frontend: cd frontend && npm run dev')
      console.log('  3. Create notifications and test clicking them')
      console.log('  4. Verify navigation to correct pages')
      console.log('  5. Test error scenarios (deleted offers, etc.)')
    } else {
      console.log('• ❌ Fix failing tests before proceeding')
      console.log('• 🔍 Review error outputs above')
      console.log('• 🛠️  Address any type errors or logic issues')
    }

    console.log('\n📋 MANUAL TESTING CHECKLIST:')
    console.log('-'.repeat(35))
    console.log('• [ ] Click notification with OFFER entity → navigates to /offers/:id')
    console.log('• [ ] Click notification with CHAT_SESSION entity → navigates to /chat/:id')
    console.log('• [ ] Click notification with TRANSACTION entity → navigates to /transactions/:id')
    console.log('• [ ] Click legacy notification with data.offerId → navigates correctly')
    console.log('• [ ] Click legacy notification with data.chatSessionId → navigates correctly')
    console.log('• [ ] Click notification without entity data → uses type fallback')
    console.log('• [ ] Offer details page loads correctly for visitors')
    console.log('• [ ] Offer details page loads correctly for owners')
    console.log('• [ ] Express interest button works for visitors')
    console.log('• [ ] Edit/status buttons work for owners')
    console.log('• [ ] Error handling for deleted/missing offers')
    console.log('• [ ] Notifications are marked as read after clicking')
    console.log('• [ ] Mobile responsive design works correctly')

    // Exit with appropriate code
    process.exit(failedTests > 0 ? 1 : 0)
  }
}

// Run the tests
const runner = new TestRunner()
runner.runAllTests().catch(error => {
  console.error('❌ Test runner failed:', error)
  process.exit(1)
})
