import { describe, it, expect, vi, beforeEach, afterEach, type Mock } from 'vitest';
import { mount, flushPromises, VueWrapper } from '@vue/test-utils';
import { ref, computed, nextTick } from 'vue';
import ProfileView from '@/views/ProfileView/ProfileView.vue';
import { createTesting<PERSON>inia } from '@pinia/testing';
import type { UserInfo } from '@/types/auth'; // Assuming this path is correct
import type { Ref } from 'vue';

// --- Mocks (Similar structure to otpActions.spec.ts) ---

// Mock Naive UI
const messageSuccessSpy = vi.fn();
const messageErrorSpy = vi.fn(); // This spy is used in the assertion

vi.mock('naive-ui', async (importOriginal) => {
  const originalNaive = await importOriginal<typeof import('naive-ui')>();
  return {
    ...originalNaive,
    // Ensure useMessage is mocked to provide your spies
    useMessage: () => ({
      success: messageSuccessSpy,
      error: messageErrorSpy,
      // Add other message types if used by handleError or the component
    }),
    // Stub components that might interfere or are complex
    NSpin: { template: '<div><slot /></div>' },
    NCard: { template: '<div><slot /></div>' },
    NDescriptions: { template: '<div><slot /></div>' },
    NDescriptionsItem: { template: '<div><slot /></div>' },
    NTag: { template: '<span><slot /></span>' },
    NDivider: { template: '<hr />' },
    NH3: { template: '<h3><slot /></h3>' },
    NAlert: { template: '<div><slot /></div>' },
    // ---> FIX: Enhance NForm stub with a mock validate method <---
    NForm: {
        // Pass through v-show style and stub validate/restoreValidation
        props: ['style'],
        template: '<form :style="style" @submit.prevent><slot /></form>',
        methods: {
            validate: vi.fn().mockResolvedValue(undefined),
            restoreValidation: vi.fn()
        }
    },
    NFormItem: { template: '<div><slot name="label" /><slot /></div>' },
    // ---> FIX: Enhance NInput stub with mock focus method <---
    NInput: {
        inheritAttrs: false,
        props: ['value', 'disabled'],
        emits: ['update:value'],
        template: '<input v-bind="$attrs" :value="value" :disabled="disabled" @input="$emit(\'update:value\', $event.target.value)" />',
        methods: {
            focus: vi.fn() // Mock focus method
        }
    },
    // -------------------------------------------------------
    NButton: { props: ['disabled'], template: '<button :disabled="disabled"><slot /></button>' },
    NIcon: { template: '<i></i>' },
    NTooltip: { template: '<span><slot name="trigger" /><slot /></span>' },
  };
});

// Mock apiClient
vi.mock('@/services/apiClient', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn() // Add get for fetchUserProfile
  }
}));

// Mock errorHandler
vi.mock('@/utils/errorHandler', () => ({
  handleError: vi.fn((err: any, messageMock: any, defaultMessage?: string) => {
    // This logic should mirror your actual errorHandler.ts
    let displayMessage = defaultMessage || 'An unknown error occurred in test'; // Fallback

    if (err) {
      if (err.response && err.response.data && (err.response.data.message || err.response.data.error)) {
        displayMessage = err.response.data.message || err.response.data.error;
      } else if (err.message) {
        displayMessage = err.message; // This path will be taken for new Error('Failed to fetch profile')
      }
      // Add other conditions from your actual handleError if necessary (e.g., err.response.statusText)
    }

    // If a message mock (like Naive UI's message api) is provided, call its error method
    if (messageMock && typeof messageMock.error === 'function') {
      messageMock.error(displayMessage);
    }
    
    return displayMessage; // The actual handleError returns the string
  })
}));

// Mock auth store
const mockUserRef = ref<UserInfo | null>(null);
const fetchUserProfileMock = vi.fn();
const updatePhoneVerificationStatusMock = vi.fn();
vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    user: mockUserRef,
    fetchUserProfile: fetchUserProfileMock,
    isLoggedIn: computed(() => !!mockUserRef.value),
    updatePhoneVerificationStatus: updatePhoneVerificationStatusMock,
  }),
}));

// Import AFTER mocks
import apiClient from '@/services/apiClient';
import { handleError } from '@/utils/errorHandler';

// Assign mocks
const apiClientPostMock = apiClient.post as Mock;
const apiClientGetMock = apiClient.get as Mock; // Mock for GET /auth/me
const handleApiErrorMock = handleError as Mock;

// --- Test Setup ---
let wrapper: VueWrapper<any>;
const testPhoneNumber = '+15559876543';
const testOtp = '654321';

// Function to setup the component mount
const setupTest = async (initialUser: UserInfo | null, fetchProfileSuccess = true) => {
  mockUserRef.value = initialUser; // Set initial store state

  // Mock fetchUserProfile based on success flag
  if (fetchProfileSuccess) {
    // If initialUser is provided, mock fetch to return that user
    // Otherwise, mock a default user or handle as needed
    const userToFetch = initialUser || { id: 'defaultUser', email: '<EMAIL>', emailVerified: true, phoneVerified: false };
    fetchUserProfileMock.mockResolvedValue(userToFetch);
    apiClientGetMock.mockResolvedValue({ data: userToFetch }); // Also mock the underlying API call if fetchUserProfile uses it directly
  } else {
    const fetchError = new Error('Failed to fetch profile');
    fetchUserProfileMock.mockRejectedValue(fetchError);
    apiClientGetMock.mockRejectedValue(fetchError); // Mock the underlying API call failure
  }

  wrapper = mount(ProfileView, {
    global: {
      plugins: [createTestingPinia({ createSpy: vi.fn })],
      // Stubs are defined in vi.mock('naive-ui', ...) above
    },
  });

  // Wait for component mount and potential initial fetch
  await flushPromises();
  await nextTick();
};

describe('ProfileView.vue - Initial Rendering & Happy Path Flow', () => {

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();
    messageSuccessSpy.mockClear();
    messageErrorSpy.mockClear();
    fetchUserProfileMock.mockClear();
    updatePhoneVerificationStatusMock.mockClear();
    apiClientPostMock.mockClear();
    apiClientGetMock.mockClear();
    handleApiErrorMock.mockClear();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  // --- Section I: Initial Rendering ---

  it('renders loading state initially', async () => {
    // Arrange: Mount without awaiting initial fetch immediately
    mockUserRef.value = null; // Start with no user
    fetchUserProfileMock.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100))); // Simulate delay
    apiClientGetMock.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

    wrapper = mount(ProfileView, {
      global: { plugins: [createTestingPinia({ createSpy: vi.fn })] },
    });

    // Assert: Check for loading state (assuming NSpin stub allows finding content)
    // This depends heavily on how NSpin is stubbed. If stubbed simply, check component state.
    expect(wrapper.vm.loadingProfile).toBe(true);
    // If NSpin stub renders conditionally: expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true);

    await flushPromises(); // Let the fetch complete
  });

  it('renders correctly when phone is NOT verified', async () => {
    // Arrange
    const user = { id: 'user1', email: '<EMAIL>', emailVerified: true, phoneVerified: false, phoneNumber: null };
    await setupTest(user);

    // Assert
    expect(wrapper.text()).toContain(user.email);
    expect(wrapper.find('[data-testid="phone-status-unverified"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="phone-verification-title"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="phone-form"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="otp-form"]').exists()).toBe(true); // Exists due to v-show
    expect(wrapper.find('[data-testid="otp-form"]').attributes('style')).toContain('display: none;');
    expect(wrapper.find('[data-testid="phone-verified-message"]').exists()).toBe(false);
  });

  it('renders correctly when phone IS verified', async () => {
    // Arrange
    const user = { id: 'user2', email: '<EMAIL>', emailVerified: true, phoneVerified: true, phoneNumber: testPhoneNumber };
    await setupTest(user);

    // Assert
    expect(wrapper.text()).toContain(user.email);
    expect(wrapper.find('[data-testid="phone-status-verified"]').exists()).toBe(true);
    expect(wrapper.text()).toContain(testPhoneNumber);
    expect(wrapper.find('[data-testid="phone-verification-title"]').exists()).toBe(false);
    expect(wrapper.find('[data-testid="phone-form"]').exists()).toBe(false); // v-if hides this section
    expect(wrapper.find('[data-testid="otp-form"]').exists()).toBe(false);   // v-if hides this section
    expect(wrapper.find('[data-testid="phone-verified-message"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="phone-verified-message"]').text()).toContain('Phone number successfully verified!');
  });

   it('handles profile fetch failure on mount', async () => {
    // Arrange
    await setupTest(null, false); // Pass false to simulate fetch failure

    // Assert
    expect(handleApiErrorMock).toHaveBeenCalled();
    // ---> FIX: Expect the actual error message passed by the mock <---
    expect(messageErrorSpy).toHaveBeenCalledWith('Failed to fetch profile');
    // -------------------------------------------------------------
    // Check that the main content area might be empty or show minimal info
    expect(wrapper.find('[data-testid="phone-verification-title"]').exists()).toBe(false); // Should not show verification if profile failed
  });

  // --- Section II: Happy Path Flow ---

  it('requests OTP successfully and shows OTP form', async () => {
    // Arrange
    const user = { id: 'user3', email: '<EMAIL>', emailVerified: true, phoneVerified: false, phoneNumber: null };
    await setupTest(user);
    apiClientPostMock.mockResolvedValue({ data: { message: 'OTP sent!' } });

    // ---> FIX: Remove .find('input') as data-testid is on the stubbed input <---
    const phoneInput = wrapper.find('[data-testid="phone-input"]'); // Assuming data-testid is on the stubbed <input>

    // Act: enter phone and click send OTP button
    await phoneInput.setValue(testPhoneNumber);
    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
    await sendOtpButton.trigger('click');
    await flushPromises();
    await nextTick();
    await nextTick();

    // Assert
    // Assert
    expect(apiClientPostMock).toHaveBeenCalledTimes(1);
    expect(apiClientPostMock).toHaveBeenCalledWith('/auth/phone/send-otp', { phoneNumber: testPhoneNumber });
    expect(messageSuccessSpy).toHaveBeenCalledWith('OTP sent!');
    // Phone form should be hidden via v-show
    expect(wrapper.find('[data-testid="phone-form"]').attributes('style')).toContain('display: none;');
    // OTP form should be visible
    expect(wrapper.find('[data-testid="otp-form"]').attributes('style') ?? '').not.toContain('display: none;');
    // Focus check is hard, trust nextTick focus logic for now
  });

   it('verifies OTP successfully and updates status', async () => {
    // Arrange: Start from OTP form state
    const user = { id: 'user4', email: '<EMAIL>', emailVerified: true, phoneVerified: false, phoneNumber: null };
    await setupTest(user);

    // Navigate to OTP form first (send OTP)
    apiClientPostMock.mockResolvedValueOnce({ data: { message: 'OTP sent!' } });
    const phoneInput = wrapper.find('[data-testid="phone-input"]');
    const sendOtpButton = wrapper.find('[data-testid="send-otp-button"]');
    await phoneInput.setValue(testPhoneNumber);
    await sendOtpButton.trigger('click');
    await flushPromises();
    await nextTick();
    await nextTick();
    apiClientPostMock.mockClear(); // Clear the mock for the next call
    messageSuccessSpy.mockClear();

    // Now, mock the verify step
    apiClientPostMock.mockResolvedValue({ data: { message: 'Phone verified!', phoneNumber: testPhoneNumber } });
    // Fill OTP and click verify
    const otpInput = wrapper.find('[data-testid="otp-input"]');
    const verifyButton = wrapper.find('[data-testid="verify-otp-button"]');
    await otpInput.setValue(testOtp);
    await verifyButton.trigger('click');
    await flushPromises(); // Wait for verify API call
    await nextTick();      // Wait for DOM updates (store update triggers watcher)
    await nextTick();      // Allow watcher/reset logic to run

    // Assert
    expect(apiClientPostMock).toHaveBeenCalledTimes(1);
    expect(apiClientPostMock).toHaveBeenCalledWith('/auth/phone/verify-otp', { otpCode: testOtp });
    expect(messageSuccessSpy).toHaveBeenCalledWith('Phone verified!');
    expect(updatePhoneVerificationStatusMock).toHaveBeenCalledWith(true, testPhoneNumber);

    // Need to manually update the store ref as the mock doesn't run the real logic
    mockUserRef.value = { ...user, phoneVerified: true, phoneNumber: testPhoneNumber };
    await nextTick(); // Allow component to react to store change

    // Assert UI updated to verified state
    expect(wrapper.find('[data-testid="phone-verified-message"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="phone-form"]').exists()).toBe(false); // v-if should remove it now
    expect(wrapper.find('[data-testid="otp-form"]').exists()).toBe(false);   // v-if should remove it now
  });

});