/**
 * NOTE: This service currently works with the basic PaymentReceivingInfo model.
 * After running the migration to add the new fields (currency, paymentMethodType, etc.),
 * run `npx prisma generate` to update the Prisma client types.
 * 
 * Enhanced fields to be added:
 * - currency: string
 * - paymentMethodType: PaymentMethodType enum
 * - swiftCode, iban, routingNumber, sortCode, bsb: optional strings
 * - isActive: boolean
 * - notes: string
 */

import { PrismaClient, PaymentReceivingInfo } from '@prisma/client';
import { ILogger } from '../utils/logger';

// Define PaymentMethodType locally - will match Prisma enum after migration
type PaymentMethodType = 'BANK_TRANSFER' | 'DIGITAL_WALLET' | 'CRYPTO_WALLET' | 'MOBILE_MONEY' | 'CASH_PICKUP';

export interface CreatePaymentMethodPayload {
  currency: string;
  paymentMethodType: PaymentMethodType;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  swiftCode?: string;
  iban?: string;
  routingNumber?: string;
  sortCode?: string;
  bsb?: string;
  notes?: string;
  isDefaultForUser?: boolean;
}

export interface UpdatePaymentMethodPayload extends Partial<CreatePaymentMethodPayload> {
  isActive?: boolean;
}

export interface PaymentMethodFilters {
  currency?: string;
  paymentMethodType?: PaymentMethodType;
  isActive?: boolean;
  isDefaultOnly?: boolean;
}

// Extended interface for enhanced payment method with validation
export interface PaymentMethodWithValidation {
  id: string;
  userId: string;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  currency: string;
  paymentMethodType: PaymentMethodType;
  swiftCode?: string | null;
  iban?: string | null;
  routingNumber?: string | null;
  sortCode?: string | null;
  bsb?: string | null;
  notes?: string | null;
  isDefaultForUser: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  validationStatus: 'complete' | 'incomplete' | 'error';
  missingFields: string[];
}

export class PaymentMethodService {
  constructor(
    private prisma: PrismaClient,
    private logger: ILogger
  ) {}

  /**
   * Get all payment methods for a user with optional filtering
   * NOTE: Currently works with basic PaymentReceivingInfo model
   */
  async getUserPaymentMethods(
    userId: string, 
    filters: PaymentMethodFilters = {}
  ): Promise<PaymentMethodWithValidation[]> {
    try {
      const whereClause: any = {
        userId,
        // isActive: true, // Will be enabled after migration
      };

      // Additional filters will be implemented after migration
      if (filters.isDefaultOnly) {
        whereClause.isDefaultForUser = true;
      }

      const paymentMethods = await this.prisma.paymentReceivingInfo.findMany({
        where: whereClause,
        orderBy: [
          { isDefaultForUser: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      // Transform to enhanced interface with default values
      return paymentMethods.map(method => this.transformToEnhanced(method));
    } catch (error) {
      this.logger.error('Error fetching user payment methods:', error);
      throw new Error('Failed to fetch payment methods');
    }
  }

  /**
   * Get payment methods grouped by currency
   */
  async getUserPaymentMethodsByCurrency(userId: string): Promise<Record<string, PaymentMethodWithValidation[]>> {
    const paymentMethods = await this.getUserPaymentMethods(userId);
    
    const groupedMethods: Record<string, PaymentMethodWithValidation[]> = {};
    
    paymentMethods.forEach(method => {
      if (!groupedMethods[method.currency]) {
        groupedMethods[method.currency] = [];
      }
      groupedMethods[method.currency].push(method);
    });

    return groupedMethods;
  }

  /**
   * Get default payment method for a specific currency
   */
  async getDefaultPaymentMethod(userId: string, currency: string): Promise<PaymentMethodWithValidation | null> {
    try {
      // For now, get the default method (ignoring currency filter until migration)
      const defaultMethod = await this.prisma.paymentReceivingInfo.findFirst({
        where: {
          userId,
          isDefaultForUser: true,
          // currency, // Will be enabled after migration
          // isActive: true
        }
      });

      return defaultMethod ? this.transformToEnhanced(defaultMethod) : null;
    } catch (error) {
      this.logger.error('Error fetching default payment method:', error);
      throw new Error('Failed to fetch default payment method');
    }
  }

  /**
   * Create a new payment method
   */
  async createPaymentMethod(
    userId: string, 
    payload: CreatePaymentMethodPayload
  ): Promise<PaymentMethodWithValidation> {
    try {
      // If this is being set as default, unset other defaults
      if (payload.isDefaultForUser) {
        await this.unsetDefaultForUser(userId);
      }

      const paymentMethod = await this.prisma.paymentReceivingInfo.create({
        data: {
          userId,
          bankName: payload.bankName,
          accountNumber: payload.accountNumber,
          accountHolderName: payload.accountHolderName,
          isDefaultForUser: payload.isDefaultForUser || false,
          // Enhanced fields will be added after migration:
          // currency: payload.currency.toUpperCase(),
          // paymentMethodType: payload.paymentMethodType,
          // swiftCode: payload.swiftCode || null,
          // iban: payload.iban || null,
          // routingNumber: payload.routingNumber || null,
          // sortCode: payload.sortCode || null,
          // bsb: payload.bsb || null,
          // notes: payload.notes || null,
          // isActive: true
        }
      });

      this.logger.info(`Payment method created for user ${userId}`);
      return this.transformToEnhanced(paymentMethod, payload);
    } catch (error) {
      this.logger.error('Error creating payment method:', error);
      throw new Error('Failed to create payment method');
    }
  }

  /**
   * Update an existing payment method
   */
  async updatePaymentMethod(
    userId: string,
    paymentMethodId: string,
    payload: UpdatePaymentMethodPayload
  ): Promise<PaymentMethodWithValidation> {
    try {
      // Verify ownership
      const existingMethod = await this.prisma.paymentReceivingInfo.findFirst({
        where: { id: paymentMethodId, userId }
      });

      if (!existingMethod) {
        throw new Error('Payment method not found or access denied');
      }

      // If setting as default, unset other defaults
      if (payload.isDefaultForUser) {
        await this.unsetDefaultForUser(userId);
      }

      const updatedMethod = await this.prisma.paymentReceivingInfo.update({
        where: { id: paymentMethodId },
        data: {
          ...(payload.bankName && { bankName: payload.bankName }),
          ...(payload.accountNumber && { accountNumber: payload.accountNumber }),
          ...(payload.accountHolderName && { accountHolderName: payload.accountHolderName }),
          ...(payload.isDefaultForUser !== undefined && { isDefaultForUser: payload.isDefaultForUser }),
          updatedAt: new Date()
          // Enhanced fields will be added after migration
        }
      });

      this.logger.info(`Payment method ${paymentMethodId} updated for user ${userId}`);
      return this.transformToEnhanced(updatedMethod, payload);
    } catch (error) {
      this.logger.error('Error updating payment method:', error);
      throw new Error('Failed to update payment method');
    }
  }

  /**
   * Delete (deactivate) a payment method
   */
  async deletePaymentMethod(userId: string, paymentMethodId: string): Promise<void> {
    try {
      // Verify ownership
      const existingMethod = await this.prisma.paymentReceivingInfo.findFirst({
        where: { id: paymentMethodId, userId }
      });

      if (!existingMethod) {
        throw new Error('Payment method not found or access denied');
      }

      // For now, hard delete. After migration, this will be soft delete with isActive: false
      await this.prisma.paymentReceivingInfo.delete({
        where: { id: paymentMethodId }
      });

      this.logger.info(`Payment method ${paymentMethodId} deleted for user ${userId}`);
    } catch (error) {
      this.logger.error('Error deleting payment method:', error);
      throw new Error('Failed to delete payment method');
    }
  }

  /**
   * Set a payment method as default
   */
  async setAsDefault(userId: string, paymentMethodId: string): Promise<PaymentMethodWithValidation> {
    try {
      const paymentMethod = await this.prisma.paymentReceivingInfo.findFirst({
        where: { id: paymentMethodId, userId }
      });

      if (!paymentMethod) {
        throw new Error('Payment method not found');
      }

      // Unset other defaults for this user
      await this.unsetDefaultForUser(userId);

      // Set this one as default
      const updatedMethod = await this.prisma.paymentReceivingInfo.update({
        where: { id: paymentMethodId },
        data: { isDefaultForUser: true, updatedAt: new Date() }
      });

      this.logger.info(`Payment method ${paymentMethodId} set as default`);
      return this.transformToEnhanced(updatedMethod);
    } catch (error) {
      this.logger.error('Error setting payment method as default:', error);
      throw new Error('Failed to set payment method as default');
    }
  }

  /**
   * Get available currencies that have payment methods configured
   */
  async getUserConfiguredCurrencies(userId: string): Promise<string[]> {
    try {
      // For now, return default currencies until migration adds currency field
      const methods = await this.prisma.paymentReceivingInfo.findMany({
        where: { userId }
      });

      // Temporary: assume IRR as default currency
      return methods.length > 0 ? ['IRR'] : [];
    } catch (error) {
      this.logger.error('Error fetching configured currencies:', error);
      throw new Error('Failed to fetch configured currencies');
    }
  }

  /**
   * Get payment method statistics for a user
   */
  async getUserPaymentMethodStats(userId: string): Promise<{
    totalMethods: number;
    activeMethodsByCurrency: Record<string, number>;
    incompleteMethodsCount: number;
    supportedCurrencies: string[];
  }> {
    try {
      const methods = await this.getUserPaymentMethods(userId);
      
      const activeMethodsByCurrency: Record<string, number> = {};
      let incompleteMethodsCount = 0;

      methods.forEach(method => {
        activeMethodsByCurrency[method.currency] = (activeMethodsByCurrency[method.currency] || 0) + 1;
        if (method.validationStatus === 'incomplete') {
          incompleteMethodsCount++;
        }
      });

      return {
        totalMethods: methods.length,
        activeMethodsByCurrency,
        incompleteMethodsCount,
        supportedCurrencies: Object.keys(activeMethodsByCurrency).sort()
      };
    } catch (error) {
      this.logger.error('Error fetching payment method stats:', error);
      throw new Error('Failed to fetch payment method statistics');
    }
  }

  /**
   * Private helper to unset default for a user (temporary until currency-based defaults)
   */
  private async unsetDefaultForUser(userId: string): Promise<void> {
    await this.prisma.paymentReceivingInfo.updateMany({
      where: {
        userId,
        isDefaultForUser: true
      },
      data: {
        isDefaultForUser: false,
        updatedAt: new Date()
      }
    });
  }

  /**
   * Private helper to transform basic PaymentReceivingInfo to enhanced interface
   */
  private transformToEnhanced(
    method: PaymentReceivingInfo, 
    additionalData?: Partial<CreatePaymentMethodPayload> | Partial<UpdatePaymentMethodPayload>
  ): PaymentMethodWithValidation {
    const currency = additionalData?.currency || 'IRR'; // Default currency
    const paymentMethodType = additionalData?.paymentMethodType || 'BANK_TRANSFER' as PaymentMethodType;
    
    const missingFields: string[] = [];
    
    // Check required fields
    if (!method.bankName) missingFields.push('bankName');
    if (!method.accountNumber) missingFields.push('accountNumber');
    if (!method.accountHolderName) missingFields.push('accountHolderName');

    const validationStatus = missingFields.length === 0 ? 'complete' : 'incomplete';

    // Type guard to check if additionalData has isActive property
    const isActiveValue = (additionalData as UpdatePaymentMethodPayload)?.isActive;

    return {
      id: method.id,
      userId: method.userId,
      bankName: method.bankName,
      accountNumber: method.accountNumber,
      accountHolderName: method.accountHolderName,
      currency,
      paymentMethodType,
      swiftCode: additionalData?.swiftCode || null,
      iban: additionalData?.iban || null,
      routingNumber: additionalData?.routingNumber || null,
      sortCode: additionalData?.sortCode || null,
      bsb: additionalData?.bsb || null,
      notes: additionalData?.notes || null,
      isDefaultForUser: method.isDefaultForUser,
      isActive: isActiveValue !== false, // Default to true
      createdAt: method.createdAt,
      updatedAt: method.updatedAt,
      validationStatus,
      missingFields
    };
  }
}
