{"recordBugReport": "🎤 Record Bug Report with AI", "aiEnabled": "AI Available", "aiDisabled": "AI Unavailable", "notSupported": "Voice recording is not supported in your browser. Please use a modern browser with microphone support.", "permissionDenied": "Microphone access denied. Please allow microphone access to record voice reports.", "noMicrophone": "No microphone found. Please connect a microphone and try again.", "recordingError": "Recording failed. Please try again.", "maxDurationReached": "Maximum recording duration of {duration} seconds reached.", "paused": "Paused", "nearMaxDuration": "{remaining}s remaining", "playback": "Play", "delete": "Delete", "resend": "Resend", "noRecordingToResend": "No recording available to resend.", "transcribing": "Converting speech to text...", "analyzing": "AI is analyzing your report...", "processing": "Processing...", "transcription": "Voice Transcription", "aiGeneratedReport": "AI Generated Report", "confidence": "{confidence}% confidence", "applyToForm": "Apply to Form", "appliedToForm": "Auto-Applied", "transcriptionComplete": "Voice transcription completed successfully!", "analysisComplete": "AI analysis completed with {confidence}% confidence!", "analysisCompleteAndApplied": "AI analysis completed with {confidence}% confidence and automatically applied to form!", "reportApplied": "AI-generated report applied to form successfully!", "processingError": "Voice processing failed. Please try again.", "playbackError": "Audio playback failed.", "serviceNotAvailable": "AI voice service is not available. Please fill out the form manually.", "analysisFailedTitle": "Voice Analysis Failed", "improvementSuggestions": "Suggestions to improve your recording:", "detectedSpeech": "What we detected:", "analysisFailures": {"audioTooShort": "The recording is too short for analysis. Please record for at least 3-5 seconds.", "audioUnclear": "The audio quality is too poor for analysis. Please record in a quieter environment.", "speechNotDetected": "No clear speech was detected in the recording. Please ensure you're speaking clearly.", "irrelevantContent": "The content doesn't appear to be a bug report. Please describe the technical issue you're experiencing.", "backgroundNoise": "Too much background noise detected. Please record in a quieter environment.", "insufficientDetail": "The description lacks enough detail for analysis. Please provide more specific information about the problem.", "generic": "Unable to analyze the recording. Please try again or fill out the form manually."}, "suggestions": {"speakClearly": "Speak clearly and at a moderate pace", "quietEnvironment": "Record in a quiet environment", "holdCloser": "Hold the microphone closer to your mouth", "recordLonger": "Record for at least 3-5 seconds to ensure proper analysis", "describeTechnical": "Focus on describing the technical problem", "includeSteps": "Include the steps that led to the issue", "mentionError": "Mention any error messages you saw", "anyLanguage": "You can record in Persian, English, or any language you're comfortable with", "avoidBackgroundNoise": "Try to minimize background noise", "adjustMicrophone": "Adjust your microphone position", "useQuietLocation": "Move to a quieter location", "describeIssueClearly": "Describe the issue clearly and provide necessary details", "focusOnTechnicalDetails": "Focus on describing the technical problem you're experiencing"}}