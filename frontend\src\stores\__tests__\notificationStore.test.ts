import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActive<PERSON>inia, createP<PERSON> } from 'pinia';
import { useNotificationStore } from '../notificationStore';

// Mock the dependencies using factory functions
vi.mock('@/services/apiClient', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    patch: vi.fn()
  }
}));

vi.mock('@/services/centralizedSocketManager', () => ({
  default: {
    on: vi.fn(() => vi.fn()), // Return unsubscribe function
    off: vi.fn()
  }
}));

vi.mock('@/types/socketEvents', () => ({
  NEW_NOTIFICATION: 'NEW_NOTIFICATION'
}));

describe('NotificationStore', () => {
  let store: ReturnType<typeof useNotificationStore>;
  let mockApiClient: any;
  let mockCentralizedSocketManager: any;

  beforeEach(async () => {
    setActivePinia(createPinia());
    
    // Get the mocked modules
    const { default: apiClient } = await import('@/services/apiClient');
    const { default: centralizedSocketManager } = await import('@/services/centralizedSocketManager');
    
    mockApiClient = apiClient;
    mockCentralizedSocketManager = centralizedSocketManager;
    
    // Clear all mocks
    vi.clearAllMocks();
    
    // Create store instance
    store = useNotificationStore();
  });

  describe('fetchNotifications', () => {
    it('should fetch notifications successfully', async () => {
      const mockNotifications = [
        {
          id: '1',
          userId: 'user1',
          type: 'NEW_INTEREST_ON_YOUR_OFFER',
          message: 'Test notification',
          isRead: false,
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        }
      ];

      mockApiClient.get.mockResolvedValue({ data: mockNotifications });

      await store.fetchNotifications();

      expect(mockApiClient.get).toHaveBeenCalledWith('/notifications', {
        params: {
          limit: 20,
          unreadOnly: true
        }
      });
      expect(store.notifications).toEqual(mockNotifications);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBe(null);
    });

    it('should handle fetch notifications error', async () => {
      const errorMessage = 'Failed to fetch';
      mockApiClient.get.mockRejectedValue(new Error(errorMessage));

      await store.fetchNotifications();

      expect(store.notifications).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBe(errorMessage);
    });
  });

  describe('markNotificationAsRead', () => {
    it('should mark notification as read when API call succeeds', async () => {
      const notification = {
        id: '1',
        userId: 'user1',
        type: 'NEW_INTEREST_ON_YOUR_OFFER',
        message: 'Test notification',
        isRead: false,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      };
      
      store.notifications = [notification];
      mockApiClient.post.mockResolvedValue({ data: { ...notification, isRead: true } });

      await store.markNotificationAsRead('1');

      expect(mockApiClient.post).toHaveBeenCalledWith('/notifications/1/mark-read');
      expect(store.notifications[0].isRead).toBe(true);
    });

    it('should not mark notification as read when API call fails', async () => {
      const notification = {
        id: '1',
        userId: 'user1',
        type: 'NEW_INTEREST_ON_YOUR_OFFER',
        message: 'Test notification',
        isRead: false,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      };
      
      store.notifications = [notification];
      mockApiClient.post.mockRejectedValue(new Error('API Error'));

      await store.markNotificationAsRead('1');

      expect(mockApiClient.post).toHaveBeenCalledWith('/notifications/1/mark-read');
      expect(store.notifications[0].isRead).toBe(false);
    });
  });

  describe('markAllNotificationsAsRead', () => {
    it('should mark all notifications as read when API call succeeds', async () => {
      const notifications = [
        {
          id: '1',
          userId: 'user1',
          type: 'NEW_INTEREST_ON_YOUR_OFFER',
          message: 'Test notification 1',
          isRead: false,
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        },
        {
          id: '2',
          userId: 'user1',
          type: 'NEW_INTEREST_ON_YOUR_OFFER',
          message: 'Test notification 2',
          isRead: false,
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        }
      ];
      
      store.notifications = [...notifications];
      mockApiClient.post.mockResolvedValue({ data: { count: 2 } });

      await store.markAllNotificationsAsRead();

      expect(mockApiClient.post).toHaveBeenCalledWith('/notifications/mark-all-read');
      expect(store.notifications.every(n => n.isRead)).toBe(true);
    });

    it('should not mark notifications as read when API call fails', async () => {
      const notifications = [
        {
          id: '1',
          userId: 'user1',
          type: 'NEW_INTEREST_ON_YOUR_OFFER',
          message: 'Test notification 1',
          isRead: false,
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        },
        {
          id: '2',
          userId: 'user1',
          type: 'NEW_INTEREST_ON_YOUR_OFFER',
          message: 'Test notification 2',
          isRead: false,
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z'
        }
      ];
      
      store.notifications = [...notifications];
      mockApiClient.post.mockRejectedValue(new Error('API Error'));

      await store.markAllNotificationsAsRead();

      expect(mockApiClient.post).toHaveBeenCalledWith('/notifications/mark-all-read');
      expect(store.notifications.every(n => n.isRead)).toBe(false);
    });
  });

  describe('socket listeners', () => {
    it('should register socket listener for new notifications', () => {
      // The store should automatically initialize socket listeners
      expect(mockCentralizedSocketManager.on).toHaveBeenCalledWith(
        'NEW_NOTIFICATION',
        expect.any(Function)
      );
    });

    it('should handle new notification socket event', () => {
      // Get the callback function that was registered
      const onCalls = mockCentralizedSocketManager.on.mock.calls;
      const newNotificationCall = onCalls.find(call => call[0] === 'NEW_NOTIFICATION');
      expect(newNotificationCall).toBeDefined();
      
      const callback = newNotificationCall[1];
      expect(callback).toBeDefined();
      
      const newNotification = {
        id: '3',
        userId: 'user1',
        type: 'NEW_INTEREST_ON_YOUR_OFFER',
        message: 'New notification',
        isRead: false,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      };

      // Call the callback with new notification data
      callback(newNotification);

      expect(store.notifications).toContainEqual(newNotification);
    });
  });
});