import { describe, it, expect, vi, beforeEach, afterEach, type Mock } from 'vitest';
import { mount, flushPromises, VueWrapper } from '@vue/test-utils';
import { ref, computed, nextTick, type Ref } from 'vue';
import ProfileView from '@/views/ProfileView/ProfileView.vue';
import { createTestingPinia } from '@pinia/testing';
import type { UserInfo } from '@/types/auth';
// Import mocks from the new file
import {
  messageSuccessSpy as topLevelMessageSuccessSpy,
  messageErrorSpy as topLevelMessageErrorSpy,
  mockNFormValidate as topLevelMockNFormValidate,
  mockNFormRestoreValidation as topLevelMockNFormRestoreValidation,
  mockNInputFocus as topLevelMockNInputFocus,
} from './mocks';

// --- Mocks ---

vi.mock('naive-ui', async (importOriginal) => {
  // Import mocks directly inside the factory
  const {
    messageSuccessSpy,
    messageErrorSpy,
    mockNFormValidate,
    mockNFormRestoreValidation,
    mockNInputFocus,
  } = await import('./mocks');
  const originalNaive = await importOriginal<typeof import('naive-ui')>();
  return {
    ...originalNaive,
    useMessage: () => ({ success: messageSuccessSpy, error: messageErrorSpy }),
    NSpin: { template: '<div><slot /></div>' },
    NCard: { template: '<div><slot /></div>' },
    NDescriptions: { template: '<div><slot /></div>' },
    NDescriptionsItem: { template: '<div><slot /></div>' },
    NTag: { template: '<span><slot /></span>' },
    NDivider: { template: '<hr />' },
    NH3: { template: '<h3><slot /></h3>' },
    NAlert: { template: '<div><slot /></div>' },
    NForm: {
      template: '<form @submit.prevent><slot /></form>',
      methods: {
        validate: mockNFormValidate, // Use mock from factory scope
        restoreValidation: mockNFormRestoreValidation, // Use mock from factory scope
      },
    },
    NFormItem: { template: '<div><slot name="label" /><slot /></div>' },
    NInput: {
      props: ['value', 'disabled'],
      emits: ['update:value'],
      // Simplified template: removed TypeScript 'as' assertion
      template: '<input :value="value" :disabled="disabled" @input="$emit(\'update:value\', $event.target.value)" />',
      methods: {
        focus: mockNInputFocus, // Use mock from factory scope
      },
    },
    NButton: { props: ['disabled'], template: '<button :disabled="disabled"><slot /></button>' },
    NIcon: { template: '<i></i>' },
    NTooltip: { template: '<span><slot name="trigger" /><slot /></span>' },
  };
});

// Mock apiClient (only if needed for initial setup, likely not for these state tests)
vi.mock('@/services/apiClient', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn(),
  },
}));

// Mock errorHandler
vi.mock('@/utils/errorHandler', () => ({
  handleError: vi.fn((err: any, message: any, errorRef?: Ref<string | null>) => {
    const errorMessage = err?.response?.data?.message || err?.message || 'An unknown error occurred';
    if (message?.error) message.error(errorMessage);
    if (errorRef && typeof errorRef === 'object' && 'value' in errorRef) {
      errorRef.value = errorMessage;
    }
    return null;
  }),
}));

// Mock auth store
const mockUserRef = ref<UserInfo | null>(null);
const fetchUserProfileMock = vi.fn();
const updatePhoneVerificationStatusMock = vi.fn();

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    user: mockUserRef,
    fetchUserProfile: fetchUserProfileMock,
    isLoggedIn: computed(() => !!mockUserRef.value),
    updatePhoneVerificationStatus: updatePhoneVerificationStatusMock,
  }),
}));

// Import AFTER mocks
import apiClient from '@/services/apiClient';
const apiClientGetMock = apiClient.get as Mock;


// --- Test Setup ---
let wrapper: VueWrapper<any>;

const initialUserUnverified: UserInfo = {
  id: 'user1',
  email: '<EMAIL>',
  emailVerified: true,
  phoneVerified: false,
  phoneNumber: null,
  reputation: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const initialUserVerified: UserInfo = {
  id: 'user2',
  email: '<EMAIL>',
  emailVerified: true,
  phoneVerified: true,
  phoneNumber: '+15551234567',
  reputation: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const setupTest = async (initialUser: UserInfo | null) => {
  mockUserRef.value = initialUser;
  if (initialUser) {
    fetchUserProfileMock.mockResolvedValue(initialUser); // Mock initial fetch
    apiClientGetMock.mockResolvedValue({ data: initialUser });
  } else {
    fetchUserProfileMock.mockResolvedValue(null);
    apiClientGetMock.mockResolvedValue({ data: null });
  }


  wrapper = mount(ProfileView, {
    global: {
      plugins: [createTestingPinia({ createSpy: vi.fn })],
    },
  });
  await flushPromises();
  await nextTick();
};

describe('ProfileView.vue - State Management & Resetting', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    topLevelMessageSuccessSpy.mockClear();
    topLevelMessageErrorSpy.mockClear();
    topLevelMockNFormValidate.mockClear().mockResolvedValue(undefined);
    topLevelMockNFormRestoreValidation.mockClear();
    topLevelMockNInputFocus.mockClear();
    fetchUserProfileMock.mockClear();
    updatePhoneVerificationStatusMock.mockClear();
    apiClientGetMock.mockClear();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
    mockUserRef.value = null; // Reset store state
  });

  it('Scenario VII.20: resets verification state when user changes to a different user', async () => {
    // Arrange: Mount with an unverified user
    await setupTest({ ...initialUserUnverified });
    // Simulate some interaction that might change component state (e.g., show OTP form)
    wrapper.vm.showOtpForm = true;
    wrapper.vm.phoneFormValue.phoneNumber = '+12345';
    wrapper.vm.otpFormData.otpCode = '123';
    wrapper.vm.error = 'Some error';
    wrapper.vm.blockedUntil = Date.now() + 60000;
    await nextTick();

    // Act: Change the user in the store to a different user
    const differentUser: UserInfo = { ...initialUserUnverified, id: 'user-different', email: '<EMAIL>' };
    mockUserRef.value = differentUser;
    await flushPromises(); // Allow watchers to react
    await nextTick();
    await nextTick(); // Extra tick for DOM updates and potential restoreValidation in nextTick

    // Assert: Check that resetVerification was effectively called
    expect(wrapper.vm.showOtpForm).toBe(false);
    expect(wrapper.vm.phoneFormValue.phoneNumber).toBe('');
    expect(wrapper.vm.otpFormData.otpCode).toBe('');
    expect(wrapper.vm.error).toBe(null);
    expect(wrapper.vm.blockedUntil).toBe(null);
    expect(wrapper.vm.remainingAttempts).toBe(null);
    // Check if NForm's restoreValidation was called (it's mocked)
    // The resetVerification calls restoreValidation in a nextTick
    expect(topLevelMockNFormRestoreValidation).toHaveBeenCalledTimes(2);
  });

  it('Scenario VII.20: resets verification state when user logs out (user becomes null)', async () => {
    // Arrange: Mount with an unverified user
    await setupTest({ ...initialUserUnverified });
    wrapper.vm.showOtpForm = true;
    wrapper.vm.phoneFormValue.phoneNumber = '+12345';
    await nextTick();

    // Act: Set user to null in the store
    mockUserRef.value = null;
    await flushPromises();
    await nextTick();
    await nextTick();

    // Assert
    expect(wrapper.vm.showOtpForm).toBe(false);
    expect(wrapper.vm.phoneFormValue.phoneNumber).toBe('');
    // restoreValidation might not be called if the forms are removed from DOM when user is null
    // So, we don't assert its call count here, or expect it to be 0 if that's more accurate.
    // For now, let's assume it might not be called if forms are gone.
    // If resetVerification is expected to always attempt it, the component might need adjustment
    // or the mock setup for refs needs to ensure they exist even if detached.
    // Given the v-if="user", the forms are gone, so refs will be null.
    expect(topLevelMockNFormRestoreValidation).not.toHaveBeenCalled();
  });

  it('Scenario VII.21: updates to "Verified" state if user.phoneVerified changes externally from false to true', async () => {
    // Arrange: Mount with an unverified user
    await setupTest({ ...initialUserUnverified });
    expect(wrapper.find('[data-testid="phone-verification-title"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="phone-verified-message"]').exists()).toBe(false);

    // Act: Change phoneVerified status in the store
    const verifiedUserUpdate = { ...initialUserUnverified, phoneVerified: true, phoneNumber: '+15557890123' };
    mockUserRef.value = verifiedUserUpdate;
    await flushPromises();
    await nextTick();
    await nextTick();


    // Assert: UI should update to verified state
    expect(wrapper.find('[data-testid="phone-verification-title"]').exists()).toBe(false);
    expect(wrapper.find('[data-testid="phone-verified-message"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="phone-verified-message"]').text()).toContain('Phone number successfully verified!');
    expect(wrapper.find('[data-testid="phone-verified-message"]').text()).toContain(verifiedUserUpdate.phoneNumber);

    // Assert internal state reset
    expect(wrapper.vm.showOtpForm).toBe(false);
    // According to component logic, phoneFormValue.phoneNumber IS repopulated if user becomes verified.
    expect(wrapper.vm.phoneFormValue.phoneNumber).toBe(verifiedUserUpdate.phoneNumber);
    // restoreValidation will not be called because the forms are removed from the DOM
    // when isPhoneVerified becomes true.
    expect(topLevelMockNFormRestoreValidation).not.toHaveBeenCalled();
  });

  it('Scenario VII.21: updates to "Unverified" state if user.phoneVerified changes externally from true to false', async () => {
    // Arrange: Mount with a verified user
    await setupTest({ ...initialUserVerified });
    expect(wrapper.find('[data-testid="phone-verification-title"]').exists()).toBe(false);
    expect(wrapper.find('[data-testid="phone-verified-message"]').exists()).toBe(true);

    // Act: Change phoneVerified status in the store
    const unverifiedUserUpdate = { ...initialUserVerified, phoneVerified: false, phoneNumber: null };
    mockUserRef.value = unverifiedUserUpdate;
    await flushPromises();
    await nextTick();
    await nextTick();

    // Assert: UI should update to unverified state
    expect(wrapper.find('[data-testid="phone-verification-title"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="phone-form"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="phone-verified-message"]').exists()).toBe(false);

    // Assert internal state reset
    expect(wrapper.vm.showOtpForm).toBe(false);
    expect(wrapper.vm.phoneFormValue.phoneNumber).toBe('');
    expect(topLevelMockNFormRestoreValidation).toHaveBeenCalledTimes(2);
  });
});