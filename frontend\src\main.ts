import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import { createPinia } from 'pinia';
import { useAuthStore } from './stores/auth'; // Import useAuthStore
import { useLanguageStore } from './stores/language'; // Import useLanguageStore
import { useClientLogger } from './composables/useClientLogger'; // Import client logger
import { autofillHandler } from './utils/autofillHandler'; // Import autofill handler
import naive from 'naive-ui';
import { i18n } from './i18n'; // Import named i18n export

// Import FontAwesome CSS
import '@fortawesome/fontawesome-free/css/all.css';

// Import Transactional Chat CSS
import './styles/transactionalChat.css';

const app = createApp(App);
const pinia = createPinia();
app.use(pinia);

// Initialize client logger and set up global error handling
const logger = useClientLogger();

// Vue component error handling
app.config.errorHandler = (err, instance, info) => {
  logger.handleVueError(err, instance, info);
};

// Global JavaScript error handling
window.onerror = (message, source, lineno, colno, error) => {
  logger.handleGlobalError(message, source, lineno, colno, error);
  // Implicit return undefined allows default browser error handling consistently
};

// Unhandled promise rejection handling
window.onunhandledrejection = (event) => {
  logger.handleUnhandledRejection(event);
  // Implicit return undefined allows default browser error handling consistently
};

// Initialize autofill handler early to prevent DOM manipulation errors
autofillHandler.initialize();

// Log application startup
logger.logInfo('app' as 'app', {
  message: 'MUNygo application starting',
  userAgent: navigator.userAgent,
  url: window.location.href,
  timestamp: new Date().toISOString(),
});

// Initialize auth store and attempt to load auth state from localStorage
const authStore = useAuthStore();
authStore.initializeAuth();

// Initialize language store and set default language to Persian
const languageStore = useLanguageStore();
languageStore.initializeLanguage();

// Initialize i18n and sync with language store
app.use(i18n);
// TODO: Replace 'any' with proper type once new translation namespaces are fully supported
// The i18n.global type is complex due to the extensive message schema generics
const i18nGlobal = i18n.global as any;
i18nGlobal.locale.value = languageStore.currentLanguage;

// Note: Socket initialization is handled by AppContent.vue component
// to avoid duplicate initialization issues

app.use(router);
app.use(naive); // Use Naive UI

// Load connection testing utilities in development
if (import.meta.env.DEV) {
  import('./utils/connectionTester');
}

// Log successful app mount
logger.logInfo('MUNygo application mounted successfully');

app.mount('#app');
