import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import type { 
  PayerNegotiation, 
  PaymentReceivingInfo,
  PaymentReceivingSubmitPayload,
} from '@/types/payerNegotiation'; // PayerNegotiation type should include currentProposal_Message
import { ReceivingInfoStatus } from '@/types/payerNegotiation';
import apiClient from '@/services/apiClient';
import { handleError } from '@/utils/errorHandler';
import { useAuthStore } from './auth';
import centralizedSocketManager from '@/services/centralizedSocketManager';
import { 
  NEGOTIATION_STATE_UPDATED,
  NEGOTIATION_FINALIZED,
  type PayerNegotiationStatePayload,
  type NegotiationFinalizedPayload
} from '@/types/socketEvents';

export const usePayerNegotiationStore = defineStore('payerNegotiation', () => {
  const authStore = useAuthStore();
  const currentNegotiation = ref<PayerNegotiation | null>(null);
  const isLoading = ref(false);
  const storeError = ref<string | null>(null);

  // Socket event unsubscribe functions for cleanup
  let negotiationStateUnsubscribe: (() => void) | null = null;
  let negotiationFinalizedUnsubscribe: (() => void) | null = null;
  const _currentUserDefaultReceivingInfo = ref<PaymentReceivingInfo | null>(null);
  const isFetchingDefaultInfo = ref(false);

  // Watch for changes in auth store user to auto-sync payment info
  watch(
    () => authStore.user?.defaultPaymentReceivingInfo,
    (newPaymentInfo) => {
      if (newPaymentInfo) {
        _currentUserDefaultReceivingInfo.value = newPaymentInfo;
      }
    },
    { immediate: true }
  );

  const currentUserDefaultReceivingInfo = computed<PaymentReceivingInfo | null>(() => {
    return _currentUserDefaultReceivingInfo.value;
  });
  async function fetchCurrentUserDefaultPaymentInfo() {
    if (!authStore.isAuthenticated) {
      return;
    }

    if (isFetchingDefaultInfo.value) {
      return;
    }

    isFetchingDefaultInfo.value = true;
    try {
      // Get payment info from auth store which already has it from /auth/me
      if (authStore.user?.defaultPaymentReceivingInfo) {
        _currentUserDefaultReceivingInfo.value = authStore.user.defaultPaymentReceivingInfo;
      } else {
        // If not available in auth store, fetch user profile to get latest data
        await authStore.fetchUserProfile();
        _currentUserDefaultReceivingInfo.value = authStore.user?.defaultPaymentReceivingInfo || null;
      }
    } catch (err) {
      const errorMessage = handleError(err, undefined, 'Failed to fetch your default payment information.');
      storeError.value = errorMessage; 
    } finally {
      isFetchingDefaultInfo.value = false;
    }
  }
  const isPaymentGatePassedForCurrentUser = computed(() => {
    if (!currentNegotiation.value || !authStore.user?.id) {
      console.log('[PayerNegotiationStore] isPaymentGatePassedForCurrentUser: Missing data', {
        hasNegotiation: !!currentNegotiation.value,
        hasUserId: !!authStore.user?.id
      });
      return false;
    }
    const userId = authStore.user.id;
    // Corrected property names: partyA_Id and partyA_receivingInfoStatus
    if (currentNegotiation.value.partyA_Id === userId) { 
        const status = currentNegotiation.value.partyA_receivingInfoStatus;
        const isPassed = status === ReceivingInfoStatus.PROVIDED || status === ReceivingInfoStatus.CONFIRMED_FROM_PROFILE;
        console.log('[PayerNegotiationStore] isPaymentGatePassedForCurrentUser (PartyA):', {
          status,
          isPassed,
          ReceivingInfoStatus
        });
        return isPassed;
    }
    // Corrected property names: partyB_Id and partyB_receivingInfoStatus
    if (currentNegotiation.value.partyB_Id === userId) { 
        const status = currentNegotiation.value.partyB_receivingInfoStatus;
        const isPassed = status === ReceivingInfoStatus.PROVIDED || status === ReceivingInfoStatus.CONFIRMED_FROM_PROFILE;
        console.log('[PayerNegotiationStore] isPaymentGatePassedForCurrentUser (PartyB):', {
          status,
          isPassed,
          ReceivingInfoStatus
        });
        return isPassed;
    }
    console.log('[PayerNegotiationStore] isPaymentGatePassedForCurrentUser: User not in negotiation', {
      userId,
      partyA_Id: currentNegotiation.value.partyA_Id,
      partyB_Id: currentNegotiation.value.partyB_Id
    });
    return false;
  });

  const canCurrentUserPropose = computed(() => {
    // User can propose as soon as their own payment info is provided/confirmed, regardless of the other party
    if (!currentNegotiation.value || !isPaymentGatePassedForCurrentUser.value) return false;
    // Allow proposal if negotiation is in READY_TO_NEGOTIATE or PENDING_RESPONSE (if it's their turn or no proposal yet)
    // or if the current proposal was made by the other party (allowing counter-proposal)
    // This logic enables the UI as soon as the current user is ready, regardless of the other party
    return (
      currentNegotiation.value.negotiationStatus === 'READY_TO_NEGOTIATE' ||
      (currentNegotiation.value.negotiationStatus === 'PENDING_RESPONSE' &&
        (!currentNegotiation.value.currentProposal_ById || currentNegotiation.value.currentProposal_ById !== authStore.user?.id))
    );
  });
  
  const canCurrentUserAgree = computed(() => {
    // User can agree as soon as their own payment info is provided/confirmed, regardless of the other party
    if (!currentNegotiation.value || !authStore.user?.id || !isPaymentGatePassedForCurrentUser.value) return false;
    // Can agree if there is a proposal by the other party and negotiation is pending response
    return !!currentNegotiation.value.currentProposal_ById &&
      currentNegotiation.value.currentProposal_ById !== authStore.user.id &&
      currentNegotiation.value.negotiationStatus === 'PENDING_RESPONSE';
  });

  async function fetchNegotiation(transactionId: string) {
    console.log('[PayerNegotiationStore] fetchNegotiation called with transactionId:', transactionId); // DEBUG
    if (!transactionId) {
      console.error('[PayerNegotiationStore] fetchNegotiation: transactionId is missing.'); // DEBUG
      currentNegotiation.value = null; // Or appropriate default
      return;
    }
    isLoading.value = true;
    storeError.value = null;
    try {
      const apiUrl = `/transactions/${transactionId}/payer-negotiation`; // Verify this is your correct endpoint
      console.log('[PayerNegotiationStore] Fetching negotiation details from:', apiUrl); // DEBUG
      
      const response = await apiClient.get<{ data: PayerNegotiation }>(apiUrl); // Adjust based on your actual API response structure
      
      console.log('[PayerNegotiationStore] API Response Data:', response.data); // DEBUG
      
      // Assuming your API returns data directly or nested under a 'data' property
      // Adjust this line based on how your API structures the response
      const negotiationData = response.data.data || response.data; 

      console.log('[PayerNegotiationStore] Attempting to set currentNegotiation to:', negotiationData); // DEBUG
      currentNegotiation.value = negotiationData; 
      console.log('[PayerNegotiationStore] currentNegotiation after set:', currentNegotiation.value); // DEBUG

    } catch (error: any) {
      console.error('[PayerNegotiationStore] Error fetching negotiation details:', error); // DEBUG
      storeError.value = error.response?.data?.message || error.message || 'Failed to fetch negotiation details';
      currentNegotiation.value = null; // Ensure it's reset on error
    } finally {
      isLoading.value = false;
    }
  }  async function provideReceivingInfo(transactionId: string, info: PaymentReceivingSubmitPayload) {
    isLoading.value = true;
    storeError.value = null;
    
    // Debug what the store receives and what it sends to API
    console.log('🔍 [PayerNegotiationStore] Received info payload:', JSON.stringify(info, null, 2));
    console.log('🔍 [PayerNegotiationStore] info.isDefaultForUser:', info.isDefaultForUser);
    console.log('🔍 [PayerNegotiationStore] About to send to API endpoint:', `/transactions/${transactionId}/payer-negotiation/receiving-info`);
    
    try {
      const response = await apiClient.post<{ data: PayerNegotiation }>(`/transactions/${transactionId}/payer-negotiation/receiving-info`, info);
      currentNegotiation.value = response.data.data || response.data; // Corrected line
      console.log('[PayerNegotiationStore] provideReceivingInfo successful. Re-fetching negotiation state.'); // DEBUG
      await fetchNegotiation(transactionId); // Re-fetch to update currentNegotiation
      
      // If payment info was saved to profile, refresh the user profile to get updated default payment info
      if (info.saveToProfile) {
        console.log('[PayerNegotiationStore] Payment info saved to profile, refreshing user profile...'); // DEBUG
        try {
          await authStore.fetchUserProfile();
          console.log('[PayerNegotiationStore] User profile refreshed successfully'); // DEBUG
        } catch (profileErr) {
          console.warn('[PayerNegotiationStore] Failed to refresh user profile after saving payment info:', profileErr);
        }
      }
    } catch (err) {
      storeError.value = handleError(err, undefined, 'Failed to submit receiving information.');
      throw err;
    } finally {
      isLoading.value = false;
    }
  }
  
  async function proposeFirstPayer(transactionId: string, proposedPayerId: string, proposalMessage?: string) { // Add proposalMessage
    isLoading.value = true;
    storeError.value = null;
    try {
      console.log(`[PayerNegotiationStore] proposeFirstPayer: Proposing for txId: ${transactionId}, payer: ${proposedPayerId}`);
      const response = await apiClient.post<{ data: PayerNegotiation }>(
        `/transactions/${transactionId}/payer-negotiation/propose`, 
        { proposedPayerId, proposalMessage } // Send message in payload
      );
      console.log('[PayerNegotiationStore] proposeFirstPayer: API Response:', JSON.stringify(response, null, 2));
      
      const negotiationData = response.data.data || response.data;
      console.log('[PayerNegotiationStore] proposeFirstPayer: Extracted negotiationData:', JSON.stringify(negotiationData, null, 2));
      
      console.log('[PayerNegotiationStore] proposeFirstPayer: currentNegotiation BEFORE set:', JSON.stringify(currentNegotiation.value, null, 2));
      currentNegotiation.value = negotiationData;
      console.log('[PayerNegotiationStore] proposeFirstPayer: currentNegotiation AFTER set:', JSON.stringify(currentNegotiation.value, null, 2));

    } catch (err) {
      console.error('[PayerNegotiationStore] proposeFirstPayer: Error', err);
      storeError.value = handleError(err, undefined, 'Failed to propose first payer.');
      throw err; // Re-throw to be caught by UI component if needed
    } finally {
      isLoading.value = false;
    }
  }

  async function acceptCurrentProposal(transactionId: string) {
    isLoading.value = true;
    storeError.value = null;
    console.log(`[PayerNegotiationStore] acceptCurrentProposal: Attempting for txId: ${transactionId}`);
    try {
      const response = await apiClient.post<{ data: PayerNegotiation }>(`/transactions/${transactionId}/payer-negotiation/agree`);
      
      // Detailed logging of the response
      console.log('[PayerNegotiationStore] acceptCurrentProposal: Full API Response object:', JSON.stringify(response, null, 2));
      console.log('[PayerNegotiationStore] acceptCurrentProposal: API response.data:', JSON.stringify(response.data, null, 2));
      console.log('[PayerNegotiationStore] acceptCurrentProposal: API response.data.data (if exists):', JSON.stringify(response.data?.data, null, 2));

      const valueToAssign = response.data.data || response.data;
      console.log('[PayerNegotiationStore] acceptCurrentProposal: Value to be assigned to currentNegotiation:', JSON.stringify(valueToAssign, null, 2));
      
      console.log('[PayerNegotiationStore] acceptCurrentProposal: currentNegotiation BEFORE set:', JSON.stringify(currentNegotiation.value, null, 2));
      currentNegotiation.value = valueToAssign;
      console.log('[PayerNegotiationStore] acceptCurrentProposal: currentNegotiation AFTER set:', JSON.stringify(currentNegotiation.value, null, 2));

      if (typeof currentNegotiation.value === 'undefined') {
        console.error('[PayerNegotiationStore] acceptCurrentProposal: CRITICAL - currentNegotiation became undefined AFTER assignment!');
        console.log('[PayerNegotiationStore] acceptCurrentProposal: This was the response.data that led to undefined:', JSON.stringify(response.data, null, 2));
      }

    } catch (err) {
      console.error('[PayerNegotiationStore] acceptCurrentProposal: Error during API call or processing:', err);
      storeError.value = handleError(err, undefined, 'Failed to agree to proposal.');
      // It's possible an error during assignment or processing could lead to issues.
      // Let's log the state of currentNegotiation if an error occurs after potential assignment attempt.
      console.log('[PayerNegotiationStore] acceptCurrentProposal: currentNegotiation value in CATCH block:', JSON.stringify(currentNegotiation.value, null, 2));
      throw err;
    } finally {
      isLoading.value = false;
      console.log(`[PayerNegotiationStore] acceptCurrentProposal: Finished for txId: ${transactionId}. Loading: ${isLoading.value}`);
    }
  }

  async function cancelNegotiation(transactionId: string, reason: string) {
    isLoading.value = true
    storeError.value = null
    console.log(`[PayerNegotiationStore] cancelNegotiation: Attempting for txId: ${transactionId}`)
    try {
      const response = await apiClient.post<{ data: PayerNegotiation }>(`/transactions/${transactionId}/payer-negotiation/cancel`, {
        reason
      })
      
      console.log('[PayerNegotiationStore] cancelNegotiation: Full API Response object:', JSON.stringify(response, null, 2))
      
      const negotiationData = response.data.data || response.data
      currentNegotiation.value = negotiationData
      
      console.log('[PayerNegotiationStore] cancelNegotiation: Updated negotiation state:', currentNegotiation.value)
    } catch (err) {
      console.error('[PayerNegotiationStore] cancelNegotiation: Error during API call or processing:', err)
      storeError.value = handleError(err, undefined, 'Failed to cancel negotiation.')
      throw err
    } finally {
      isLoading.value = false
      console.log(`[PayerNegotiationStore] cancelNegotiation: Finished for txId: ${transactionId}. Loading: ${isLoading.value}`)
    }
  }

  // Setup socket listeners using centralized socket manager
  function setupSocketListeners() {
    console.log('[PayerNegotiationStore] Setting up socket listeners with centralized socket manager');
    
    // Clean up existing listeners
    if (negotiationStateUnsubscribe) {
      negotiationStateUnsubscribe();
      negotiationStateUnsubscribe = null;
    }
    if (negotiationFinalizedUnsubscribe) {
      negotiationFinalizedUnsubscribe();
      negotiationFinalizedUnsubscribe = null;
    }
    
    // Listen for negotiation state updates (when users provide info, agree to proposals, etc.)
    negotiationStateUnsubscribe = centralizedSocketManager.on(NEGOTIATION_STATE_UPDATED, (payload: PayerNegotiationStatePayload) => {
      console.log('[PayerNegotiationStore] Received NEGOTIATION_STATE_UPDATED:', payload);
      
      // Only update if this is the current negotiation we're tracking
      if (currentNegotiation.value && currentNegotiation.value.negotiationId === payload.negotiationId) {
        // Map the payload to our local type structure
        currentNegotiation.value = {
          ...currentNegotiation.value, // Preserve existing state
          partyA_receivingInfoStatus: payload.partyA_receivingInfoStatus,
          partyB_receivingInfoStatus: payload.partyB_receivingInfoStatus,
          partyA_PaymentReceivingInfo: payload.partyA_PaymentReceivingInfo,
          partyB_PaymentReceivingInfo: payload.partyB_PaymentReceivingInfo,
          currentProposal_PayerId: payload.currentProposal_PayerId,
          currentProposal_ById: payload.currentProposal_ById,
          currentProposal_Message: payload.currentProposal_Message,
          partyA_agreedToCurrentProposal: payload.partyA_agreedToCurrentProposal,
          partyB_agreedToCurrentProposal: payload.partyB_agreedToCurrentProposal,
          negotiationStatus: payload.negotiationStatus,
          isFinalOffer: payload.isFinalOffer, // Correctly map isFinalOffer
          updatedAt: payload.updatedAt
        };
        console.log('[PayerNegotiationStore] Negotiation state updated via socket:', currentNegotiation.value);
      }
    });

    // Listen for negotiation finalization (when agreement is reached and payment phase begins)
    negotiationFinalizedUnsubscribe = centralizedSocketManager.on(NEGOTIATION_FINALIZED, (payload: NegotiationFinalizedPayload) => {
      console.log('[PayerNegotiationStore] Received NEGOTIATION_FINALIZED:', payload);
      
      // Update the full negotiation state if this is our current negotiation
      if (currentNegotiation.value && currentNegotiation.value.negotiationId === payload.negotiationId) {
        // Update the entire negotiation state with the finalized data
        currentNegotiation.value = {
          ...currentNegotiation.value, // Preserve existing state
          finalizedPayerId: payload.finalizedPayerId,
          negotiationStatus: payload.negotiationStatus,
          paymentTimerDueDate: payload.paymentTimerDueDate,
          updatedAt: payload.updatedAt
        };
        console.log('[PayerNegotiationStore] Negotiation finalized, payment phase can begin');
        console.log('[PayerNegotiationStore] Updated negotiation state:', currentNegotiation.value);
      }
    });
  }
  // Initialize socket listeners when the store is first used
  function initializeSocketListeners() {
    console.log('[PayerNegotiationStore] Initializing socket listeners with centralized socket manager');
    setupSocketListeners();
  }

  // Cleanup socket listeners
  function cleanupSocketListeners() {
    console.log('[PayerNegotiationStore] Cleaning up socket listeners');
    if (negotiationStateUnsubscribe) {
      negotiationStateUnsubscribe();
      negotiationStateUnsubscribe = null;
    }
    if (negotiationFinalizedUnsubscribe) {
      negotiationFinalizedUnsubscribe();
      negotiationFinalizedUnsubscribe = null;
    }
  }

  // Call initialization when store is created
  if (authStore.isAuthenticated) {
    initializeSocketListeners();
  }
  // Watch for auth changes to initialize/cleanup socket listeners
  authStore.$onAction(({ name, after }) => {
    if (name === 'login') {
      after(() => {
        console.log('[PayerNegotiationStore] User logged in, initializing socket listeners');
        initializeSocketListeners();
      });
    } else if (name === 'logout') {
      after(() => {
        console.log('[PayerNegotiationStore] User logged out, clearing negotiation state');
        cleanupSocketListeners();
        currentNegotiation.value = null;
        storeError.value = null;
      });
    }
  });
  return {
    currentNegotiation,
    isLoading,
    storeError,

    currentUserDefaultReceivingInfo,
    fetchCurrentUserDefaultPaymentInfo,
    isFetchingDefaultInfo,    isPaymentGatePassedForCurrentUser,
    canCurrentUserPropose,
    canCurrentUserAgree,
    fetchNegotiation,
    provideReceivingInfo,
    proposeFirstPayer,
    acceptCurrentProposal,
    cancelNegotiation,
    initializeSocketListeners,
  };
});
