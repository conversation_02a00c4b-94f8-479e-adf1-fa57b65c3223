# Mobile-First Design Principles for MUNygo

## 🎯 Core Design Philosophy

### 1. Mobile-First, Always
- **Primary Target**: Mobile devices (320px - 768px)
- **Progressive Enhancement**: Desktop as enhancement, not requirement
- **Touch-First Interactions**: Every element designed for finger navigation
- **One-Handed Usage**: Critical actions within thumb reach

### 2. Radical Simplicity
- **Cognitive Load Reduction**: Maximum 3 options per screen
- **Single Purpose Screens**: Each screen has one primary goal
- **Progressive Disclosure**: Show only what's needed now
- **Clear Visual Hierarchy**: Obvious action priorities

### 3. Naive UI Mobile Optimization
- **Leverage Mobile-First Components**: Use `n-drawer` for navigation, `n-grid` with responsive breakpoints
- **Optimize Component Props**: `size="large"` for touch targets, `block` for full-width buttons
- **Mobile-Specific Patterns**: `n-back-top`, `n-affix` for mobile scrolling, `n-space` for consistent spacing
- **Touch-Friendly Interactions**: Utilize Naive UI's built-in touch optimizations and mobile behaviors

## 📱 Mobile UI Principles

### Touch Target Standards
```css
/* Minimum touch target sizes */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
}

.primary-button {
  min-height: 56px; /* Larger for primary actions */
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
}
```

### Thumb Zone Design
```
┌─────────────────┐ ← Hard to reach
│                 │
│                 │
│                 │ ← Easy to reach
│     [ACTION]    │ ← Primary actions here
│     [SECONDARY] │ ── Comfortable thumb zone
└─────────────────┘ ← Natural thumb position
```

### Responsive Breakpoints
```css
/* Mobile-first approach */
/* Base styles apply to mobile (320px+) */

/* Tablet enhancement */
@media (min-width: 768px) {
  /* Tablet-specific improvements */
}

/* Desktop enhancement */
@media (min-width: 1024px) {
  /* Desktop-specific improvements */
}
```

## 🎨 Comprehensive Visual Design System & Brand Identity

### Design Philosophy for P2P Currency Exchange

Our visual design system is meticulously crafted around the core psychological needs of financial technology users: **trust, security, clarity, and accessibility**. Every color choice, typography decision, and interactive element has been designed to convey reliability and professionalism while maintaining modern aesthetics and excellent mobile usability.

### Core Visual Principles

#### 1. Financial Trust Through Visual Language
- **Professional Foundation**: Clean lines, consistent spacing, premium typography
- **Security Indicators**: Visual cues that reinforce platform safety and reliability  
- **Transparency**: Clear visual hierarchy with no hidden or confusing elements
- **Approachability**: Warm, human touches that make finance less intimidating

#### 2. Cultural Sensitivity & Bilingual Excellence
- **Respectful Design**: Appropriate for Persian/English bilingual users
- **RTL/LTR Optimization**: Seamless experience in both text directions
- **Cultural Color Considerations**: Colors that work across cultural contexts

### Color Psychology & Brand Identity

#### Primary Brand Colors (Trust & Security)

**Deep Blue Family - "Financial Trust"**
```css
/* Primary Blues: Convey trust, security, stability */
--blue-900: #0F172A;  /* Deep navy - Headers, premium elements */
--blue-800: #1E293B;  /* Rich blue - Primary navigation, CTAs */
--blue-700: #334155;  /* Standard blue - Buttons, links */
--blue-600: #475569;  /* Balanced blue - Active states */
--blue-500: #64748B;  /* Medium blue - Hover states */
--blue-400: #94A3B8;  /* Light blue - Disabled elements */
--blue-300: #CBD5E1;  /* Subtle blue - Borders */
--blue-200: #E2E8F0;  /* Very light blue - Backgrounds */
--blue-100: #F1F5F9;  /* Whisper blue - Cards */
--blue-50: #F8FAFC;   /* Almost white - Page backgrounds */
```

**Accent Teal - "Growth & Innovation"**
```css
/* Teal Accents: Growth, prosperity, innovation */
--teal-700: #0F766E;  /* Deep teal - Premium features */
--teal-600: #0D9488;  /* Rich teal - Secondary CTAs */
--teal-500: #14B8A6;  /* Bright teal - Highlights, success elements */
--teal-400: #2DD4BF;  /* Light teal - Interactive states */
--teal-300: #7DD3FC;  /* Soft teal - Subtle accents */
--teal-100: #CCFBF1;  /* Whisper teal - Success backgrounds */
--teal-50: #F0FDFA;   /* Almost white teal - Subtle backgrounds */
```

#### Semantic Colors (Clear Communication)

**Success Green - "Completed & Positive"**
```css
/* Success: Completed transactions, positive outcomes */
--success-700: #15803D;  /* Deep success - Important completions */
--success-600: #16A34A;  /* Standard success - Confirmations */
--success-500: #22C55E;  /* Bright success - Celebrations */
--success-400: #4ADE80;  /* Light success - Positive indicators */
--success-100: #DCFCE7;  /* Success background - Subtle positive areas */
```

**Warning Amber - "Attention & Pending"**
```css
/* Warning: Pending states, requires attention */
--warning-700: #B45309;  /* Deep warning - Serious attention needed */
--warning-600: #D97706;  /* Standard warning - Normal alerts */
--warning-500: #F59E0B;  /* Bright warning - Pending transactions */
--warning-400: #FBBF24;  /* Light warning - Mild cautions */
--warning-100: #FEF3C7;  /* Warning background - Subtle caution areas */
```

**Error Red - "Problems & Declined"**
```css
/* Error: Failures, declined transactions, critical issues */
--error-700: #B91C1C;    /* Deep error - Critical failures */
--error-600: #DC2626;    /* Standard error - Normal errors */
--error-500: #EF4444;    /* Bright error - Validation errors */
--error-400: #F87171;    /* Light error - Mild issues */
--error-100: #FEE2E2;    /* Error background - Subtle error areas */
```

#### Professional Neutral Grays

**Gray Scale - "Professional Foundation"**
```css
/* Sophisticated grays for professional UI foundation */
--gray-900: #111827;  /* Deepest gray - Headers, important text */
--gray-800: #1F2937;  /* Dark gray - Primary text */
--gray-700: #374151;  /* Standard gray - Body text */
--gray-600: #4B5563;  /* Medium gray - Secondary text */
--gray-500: #6B7280;  /* Balanced gray - Placeholder text */
--gray-400: #9CA3AF;  /* Light gray - Disabled text */
--gray-300: #D1D5DB;  /* Subtle gray - Borders, dividers */
--gray-200: #E5E7EB;  /* Very light gray - Light backgrounds */
--gray-100: #F3F4F6;  /* Whisper gray - Card backgrounds */
--gray-50: #F9FAFB;   /* Almost white - Page backgrounds */
```

### Dark Mode Color System (OLED Optimized)

#### True Black for Premium Experience
```css
/* Dark mode: OLED-optimized, battery-saving, premium appearance */
[data-theme="dark"] {
  /* Background hierarchy */
  --background-page: #000000;      /* True black - OLED optimization */
  --background-elevated: #0A0A0A;  /* Slightly raised surfaces */
  --background-card: #111111;      /* Cards and containers */
  --background-input: #1A1A1A;     /* Form inputs */
  --background-modal: #000000;     /* Modals and overlays */
  
  /* Text hierarchy - High contrast for readability */
  --text-primary: rgba(255, 255, 255, 0.95);    /* Main content */
  --text-secondary: rgba(255, 255, 255, 0.80);  /* Secondary info */
  --text-tertiary: rgba(255, 255, 255, 0.60);   /* Subtle text */
  --text-disabled: rgba(255, 255, 255, 0.40);   /* Disabled elements */
  
  /* Adapted brand colors for dark mode */
  --primary-dark: #60A5FA;      /* Brighter blue for dark backgrounds */
  --accent-dark: #2DD4BF;       /* Brighter teal for visibility */
  --success-dark: #4ADE80;      /* Softer green for comfort */
  --warning-dark: #FBBF24;      /* Warmer amber for clarity */
  --error-dark: #F87171;        /* Softer red for less aggression */
  
  /* Border hierarchy */
  --border-subtle: rgba(255, 255, 255, 0.1);   /* Subtle separators */
  --border-standard: rgba(255, 255, 255, 0.2); /* Standard borders */
  --border-emphasis: rgba(255, 255, 255, 0.3); /* Emphasized borders */
}
```

### Typography System for Bilingual Excellence

#### Font Strategy & Implementation
```css
/* Optimized font stacks for bilingual users */
:root {
  /* English-optimized: Inter for maximum clarity and professionalism */
  --font-english: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  
  /* Persian-optimized: Vazirmatn for beautiful Persian rendering */
  --font-persian: 'Vazirmatn', 'Vazir', 'Tahoma', 'Iran Sans', 'B Nazanin', sans-serif;
  
  /* Monospace: Financial data, amounts, verification codes */
  --font-mono: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
}

/* Smart font switching based on language/direction */
[lang="en"], [dir="ltr"] { font-family: var(--font-english); }
[lang="fa"], [dir="rtl"] { font-family: var(--font-persian); }

/* Financial elements always use monospace for alignment */
.amount, .rate, .exchange-rate, .verification-code, .transaction-id {
  font-family: var(--font-mono);
  font-variant-numeric: tabular-nums; /* Aligned numbers */
  letter-spacing: 0.05em; /* Slight spacing for clarity */
}
```

#### Mobile-First Typography Scale
```css
/* Responsive typography optimized for mobile-first design */

/* Display text - Hero amounts, main CTAs */
.text-display {
  font-size: clamp(2rem, 6vw, 3.5rem);      /* 32px - 56px */
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.025em;
}

/* Headlines - Page titles, major sections */
.text-headline {
  font-size: clamp(1.5rem, 4vw, 2.25rem);   /* 24px - 36px */
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

/* Titles - Section headers, card titles */
.text-title {
  font-size: clamp(1.25rem, 3vw, 1.5rem);   /* 20px - 24px */
  font-weight: 600;
  line-height: 1.3;
}

/* Body text - Standard content */
.text-body {
  font-size: clamp(1rem, 2.5vw, 1.125rem);  /* 16px - 18px */
  font-weight: 400;
  line-height: 1.6;
}

/* Small text - Secondary information */
.text-small {
  font-size: clamp(0.875rem, 2vw, 1rem);    /* 14px - 16px */
  font-weight: 400;
  line-height: 1.5;
}

/* Caption text - Timestamps, subtle info */
.text-caption {
  font-size: clamp(0.75rem, 1.8vw, 0.875rem); /* 12px - 14px */
  font-weight: 500;
  line-height: 1.4;
  color: var(--text-tertiary);
}
```

### Component Visual Language

#### Interactive Elements
```css
/* Button system - Trust-focused design */
.btn-primary {
  background: linear-gradient(135deg, var(--blue-700) 0%, var(--blue-800) 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px 24px;
  font-weight: 600;
  font-size: 16px;
  min-height: 56px; /* Mobile-friendly touch target */
  
  /* Professional shadow */
  box-shadow: 0 2px 4px rgba(51, 65, 85, 0.2), 0 1px 2px rgba(51, 65, 85, 0.1);
  
  /* Smooth interactions */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(51, 65, 85, 0.3), 0 2px 6px rgba(51, 65, 85, 0.2);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(51, 65, 85, 0.2);
}

/* Mobile touch feedback */
@media (hover: none) {
  .btn-primary:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* Secondary button - Professional outline */
.btn-secondary {
  background: var(--background-card);
  color: var(--blue-700);
  border: 2px solid var(--blue-700);
  border-radius: 8px;
  padding: 14px 24px; /* Adjusted for border */
  font-weight: 600;
  min-height: 56px;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--blue-50);
  border-color: var(--blue-800);
  color: var(--blue-800);
}

/* Success button - For confirmations */
.btn-success {
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-700) 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px 24px;
  font-weight: 600;
  min-height: 56px;
  box-shadow: 0 2px 4px rgba(22, 163, 74, 0.2);
}
```

#### Cards & Containers
```css
/* Standard card - Professional elevation */
.card {
  background: var(--background-card);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid var(--border-standard);
  
  /* Subtle, professional shadow */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  
  /* Smooth interactions */
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

/* Premium card - For important content */
.card-premium {
  background: var(--background-card);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid var(--teal-200);
  
  /* Enhanced elevation */
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.06);
  
  /* Subtle gradient accent */
  background-image: linear-gradient(135deg, var(--background-card) 0%, var(--teal-50) 100%);
}

/* Dark mode card adaptations */
[data-theme="dark"] .card {
  background: var(--background-card);
  border-color: var(--border-subtle);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3), 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .card-premium {
  background: var(--background-card);
  border-color: var(--accent-dark);
  background-image: linear-gradient(135deg, var(--background-card) 0%, rgba(45, 212, 191, 0.05) 100%);
}
```

### Spacing System (8px Grid)

#### Touch-Friendly Spacing Scale
```css
/* Mobile-optimized spacing system */
:root {
  --space-0: 0px;
  --space-1: 4px;    /* Tight internal spacing */
  --space-2: 8px;    /* Component internal spacing */
  --space-3: 12px;   /* Small component padding */
  --space-4: 16px;   /* Standard element spacing */
  --space-5: 20px;   /* Component separation */
  --space-6: 24px;   /* Section spacing */
  --space-8: 32px;   /* Major section separation */
  --space-10: 40px;  /* Large spacing */
  --space-12: 48px;  /* Page section spacing */
  --space-16: 64px;  /* Major page divisions */
  --space-20: 80px;  /* Hero section spacing */
}

/* Responsive spacing adjustments */
@media (max-width: 768px) {
  :root {
    --space-8: 24px;   /* Reduced on mobile */
    --space-12: 32px;  /* Reduced on mobile */
    --space-16: 48px;  /* Reduced on mobile */
  }
}
```

### Iconography System

#### Icon Philosophy & Implementation
```css
/* Consistent, professional icon system */
.icon {
  /* Standard sizes optimized for mobile touch */
  --icon-xs: 16px;   /* Inline with text */
  --icon-sm: 20px;   /* Button icons */
  --icon-md: 24px;   /* Standard size */
  --icon-lg: 32px;   /* Prominent actions */
  --icon-xl: 48px;   /* Hero/feature icons */
}

/* Financial context styling */
.icon-currency {
  color: var(--teal-600);
  font-weight: 600;
}

.icon-status-success {
  color: var(--success-500);
}

.icon-status-warning {
  color: var(--warning-500);
}

.icon-status-error {
  color: var(--error-500);
}

/* Icon with text alignment */
.icon-with-text {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}
```

#### Financial-Specific Icon Standards
- **Currency Exchange**: Custom-designed exchange arrows
- **Security**: Shield icons for trust indicators  
- **Transaction Status**: Clear, colorful status icons
- **User Verification**: Check badges for verified users
- **Navigation**: Consistent outline icons throughout

### Animation & Motion Design

#### Professional Micro-Interactions
```css
/* Trust-building animation principles */
.interaction-gentle {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.interaction-bouncy {
  transition: transform 0.15s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.interaction-smooth {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Success feedback animations */
@keyframes success-pulse {
  0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(34, 197, 94, 0); }
  100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
}

.success-feedback {
  animation: success-pulse 0.6s ease-out;
}

/* Loading states - Professional and calming */
@keyframes gentle-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--blue-600);
  border-radius: 50%;
  animation: gentle-spin 1s linear infinite;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

### Mobile-Specific Visual Optimizations

#### Touch Feedback & Interactions
```css
/* Mobile touch feedback */
.touchable {
  min-width: 44px;
  min-height: 44px;
  position: relative;
  overflow: hidden;
}

.touchable::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.2s ease, height 0.2s ease;
}

.touchable:active::after {
  width: 200px;
  height: 200px;
}

/* Dark mode touch feedback */
[data-theme="dark"] .touchable::after {
  background: rgba(255, 255, 255, 0.1);
}
```

#### OLED Dark Mode Benefits
```css
/* OLED optimization for battery savings and premium feel */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
  [data-theme="dark"] {
    --background-page: #000000;     /* True black saves 20-30% battery */
    --background-card: #0A0A0A;     /* Near-black for subtle elevation */
    --background-elevated: #111111; /* Minimal elevation for hierarchy */
  }
}
```

### Implementation Guidelines

#### CSS Custom Properties Structure
```css
/* Complete custom property system for theme consistency */
:root {
  /* Color system */
  --color-primary: var(--blue-700);
  --color-primary-hover: var(--blue-800);
  --color-accent: var(--teal-600);
  --color-success: var(--success-500);
  --color-warning: var(--warning-500);
  --color-error: var(--error-500);
  
  /* Background system */
  --bg-page: var(--gray-50);
  --bg-card: white;
  --bg-elevated: var(--gray-100);
  
  /* Text system */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --text-disabled: var(--gray-400);
  
  /* Border system */
  --border-subtle: var(--gray-200);
  --border-standard: var(--gray-300);
  --border-emphasis: var(--gray-400);
  
  /* Shadow system */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Border radius system */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;
}
```

This comprehensive visual design system creates a cohesive, trustworthy, and modern brand identity specifically tailored for a P2P financial application. Every element has been carefully considered for mobile-first usage, accessibility, and the psychological needs of users handling financial transactions.

## 🌓 Dark/Light Mode Design Considerations

### Mobile Theme Optimization
Your existing dark/light mode implementation is excellent! Here's how to enhance it for mobile:

#### Dark Mode Mobile Considerations
```css
/* Enhanced dark mode for mobile readability */
[data-theme="dark"] {
  /* Ensure sufficient contrast on mobile screens */
  --mobile-text-contrast: #f6f8fa;
  --mobile-accent-visibility: #7c3aed;
  
  /* Reduce eye strain on mobile */
  --card-elevation: 0 2px 8px rgba(0, 0, 0, 0.4);
  --input-background: #21262d;
}

/* OLED optimization for dark mode */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
  [data-theme="dark"] {
    --background-color: #000000; /* True black for OLED */
    --card-color: #111111;       /* Near black for cards */
  }
}
```

#### Light Mode Mobile Enhancements
```css
/* Enhanced light mode for mobile visibility */
[data-theme="light"] {
  /* High contrast for outdoor mobile usage */
  --mobile-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  --mobile-border-strong: #c1c9d0;
  
  /* Reduce glare on mobile */
  --background-soft: #fafbfc;
  --input-background: #ffffff;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  [data-theme="light"] {
    --text-color: #000000;
    --border-color: #666666;
  }
  
  [data-theme="dark"] {
    --text-color: #ffffff;
    --border-color: #cccccc;
  }
}
```

### Theme Toggle Mobile UX
```vue
<!-- Your existing ThemeToggle optimized for mobile -->
<template>
  <n-button
    circle
    size="large"
    @click="toggleTheme"
    class="mobile-theme-toggle"
  >
    <template #icon>
      <n-icon size="20">
        <SunnyOutline v-if="isDark" />
        <MoonOutline v-else />
      </n-icon>
    </template>
  </n-button>
</template>

<style scoped>
.mobile-theme-toggle {
  min-width: 44px;
  min-height: 44px;
}

/* Visual feedback for theme switching */
.mobile-theme-toggle:active {
  transform: scale(0.95);
}
</style>
```

### System Theme Detection
```javascript
// Enhance existing theme system with mobile-specific logic
const useThemeSystem = () => {
  const detectMobilePreference = () => {
    // Respect user's system preference on mobile
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
    
    return {
      prefersDark,
      prefersHighContrast,
      isOLED: window.matchMedia('(display-gamut: p3)').matches // Detect OLED displays
    };
  };
  
  return { detectMobilePreference };
};
```

### Single Path Navigation
- **Linear Flow**: Users follow one clear path to their goal
- **Breadcrumb Context**: Always show where users are
- **Easy Back**: Consistent back navigation
- **Progress Indicators**: Show completion status

### Bottom Navigation (Mobile-Native)
```
┌─────────────────┐
│                 │
│   MAIN CONTENT  │
│                 │
│                 │
├─────────────────┤
│ [💱] [🔍] [💬] [👤] │ ← Bottom tab bar
└─────────────────┘
  Exchange Browse Chat Profile
```

### Gesture Support
- **Swipe Back**: Natural mobile back navigation
- **Pull to Refresh**: Standard mobile pattern
- **Swipe Actions**: Card-based interactions
- **Tap and Hold**: Secondary actions

## 🎪 Component Design Guidelines

### Button Hierarchy (Theme-Aware)
```css
/* Primary Button - Works in both themes */
.btn-primary {
  background: var(--primary-color);      /* Uses theme variables */
  color: var(--primary-text-color);
  min-height: 56px;
  font-weight: 600;
  border-radius: 8px;
  width: 100%; /* Full width on mobile */
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--primary-hover);
}

.btn-primary:active {
  transform: scale(0.98);
}

/* Secondary Button - Theme adaptive */
.btn-secondary {
  background: var(--card-color);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  min-height: 48px;
}

/* Theme-specific enhancements */
[data-theme="dark"] .btn-secondary {
  background: var(--background-color);
  border-color: var(--primary-color);
}

/* Text Button - Minimal theme impact */
.btn-text {
  background: transparent;
  color: var(--primary-color);
  min-height: 44px;
  padding: 12px 16px;
}
```

### Card Design (Theme-Responsive)
```css
.card {
  background: var(--card-color);
  border-radius: 12px;
  padding: 20px;
  margin: 16px;
  border: 1px solid var(--border-color);
  
  /* Theme-aware shadows */
  box-shadow: var(--card-elevation, 0 2px 8px rgba(0, 0, 0, 0.1));
  
  /* Mobile touch feedback */
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:active {
  transform: scale(0.98);
}

/* Dark theme card enhancement */
[data-theme="dark"] .card {
  background: var(--card-color);
  border-color: var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Light theme card enhancement */
[data-theme="light"] .card {
  background: var(--card-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}
```

### Form Design (Mobile-Friendly)
```css
.form-input {
  min-height: 56px;
  padding: 16px;
  font-size: 16px; /* Prevents zoom on iOS */
  border-radius: 8px;
  border: 2px solid var(--gray-200);
  width: 100%;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 8px;
}
```

## 📱 Mobile-Specific Patterns

### Loading States
```css
/* Mobile-friendly loading indicators */
.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-skeleton {
  background: linear-gradient(90deg, 
    var(--gray-200) 25%, 
    var(--gray-100) 50%, 
    var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}
```

### Modal Design (Mobile-Native)
```css
/* Full-screen mobile modals */
@media (max-width: 768px) {
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    z-index: 1000;
    
    /* Slide up animation */
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }
  
  .modal.open {
    transform: translateY(0);
  }
}
```

## ♿ Accessibility Standards

### Mobile Accessibility
- **Minimum Text Size**: 16px for body text
- **Color Contrast**: 4.5:1 ratio minimum
- **Focus Indicators**: Visible focus states
- **Screen Reader**: Proper semantic HTML
- **Voice Control**: Support for voice navigation

### Touch Accessibility
```css
/* Focus states for keyboard/switch navigation */
.focusable:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid var(--gray-900);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

## 🚀 Performance Guidelines

### Mobile Performance Standards
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

### Optimization Strategies
```javascript
// Lazy loading for mobile
const LazyComponent = defineAsyncComponent({
  loader: () => import('./ExpensiveComponent.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorFallback,
  delay: 200,
  timeout: 3000
});

// Image optimization
const optimizedImage = {
  srcset: `
    image-320w.webp 320w,
    image-640w.webp 640w,
    image-1024w.webp 1024w
  `,
  sizes: '(max-width: 768px) 100vw, 50vw',
  loading: 'lazy'
};
```

## 🎯 Implementation Priorities

### Phase 1: Foundation
1. Responsive breakpoint system
2. Touch-friendly component library
3. Mobile navigation patterns
4. Basic accessibility compliance

### Phase 2: Enhancement
1. Gesture support
2. Performance optimization
3. Advanced accessibility
4. Progressive web app features

### Phase 3: Polish
1. Micro-interactions
2. Advanced animations
3. Offline support
4. Native app-like features

---

*These principles will guide all design decisions, ensuring a consistently excellent mobile experience throughout MUNygo.*
