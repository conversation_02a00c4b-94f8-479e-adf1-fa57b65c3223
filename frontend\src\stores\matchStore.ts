import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { OfferMatch, MatchStatus, MatchResponse } from '@/types/api';
import { matchService } from '@/services/matchService';
import centralizedSocketManager from '@/services/centralizedSocketManager';
import { FrontendNotificationType } from '@/stores/notificationStore';
import {
  type MatchFoundPayload,
  type MatchAcceptedPayload,
  type MatchDeclinedPayload,
  type MatchExpiredPayload,
  type MatchConvertedPayload
} from '@/types/socketEvents';
import { useAuthStore } from './auth';
import { useNotificationStore } from './notificationStore';
import { useClientLogger } from '@/composables/useClientLogger';

const logger = useClientLogger();

export const useMatchStore = defineStore('matchStore', () => {
  // State
  const matches = ref<OfferMatch[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const lastFetchTime = ref<Date | null>(null);

  // Socket listener tracking
  let socketListenersAttached = false;
  let unsubscribeMatchFound: (() => void) | null = null;
  let unsubscribeMatchAccepted: (() => void) | null = null;
  let unsubscribeMatchDeclined: (() => void) | null = null;
  let unsubscribeMatchExpired: (() => void) | null = null;
  let unsubscribeMatchConverted: (() => void) | null = null;
  // Computed properties
  const pendingMatches = computed(() => 
    matches.value.filter(match => 
      ['PENDING', 'PARTIAL_ACCEPT'].includes(match.status) && !match.isExpired
    )
  );const matchesNeedingResponse = computed(() => {
    return pendingMatches.value.filter(match => 
      match.isCurrentUserInvolved && 
      match.canCurrentUserRespond &&
      !match.currentUserResponse
    );
  });
  const activeMatches = computed(() => 
    matches.value.filter(match => 
      ['PENDING', 'PARTIAL_ACCEPT', 'BOTH_ACCEPTED', 'CONVERTED'].includes(match.status) && !match.isExpired
    )
  );

  const expiredMatches = computed(() => 
    matches.value.filter(match => match.isExpired || match.status === 'EXPIRED')
  );

  const completedMatches = computed(() => 
    matches.value.filter(match => 
      ['CONVERTED', 'DECLINED', 'CANCELLED'].includes(match.status)
    )
  );

  const hasUnreadMatches = computed(() => 
    matchesNeedingResponse.value.length > 0
  );

  // Actions
  async function loadMatches(statusFilter?: MatchStatus) {
    if (isLoading.value) return;
    
    isLoading.value = true;
    error.value = null;
    
    try {
      logger.logInfo('Loading matches', { statusFilter });
      
      const response = await matchService.fetchMatches({ 
        status: statusFilter,
        limit: 50 // Reasonable limit for mobile performance
      });
      
      matches.value = response.matches.map(enhanceMatchForFrontend);
      lastFetchTime.value = new Date();
      
      logger.logInfo('Matches loaded successfully', { 
        count: matches.value.length,
        pending: pendingMatches.value.length,
        needingResponse: matchesNeedingResponse.value.length
      });
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load matches';
      error.value = errorMessage;
      logger.logError('Failed to load matches', err, { statusFilter });
    } finally {
      isLoading.value = false;
    }
  }

  async function acceptMatch(matchId: string) {
    try {
      logger.logInfo('Accepting match', { matchId });      const response = await matchService.acceptMatch(matchId);
      
      logger.logInfo('Accept match response', { 
        response, 
        chatSessionId: response.chatSessionId,
        transactionId: response.transactionId,
        matchChatSessionId: response.match?.chatSessionId,
        matchTransactionId: response.match?.transactionId
      });
      
      if (response.success) {
        // Update local state - merge response data with existing match
        const matchIndex = matches.value.findIndex(m => m.id === matchId);        if (matchIndex !== -1) {
          const existingMatch = matches.value[matchIndex];
          
          console.log('Existing match before update:', existingMatch);
          console.log('Response chatSessionId:', response.chatSessionId);
          console.log('Response transactionId:', response.transactionId);
          
          const updatedMatch = {
            ...existingMatch,
            status: response.match.status,
            userAResponse: response.match.userAResponse,
            userBResponse: response.match.userBResponse,
            chatSessionId: response.chatSessionId || response.match.chatSessionId,
            transactionId: response.transactionId || response.match.transactionId
          };
          
          console.log('Updated match before enhancement:', updatedMatch);
          
          matches.value[matchIndex] = enhanceMatchForFrontend(updatedMatch);
            console.log('Final enhanced match:', matches.value[matchIndex]);
        }

        logger.logInfo('Match accepted successfully', { 
          matchId, 
          chatSessionId: response.chatSessionId,
          transactionId: response.transactionId 
        });
        
        return response;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to accept match';
      error.value = errorMessage;
      logger.logError('Failed to accept match', err, { matchId });
      throw err;
    }
  }

  async function declineMatch(matchId: string, reason?: string) {
    try {
      logger.logInfo('Declining match', { matchId, reason });
      
      const response = await matchService.declineMatch(matchId, { reason });
      
      if (response.success) {
        // Update local state
        const matchIndex = matches.value.findIndex(m => m.id === matchId);
        if (matchIndex !== -1) {
          matches.value[matchIndex] = enhanceMatchForFrontend(response.match);
        }
        
        logger.logInfo('Match declined successfully', { matchId, reason });
        return response;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to decline match';
      error.value = errorMessage;
      logger.logError('Failed to decline match', err, { matchId, reason });
      throw err;
    }
  }

  function findMatchById(matchId: string): OfferMatch | undefined {
    return matches.value.find(match => match.id === matchId || match.matchId === matchId);
  }

  function removeMatch(matchId: string) {
    const index = matches.value.findIndex(m => m.id === matchId || m.matchId === matchId);
    if (index !== -1) {
      matches.value.splice(index, 1);
    }
  }

  function updateMatch(updatedMatch: OfferMatch) {
    const index = matches.value.findIndex(m => m.id === updatedMatch.id);
    if (index !== -1) {
      matches.value[index] = enhanceMatchForFrontend(updatedMatch);
    } else {
      // Add new match if not found
      matches.value.unshift(enhanceMatchForFrontend(updatedMatch));
    }
  }

  function clearMatches() {
    matches.value = [];
    error.value = null;
    lastFetchTime.value = null;
  }

  // Socket event handlers
  function handleMatchFound(payload: MatchFoundPayload) {
    logger.logInfo('New match found', { matchId: payload.matchId });
    
    // Create OfferMatch from payload
    const match: OfferMatch = {
      id: payload.match.id,
      matchId: payload.match.matchId,
      status: 'PENDING' as MatchStatus,
      compatibilityScore: payload.match.compatibilityScore,
      currencyA: payload.match.offerA.currencyPair.split('-')[0],
      currencyB: payload.match.offerA.currencyPair.split('-')[1],
      amountA: payload.match.offerA.amount,
      amountB: payload.match.offerB.amount,
      rateAToB: payload.match.offerA.baseRate,
      rateBToA: payload.match.offerB.baseRate,
      offerA: {
        id: payload.match.offerA.id,
        type: payload.match.offerA.type as 'BUY' | 'SELL',
        user: {
          id: payload.match.offerA.user.id,
          username: payload.match.offerA.user.username || payload.match.offerA.user.email,
          reputationLevel: payload.match.offerA.user.reputationLevel
        }
      },
      offerB: {
        id: payload.match.offerB.id,
        type: payload.match.offerB.type as 'BUY' | 'SELL',
        user: {
          id: payload.match.offerB.user.id,
          username: payload.match.offerB.user.username || payload.match.offerB.user.email,
          reputationLevel: payload.match.offerB.user.reputationLevel
        }
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      expiresAt: payload.match.expiresAt,
      isExpired: false,
      isCurrentUserInvolved: false,
      canCurrentUserRespond: false
    };

    updateMatch(match);
      // Add notification
    const notificationStore = useNotificationStore();
    const authStore = useAuthStore();
    notificationStore.addOrUpdateNotification({
      id: `match-found-${match.matchId}-${Date.now()}`,
      userId: authStore.user?.id || '',
      type: FrontendNotificationType.MATCH_FOUND,
      message: `A compatible offer has been found for your ${match.currencyA}-${match.currencyB} exchange.`,
      isRead: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      relatedEntityType: 'match',
      relatedEntityId: match.matchId,
      data: { matchId: match.matchId }
    });
  }  function handleMatchAccepted(payload: MatchAcceptedPayload) {
    logger.logInfo('Match accepted', { matchId: payload.matchId, status: payload.status });
    console.log('Received match accepted payload:', JSON.stringify(payload, null, 2));
    
    const match = findMatchById(payload.matchId);
    if (match) {
      match.status = payload.match.status as MatchStatus;
      match.updatedAt = new Date().toISOString();
      
      if (payload.match.userAResponse) {
        match.userAResponse = payload.match.userAResponse as MatchResponse;
      }
      if (payload.match.userBResponse) {
        match.userBResponse = payload.match.userBResponse as MatchResponse;
      }
      
      // Update IDs if available in the socket payload
      if (payload.match.chatSessionId) {
        match.chatSessionId = payload.match.chatSessionId;
      }
      if (payload.match.transactionId) {
        match.transactionId = payload.match.transactionId;
      }
      
      // If both users have accepted, we need to ensure we have the full match data
      // If IDs are missing, fetch the full match data from the API
      if (payload.status === 'both_accepted') {
        logger.logInfo('Both users accepted', { 
          matchId: payload.matchId,
          hasChatSessionId: !!match.chatSessionId,
          hasTransactionId: !!match.transactionId,
          socketChatSessionId: payload.match.chatSessionId,
          socketTransactionId: payload.match.transactionId
        });
        
        // If we still don't have the IDs, refresh matches from API
        if (!match.chatSessionId || !match.transactionId) {
          logger.logInfo('Missing IDs after socket event, fetching full match data', { matchId: payload.matchId });
          // Use a small delay to ensure the database transaction has fully committed
          setTimeout(() => {
            loadMatches();
          }, 1000);
        } else {
          updateMatch(match);
        }
      } else {
        updateMatch(match);
      }
    }
  }

  function handleMatchDeclined(payload: MatchDeclinedPayload) {
    logger.logInfo('Match declined', { matchId: payload.matchId });
    
    const match = findMatchById(payload.matchId);
    if (match) {
      match.status = 'DECLINED' as MatchStatus;
      match.updatedAt = new Date().toISOString();
      if (payload.reason) {
        match.declineReason = payload.reason;
      }
      
      updateMatch(match);
    }
  }

  function handleMatchExpired(payload: MatchExpiredPayload) {
    logger.logInfo('Match expired', { matchId: payload.matchId });
    
    const match = findMatchById(payload.matchId);
    if (match) {
      match.status = 'EXPIRED' as MatchStatus;
      match.isExpired = true;
      match.updatedAt = new Date().toISOString();
      
      updateMatch(match);
    }
  }

  function handleMatchConverted(payload: MatchConvertedPayload) {
    logger.logInfo('Match converted', { 
      matchId: payload.matchId, 
      chatSessionId: payload.chatSessionId 
    });
    
    const match = findMatchById(payload.matchId);
    if (match) {
      match.status = 'CONVERTED' as MatchStatus;
      match.chatSessionId = payload.chatSessionId;
      match.transactionId = payload.transactionId;
      match.updatedAt = new Date().toISOString();
      
      updateMatch(match);
    }
  }
  // Socket management
  function attachSocketListeners() {
    if (socketListenersAttached) return;

    logger.logInfo('Attaching match socket listeners');

    unsubscribeMatchFound = centralizedSocketManager.on('MATCH_FOUND', handleMatchFound);
    unsubscribeMatchAccepted = centralizedSocketManager.on('MATCH_ACCEPTED', handleMatchAccepted);
    unsubscribeMatchDeclined = centralizedSocketManager.on('MATCH_DECLINED', handleMatchDeclined);
    unsubscribeMatchExpired = centralizedSocketManager.on('MATCH_EXPIRED', handleMatchExpired);
    unsubscribeMatchConverted = centralizedSocketManager.on('MATCH_CONVERTED', handleMatchConverted);

    socketListenersAttached = true;
  }

  function detachSocketListeners() {
    if (!socketListenersAttached) return;

    logger.logInfo('Detaching match socket listeners');

    unsubscribeMatchFound?.();
    unsubscribeMatchAccepted?.();
    unsubscribeMatchDeclined?.();
    unsubscribeMatchExpired?.();
    unsubscribeMatchConverted?.();

    unsubscribeMatchFound = null;
    unsubscribeMatchAccepted = null;
    unsubscribeMatchDeclined = null;
    unsubscribeMatchExpired = null;
    unsubscribeMatchConverted = null;

    socketListenersAttached = false;
  }  // Helper function to enhance match data for frontend use
  function enhanceMatchForFrontend(match: OfferMatch): OfferMatch {
    const authStore = useAuthStore();
    const currentUserId = authStore.user?.id;
    
    const now = new Date();
    const expiresAt = new Date(match.expiresAt);
    const isExpired = now >= expiresAt;
    const timeRemaining = isExpired ? 0 : Math.floor((expiresAt.getTime() - now.getTime()) / 1000);
    
    // Determine if current user is involved and their role
    const isCurrentUserInvolved = currentUserId && (
      match.userAId === currentUserId || 
      match.userBId === currentUserId
    );
    
    let currentUserResponse: MatchResponse | undefined;
    let otherUserResponse: MatchResponse | undefined;
    
    if (isCurrentUserInvolved && currentUserId) {
      if (match.userAId === currentUserId) {
        currentUserResponse = match.userAResponse;
        otherUserResponse = match.userBResponse;
      } else {
        currentUserResponse = match.userBResponse;
        otherUserResponse = match.userAResponse;
      }
    }
      const canCurrentUserRespond = !!(isCurrentUserInvolved && 
      !isExpired && 
      (match.status === 'PENDING' || match.status === 'PARTIAL_ACCEPT') && 
      !currentUserResponse);

    return {
      ...match,
      isExpired,
      timeRemaining,
      isCurrentUserInvolved: !!isCurrentUserInvolved,
      currentUserResponse,
      otherUserResponse,
      canCurrentUserRespond
    };
  }

  return {
    // State
    matches,
    isLoading,
    error,
    lastFetchTime,
    
    // Computed
    pendingMatches,
    matchesNeedingResponse,
    activeMatches,
    expiredMatches,
    completedMatches,
    hasUnreadMatches,
    
    // Actions
    loadMatches,
    acceptMatch,
    declineMatch,
    findMatchById,
    removeMatch,
    updateMatch,
    clearMatches,
    
    // Socket management
    attachSocketListeners,
    detachSocketListeners
  };
});
