<template>
  <div class="negotiation-container" data-testid="smart-negotiation-section">
    
    <!-- Loading State -->
    <div v-if="negotiationState === 'loading'" class="state-view loading-state">
      <div class="loading-spinner">⏳</div>
      <div class="loading-text">{{ translate('transactionalChat.actionCards.negotiation.loadingNegotiation') }}</div>
    </div>

    <!-- System Recommendation View -->
    <div v-else-if="negotiationState === 'system-recommendation'" class="state-view">
      
      <!-- Main Title -->
      <h2 class="main-title">{{ translate('transactionalChat.actionCards.negotiation.whoShouldPayFirst') }}</h2>
      <p class="subtitle">{{ translate('transactionalChat.actionCards.negotiation.systemRecommendation') }}</p>
      
      <!-- Recommendation Card -->
      <div class="recommendation-card">
        <div class="rec-icon">🤖</div>
        <div class="rec-content">
          <div class="rec-main">
            {{ systemRecommendation?.isCurrentUser 
              ? translate('transactionalChat.actionCards.negotiation.youShouldPayFirst') 
              : translate('transactionalChat.actionCards.negotiation.otherShouldPayFirst', { name: systemRecommendation?.userDisplayName })
            }}
          </div>
          <div class="rec-reason">{{ systemRecommendationReason }}</div>
        </div>
      </div>
      
      <!-- Payment Flow Visualization -->
      <div class="payment-flow">
        <div class="payment-step" :class="{ first: true }">
          <div class="step-badge">1</div>
          <div class="step-content">
            <div class="payer">{{ systemRecommendation?.isCurrentUser ? translate('common.you') : systemRecommendation?.userDisplayName }}</div>
            <div class="arrow">→</div>
            <div class="amount">{{ formatAmount(negotiationContext.userSends.amount, negotiationContext.userSends.currency) }}</div>
            <div class="arrow">→</div>
            <div class="receiver">{{ systemRecommendation?.isCurrentUser ? systemRecommendation?.userDisplayName : translate('common.you') }}</div>
          </div>
        </div>
        
        <div class="payment-step" :class="{ second: true }">
          <div class="step-badge">2</div>
          <div class="step-content">
            <div class="payer">{{ systemRecommendation?.isCurrentUser ? systemRecommendation?.userDisplayName : translate('common.you') }}</div>
            <div class="arrow">→</div>
            <div class="amount">{{ formatAmount(negotiationContext.userReceives.amount, negotiationContext.userReceives.currency) }}</div>
            <div class="arrow">→</div>
            <div class="receiver">{{ systemRecommendation?.isCurrentUser ? translate('common.you') : systemRecommendation?.userDisplayName }}</div>
          </div>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="action-section">
        <div v-if="systemRecommendation?.isCurrentUser" class="recommended-user-actions">
          <button 
            class="btn btn-primary btn-large"
            @click="handleAcceptSystemRecommendation"
            :disabled="isSubmitting"
            data-testid="agree-button"
          >
            <span v-if="isSubmitting" class="spinner">⏳</span>
            <span>{{ translate('transactionalChat.actionCards.negotiation.iAgree') }}</span>
          </button>
          
          <button 
            class="btn btn-secondary btn-large"
            @click="handleRequestCounterOffer"
            :disabled="isSubmitting"
            data-testid="request-other-pays-first-button"
          >
            {{ translate('transactionalChat.actionCards.negotiation.requestOtherPaysFirst') }}
          </button>
        </div>
        
        <div v-else class="non-recommended-user-actions">
          <button 
            class="btn btn-primary btn-large"
            @click="handleAcceptSystemRecommendation"
            :disabled="isSubmitting"
            data-testid="agree-button"
          >
            <span v-if="isSubmitting" class="spinner">⏳</span>
            <span>{{ translate('transactionalChat.actionCards.negotiation.iAgree') }}</span>
          </button>
          
          <p class="waiting-note">
            {{ translate('transactionalChat.actionCards.negotiation.waitingForOtherResponse', { name: systemRecommendation?.userDisplayName }) }}
          </p>
        </div>
      </div>
      
      <!-- Optional Message Input -->
      <div v-if="showMessageInput" class="message-input-section">
        <label class="message-label">
          {{ translate('transactionalChat.actionCards.negotiation.addOptionalMessage') }}
        </label>
        <textarea 
          v-model="proposalMessage"
          class="message-textarea"
          :placeholder="translate('transactionalChat.actionCards.negotiation.messagePlaceholder')"
          maxlength="300"
          data-testid="message-input"
        ></textarea>
        <div class="message-footer">
          <span class="char-count">{{ proposalMessage.length }}/300</span>
          <div class="message-actions">
            <button class="btn btn-cancel" @click="cancelMessageInput">
              {{ translate('common.cancel') }}
            </button>
            <button 
              class="btn btn-send" 
              @click="handleProposeCounter"
              :disabled="isSubmitting"
              data-testid="send-proposal-button"
            >
              <span v-if="isSubmitting" class="spinner">⏳</span>
              <span>{{ translate('common.send') }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- User Proposal View -->
    <div v-else-if="negotiationState === 'user-proposal'" class="state-view">
      
      <!-- Proposal Header -->
      <h2 class="main-title">
        {{ currentProposalInfo?.proposerName === 'You' 
          ? translate('transactionalChat.actionCards.negotiation.yourProposal') 
          : translate('transactionalChat.actionCards.negotiation.newProposal', { name: currentProposalInfo?.proposerName })
        }}
      </h2>
      
      <!-- Proposal Message (if exists) -->
      <div v-if="currentProposalInfo?.message" class="proposal-message">
        <div class="message-icon">💬</div>
        <div class="message-text">"{{ currentProposalInfo.message }}"</div>
      </div>
      
      <!-- New Payment Flow -->
      <div class="payment-flow">
        <div class="payment-step" :class="{ first: true }">
          <div class="step-badge">1</div>
          <div class="step-content">
            <div class="payer">{{ currentProposalInfo?.isUserProposed ? translate('common.you') : currentProposalInfo?.proposerName }}</div>
            <div class="arrow">→</div>
            <div class="amount">{{ formatAmount(negotiationContext.userSends.amount, negotiationContext.userSends.currency) }}</div>
            <div class="arrow">→</div>
            <div class="receiver">{{ currentProposalInfo?.isUserProposed ? currentProposalInfo?.proposerName : translate('common.you') }}</div>
          </div>
        </div>
        
        <div class="payment-step" :class="{ second: true }">
          <div class="step-badge">2</div>
          <div class="step-content">
            <div class="payer">{{ currentProposalInfo?.isUserProposed ? currentProposalInfo?.proposerName : translate('common.you') }}</div>
            <div class="arrow">→</div>
            <div class="amount">{{ formatAmount(negotiationContext.userReceives.amount, negotiationContext.userReceives.currency) }}</div>
            <div class="arrow">→</div>
            <div class="receiver">{{ currentProposalInfo?.isUserProposed ? translate('common.you') : currentProposalInfo?.proposerName }}</div>
          </div>
        </div>
      </div>
      
      <!-- Proposal Actions -->
      <div class="action-section">
        <button 
          class="btn btn-primary btn-large"
          @click="handleAcceptUserProposal"
          :disabled="isSubmitting"
          data-testid="agree-to-proposal-button"
        >
          <span v-if="isSubmitting" class="spinner">⏳</span>
          <span>{{ translate('transactionalChat.actionCards.negotiation.agreeToProposal') }}</span>
        </button>
        
        <button 
          class="btn btn-secondary btn-large"
          @click="handleRequestCounterOffer"
          :disabled="isSubmitting"
          data-testid="propose-alternative-button"
        >
          {{ translate('transactionalChat.actionCards.negotiation.proposeAlternative') }}
          <span v-if="wouldBeFinalProposal" class="warning-badge">{{ translate('transactionalChat.actionCards.negotiation.final') }}</span>
        </button>
        
        <button 
          class="btn btn-danger btn-large"
          @click="handleDeclineProposal"
          :disabled="isSubmitting"
          data-testid="decline-button"
        >
          {{ translate('transactionalChat.actionCards.negotiation.decline') }}
        </button>
      </div>
      
      <!-- Message Input for Counter-Proposal -->
      <div v-if="showMessageInput" class="message-input-section">
        <label class="message-label">
          {{ translate('transactionalChat.actionCards.negotiation.addOptionalMessage') }}
        </label>
        <textarea 
          v-model="proposalMessage"
          class="message-textarea"
          :placeholder="translate('transactionalChat.actionCards.negotiation.messagePlaceholder')"
          maxlength="300"
          data-testid="message-input"
        ></textarea>
        <div class="message-footer">
          <span class="char-count">{{ proposalMessage.length }}/300</span>
          <div class="message-actions">
            <button class="btn btn-cancel" @click="cancelMessageInput">
              {{ translate('common.cancel') }}
            </button>
            <button 
              class="btn btn-send" 
              @click="handleProposeCounter"
              :disabled="isSubmitting"
              data-testid="send-proposal-button"
            >
              <span v-if="isSubmitting" class="spinner">⏳</span>
              <span>{{ translate('common.send') }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Waiting for Response State -->
    <div v-else-if="negotiationState === 'waiting-for-response'" class="state-view waiting-state">
      <div class="waiting-icon">⏳</div>
      <h3 class="waiting-title">{{ translate('transactionalChat.actionCards.negotiation.waitingForResponse') }}</h3>
      <p class="waiting-text">
        {{ translate('transactionalChat.actionCards.negotiation.waitingMessage', { name: otherUserInfo?.name || 'Other User' }) }}
      </p>
      
      <div v-if="currentProposalInfo?.message" class="proposal-summary">
        <div class="summary-label">{{ translate('transactionalChat.actionCards.negotiation.yourMessage') }}:</div>
        <div class="summary-text">"{{ currentProposalInfo.message }}"</div>
      </div>
    </div>

    <!-- Finalized State -->
    <div v-else-if="negotiationState === 'finalized'" class="state-view success-state">
      <div class="success-icon">🎉</div>
      <h3 class="success-title">{{ translate('transactionalChat.actionCards.negotiation.agreementReached') }}</h3>
      <p class="success-text">
        {{ negotiation?.finalizedPayerId === currentUserId 
          ? translate('transactionalChat.actionCards.negotiation.youWillPayFirst')
          : translate('transactionalChat.actionCards.negotiation.otherWillPayFirst', { name: otherUserInfo?.name })
        }}
      </p>
    </div>

    <!-- Cancelled State -->
    <div v-else-if="negotiationState === 'cancelled'" class="state-view error-state">
      <div class="error-icon">❌</div>
      <h3 class="error-title">{{ translate('transactionalChat.actionCards.negotiation.transactionCancelled') }}</h3>
      <p class="error-text">{{ translate('transactionalChat.actionCards.negotiation.cancelledDescription') }}</p>
    </div>

    <!-- Final Warning Modal -->
    <div v-if="showFinalWarning" class="modal-overlay" @click="handleCancelFinalProposal">
      <div class="modal-content" @click.stop data-testid="final-warning-modal">
        <div class="modal-header">
          <div class="warning-icon">⚠️</div>
          <h4 class="modal-title">{{ translate('transactionalChat.actionCards.negotiation.finalOfferWarning') }}</h4>
        </div>
        <p class="modal-text">{{ translate('transactionalChat.actionCards.negotiation.finalOfferDescription') }}</p>
        <div class="modal-actions">
          <button class="btn btn-cancel" @click="handleCancelFinalProposal">
            {{ translate('common.cancel') }}
          </button>
          <button class="btn btn-warning" @click="handleConfirmFinalProposal">
            {{ translate('transactionalChat.actionCards.negotiation.proceed') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Custom Toast Notifications -->
    <div ref="toastContainer" class="toast-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import { usePayerNegotiationStore } from '@/stores/payerNegotiation'
import { useTransactionalChatStore } from '@/stores/transactionalChat'
import { NegotiationStatus } from '@/types/payerNegotiation'

interface NegotiationProps {
  transactionId: string
}

interface NegotiationEvent {
  action: 'agree' | 'propose' | 'decline'
  proposedPayerId?: string
  message?: string
}

const props = defineProps<NegotiationProps>()
const emit = defineEmits<{
  negotiationUpdate: [event: NegotiationEvent]
}>()

// Stores and utilities
const authStore = useAuthStore()
const payerNegotiationStore = usePayerNegotiationStore()
const transactionalChatStore = useTransactionalChatStore()
const { t: translate } = useI18n()

// Refs
const toastContainer = ref<HTMLElement>()

// Reactive state
const isSubmitting = ref(false)
const showMessageInput = ref(false)
const showFinalWarning = ref(false)
const proposalMessage = ref('')

// Computed properties
const currentUserId = computed(() => authStore.user?.id)
const negotiation = computed(() => payerNegotiationStore.currentNegotiation)
const otherUserInfo = computed(() => transactionalChatStore.otherUserProfile)

const negotiationState = computed(() => {
  if (!negotiation.value) return 'loading'
  
  switch (negotiation.value.negotiationStatus) {
    case 'AWAITING_FIRST_PAYER_DESIGNATION':
      return negotiation.value.currentProposal_PayerId ? 'user-proposal' : 'system-recommendation'
    case 'AWAITING_COUNTER_PROPOSAL_RESPONSE':
      return 'waiting-for-response'
    case 'FINALIZED':
      return 'finalized'
    case 'CANCELLED':
      return 'cancelled'
    default:
      return 'loading'
  }
})

const systemRecommendation = computed(() => {
  if (!negotiation.value?.systemRecommendedPayerId) return null
  
  const isCurrentUserRecommended = negotiation.value.systemRecommendedPayerId === currentUserId.value
  
  return {
    payerId: negotiation.value.systemRecommendedPayerId,
    isCurrentUser: isCurrentUserRecommended,
    reason: negotiation.value.systemRecommendationRule || 'trustScore',
    userDisplayName: isCurrentUserRecommended ? translate('common.you') : otherUserInfo.value?.name || 'Other User'
  }
})

const currentProposalInfo = computed(() => {
  if (!negotiation.value?.currentProposal_PayerId) return null
  
  const isUserProposed = negotiation.value.currentProposal_PayerId === currentUserId.value
  
  return {
    payerId: negotiation.value.currentProposal_PayerId,
    isUserProposed,
    message: negotiation.value.currentProposal_Message,
    proposerName: isUserProposed ? translate('common.you') : otherUserInfo.value?.name || 'Other User'
  }
})

const negotiationContext = computed(() => {
  // Mock data - replace with real transaction data
  return {
    userSends: { amount: 28500000, currency: 'IRR' },
    userReceives: { amount: 500, currency: 'CAD' }
  }
})

// Check if this would be a second counter-offer (final proposal)
const wouldBeFinalProposal = computed(() => {
  return negotiationState.value === 'user-proposal' && !currentProposalInfo.value?.isUserProposed
})

const systemRecommendationReason = computed(() => {
  if (!systemRecommendation.value) return ''
  
  const reasonKey = systemRecommendation.value.reason
  const isCurrentUser = systemRecommendation.value.isCurrentUser
  
  if (reasonKey === 'lowerReputation') {
    return isCurrentUser 
      ? translate('transactionalChat.actionCards.negotiation.reasons.lowerReputation')
      : translate('transactionalChat.actionCards.negotiation.reasons.lowerReputationOther', { 
          name: systemRecommendation.value.userDisplayName 
        })
  }
  
  return translate('transactionalChat.actionCards.negotiation.reasons.trustScore')
})

// Utility functions
const formatAmount = (amount: number, currency: string) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount)
}

// Toast notification system
const showToast = (message: string, type: 'success' | 'error' | 'warning' = 'success') => {
  if (!toastContainer.value) return
  
  const toast = document.createElement('div')
  toast.className = `toast toast-${type}`
  toast.textContent = message
  
  toastContainer.value.appendChild(toast)
  
  // Trigger animation
  nextTick(() => {
    toast.classList.add('toast-show')
  })
  
  // Remove after 3 seconds
  setTimeout(() => {
    toast.classList.add('toast-hide')
    setTimeout(() => {
      if (toastContainer.value && toastContainer.value.contains(toast)) {
        toastContainer.value.removeChild(toast)
      }
    }, 300)
  }, 3000)
}

// Event handlers
const handleAcceptSystemRecommendation = async () => {
  if (isSubmitting.value || !negotiation.value) return
  
  isSubmitting.value = true
  
  try {
    const event: NegotiationEvent = { action: 'agree' }
    emit('negotiationUpdate', event)
    showToast(translate('transactionalChat.actionCards.negotiation.agreementSent'), 'success')
  } catch (error) {
    showToast(translate('transactionalChat.actionCards.negotiation.actionFailed'), 'error')
  } finally {
    isSubmitting.value = false
  }
}

const handleAcceptUserProposal = async () => {
  if (isSubmitting.value || !negotiation.value) return
  
  isSubmitting.value = true
  
  try {
    const event: NegotiationEvent = { action: 'agree' }
    emit('negotiationUpdate', event)
    showToast(translate('transactionalChat.actionCards.negotiation.proposalAccepted'), 'success')
  } catch (error) {
    showToast(translate('transactionalChat.actionCards.negotiation.actionFailed'), 'error')
  } finally {
    isSubmitting.value = false
  }
}

const handleRequestCounterOffer = () => {
  if (wouldBeFinalProposal.value) {
    showFinalWarning.value = true
  } else {
    showMessageInput.value = true
  }
}

const handleConfirmFinalProposal = () => {
  showFinalWarning.value = false
  showMessageInput.value = true
}

const handleCancelFinalProposal = () => {
  showFinalWarning.value = false
}

const handleProposeCounter = async () => {
  if (isSubmitting.value || !negotiation.value || !currentUserId.value) return
  
  isSubmitting.value = true
  
  try {
    const event: NegotiationEvent = {
      action: 'propose',
      proposedPayerId: currentUserId.value,
      message: proposalMessage.value.trim() || undefined
    }
    emit('negotiationUpdate', event)
    
    // Reset form
    showMessageInput.value = false
    proposalMessage.value = ''
    showToast(translate('transactionalChat.actionCards.negotiation.proposalSent'), 'success')
  } catch (error) {
    showToast(translate('transactionalChat.actionCards.negotiation.actionFailed'), 'error')
  } finally {
    isSubmitting.value = false
  }
}

const handleDeclineProposal = async () => {
  try {
    const event: NegotiationEvent = { action: 'decline' }
    emit('negotiationUpdate', event)
    showToast(translate('transactionalChat.actionCards.negotiation.proposalDeclined'), 'warning')
  } catch (error) {
    showToast(translate('transactionalChat.actionCards.negotiation.actionFailed'), 'error')
  }
}

const cancelMessageInput = () => {
  showMessageInput.value = false
  proposalMessage.value = ''
}

// Watch for negotiation changes
watch(() => props.transactionId, async (newTransactionId) => {
  if (newTransactionId) {
    await payerNegotiationStore.fetchNegotiation(newTransactionId)
  }
}, { immediate: true })

// Initialize socket listeners
payerNegotiationStore.initializeSocketListeners()
</script>

<style scoped>
/* Reset and base styles */
* {
  box-sizing: border-box;
}

.negotiation-container {
  max-width: 100%;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.state-view {
  padding: 24px 20px;
}

/* Typography */
.main-title {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.3;
  text-align: center;
}

.subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 20px 0;
  text-align: center;
  font-weight: 500;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  font-size: 32px;
  margin-bottom: 12px;
  animation: pulse 2s infinite;
}

.loading-text {
  color: #6b7280;
  font-size: 16px;
}

/* Recommendation Card */
.recommendation-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
  border: 1px solid #c7d2fe;
  border-left: 4px solid #3b82f6;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
}

.rec-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.rec-content {
  flex: 1;
}

.rec-main {
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 6px;
  line-height: 1.4;
}

.rec-reason {
  font-size: 14px;
  color: #64748b;
  font-style: italic;
  line-height: 1.3;
}

/* Payment Flow */
.payment-flow {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
}

.payment-step {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.payment-step:last-child {
  margin-bottom: 0;
}

.step-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: #1f2937;
  color: white;
  border-radius: 50%;
  font-size: 14px;
  font-weight: 700;
  flex-shrink: 0;
}

.step-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  font-size: 14px;
  flex-wrap: wrap;
}

.payer, .receiver {
  font-weight: 600;
  color: #1f2937;
  min-width: 60px;
  text-align: center;
}

.arrow {
  color: #6b7280;
  font-weight: bold;
  font-size: 16px;
}

.amount {
  background: #3b82f6;
  color: white;
  padding: 6px 10px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 700;
  white-space: nowrap;
  min-width: 80px;
  text-align: center;
}

/* Action Section */
.action-section {
  margin-bottom: 16px;
}

.recommended-user-actions,
.non-recommended-user-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.waiting-note {
  color: #6b7280;
  font-size: 14px;
  text-align: center;
  margin: 12px 0 0 0;
  font-style: italic;
  line-height: 1.4;
}

/* Buttons */
.btn {
  border: none;
  border-radius: 10px;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 52px;
  text-decoration: none;
  font-family: inherit;
}

.btn-large {
  width: 100%;
  padding: 16px 24px;
  font-size: 16px;
  min-height: 56px;
}

.btn-primary {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(22, 163, 74, 0.25);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(22, 163, 74, 0.35);
}

.btn-secondary {
  background: white;
  color: #3b82f6;
  border: 2px solid #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.btn-secondary:hover:not(:disabled) {
  background: #3b82f6;
  color: white;
}

.btn-danger {
  background: white;
  color: #dc2626;
  border: 2px solid #dc2626;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.1);
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
  color: white;
}

.btn-cancel {
  background: #f8fafc;
  color: #6b7280;
  border: 1px solid #d1d5db;
  padding: 10px 16px;
  font-size: 14px;
  min-height: 40px;
}

.btn-cancel:hover:not(:disabled) {
  background: #e5e7eb;
}

.btn-send {
  background: #3b82f6;
  color: white;
  padding: 10px 16px;
  font-size: 14px;
  min-height: 40px;
}

.btn-send:hover:not(:disabled) {
  background: #2563eb;
}

.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.25);
}

.btn-warning:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(245, 158, 11, 0.35);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.spinner {
  animation: spin 1s linear infinite;
}

.warning-badge {
  background: #fbbf24;
  color: #92400e;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  margin-left: 8px;
  font-weight: 700;
}

/* Message Input Section */
.message-input-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
}

.message-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.message-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  margin-bottom: 12px;
  background: white;
  transition: border-color 0.2s ease;
}

.message-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.char-count {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

.message-actions {
  display: flex;
  gap: 8px;
}

/* Proposal Message */
.proposal-message {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-left: 4px solid #d97706;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
}

.message-icon {
  font-size: 18px;
  flex-shrink: 0;
  margin-top: 2px;
}

.message-text {
  color: #92400e;
  font-size: 14px;
  line-height: 1.4;
  font-style: italic;
}

/* State Components */
.waiting-state,
.success-state,
.error-state {
  text-align: center;
  padding: 40px 20px;
}

.waiting-icon,
.success-icon,
.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.waiting-title,
.success-title,
.error-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 12px 0;
}

.waiting-text,
.success-text,
.error-text {
  font-size: 16px;
  line-height: 1.4;
  margin: 0;
}

.success-state {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
}

.error-state {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  color: #991b1b;
}

.proposal-summary {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
  text-align: left;
}

.summary-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 6px;
}

.summary-text {
  color: #1f2937;
  font-size: 14px;
  font-style: italic;
  line-height: 1.4;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 24px;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.warning-icon {
  font-size: 24px;
}

.modal-title {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.modal-text {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 24px 0;
}

.modal-actions {
  display: flex;
  gap: 12px;
}

.modal-actions .btn {
  flex: 1;
  padding: 12px 16px;
  font-size: 14px;
  min-height: 44px;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.toast {
  padding: 12px 16px;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.toast-success {
  background: #16a34a;
}

.toast-error {
  background: #dc2626;
}

.toast-warning {
  background: #f59e0b;
}

.toast-show {
  transform: translateX(0);
  opacity: 1;
}

.toast-hide {
  transform: translateX(100%);
  opacity: 0;
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Mobile Responsive Design */
@media (max-width: 480px) {
  .negotiation-container {
    border-radius: 12px;
  }
  
  .state-view {
    padding: 20px 16px;
  }
  
  .main-title {
    font-size: 18px;
  }
  
  .recommendation-card {
    padding: 12px;
  }
  
  .rec-main {
    font-size: 15px;
  }
  
  .payment-flow {
    padding: 12px;
  }
  
  .step-content {
    flex-direction: column;
    gap: 6px;
    text-align: center;
  }
  
  .step-content .arrow {
    transform: rotate(90deg);
    margin: 4px 0;
  }
  
  .amount {
    min-width: 100px;
  }
  
  .btn-large {
    padding: 14px 20px;
    font-size: 15px;
    min-height: 50px;
  }
  
  .modal-overlay {
    padding: 16px;
  }
  
  .modal-content {
    padding: 20px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .message-footer {
    flex-direction: column;
    gap: 8px;
  }
  
  .message-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .message-actions .btn {
    flex: 1;
  }
  
  .toast-container {
    top: 16px;
    right: 16px;
    left: 16px;
  }
  
  .toast {
    max-width: none;
  }
}

/* RTL Support */
[dir="rtl"] .recommendation-card {
  flex-direction: row-reverse;
  border-left: 1px solid #c7d2fe;
  border-right: 4px solid #3b82f6;
}

[dir="rtl"] .step-content {
  flex-direction: row-reverse;
}

[dir="rtl"] .proposal-message {
  flex-direction: row-reverse;
  border-left: 1px solid #f59e0b;
  border-right: 4px solid #d97706;
}

[dir="rtl"] .modal-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .message-footer {
  flex-direction: row-reverse;
}

[dir="rtl"] .message-actions {
  flex-direction: row-reverse;
}

[dir="rtl"] .toast-container {
  right: auto;
  left: 20px;
}

[dir="rtl"] .toast {
  transform: translateX(-100%);
}

[dir="rtl"] .toast-show {
  transform: translateX(0);
}

[dir="rtl"] .toast-hide {
  transform: translateX(-100%);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .negotiation-container {
    border: 2px solid #000;
  }
  
  .btn {
    border-width: 2px;
  }
  
  .recommendation-card,
  .payment-flow,
  .proposal-message {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner,
  .spinner,
  .btn:hover,
  .toast {
    animation: none;
  }
  
  .btn:hover {
    transform: none;
  }
  
  .toast {
    transition: none;
  }
}
</style>
