const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testOfferLifecycle() {
  console.log('=== TESTING OFFER LIFECYCLE SCENARIOS ===\n');
  
  try {
    console.log('🎯 SCENARIO 1: Multiple BUY offers, one SELL offer comes');
    console.log('===============================================');
    console.log('');
    console.log('Initial State:');
    console.log('├─ Alice: BUY 100 CAD-IRR @ 200 (ACTIVE)');
    console.log('├─ Bob: BUY 100 CAD-IRR @ 200 (ACTIVE)');
    console.log('├─ Carol: BUY 100 CAD-IRR @ 200 (ACTIVE)');
    console.log('└─ All offers available for matching');
    console.log('');
    
    console.log('Dave creates SELL 100 CAD-IRR @ 200...');
    console.log('');
    console.log('System Response:');
    console.log('├─ Creates MATCH_001: Dave (SELL) ↔ <PERSON> (BUY)');
    console.log('├─ Creates MATCH_002: Dave (SELL) ↔ Bob (BUY)');
    console.log('└─ Creates MATCH_003: Dave (SELL) ↔ Carol (BUY)');
    console.log('');
    
    console.log('Dave chooses Alice and both accept...');
    console.log('');
    console.log('What happens:');
    console.log('1. MATCH_001: PENDING → BOTH_ACCEPTED → Transaction created');
    console.log('2. Dave\'s SELL offer: ACTIVE → ???');
    console.log('3. Alice\'s BUY offer: ACTIVE → ???');
    console.log('4. MATCH_002 & MATCH_003: PENDING → CANCELLED');
    console.log('5. Bob\'s BUY offer: Still ACTIVE → Available for new matches ✅');
    console.log('6. Carol\'s BUY offer: Still ACTIVE → Available for new matches ✅');
    console.log('');
    
    console.log('🔍 CRITICAL QUESTION: What happens to offers that enter transaction?');
    console.log('');
    
    // Let's check the current system behavior
    console.log('=== CURRENT SYSTEM ANALYSIS ===');
    
    // Check if there are any offers currently in transaction
    const offersInTransaction = await prisma.offer.findMany({
      where: {
        transaction: { isNot: null } // Has a linked transaction
      },
      include: {
        transaction: { select: { id: true, status: true } },
        user: { select: { username: true } }
      }
    });
    
    console.log(`Found ${offersInTransaction.length} offers currently in transaction:`);
    offersInTransaction.forEach(offer => {
      console.log(`- ${offer.user.username}: ${offer.type} ${offer.amount}@${offer.baseRate} [${offer.status}] → Transaction: ${offer.transaction?.status}`);
    });
    
    if (offersInTransaction.length === 0) {
      console.log('(No offers currently in transaction)');
    }
    
    console.log('');
    console.log('🎯 SCENARIO 2: Offers in transaction - are they excluded from matching?');
    console.log('=================================================================');
    console.log('');
    
    // Check the matching query to see if it excludes offers in transaction
    console.log('Current matching query analysis:');
    console.log('');
    console.log('The findPotentialMatches query searches for:');
    console.log('├─ type = opposite_type ✅');
    console.log('├─ currencyPair = same_pair ✅');
    console.log('├─ status = "ACTIVE" ✅');
    console.log('├─ userId != current_user ✅');
    console.log('├─ amount = matching_amount ✅');
    console.log('├─ baseRate within tolerance ✅');
    console.log('└─ transaction IS NULL ??? ← Let\'s check this!');
    console.log('');
    
    // Test what the current query would find
    console.log('Testing current matching behavior...');
    
    // Simulate a search for BUY offers (what a SELL offer would match with)
    const potentialBuyMatches = await prisma.offer.findMany({
      where: {
        type: 'BUY',
        currencyPair: 'CAD-IRR',
        status: 'ACTIVE',
        amount: 100,
        baseRate: {
          gte: 200 * 0.99,
          lte: 200 * 1.01
        }
      },
      include: {
        user: { select: { username: true } },
        transaction: { select: { id: true, status: true } }
      }
    });
    
    console.log(`\nFound ${potentialBuyMatches.length} potential BUY matches:`);
    potentialBuyMatches.forEach(offer => {
      const inTransaction = offer.transaction ? `IN TRANSACTION (${offer.transaction.status})` : 'Available';
      console.log(`- ${offer.user.username}: BUY ${offer.amount}@${offer.baseRate} [${offer.status}] - ${inTransaction}`);
      
      if (offer.transaction && offer.status === 'ACTIVE') {
        console.log('  ⚠️  WARNING: Offer is ACTIVE but already in transaction!');
      }
    });
    
    console.log('');
    console.log('🔧 RECOMMENDED BEHAVIOR:');
    console.log('========================');
    console.log('');
    console.log('When match is accepted and transaction created:');
    console.log('1. ✅ Offers should be marked as INACTIVE or IN_TRANSACTION');
    console.log('2. ✅ Competing matches should be cancelled');
    console.log('3. ✅ Other offers remain ACTIVE and available');
    console.log('4. ✅ Matching queries should exclude offers with transactions');
    console.log('');
    
    console.log('Current Implementation Status:');
    console.log('├─ ✅ Competing matches cancelled');
    console.log('├─ ❓ Offer status update when transaction created');
    console.log('└─ ❓ Matching query excludes offers in transaction');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testOfferLifecycle();
