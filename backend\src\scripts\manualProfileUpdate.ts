import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting manual user profile update...');
    
    // Update all users one by one with hardcoded values
    const users = await prisma.user.findMany();
    
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      const email = user.email;
      const username = email.split('@')[0];
      
      // Calculate reputation score
      let score = 0;
      if (user.emailVerified) score += 10;
      if (user.phoneVerified) score += 15;
      
      // Calculate level based on score
      let level = 1;
      if (score >= 25) level = 3;
      else if (score >= 10) level = 2;
      
      console.log(`Updating user ${i+1}/${users.length}: ${email}`);
      console.log(`- Setting username to: ${username}`);
      console.log(`- Setting reputation score to: ${score}`);
      console.log(`- Setting reputation level to: ${level}`);
      
      try {
        // Update each field separately to avoid any issues
        await prisma.user.update({
          where: { id: user.id },
          data: { username }
        });
        
        await prisma.user.update({
          where: { id: user.id },
          data: { reputationScore: score }
        });
        
        await prisma.user.update({
          where: { id: user.id },
          data: { reputationLevel: level }
        });
        
        console.log(`✅ Successfully updated ${email}`);
      } catch (err) {
        console.error(`❌ Failed to update ${email}:`, err);
      }
    }
    
    console.log('\nAll users processed.');
    
  } catch (error) {
    console.error('Script error:', error);
  } finally {
    await prisma.$disconnect();
    console.log('Database disconnected.');
  }
}

main()
  .then(() => console.log('Script completed.'))
  .catch(e => {
    console.error('Fatal error:', e);
    process.exit(1);
  });
