const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function listUsers() {
  try {
    const users = await prisma.user.findMany();
    console.log('All users in database:');
    users.forEach(u => {
      console.log(`- ${u.email} (${u.username}) - Rep Level: ${u.reputationLevel}, Verified: ${u.emailVerified}/${u.phoneVerified}`);
    });
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

listUsers();
