// Simple test to create a user and test payment methods
async function testUserAndPaymentMethods() {
  const testEmail = `test-${Date.now()}@example.com`;
  const testPassword = 'password123';
  
  console.log('🚀 Testing Payment Methods with fresh user...');
  console.log(`📧 Test email: ${testEmail}\n`);

  try {
    // 1. Register a new user
    console.log('1️⃣ Registering new user...');
    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword
      })
    });
    
    const registerData = await registerResponse.json();
    console.log('Register status:', registerResponse.status);
    
    if (!registerResponse.ok) {
      console.log('❌ Registration failed:', registerData);
      return;
    }
    console.log('✅ User registered successfully');

    // 2. For testing purposes, let's manually verify the user via database update
    console.log('2️⃣ Manually verifying user for testing purposes...');
    
    // We'll directly update the database using the development database connection
    const userId = registerData.user.id;
    console.log(`User ID: ${userId}`);
    
    // For now, let's skip verification and try with an existing verified user
    console.log('ℹ️ For this test, we\'ll use a different approach...\n');

    // 3. Try to login anyway (might work if email verification is not strictly enforced)
    console.log('3️⃣ Attempting login...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login status:', loginResponse.status);
    console.log('Login response:', JSON.stringify(loginData, null, 2));
    
    if (!loginResponse.ok) {
      console.log('❌ Login failed - email verification required');
      console.log('ℹ️ Let\'s try with a mock verification approach...\n');
      
      // For testing, let's try using an existing user or create one that bypasses verification
      return await testWithBypassVerification();
    }
    
    const token = loginData.token;
    console.log('✅ Login successful, got token\n');

    return await runPaymentMethodTests(token);
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

// Function to test with bypassed verification
async function testWithBypassVerification() {
  console.log('🔄 Trying alternative approach - using pre-existing test user...');
  
  // Try to login with a known test user that might already exist
  const knownTestEmails = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
  ];
  
  for (const email of knownTestEmails) {
    console.log(`🔍 Trying to login with: ${email}`);
    
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: email,
        password: 'password123'
      })
    });
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log(`✅ Successfully logged in with ${email}`);
      return await runPaymentMethodTests(loginData.token);
    }
  }
  
  console.log('❌ Could not find a working test user');
  console.log('ℹ️ You may need to manually create a verified user first');
}

// Function to run the actual payment method tests
async function runPaymentMethodTests(token) {
  try {
    // 1. Create first payment method
    console.log('4️⃣ Creating first payment method (IRR, BANK_TRANSFER)...');
    const firstMethodResponse = await fetch('http://localhost:3000/api/payment-methods', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        currency: 'IRR',
        paymentMethodType: 'BANK_TRANSFER',
        bankName: 'First Bank',
        accountNumber: '**********',
        accountHolderName: 'Test User'
      })
    });
    
    const firstMethodData = await firstMethodResponse.json();
    console.log('First method status:', firstMethodResponse.status);
    console.log('First method response:', JSON.stringify(firstMethodData, null, 2));
    
    if (!firstMethodResponse.ok) {
      console.log('❌ First payment method creation failed');
      return;
    }
    console.log('✅ First payment method created\n');

    // 2. Create second payment method (same currency - this should work now!)
    console.log('5️⃣ Creating second payment method (IRR, DIGITAL_WALLET)...');
    const secondMethodResponse = await fetch('http://localhost:3000/api/payment-methods', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        currency: 'IRR',
        paymentMethodType: 'DIGITAL_WALLET',
        bankName: 'Second Bank',
        accountNumber: '**********',
        accountHolderName: 'Test User'
      })
    });
    
    const secondMethodData = await secondMethodResponse.json();
    console.log('Second method status:', secondMethodResponse.status);
    console.log('Second method response:', JSON.stringify(secondMethodData, null, 2));
    
    if (!secondMethodResponse.ok) {
      console.log('❌ Second payment method creation failed');
      console.log('ℹ️ If this failed with a unique constraint error, the fix didn\'t work');
      return;
    }
    console.log('✅ Second payment method created! 🎉 THIS PROVES THE CONSTRAINT FIX WORKS!\n');

    // 3. Get all payment methods
    console.log('6️⃣ Getting all payment methods...');
    const getAllResponse = await fetch('http://localhost:3000/api/payment-methods', {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const getAllData = await getAllResponse.json();
    console.log('Get all status:', getAllResponse.status);
    console.log('Get all response:', JSON.stringify(getAllData, null, 2));
    
    if (!getAllResponse.ok) {
      console.log('❌ Failed to get payment methods');
      return;
    }
    console.log(`✅ Retrieved ${getAllData.count} payment methods\n`);

    // 4. Delete the first payment method
    const firstMethodId = firstMethodData.data?.id;
    if (firstMethodId) {
      console.log('7️⃣ Deleting first payment method...');
      const deleteResponse = await fetch(`http://localhost:3000/api/payment-methods/${firstMethodId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const deleteData = await deleteResponse.json();
      console.log('Delete status:', deleteResponse.status);
      console.log('Delete response:', JSON.stringify(deleteData, null, 2));
      
      if (!deleteResponse.ok) {
        console.log('❌ Failed to delete payment method');
        return;
      }
      console.log('✅ Payment method deleted successfully\n');
    }

    // 5. Final verification - get all methods again
    console.log('8️⃣ Final verification - getting all payment methods...');
    const finalGetResponse = await fetch('http://localhost:3000/api/payment-methods', {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const finalGetData = await finalGetResponse.json();
    console.log('Final get status:', finalGetResponse.status);
    console.log('Final get response:', JSON.stringify(finalGetData, null, 2));
    
    console.log(`\n🎉 ALL TESTS COMPLETED SUCCESSFULLY! 🎉`);
    console.log(`✅ Created multiple payment methods for same currency (constraint fix working)`);
    console.log(`✅ Successfully deleted payment method`);
    console.log(`📊 Final count: ${finalGetData.count} payment methods`);
    
  } catch (error) {
    console.error('❌ Payment method test error:', error);
  }
}

// Run the test
testUserAndPaymentMethods();
