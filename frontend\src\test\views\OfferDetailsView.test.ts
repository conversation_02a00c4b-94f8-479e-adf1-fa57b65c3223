import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, type VueWrapper } from '@vue/test-utils'
import type { ComponentPublicInstance } from 'vue'
import { nextTick } from 'vue'
import OfferDetailsView from '@/views/OfferDetailsView.vue'
import type { OfferWithUser } from '@/types/offer'

// Mock the router
const mockParams = { id: 'offer-123' }
const mockPush = vi.fn()
vi.mock('vue-router', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    useRoute: () => ({
      params: mockParams
    }),
    useRouter: () => ({
      push: mockPush
    })
  }
})

// Mock offer service
const mockOfferService = {
  getOfferById: vi.fn(),
  expressInterest: vi.fn(),
  updateOfferStatus: vi.fn()
}
vi.mock('@/services/offerService', () => mockOfferService)

// Mock auth store
const mockAuthStore = {
  user: {
    id: 'user-123',
    username: 'testuser',
    reputationLevel: 3
  }
}
vi.mock('@/stores/auth', () => ({
  useAuthStore: vi.fn(() => mockAuthStore)
}))

// Mock interest store
const mockInterestStore = {
  expressInterest: vi.fn(),
  isExpressingInterest: false
}
vi.mock('@/stores/interestStore', () => ({
  useInterestStore: vi.fn(() => mockInterestStore)
}))

// Mock Vue I18n
const mockT = vi.fn((key: string) => key)
vi.mock('vue-i18n', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    useI18n: () => ({
      t: mockT
    })
  }
})

// Mock naive UI composables
const mockMessage = vi.fn()
const mockDialog = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn()
}
vi.mock('naive-ui', () => ({
  useMessage: () => mockMessage,
  useDialog: () => mockDialog
}))

// Mock components
vi.mock('@/components/ReputationIcon.vue', () => ({
  default: { template: '<div data-testid="reputation-icon">Reputation</div>' }
}))

vi.mock('@/components/InterestRequestCard.vue', () => ({
  default: { template: '<div data-testid="interest-request-card">Interest Card</div>' }
}))

describe('OfferDetailsView', () => {
  let wrapper: VueWrapper<ComponentPublicInstance>
    const createMockOffer = (overrides: Partial<OfferWithUser> = {}): OfferWithUser => ({
    id: 'offer-123',
    type: 'SELL',
    amount: 1000,
    baseRate: 150000,
    adjustmentForLowerRep: 2,
    adjustmentForHigherRep: 1,
    status: 'ACTIVE',
    createdAt: new Date().toISOString(),
    currencyPair: 'USD-IRR',
    userId: 'owner-456',
    user: {
      username: 'offerowner',
      reputationLevel: 4
    },
    userInterest: null,
    isOwner: false,
    ...overrides
  })

  const createComponent = () => {
    return mount(OfferDetailsView)
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockT.mockImplementation((key: string) => key)
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  describe('Loading State', () => {
    it('should show loading state initially', () => {
      mockOfferService.getOfferById.mockReturnValue(new Promise(() => {})) // Never resolves
      wrapper = createComponent()
      
      const loading = wrapper.find('[data-testid="nspin"]')
      expect(loading.exists()).toBe(true)
    })

    it('should call getOfferById with correct offer ID', () => {
      mockOfferService.getOfferById.mockResolvedValue(createMockOffer())
      wrapper = createComponent()
      
      expect(mockOfferService.getOfferById).toHaveBeenCalledWith('offer-123')
    })
  })

  describe('Offer Display', () => {
    it('should display offer details when loaded successfully', async () => {
      const mockOffer = createMockOffer()
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      expect(wrapper.text()).toContain('offerDetails.title')
      expect(wrapper.text()).toContain('offerowner')
      expect(wrapper.text()).toContain('1000')
      expect(wrapper.text()).toContain('150000')
    })

    it('should show correct offer type', async () => {
      const sellOffer = createMockOffer({ type: 'SELL' })
      mockOfferService.getOfferById.mockResolvedValue(sellOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      expect(wrapper.text()).toContain('offerDetails.sellCAD')
    })

    it('should display owner reputation', async () => {
      const mockOffer = createMockOffer()
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      const reputationIcon = wrapper.find('[data-testid="reputation-icon"]')
      expect(reputationIcon.exists()).toBe(true)
    })

    it('should show offer status', async () => {
      const mockOffer = createMockOffer({ status: 'ACTIVE' })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      expect(wrapper.text()).toContain('status.active')
    })
  })

  describe('Owner Actions', () => {
    it('should show edit button for owner', async () => {
      const mockOffer = createMockOffer({ isOwner: true })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      const editButton = wrapper.find('[data-testid="edit-offer-btn"]')
      expect(editButton.exists()).toBe(true)
    })

    it('should navigate to edit page when edit button clicked', async () => {
      const mockOffer = createMockOffer({ isOwner: true })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      const editButton = wrapper.find('[data-testid="edit-offer-btn"]')
      await editButton.trigger('click')
      
      expect(mockPush).toHaveBeenCalledWith('/offers/offer-123/edit')
    })

    it('should show status toggle for owner', async () => {
      const mockOffer = createMockOffer({ isOwner: true, status: 'ACTIVE' })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      const statusButton = wrapper.find('[data-testid="toggle-status-btn"]')
      expect(statusButton.exists()).toBe(true)
      expect(statusButton.text()).toContain('offerDetails.deactivate')
    })

    it('should update offer status when toggle clicked', async () => {
      const mockOffer = createMockOffer({ isOwner: true, status: 'ACTIVE' })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      mockOfferService.updateOfferStatus.mockResolvedValue({ success: true })
      
      wrapper = createComponent()
      await nextTick()
      
      const statusButton = wrapper.find('[data-testid="toggle-status-btn"]')
      await statusButton.trigger('click')
      
      expect(mockOfferService.updateOfferStatus).toHaveBeenCalledWith('offer-123', 'INACTIVE')
      expect(mockMessage).toHaveBeenCalledWith('offerDetails.statusUpdated', { type: 'success' })
    })
  })

  describe('Visitor Actions', () => {
    it('should show express interest button for visitors when no existing interest', async () => {
      const mockOffer = createMockOffer({ 
        isOwner: false, 
        status: 'ACTIVE',
        userInterest: null 
      })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      const interestButton = wrapper.find('[data-testid="express-interest-btn"]')
      expect(interestButton.exists()).toBe(true)
      expect(interestButton.text()).toContain('offerDetails.expressInterest')
    })

    it('should not show express interest button when user already has interest', async () => {
      const mockOffer = createMockOffer({ 
        isOwner: false,
        userInterest: { id: 'interest-123', status: 'PENDING' }
      })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      const interestButton = wrapper.find('[data-testid="express-interest-btn"]')
      expect(interestButton.exists()).toBe(false)
    })

    it('should show interest status when user has existing interest', async () => {
      const mockOffer = createMockOffer({ 
        isOwner: false,
        userInterest: { id: 'interest-123', status: 'PENDING' }
      })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      expect(wrapper.text()).toContain('interests.status.pending')
    })

    it('should express interest when button clicked', async () => {
      const mockOffer = createMockOffer({ isOwner: false, userInterest: null })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      mockInterestStore.expressInterest.mockResolvedValue({ success: true })
      
      wrapper = createComponent()
      await nextTick()
      
      const interestButton = wrapper.find('[data-testid="express-interest-btn"]')
      await interestButton.trigger('click')
      
      expect(mockInterestStore.expressInterest).toHaveBeenCalledWith('offer-123')
    })

    it('should not show express interest button for inactive offers', async () => {
      const mockOffer = createMockOffer({ 
        isOwner: false, 
        status: 'INACTIVE',
        userInterest: null 
      })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      const interestButton = wrapper.find('[data-testid="express-interest-btn"]')
      expect(interestButton.exists()).toBe(false)
      expect(wrapper.text()).toContain('offerDetails.offerInactive')
    })
  })

  describe('Error Handling', () => {
    it('should show error message when offer not found', async () => {
      mockOfferService.getOfferById.mockRejectedValue({ response: { status: 404 } })
      
      wrapper = createComponent()
      await nextTick()
      
      expect(wrapper.text()).toContain('errors.offerNotFound')
    })

    it('should show generic error for other API errors', async () => {
      mockOfferService.getOfferById.mockRejectedValue(new Error('Network error'))
      
      wrapper = createComponent()
      await nextTick()
      
      expect(wrapper.text()).toContain('errors.loadingOffer')
    })

    it('should handle express interest errors gracefully', async () => {
      const mockOffer = createMockOffer({ isOwner: false, userInterest: null })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      mockInterestStore.expressInterest.mockRejectedValue(new Error('Interest failed'))
      
      wrapper = createComponent()
      await nextTick()
      
      const interestButton = wrapper.find('[data-testid="express-interest-btn"]')
      await interestButton.trigger('click')
      
      expect(mockMessage).toHaveBeenCalledWith('errors.expressInterestFailed', { type: 'error' })
    })

    it('should handle status update errors gracefully', async () => {
      const mockOffer = createMockOffer({ isOwner: true, status: 'ACTIVE' })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      mockOfferService.updateOfferStatus.mockRejectedValue(new Error('Update failed'))
      
      wrapper = createComponent()
      await nextTick()
      
      const statusButton = wrapper.find('[data-testid="toggle-status-btn"]')
      await statusButton.trigger('click')
      
      expect(mockMessage).toHaveBeenCalledWith('errors.updateStatusFailed', { type: 'error' })
    })
  })

  describe('Interest Status Display', () => {
    const interestStatuses = [
      { status: 'PENDING', expectedText: 'interests.status.pending' },
      { status: 'ACCEPTED', expectedText: 'interests.status.accepted' },
      { status: 'DECLINED', expectedText: 'interests.status.declined' }
    ]

    interestStatuses.forEach(({ status, expectedText }) => {
      it(`should display correct status for ${status} interest`, async () => {
        const mockOffer = createMockOffer({ 
          isOwner: false,
          userInterest: { id: 'interest-123', status: status as any }
        })
        mockOfferService.getOfferById.mockResolvedValue(mockOffer)
        
        wrapper = createComponent()
        await nextTick()
        
        expect(wrapper.text()).toContain(expectedText)
      })
    })
  })

  describe('Rate Calculations', () => {
    it('should display calculated rates for different reputation tiers', async () => {
      const mockOffer = createMockOffer({
        type: 'SELL',
        baseRate: 150000,
        adjustmentForLowerRep: 2,
        adjustmentForHigherRep: 1
      })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      // Should show calculated rates
      expect(wrapper.text()).toContain('153000') // 150000 * 1.02
      expect(wrapper.text()).toContain('148500') // 150000 * 0.99
    })

    it('should handle buy offers correctly', async () => {
      const mockOffer = createMockOffer({
        type: 'BUY',
        baseRate: 150000
      })
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      expect(wrapper.text()).toContain('offerDetails.buyCAD')
    })
  })

  describe('Responsive Design', () => {
    it('should render mobile-friendly layout', async () => {
      const mockOffer = createMockOffer()
      mockOfferService.getOfferById.mockResolvedValue(mockOffer)
      
      wrapper = createComponent()
      await nextTick()
      
      const card = wrapper.find('[data-testid="ncard"]')
      expect(card.exists()).toBe(true)
    })
  })
})
