import apiClient from './apiClient';
import type { 
  GetReportsResponse,
  ParsedReport
} from '../types/admin';

/**
 * Admin debug report API service
 */

export interface GetReportsParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filterByType?: string;
  filterBySeverity?: string;
  filterByStatus?: string; // Add status filter support
  filterByDateStart?: string;
  filterByDateEnd?: string;
  searchQuery?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

/**
 * Fetch paginated debug reports with filtering and sorting
 */
export async function fetchReports(params: GetReportsParams = {}): Promise<GetReportsResponse> {
  const queryParams = new URLSearchParams();
  
  // Add all non-undefined parameters to query string
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, value.toString());
    }
  });

  const response = await apiClient.get<ApiResponse<GetReportsResponse>>(
    `/debug/admin/reports?${queryParams.toString()}`
  );

  if (!response.data.success || !response.data.data) {
    throw new Error(response.data.message || 'Failed to fetch debug reports');
  }

  return response.data.data;
}

/**
 * Fetch a specific debug report by ID
 */
export async function fetchReportById(reportId: string): Promise<ParsedReport | null> {
  try {
    const response = await apiClient.get<ApiResponse<ParsedReport | null>>(
      `/debug/admin/reports/${reportId}`
    );
    return response.data.data || null;
  } catch (err: any) {
    if (err?.response?.status === 404) {
      return null;
    }
    throw new Error(err?.response?.data?.message || 'Failed to fetch debug report');
  }
 }

/**
 * Get the export URL for a specific report
 */
export function getReportExportUrl(reportId: string): string {
  return `${apiClient.defaults.baseURL}/debug/admin/reports/${reportId}/export`;
}

/**
 * Download a report as JSON file
 */
export async function downloadReport(reportId: string): Promise<void> {
  try {
    const response = await apiClient.get(`/debug/admin/reports/${reportId}/export`, {
      responseType: 'blob',
    });

    // Create a download link
    const blob = new Blob([response.data], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `debug_report_${reportId}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Failed to download report:', error);
    throw error;
  }
}

/**
 * Update a report's status
 */
export async function updateReportStatus(
  reportId: string, 
  status: string, 
  comment?: string
): Promise<void> {
  console.log('[AdminDebugReportService] updateReportStatus called:', { reportId, status, comment });
  try {
    const response = await apiClient.put<ApiResponse<null>>(
      `/debug/admin/reports/${reportId}/status`,
      { status, comment }
    );

    console.log('[AdminDebugReportService] Response received:', response.data);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to update report status');
    }
    
    console.log('[AdminDebugReportService] Status update successful');
  } catch (error: any) {
    console.error('[AdminDebugReportService] Failed to update report status:', error);
    throw new Error(error?.response?.data?.message || 'Failed to update report status');
  }
}
