# MUNygo Dual Environment Setup

This setup allows you to seamlessly switch between SQLite (local development) and PostgreSQL (Docker) environments across different operating systems.

## Cross-Platform Support

The environment switching scripts are now cross-platform compatible:
- **Windows**: Uses PowerShell script (`switch-env.ps1`)
- **macOS/Linux**: Uses Bash script (`switch-env.sh`)
- **Automatic Detection**: `npm run` commands automatically detect your OS and use the appropriate script

## Quick Start

```bash
# Initial setup (works on all platforms)
cd C:\Code\MUNygo\backend  # Windows
# or
cd ~/Code/MUNygo/backend   # macOS/Linux

# Direct commands (cross-platform):
npm run dev:sqlite     # Start with SQLite
npm run dev:postgres   # Start with PostgreSQL (requires Docker)
```

## Environment Management

### Switch Environments

```bash
# Switch to SQLite (local development) - cross-platform
npm run env:sqlite

# Switch to PostgreSQL (Docker) - cross-platform
npm run env:postgres
```

### Start Development Server

```powershell
# Start with SQLite
npm run dev:sqlite

# Start with PostgreSQL
npm run dev:postgres
```

### Database Operations

```powershell
# Migrations
npm run migrate:sqlite    # Run migrations on SQLite
npm run migrate:postgres  # Run migrations on PostgreSQL

# Reset databases (fresh start)
npm run reset:sqlite      # Reset SQLite database
npm run reset:postgres    # Reset PostgreSQL database

# Prisma Studio
npm run studio:sqlite     # Open Studio for SQLite
npm run studio:postgres   # Open Studio for PostgreSQL
```

## File Structure

```
backend/
├── prisma/
│   ├── schema.prisma             # Current active schema (auto-switched)
│   ├── schema.sqlite.template    # SQLite schema template
│   ├── schema.postgres.template  # PostgreSQL schema template
│   └── dev.db                   # SQLite database file
├── .env                         # Current environment file (auto-switched)
├── .env.sqlite                  # SQLite environment variables
├── .env.postgres                # PostgreSQL environment variables
├── switch-env.ps1               # Windows PowerShell environment switching script
├── switch-env.sh                # Unix/Linux Bash environment switching script
└── setup-env.ps1                # Initial setup script (Windows)
```

## Cross-Platform Script Details

### Windows (PowerShell)
- **File**: `switch-env.ps1`
- **Features**: Colored output, error checking, validation
- **Usage**: Automatically used on Windows via `npm run` commands

### Unix-like Systems (Bash)
- **File**: `switch-env.sh`
- **Features**: Colored output, error checking, validation, POSIX-compliant
- **Usage**: Automatically used on macOS/Linux via `npm run` commands

Both scripts provide identical functionality and user experience across platforms.

## Environment Configurations

### SQLite Configuration
- **Database**: `file:./dev.db`
- **Frontend URL**: `http://localhost:5173`
- **Use case**: Local development, fast iteration
- **Pros**: No Docker required, fast startup, portable
- **Cons**: Limited concurrency, no production similarity

### PostgreSQL Configuration
- **Database**: `postgresql://munygo:yourpassword@localhost:5432/munygo`
- **Frontend URL**: `http://localhost`
- **Use case**: Docker testing, production similarity
- **Pros**: Production-like environment, better concurrency
- **Cons**: Requires Docker, slower startup

## Docker Integration

### Start PostgreSQL Container

```powershell
# From project root
cd C:\Code\MUNygo
docker-compose up -d postgres

# Or start all services
docker-compose up -d
```

### Check Container Status

```powershell
docker-compose ps
docker-compose logs postgres
```

## Workflow Examples

### Local Development (SQLite)

```powershell
# 1. Switch to SQLite
npm run env:sqlite

# 2. Run migrations if needed
npm run migrate:sqlite

# 3. Start development
npm run dev

# 4. Open Prisma Studio (in another terminal)
npm run studio:sqlite
```

### Docker Testing (PostgreSQL)

```powershell
# 1. Start PostgreSQL container
cd C:\Code\MUNygo
docker-compose up -d postgres

# 2. Switch to PostgreSQL
cd backend
npm run env:postgres

# 3. Run migrations
npm run migrate:postgres

# 4. Start development
npm run dev

# 5. Open Prisma Studio (in another terminal)
npm run studio:postgres
```

### Full Docker Stack Testing

```powershell
# Start everything with Docker
cd C:\Code\MUNygo
docker-compose up --build -d

# Check logs
docker-compose logs -f backend
```

## Database Schema Differences

Both schemas are functionally identical, with only these differences:

### SQLite Schema
```prisma
datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// JSON fields use String for SQLite compatibility
data String? // For storing additional context
```

### PostgreSQL Schema
```prisma
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// JSON fields use native Json type
data Json? // For storing additional context
```

## Environment Variables

### Shared Variables
- `JWT_SECRET`: JWT signing secret
- `TWILIO_*`: Phone verification settings
- `EMAIL_*`: Email service configuration
- `MOCK_TWILIO_OTP`: Development OTP bypass

### Environment-Specific Variables
- `DATABASE_URL`: Database connection string
- `FRONTEND_URL`: Frontend URL for CORS

## Troubleshooting

### Prisma Issues

```powershell
# Regenerate Prisma client
npx prisma generate

# Reset database
npm run reset:sqlite    # or reset:postgres

# Check schema status
npx prisma migrate status
```

### Environment Issues

```powershell
# Check current environment
Get-Content .env | Select-String "DATABASE_URL"

# Manual switch
Copy-Item ".env.sqlite" ".env" -Force
npx prisma generate
```

### Database Connection Issues

```powershell
# SQLite: Check if file exists
Test-Path "prisma\dev.db"

# PostgreSQL: Check Docker container
docker-compose ps
docker-compose logs postgres
```

## Best Practices

1. **Default to SQLite** for daily development
2. **Test with PostgreSQL** before major releases
3. **Keep schemas in sync** between sqlite and postgres files
4. **Use Docker** for integration testing
5. **Commit only schema.prisma** (it gets auto-generated)

## Migration Strategy

When updating the database schema:

1. **Update one schema file** (sqlite or postgres)
2. **Copy changes** to the other schema file
3. **Test migrations** on both environments
4. **Commit both schema files** for consistency

```powershell
# Example workflow
npm run env:sqlite
# Make schema changes in schema.sqlite.prisma
npm run migrate:sqlite

# Copy changes to postgres schema
# Update schema.postgres.prisma with same changes
npm run env:postgres
npm run migrate:postgres
```

This setup gives you the flexibility to develop quickly with SQLite while ensuring your app works correctly with PostgreSQL for production deployment.
