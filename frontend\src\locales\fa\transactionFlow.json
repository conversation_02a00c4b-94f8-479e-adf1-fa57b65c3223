{"title": "جریان تراکنش", "loading": "در حال بارگذاری جزئیات تراکنش...", "error": "امکان بارگذاری تراکنش وجود ندارد", "transactionId": "شناسه تراکنش", "steps": {"designate": "تعیین پرداخت‌کننده", "designateDescription": "تصمیم‌گیری برای پرداخت‌کننده اول", "payment1": "پرداخت ۱", "payment1Description": "پرداخت طرف اول", "confirmation1": "تأیید ۱", "confirmation1Description": "تأیید طرف دوم", "payment2": "پرداخت ۲", "payment2Description": "پرداخت طرف دوم", "confirmation2": "تأیید ۲", "confirmation2Description": "ت<PERSON><PERSON>ید طرف اول", "completed": "تکمیل شده", "completedDescription": "پایان تراکنش"}, "userRoles": {"providerOf": "ارائه‌دهنده {currency}", "loadingRole": "در حال بارگذاری نقش...", "userDataMissing": "اطلاعات کاربر در تراکنش موجود نیست...", "determiningRole": "در حال تعیین نقش..."}, "currentAction": {"loadingAction": "در حال بارگذاری...", "awaitingDesignation": "در انتظار تعیین پرداخت‌کننده اول", "yourTurnToPay": "نوبت پرداخت شماست", "yourTurnToConfirm": "نوبت تأیید پرداخت شماست", "waitingForPayment": "در انتظار پرداخت {username}", "waitingForConfirmation": "در انتظار تأیید {username}", "transactionCompleted": "تراکنش با موفقیت تکمیل شد", "transactionCancelled": "تراکنش لغو شد", "transactionDisputed": "تراکنش در اختلاف قرار دارد"}, "actionInfo": {"designationPending": "هر دو طرف باید در مورد پرداخت‌کننده اول توافق کنند. از ابزارهای مذاکره زیر برای رسیدن به توافق استفاده کنید.", "youPayFirst": "شما به عنوان پرداخت‌کننده اول تعیین شده‌اید. لطفاً {amount} {currency} را به {username} ارسال کنید و سپس پرداخت خود را اعلام کنید.", "youConfirmPayment": "لطفاً تأیید کنید که {amount} {currency} را از {username} دریافت کرده‌اید.", "otherPartyPaying": "{username} ابتدا {amount} {currency} را به شما پرداخت خواهد کرد. پس از دریافت، تأیید خواهید کرد.", "otherPartyConfirming": "در انتظار تأیید {username} برای دریافت پرداخت {amount} {currency} شما.", "youPaySecond": "حالا نوبت شماست که {amount} {currency} را به {username} پرداخت کنید.", "youConfirmSecond": "لطفاً تأیید کنید که آخرین پرداخت {amount} {currency} را از {username} دریافت کرده‌اید.", "waitingSecondPayment": "در انتظار ارسال {amount} {currency} از طرف {username}.", "waitingSecondConfirmation": "در انتظار تأیید {username} برای دریافت پرداخت شما.", "completedExchange": "تبادل <strong>{amountA} {currencyA}</strong> با <strong>{amountB} {currencyB}</strong> کامل شد.", "cancellationReason": "دلیل", "disputeReason": "دلیل", "noReasonProvided": "هیچ دلیلی ارائه نشده.", "adminReview": "یک مدیر بررسی خواهد کرد.", "followInstructions": "لطفاً دستورالعمل‌ها را دنبال کنید."}, "subSteps": {"agreementReached": "توافق در مورد پرداخت‌کننده اول حاصل شد", "paymentInfoProvided": "اطلاعات پرداخت توسط هر دو طرف ارائه شد", "firstPaymentDeclared": "پرداخت اول توسط {username} اعلام شد", "firstPaymentConfirmed": "پرداخت اول توسط {username} تأیید شد", "secondPaymentDeclared": "پرداخت دوم توسط {username} اعلام شد", "secondPaymentConfirmed": "پرداخت نهایی توسط {username} تأیید شد", "allStepsCompleted": "تمام مراحل تراکنش با موفقیت تکمیل شد", "transactionWasCancelled": "تراکنش لغو شد", "transactionWasDisputed": "تراکنش مورد اختلاف قرار گرفت", "designateFirstPayer": "تعیین اینکه چه کسی ابتدا پرداخت کند", "userMakesPayment": "{username} پرد<PERSON><PERSON>ت می‌کند", "userConfirmsReceipt": "{username} دریافت را تأیید می‌کند", "transactionComplete": "تراکنش تکمیل شد!", "transactionCancelled": "تراکنش لغو شد", "transactionDisputed": "تراکنش مورد اختلاف قرار گرفت", "designationPending": "هر دو طرف باید در مورد اینکه چه کسی ابتدا پرداخت کند توافق کنند. از ابزارهای مذاکره زیر برای رسیدن به توافق استفاده کنید.", "youPayFirst": "شما برای پرداخت اول انتخاب شده‌اید. لطفاً {amount} {currency} را به {username} ارسال کرده و سپس پرداخت خود را اعلام کنید.", "youConfirmPayment": "لطفاً تأیید کنید که {amount} {currency} را از {username} دریافت کرده‌اید.", "otherPartyPaying": "{username} ابتدا {amount} {currency} را به شما پرداخت خواهد کرد. شما پس از دریافت تأیید خواهید کرد.", "otherPartyConfirming": "در انتظار تأیید {username} برای دریافت پرداخت {amount} {currency} شما.", "youPaySecond": "اکنون نوبت شماست که {amount} {currency} را به {username} پرداخت کنید.", "youConfirmSecond": "لطفاً تأیید کنید که پرداخت نهایی {amount} {currency} را از {username} دریافت کرده‌اید.", "waitingSecondPayment": "در انتظار ارسال {amount} {currency} توسط {username}.", "waitingSecondConfirmation": "در انتظار تأیید دریافت پرداخت شما توسط {username}.", "completedExchange": "تبادل <strong>{amountA} {currencyA}</strong> با <strong>{amountB} {currencyB}</strong> تکمیل شده است.", "cancellationReason": "دلیل", "disputeReason": "دلیل", "noReasonProvided": "دلیلی ارائه نشده است.", "adminReview": "یک ادمین بررسی خواهد کرد.", "followInstructions": "لطفاً دستورالعمل‌ها را دنبال کنید."}, "buttons": {"declarePayment": "اعلام پرداخت", "confirmReceipt": "تأیید دریافت", "cancelTransaction": "لغو تراکنش", "disputeTransaction": "اختلاف تراکنش", "agreeToProposal": "موافقت با {target}", "agreeToSystemRecommendation": "موافقت با پیشنهاد سیستم", "agreeToUserProposal": "موافقت با پیشنهاد {username}", "proposeOtherPaysFirst": "پیشنهاد پرداخت ابتدا توسط {username}", "useProfileDetails": "استفاده از جزئیات پرداخت پروفایل"}, "timer": {"timeRemaining": "زمان باقی‌مانده:", "timeElapsed": "زمان سپری شده:", "timeLeft": "زمان باقی‌مانده:", "timePassed": "زمان گذشته:", "paymentWindow": "مهلت پرداخت:", "confirmationWindow": "پنجره تأیید:", "expired": "منقضی شده"}, "negotiation": {"systemRecommendation": "سیستم توصیه می‌کند <strong>{username}</strong> ابتدا پرداخت کند.", "systemRecommendationReason": "دلیل: {reason}", "reasonReputation": "{username} سطح اعتبار کمتری دارد و باید ابتدا پرداخت کند.", "reasonCurrency": "{username} در حال ارائه {currency} است و باید ابتدا پرداخت کند.", "reasonCurrencyGeneric": "{username} در حال ارائه ارز محلی است و باید ابتدا پرداخت کند.", "reasonOfferCreator": "{username} پیشنهاد را ایجاد کرده و باید ابتدا پرداخت کند.", "systemRecommendationDetermining": "توصیه سیستم در حال تعیین است.", "proposalFrom": "پ<PERSON><PERSON><PERSON><PERSON><PERSON> از {username}", "systemProposal": "توصیه سیستم", "waitingForResponse": "در انتظار پاسخ طرف مقابل به پیشنهاد شما.", "bothPartiesAgreed": "هر دو طرف با پیشنهاد فعلی موافقت کرده‌اند.", "negotiationFinalized": "مذاکره نهایی شده است.", "youAgreed": "شما با این پیشنهاد موافقت کرده‌اید.", "otherPartyAgreed": "{username} با این پیشنهاد موافقت کرده است.", "agreementStatus": "وضعیت توافق", "pendingResponse": "در انتظار پاسخ", "finalized": "نهایی شده", "youAgreedWaitingForFinalize": "شما با پیشنهاد {username} موافقت کردید. در انتظار نهایی‌سازی توسط آنها یا پردازش سیستم.", "awaitingYourResponse": "در انتظار پاسخ شما به پیشنهاد {username}.", "youAgreedSystemWaiting": "شما با توصیه سیستم موافقت کردید. در انتظار {username}.", "otherPartyAgreedSystemWaiting": "{username} با توصیه سیستم موافقت کرد. در انتظار شما.", "bothPartiesAgreedFinalizing": "هر دو طرف با توصیه سیستم موافقت کردند. در حال نهایی‌سازی...", "agreementReached": "توافق حاصل شد! {username} ابتدا پرداخت خواهد کرد.", "negotiationStatus": "وضعیت مذاکره", "proposalActive": "پیشنهاد فعال", "agreementReachedTitle": "توافق حاصل شد", "proposal": "پیشنهاد", "usersProposal": "پ<PERSON><PERSON><PERSON><PERSON><PERSON> {username}", "userProposal": "پیشنهاد از کاربر", "reasonLabel": "دلیل", "proposedPayer": "پرداخت‌کننده پیشنهادی: {username}"}, "paymentDetails": {"accountHolderName": "نام صاحب حساب", "bankName": "نام بانک", "recipientDetails": "جزئیات پرداخت گیرنده", "accountNumber": "شماره حساب", "maskedAccount": "****{lastDigits}", "institutionName": "نام موسسه", "additionalInfo": "اطلاعات اضافی", "noDetailsAvailable": "جزئیات پرداخت پس از نهایی شدن مذاکره در دسترس خواهد بود.", "paymentDueDate": "سررسید پرداخت:"}, "messages": {"paymentDeclared": "پرداخت با موفقیت اعلام شد", "paymentConfirmed": "دریافت پرداخت تأیید شد", "transactionCancelled": "تراکنش لغو شد", "transactionDisputed": "اختلاف تراکنش ارسال شد", "proposalSubmitted": "پیشنهاد با موفقیت ارسال شد", "agreementConfirmed": "شما با پیشنهاد فعلی موافقت کردید", "paymentDetailsSubmitted": "جزئیات دریافت پرداخت با موفقیت ارسال شد", "profileDetailsConfirmed": "جزئیات پروفایل برای این تراکنش تأیید شد", "errorOccurred": "خطایی رخ داد. لطفاً دوباره تلاش کنید.", "paymentGateRequired": "لطفاً اطلاعات پرداخت خود را برای ادامه ارائه دهید", "waitingForOtherParty": "در انتظار ارائه اطلاعات پرداخت توسط طرف مقابل"}, "modals": {"declarePaymentTitle": "اعلام ارسال پرداخت", "trackingNumberLabel": "شماره پیگیری (اختیاری)", "trackingNumberPlaceholder": "در صورت وجود شماره پیگیری را وارد کنید", "cancelTransactionTitle": "لغو تراکنش", "cancelReasonLabel": "دلیل لغو", "cancelReasonPlaceholder": "لطفاً دلیل لغو را ارائه دهید", "disputeTransactionTitle": "اختلاف تراکنش", "disputeReasonLabel": "دلیل اختلاف", "disputeReasonPlaceholder": "لطفاً مشکل را توضیح دهید", "proposalTitle": "پیشنهاد پرداخت ابتدا توسط {username}", "proposalExplanation": "شما پیشنهاد می‌کنید که {username} به جای شما ابتدا پرداخت کند.", "proposalMessageLabel": "پیام برای حمایت از پیشنهادتان (اختیاری)", "proposalMessagePlaceholder": "توضیح دهید چرا فکر می‌کنید {username} باید ابتدا پرداخت کند (مثال: 'فکر می‌کنم شما باید ابتدا پرداخت کنید چون اعتبار بالاتری دارید')", "submitProposal": "پیشنهاد پرداخت ابتدا توسط {username}"}}