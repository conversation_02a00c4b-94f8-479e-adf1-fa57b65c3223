# Pinned Action Banner Implementation

## Overview

The Pinned Action Banner is a critical UX feature that ensures users never lose track of their required actions during chat scrolling. It provides a sticky banner at the top of the feed that displays the current action the user needs to take.

## Key Features

### 1. **Persistent Visibility**
- The banner remains visible at the top of the scrollable feed area
- Positioned between `TheSmartStatusBar` and `TheUnifiedFeed`
- Uses `position: sticky` to stay visible during scroll

### 2. **Dual Rendering System**
- **Full ActionCard**: Rendered chronologically within the feed's timeline
- **Pinned Banner**: Always visible summary at the top
- Both reference the same action but serve different purposes

### 3. **Smart Scrolling**
- "View Details" button smoothly scrolls to the full ActionCard
- Includes highlight effects for better user feedback
- <PERSON><PERSON> special cases for Dynamic Action Bar steps

### 4. **Lifecycle Management**
- Automatically appears when user action is required
- Cleared immediately upon action completion
- Survives chat scrolling and new messages

## Technical Implementation

### Store State

```typescript
interface PinnedAction {
  title: string;        // i18n key for the action title
  cardId: string;       // ID to scroll to the full action card
  actionType: string;   // Type of action (paymentInfo, negotiation, etc.)
  stepIndex: number;    // Current step index
}

const pinnedAction = ref<PinnedAction | null>(null);
```

### Component Structure

```vue
<!-- PinnedActionBanner.vue -->
<template>
  <div v-if="pinnedAction" class="pinned-action-banner">
    <div class="banner-content">
      <div class="action-info">
        <div class="action-icon"><!-- Dynamic icon --></div>
        <div class="action-text">
          <h4 class="action-title">{{ $t(pinnedAction.title) }}</h4>
          <p class="action-subtitle">{{ getActionSubtitle(pinnedAction.actionType) }}</p>
        </div>
      </div>
      
      <n-button @click="scrollToAction">
        View Details
      </n-button>
    </div>
  </div>
</template>
```

### Integration Points

1. **TransactionView.vue**: Renders the banner between status bar and feed
2. **transactionalChatStore.ts**: Manages pinned action state and scroll logic
3. **ActionCard.vue**: Includes `data-card-id` for scroll targeting
4. **TheDynamicActionBar.vue**: Includes highlight styles for special steps

## Step-by-Step Behavior

### Steps 1-2: Regular Action Cards
- Banner shows for `paymentInfo` and `negotiation` steps
- Scrolls to traditional ActionCard components in the feed
- Banner disappears when action is completed

### Steps 3-4: Dynamic Action Bar Integration
- Banner shows for `confirmReceipt` and `yourTurnToPay` steps
- "View Details" scrolls to and highlights the Dynamic Action Bar
- Special handling since these steps don't use traditional ActionCards

### Steps 5-7: Mixed Behavior
- Step 6: No banner (waiting state)
- Step 7: Banner returns for `rateExperience` action

## Accessibility Features

- **Keyboard Navigation**: All interactive elements are focusable
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **High Contrast Mode**: Enhanced borders and contrast
- **Reduced Motion**: Respects user's motion preferences

## Internationalization

All text content is fully internationalized:

```json
{
  "pinnedBanner": {
    "viewDetails": "View Details",
    "subtitles": {
      "paymentInfo": "Complete this step to continue",
      "negotiation": "Your decision is required",
      "confirmReceipt": "Action required - check your account",
      "yourTurnToPay": "Send payment using the details below",
      "rateExperience": "Share your experience",
      "default": "Action required"
    }
  }
}
```

## Responsive Design

- **Mobile-First**: Optimized for touch interactions
- **Flexible Layout**: Adapts to different screen sizes
- **Safe Areas**: Respects device-specific safe areas
- **Touch Targets**: Minimum 44px touch targets

## Visual Effects

### Highlight Animation
```css
.highlight-card {
  box-shadow: 0 0 0 3px var(--tc-primary), var(--tc-action-card-shadow);
  transform: scale(1.02);
  transition: all 0.3s ease;
}
```

### Entrance Animation
```css
.pinned-action-banner {
  animation: slideDown 0.3s ease-out;
}
```

## Testing

Comprehensive test suite covers:
- ✅ Visibility logic based on store state
- ✅ Content rendering and i18n
- ✅ Icon selection for different action types
- ✅ Scroll functionality and store integration
- ✅ Responsive behavior and accessibility
- ✅ Error handling for edge cases

## Usage Examples

### Setting a Pinned Action
```typescript
// In the store
const setPinnedAction = (actionType: string, cardId: string) => {
  pinnedAction.value = {
    title: currentStep.value.titleKey,
    cardId,
    actionType,
    stepIndex: currentStepIndex.value
  };
};
```

### Clearing a Pinned Action
```typescript
// After successful action completion
const clearPinnedAction = () => {
  pinnedAction.value = null;
};
```

### Scrolling to Action
```typescript
// From the banner component
const scrollToAction = () => {
  if (pinnedAction.value) {
    transactionStore.scrollToActionCard(pinnedAction.value.cardId);
  }
};
```

## Benefits

1. **Never Lose Context**: Users always know what action is required
2. **Freedom to Chat**: Can scroll and chat without losing their place
3. **Clear Visual Hierarchy**: Separates communication from required actions
4. **Smooth UX Flow**: Seamless navigation between banner and full details
5. **Accessible Design**: Works for all users regardless of abilities

This implementation fulfills the critical UX requirement that "the user's current required action must never be lost due to chat scrolling" while maintaining all the benefits of a chronological chat timeline.
