-- Enhanced Tag System Migration
-- This creates a proper tag management system with categories, hierarchies, and metadata

-- Create trigger function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create tag categories table
CREATE TABLE tag_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  color VARCHAR(7), -- hex color
  icon VARCHAR(50),  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create predefined tags table
CREATE TABLE predefined_tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tag_key VARCHAR(50) NOT NULL UNIQUE, -- internal key like 'urgent', 'new-feature'
  category_id UUID REFERENCES tag_categories(id) ON DELETE SET NULL,
  parent_tag_id UUID REFERENCES predefined_tags(id) ON DELETE SET NULL, -- for hierarchical tags
  default_label_en VARCHAR(100) NOT NULL,
  default_label_fa VARCHAR(100),
  description_en TEXT,
  description_fa TEXT,
  color VARCHAR(7), -- override category color if needed
  sort_order INTEGER DEFAULT 0,  applicable_report_types TEXT[], -- array of report types this tag applies to
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create tag metadata for dynamic properties
CREATE TABLE tag_metadata (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),  tag_id UUID REFERENCES predefined_tags(id) ON DELETE CASCADE,
  metadata_key VARCHAR(50) NOT NULL,
  metadata_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(tag_id, metadata_key)
);

-- Insert default categories
INSERT INTO tag_categories (name, description, color, icon, sort_order) VALUES
('severity', 'Tags related to issue severity', '#ef4444', 'alert-triangle', 1),
('type', 'Tags related to issue classification', '#3b82f6', 'tag', 2),
('area', 'Tags related to application areas', '#8b5cf6', 'map', 3),
('priority', 'Tags related to priority levels', '#f59e0b', 'flag', 4),
('status', 'Tags related to resolution status', '#10b981', 'check-circle', 5);

-- Insert predefined tags with proper categorization
INSERT INTO predefined_tags (tag_key, category_id, default_label_en, default_label_fa, applicable_report_types, sort_order) VALUES
-- Severity tags
('urgent', (SELECT id FROM tag_categories WHERE name = 'severity'), 'Urgent', 'فوری', ARRAY['bug', 'performance'], 1),
('critical', (SELECT id FROM tag_categories WHERE name = 'severity'), 'Critical', 'بحرانی', ARRAY['bug', 'performance'], 2),

-- Type tags
('fix-needed', (SELECT id FROM tag_categories WHERE name = 'type'), 'Fix Needed', 'نیاز به رفع', ARRAY['bug'], 1),
('enhancement', (SELECT id FROM tag_categories WHERE name = 'type'), 'Enhancement', 'بهبود', ARRAY['feature-request', 'improvement'], 2),
('new-feature', (SELECT id FROM tag_categories WHERE name = 'type'), 'New Feature', 'ویژگی جدید', ARRAY['feature-request'], 3),

-- Area tags  
('user-experience', (SELECT id FROM tag_categories WHERE name = 'area'), 'User Experience', 'تجربه کاربری', ARRAY['ui-ux', 'improvement'], 1),
('performance', (SELECT id FROM tag_categories WHERE name = 'area'), 'Performance', 'عملکرد', ARRAY['performance'], 2),
('documentation', (SELECT id FROM tag_categories WHERE name = 'area'), 'Documentation', 'مستندات', ARRAY['question'], 3);

-- Create indexes
CREATE INDEX idx_predefined_tags_category ON predefined_tags(category_id);
CREATE INDEX idx_predefined_tags_parent ON predefined_tags(parent_tag_id);
CREATE INDEX idx_predefined_tags_active ON predefined_tags(is_active);
CREATE INDEX idx_tag_metadata_key ON tag_metadata(metadata_key);

-- Create triggers to automatically update updated_at timestamps
CREATE TRIGGER update_tag_categories_updated_at
    BEFORE UPDATE ON tag_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_predefined_tags_updated_at
    BEFORE UPDATE ON predefined_tags
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
