// src/stores/offerStore.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// Import OfferStatusFrontend if needed for type annotations, otherwise string literals are used directly.
import type { BrowseOffer, ViewedOfferDetails, OfferStatusFrontend } from '../types/offer'; 
// Removed incorrect import: import { OfferStatus } from '../types/offer';
// Import the socket event constants
import centralizedSocketManager from '../services/centralizedSocketManager';
import {
  OFFER_CREATED,
  OFFER_UPDATED,
  OFFER_STATUS_CHANGED,
  TRANSACTION_STATUS_UPDATED,
  NEGOTIATION_STATE_UPDATED,
} from '../types/socketEvents';
import { offerService } from '../services/offerService';
import { EventBus, EventTypes, type EventPayloads } from '../services/eventBus';

// Define minimal payload types for socket events
interface OfferIdPayload {
  offerId: string;
}

interface OfferStatusChangedMinimalPayload {
  offerId: string;
  newStatus: OfferStatusFrontend; // Use the imported type here
}


export const useOfferStore = defineStore('offerStore', () => {
  const offers = ref<BrowseOffer[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const currentlyViewedOfferDetails = ref<ViewedOfferDetails | null>(null);
  let socketListenersAttached = false;  let unsubscribeInterestStatusChanged: (() => void) | null = null;
  let unsubscribeOfferStatusChanged: (() => void) | null = null;
  let unsubscribeOfferUpdated: (() => void) | null = null;
  let unsubscribeOfferCreated: (() => void) | null = null;
  let unsubscribeTransactionStatusUpdated: (() => void) | null = null;
  let unsubscribeNegotiationStateUpdated: (() => void) | null = null;

  async function loadOffers() {
    isLoading.value = true;
    error.value = null;
    try {
      console.log('[offerStore] Loading offers via API...');      const freshOffers: BrowseOffer[] = await offerService.getBrowseOffers();
      
      offers.value = freshOffers.map((newOffer: BrowseOffer) => {
        const existingOffer = offers.value.find(o => o.id === newOffer.id);
        return {
          ...newOffer,
          currentUserHasShownInterest: newOffer.currentUserHasShownInterest ?? existingOffer?.currentUserHasShownInterest ?? false
        };
      });
      console.log('[offerStore] Offers loaded:', freshOffers.length);
    } catch (e: any) {
      error.value = e?.message || 'Failed to load offers';
      console.error('[offerStore] Error loading offers:', e);
    } finally {
      isLoading.value = false;
    }
  }

  async function fetchAndDisplayOfferForModal(offerId: string) {
    if (!offerId) return;
      const initialState = {
      id: offerId,
      isLoading: true,
      type: 'BUY' as const,
      amount: 0,
      baseRate: 0,
      adjustmentForLowerRep: 0,
      adjustmentForHigherRep: 0,
      status: 'ACTIVE' as OfferStatusFrontend, // Use string literal, cast to type
      createdAt: new Date().toISOString(),
      offerCreatorId: '',
      offerCreatorUsername: 'Loading...',
      offerCreatorReputationLevel: null,
      calculatedApplicableRate: 0,
      isNoLongerActive: false,
      fetchError: null,
      currentUserHasShownInterest: false
    };

    currentlyViewedOfferDetails.value = initialState;

    try {
      const offerDetails = await offerService.getBrowsableOfferById(offerId);
      currentlyViewedOfferDetails.value = {
        ...offerDetails,
        isLoading: false,
        isNoLongerActive: offerDetails.status !== 'ACTIVE', // Use string literal for comparison
        lastUpdated: new Date().toISOString(),
        fetchError: null
      };
    } catch (e: any) {
      const errorMessage = e?.message || 'Failed to load offer details';
      if (currentlyViewedOfferDetails.value) {
        currentlyViewedOfferDetails.value = {
          ...currentlyViewedOfferDetails.value,
          isLoading: false,
          isNoLongerActive: true,
          fetchError: errorMessage
        };
      }
      EventBus.emit(EventTypes.OFFER_ERROR, { message: errorMessage });
    }
  }

  function clearViewedOfferDetails() {
    currentlyViewedOfferDetails.value = null;
  }

  function handleSocketOfferStatusChanged(payload: OfferStatusChangedMinimalPayload) {
    console.log(`[offerStore] Received OFFER_STATUS_CHANGED: offerId=${payload.offerId}, newStatus=${payload.newStatus}`);
    const offerIndex = offers.value.findIndex(o => o.id === payload.offerId);

    if (offerIndex !== -1) {
      console.log(`[offerStore] Offer found in store: id=${payload.offerId}. Current status=${offers.value[offerIndex].status}. Updating to newStatus=${payload.newStatus}.`);
      const updatedOffer = {
        ...offers.value[offerIndex],
        status: payload.newStatus, // Assign directly from payload (already OfferStatusFrontend type)
      };
      const newOffers = [...offers.value];
      newOffers[offerIndex] = updatedOffer;
      offers.value = newOffers;
      console.log(`[offerStore] Offer id=${payload.offerId} status updated in offers.value. New offers.value length: ${offers.value.length}`);
    } else {
      console.warn(`[offerStore] Offer with ID ${payload.offerId} not found in local store. Cannot update status from socket event. New status is ${payload.newStatus}.`);
      if (payload.newStatus === 'ACTIVE') { // Use string literal for comparison
        console.log(`[offerStore] Unknown offer ${payload.offerId} became ACTIVE. Reloading all offers to include it.`);
        loadOffers();
      }
    }

    if (currentlyViewedOfferDetails.value?.id === payload.offerId) {
      currentlyViewedOfferDetails.value = {
        ...currentlyViewedOfferDetails.value,
        status: payload.newStatus, // Assign directly
        isNoLongerActive: payload.newStatus !== 'ACTIVE', // Use string literal for comparison
        lastUpdated: new Date().toISOString()
      };

      EventBus.emit(EventTypes.OFFER_STATUS_CHANGED, {
        status: payload.newStatus,
        offerId: payload.offerId
      });
    }
  }
  function handleSocketOfferUpdated({ offerId }: OfferIdPayload) {
    if (currentlyViewedOfferDetails.value?.id === offerId) {
      fetchAndDisplayOfferForModal(offerId);
      EventBus.emit(EventTypes.OFFER_UPDATED, { offerId });
    }
    loadOffers();
  }

  function handleSocketOfferCreated(_payload: OfferIdPayload) {
    EventBus.emit(EventTypes.OFFER_CREATED);
    loadOffers();
  }

  function handleExternalInterestStatusUpdate(payload: EventPayloads[typeof EventTypes.INTEREST_STATUS_CHANGED_BY_OTHERS]) {
    console.log('[offerStore] Received INTEREST_STATUS_CHANGED_BY_OTHERS via EventBus:', payload);
    const offerIndex = offers.value.findIndex(o => o.id === payload.offerId);

    if (offerIndex !== -1) {
      // Create a shallow copy of the offer to update
      const offerToUpdate = { ...offers.value[offerIndex] };
      offerToUpdate.currentUserInterestStatus = payload.newStatus;
      offerToUpdate.currentUserHasShownInterest = true; // Ensure this is true if status is updated
      if (payload.newStatus === 'ACCEPTED' && payload.chatSessionId) {
        offerToUpdate.chatSessionId = payload.chatSessionId;
      }
      // Create a new array for reactivity
      const newOffers = [...offers.value];
      newOffers[offerIndex] = offerToUpdate;
      offers.value = newOffers;

      console.log(`[offerStore] Updated offer ${payload.offerId} interest status to ${payload.newStatus} and chatSessionId ${payload.chatSessionId} from EventBus.`);
    }

    if (currentlyViewedOfferDetails.value && currentlyViewedOfferDetails.value.id === payload.offerId) {
      // Create a new object for reactivity if you're updating nested properties
      const newDetails = { ...currentlyViewedOfferDetails.value };
      newDetails.currentUserInterestStatus = payload.newStatus;
      newDetails.currentUserHasShownInterest = true;
      if (payload.newStatus === 'ACCEPTED' && payload.chatSessionId) {
        newDetails.chatSessionId = payload.chatSessionId;
      }
      currentlyViewedOfferDetails.value = newDetails;
      console.log(`[offerStore] Updated currentlyViewedOfferDetails for offer ${payload.offerId} interest status to ${payload.newStatus} and chatSessionId ${payload.chatSessionId}.`);
    }
  }  function initializeSocketListeners() {
    if (socketListenersAttached) return;

    console.log('[offerStore] Initializing socket listeners via centralizedSocketManager');

    // Set up socket event listeners using centralizedSocketManager
    unsubscribeOfferStatusChanged = centralizedSocketManager.on(OFFER_STATUS_CHANGED, handleSocketOfferStatusChanged);
    unsubscribeOfferUpdated = centralizedSocketManager.on(OFFER_UPDATED, handleSocketOfferUpdated);
    unsubscribeOfferCreated = centralizedSocketManager.on(OFFER_CREATED, handleSocketOfferCreated);
    
    // Listen for transaction status updates
    unsubscribeTransactionStatusUpdated = centralizedSocketManager.on(TRANSACTION_STATUS_UPDATED, (payload: any) => {
      console.log('[OfferStore] Received TRANSACTION_STATUS_UPDATED:', payload);
      
      // Find and update the offer with the new transaction status
      const offerIndex = offers.value.findIndex(o => o.id === payload.offerId);
      if (offerIndex !== -1) {
        offers.value[offerIndex] = {
          ...offers.value[offerIndex],
          transactionStatus: payload.status
        };
        console.log(`[OfferStore] Updated transaction status for offer ${payload.offerId} to ${payload.status}`);
      }
    });

    // Listen for negotiation status updates
    unsubscribeNegotiationStateUpdated = centralizedSocketManager.on(NEGOTIATION_STATE_UPDATED, (payload: any) => {
      console.log('[OfferStore] Received NEGOTIATION_STATE_UPDATED:', payload);
      
      // Find and update the offer with the new negotiation status
      const offerIndex = offers.value.findIndex(o => o.chatSessionId === payload.chatSessionId);
      if (offerIndex !== -1) {
        offers.value[offerIndex] = {
          ...offers.value[offerIndex],
          negotiationStatus: payload.negotiationStatus
        };
        console.log(`[OfferStore] Updated negotiation status for offer with chat session ${payload.chatSessionId} to ${payload.negotiationStatus}`);
      }
    });
    
    // Store the unsubscribe function returned by EventBus.on
    if (!unsubscribeInterestStatusChanged) {
      unsubscribeInterestStatusChanged = EventBus.on(EventTypes.INTEREST_STATUS_CHANGED_BY_OTHERS, handleExternalInterestStatusUpdate);
    }

    socketListenersAttached = true;
    console.log('[offerStore] Socket and EventBus listeners initialized.');
  }  function cleanup() {
    // Clean up socket event listeners using unsubscribe functions
    if (unsubscribeOfferStatusChanged) {
      unsubscribeOfferStatusChanged();
      unsubscribeOfferStatusChanged = null;
    }
    if (unsubscribeOfferUpdated) {
      unsubscribeOfferUpdated();
      unsubscribeOfferUpdated = null;
    }
    if (unsubscribeOfferCreated) {
      unsubscribeOfferCreated();
      unsubscribeOfferCreated = null;
    }
    if (unsubscribeTransactionStatusUpdated) {
      unsubscribeTransactionStatusUpdated();
      unsubscribeTransactionStatusUpdated = null;
    }
    if (unsubscribeNegotiationStateUpdated) {
      unsubscribeNegotiationStateUpdated();
      unsubscribeNegotiationStateUpdated = null;
    }
    
    // Call the stored unsubscribe function for EventBus
    if (unsubscribeInterestStatusChanged) {
      unsubscribeInterestStatusChanged();
      unsubscribeInterestStatusChanged = null;
      console.log('[offerStore] EventBus listener for INTEREST_STATUS_CHANGED_BY_OTHERS cleaned up.');
    }
    
    if (socketListenersAttached) {
      socketListenersAttached = false;
      console.log('[offerStore] Socket listeners cleaned up.');
    }
  }

  const activeBrowsableOffers = computed(() => {
    const currentOffers = offers.value;
    const filteredOffers = currentOffers.filter((o: BrowseOffer) => {
      return o.status === 'ACTIVE'; // Use string literal for comparison
    });
    console.log(
      `[offerStore] Computing activeBrowsableOffers. Total offers: ${currentOffers.length}. Filtered active offers: ${filteredOffers.length}. Active IDs: ${filteredOffers.map(o => o.id).join(', ') || 'None'}`
    );
    return filteredOffers;
  });
  function markInterestShown(offerId: string) {
    const offer = offers.value.find(o => o.id === offerId);
    if (offer) {
      offer.currentUserHasShownInterest = true;
      offer.currentUserInterestStatus = 'PENDING'; // Explicitly set status to PENDING
      console.log(`[offerStore] Marked interest shown and status PENDING for offer ${offerId}`);
    }
    // Also update currentlyViewedOfferDetails if it's the same offer
    if (currentlyViewedOfferDetails.value?.id === offerId) {
      currentlyViewedOfferDetails.value.currentUserHasShownInterest = true;
      currentlyViewedOfferDetails.value.currentUserInterestStatus = 'PENDING';
    }
  }

  function updateInterestStatus(offerId: string, status: 'ACCEPTED' | 'DECLINED', chatSessionId?: string) {
    const offer = offers.value.find(o => o.id === offerId);
    if (offer) {
      offer.currentUserInterestStatus = status;
      if (chatSessionId) {
        offer.chatSessionId = chatSessionId;
      }
      console.log(`[offerStore] Updated interest status to ${status} for offer ${offerId}${chatSessionId ? ` with chatSessionId ${chatSessionId}` : ''}`);
    }
    // Also update currentlyViewedOfferDetails if it's the same offer
    if (currentlyViewedOfferDetails.value?.id === offerId) {
      currentlyViewedOfferDetails.value.currentUserInterestStatus = status;
      if (chatSessionId) {
        currentlyViewedOfferDetails.value.chatSessionId = chatSessionId;
      }
    }
  }  function removeOffer(offerId: string) {
    console.log('[offerStore] Removing offer:', offerId);
    const index = offers.value.findIndex(offer => offer.id === offerId);
    if (index !== -1) {
      offers.value.splice(index, 1);
      console.log('[offerStore] Offer removed successfully');
    }
    
    // Clear viewed details if this was the currently viewed offer
    if (currentlyViewedOfferDetails.value?.id === offerId) {
      clearViewedOfferDetails();
    }
  }

  return {
    offers,    isLoading,
    error,
    currentlyViewedOfferDetails,
    loadOffers,
    fetchAndDisplayOfferForModal,
    clearViewedOfferDetails,
    removeOffer,
    initializeSocketListeners,
    cleanup,
    activeBrowsableOffers,
    markInterestShown,    updateInterestStatus
  };
});
