/**
 * Determines the number of decimal places to use for a given currency code.
 * @param currencyCode The currency code (e.g., 'CAD', 'BTC', 'ETH').
 * @returns The number of decimal places.
 */
export function getPrecision(currencyCode: string | null | undefined): number {
  if (!currencyCode) {
    return 2; // Default precision if currency code is not provided
  }
  const upperCaseCurrencyCode = currencyCode.toUpperCase();  switch (upperCaseCurrencyCode) {
    case 'CAD':
      return 2;
    case 'USD':
      return 2;
    case 'EUR':
      return 2;
    case 'IRR':
      return 0; // Iranian Rial doesn't use decimal places
    case 'BTC':
      return 8;
    case 'ETH':
      return 8; // Common precision for ETH, though can vary
    case 'USDT':
      return 6; // Common precision for USDT
    // Add other common cryptocurrencies or fiat currencies as needed
    default:
      // For unknown currencies, assume a higher precision suitable for many assets
      // or a common default like 2 if it's more likely to be fiat-like.
      // Let's default to 2 for now if not a known crypto, can be adjusted.
      return 2; 
  }
}

/**
 * Formats an amount according to the currency's precision rules.
 * @param amount The numerical amount to format.
 * @param currencyCode The currency code (e.g., 'CAD', 'IRR', 'BTC').
 * @returns The formatted amount as a string.
 */
export function formatAmount(amount: number, currencyCode?: string): string {
  const precision = getPrecision(currencyCode);
  return amount.toLocaleString(undefined, { 
    minimumFractionDigits: precision,
    maximumFractionDigits: precision 
  });
}

/**
 * Formats an amount for exchange rate display (e.g., in transaction details).
 * Uses up to 8 decimal places for rates but respects currency precision for final amounts.
 * @param amount The numerical amount to format.
 * @param currencyCode The currency code.
 * @param isRate Whether this is an exchange rate (allows more decimals).
 * @returns The formatted amount as a string.
 */
export function formatAmountForDisplay(amount: number, currencyCode?: string, isRate: boolean = false): string {
  const precision = getPrecision(currencyCode);
  
  if (isRate) {
    // For exchange rates, allow up to 8 decimal places, but still respect currency-specific limits
    // For currencies like IRR with 0 precision, even rates should use 0 decimals
    const maxDecimals = precision === 0 ? 0 : 8;
    
    return amount.toLocaleString(undefined, { 
      minimumFractionDigits: precision,
      maximumFractionDigits: maxDecimals 
    });
  }
  return formatAmount(amount, currencyCode);
}
