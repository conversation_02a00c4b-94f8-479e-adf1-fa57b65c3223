/**
 * Payment Persistence End-to-End Test Script
 * 
 * This script tests the complete payment information persistence flow
 * by making actual API calls to verify the fix works correctly.
 * 
 * Run with: npm run test:payment-persistence
 */

import axios from 'axios'
import { config } from 'dotenv'

// Load environment variables
config({ path: '../../.env' })

const API_BASE_URL = process.env.VITE_BACKEND_URL || 'http://localhost:3000/api'

interface PaymentInfo {
  bankName: string
  accountNumber: string
  accountHolderName: string
}

interface UserInfo {
  id: string
  email: string
  username: string
  isPhoneVerified: boolean
  reputation: number
  defaultPaymentReceivingInfo: PaymentInfo | null
}

class PaymentPersistenceTest {
  private token: string = ''
  private userId: string = ''
  private negotiationId: string = ''
  private testEmail: string = `test-payment-${Date.now()}@example.com`
  private testUsername: string = `testpayment${Date.now()}`

  constructor() {
    console.log('🧪 Payment Persistence E2E Test Starting...')
    console.log(`📡 API Base URL: ${API_BASE_URL}`)
  }

  async run(): Promise<void> {
    try {
      await this.registerTestUser()
      await this.verifyInitialNoPaymentInfo()
      await this.createTestNegotiation()
      await this.testSavePaymentInfoToProfile()
      await this.verifyPaymentInfoPersisted()
      await this.testUpdatePaymentInfo()
      await this.verifyUpdatedPaymentInfo()
      await this.testSaveToProfileFalse()
      await this.cleanup()
      
      console.log('✅ All payment persistence tests passed!')
    } catch (error) {
      console.error('❌ Test failed:', error)
      throw error
    }
  }

  private async registerTestUser(): Promise<void> {
    console.log('\n📝 Registering test user...')
    
    const response = await axios.post(`${API_BASE_URL}/auth/register`, {
      email: this.testEmail,
      username: this.testUsername,
      password: 'TestPassword123!'
    })

    if (!response.data.success) {
      throw new Error('Failed to register test user')
    }

    this.token = response.data.token
    this.userId = response.data.user.id

    console.log(`✅ User registered with ID: ${this.userId}`)
  }

  private async verifyInitialNoPaymentInfo(): Promise<void> {
    console.log('\n🔍 Verifying user has no initial payment info...')
    
    const response = await axios.get(`${API_BASE_URL}/auth/me`, {
      headers: { Authorization: `Bearer ${this.token}` }
    })

    const user: UserInfo = response.data.user

    if (user.defaultPaymentReceivingInfo !== null) {
      throw new Error('User should not have payment info initially')
    }

    console.log('✅ Confirmed: No initial payment info')
  }

  private async createTestNegotiation(): Promise<void> {
    console.log('\n🤝 Creating test negotiation...')
    
    // First create a test offer
    const offerResponse = await axios.post(`${API_BASE_URL}/offers`, {
      title: 'Test Offer for Payment',
      description: 'Test offer for payment persistence testing',
      amount: 100,
      currency: 'USD',
      category: 'TEST',
      isPayerOffer: false
    }, {
      headers: { Authorization: `Bearer ${this.token}` }
    })

    const offerId = offerResponse.data.offer.id

    // Create a negotiation (simplified - in real app this would be more complex)
    // For testing purposes, we'll use a mock negotiation ID
    this.negotiationId = `test-negotiation-${Date.now()}`
    
    console.log(`✅ Test negotiation created: ${this.negotiationId}`)
  }

  private async testSavePaymentInfoToProfile(): Promise<void> {
    console.log('\n💳 Testing save payment info to profile (saveToProfile: true)...')
    
    const paymentData = {
      bankName: 'Test Bank Initial',
      accountNumber: '*********',
      accountHolderName: 'John Doe',
      saveToProfile: true
    }

    const response = await axios.post(
      `${API_BASE_URL}/payer-negotiations/${this.negotiationId}/payment-info`,
      paymentData,
      { headers: { Authorization: `Bearer ${this.token}` } }
    )

    if (!response.data.success) {
      throw new Error('Failed to save payment info')
    }

    console.log('✅ Payment info saved with saveToProfile: true')
  }

  private async verifyPaymentInfoPersisted(): Promise<void> {
    console.log('\n🔍 Verifying payment info was saved to user profile...')
    
    const response = await axios.get(`${API_BASE_URL}/auth/me`, {
      headers: { Authorization: `Bearer ${this.token}` }
    })

    const user: UserInfo = response.data.user
    const paymentInfo = user.defaultPaymentReceivingInfo

    if (!paymentInfo) {
      throw new Error('Payment info should be saved to user profile')
    }

    if (paymentInfo.bankName !== 'Test Bank Initial') {
      throw new Error(`Expected bankName 'Test Bank Initial', got '${paymentInfo.bankName}'`)
    }

    if (paymentInfo.accountNumber !== '*********') {
      throw new Error(`Expected accountNumber '*********', got '${paymentInfo.accountNumber}'`)
    }

    if (paymentInfo.accountHolderName !== 'John Doe') {
      throw new Error(`Expected accountHolderName 'John Doe', got '${paymentInfo.accountHolderName}'`)
    }

    console.log('✅ Payment info correctly persisted to user profile')
    console.log(`   Bank: ${paymentInfo.bankName}`)
    console.log(`   Account: ${paymentInfo.accountNumber}`)
    console.log(`   Holder: ${paymentInfo.accountHolderName}`)
  }

  private async testUpdatePaymentInfo(): Promise<void> {
    console.log('\n📝 Testing update payment info (saveToProfile: true)...')
    
    const updatedPaymentData = {
      bankName: 'Updated Bank',
      accountNumber: '*********',
      accountHolderName: 'Jane Smith',
      saveToProfile: true
    }

    const response = await axios.post(
      `${API_BASE_URL}/payer-negotiations/${this.negotiationId}/payment-info`,
      updatedPaymentData,
      { headers: { Authorization: `Bearer ${this.token}` } }
    )

    if (!response.data.success) {
      throw new Error('Failed to update payment info')
    }

    console.log('✅ Payment info updated with saveToProfile: true')
  }

  private async verifyUpdatedPaymentInfo(): Promise<void> {
    console.log('\n🔍 Verifying updated payment info was saved to user profile...')
    
    const response = await axios.get(`${API_BASE_URL}/auth/me`, {
      headers: { Authorization: `Bearer ${this.token}` }
    })

    const user: UserInfo = response.data.user
    const paymentInfo = user.defaultPaymentReceivingInfo

    if (!paymentInfo) {
      throw new Error('Payment info should still be in user profile')
    }

    if (paymentInfo.bankName !== 'Updated Bank') {
      throw new Error(`Expected updated bankName 'Updated Bank', got '${paymentInfo.bankName}'`)
    }

    if (paymentInfo.accountNumber !== '*********') {
      throw new Error(`Expected updated accountNumber '*********', got '${paymentInfo.accountNumber}'`)
    }

    if (paymentInfo.accountHolderName !== 'Jane Smith') {
      throw new Error(`Expected updated accountHolderName 'Jane Smith', got '${paymentInfo.accountHolderName}'`)
    }

    console.log('✅ Updated payment info correctly persisted to user profile')
    console.log(`   Bank: ${paymentInfo.bankName}`)
    console.log(`   Account: ${paymentInfo.accountNumber}`)
    console.log(`   Holder: ${paymentInfo.accountHolderName}`)
  }

  private async testSaveToProfileFalse(): Promise<void> {
    console.log('\n🚫 Testing saveToProfile: false (should not update profile)...')
    
    const tempPaymentData = {
      bankName: 'Temporary Bank',
      accountNumber: '*********',
      accountHolderName: 'Temp User',
      saveToProfile: false
    }

    const response = await axios.post(
      `${API_BASE_URL}/payer-negotiations/${this.negotiationId}/payment-info`,
      tempPaymentData,
      { headers: { Authorization: `Bearer ${this.token}` } }
    )

    if (!response.data.success) {
      throw new Error('Failed to save temporary payment info')
    }

    console.log('✅ Temporary payment info saved with saveToProfile: false')

    // Verify profile wasn't updated
    const profileResponse = await axios.get(`${API_BASE_URL}/auth/me`, {
      headers: { Authorization: `Bearer ${this.token}` }
    })

    const user: UserInfo = profileResponse.data.user
    const paymentInfo = user.defaultPaymentReceivingInfo

    if (!paymentInfo) {
      throw new Error('Payment info should still exist in profile')
    }

    // Should still have the previous values, not the temporary ones
    if (paymentInfo.bankName === 'Temporary Bank') {
      throw new Error('Profile should not be updated when saveToProfile is false')
    }

    if (paymentInfo.bankName !== 'Updated Bank') {
      throw new Error('Profile should retain previous payment info')
    }

    console.log('✅ Profile correctly unchanged when saveToProfile: false')
    console.log(`   Profile still has: ${paymentInfo.bankName}`)
  }

  private async cleanup(): Promise<void> {
    console.log('\n🧹 Cleaning up test data...')
    
    // In a real implementation, you might want to delete the test user
    // For now, we'll just log that cleanup is complete
    console.log('✅ Cleanup complete')
  }
}

// Export for programmatic use
export { PaymentPersistenceTest }

// Run if called directly
if (require.main === module) {
  const test = new PaymentPersistenceTest()
  test.run().catch((error) => {
    console.error('Test failed:', error)
    process.exit(1)
  })
}
