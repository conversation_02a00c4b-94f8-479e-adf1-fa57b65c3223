import { describe, it, expect, vi, beforeEach } from 'vitest';
import axios from 'axios';
import type { AxiosInstance, InternalAxiosRequestConfig } from 'axios';
import { useAuthStore } from '@/stores/auth';

// --- Mock Setup ---
const mockAxiosInstance = {
  defaults: { headers: { common: {} } },
  interceptors: {
    request: { use: vi.fn(), eject: vi.fn() },
    response: { use: vi.fn(), eject: vi.fn() },
  },
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
} as unknown as AxiosInstance;

vi.mock('axios', async (importOriginal) => {
  const actualAxios = await importOriginal<typeof axios>();
  return {
    __esModule: true,
    default: {
      ...actualAxios,
      create: vi.fn(() => mockAxiosInstance), // Ensure create returns the mock instance
    },
  };
});

vi.mock('@/stores/auth', () => ({
  useAuthStore: vi.fn(),
}));

// --- Helper References ---
// It's often cleaner to get the mock functions directly after mocking
const mockedAxiosCreate = vi.mocked(axios.create);
const mockedUseAuthStore = vi.mocked(useAuthStore);
// Reference the mock interceptor function from the predefined instance
const mockedRequestInterceptorUse = vi.mocked(mockAxiosInstance.interceptors.request.use);

describe('apiClient', () => {
  let requestInterceptorSuccessCallback: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>;
  let requestInterceptorErrorCallback: (error: any) => any;

  beforeEach(async () => {
    // Reset modules FIRST to ensure apiClient.ts runs fresh with mocks applied
    vi.resetModules();
    // Reset mocks state AFTER resetting modules
    vi.clearAllMocks();

    // Set up default mock return value for the store *before* import
    mockedUseAuthStore.mockReturnValue({ token: null } as any);
    // Re-ensure the mocked create function is set to return our instance
    // (This might be redundant due to vi.mock factory, but ensures clarity)
    mockedAxiosCreate.mockReturnValue(mockAxiosInstance);

    // Dynamically import the apiClient module.
    // This executes apiClient.ts, calling the mocked axios.create()
    await import('../apiClient');

    // Capture the arguments passed to the interceptor's use() method
    // We assume the import succeeded and called create/use.
    // If not, tests checking for these calls will fail.
    if (mockedRequestInterceptorUse.mock.calls.length === 0) {
      // This case indicates a setup problem if reached.
      throw new Error('Interceptor use() was not called during apiClient import.');
    }
    const interceptorArgs = mockedRequestInterceptorUse.mock.calls[0];
    requestInterceptorSuccessCallback = interceptorArgs[0] as (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>;
    requestInterceptorErrorCallback = interceptorArgs[1] as (error: any) => any;
  });

  // No afterEach needed with this setup unless adding side effects

  it('should create an Axios instance with the correct baseURL', () => {
    // Assert that the mocked create function was called during the beforeEach import
    expect(mockedAxiosCreate).toHaveBeenCalledTimes(1);
    expect(mockedAxiosCreate).toHaveBeenCalledWith({
      baseURL: '/api',
    });
  });

  it('should add a request interceptor with success and error handlers', () => {
    // Assert that the interceptor's use method was called on our mock instance
    expect(mockedRequestInterceptorUse).toHaveBeenCalledTimes(1);
    // Check that the captured callbacks are functions
    expect(requestInterceptorSuccessCallback).toBeInstanceOf(Function);
    expect(requestInterceptorErrorCallback).toBeInstanceOf(Function);
  });

  describe('Request Interceptor Success Callback', () => {
    it('should call useAuthStore', () => {
        const config = { headers: {} } as InternalAxiosRequestConfig;
        // Clear any calls from beforeEach if necessary, although clearAllMocks should handle it.
        // mockedUseAuthStore.mockClear(); // Usually not needed if vi.clearAllMocks() is used correctly

        // Act: Call the interceptor callback once
        requestInterceptorSuccessCallback(config);

        // Assert: Check if useAuthStore was called exactly once during this action
        expect(mockedUseAuthStore).toHaveBeenCalledTimes(1);
    });

    it('should not add Authorization header if token is null', () => {
      // Arrange: Auth store is mocked to return null token in beforeEach
      const config = { headers: {} } as InternalAxiosRequestConfig;
      // mockedUseAuthStore.mockClear(); // Clear calls from beforeEach if needed

      // Act
      const updatedConfig = requestInterceptorSuccessCallback(config);

      // Assert
      expect(mockedUseAuthStore).toHaveBeenCalledTimes(1); // Called once during this test
      expect((updatedConfig as InternalAxiosRequestConfig).headers).not.toHaveProperty('Authorization');
      expect(updatedConfig).toBe(config);
    });

    it('should add Authorization header if token exists', () => {
      // Arrange
      const mockToken = 'test-jwt-token';
      mockedUseAuthStore.mockReturnValue({ token: mockToken } as any); // Override return value
      // mockedUseAuthStore.mockClear(); // Clear calls from beforeEach if needed
      const config = { headers: {} } as InternalAxiosRequestConfig;

      // Act
      const updatedConfig = requestInterceptorSuccessCallback(config);

      // Assert
      expect(mockedUseAuthStore).toHaveBeenCalledTimes(1); // Called once during this test
      expect((updatedConfig as InternalAxiosRequestConfig).headers).toHaveProperty('Authorization', `Bearer ${mockToken}`);
      expect(updatedConfig).toBe(config);
    });

     it('should add Authorization header correctly even with existing headers', () => {
      // Arrange
      const mockToken = 'test-jwt-token-other';
      mockedUseAuthStore.mockReturnValue({ token: mockToken } as any);
      // mockedUseAuthStore.mockClear(); // Clear calls from beforeEach if needed
      const config = { headers: { 'X-Custom-Header': 'value' } as any } as InternalAxiosRequestConfig;

      // Act
      const updatedConfig = requestInterceptorSuccessCallback(config);

      // Assert
      expect(mockedUseAuthStore).toHaveBeenCalledTimes(1); // Called once during this test
      expect((updatedConfig as InternalAxiosRequestConfig).headers).toHaveProperty('Authorization', `Bearer ${mockToken}`);
      expect((updatedConfig as InternalAxiosRequestConfig).headers).toHaveProperty('X-Custom-Header', 'value');
      expect(updatedConfig).toBe(config);
    });
  });

  describe('Request Interceptor Error Callback', () => {
      it('should reject with the same error object', async () => {
        // Arrange
        const mockError = new Error('Test interceptor error');
        mockedUseAuthStore.mockClear(); // Clear calls from beforeEach

        // Act & Assert
        await expect(requestInterceptorErrorCallback(mockError)).rejects.toBe(mockError);
        // Ensure auth store is not called on error path
        expect(mockedUseAuthStore).not.toHaveBeenCalled();
      });
  });

  it('should export the created axios instance', async () => {
    // Re-import dynamically AFTER vi.resetModules() in beforeEach
    // to get the export associated with *this test's* module execution.
    const module = await import('../apiClient');
    expect(module.default).toBe(mockAxiosInstance);
  });
});