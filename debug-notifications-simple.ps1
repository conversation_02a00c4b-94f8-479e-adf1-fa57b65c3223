# Notification System Debug Script - PowerShell
# Test backend and frontend services for notification functionality

Write-Host "Starting notification system debug..." -ForegroundColor Cyan

# Test 1: Check if backend is running
Write-Host "`nTesting backend service..." -ForegroundColor Yellow
try {
    $backendResponse = Invoke-RestMethod -Uri "http://localhost:3000/health" -Method Get -TimeoutSec 5
    Write-Host "SUCCESS: Backend is running" -ForegroundColor Green
    Write-Host "Response: $backendResponse" -ForegroundColor Gray
} catch {
    Write-Host "ERROR: Backend is not responding" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure to run: npm run dev in the backend directory" -ForegroundColor Yellow
}

# Test 2: Check if frontend is running
Write-Host "`nTesting frontend service..." -ForegroundColor Yellow
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:5173" -Method Get -TimeoutSec 5
    Write-Host "SUCCESS: Frontend is running" -ForegroundColor Green
    Write-Host "Status Code: $($frontendResponse.StatusCode)" -ForegroundColor Gray
} catch {
    Write-Host "ERROR: Frontend is not responding" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure to run: npm run dev in the frontend directory" -ForegroundColor Yellow
}

# Test 3: Check database connection (if using PostgreSQL)
Write-Host "`nTesting database connection..." -ForegroundColor Yellow
try {
    # Try to ping PostgreSQL container
    $containerStatus = docker ps --filter "name=munygo-postgres" --format "{{.Status}}"
    if ($containerStatus) {
        Write-Host "SUCCESS: PostgreSQL container is running: $containerStatus" -ForegroundColor Green
    } else {
        Write-Host "ERROR: PostgreSQL container is not running" -ForegroundColor Red
        Write-Host "Run: docker-compose up -d postgres" -ForegroundColor Yellow
    }
} catch {
    Write-Host "ERROR: Docker is not available or container check failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Show backend logs for debugging
Write-Host "`nBackend Service Status..." -ForegroundColor Yellow
$backendDir = "C:\Code\MUNygo\backend"
if (Test-Path $backendDir) {
    Write-Host "Backend directory exists: $backendDir" -ForegroundColor Green
    
    # Check if package.json exists
    if (Test-Path "$backendDir\package.json") {
        Write-Host "SUCCESS: package.json found" -ForegroundColor Green
    } else {
        Write-Host "ERROR: package.json not found" -ForegroundColor Red
    }
    
    # Check if node_modules exists
    if (Test-Path "$backendDir\node_modules") {
        Write-Host "SUCCESS: node_modules found" -ForegroundColor Green
    } else {
        Write-Host "ERROR: node_modules not found - run: npm install" -ForegroundColor Red
    }
} else {
    Write-Host "ERROR: Backend directory not found: $backendDir" -ForegroundColor Red
}

# Test 5: Show frontend status
Write-Host "`nFrontend Service Status..." -ForegroundColor Yellow
$frontendDir = "C:\Code\MUNygo\frontend"
if (Test-Path $frontendDir) {
    Write-Host "Frontend directory exists: $frontendDir" -ForegroundColor Green
    
    # Check if package.json exists
    if (Test-Path "$frontendDir\package.json") {
        Write-Host "SUCCESS: package.json found" -ForegroundColor Green
    } else {
        Write-Host "ERROR: package.json not found" -ForegroundColor Red
    }
    
    # Check if node_modules exists
    if (Test-Path "$frontendDir\node_modules") {
        Write-Host "SUCCESS: node_modules found" -ForegroundColor Green
    } else {
        Write-Host "ERROR: node_modules not found - run: npm install" -ForegroundColor Red
    }
} else {
    Write-Host "ERROR: Frontend directory not found: $frontendDir" -ForegroundColor Red
}

Write-Host "NEXT STEPS FOR TESTING NOTIFICATIONS:" -ForegroundColor Cyan
Write-Host "========================================"
Write-Host "1. Ensure both backend and frontend are running"
Write-Host "2. Open http://localhost:5173 in your browser"
Write-Host "3. Open browser DevTools > Console"
Write-Host "4. Copy and paste the contents of 'debug-notifications-console.js' into the console"
Write-Host "5. Follow the manual test procedure shown in the console"
Write-Host ""
Write-Host "Browser debug script location: C:\Code\MUNygo\debug-notifications-console.js" -ForegroundColor Yellow
Write-Host ""
Write-Host "Debug script complete!" -ForegroundColor Green
