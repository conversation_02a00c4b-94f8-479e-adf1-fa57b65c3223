import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import ProfileView from '../ProfileView.vue';
import { useAuthStore } from '@/stores/auth';
import { nextTick, defineComponent, h } from 'vue';

// Import from existing mocks file if applicable
import { mockUser } from './mocks';

// Mock external dependencies
const apiClientPostMock = vi.fn();
const apiClientGetMock = vi.fn();
vi.mock('@/services/apiClient', () => ({
  default: {
    get post() { return apiClientPostMock; },
    get get() { return apiClientGetMock; }
  },
}));

// Simplified mock for Naive UI components we need to test
vi.mock('naive-ui', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useMessage: () => ({
      success: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
      warning: vi.fn(),
    })
  };
});

describe('ProfileView - User Profile Display (MUN-005)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  it('displays all required profile information fields', async () => {
    // Set up initial user data with all required profile fields
    const userWithProfile = {
      ...mockUser,
      username: 'testuser',
      createdAt: '2025-01-01T00:00:00Z',
      emailVerified: true,
      phoneVerified: true,
      phoneNumber: '+15551234567',
      reputationScore: 35, // Level 3
      reputationLevel: 3
    };
      // Create a pinia store with stubbed actions
    const wrapper = mount(ProfileView, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: { user: userWithProfile }
            },
            createSpy: vi.fn,
            stubActions: true // Stub all actions
          })
        ]
      }
    });
    
    // Manually mock the fetchUserProfile function
    const authStore = useAuthStore();
    vi.spyOn(authStore, 'fetchUserProfile').mockResolvedValue(userWithProfile);
    
    // Wait for component to stabilize
    await flushPromises();
    
    // Check that all required profile fields are displayed
    const html = wrapper.html();
    
    // 1. Username
    expect(html).toContain('testuser');
    
    // 2. Join Date
    expect(wrapper.text()).toMatch(/January 1, 2025|2025-01-01/); // Date format might vary
    
    // 3. Email Verification Status
    expect(html).toContain('Verified');
    
    // 4. Phone Verification Status
    expect(html).toContain('+15551234567');
    
    // 5. Reputation Level
    expect(wrapper.text()).toContain('Level 3');
    expect(wrapper.text()).toContain('Reliable');
  });
    it('handles phone number display correctly when unverified', async () => {
    // User with phone number but it's unverified
    const userWithUnverifiedPhone = {
      ...mockUser,
      username: 'phoneuser',
      phoneNumber: '+15551234567',
      phoneVerified: false,
      emailVerified: true
    };
    
    const wrapper = mount(ProfileView, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: { user: userWithUnverifiedPhone }
            },
            createSpy: vi.fn,
            stubActions: true
          })
        ]
      }
    });
    
    // Mock the store
    const authStore = useAuthStore();
    vi.spyOn(authStore, 'fetchUserProfile').mockResolvedValue(userWithUnverifiedPhone);
    
    await flushPromises();
    
    // Phone number shouldn't be displayed with the status if unverified
    const phoneStatusElement = wrapper.find('[data-testid="phone-status-unverified"]');
    expect(phoneStatusElement.exists()).toBe(true);
    
    // Phone number is present in the user object but shouldn't be displayed in the status section
    // when unverified (should only be shown in the verification form)
    const statusSectionText = phoneStatusElement.element.parentElement?.textContent || '';
    expect(statusSectionText).not.toContain('+15551234567');
  });
  it('displays correct reputation level based on user data', async () => {
    // Create a store with various reputation levels to test
    const testCases = [
      { level: 1, name: 'Newcomer' },
      { level: 2, name: 'Verified' },
      { level: 3, name: 'Reliable' },
      { level: 4, name: 'Trusted' },
      { level: 5, name: 'Elite' },
    ];
    
    for (const testCase of testCases) {
      const userWithReputation = {
        ...mockUser,
        reputationLevel: testCase.level
      };
        const wrapper = mount(ProfileView, {
        global: {
          plugins: [
            createTestingPinia({
              initialState: {
                auth: { user: userWithReputation }
              },
              createSpy: vi.fn,
              stubActions: true
            })
          ]
        }
      });
      
      // Manually mock the fetchUserProfile function
      const authStore = useAuthStore();
      vi.spyOn(authStore, 'fetchUserProfile').mockResolvedValue(userWithReputation);
        await flushPromises();
      
      // Verify the appropriate reputation level and name is displayed
      expect(wrapper.text()).toContain(`Level ${testCase.level}`);
      expect(wrapper.text()).toContain(testCase.name);
      
      wrapper.unmount();
    }
  });
    it('applies correct tag color styling based on reputation level', async () => {
    // Map of reputation levels to expected tag types in reputationTagType computed
    const tagTypeMap = [
      { level: 1, type: 'default' },  // Level 1: Newcomer - default
      { level: 2, type: 'info' },     // Level 2: Verified - info
      { level: 3, type: 'success' },  // Level 3: Reliable - success
      { level: 4, type: 'warning' },  // Level 4: Trusted - warning
      { level: 5, type: 'error' },    // Level 5: Elite - error (for high visibility)
    ];
    
    for (const { level, type } of tagTypeMap) {
      const userWithLevel = {
        ...mockUser,
        reputationLevel: level
      };
      
      const wrapper = mount(ProfileView, {
        global: {
          plugins: [
            createTestingPinia({
              initialState: {
                auth: { user: userWithLevel }
              },
              createSpy: vi.fn,
              stubActions: true
            })
          ]
        }
      });
      
      // Manually mock the fetchUserProfile function
      const authStore = useAuthStore();
      vi.spyOn(authStore, 'fetchUserProfile').mockResolvedValue(userWithLevel);
      await flushPromises();
      
      // Verify the reputationTagType computed property through a test component
      const vm = wrapper.vm as any; // Access the component instance
      expect(vm.reputationTagType).toBe(type);
      
      // Also check the rendered HTML contains the n-tag with the right type class
      // This is more of a visual check that indirectly confirms the tag styling
      const levelText = level === 1 ? 'Newcomer' : 
                      level === 2 ? 'Verified' : 
                      level === 3 ? 'Reliable' : 
                      level === 4 ? 'Trusted' : 'Elite';
      expect(wrapper.text()).toContain(`Level ${level}: ${levelText}`);
      
      wrapper.unmount();
    }
  });
  it('displays appropriate fallbacks for missing profile data', async () => {
    // Test with minimal user data
    const minimalUser = {
      id: 'user-123',
      email: '<EMAIL>',
      // No username, createdAt, or reputation fields
    };
      const wrapper = mount(ProfileView, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: { user: minimalUser }
            },
            createSpy: vi.fn,
            stubActions: true
          })
        ]
      }
    });
    
    // Manually mock the fetchUserProfile function
    const authStore = useAuthStore();
    vi.spyOn(authStore, 'fetchUserProfile').mockResolvedValue(minimalUser);
    
    await flushPromises();
      // Instead of checking computed properties directly, check rendered text
    const html = wrapper.html();
    expect(html).toContain('user');  // Email prefix as username
    expect(wrapper.text()).toContain('May 2025'); // Should display May 2025 as fallback for missing date
    expect(wrapper.text()).toContain('Level 1: Newcomer'); // Should default to Level 1
  });
    it('calculates reputation level based on verification status when not stored', async () => {
    // Define test cases for dynamic reputation calculation
    const testCases = [
      { 
        userData: { 
          ...mockUser, 
          emailVerified: false, 
          phoneVerified: false 
        },
        expectedLevel: 1,
        expectedName: 'Newcomer',
        expectedTagType: 'default'
      },
      { 
        userData: { 
          ...mockUser, 
          emailVerified: true, 
          phoneVerified: false 
        },
        expectedLevel: 2,
        expectedName: 'Verified',
        expectedTagType: 'info'
      },
      { 
        userData: { 
          ...mockUser, 
          emailVerified: false, 
          phoneVerified: true 
        },
        expectedLevel: 2,
        expectedName: 'Verified',
        expectedTagType: 'info'
      },
      { 
        userData: { 
          ...mockUser, 
          emailVerified: true, 
          phoneVerified: true 
        },
        expectedLevel: 3,
        expectedName: 'Reliable',
        expectedTagType: 'success'
      }
    ];
    
    for (const testCase of testCases) {
      const wrapper = mount(ProfileView, {
        global: {
          plugins: [
            createTestingPinia({
              initialState: {
                auth: { user: testCase.userData }
              },
              createSpy: vi.fn,
              stubActions: true
            })
          ]
        }
      });
      
      // Manually mock the fetchUserProfile function
      const authStore = useAuthStore();
      vi.spyOn(authStore, 'fetchUserProfile').mockResolvedValue(testCase.userData);
      
      await flushPromises();
      
      // Check that the reputation text shows the correct level
      expect(wrapper.text()).toContain(`Level ${testCase.expectedLevel}`);
      expect(wrapper.text()).toContain(testCase.expectedName);
      
      // Access the computed property directly to verify tag type
      const vm = wrapper.vm as any;
      expect(vm.reputationTagType).toBe(testCase.expectedTagType);
      
      wrapper.unmount();
    }
  });
});
