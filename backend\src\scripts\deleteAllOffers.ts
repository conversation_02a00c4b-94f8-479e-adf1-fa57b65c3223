import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    // Delete notifications related to offers or interests
    // These should be deleted before the entities they might refer to.
    const deletedNotifications = await prisma.notification.deleteMany({
      where: {
        OR: [
          { relatedEntityType: 'OFFER' },
          { relatedEntityType: 'INTEREST' },
          // Add other relatedEntityTypes if offers/interests can be linked via other string keys
        ],
      },
    });
    console.log(`Deleted ${deletedNotifications.count} offer/interest related notifications.`);

    // Delete all interests first (to avoid foreign key constraint errors if not using onDelete: Cascade on Offer for Interests)
    // Interest model has onDelete: Cascade for offer, so deleting Offer would delete its Interests.
    // Explicit deletion here is for clarity and specific logging.
    const deletedInterests = await prisma.interest.deleteMany({});
    console.log(`Deleted ${deletedInterests.count} interests.`);

    // Delete all chat sessions
    // ChatSession model has onDelete: Cascade for offer.
    // It also has an optional link to Interest (interestId) with onDelete: SetNull.
    // Explicitly deleting all ChatSessions ensures cleanup.
    const deletedChatSessions = await prisma.chatSession.deleteMany({});
    console.log(`Deleted ${deletedChatSessions.count} chat sessions.`);

    // Delete all offers
    // This will also cascade delete related Interests and ChatSessions if they weren't deleted explicitly above.
    const deletedOffers = await prisma.offer.deleteMany({});
    console.log(`Deleted ${deletedOffers.count} offers.`);
  } catch (error) {
    console.error('Error deleting offer-related data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('All offers and related data (interests, chat sessions, notifications) deleted.'))
  .catch(e => {
    console.error('Script failed:', e);
    process.exit(1);
  });
