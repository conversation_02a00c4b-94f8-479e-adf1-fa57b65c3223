import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AiService } from '../services/aiService';

// Create a global mock function that we can control
const mockGenerateContent = vi.fn();

// Mock the Google Gen AI SDK
vi.mock('@google/genai', () => ({
  GoogleGenAI: vi.fn().mockImplementation(() => ({
    models: {
      generateContent: mockGenerateContent
    }
  })),
  Type: {}
}));

// Mock console methods to test logging
const mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {});
const mockConsoleLog = vi.spyOn(console, 'log').mockImplementation(() => {});

describe('AiService', () => {
  let aiService: AiService;

  beforeEach(() => {
    // Reset environment variables
    process.env.GEMINI_API_KEY = 'test-api-key';
    
    // Clear console mocks
    mockConsoleWarn.mockClear();
    mockConsoleLog.mockClear();
    
    // Reset mock function
    mockGenerateContent.mockReset();
    
    // Create new instance
    aiService = new AiService();
  });

  describe('isAvailable', () => {
    it('should return true when API key is provided', () => {
      expect(aiService.isAvailable()).toBe(true);
    });

    it('should return false when API key is not provided', () => {
      delete process.env.GEMINI_API_KEY;
      const serviceWithoutKey = new AiService();
      expect(serviceWithoutKey.isAvailable()).toBe(false);
    });

    it('should log warning when API key is not provided during construction', () => {
      delete process.env.GEMINI_API_KEY;
      new AiService();
      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '[AiService] Gemini API key not found. AI features will be disabled.'
      );
    });
  });

  describe('processAudioToReport', () => {
    // Create valid audio data that will pass base64 validation
    const mockAudioBuffer = Buffer.from('fake-audio-data-that-is-valid', 'utf8');
    const mockMimeType = 'audio/webm';
    const mockUserContext = {
      currentPage: 'https://example.com/login',
      userAgent: 'Mozilla/5.0...',
      viewport: { width: 1920, height: 1080 }
    };

    it('should return error when service is not available', async () => {
      delete process.env.GEMINI_API_KEY;
      const serviceWithoutKey = new AiService();
      
      const result = await serviceWithoutKey.processAudioToReport(
        mockAudioBuffer,
        mockMimeType,
        mockUserContext
      );
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('AI service is not available');
    });

    it('should successfully process audio and return structured report', async () => {
      const mockAiResponse = {
        text: JSON.stringify({
          transcription: 'User reports login button issue',
          analysis_status: 'SUCCESS',
          title: 'Login Button Not Working',
          description: 'User reports that the login button is unresponsive when clicked',
          stepsToReproduce: '1. Navigate to login page\n2. Click login button\n3. Nothing happens',
          expectedBehavior: 'Login form should submit and user should be authenticated',
          actualBehavior: 'Button click has no effect',
          suggestedSeverity: 'high',
          suggestedType: 'bug',
          suggestedTags: ['login', 'ui', 'button'],
          confidence: 0.85
        })
      };

      mockGenerateContent.mockResolvedValue(mockAiResponse);

      const result = await aiService.processAudioToReport(
        mockAudioBuffer,
        mockMimeType,
        mockUserContext
      );

      expect(result.success).toBe(true);
      expect(result.generatedReport).toBeDefined();
      expect(result.generatedReport?.title).toBe('Login Button Not Working');
      expect(result.generatedReport?.suggestedType).toBe('bug');
      expect(result.generatedReport?.suggestedSeverity).toBe('high');
      expect(result.transcription).toBe('User reports login button issue');
      expect(result.processingTime).toBeGreaterThan(0);
    });

    it('should handle API errors gracefully', async () => {
      mockGenerateContent.mockRejectedValue(new Error('API Error'));

      const result = await aiService.processAudioToReport(
        mockAudioBuffer,
        mockMimeType,
        mockUserContext
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Direct audio processing failed');
    });

    it('should handle cancellation via AbortSignal', async () => {
      const abortController = new AbortController();
      abortController.abort();

      const result = await aiService.processAudioToReport(
        mockAudioBuffer,
        mockMimeType,
        mockUserContext,
        undefined,
        abortController.signal
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('cancelled');
    });

    it('should handle AI-reported analysis failures', async () => {
      const mockFailureResponse = {
        text: JSON.stringify({
          transcription: 'unclear audio...',
          analysis_status: 'FAILED',
          failure_reason: 'POOR_QUALITY_OR_SHORT',
          user_message: 'The audio quality was too poor to analyze',
          suggestions: ['Speak more clearly', 'Find a quieter environment']
        })
      };

      mockGenerateContent.mockResolvedValue(mockFailureResponse);

      const result = await aiService.processAudioToReport(
        mockAudioBuffer,
        mockMimeType,
        mockUserContext
      );

      expect(result.success).toBe(false);
      expect(result.analysisFailure).toBeDefined();
      expect(result.analysisFailure?.reason).toBe('POOR_QUALITY_OR_SHORT');
      expect(result.analysisFailure?.transcription).toBe('unclear audio...');
      expect(result.transcription).toBe('unclear audio...');
    });

    it('should handle malformed AI response gracefully', async () => {
      const mockMalformedResponse = {
        text: 'invalid json response'
      };

      mockGenerateContent.mockResolvedValue(mockMalformedResponse);

      const result = await aiService.processAudioToReport(
        mockAudioBuffer,
        mockMimeType,
        mockUserContext
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Error processing AI response format');
    });

    it('should handle timeout and trigger fallback processing', async () => {
      let callCount = 0;
      
      // Mock generateContent to simulate the race condition scenario
      mockGenerateContent.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          // First call times out - use the exact error message that triggers fallback
          return Promise.reject(new Error('TIMEOUT: Gemini API call exceeded 60 seconds'));
        } else {
          // Second call (fallback) succeeds
          return Promise.resolve({
            text: JSON.stringify({
              transcription: 'fallback transcription',
              analysis_status: 'SUCCESS',
              title: 'Fallback Report',
              description: 'Generated via fallback',
              steps_to_reproduce: '1. Fallback step',
              expected_behavior: 'Fallback expected behavior',
              actual_behavior: 'Fallback actual behavior',
              suggestedSeverity: 'medium',
              suggestedType: 'bug',
              suggestedTags: ['fallback'],
              confidence: 0.7
            })
          });
        }
      });

      const result = await aiService.processAudioToReport(
        mockAudioBuffer,
        mockMimeType,
        mockUserContext
      );

      // The inner catch should handle TIMEOUT and call fallback
      expect(callCount).toBe(2); // Should have called mock twice (main + fallback)
      expect(result.success).toBe(true);
      expect(result.isFallback).toBe(true);
      expect(result.generatedReport?.title).toBe('Fallback Report');
      expect(result.transcription).toBe('fallback transcription');
    }, 15000); // Increase timeout for this test

    it('should handle cancellation during API call', async () => {
      const abortController = new AbortController();
      
      // Mock implementation that properly simulates abort behavior
      mockGenerateContent.mockImplementation(() => {
        return new Promise((resolve, reject) => {
          // Check if already aborted
          if (abortController.signal.aborted) {
            reject(new Error('CANCELLED: Request was cancelled by client'));
            return;
          }

          // Single abort handler to avoid conflicts
          const abortHandler = () => {
            reject(new Error('CANCELLED: Request was cancelled by client'));
          };

          // Set up abort listener (use once to avoid duplicates)
          abortController.signal.addEventListener('abort', abortHandler, { once: true });

          // Simulate a long-running operation that gets interrupted
          const timeoutId = setTimeout(() => {
            resolve({
              text: JSON.stringify({ 
                transcription: 'test',
                analysis_status: 'SUCCESS',
                title: 'Test Report'
              })
            });
          }, 2000); // Longer timeout to ensure abort happens first

          // Clean up timeout when aborted
          abortController.signal.addEventListener('abort', () => {
            clearTimeout(timeoutId);
          }, { once: true });
        });
      });

      // Start the process
      const processPromise = aiService.processAudioToReport(
        mockAudioBuffer,
        mockMimeType,
        mockUserContext,
        undefined,
        abortController.signal
      );

      // Abort immediately to ensure cancellation
      abortController.abort();

      const result = await processPromise;

      expect(result.success).toBe(false);
      expect(result.error).toContain('Request was cancelled by client');
    });

    it('should handle invalid audio data', async () => {
      // Create actually invalid base64 data that will fail the validation
      const invalidAudioBuffer = Buffer.from('not-base64-data-at-all!!!');

      const result = await aiService.processAudioToReport(
        invalidAudioBuffer,
        mockMimeType,
        mockUserContext
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Direct audio processing failed');
    });

    it('should include predefined tags in successful response', async () => {
      const mockAiResponse = {
        text: JSON.stringify({
          transcription: 'test transcription',
          analysis_status: 'SUCCESS',
          title: 'Login Issue',
          description: 'Login button not working',
          suggestedSeverity: 'high',
          suggestedType: 'bug',
          suggestedTags: ['login', 'authentication'],
          confidence: 0.9
        })
      };

      const predefinedTags = {
        bug: ['critical-bug', 'ui-issue'],
        'feature-request': ['enhancement']
      };

      mockGenerateContent.mockResolvedValue(mockAiResponse);

      const result = await aiService.processAudioToReport(
        mockAudioBuffer,
        mockMimeType,
        mockUserContext,
        predefinedTags
      );

      expect(result.success).toBe(true);
      expect(result.generatedReport?.suggestedTags).toBeDefined();
      expect(result.generatedReport?.title).toBe('Login Issue');
    });

    it('should handle fallback timeout gracefully', async () => {
      let callCount = 0;
      
      // Mock both main and fallback calls to timeout
      mockGenerateContent.mockImplementation(() => {
        callCount++;
        // Both calls should timeout
        return Promise.reject(new Error('TIMEOUT: Gemini API call exceeded 60 seconds'));
      });

      const result = await aiService.processAudioToReport(
        mockAudioBuffer,
        mockMimeType,
        mockUserContext
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Fallback processing failed');
    }, 15000); // Increase timeout for this test

    it('should handle large audio files with optimized config', async () => {
      // Create a large audio buffer (simulate > 1MB)
      const largeAudioBuffer = Buffer.alloc(2 * 1024 * 1024, 'a'); // 2MB buffer

      const mockAiResponse = {
        text: JSON.stringify({
          transcription: 'large file transcription',
          analysis_status: 'SUCCESS',
          title: 'Large File Report',
          description: 'Processed large audio file',
          suggestedSeverity: 'medium',
          suggestedType: 'bug',
          confidence: 0.8
        })
      };

      mockGenerateContent.mockResolvedValue(mockAiResponse);

      const result = await aiService.processAudioToReport(
        largeAudioBuffer,
        mockMimeType,
        mockUserContext
      );

      expect(result.success).toBe(true);
      expect(result.generatedReport?.title).toBe('Large File Report');
      expect(result.transcription).toBe('large file transcription');
      
      // Verify that generateContent was called with the correct structure
      expect(mockGenerateContent).toHaveBeenCalledWith(
        expect.objectContaining({
          config: expect.objectContaining({
            temperature: expect.any(Number),
            responseMimeType: 'application/json',
            responseSchema: expect.objectContaining({
              type: undefined,
              properties: expect.objectContaining({
                analysis_status: expect.any(Object),
                transcription: expect.any(Object),
                report: expect.any(Object)
              }),
              required: expect.arrayContaining(['analysis_status', 'transcription'])
            }),
            thinkingConfig: expect.objectContaining({
              thinkingBudget: expect.any(Number)
            })
          }),
          contents: expect.arrayContaining([
            expect.objectContaining({
              role: 'user',
              parts: expect.arrayContaining([
                expect.objectContaining({
                  inlineData: expect.objectContaining({
                    data: expect.any(String),
                    mimeType: 'audio/webm'
                  })
                }),
                expect.objectContaining({
                  text: expect.any(String)
                })
              ])
            })
          ])
        })
      );
    });
  });
});
