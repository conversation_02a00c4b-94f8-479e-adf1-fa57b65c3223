import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function updateTestUserPasswords() {
  try {
    console.log('🔐 Updating test user passwords...');

    const saltRounds = 10;
    const testPassword = 'password123';
    const hashedPassword = await bcrypt.hash(testPassword, saltRounds);

    // Update both test users
    const updatedUsers = await prisma.user.updateMany({
      where: {
        email: {
          in: [
            '<EMAIL>',
            '<EMAIL>'
          ]
        }
      },
      data: {
        password: hashedPassword
      }
    });

    console.log(`✅ Updated ${updatedUsers.count} test users with password: ${testPassword}`);
    console.log('\n🔑 Login Credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');

  } catch (error) {
    console.error('❌ Error updating passwords:', error);
    throw error;
  }
}

async function main() {
  try {
    await updateTestUserPasswords();
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export { updateTestUserPasswords };
