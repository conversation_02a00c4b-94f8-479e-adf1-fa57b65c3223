import { PrismaClient, TransactionStatus } from '@prisma/client';
import { Server } from 'socket.io';
import { TransactionService } from '../services/transactionService';
import { PayerNegotiationService } from '../services/payerNegotiationService';
import { NotificationService } from '../services/notificationService';
import { ChatService } from '../services/chatService';
import { createInitialTransaction } from '../services/transactionService';
import { ConsoleLogger } from '../utils/logger';

const prisma = new PrismaClient();

// Mock Socket.IO server for testing
const mockSocketServer = {
  to: (room: string) => ({
    emit: (event: string, data: any) => {
      console.log(`[SOCKET] Emitting '${event}' to room '${room}':`, JSON.stringify(data, null, 2));
      return mockSocketServer;
    }
  }),
  emit: (event: string, data: any) => {
    console.log(`[SOCKET] Broadcasting '${event}':`, JSON.stringify(data, null, 2));
    return mockSocketServer;
  }
} as any;

async function testScenarioA_WithPaymentInfo() {
  console.log('🔷 SCENARIO A: Users with pre-saved payment information\n');

  let testUser1: any = null;
  let testUser2: any = null;
  let testOffer: any = null;
  let testInterest: any = null;
  let testChatSession: any = null;
  let transaction: any = null;
  try {
    // Initialize services
    const logger = new ConsoleLogger();
    const notificationService = new NotificationService(mockSocketServer);
    const chatService = new ChatService(mockSocketServer);    const transactionService = new TransactionService(mockSocketServer, notificationService, chatService);
    const payerNegotiationService = new PayerNegotiationService(prisma, chatService, mockSocketServer, logger, transactionService, notificationService);

    console.log('✅ Services initialized successfully\n');

    // Step 1: Create test users WITH payment information
    console.log('📝 Step 1: Creating test users WITH payment information...');
    
    testUser1 = await prisma.user.create({
      data: {
        email: `testuser1_${Date.now()}@test.com`,
        username: 'TestUser1_WithPayment',
        password: 'hashedpassword123',
        emailVerified: true,
        phoneVerified: true,
        reputationLevel: 5,
      }
    });

    // Create payment receiving info for user1
    await prisma.paymentReceivingInfo.create({
      data: {
        userId: testUser1.id,
        bankName: 'Test Bank 1',
        accountNumber: '*********',
        accountHolderName: 'Test User One',
        isDefaultForUser: true
      }
    });

    testUser2 = await prisma.user.create({
      data: {
        email: `testuser2_${Date.now()}@test.com`,
        username: 'TestUser2_WithPayment',
        password: 'hashedpassword456',
        emailVerified: true,
        phoneVerified: true,
        reputationLevel: 3,
      }
    });

    // Create payment receiving info for user2
    await prisma.paymentReceivingInfo.create({
      data: {
        userId: testUser2.id,
        bankName: 'Test Bank 2',
        accountNumber: '*********',
        accountHolderName: 'Test User Two',
        isDefaultForUser: true
      }
    });

    console.log(`   ✅ User1 created: ${testUser1.username} (ID: ${testUser1.id})`);
    console.log(`   ✅ User2 created: ${testUser2.username} (ID: ${testUser2.id})`);
    console.log(`   ✅ Both users have payment info pre-saved\n`);

    // Continue with offer creation and transaction flow...
    await runTransactionFlow(testUser1, testUser2, payerNegotiationService, transactionService, 'WITH_PAYMENT_INFO');

    return { success: true, testUser1, testUser2 };

  } catch (error) {
    console.error('❌ Scenario A failed:', error);
    return { success: false, error, testUser1, testUser2 };
  }
}

async function testScenarioB_WithoutPaymentInfo() {
  console.log('🔶 SCENARIO B: Users without payment information\n');

  let testUser1: any = null;
  let testUser2: any = null;

  try {
    // Initialize services
    const logger = new ConsoleLogger();
    const notificationService = new NotificationService(mockSocketServer);
    const chatService = new ChatService(mockSocketServer);    const transactionService = new TransactionService(mockSocketServer, notificationService, chatService);
    const payerNegotiationService = new PayerNegotiationService(prisma, chatService, mockSocketServer, logger, transactionService, notificationService);

    console.log('✅ Services initialized successfully\n');

    // Step 1: Create test users WITHOUT payment information
    console.log('📝 Step 1: Creating test users WITHOUT payment information...');
    
    testUser1 = await prisma.user.create({
      data: {
        email: `testuser1_nopay_${Date.now()}@test.com`,
        username: 'TestUser1_NoPayment',
        password: 'hashedpassword123',
        emailVerified: true,
        phoneVerified: true,
        reputationLevel: 5,
      }
    });

    testUser2 = await prisma.user.create({
      data: {
        email: `testuser2_nopay_${Date.now()}@test.com`,
        username: 'TestUser2_NoPayment',
        password: 'hashedpassword456',
        emailVerified: true,
        phoneVerified: true,
        reputationLevel: 3,
      }
    });

    console.log(`   ✅ User1 created: ${testUser1.username} (ID: ${testUser1.id})`);
    console.log(`   ✅ User2 created: ${testUser2.username} (ID: ${testUser2.id})`);
    console.log(`   ⚠️  No payment info saved for either user\n`);

    // Continue with offer creation and transaction flow...
    await runTransactionFlow(testUser1, testUser2, payerNegotiationService, transactionService, 'WITHOUT_PAYMENT_INFO');

    return { success: true, testUser1, testUser2 };

  } catch (error) {
    console.error('❌ Scenario B failed:', error);
    return { success: false, error, testUser1, testUser2 };
  }
}

async function runTransactionFlow(testUser1: any, testUser2: any, payerNegotiationService: any, transactionService: any, scenario: string) {
  let testOffer: any = null;
  let testInterest: any = null;
  let testChatSession: any = null;
  let transaction: any = null;

  try {
    // Step 2: Create test offer
    console.log('📝 Step 2: Creating test offer...');
    
    testOffer = await prisma.offer.create({
      data: {
        userId: testUser1.id,
        type: 'SELL',
        currencyPair: 'CAD-IRR',
        amount: 1000,
        baseRate: 30000,
        adjustmentForLowerRep: 2.0,
        adjustmentForHigherRep: 1.0,
        status: 'ACTIVE'
      }
    });

    console.log(`   ✅ Offer created: ${testOffer.type} ${testOffer.amount} ${testOffer.currencyPair} (ID: ${testOffer.id})\n`);

    // Step 3: Create interest and chat session
    console.log('📝 Step 3: Creating interest and chat session...');
    
    testInterest = await prisma.interest.create({
      data: {
        offerId: testOffer.id,
        interestedUserId: testUser2.id,
        status: 'ACCEPTED'
      }
    });    testChatSession = await prisma.chatSession.create({
      data: {
        offerId: testOffer.id,
        interestId: testInterest.id,
        userOneId: testUser1.id,
        userTwoId: testUser2.id
      }
    });

    console.log(`   ✅ Interest created and accepted`);
    console.log(`   ✅ Chat session created (ID: ${testChatSession.id})\n`);

    // Step 4: Create transaction with different expectations based on scenario
    console.log('📝 Step 4: Testing transaction creation...');

    transaction = await prisma.$transaction(async (tx) => {
      return await createInitialTransaction(
        tx,
        testChatSession.id,
        testOffer.id,
        'CAD',
        1000,
        testUser1.id,
        'IRR',
        30000,
        testUser2.id
      );
    });

    console.log(`   ✅ Transaction created (ID: ${transaction.id})`);
    console.log(`   ✅ Status: ${transaction.status}\n`);

    // Step 5: Initialize payer negotiation
    console.log('📝 Step 5: Initializing payer negotiation...');
    
    const negotiationId = await payerNegotiationService.initializeNegotiation(transaction.id);
    
    console.log(`   ✅ Negotiation initialized (ID: ${negotiationId})\n`);    // Step 6: Check negotiation details based on scenario
    const negotiation = await prisma.payerNegotiation.findUnique({
      where: { negotiationId: negotiationId },
      include: {
        partyA_PaymentReceivingInfo: true,
        partyB_PaymentReceivingInfo: true
      }
    });

    console.log('📝 Step 6: Verifying payment info status based on scenario...');

    if (scenario === 'WITH_PAYMENT_INFO') {
      console.log(`   ✅ PartyA receiving info status: ${negotiation?.partyA_receivingInfoStatus}`);
      console.log(`   ✅ PartyB receiving info status: ${negotiation?.partyB_receivingInfoStatus}`);
      console.log(`   ✅ PartyA payment info: ${negotiation?.partyA_PaymentReceivingInfo?.bankName}`);
      console.log(`   ✅ PartyB payment info: ${negotiation?.partyB_PaymentReceivingInfo?.bankName}`);
      
      // Verify payment info is loaded from profile
      if (negotiation?.partyA_receivingInfoStatus !== 'CONFIRMED_FROM_PROFILE') {
        throw new Error(`Expected CONFIRMED_FROM_PROFILE for PartyA, got ${negotiation?.partyA_receivingInfoStatus}`);
      }
      if (negotiation?.partyB_receivingInfoStatus !== 'CONFIRMED_FROM_PROFILE') {
        throw new Error(`Expected CONFIRMED_FROM_PROFILE for PartyB, got ${negotiation?.partyB_receivingInfoStatus}`);
      }
    } else {
      console.log(`   ⚠️  PartyA receiving info status: ${negotiation?.partyA_receivingInfoStatus}`);
      console.log(`   ⚠️  PartyB receiving info status: ${negotiation?.partyB_receivingInfoStatus}`);
      console.log(`   ⚠️  PartyA payment info: ${negotiation?.partyA_PaymentReceivingInfo?.bankName || 'Not set'}`);
      console.log(`   ⚠️  PartyB payment info: ${negotiation?.partyB_PaymentReceivingInfo?.bankName || 'Not set'}`);
        // Verify payment info requires user input
      if (negotiation?.partyA_receivingInfoStatus !== 'PENDING_INPUT') {
        console.log(`   ⚠️  Expected PENDING_INPUT for PartyA, got ${negotiation?.partyA_receivingInfoStatus}`);
      }
      if (negotiation?.partyB_receivingInfoStatus !== 'PENDING_INPUT') {
        console.log(`   ⚠️  Expected PENDING_INPUT for PartyB, got ${negotiation?.partyB_receivingInfoStatus}`);
      }
      
      console.log(`   ✅ UI should prompt users to enter payment information before proceeding`);
    }

    console.log('\n');    // Cleanup
    console.log('🧹 Cleaning up test data...');
    await prisma.payerNegotiation.delete({ where: { negotiationId: negotiationId } });
    await prisma.transaction.delete({ where: { id: transaction.id } });
    await prisma.chatSession.delete({ where: { id: testChatSession.id } });
    await prisma.interest.delete({ where: { id: testInterest.id } });
    await prisma.offer.delete({ where: { id: testOffer.id } });
    console.log('   ✅ Transaction flow cleanup completed\n');

  } catch (error) {
    console.error('❌ Transaction flow failed:', error);
    throw error;
  }
}

async function cleanupUsers(testUser1: any, testUser2: any) {
  if (testUser1) {
    // Clean up payment info first
    await prisma.paymentReceivingInfo.deleteMany({ where: { userId: testUser1.id } });
    await prisma.user.delete({ where: { id: testUser1.id } });
  }
  if (testUser2) {
    // Clean up payment info first
    await prisma.paymentReceivingInfo.deleteMany({ where: { userId: testUser2.id } });
    await prisma.user.delete({ where: { id: testUser2.id } });
  }
}

async function testComprehensiveTransactionFlow() {  console.log('🚀 Starting Comprehensive Transaction Flow Test...\n');
  console.log('This test covers both scenarios:\n');
  console.log('🔷 Scenario A: Users with pre-saved payment information');
  console.log('🔶 Scenario B: Users without payment information\n');
  console.log('='.repeat(80) + '\n');

  let scenarioA_Users: any = { testUser1: null, testUser2: null };
  let scenarioB_Users: any = { testUser1: null, testUser2: null };

  try {    // Test Scenario A: With payment info
    const scenarioA_Result = await testScenarioA_WithPaymentInfo();
    scenarioA_Users = scenarioA_Result;

    console.log('='.repeat(80) + '\n');

    // Test Scenario B: Without payment info
    const scenarioB_Result = await testScenarioB_WithoutPaymentInfo();
    scenarioB_Users = scenarioB_Result;

    console.log('='.repeat(80) + '\n');

    // Summary
    console.log('🎉 COMPREHENSIVE TEST COMPLETED!\n');
    console.log('📊 Test Results Summary:');
    console.log(`   ${scenarioA_Result.success ? '✅' : '❌'} Scenario A (With Payment Info): ${scenarioA_Result.success ? 'PASSED' : 'FAILED'}`);
    console.log(`   ${scenarioB_Result.success ? '✅' : '❌'} Scenario B (Without Payment Info): ${scenarioB_Result.success ? 'PASSED' : 'FAILED'}`);
    
    if (scenarioA_Result.success && scenarioB_Result.success) {
      console.log('\n🔥 All scenarios passed! The transaction flow handles both cases correctly.');
      console.log('🔥 UI can now support both payment info states properly!');
    } else {
      console.log('\n⚠️  Some scenarios failed. Check the logs above for details.');
    }

  } catch (error) {
    console.error('❌ Comprehensive test failed:', error);
  } finally {
    // Cleanup all test data
    console.log('\n🧹 Final cleanup...');
    try {
      await cleanupUsers(scenarioA_Users.testUser1, scenarioA_Users.testUser2);
      await cleanupUsers(scenarioB_Users.testUser1, scenarioB_Users.testUser2);
      console.log('   ✅ All test users cleaned up');
    } catch (cleanupError) {
      console.error('   ❌ Cleanup error:', cleanupError);
    }
    
    await prisma.$disconnect();
    console.log('   ✅ Database connection closed');
    console.log('\n✨ Comprehensive test execution completed!');
  }
}

// Run the comprehensive test
testComprehensiveTransactionFlow();
