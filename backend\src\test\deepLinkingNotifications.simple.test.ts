import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NotificationService } from '../services/notificationService';

// Use vi.hoisted to ensure these are available when the mock is created
const mockPrismaNotificationCreate = vi.hoisted(() => vi.fn());
const mockPrismaNotificationFindMany = vi.hoisted(() => vi.fn());
const mockPrismaNotificationFindUnique = vi.hoisted(() => vi.fn());
const mockPrismaNotificationUpdate = vi.hoisted(() => vi.fn());

vi.mock('@prisma/client', () => {
  return {
    PrismaClient: vi.fn().mockImplementation(() => ({
      notification: {
        create: mockPrismaNotificationCreate,
        findMany: mockPrismaNotificationFindMany,
        findUnique: mockPrismaNotificationFindUnique,
        update: mockPrismaNotificationUpdate,
      },
    })),
    NotificationType: {
      NEW_INTEREST_ON_YOUR_OFFER: 'NEW_INTEREST_ON_YOUR_OFFER',
      CHAT_MESSAGE_RECEIVED: 'CHAT_MESSAGE_RECEIVED',
      TRANSACTION_STATUS_CHANGED: 'TRANSACTION_STATUS_CHANGED',
      GENERIC_NOTIFICATION: 'GENERIC_NOTIFICATION',
    },
  };
});

// Mock Socket.IO
const mockSocket = {
  to: vi.fn().mockReturnThis(),
  emit: vi.fn(),
};

describe('Deep Linking Notifications - Backend', () => {
  let notificationService: NotificationService;

  beforeEach(() => {
    vi.clearAllMocks();
    notificationService = new NotificationService(mockSocket);
  });
  it('should support creating notifications with entity linking for offers', async () => {
    const mockNotification = {
      id: 'test-notification-id',
      userId: 'user-123',
      type: 'NEW_INTEREST_ON_YOUR_OFFER',
      title: 'New Interest',
      message: 'Someone is interested in your offer',
      relatedEntityType: 'OFFER',
      relatedEntityId: 'offer-123',
      data: JSON.stringify({
        offerId: 'offer-123',
        interestId: 'interest-456'
      }),
      isRead: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    mockPrismaNotificationCreate.mockResolvedValue(mockNotification);

    const notificationInput = {
      userId: 'user-123',
      type: 'NEW_INTEREST_ON_YOUR_OFFER' as any,
      message: 'Someone is interested in your offer',
      relatedEntityType: 'OFFER',
      relatedEntityId: 'offer-123',
      data: JSON.stringify({
        offerId: 'offer-123',
        interestId: 'interest-456'
      })
    };

    const result = await notificationService.createNotification(notificationInput);

    // Verify that the service called Prisma with the correct parameters
    expect(mockPrismaNotificationCreate).toHaveBeenCalledWith({
      data: {
        userId: 'user-123',
        type: 'NEW_INTEREST_ON_YOUR_OFFER',
        message: 'Someone is interested in your offer',
        relatedEntityType: 'OFFER',
        relatedEntityId: 'offer-123',
        actorId: undefined,
        actorUsername: undefined,
        data: JSON.stringify({
          offerId: 'offer-123',
          interestId: 'interest-456'
        })
      }
    });

    // Verify the returned result
    expect(result.relatedEntityType).toBe('OFFER');
    expect(result.relatedEntityId).toBe('offer-123');
    expect(JSON.parse(result.data!)).toMatchObject({
      offerId: 'offer-123',
      interestId: 'interest-456'
    });

    // Verify socket emission
    expect(mockSocket.to).toHaveBeenCalledWith('user-123');
    expect(mockSocket.emit).toHaveBeenCalledWith('NEW_NOTIFICATION', mockNotification);
  });
  it('should support creating notifications with entity linking for chat sessions', async () => {
    const mockNotification = {
      id: 'test-notification-id',
      userId: 'user-123',
      type: 'CHAT_MESSAGE_RECEIVED',
      title: 'New Message',
      message: 'You have a new message',
      relatedEntityType: 'CHAT_SESSION',
      relatedEntityId: 'chat-789',
      data: JSON.stringify({
        chatSessionId: 'chat-789'
      }),
      isRead: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    mockPrismaNotificationCreate.mockResolvedValue(mockNotification);

    const notificationInput = {
      userId: 'user-123',
      type: 'CHAT_MESSAGE_RECEIVED' as any,
      message: 'You have a new message',
      relatedEntityType: 'CHAT_SESSION',
      relatedEntityId: 'chat-789',
      data: JSON.stringify({
        chatSessionId: 'chat-789'
      })
    };

    const result = await notificationService.createNotification(notificationInput);

    // Verify that the service called Prisma with the correct parameters
    expect(mockPrismaNotificationCreate).toHaveBeenCalledWith({
      data: {
        userId: 'user-123',
        type: 'CHAT_MESSAGE_RECEIVED',
        message: 'You have a new message',
        relatedEntityType: 'CHAT_SESSION',
        relatedEntityId: 'chat-789',
        actorId: undefined,
        actorUsername: undefined,
        data: JSON.stringify({
          chatSessionId: 'chat-789'
        })
      }
    });

    // Verify the returned result
    expect(result.relatedEntityType).toBe('CHAT_SESSION');
    expect(result.relatedEntityId).toBe('chat-789');

    // Verify socket emission
    expect(mockSocket.to).toHaveBeenCalledWith('user-123');
    expect(mockSocket.emit).toHaveBeenCalledWith('NEW_NOTIFICATION', mockNotification);
  });
  it('should support creating notifications with entity linking for transactions', async () => {
    const mockNotification = {
      id: 'test-notification-id',
      userId: 'user-123',
      type: 'TRANSACTION_STATUS_CHANGED',
      title: 'Transaction Update',
      message: 'Your transaction status changed',
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: 'transaction-101',
      data: JSON.stringify({
        transactionId: 'transaction-101'
      }),
      isRead: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    mockPrismaNotificationCreate.mockResolvedValue(mockNotification);

    const notificationInput = {
      userId: 'user-123',
      type: 'TRANSACTION_STATUS_CHANGED' as any,
      message: 'Your transaction status changed',
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: 'transaction-101',
      data: JSON.stringify({
        transactionId: 'transaction-101'
      })
    };

    const result = await notificationService.createNotification(notificationInput);

    // Verify that the service called Prisma with the correct parameters
    expect(mockPrismaNotificationCreate).toHaveBeenCalledWith({
      data: {
        userId: 'user-123',
        type: 'TRANSACTION_STATUS_CHANGED',
        message: 'Your transaction status changed',
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: 'transaction-101',
        actorId: undefined,
        actorUsername: undefined,
        data: JSON.stringify({
          transactionId: 'transaction-101'
        })
      }
    });

    // Verify the returned result
    expect(result.relatedEntityType).toBe('TRANSACTION');
    expect(result.relatedEntityId).toBe('transaction-101');

    // Verify socket emission
    expect(mockSocket.to).toHaveBeenCalledWith('user-123');
    expect(mockSocket.emit).toHaveBeenCalledWith('NEW_NOTIFICATION', mockNotification);
  });
  it('should fetch notifications with entity linking data', async () => {
    const mockNotifications = [
      {
        id: 'notification-1',
        type: 'NEW_INTEREST_ON_YOUR_OFFER',
        relatedEntityType: 'OFFER',
        relatedEntityId: 'offer-123',
        data: JSON.stringify({ offerId: 'offer-123' }),
        userId: 'user-123',
        message: 'Test message',
        isRead: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        actorId: null,
        actorUsername: null
      },
      {
        id: 'notification-2',
        type: 'CHAT_MESSAGE_RECEIVED',
        relatedEntityType: 'CHAT_SESSION',
        relatedEntityId: 'chat-789',
        data: JSON.stringify({ chatSessionId: 'chat-789' }),
        userId: 'user-123',
        message: 'Test message',
        isRead: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        actorId: null,
        actorUsername: null
      }
    ];

    mockPrismaNotificationFindMany.mockResolvedValue(mockNotifications);

    const result = await notificationService.getNotificationsForUser('user-123');

    // Verify that the service called Prisma with the correct parameters
    expect(mockPrismaNotificationFindMany).toHaveBeenCalledWith({
      where: { userId: 'user-123' },
      orderBy: { createdAt: 'desc' },
      take: 20,
      skip: 0
    });

    // Verify the returned result
    expect(result).toHaveLength(2);
    expect(result[0].relatedEntityType).toBe('OFFER');
    expect(result[1].relatedEntityType).toBe('CHAT_SESSION');
  });
  it('should support all entity types for deep linking', async () => {
    const entityTypes = ['OFFER', 'CHAT_SESSION', 'CHAT', 'TRANSACTION'];
    
    for (const entityType of entityTypes) {
      const mockNotification = {
        id: `notification-${entityType}`,
        userId: 'user-123',
        type: 'GENERIC_NOTIFICATION',
        message: 'Test message',
        relatedEntityType: entityType,
        relatedEntityId: `${entityType.toLowerCase()}-123`,
        data: null,
        isRead: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        actorId: null,
        actorUsername: null
      };

      mockPrismaNotificationCreate.mockResolvedValue(mockNotification);

      const notificationInput = {
        userId: 'user-123',
        type: 'GENERIC_NOTIFICATION' as any,
        message: 'Test message',
        relatedEntityType: entityType,
        relatedEntityId: `${entityType.toLowerCase()}-123`
      };

      const result = await notificationService.createNotification(notificationInput);

      // Verify that the service called Prisma with the correct parameters
      expect(mockPrismaNotificationCreate).toHaveBeenCalledWith({
        data: {
          userId: 'user-123',
          type: 'GENERIC_NOTIFICATION',
          message: 'Test message',
          relatedEntityType: entityType,
          relatedEntityId: `${entityType.toLowerCase()}-123`,
          actorId: undefined,
          actorUsername: undefined,
          data: null
        }
      });

      expect(result.relatedEntityType).toBe(entityType);
    }
  });
});
