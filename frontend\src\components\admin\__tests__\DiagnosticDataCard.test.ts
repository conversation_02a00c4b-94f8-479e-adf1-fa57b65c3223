import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import DiagnosticDataCard from '../DiagnosticDataCard.vue';
import type { DiagnosticData } from '@/types/admin';

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => '2024/01/15 10:30:00'),
  formatDistanceToNow: vi.fn(() => '5 minutes ago')
}));

// Mock icons
vi.mock('@vicons/ionicons5', () => ({
  WifiOutline: { name: 'WifiOutline' },
  FolderOutline: { name: 'FolderOutline' },
  TimeOutline: { name: 'TimeOutline' }
}));

// Mock Naive UI components
vi.mock('naive-ui', () => ({
  NCard: { name: 'NCard', template: '<div class="n-card"><slot /></div>' },
  NTag: { name: 'NTag', template: '<span class="n-tag"><slot /></span>' },
  NResult: { name: 'NResult', template: '<div class="n-result"><slot /></div>' },
  NDescriptions: { name: 'NDescriptions', template: '<div class="n-descriptions"><slot /></div>' },
  NDescriptionsItem: { name: 'NDescriptionsItem', template: '<div class="n-descriptions-item"><slot /></div>' },
  NText: { name: 'NText', template: '<span class="n-text"><slot /></span>' },
  NIcon: { name: 'NIcon', template: '<i class="n-icon"><slot /></i>' },
  NCollapse: { name: 'NCollapse', template: '<div class="n-collapse"><slot /></div>' },
  NCollapseItem: { name: 'NCollapseItem', template: '<div class="n-collapse-item"><slot /></div>' },
  NCode: { name: 'NCode', template: '<pre class="n-code"><slot /></pre>' }
}));

describe('DiagnosticDataCard', () => {
  beforeEach(() => {
    const pinia = createPinia();
    setActivePinia(pinia);
  });

  const mockDiagnosticData: DiagnosticData = {
    connectionStatus: {
      isConnected: true,
      connectionQuality: 'excellent',
      connectionStatus: 'Connected - Real-time updates',
      transportType: 'websocket',
      reconnectAttempts: 0,
      isReconnecting: false,
      lastDisconnectReason: null,
      socketId: 'socket-test-123',
      socketConnected: true
    },
    piniaStoreSnapshot: {
      auth: {
        user: { id: 'user-123', email: '<EMAIL>' },
        token: 'mock-token',
        isAuthenticated: true
      },
      connection: {
        isConnected: true,
        connectionQuality: 'excellent'
      },
      theme: {
        isDark: true,
        currentTheme: 'dark'
      }
    },
    captureTimestamp: '2024-01-15T10:30:00.000Z'
  };

  describe('with diagnostic data', () => {
    it('should render diagnostic data correctly', () => {
      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: mockDiagnosticData
        }
      });

      // Check that the enhanced report tag is shown
      expect(wrapper.text()).toContain('Enhanced Report');
      
      // Check connection status section
      expect(wrapper.text()).toContain('Connection Status');
      expect(wrapper.text()).toContain('Connected - Real-time updates');
      expect(wrapper.text()).toContain('websocket');
      expect(wrapper.text()).toContain('socket-test-123');
      
      // Check store snapshot section
      expect(wrapper.text()).toContain('Application State Snapshot');
      expect(wrapper.text()).toContain('3 stores');
      
      // Check capture information
      expect(wrapper.text()).toContain('Capture Information');
    });

    it('should display connection quality with correct tag type', () => {
      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: mockDiagnosticData
        }
      });

      // Should show excellent quality
      expect(wrapper.text()).toContain('Excellent');
    });

    it('should handle poor connection quality', () => {
      const poorConnectionData = {
        ...mockDiagnosticData,
        connectionStatus: {
          ...mockDiagnosticData.connectionStatus,
          connectionQuality: 'poor',
          connectionStatus: 'Connected - Limited functionality',
          reconnectAttempts: 3
        }
      };

      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: poorConnectionData
        }
      });

      expect(wrapper.text()).toContain('Poor');
      expect(wrapper.text()).toContain('Connected - Limited functionality');
      expect(wrapper.text()).toContain('3'); // reconnect attempts
    });

    it('should handle missing socket information', () => {
      const noSocketData = {
        ...mockDiagnosticData,
        connectionStatus: {
          ...mockDiagnosticData.connectionStatus,
          socketId: undefined,
          socketConnected: undefined
        }
      };

      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: noSocketData
        }
      });

      expect(wrapper.text()).toContain('Not available');
    });

    it('should display store names in readable format', () => {
      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: mockDiagnosticData
        }
      });

      // Check that store names are converted to readable format
      expect(wrapper.text()).toContain('Authentication');
      expect(wrapper.text()).toContain('Connection');
      expect(wrapper.text()).toContain('Theme Settings');
    });

    it('should calculate store data sizes', () => {
      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: mockDiagnosticData
        }
      });

      // Should show data sizes (exact values may vary, but format should be present)
      const text = wrapper.text();
      expect(text).toMatch(/\d+(\.\d+)?\s*(B|KB|MB)/);
    });
  });

  describe('without diagnostic data', () => {
    it('should show legacy report message when no diagnostic data', () => {
      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: null
        }
      });

      expect(wrapper.text()).toContain('Legacy Report');
      expect(wrapper.text()).toContain('No Diagnostic Data');
      expect(wrapper.text()).toContain('This report was submitted before diagnostic data collection was implemented');
    });

    it('should show legacy report message when diagnostic data is undefined', () => {
      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: undefined
        }
      });

      expect(wrapper.text()).toContain('Legacy Report');
      expect(wrapper.text()).toContain('No Diagnostic Data');
    });
  });

  describe('helper functions', () => {
    it('should format connection quality correctly', () => {
      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: mockDiagnosticData
        }
      });

      // Test the component instance methods
      const vm = wrapper.vm as any;
      
      expect(vm.formatConnectionQuality('excellent')).toBe('Excellent');
      expect(vm.formatConnectionQuality('good')).toBe('Good');
      expect(vm.formatConnectionQuality('poor')).toBe('Poor');
    });

    it('should get correct connection status tag types', () => {
      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: mockDiagnosticData
        }
      });

      const vm = wrapper.vm as any;
      
      expect(vm.getConnectionStatusType('excellent')).toBe('success');
      expect(vm.getConnectionStatusType('good')).toBe('info');
      expect(vm.getConnectionStatusType('poor')).toBe('warning');
      expect(vm.getConnectionStatusType('disconnected')).toBe('error');
    });

    it('should convert store names to display names', () => {
      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: mockDiagnosticData
        }
      });

      const vm = wrapper.vm as any;
      
      expect(vm.getStoreDisplayName('auth')).toBe('Authentication');
      expect(vm.getStoreDisplayName('chatStore')).toBe('Chat');
      expect(vm.getStoreDisplayName('unknownStore')).toBe('unknownStore');
    });

    it('should calculate data sizes correctly', () => {
      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: mockDiagnosticData
        }
      });

      const vm = wrapper.vm as any;
      
      // Test with small object
      const smallData = { test: 'value' };
      const size = vm.getStoreDataSize(smallData);
      expect(size).toMatch(/\d+(\.\d+)?\s*(B|KB)/);
      
      // Test with invalid data
      const invalidData = { circular: null };
      invalidData.circular = invalidData; // Create circular reference
      const invalidSize = vm.getStoreDataSize(invalidData);
      expect(invalidSize).toBe('Unknown size');
    });
  });

  describe('responsive design', () => {
    it('should render properly on mobile viewports', () => {
      // Mock window.innerWidth for mobile
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const wrapper = mount(DiagnosticDataCard, {
        props: {
          diagnosticData: mockDiagnosticData
        }
      });

      // Component should still render all sections
      expect(wrapper.text()).toContain('Connection Status');
      expect(wrapper.text()).toContain('Application State Snapshot');
      expect(wrapper.text()).toContain('Capture Information');
    });
  });
});
