import { Hono } from 'hono';
import { authMiddleware, type AuthVariables, type JwtPayload } from '../middleware/auth';
import { PaymentMethodService } from '../services/paymentMethodService';
import { PrismaClient } from '@prisma/client';
import { ConsoleLogger } from '../utils/logger';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

// Import PaymentMethodType from the generated client
type PaymentMethodType = 'BANK_TRANSFER' | 'DIGITAL_WALLET' | 'CRYPTO_WALLET' | 'MOBILE_MONEY' | 'CASH_PICKUP';

// Validation schemas
const CreatePaymentMethodSchema = z.object({
  currency: z.string().min(3).max(3).transform(s => s.toUpperCase()),
  paymentMethodType: z.enum(['BANK_TRANSFER', 'DIGITAL_WALLET', 'CRYPTO_WALLET', 'MOBILE_MONEY', 'CASH_PICKUP']),
  bankName: z.string().min(1).max(100),
  accountNumber: z.string().min(1).max(50),
  accountHolderName: z.string().min(1).max(100),
  swiftCode: z.string().max(11).optional(),
  iban: z.string().max(34).optional(),
  routingNumber: z.string().max(20).optional(),
  sortCode: z.string().max(8).optional(),
  bsb: z.string().max(6).optional(),
  notes: z.string().max(500).optional(),
  isDefaultForUser: z.boolean().optional()
});

const UpdatePaymentMethodSchema = CreatePaymentMethodSchema.partial().extend({
  isActive: z.boolean().optional()
});

const PaymentMethodFiltersSchema = z.object({
  currency: z.string().optional(),
  paymentMethodType: z.enum(['BANK_TRANSFER', 'DIGITAL_WALLET', 'CRYPTO_WALLET', 'MOBILE_MONEY', 'CASH_PICKUP']).optional(),
  isActive: z.boolean().optional(),
  isDefaultOnly: z.boolean().optional()
});

export function createPaymentMethodRoutes(prisma: PrismaClient) {
  const router = new Hono<{ Variables: AuthVariables }>();
  const logger = new ConsoleLogger();
  const paymentMethodService = new PaymentMethodService(prisma, logger);

  // Get all payment methods for the authenticated user
  router.get(
    '/',
    authMiddleware,
    zValidator('query', PaymentMethodFiltersSchema),
    async (c) => {
      try {
        const jwtPayload = c.get('jwtPayload');
        const userId = jwtPayload.userId;
        const filters = c.req.valid('query');

        const paymentMethods = await paymentMethodService.getUserPaymentMethods(userId, filters);

        return c.json({
          success: true,
          data: paymentMethods,
          message: 'Payment methods retrieved successfully'
        });
      } catch (error: any) {
        logger.error('Error in GET /payment-methods:', error);
        return c.json({
          success: false,
          error: error.message || 'Failed to fetch payment methods'
        }, 500);
      }
    }
  );

  // Get payment methods grouped by currency
  router.get(
    '/by-currency',
    authMiddleware,
    async (c) => {
      try {
        const jwtPayload = c.get('jwtPayload');
        const userId = jwtPayload.userId;

        const paymentMethodsByCurrency = await paymentMethodService.getUserPaymentMethodsByCurrency(userId);

        return c.json({
          success: true,
          data: paymentMethodsByCurrency,
          message: 'Payment methods by currency retrieved successfully'
        });
      } catch (error: any) {
        logger.error('Error in GET /payment-methods/by-currency:', error);
        return c.json({
          success: false,
          error: error.message || 'Failed to fetch payment methods by currency'
        }, 500);
      }
    }
  );

  // Get default payment method for a specific currency
  router.get(
    '/default/:currency',
    authMiddleware,
    async (c) => {
      try {
        const jwtPayload = c.get('jwtPayload');
        const userId = jwtPayload.userId;
        const currency = c.req.param('currency').toUpperCase();

        const defaultMethod = await paymentMethodService.getDefaultPaymentMethod(userId, currency);

        if (!defaultMethod) {
          return c.json({
            success: false,
            error: `No default payment method found for currency ${currency}`
          }, 404);
        }

        return c.json({
          success: true,
          data: defaultMethod,
          message: 'Default payment method retrieved successfully'
        });
      } catch (error: any) {
        logger.error('Error in GET /payment-methods/default/:currency:', error);
        return c.json({
          success: false,
          error: error.message || 'Failed to fetch default payment method'
        }, 500);
      }
    }
  );

  // Get user payment method statistics
  router.get(
    '/stats',
    authMiddleware,
    async (c) => {
      try {
        const jwtPayload = c.get('jwtPayload');
        const userId = jwtPayload.userId;

        const stats = await paymentMethodService.getUserPaymentMethodStats(userId);

        return c.json({
          success: true,
          data: stats,
          message: 'Payment method statistics retrieved successfully'
        });
      } catch (error: any) {
        logger.error('Error in GET /payment-methods/stats:', error);
        return c.json({
          success: false,
          error: error.message || 'Failed to fetch payment method statistics'
        }, 500);
      }
    }
  );

  // Get configured currencies
  router.get(
    '/currencies',
    authMiddleware,
    async (c) => {
      try {
        const jwtPayload = c.get('jwtPayload');
        const userId = jwtPayload.userId;

        const currencies = await paymentMethodService.getUserConfiguredCurrencies(userId);

        return c.json({
          success: true,
          data: currencies,
          message: 'Configured currencies retrieved successfully'
        });
      } catch (error: any) {
        logger.error('Error in GET /payment-methods/currencies:', error);
        return c.json({
          success: false,
          error: error.message || 'Failed to fetch configured currencies'
        }, 500);
      }
    }
  );

  // Create a new payment method
  router.post(
    '/',
    authMiddleware,
    zValidator('json', CreatePaymentMethodSchema),
    async (c) => {
      try {
        const jwtPayload = c.get('jwtPayload');
        const userId = jwtPayload.userId;
        const payload = c.req.valid('json');

        // Validate required fields
      if (!payload.currency) {
        return c.json({ error: 'Currency is required' }, 400);
      }
      if (!payload.paymentMethodType) {
        return c.json({ error: 'Payment method type is required' }, 400);
      }
      if (!payload.bankName) {
        return c.json({ error: 'Bank name is required' }, 400);
      }
      if (!payload.accountNumber) {
        return c.json({ error: 'Account number is required' }, 400);
      }
      if (!payload.accountHolderName) {
        return c.json({ error: 'Account holder name is required' }, 400);
      }

      // Type assertion after validation
      const validPayload: CreatePaymentMethodPayload = {
        currency: payload.currency,
        paymentMethodType: payload.paymentMethodType,
        bankName: payload.bankName,
        accountNumber: payload.accountNumber,
        accountHolderName: payload.accountHolderName,
        swiftCode: payload.swiftCode,
        iban: payload.iban,
        routingNumber: payload.routingNumber,
        sortCode: payload.sortCode,
        bsb: payload.bsb,
        notes: payload.notes
      };

      const paymentMethod = await paymentMethodService.createPaymentMethod(userId, validPayload);

        return c.json({
          success: true,
          data: paymentMethod,
          message: 'Payment method created successfully'
        }, 201);
      } catch (error: any) {
        logger.error('Error in POST /payment-methods:', error);
        return c.json({
          success: false,
          error: error.message || 'Failed to create payment method'
        }, 500);
      }
    }
  );

  // Update an existing payment method
  router.put(
    '/:id',
    authMiddleware,
    zValidator('json', UpdatePaymentMethodSchema),
    async (c) => {
      try {
        const jwtPayload = c.get('jwtPayload');
        const userId = jwtPayload.userId;
        const paymentMethodId = c.req.param('id');
        const payload = c.req.valid('json');

        const paymentMethod = await paymentMethodService.updatePaymentMethod(userId, paymentMethodId, payload);

        return c.json({
          success: true,
          data: paymentMethod,
          message: 'Payment method updated successfully'
        });
      } catch (error: any) {
        logger.error('Error in PUT /payment-methods/:id:', error);
        return c.json({
          success: false,
          error: error.message || 'Failed to update payment method'
        }, 500);
      }
    }
  );

  // Set payment method as default
  router.patch(
    '/:id/set-default',
    authMiddleware,
    async (c) => {
      try {
        const jwtPayload = c.get('jwtPayload');
        const userId = jwtPayload.userId;
        const paymentMethodId = c.req.param('id');

        const paymentMethod = await paymentMethodService.setAsDefault(userId, paymentMethodId);

        return c.json({
          success: true,
          data: paymentMethod,
          message: 'Payment method set as default successfully'
        });
      } catch (error: any) {
        logger.error('Error in PATCH /payment-methods/:id/set-default:', error);
        return c.json({
          success: false,
          error: error.message || 'Failed to set payment method as default'
        }, 500);
      }
    }
  );

  // Delete (deactivate) a payment method
  router.delete(
    '/:id',
    authMiddleware,
    async (c) => {
      try {
        const jwtPayload = c.get('jwtPayload');
        const userId = jwtPayload.userId;
        const paymentMethodId = c.req.param('id');

        await paymentMethodService.deletePaymentMethod(userId, paymentMethodId);

        return c.json({
          success: true,
          message: 'Payment method deleted successfully'
        });
      } catch (error: any) {
        logger.error('Error in DELETE /payment-methods/:id:', error);
        return c.json({
          success: false,
          error: error.message || 'Failed to delete payment method'
        }, 500);
      }
    }
  );

  return router;
}
