// Test script to verify the singleton PrismaClient pattern works correctly
import { getPrismaClient, disconnectDatabase } from '../src/utils/database';
import { PaymentInfoService } from '../src/services/paymentInfoService';

async function testSingletonPattern() {
  console.log('Testing PrismaClient singleton pattern...');

  // Test 1: Multiple calls to getPrismaClient should return the same instance
  const prisma1 = getPrismaClient();
  const prisma2 = getPrismaClient();
  
  if (prisma1 === prisma2) {
    console.log('✅ Test 1 PASSED: getPrismaClient returns the same instance');
  } else {
    console.log('❌ Test 1 FAILED: getPrismaClient returns different instances');
  }

  // Test 2: PaymentInfoService should work with injected PrismaClient
  try {
    const paymentInfoService = new PaymentInfoService(prisma1);
    console.log('✅ Test 2 PASSED: PaymentInfoService accepts injected PrismaClient');
  } catch (error) {
    console.log('❌ Test 2 FAILED: PaymentInfoService constructor error:', error);
  }

  // Test 3: Database connection should work
  try {
    await prisma1.$queryRaw`SELECT 1 as test`;
    console.log('✅ Test 3 PASSED: Database connection works');
  } catch (error) {
    console.log('⚠️  Test 3 SKIPPED: Database not available (expected in test environment)');
  }

  // Cleanup
  await disconnectDatabase();
  console.log('✅ Cleanup completed: Database disconnected');
}

// Run tests if this file is executed directly
if (require.main === module) {
  testSingletonPattern().catch(console.error);
}
