<template>
  <section class="actions-section" :class="{ 'enhanced': true }">
    <h2 class="section-title enhanced-title animate-fade-up" :style="{ animationDelay: '0.1s' }">
      {{ $t('homeView.quickActions') }}
    </h2>

    <!-- Enhanced loading skeleton -->
    <div v-if="!contentLoaded" class="actions-skeleton">
      <div class="skeleton-grid">
        <div 
          class="action-skeleton" 
          v-for="n in 3" 
          :key="n" 
          :style="{ animationDelay: `${n * 0.1}s` }"
        >
          <div class="skeleton-card">
            <div class="skeleton-icon"></div>
            <div class="skeleton-title"></div>
            <div class="skeleton-description"></div>
            <div class="skeleton-button"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced actions with animations -->
    <div v-else class="actions-enhanced" :class="{ 'loaded': contentLoaded }">
      <n-grid 
        :cols="1" 
        :s-cols="1" 
        :m-cols="2" 
        :l-cols="3" 
        :x-gap="16" 
        :y-gap="16" 
        responsive="screen" 
        class="actions-grid"
        item-responsive
      >
        <n-grid-item>
          <n-card 
            hoverable 
            class="action-card enhanced-action animate-fade-up" 
            :style="{ animationDelay: '0.2s' }"
            @click="goToMyOffers"
            @touchstart="handleCardPress"
            @mousedown="handleCardPress"
          >
            <div class="action-content enhanced-content">
              <div class="action-header">
                <div class="icon-container">
                  <n-icon size="40" color="#2080f0" class="action-icon">
                    <FileTextOutlined />
                  </n-icon>
                </div>
                <h3 class="action-title">{{ $t('homeView.myOffersAction') }}</h3>
              </div>
              <p class="action-description">{{ $t('homeView.myOffersDescription') }}</p>
              <div class="action-footer">
                <n-button text type="primary" class="action-button">
                  {{ $t('homeView.viewMyOffers') }}
                  <template #icon>
                    <n-icon class="arrow-icon"><ArrowRightOutlined /></n-icon>
                  </template>
                </n-button>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        
        <n-grid-item>
          <n-card 
            hoverable 
            class="action-card enhanced-action animate-fade-up" 
            :style="{ animationDelay: '0.3s' }"
            @click="goToMatches"
            @touchstart="handleCardPress"
            @mousedown="handleCardPress"
          >
            <div class="action-content enhanced-content">
              <div class="action-header">
                <div class="icon-container">
                  <n-icon size="40" color="#f0a020" class="action-icon">
                    <HeartOutlined />
                  </n-icon>
                  <!-- Enhanced animated badge -->
                  <n-badge 
                    v-if="props.pendingMatchesCount && props.pendingMatchesCount > 0" 
                    :value="props.pendingMatchesCount"
                    class="enhanced-badge animated-badge"
                    type="warning"
                  />
                </div>
                <h3 class="action-title">{{ $t('homeView.myMatchesAction') }}</h3>
              </div>
              <p class="action-description">{{ $t('homeView.myMatchesDescription') }}</p>
              <div class="action-footer">
                <n-button text type="primary" class="action-button">
                  {{ $t('homeView.viewMyMatches') }}
                  <template #icon>
                    <n-icon class="arrow-icon"><ArrowRightOutlined /></n-icon>
                  </template>
                </n-button>
              </div>
            </div>
          </n-card>
        </n-grid-item>
        
        <n-grid-item>
          <n-card 
            hoverable 
            class="action-card enhanced-action animate-fade-up" 
            :style="{ animationDelay: '0.4s' }"
            @click="goToProfile"
            @touchstart="handleCardPress"
            @mousedown="handleCardPress"
          >
            <div class="action-content enhanced-content">
              <div class="action-header">
                <div class="icon-container">
                  <n-icon size="40" color="#18a058" class="action-icon">
                    <UserOutlined />
                  </n-icon>
                </div>
                <h3 class="action-title">{{ $t('homeView.profile') }}</h3>
              </div>
              <p class="action-description">{{ $t('homeView.profileDescription') }}</p>
              <div class="action-footer">
                <n-button text type="primary" class="action-button">
                  {{ $t('homeView.viewProfile') }}
                  <template #icon>
                    <n-icon class="arrow-icon"><ArrowRightOutlined /></n-icon>
                  </template>
                </n-button>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { NCard, NGrid, NGridItem, NIcon, NButton, NBadge } from 'naive-ui'
import { FileTextOutlined, HeartOutlined, UserOutlined, ArrowRightOutlined } from '@vicons/antd'

const router = useRouter();

// Enhanced loading and animation state
const contentLoaded = ref(false)

// Props
const props = defineProps<{
  pendingMatchesCount?: number
}>()

// Simulate realistic loading time for skeleton
onMounted(() => {
  // Longer delay for QuickActions to sequence after Stats
  setTimeout(() => {
    contentLoaded.value = true
  }, 900)
})

// Enhanced card interactions with haptic-like feedback
function handleCardPress(event: Event) {
  const cardElement = event.currentTarget as HTMLElement
  cardElement.style.transform = 'scale(0.98)'
  
  // Reset after short delay (haptic-like feedback)
  setTimeout(() => {
    cardElement.style.transform = ''
  }, 150)
}

// Enhanced navigation functions with feedback
function goToMyOffers() {
  console.log('📄 Navigating to My Offers')
  router.push({ name: 'MyOffers' });
}

function goToMatches() {
  console.log('💖 Navigating to Matches')
  router.push({ name: 'MatchList' });
}

function goToProfile() {
  console.log('👤 Navigating to Profile')
  router.push({ name: 'profile' });
}
</script>

<style scoped>
/* Mobile-first enhanced section styling */
.actions-section {
  padding: 1.5rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

/* Mobile-first grid configuration */
.actions-grid {
  --grid-cols: 1; /* Mobile: single column */
  width: 100%;
}

/* Force proper grid item layout on mobile */
.actions-grid .n-grid-item {
  width: 100%;
  max-width: 100%;
}

/* Enhanced skeleton loading */
.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(var(--grid-cols), 1fr);
  gap: 1rem;
}

/* Enhanced section title with mobile optimization */
.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease 0.2s forwards;
}

/* Light theme section title */
[data-theme="light"] .section-title {
  color: #1e293b;
  text-shadow: none;
}

/* Dark theme section title */
[data-theme="dark"] .section-title {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

/* Enhanced skeleton loading */
.skeleton-card {
  border-radius: 16px;
  min-height: 140px;
  max-width: 400px;
  margin: 0 auto;
  padding: 1.25rem;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-shimmer 1.5s infinite;
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.skeleton-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 0.5rem;
}

.skeleton-title {
  width: 60%;
  height: 16px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 0.75rem;
}

.skeleton-description {
  width: 80%;
  height: 12px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.15);
  margin-bottom: 1rem;
}

.skeleton-button {
  width: 50%;
  height: 14px;
  border-radius: 7px;
  background: rgba(255, 255, 255, 0.2);
  margin-top: auto;
}

[data-theme="light"] .skeleton-card {
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
}

[data-theme="light"] .skeleton-icon,
[data-theme="light"] .skeleton-title,
[data-theme="light"] .skeleton-description,
[data-theme="light"] .skeleton-button {
  background: rgba(0, 0, 0, 0.1);
}

/* Make skeleton responsive too */
@media (min-width: 768px) {
  .skeleton-card {
    max-width: none;
    min-height: 160px;
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .skeleton-card {
    min-height: 180px;
    padding: 2rem;
  }
}

/* Enhanced action cards with mobile-first design */
.action-card {
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  /* Mobile optimization: comfortable height and padding */
  min-height: 140px;
  width: 100%;
  max-width: 400px; /* Prevent cards from being too wide on mobile */
  margin: 0 auto; /* Center cards on mobile */
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
  touch-action: manipulation;
  color: #1e293b; /* Default text color for light theme */
  /* Ensure content is contained within card boundaries */
  box-sizing: border-box;
  contain: layout style;
}

/* Staggered entrance animations */
.action-card:nth-child(1) { animation-delay: 0.4s; }
.action-card:nth-child(2) { animation-delay: 0.6s; }
.action-card:nth-child(3) { animation-delay: 0.8s; }

/* Light theme action cards */
[data-theme="light"] .action-card {
  background: rgba(255, 255, 255, 0.98) !important;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  color: #1e293b;
}

/* Dark theme action cards */
[data-theme="dark"] .action-card {
  background: rgba(26, 27, 46, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.3);
  color: rgba(255, 255, 255, 0.95);
}

/* Enhanced hover effects with better mobile support */
.action-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.12);
}

.action-card:active {
  transform: translateY(-2px) scale(0.98);
  transition: all 0.1s ease;
}

/* Light theme hover */
[data-theme="light"] .action-card:hover {
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.08);
}

/* Dark theme hover */
[data-theme="dark"] .action-card:hover {
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.4);
}

/* Enhanced action content */
.action-content {
  padding: 1.25rem;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  /* Ensure content is properly contained */
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  min-height: 0; /* Allow flex child to shrink */
}

/* Fix Naive UI card content styling */
.action-card :deep(.n-card__content) {
  padding: 0 !important;
  background: transparent !important;
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}

/* Ensure proper text color inheritance and containment */
.action-card :deep(.n-card__content) * {
  box-sizing: border-box !important;
}

/* Preserve text color inheritance for text elements only, not icons */
.action-card :deep(.n-card__content) h3,
.action-card :deep(.n-card__content) p,
.action-card :deep(.n-card__content) .action-button {
  color: inherit !important;
}

/* Force proper content styling and containment */
.action-card .action-content,
.action-card .action-content * {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
}

/* Preserve text color inheritance for text elements only */
.action-card .action-content h3,
.action-card .action-content p,
.action-card .action-content .action-button {
  color: inherit !important;
}

/* Enhanced action header */
.action-header {
  margin-bottom: 0.75rem;
  position: relative;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.action-header .n-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  /* Don't force color inheritance for icons - let them keep their prop colors */
}

.action-card:hover .action-header .n-icon {
  transform: scale(1.1) translateY(-2px);
}

.action-header h3 {
  margin: 0.5rem 0 0 0;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  line-height: 1.3;
  color: inherit;
  /* Ensure text stays within boundaries */
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
}

/* Light theme action header */
[data-theme="light"] .action-header h3 {
  color: #1e293b !important;
}

/* Dark theme action header */
[data-theme="dark"] .action-header h3 {
  color: rgba(255, 255, 255, 0.95) !important;
}

/* Enhanced action description */
.action-content p {
  margin-bottom: 1rem;
  line-height: 1.5;
  flex-grow: 1;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  /* Prevent text cramping on mobile and ensure containment */
  hyphens: auto;
  word-wrap: break-word;
  overflow-wrap: break-word;
  color: inherit;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Limit to 3 lines on mobile */
  line-clamp: 3; /* Standard property for compatibility */
  -webkit-box-orient: vertical;
}

/* Light theme action content */
[data-theme="light"] .action-content p {
  color: #64748b !important;
}

/* Dark theme action content */
[data-theme="dark"] .action-content p {
  color: rgba(255, 255, 255, 0.75) !important;
}

/* Enhanced action footer */
.action-footer {
  position: relative;
  margin-top: auto;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

/* Ensure action button stays contained */
.action-footer .action-button {
  width: 100%;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Enhanced match badge with animation */
.match-badge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  animation: pulse 2s infinite;
  z-index: 3;
}

.match-badge .n-badge {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Tablet enhancements */
@media (min-width: 768px) {
  .actions-section {
    padding: 2rem 1.5rem;
  }
  
  .actions-grid {
    --grid-cols: 2; /* Tablet: two columns */
  }
  
  .section-title {
    font-size: 1.875rem;
    margin-bottom: 2rem;
  }
  
  .action-card {
    min-height: 160px;
    max-width: none; /* Remove mobile width constraint */
  }
  
  .action-content {
    padding: 1.5rem;
  }
  
  .action-header h3 {
    font-size: 1.125rem;
  }
  
  .action-content p {
    font-size: 0.9rem;
    margin-bottom: 1.25rem;
  }
}

/* Desktop enhancements */
@media (min-width: 1024px) {
  .actions-section {
    padding: 2.5rem 2rem;
  }
  
  .actions-grid {
    --grid-cols: 3; /* Desktop: three columns */
  }
  
  .section-title {
    font-size: 2rem;
    margin-bottom: 2.5rem;
  }
  
  .action-card {
    min-height: 180px;
  }
  
  .action-content {
    padding: 2rem;
  }
  
  .action-header h3 {
    font-size: 1.25rem;
  }
  
  .action-content p {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
  
  /* Enhanced desktop hover effects */
  .action-card:hover {
    transform: translateY(-8px) scale(1.03);
  }
}

/* Additional mobile optimizations for very small screens */
@media (max-width: 360px) {
  .actions-section {
    padding: 1rem 0.5rem;
  }
  
  .section-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }
  
  .action-card {
    min-height: 120px;
    max-width: 100%;
    margin: 0;
    /* Ensure strict containment on small screens */
    overflow: hidden !important;
  }
  
  .action-content {
    padding: 1rem;
    /* Force strict boundaries */
    width: 100% !important;
    overflow: hidden !important;
  }
  
  .action-header h3 {
    font-size: 0.9rem;
    /* Allow wrapping on very small screens */
    white-space: normal;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }
  
  .action-content p {
    font-size: 0.8rem;
    margin-bottom: 0.75rem;
    /* Stricter line clamping on small screens */
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }
  
  .action-footer .action-button {
    font-size: 0.8rem;
  }
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .action-card,
  .section-title,
  .action-header .n-icon,
  .match-badge {
    animation: none !important;
    transition: none !important;
  }
  
  .action-card:hover {
    transform: none !important;
  }
}

/* High contrast accessibility */
@media (prefers-contrast: high) {
  .action-card {
    border-width: 2px;
  }
  
  [data-theme="light"] .action-card {
    border-color: #000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  
  [data-theme="dark"] .action-card {
    border-color: #fff;
    box-shadow: 0 4px 8px rgba(255, 255, 255, 0.3);
  }
}

/* Force proper contrast and layout for all themes */
@media (forced-colors: active) {
  .action-card {
    border: 2px solid ButtonText;
    background: ButtonFace;
  }
  
  .action-header h3,
  .action-content p {
    color: ButtonText;
  }
}
</style>
