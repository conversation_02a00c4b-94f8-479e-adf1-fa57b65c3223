<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { NForm, NFormItem, NInput, type FormInst, type FormRules } from 'naive-ui'

interface PaymentMethodData {
  bankName?: string
  accountNumber?: string
  accountHolderName?: string
  iban?: string
  swiftCode?: string
  routingNumber?: string
  notes?: string
}

interface Props {
  modelValue: PaymentMethodData
  rules?: FormRules
  testIdPrefix?: string
  formClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  testIdPrefix: '',
  formClass: 'payment-method-form'
})

const emit = defineEmits<{
  'update:modelValue': [value: PaymentMethodData]
}>()

const { t } = useI18n()

// Form reference for external validation
const formRef = ref<FormInst | null>(null)

// Update model value
const updateValue = (field: keyof PaymentMethodData, value: string) => {
  emit('update:modelValue', {
    ...props.modelValue,
    [field]: value
  })
}

// Expose form ref for parent component validation
defineExpose({
  formRef,
  validate: () => formRef.value?.validate(),
  restoreValidation: () => formRef.value?.restoreValidation()
})
</script>

<template>
  <n-form 
    ref="formRef" 
    :model="modelValue" 
    :rules="rules"
    :class="formClass">
    <n-form-item 
      :label="t('paymentMethods.bankName')" 
      path="bankName">
      <n-input 
        :value="modelValue.bankName"
        @update:value="updateValue('bankName', $event)"
        :placeholder="t('paymentMethods.bankNamePlaceholder')"
        :data-testid="`${testIdPrefix}bank-name-input`" />
    </n-form-item>
    
    <n-form-item 
      :label="t('paymentMethods.accountNumber')" 
      path="accountNumber">
      <n-input 
        :value="modelValue.accountNumber"
        @update:value="updateValue('accountNumber', $event)"
        :placeholder="t('paymentMethods.accountNumberPlaceholder')"
        :data-testid="`${testIdPrefix}account-number-input`" />
    </n-form-item>
    
    <n-form-item 
      :label="t('paymentMethods.accountHolderName')" 
      path="accountHolderName">
      <n-input 
        :value="modelValue.accountHolderName"
        @update:value="updateValue('accountHolderName', $event)"
        :placeholder="t('paymentMethods.accountHolderNamePlaceholder')"
        :data-testid="`${testIdPrefix}account-holder-input`" />
    </n-form-item>
    
    <n-form-item 
      :label="t('paymentMethods.iban')" 
      path="iban">
      <n-input 
        :value="modelValue.iban"
        @update:value="updateValue('iban', $event)"
        :placeholder="t('paymentMethods.ibanPlaceholder')"
        :data-testid="`${testIdPrefix}iban-input`" />
    </n-form-item>
    
    <n-form-item 
      :label="t('paymentMethods.swiftCode')" 
      path="swiftCode">
      <n-input 
        :value="modelValue.swiftCode"
        @update:value="updateValue('swiftCode', $event)"
        :placeholder="t('paymentMethods.swiftCodePlaceholder')"
        :data-testid="`${testIdPrefix}swift-code-input`" />
    </n-form-item>
    
    <n-form-item 
      :label="t('paymentMethods.routingNumber')" 
      path="routingNumber">
      <n-input 
        :value="modelValue.routingNumber"
        @update:value="updateValue('routingNumber', $event)"
        :placeholder="t('paymentMethods.routingNumberPlaceholder')"
        :data-testid="`${testIdPrefix}routing-number-input`" />
    </n-form-item>
    
    <n-form-item 
      :label="t('paymentMethods.notes')" 
      path="notes">
      <n-input 
        :value="modelValue.notes"
        @update:value="updateValue('notes', $event)"
        type="textarea"
        :placeholder="t('paymentMethods.notesPlaceholder')"
        :data-testid="`${testIdPrefix}notes-input`" />
    </n-form-item>
  </n-form>
</template>

<style scoped>
.payment-method-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Support for the new-method-form class styling */
.new-method-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
</style>
