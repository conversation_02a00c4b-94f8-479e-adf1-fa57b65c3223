# Enhanced Gemini API Logging System

## Overview
This document outlines the improvements made to the backend logging system to provide better visibility into Gemini API issues, timeouts, and processing bottlenecks.

## Problem Identified
- Voice recordings were timing out after 60 seconds with no clear indication whether the issue was with Gemini API processing or other errors
- Limited visibility into the actual Gemini API call status and error details
- Difficult to distinguish between network issues, API errors, and processing delays

## Enhanced Logging Features

### 1. Detailed Request Pipeline Tracking
- **Request Start**: Logs when voice-to-report processing begins with timestamp
- **Stage Timings**: Tracks timing for each processing stage:
  - Audio buffer conversion to base64
  - Audio data validation
  - Prompt generation
  - Gemini API call duration
  - Response parsing duration
  - Total processing time

### 2. Gemini API Call Monitoring
- **Pre-API Logging**: Logs request details before sending to Gemini:
  - Model being used (`gemini-2.5-flash-preview-05-20`)
  - Audio buffer size and mime type
  - Language and other parameters
- **Success Logging**: Detailed success metrics:
  - Exact API call duration
  - Response size and content preview
- **Error Logging**: Comprehensive error details:
  - Error type, message, code, and status
  - Stack trace (first 500 chars)
  - Categorized error types for easier debugging

### 3. Error Categorization
Errors are now categorized into specific types for easier debugging:

- **TIMEOUT**: Request exceeded time limits (60s+)
- **NETWORK**: Connection issues (ECONNRESET, ENOTFOUND)
- **CLIENT_ERROR**: HTTP 4xx errors from Gemini API
- **SERVER_ERROR**: HTTP 5xx errors from Gemini API  
- **PARSING_ERROR**: Issues processing AI response format
- **UNKNOWN**: Other uncategorized errors

### 4. Route-Level Enhancements
- **Service Availability Check**: Verifies AI service is available before processing
- **Progress Tracking**: Logs when AI processing starts relative to request start
- **Success/Failure Indicators**: Visual indicators (✅/❌) for easy log scanning
- **Long Processing Warnings**: Alerts when processing exceeds 45 seconds
- **Specific HTTP Status Codes**: Returns appropriate status codes for different error types

## Log Output Example

### Successful Request
```
[AiRoutes] Voice-to-report request received - Duration: 9.1s, Language: en, MimeType: audio/webm;codecs=opus
[AiRoutes] Audio buffer received: size=142960 bytes, sha256=52ba52a6...
[AiRoutes] Starting AI processing at 5ms from request start
[AiService] 🎵 Starting processAudioToReport at 2025-06-05T10:30:15.123Z
[AiService] ✅ Service available, processing audio...
[AiService] Base64 conversion completed in 2ms, output size=190613 chars
[AiService] ✅ Audio validation completed in 1ms
[AiService] ✅ Prompt generation completed in 3ms, prompt length=1250 chars
[AiService] Sending audio to Gemini API...
[AiService] Gemini request details: model=gemini-2.5-flash-preview-05-20, audio_size=142960, mime_type=audio/webm;codecs=opus, language=en
[AiService] Gemini API call completed successfully in 8500ms
[AiService] Gemini response (truncated): {"analysis_status":"SUCCESS","transcription":"User is reporting a bug with the login form..."... [1250 chars total]
[AiService] ✅ Response received, starting parsing...
[AiService] ✅ Response parsing completed in 5ms, status=SUCCESS
[AiService] Direct audio processing successful - Processing time: 8520ms
[AiRoutes] ✅ AI report generated successfully. Transcription length: 150, Processing time: 8520ms
```

### Timeout Error
```
[AiRoutes] Voice-to-report request received - Duration: 9.1s, Language: en, MimeType: audio/webm;codecs=opus
[AiRoutes] Audio buffer received: size=142960 bytes, sha256=52ba52a6...
[AiService] Sending audio to Gemini API...
[AiService] Gemini request details: model=gemini-2.5-flash-preview-05-20, audio_size=142960, mime_type=audio/webm;codecs=opus, language=en
[AiService] Gemini API call FAILED after 60006ms
[AiService] Gemini error type: Error
[AiService] Gemini error message: timeout of 60000ms exceeded
[AiService] Gemini error code: ECONNABORTED
[AiService] CRITICAL ERROR processing audio directly after 60010ms
[AiService] TIMEOUT ERROR - Request exceeded time limit. Duration: 60010ms
[AiService] Error category: TIMEOUT
[AiRoutes] ❌ AI report failed: Direct audio processing failed: Request timed out after 60010ms. The AI service may be experiencing high load.
[AiRoutes] ⚠️ Long processing time detected: 60010ms - this may indicate Gemini API issues
```

## Testing the Enhanced Logging

Use the provided test script to verify the logging system:

```powershell
.\test-gemini-logging.ps1
```

This script will:
1. Check if the backend is running
2. Verify AI service availability
3. Send a test audio request
4. Show you where to look for the detailed logging output

## Benefits

1. **Faster Issue Identification**: Clear categorization of error types helps identify root causes quickly
2. **Performance Monitoring**: Stage-by-stage timing helps identify bottlenecks
3. **Gemini API Visibility**: Direct insight into whether Gemini API is the source of delays or errors
4. **Production Monitoring**: Visual indicators and structured logging make it easier to monitor in production
5. **User Experience**: Better error messages help users understand what went wrong

## Next Steps

1. Monitor the logs to identify patterns in Gemini API timeouts
2. Consider implementing retry logic for transient errors
3. Add alerting for excessive timeout rates
4. Optimize audio processing pipeline based on timing data
