#!/usr/bin/env pwsh

Write-Host "🔧 Testing AI Service Refactoring Changes" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

$ErrorActionPreference = "Stop"

try {
    # Change to backend directory
    Set-Location "C:\Code\MUNygo\backend"
    
    Write-Host "`n📦 Installing dependencies..." -ForegroundColor Yellow
    npm install
    
    Write-Host "`n🏗️  Building backend to check for TypeScript errors..." -ForegroundColor Yellow
    npm run build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Backend build successful!" -ForegroundColor Green
    } else {
        throw "Backend build failed"
    }
    
    # Change to frontend directory  
    Set-Location "C:\Code\MUNygo\frontend"
    
    Write-Host "`n📦 Installing frontend dependencies..." -ForegroundColor Yellow
    npm install
    
    Write-Host "`n🏗️  Building frontend to check for TypeScript errors..." -ForegroundColor Yellow
    npm run build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Frontend build successful!" -ForegroundColor Green
    } else {
        throw "Frontend build failed"
    }
    
    Write-Host "`n🎉 All refactoring changes verified successfully!" -ForegroundColor Green
    Write-Host "`nChanges implemented:" -ForegroundColor Cyan
    Write-Host "✅ 1. Removed explicit language input from AI prompt" -ForegroundColor White
    Write-Host "✅ 2. Reorganized AI prompt with natural flow (persona → context → goal → tagging → error handling)" -ForegroundColor White
    Write-Host "✅ 3. Updated fallback model to use gemini-2.0-flash" -ForegroundColor White
    Write-Host "✅ 4. Extended frontend timeout (120s) to receive fallback responses after backend timeout (90s)" -ForegroundColor White
    
    Write-Host "`nNext steps:" -ForegroundColor Cyan
    Write-Host "- Test with actual audio recordings to verify AI language detection works correctly" -ForegroundColor White
    Write-Host "- Monitor fallback processing performance with the new gemini-2.0-flash model" -ForegroundColor White
    Write-Host "- Verify timeout coordination between frontend and backend in production" -ForegroundColor White
    
} catch {
    Write-Host "`n❌ Error during testing: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n🚀 Ready for testing with development environment!" -ForegroundColor Green
