#!/usr/bin/env node

console.log('=== AI Tag Application Flow Test ===');

// Simulate the data structures we're working with
const mockAllPredefinedTags = [
  { id: 'error', name: 'error' },
  { id: 'fix-needed', name: 'fix-needed' },
  { id: 'urgent', name: 'urgent' },
  { id: 'authentication', name: 'authentication' },
  { id: 'login-flow', name: 'login-flow' }
];

const mockAiTags = [
  { tag: 'error', origin: 'PREDEFINED' },
  { tag: 'fix-needed', origin: 'PREDEFINED' },
  { tag: 'urgent', origin: 'PREDEFINED' },
  { tag: 'authentication', origin: 'AI_SUGGESTED' },
  { tag: 'login-flow', origin: 'AI_SUGGESTED' }
];

console.log('Mock AI suggested tags:', mockAiTags);
console.log('Mock predefined tags:', mockAllPredefinedTags);

// Simulate the applyAiReport logic
const currentTags = [];
const allPredefinedTagsList = mockAllPredefinedTags.map(tag => tag.name);
const predefinedTagsToSelect = [];

console.log('\n=== Processing AI tags ===');
mockAiTags.forEach(aiTag => {
  const tagName = typeof aiTag === 'string' ? aiTag : aiTag.tag;
  console.log(`\nProcessing AI tag: ${tagName} (origin: ${aiTag.origin})`);
  
  // Check if this AI-suggested tag matches a predefined tag
  if (allPredefinedTagsList.includes(tagName)) {
    console.log(`  -> Matches predefined tag: ${tagName}`);
    
    // Check if it's not already in the form
    const alreadyExists = currentTags.some(existingTag => {
      if (existingTag.origin === 'PREDEFINED') {
        const tagId = existingTag.tagId;
        if (tagId) {
          const predefinedTag = mockAllPredefinedTags.find(t => t.id === tagId);
          return predefinedTag?.name === tagName;
        }
      } else {
        const tagName_existing = existingTag.tagName;
        return tagName_existing === tagName;
      }
      return false;
    });
    
    if (!alreadyExists) {
      console.log(`  -> Adding as predefined tag to form`);
      
      // Find the predefined tag to get its ID
      const predefinedTag = mockAllPredefinedTags.find(t => t.name === tagName);
      if (predefinedTag) {
        currentTags.push({ tagId: predefinedTag.id, origin: 'PREDEFINED' });
        predefinedTagsToSelect.push(predefinedTag.id);
        console.log(`  -> Added to currentTags: ${JSON.stringify({ tagId: predefinedTag.id, origin: 'PREDEFINED' })}`);
        console.log(`  -> Added to predefinedTagsToSelect: ${predefinedTag.id}`);
      }
    } else {
      console.log(`  -> Already exists in form`);
    }
  } else {
    console.log(`  -> AI-only tag, will be handled by TagSelectorV2`);
  }
});

console.log('\n=== Final Results ===');
console.log('Final currentTags (should go to reportForm.reportTags):', JSON.stringify(currentTags, null, 2));
console.log('Final predefinedTagsToSelect (should go to selectedTagIds):', predefinedTagsToSelect);

console.log('\n=== Expected TagSelectorV2 Input ===');
console.log('selectedTagIds should contain:', predefinedTagsToSelect);
console.log('ai-suggested-tags prop should contain all AI tags for display');

console.log('\n=== Test Complete ===');
