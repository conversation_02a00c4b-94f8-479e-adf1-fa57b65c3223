# Debug Notification System Script
# This script helps debug why notifications are not being received

Write-Host "🔍 MUNygo Notification System Debug Script" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if services are running
function Test-Services {
    Write-Host "📋 Checking Services Status..." -ForegroundColor Yellow
    
    # Check if backend is running
    try {
        $backendResponse = Invoke-RestMethod -Uri "http://localhost:3000/health" -Method GET -TimeoutSec 5
        Write-Host "✅ Backend: Running" -ForegroundColor Green
        Write-Host "   - Status: $($backendResponse.status)" -ForegroundColor Gray
        Write-Host "   - Environment: $($backendResponse.environment)" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ Backend: Not running or unreachable" -ForegroundColor Red
        Write-Host "   Please start the backend with: npm run dev" -ForegroundColor Gray
        return $false
    }
    
    # Check if frontend is accessible
    try {
        $frontendResponse = Invoke-WebRequest -Uri "http://localhost:5173" -Method GET -TimeoutSec 5
        if ($frontendResponse.StatusCode -eq 200) {
            Write-Host "✅ Frontend: Running" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "❌ Frontend: Not running or unreachable" -ForegroundColor Red
        Write-Host "   Please start the frontend with: npm run dev" -ForegroundColor Gray
        return $false
    }
    
    Write-Host ""
    return $true
}

# Function to test database notifications
function Test-DatabaseNotifications {
    Write-Host "🗄️ Testing Database Notifications..." -ForegroundColor Yellow
    
    $userId = Read-Host "Enter a user ID to check notifications for"
      if ([string]::IsNullOrWhiteSpace($userId)) {
        Write-Host "❌ User ID is required" -ForegroundColor Red
        return
    }
    
    try {
        # Check notifications via API (without auth for now, just test endpoint)
        Write-Host "   Attempting to check notifications endpoint..." -ForegroundColor Gray
        Write-Host "   Note: Authentication required for actual data" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ Failed to connect to notifications API" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Gray
    }
    
    Write-Host ""
}

# Function to check recent offers and interests
function Test-OfferInterestFlow {
    Write-Host "🎯 Testing Offer-Interest Flow..." -ForegroundColor Yellow
    
    Write-Host "This will help you manually test the notification flow:" -ForegroundColor Gray
    Write-Host "1. Create an offer with User A" -ForegroundColor Gray
    Write-Host "2. Express interest with User B" -ForegroundColor Gray
    Write-Host "3. Check if User A receives notification" -ForegroundColor Gray
    Write-Host ""
    
    $testNow = Read-Host "Do you want to run the test now? (y/N)"
    
    if ($testNow -eq "y" -or $testNow -eq "Y") {
        Write-Host "📝 Step-by-step test:" -ForegroundColor Cyan
        Write-Host "1. Open browser 1 in incognito: http://localhost:5173" -ForegroundColor White
        Write-Host "2. Open browser 2 in different incognito: http://localhost:5173" -ForegroundColor White
        Write-Host "3. Register/login as different users in each browser" -ForegroundColor White
        Write-Host "4. In browser 1: Create an offer" -ForegroundColor White
        Write-Host "5. In browser 2: Browse offers and express interest in the offer from step 4" -ForegroundColor White
        Write-Host "6. In browser 1: Check the notification bell (should show a red badge)" -ForegroundColor White
        Write-Host ""
        Write-Host "Press any key when you're ready to continue..."
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
    
    Write-Host ""
}

# Function to check browser console logs
function Show-DebuggingTips {
    Write-Host "🔧 Debugging Tips..." -ForegroundColor Yellow
    
    Write-Host "To debug notifications in the browser:" -ForegroundColor Gray
    Write-Host "1. Open Developer Tools (F12)" -ForegroundColor White
    Write-Host "2. Go to Console tab" -ForegroundColor White
    Write-Host "3. Look for these log messages:" -ForegroundColor White
    Write-Host "   - '🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION:'" -ForegroundColor Green
    Write-Host "   - '[NotificationStore] Received NEW_NOTIFICATION via centralized manager:'" -ForegroundColor Green
    Write-Host "   - '[NotificationStore] Added new notification'" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "To debug notifications in the backend:" -ForegroundColor Gray
    Write-Host "1. Check the backend console/terminal for:" -ForegroundColor White
    Write-Host "   - '🔔🔔🔔 [BACKEND] Emitting INTEREST_RECEIVED to user'" -ForegroundColor Green
    Write-Host "   - '[NotificationService] Notification created:'" -ForegroundColor Green
    Write-Host "   - '[NotificationService] Emitted NEW_NOTIFICATION to userId:'" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Common issues and fixes:" -ForegroundColor Gray
    Write-Host "❌ Socket not connected:" -ForegroundColor Red
    Write-Host "   - Check connection status in navbar (should show 'connected')" -ForegroundColor White
    Write-Host "   - Refresh the page to retry connection" -ForegroundColor White
    Write-Host ""
    Write-Host "❌ Notifications created but not reaching frontend:" -ForegroundColor Red
    Write-Host "   - Check if user is in the correct Socket.IO room" -ForegroundColor White
    Write-Host "   - Verify JWT token is valid" -ForegroundColor White
    Write-Host ""
    Write-Host "❌ Notifications received but not displaying:" -ForegroundColor Red
    Write-Host "   - Check notification store state in Vue DevTools" -ForegroundColor White
    Write-Host "   - Verify DreamNavBar is using notificationStore correctly" -ForegroundColor White
    Write-Host ""
}

# Function to check specific notification components
function Test-NotificationComponents {
    Write-Host "🧩 Testing Notification Components..." -ForegroundColor Yellow
    
    Write-Host "Key files to check for issues:" -ForegroundColor Gray
    Write-Host "Backend:" -ForegroundColor Cyan
    Write-Host "  - backend\src\routes\offer.ts (lines 620-695) - Interest creation and notification" -ForegroundColor White
    Write-Host "  - backend\src\services\notificationService.ts - Notification creation" -ForegroundColor White
    Write-Host "  - backend\src\index.ts (lines 141) - Socket room joining" -ForegroundColor White
    Write-Host ""
    Write-Host "Frontend:" -ForegroundColor Cyan
    Write-Host "  - frontend\src\stores\notificationStore.ts - Notification state management" -ForegroundColor White
    Write-Host "  - frontend\src\services\centralizedSocketManager.ts - Socket event handling" -ForegroundColor White
    Write-Host "  - frontend\src\components\DreamNavBar.vue - Notification display" -ForegroundColor White
    Write-Host "  - frontend\src\components\AppContent.vue - Notification store initialization" -ForegroundColor White
    Write-Host ""
}

# Function to run database query to check notifications
function Test-DatabaseQuery {
    Write-Host "🔍 Database Notification Check..." -ForegroundColor Yellow
    
    Write-Host "You can manually check notifications in the database:" -ForegroundColor Gray
    Write-Host "1. Connect to your database (PostgreSQL or SQLite)" -ForegroundColor White
    Write-Host "2. Run this query:" -ForegroundColor White
    Write-Host ""
    Write-Host "SELECT * FROM \"Notification\" ORDER BY \"createdAt\" DESC LIMIT 10;" -ForegroundColor Green
    Write-Host ""
    Write-Host "Check for:" -ForegroundColor Gray
    Write-Host "- Recent notifications with type 'NEW_INTEREST_ON_YOUR_OFFER'" -ForegroundColor White
    Write-Host "- Correct userId (should match the offer creator)" -ForegroundColor White
    Write-Host "- isRead status (should be false for new notifications)" -ForegroundColor White
    Write-Host ""
}

# Main execution
$continue = Test-Services

if ($continue) {
    Write-Host "What would you like to debug?" -ForegroundColor Cyan
    Write-Host "1. Test offer-interest flow manually" -ForegroundColor White
    Write-Host "2. Check database notifications" -ForegroundColor White
    Write-Host "3. Run database query guidance" -ForegroundColor White
    Write-Host "4. Show debugging tips" -ForegroundColor White
    Write-Host "5. Show component details" -ForegroundColor White
    Write-Host "6. All of the above" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Enter your choice (1-6)"
    
    switch ($choice) {
        "1" { Test-OfferInterestFlow }
        "2" { Test-DatabaseNotifications }
        "3" { Test-DatabaseQuery }
        "4" { Show-DebuggingTips }
        "5" { Test-NotificationComponents }
        "6" { 
            Test-OfferInterestFlow
            Test-DatabaseNotifications
            Test-DatabaseQuery
            Show-DebuggingTips
            Test-NotificationComponents
        }
        default { 
            Write-Host "Invalid choice. Showing all debugging information..." -ForegroundColor Yellow
            Test-OfferInterestFlow
            Test-DatabaseNotifications
            Test-DatabaseQuery
            Show-DebuggingTips
            Test-NotificationComponents
        }
    }
}

Write-Host ""
Write-Host "🏁 Debug script completed!" -ForegroundColor Green
Write-Host "If you're still having issues, please check the browser console and backend logs for specific error messages." -ForegroundColor Gray
